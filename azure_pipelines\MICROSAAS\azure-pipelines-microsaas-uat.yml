trigger: none

resources:
- repo: self

variables:    
  # Container registry service connection established during pipeline creation
  dockerRegistryServiceConnection: 'uat-microsaas-acr-azdvp-sc'
  imageRepository: 'uat-microsaas-microsaas'
  containerRegistry: 'uatmicroSaasacr.azurecr.io'
  dockerfilePath: '$(Build.SourcesDirectory)/Dockerfile'  
  appSettingsFileName: "appsettings_$(client)_$(environment).json"
  dynamicTag: $(Build.BuildId)
  dynamicDescription: "Pipeline run for Client- $(client), Environment- $(environment), Tag- $(dynamicTag)"
  appSettingsEnvironment: '-ASPNETCORE_ENVIRONMENT Staging'
  webAppName: "microsaas-microsaas-uat"
  azureSubscription: "uat-microsaas-apps-azdvp-sc"

stages:
- stage: Initialize
  jobs:
  - job: SetupDescription
    pool:
      vmImage: ubuntu-latest
    steps:
    - script: |
        echo "Selected Client: $(client)"
        echo "Selected Environment: $(environment)"
        echo "Current Project Path: $(System.DefaultWorkingDirectory)"
        echo "$(dynamicDescription)"
      displayName: "Print Parameters and Project Path"

- stage: Build
  displayName: Build and push stage
  jobs:
  - job: Build
    displayName: Build
    pool:
      vmImage: ubuntu-latest
    steps:
    - bash: |
        echo "Application Name Environment: $(client)-microsaas-$(environment)"
        echo "Application Settings FileName to be Copied: appsettings_$(client)_$(environment).json"
        echo "Current Project Path: $(System.DefaultWorkingDirectory)"
      displayName: "Print Variables."

    - script: cp MicroSaasWebApi/Assets/$(client)/AppSettings/${{ variables.appSettingsFileName }} MicroSaasWebApi/appsettings.json
      displayName: "Setup Client-Specific App Settings"
    - task: Docker@2
      displayName: Build and push an image to container registry
      inputs:
        containerRegistry: "$(dockerRegistryServiceConnection)"
        repository: "$(imageRepository)"
        command: "buildAndPush"
        Dockerfile: "$(dockerfilePath)"
        tags: "$(dynamicTag)"
    - task: AzureWebAppContainer@1
      displayName: "Deploy to $(environment)"
      inputs:
        azureSubscription: $(azureSubscription)
        appName: $(webAppName)
        containers: "$(containerRegistry)/$(imageRepository):$(dynamicTag)"
        appSettings: "$(appSettingsEnvironment)"
