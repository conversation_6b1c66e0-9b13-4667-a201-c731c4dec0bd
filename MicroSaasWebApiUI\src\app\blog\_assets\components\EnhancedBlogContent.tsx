'use client'

import { useEffect } from 'react'
import { useTheme } from 'next-themes'
import { enhanceBlogContent } from '../scripts/codeBlockEnhancer'

interface EnhancedBlogContentProps {
  content: string
}

const EnhancedBlogContent = ({ content }: EnhancedBlogContentProps) => {
  const { theme } = useTheme()

  useEffect(() => {
    // Apply enhancements after the content is rendered
    enhanceBlogContent(theme)

    // Create a function to handle theme changes
    const handleThemeChange = (event: Event) => {
      const customEvent = event as CustomEvent
      const newTheme = customEvent.detail?.theme || theme
      enhanceBlogContent(newTheme)
    }

    // Listen for theme changes
    window.addEventListener('themeChange', handleThemeChange)

    // Clean up event listener when the component unmounts
    return () => {
      window.removeEventListener('themeChange', handleThemeChange)
    }
  }, [content, theme])

  // Determine classes based on theme
  const getThemeClasses = () => {
    if (theme === 'dark') {
      return 'blog-content bg-[#1E232C] text-white border border-[#373C53] dark-theme'
    }
    // Explicitly set light theme colors with light-theme class
    return 'blog-content bg-white text-gray-800 border border-gray-200 light-theme'
  }

  return (
    <div
      className={getThemeClasses()}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  )
}

export default EnhancedBlogContent



