extends:
  template: ../../azure_pipeline_template.yml
  parameters:
    environment: PROD
    client: PHW
    dockerRegistryServiceConnection: "prod-phw-acr-azdvp-sc"
    imageRepository: "prod-phw-webapi"
    containerRegistry: "phwproddatainterchangecontainerregistry.azurecr.io"
    dockerfilePath: "$(Build.SourcesDirectory)/Dockerfile"
    tag: "PHW-PROD-$(Build.BuildId)"
    poolName: "prod-di-pool"
    azureSubscription: "prod-phw-apps-azdvp-sc"
    webAppName: "prod-phw-webapi-appService"
    appSettingsEnvironment: "-ASPNETCORE_ENVIRONMENT Staging"
