import { ConfigProps } from '@/types'
import themes from 'daisyui/src/theming/themes'

const config: ConfigProps = {
	// REQUIRED
	appName: 'Javian Picardo',
	// REQUIRED: a short description of your app for SEO tags (can be overwritten)
	appDescription:
		'Portal for the services provided by <PERSON><PERSON><PERSON>.',
	// REQUIRED (no https://, not trialing slash at the end, just the naked domain)
	domainName: 'www.javianpicardo.com',
	// Add required robots configuration
	robots: {
		index: true,
		follow: true,
		'max-image-preview': 'large',
		'max-snippet': -1,
		googleBot: {
			index: true,
			follow: true,
			'max-image-preview': 'large',
			'max-snippet': -1,
		},
	},
	stripe: {
		// Create multiple products in your Stripe dashboard, then add them here. You can add as many plans as you want, just make sure to add the priceId
		products: [
			{
				type: 'one-time',
				title: 'Quick Shoots',
				productId: 'prod_S0c6NHMe0vINXA',
				subtitle: 'Professional photography session',
				price: 300,
				isBest: false,
				linkTitle: 'Book Quick Shoots',
				featuresTitle: 'Package Features',
				priceId: 'price_1R6aslP82YH9JfOlJZcweC4d',
				features: [
					{
						title: 'One hour of footage time',
						disabled: false,
					},
					{
						title: '50 edited photos',
						disabled: false,
					},
					{
						title: 'Help with posing',
						disabled: false,
					},
					{
						title: 'Location list to pick from',
						disabled: false,
					},
					{
						title: '7 business days turn around time',
						disabled: false,
					},
				],
			},
			{
				type: 'one-time',
				title: 'Mini Shoots',
				productId: 'prod_S0c9zDLLtK9rnS',
				subtitle: 'Perfect for professional portraits',
				price: 150,
				isBest: true,
				linkTitle: 'Book Mini Shoots',
				featuresTitle: 'Package Features',
				priceId: 'price_1R6avXP82YH9JfOlIGpAfwjU',
				features: [
					{
						title: '30 minutes of footage time',
						disabled: false,
					},
					{
						title: '25 edited photos',
						disabled: false,
					},
					{
						title: 'Help with posing',
						disabled: false,
					},
					{
						title: 'Location list to pick from',
						disabled: false,
					},
					{
						title: '7 business days turn around time',
						disabled: false,
					},
				],
			},
		],
	},
	colors: {
		// REQUIRED — The DaisyUI theme to use (added to the main layout.js). Leave blank for default (light & dark mode). If you any other theme than light/dark, you need to add it in config.tailwind.js in daisyui.themes.
		theme: 'light',
		// REQUIRED — This color will be reflected on the whole app outside of the document (loading bar, Chrome tabs, etc..). By default it takes the primary color from your DaisyUI theme (make sure to update your the theme name after "data-theme=")
		// OR you can just do this to use a custom color: main: "#f37055". HEX only.
		main: themes['light']['primary'],
	},
	resend: {
		// REQUIRED — Email 'From' field to be used when sending other emails, like abandoned carts, updates etc..
		fromAdmin: `Javian Picardo <<EMAIL>>`,
		// Email shown to customer if need support. Leave empty if not needed => if empty, set up Crisp above, otherwise you won't be able to offer customer support."
		supportEmail: '<EMAIL>',
		// When someone replies to supportEmail sent by the app, forward it to the email below (otherwise it's lost). If you set supportEmail to empty, this will be ignored.
		forwardRepliesTo: '<EMAIL>',
		subjects: {
			thankYou: 'Welcome to Javian Picardo',
		},
	},
}

export default config
