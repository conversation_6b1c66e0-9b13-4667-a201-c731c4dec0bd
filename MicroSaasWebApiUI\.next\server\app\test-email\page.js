(()=>{var e={};e.id=207,e.ids=[207],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},85807:e=>{"use strict";e.exports=require("module")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},6005:e=>{"use strict";e.exports=require("node:crypto")},78362:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),r(33075),r(8130),r(7629),r(12523);var a=r(23191),s=r(88716),i=r(37922),o=r.n(i),n=r(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d=["",{children:["test-email",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,33075)),"C:\\Projects\\PersonalPortal\\src\\app\\test-email\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e),async e=>(await Promise.resolve().then(r.bind(r,71150))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,63601))).default(e)],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,51922))).default(e)],twitter:[async e=>(await Promise.resolve().then(r.bind(r,40680))).default(e)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,8130)),"C:\\Projects\\PersonalPortal\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,7629)),"C:\\Projects\\PersonalPortal\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Projects\\PersonalPortal\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e),async e=>(await Promise.resolve().then(r.bind(r,71150))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,63601))).default(e)],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,51922))).default(e)],twitter:[async e=>(await Promise.resolve().then(r.bind(r,40680))).default(e)],manifest:void 0}}],c=["C:\\Projects\\PersonalPortal\\src\\app\\test-email\\page.tsx"],u="/test-email/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/test-email/page",pathname:"/test-email",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},61324:(e,t,r)=>{Promise.resolve().then(r.bind(r,21538))},21538:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var a=r(10326),s=r(17577),i=r(40381);function o(){let[e,t]=(0,s.useState)(""),[r,o]=(0,s.useState)(!1),[n,l]=(0,s.useState)(null),d=async t=>{t.preventDefault(),o(!0);try{let t=await fetch("/api/test-email",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e})}),r=await t.json();t.ok?i.Am.success("Email sent successfully!"):i.Am.error(r.error||"Failed to send email"),l(r)}catch(e){i.Am.error("An error occurred"),console.error(e)}finally{o(!1)}};return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-12 max-w-md",children:[a.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Test Resend Email"}),a.jsx("p",{className:"text-gray-600 mb-6",children:"This is a public page to test the Resend email <NAME_EMAIL>. No authentication required."}),(0,a.jsxs)("form",{onSubmit:d,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{htmlFor:"email",className:"block text-sm font-medium mb-1",children:"Email Address"}),a.jsx("input",{type:"email",id:"email",value:e,onChange:e=>t(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter recipient email",required:!0})]}),a.jsx("button",{type:"submit",disabled:r,className:`w-full py-2 px-4 rounded-md bg-blue-600 text-white font-medium ${r?"opacity-70 cursor-not-allowed":"hover:bg-blue-700"}`,children:r?"Sending...":"Send Test Email"})]}),n&&(0,a.jsxs)("div",{className:"mt-6 p-4 bg-gray-100 rounded-md",children:[a.jsx("h2",{className:"text-lg font-medium mb-2",children:"Result:"}),a.jsx("pre",{className:"text-sm overflow-auto p-2 bg-gray-200 rounded",children:JSON.stringify(n,null,2)})]})]})}},33075:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(68570).createProxy)(String.raw`C:\Projects\PersonalPortal\src\app\test-email\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[948,70,251,746],()=>r(78362));module.exports=a})();