using MicroSaasWebApi.Models.Auth;

namespace MicroSaasWebApi.Services.MICROSAAS.Interface
{
    /// <summary>
    /// Authentication service interface for MicroSaaS template with Supabase integration
    /// </summary>
    public interface IAuthService
    {
        /// <summary>
        /// Register a new user with Supabase
        /// </summary>
        /// <param name="request">Registration request</param>
        /// <returns>Authentication response</returns>
        Task<AuthResponse> RegisterAsync(RegisterRequest request);

        /// <summary>
        /// Login user with Supabase
        /// </summary>
        /// <param name="request">Login request</param>
        /// <returns>Authentication response</returns>
        Task<AuthResponse> LoginAsync(LoginRequest request);

        /// <summary>
        /// Refresh access token
        /// </summary>
        /// <param name="refreshToken">Refresh token</param>
        /// <returns>Authentication response with new tokens</returns>
        Task<AuthResponse> RefreshTokenAsync(string refreshToken);

        /// <summary>
        /// Logout user and invalidate tokens
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Task</returns>
        Task LogoutAsync(string userId);

        /// <summary>
        /// Request password reset email
        /// </summary>
        /// <param name="email">User email</param>
        /// <returns>Task</returns>
        Task RequestPasswordResetAsync(string email);

        /// <summary>
        /// Verify email address
        /// </summary>
        /// <param name="token">Verification token</param>
        /// <returns>True if verification successful</returns>
        Task<bool> VerifyEmailAsync(string token);

        /// <summary>
        /// Update user password
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="currentPassword">Current password</param>
        /// <param name="newPassword">New password</param>
        /// <returns>True if update successful</returns>
        Task<bool> UpdatePasswordAsync(string userId, string currentPassword, string newPassword);

        /// <summary>
        /// Get user information from Supabase
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>User information</returns>
        Task<UserInfo?> GetUserInfoAsync(string userId);

        /// <summary>
        /// Validate JWT token
        /// </summary>
        /// <param name="token">JWT token</param>
        /// <returns>True if token is valid</returns>
        Task<bool> ValidateTokenAsync(string token);

        /// <summary>
        /// Get user by email
        /// </summary>
        /// <param name="email">User email</param>
        /// <returns>User information</returns>
        Task<UserInfo?> GetUserByEmailAsync(string email);

        /// <summary>
        /// Update user email verification status
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="isVerified">Verification status</param>
        /// <returns>Task</returns>
        Task UpdateEmailVerificationStatusAsync(string userId, bool isVerified);

        /// <summary>
        /// Generate email verification token
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Verification token</returns>
        Task<string> GenerateEmailVerificationTokenAsync(string userId);

        /// <summary>
        /// Send welcome email to new user
        /// </summary>
        /// <param name="userInfo">User information</param>
        /// <returns>Task</returns>
        Task SendWelcomeEmailAsync(UserInfo userInfo);
    }
}
