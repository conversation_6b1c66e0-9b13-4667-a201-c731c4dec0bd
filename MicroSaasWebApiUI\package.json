{"name": "micro-sass-fast", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "npx prisma generate && next build", "start": "next start", "lint": "next lint", "migrate-db": "npx prisma migrate dev --name init"}, "dependencies": {"@clerk/nextjs": "^5.7.1", "@headlessui/react": "^1.7.18", "@mdx-js/loader": "^2.3.0", "@mdx-js/react": "^2.3.0", "@next/mdx": "^13.5.6", "@next/third-parties": "^14.2.5", "@prisma/client": "^6.5.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.2", "@react-email/components": "^0.0.25", "@strapi/blocks-react-renderer": "^1.0.1", "@stripe/stripe-js": "^4.7.0", "@types/wpapi": "^1.1.4", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "axios": "^1.7.9", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "eslint": "^8", "eslint-config-next": "13.4.19", "form-data": "^4.0.0", "formik": "^2.4.6", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.2", "jspdf": "^2.5.1", "lucide-react": "^0.408.0", "motion": "^12.5.0", "next": "^14.2.5", "next-plausible": "^3.12.0", "next-themes": "^0.4.4", "nextjs-toploader": "^1.6.11", "pg": "^8.12.0", "prop-types": "^15.8.1", "radix-ui": "^1.0.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-email": "^3.0.1", "react-hot-toast": "^2.4.1", "react-responsive-masonry": "^2.4.1", "react-syntax-highlighter": "^15.5.0", "react-to-print": "^2.15.1", "react-tooltip": "^5.26.3", "resend": "^4.0.0", "sass": "^1.82.0", "stripe": "^16.0.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "wpapi": "^1.2.2", "yup": "^1.4.0"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/mdx": "^2.0.12", "@types/node": "^20", "@types/pdfkit": "^0.13.5", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-syntax-highlighter": "^15.5.11", "@typescript-eslint/eslint-plugin": "^7.16.0", "@typescript-eslint/parser": "^7.16.0", "autoprefixer": "^10.4.20", "daisyui": "^4.10.1", "husky": "^9.1.7", "postcss": "^8.4.49", "prisma": "^6.5.0", "tailwindcss": "^3.4.16", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}