{"version": 3, "file": "app/api/webhook/stripe/route.js", "mappings": "oFAAAA,CAAAA,EAAAC,OAAA,CAAAC,QAAA,0CCAAF,CAAAA,EAAAC,OAAA,CAAAC,QAAA,+ZGEO,IAAMC,EAAS,IAAIC,EAAAA,CAAMA,CAACC,QAAQC,GAAG,CAACC,iBAAiB,CAAE,CAC9DC,WAAY,EACd,GAEaC,EAAgBJ,QAAQC,GAAG,CAACI,qBAAqB,cCLvD,SAAAC,EAAAC,CAAA,CAAAC,CAAA,EACP,MAAW,GAAAC,EAAAH,uBAAA,EAA6BE,EAAAD,EAAA,KACxC,yBCCA,IAAAG,WAAA,CACA,GACA,IACA,IACA,IACA,IACA,EAEA,IAAAA,WAAA,CACA,GACA,GACA,IACA,IACA,IACA,EAIA,IAAAA,WAAA,CACA,GACA,GACA,IACA,IACA,GACA,IACA,GACA,EAEA,IAAAA,WAAA,CACA,GACA,GACA,GACA,IACA,IACA,IACA,GACA,EAEA,IAAAA,WAAA,CACA,GACA,GACA,IACA,IACA,IACA,IACA,GACA,EAEA,IAAAA,WAAA,CACA,GACA,GACA,GACA,IACA,IACA,IACA,GACA,GACA,GACA,IACA,IACA,IACA,IACA,GACA,ECrDA,IAAAC,YCmEAC,OAAAC,GAAA,iCC5EA,IAAAF,YACA,IAAAG,wDCNA,IAAMC,EAA4BlB,QAAA,6BCGlC,IAAMmB,EAAc,IAAIC,EAAAA,YAAYA,CAEpCD,EAAYE,EAAE,CAAC,qBAAsB,MAAOC,IACxCC,EAAAA,CAAaA,CAACC,kBAAkB,CAACF,EACrC,GEEA,IAAMrB,EAAS,IAAIC,EAAAA,CAAMA,CAACC,QAAQC,GAAG,CAACC,iBAAiB,CAAG,CAAEoB,WAAY,YAAa,GAE9E,eAAeC,EAAwBC,CAAc,EAIxD,OAH6B,MAAMC,EAAAA,CAAMA,CAACC,YAAY,CAACC,SAAS,CAAC,CAC7DC,MAAO,CAAEC,cAAeL,CAAO,CACnC,EAEJ,CAEO,eAAeM,EAA8BC,CAAS,CAAEC,CAAmB,EAChF,IAAMC,EAAwCD,EAAME,IAAI,CACvDC,MAAM,CACHC,EAAU,MAAMC,EAAAA,CAAaA,CAACC,mBAAmB,CAACL,EAAa1B,EAAE,EACjEgC,EAAaH,GAASI,SACtBC,EAAUL,GAASM,YAAYR,IAAI,CAAC,EAAE,EAAES,MAAMpC,GAEpD,GAAI,CADSqC,EAAAA,CAAUA,CAAC9C,MAAM,CAAC+C,QAAQ,CAACC,IAAI,CAAC,GAAOC,EAAEN,OAAO,GAAKA,GAEhE,OAAOO,EAAAA,EAAYA,CAACC,IAAI,CAAC,CAAEC,MAAO,gBAAiB,EAAG,CAAEC,OAAQ,GAAI,GAEtE,IAAMX,EAAY,MAAM1C,EAAOsD,SAAS,CAACC,QAAQ,CAC/Cd,GAGF,GAAI,CAACC,GAAY,CAACA,EAASrB,KAAK,CAE9B,OADAmC,QAAQJ,KAAK,CAAC,2BACPF,EAAAA,EAAYA,CAACC,IAAI,CAAC,CAAEC,MAAO,0CAA2C,EAAG,CAAEC,OAAQ,GAAI,GAGhG,IAAMR,EAAQ,MAAM7C,EAAOyD,MAAM,CAACF,QAAQ,CAACZ,GAE3C,GAAG,CAACE,EACF,OAAO,IAAIa,SAAS,kBAAmB,CACrCL,OAAQ,GACV,GAGF,IAAMM,EAAU,MAAM3D,EAAO+C,QAAQ,CAACQ,QAAQ,CAC5C,iBAAOV,EAAMc,OAAO,CAAgBd,EAAMc,OAAO,CAAGd,EAAMc,OAAO,CAAClD,EAAE,EAGtE,GAAG,CAACkD,EACJ,OAAO,IAAID,SAAS,oBAAqB,CACvCL,OAAQ,GACV,GAGA,IAAMO,EAAWD,EAAQE,QAAQ,CAACC,iBAAiB,EAAI,UACjDC,EAAgBrB,EAASrB,KAAK,CAC9B2C,EAAQ,MAAMC,EAAAA,CAAWA,CAACD,KAAK,CAACE,WAAW,CAAC,CAACC,aAAa,CAACJ,EAAc,GAE/E,GAAI,CAACC,EAAM5B,IAAI,CAACgC,MAAM,CAEpB,OADAZ,QAAQJ,KAAK,CAAC,wBACPF,EAAAA,EAAYA,CAACC,IAAI,CAAC,CAAEC,MAAO,0CAA2C,EAAG,CAAEC,OAAQ,GAAI,GAGhG,IAAMgB,EAAOL,EAAM5B,IAAI,CAAC,EAAE,CACpBkC,EAASrC,GAAMG,MAAMC,QAAQT,aAwBnC,OAtBA,MAAMD,EAAAA,CAAMA,CAACC,YAAY,CAAC2C,MAAM,CAAC,CAC/BzC,MAAO,CACLC,cAAesC,EAAK5D,EAAE,EAExB+D,OAAQ,CACJC,kBAAmBtC,EAAa1B,EAAE,CACpCsB,cAAesC,EAAK5D,EAAE,CACtB,GAAI6D,EAAS,CAACI,cAAeJ,CAAM,EAAG,CAAC,CAAC,CACxCK,WAAY,SACZC,WAAYb,EACZc,SAAUjB,EACVkB,mBAAoBpC,EAASjC,EAAE,EAEjCsE,OAAQ,CACJN,kBAAmBtC,EAAa1B,EAAE,CACpC,GAAI6D,EAAS,CAACI,cAAeJ,CAAM,EAAG,CAAC,CAAC,CACxCK,WAAY,QACd,CACF,GAEAzD,EAAY8D,IAAI,CAAC,qBAAsBjB,GAEhCb,EAAAA,EAAYA,CAACC,IAAI,CAAC,CAAC,EAAG,CAAEE,OAAQ,GAAI,EAC3C,CAEO,eAAe4B,EAAyB/C,CAAmB,EAI1D,IAAMC,EAAoCD,EAAME,IAAI,CACjDC,MAAM,CACHT,EAAe,MAAM5B,EAAOkF,aAAa,CAAC3B,QAAQ,CACtDpB,EAAa1B,EAAE,EAEX0E,EAAY,MAAMxD,EAAAA,CAAMA,CAACC,YAAY,CAACC,SAAS,CAAC,CACpDC,MAAO,CAAE4C,cAAe9C,EAAanB,EAAE,CACzC,EAEA,OAAMkB,EAAAA,CAAMA,CAACC,YAAY,CAACmD,MAAM,CAAC,CAC/BjD,MAAO,CAAErB,GAAI0E,EAAU1E,EAAE,EACzB2B,KAAM,CAAEuC,WAAY,UAAY,CAClC,EACR,CAEO,eAAeS,EAAmBnD,CAAS,CAAEC,CAAmB,EAG/D,IAAMC,EAA+BD,EAAME,IAAI,CAC5CC,MAAM,CACHT,EAAe,MAAMD,EAAAA,CAAMA,CAACC,YAAY,CAACC,SAAS,CAAC,CACvDC,MAAO,CAAEgD,mBAAoB3C,EAAaO,QAAQ,CAAC2C,QAAQ,EAAG,CAChE,GAEMf,EAASrC,GAAMG,MAAMC,QAAQT,oBAEnC,EAMGA,GAAc8C,gBAAkBJ,EAC1B,IAAIZ,SAAS,0BAA2B,CAC7CL,OAAQ,GACV,QAKF,MAAM1B,EAAAA,CAAMA,CAACC,YAAY,CAACmD,MAAM,CAAC,CAC/BjD,MAAO,CAAErB,GAAImB,EAAanB,EAAE,EAC5B2B,KAAM,CAAEuC,WAAY,QAAU,CAChC,GAhBS,IAAIjB,SAAS,0BAA2B,CAC7CL,OAAQ,GACV,EAeV,EDzIO,SAAkCiC,CAAc,EACrD,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAQlB,MAAM,CAAEmB,IAAK,CACvC,IAAM7E,EAAS4E,CAAO,CAACC,EAAE,CACzB,GAAI,mBAAO7E,EACT,MAAM,MACJ,CAAC,2DAA2D,EAAE,OAAOA,EAAO;oEAAuE,CAAC,CAG1J,CACF,6MELO,IAAM8E,EAAU,gBACVC,EAAU,OAEhB,eAAeC,EAAKC,CAAgB,MAGrCzD,EAFJ,IAAM0D,EAAiB,MAAMD,EAAIE,IAAI,GAC/BC,EAAYC,CAAAA,EAAAA,EAAAA,OAAAA,IAAUC,GAAG,CAAC,oBAGhCxC,QAAQyC,GAAG,CAAC,SAAUL,GACtBpC,QAAQyC,GAAG,CAAC,cAAeH,GAC3B,GAAI,CACF5D,EAAQlC,EAAOkG,QAAQ,CAACC,cAAc,CAACP,EAAgBE,EAAWxF,EACpE,CAAE,MAAO8F,EAAK,CAEZ,OADA5C,QAAQJ,KAAK,CAAC,CAAC,uCAAuC,EAAEgD,EAAIC,OAAO,CAAC,CAAC,EAC9DnD,EAAAA,EAAYA,CAACC,IAAI,CAAC,CAAEC,MAAOgD,EAAIC,OAAO,EAAI,CAAEhD,OAAQ,GAAI,EACjE,CACA,GAAI,CACF,OAAQnB,EAAMoE,IAAI,EAChB,IAAK,6BAA8B,CACjC,IAAMrE,EAAOsE,KAAKC,KAAK,CAACZ,GACxB,OAAO5D,EAA8BC,EAAMC,EAC7C,CACA,IAAK,2BAKL,IAAK,gCAeL,IAAK,yBAjBH,KAQF,KAAK,gCACH+C,EAAyB/C,GACzB,KAEF,KAAK,eAAgB,CACnB,IAAMD,EAAOsE,KAAKC,KAAK,CAACZ,GACxBR,EAAmBnD,EAAMC,EAE3B,CAUF,CACF,CAAE,MAAOuE,EAAG,CACVjD,QAAQJ,KAAK,CAAC,iBAAkBqD,EAAEJ,OAAO,CAC3C,CACA,OAAOnD,EAAAA,EAAYA,CAACC,IAAI,CAAC,CAAC,EAC5B,CCxDA,IAAAuD,EAAA,IAAwBC,EAAAC,mBAAmB,EAC3CC,WAAA,CACAC,KAAcC,EAAAC,CAAS,CAAAC,SAAA,CACvBC,KAAA,4BACAC,SAAA,sBACAC,SAAA,QACAC,WAAA,8BACA,EACAC,iBAAA,yEACAC,iBAVA,GAWAC,SAAYC,CACZ,GAIA,CAAQC,oBAAAA,CAAA,CAAAC,6BAAAA,CAAA,CAAAC,YAAAA,CAAA,EAAiElB,EACzEmB,EAAA,4BACA,SAAAC,IACA,MAAW,GAAAC,EAAAC,EAAA,EAAW,CACtBJ,YAAAA,EACAD,6BAAAA,CACA,EACA,CC1BO,IAAAM,EAAqBC,EAC5BC,EAAeC,EAAAC,CAAsB,CAAAC,IAAA,CAAM5B,gCCK9B,IAAI6B,EAAExI,EAAQ,KAGyNwI,CAAAA,EAAEC,kDAAkD,CAACC,UAAU,CAAG3H,OAAOC,GAAG,CAAC,iBAAoBD,OAAOC,GAAG,CAAC,cAAlH,IAAgI2H,EAAE5H,OAAO6H,QAAQ,CACjQC,EAAGC,MAAMC,OAAO,CAACC,EAAEC,OAAOC,cAAc,CAACC,EAAGF,OAAOG,SAAS,CAACC,EAAE,IAAIC,QAIwGC,EAAE,IAAID,QAG/S,SAASE,EAAElB,CAAC,CAACmB,CAAC,EAAE,IAAIC,EAAEL,EAAEpD,GAAG,CAAC,IAAI,EAAE,GAAG,CAACyD,EAAE,MAAMC,MAAM,+GAA+G,GAAGD,EAAEhJ,EAAE,GAAG4H,EAAE,MAAM,CAAC,EAAE,IAAIsB,EAAEF,EAAEG,KAAK,CAAC,GAAG,OAAOD,EAAE,OAAO,IAAIH,EAAE,OAAOG,EAAEtG,MAAM,EAAE,IAAK,YAAY,OAAOsG,EAAEE,KAAK,CAACzF,MAAM,GAAGoF,CAAE,KAAK,UAAU,MAAMG,CAAE,KAAK,WAAW,MAAMA,EAAEG,MAAM,SAAS,KAAK,UAAW,OAAOH,EAAEtG,MAAM,EAAGsG,CAAAA,EAAEtG,MAAM,CAAC,UAAUsG,EAAEI,IAAI,CAAC,SAASC,CAAC,EAAEL,EAAEtG,MAAM,CAAC,YAAYsG,EAAEE,KAAK,CAACG,CAAC,EAAE,SAASA,CAAC,EAAEL,EAAEtG,MAAM,CAAC,WAAWsG,EAAEG,MAAM,CAACE,CAAC,IAAIL,CAAE,CAAC,CACtI,IAAIM,EAAGC,SAASf,SAAS,CAACgB,IAAI,CAACC,EAAGvB,MAAMM,SAAS,CAACkB,KAAK,CACna,SAASC,IAAI,IAAIjC,EAAE4B,EAAGM,KAAK,CAAC,IAAI,CAACC,WAAWhB,EAAEJ,EAAEpD,GAAG,CAAC,IAAI,EAAE,GAAGwD,EAAE,CAAC,IAAIC,EAAEW,EAAGK,IAAI,CAACD,UAAU,GAAGb,EAAE,KAAKA,EAAE,OAAOH,EAAEI,KAAK,CAACc,QAAQC,OAAO,CAACnB,EAAEI,KAAK,EAAEG,IAAI,CAAC,SAASC,CAAC,EAAE,OAAOA,EAAEY,MAAM,CAACnB,EAAE,GAAGiB,QAAQC,OAAO,CAAClB,GAAGT,OAAO6B,gBAAgB,CAACxC,EAAE,CAACyC,cAAc,CAACjB,MAAM,IAAI,CAACiB,aAAa,EAAEC,qBAAqB,CAAClB,MAAMN,CAAC,EAAEY,KAAK,CAACN,MAAMS,CAAC,CAAC,GAAGlB,EAAE4B,GAAG,CAAC3C,EAAE,CAAC5H,GAAG+I,EAAE/I,EAAE,CAACmJ,MAAMD,CAAC,EAAE,CAAC,OAAOtB,CAAC,CAC5V,SAAS4C,EAAE5C,CAAC,CAACmB,CAAC,CAACC,CAAC,CAACE,CAAC,EAAE,IAAI,CAACtG,MAAM,CAACgF,EAAE,IAAI,CAACwB,KAAK,CAACL,EAAE,IAAI,CAACM,MAAM,CAACL,EAAE,IAAI,CAACyB,SAAS,CAACvB,CAAC,CAC8I,SAASwB,EAAE9C,CAAC,CAACmB,CAAC,EAAE,IAAI,IAAIC,EAAE,EAAEA,EAAEpB,EAAEjE,MAAM,CAACqF,IAAI,CAAC,EAAEpB,CAAC,CAACoB,EAAE,EAAED,EAAE,CADnMyB,EAAE9B,SAAS,CAACH,OAAOxE,MAAM,CAACkG,QAAQvB,SAAS,EAAE8B,EAAE9B,SAAS,CAACY,IAAI,CAAC,SAAS1B,CAAC,CAACmB,CAAC,EAAE,OAAO,IAAI,CAACnG,MAAM,EAAE,IAAK,iBAAiB+H,EAAE,IAAI,EAAE,KAAM,KAAK,kBAAkBC,EAAE,IAAI,CAAC,CAAC,OAAO,IAAI,CAAChI,MAAM,EAAE,IAAK,YAAYgF,EAAE,IAAI,CAACwB,KAAK,EAAE,KAAM,KAAK,UAAU,IAAK,UAAU,IAAK,SAASxB,GAAI,QAAO,IAAI,CAACwB,KAAK,EAAG,KAAI,CAACA,KAAK,CAAC,EAAE,EAAE,IAAI,CAACA,KAAK,CAACyB,IAAI,CAACjD,EAAAA,EAAImB,GAAI,QAAO,IAAI,CAACM,MAAM,EAAG,KAAI,CAACA,MAAM,CAAC,EAAE,EAAE,IAAI,CAACA,MAAM,CAACwB,IAAI,CAAC9B,EAAAA,EAAI,KAAM,SAAQA,EAAE,IAAI,CAACM,MAAM,CAAC,CAAC,EAErN,IAAIyB,EAAE,KAAKC,EAAE,KACpS,SAASJ,EAAE/C,CAAC,EAAE,IAAImB,EAAE+B,EAAE9B,EAAE+B,EAAED,EAAElD,EAAEmD,EAAE,KAAK,IAAI7B,EAAEtB,EAAEwB,KAAK,CAACxB,EAAEhF,MAAM,CAAC,SAASgF,EAAEwB,KAAK,CAAC,KAAKxB,EAAEyB,MAAM,CAAC,KAAK,GAAG,CAAC,IAAIE,EAAEzD,KAAKC,KAAK,CAACmD,EAAEtB,EAAE6C,SAAS,CAACO,SAAS,EAAE,GAAG,OAAOD,GAAG,EAAEA,EAAEE,IAAI,CAACF,EAAE3B,KAAK,CAACG,EAAE3B,EAAEhF,MAAM,CAAC,UAAUgF,EAAEwB,KAAK,CAAC,KAAKxB,EAAEyB,MAAM,CAAC,SAAS,CAAC,IAAI6B,EAAEtD,EAAEwB,KAAK,CAACxB,EAAEhF,MAAM,CAAC,YAAYgF,EAAEwB,KAAK,CAACG,EAAE,OAAO2B,GAAGR,EAAEQ,EAAE3B,EAAE,CAAC,CAAC,MAAM4B,EAAE,CAACvD,EAAEhF,MAAM,CAAC,WAAWgF,EAAEyB,MAAM,CAAC8B,CAAC,QAAQ,CAACL,EAAE/B,EAAEgC,EAAE/B,CAAC,CAAC,CACxV,SAAS4B,EAAEhD,CAAC,EAAE,GAAG,CAAC,IAAImB,EAAEnB,EAAEwB,KAAK,CAACJ,EAAEoC,WAAWC,gBAAgB,CAACtC,CAAC,CAAC,EAAE,EAAE,GAAG,IAAIA,EAAEpF,MAAM,EAAE,YAAa,OAAOqF,EAAEM,IAAI,EAAC,GAAG,cAAcN,EAAEpG,MAAM,CAACoG,EAAEA,EAAEI,KAAK,MAAM,MAAMJ,EAAEK,MAAM,CAAC,IAAIH,EAAE,MAAMH,CAAC,CAAC,EAAE,CAACC,EAAE,KAAKD,CAAC,CAAC,EAAE,CAACC,EAAEsC,UAAU,CAACtC,EAAEuC,OAAO,CAACvC,EAAEA,CAAC,CAACD,CAAC,CAAC,EAAE,CAAC,CAACnB,EAAEhF,MAAM,CAAC,YAAYgF,EAAEwB,KAAK,CAACF,CAAC,CAAC,MAAMK,EAAE,CAAC3B,EAAEhF,MAAM,CAAC,WAAWgF,EAAEyB,MAAM,CAACE,CAAC,CAAC,+BCxB3SiC,EAAA,kECgHF,IAAAC,EAhH4B,CAE3BC,QAAS,iBAETC,eACC,sDAEDC,WAAY,wBAEZC,OAAQ,CACPC,MAAO,GACPC,OAAQ,GACR,oBAAqB,QACrB,cAAe,GACfC,UAAW,CACVF,MAAO,GACPC,OAAQ,GACR,oBAAqB,QACrB,cAAe,EAChB,CACD,EACAxM,OAAQ,CAEP+C,SAAU,CACT,CACCuD,KAAM,WACNoG,MAAO,eACPC,UAAW,sBACXC,SAAU,mCACV/J,MAAO,IACPgK,OAAQ,GACRC,UAAW,oBACXC,cAAe,mBACfpK,QAAS,iCACTqK,SAAU,CACT,CACCN,MAAO,2BACPO,SAAU,EACX,EACA,CACCP,MAAO,mBACPO,SAAU,EACX,EACA,CACCP,MAAO,mBACPO,SAAU,EACX,EACA,CACCP,MAAO,6BACPO,SAAU,EACX,EACA,CACCP,MAAO,mCACPO,SAAU,EACX,EACA,EAEF,CACC3G,KAAM,WACNoG,MAAO,cACPC,UAAW,sBACXC,SAAU,qCACV/J,MAAO,IACPgK,OAAQ,GACRC,UAAW,mBACXC,cAAe,mBACfpK,QAAS,iCACTqK,SAAU,CACT,CACCN,MAAO,6BACPO,SAAU,EACX,EACA,CACCP,MAAO,mBACPO,SAAU,EACX,EACA,CACCP,MAAO,mBACPO,SAAU,EACX,EACA,CACCP,MAAO,6BACPO,SAAU,EACX,EACA,CACCP,MAAO,mCACPO,SAAU,EACX,EACA,EAEF,EAEFC,OAAQ,CAEPC,MAAO,QAGPC,KAAMC,SAAAA,KAAAA,CAAgB,OAAU,EAEjCC,OAAQ,CAEPC,UAAW,6CAEXC,aAAc,4BAEdC,iBAAkB,4BAClBC,SAAU,CACTC,SAAU,2BACX,CACD,CACD,gDC7GA,IAAAzB,EAFe,GAAI0B,UAAAA,YAAYA,qECuF/B,IAAAC,EAvFwB,GAEhB,GAAAC,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,2CACX,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,iBACX,GAAAH,EAAAI,GAAA,EAACC,KAAAA,CAAGF,UAAU,iDAAwC,YAEtD,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,kCACX,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sBACX,GAAAH,EAAAC,IAAA,EAAC9K,IAAAA,CAAEgL,UAAU,kCACT,GAAAH,EAAAI,GAAA,EAACE,OAAAA,CAAKH,UAAU,uBAAc,qBAC7BI,EAAM5N,EAAE,IAEb,GAAAqN,EAAAC,IAAA,EAAC9K,IAAAA,CAAEgL,UAAU,kCACT,GAAAH,EAAAI,GAAA,EAACE,OAAAA,CAAKH,UAAU,uBAAc,gBAC7BI,EAAMC,SAAS,YAOhC,GAAAR,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,wCACX,GAAAH,EAAAC,IAAA,EAACC,MAAAA,WACG,GAAAF,EAAAI,GAAA,EAACK,KAAAA,CAAGN,UAAU,sCAA6B,UAC3C,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,4CACX,GAAAH,EAAAI,GAAA,EAACjL,IAAAA,CAAEgL,UAAU,uBAAc,qBAC3B,GAAAH,EAAAI,GAAA,EAACjL,IAAAA,UAAE,mBACH,GAAA6K,EAAAI,GAAA,EAACjL,IAAAA,UAAE,yBACH,GAAA6K,EAAAI,GAAA,EAACjL,IAAAA,UAAE,cACH,GAAA6K,EAAAI,GAAA,EAACjL,IAAAA,UAAE,mBACH,GAAA6K,EAAAI,GAAA,EAACjL,IAAAA,UAAE,kBAIX,GAAA6K,EAAAC,IAAA,EAACC,MAAAA,WACG,GAAAF,EAAAI,GAAA,EAACK,KAAAA,CAAGN,UAAU,sCAA6B,QAC3C,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,0FACX,GAAAH,EAAAI,GAAA,EAACE,OAAAA,UACIC,EAAMjM,IAAI,WAO3B,GAAA0L,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,gBACX,GAAAH,EAAAC,IAAA,EAACS,QAAAA,CAAMP,UAAU,mBACb,GAAAH,EAAAI,GAAA,EAACO,QAAAA,UACD,GAAAX,EAAAC,IAAA,EAACW,KAAAA,CAAGT,UAAU,qCACV,GAAAH,EAAAI,GAAA,EAACS,KAAAA,CAAGV,UAAU,sDAA6C,gBAC3D,GAAAH,EAAAI,GAAA,EAACS,KAAAA,CAAGV,UAAU,wDAA+C,QAC7D,GAAAH,EAAAI,GAAA,EAACS,KAAAA,CAAGV,UAAU,uDAA8C,eAC5D,GAAAH,EAAAI,GAAA,EAACS,KAAAA,CAAGV,UAAU,uDAA8C,gBAGhE,GAAAH,EAAAI,GAAA,EAACU,QAAAA,UACD,GAAAd,EAAAC,IAAA,EAACW,KAAAA,CAAGT,UAAU,qCACV,GAAAH,EAAAI,GAAA,EAACW,KAAAA,CAAGZ,UAAU,sCAA6B,4BAC3C,GAAAH,EAAAI,GAAA,EAACW,KAAAA,CAAGZ,UAAU,kDAAyC,MACvD,GAAAH,EAAAI,GAAA,EAACW,KAAAA,CAAGZ,UAAU,iDAAyCI,EAAMxL,KAAK,GAClE,GAAAiL,EAAAI,GAAA,EAACW,KAAAA,CAAGZ,UAAU,iDAAyCI,EAAMxL,KAAK,aAO9E,GAAAiL,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,yCACX,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sBACX,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,uDACX,GAAAH,EAAAI,GAAA,EAACE,OAAAA,UAAK,cACN,GAAAN,EAAAI,GAAA,EAACE,OAAAA,CAAAA,MAEL,GAAAN,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,mEACX,GAAAH,EAAAI,GAAA,EAACE,OAAAA,UAAK,WACN,GAAAN,EAAAI,GAAA,EAACE,OAAAA,CAAAA,MAEL,GAAAN,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,oEACX,GAAAH,EAAAI,GAAA,EAACE,OAAAA,UAAK,uBACN,GAAAN,EAAAI,GAAA,EAACE,OAAAA,CAAAA,gGC1EzB,IAAMU,EAAS,CACb7M,KAAM,CACJ8M,gBAAiB,UACjBC,WAAY,mBACd,EACAC,UAAW,CACTC,OAAQ,SACRC,QAAS,cACTC,MAAO,OACT,EACAC,QAAS,CACPN,gBAAiB,UACjBO,aAAc,MACdH,QAAS,MACX,EACAhB,GAAI,CACFoB,MAAO,UACPC,SAAU,OACVC,WAAY,OACZC,UAAW,SACXR,OAAQ,UACV,EACArJ,KAAM,CACJ0J,MAAO,UACPC,SAAU,OACVG,WAAY,OACZD,UAAW,QACb,EACAE,OAAQ,CACNb,gBAAiB,UACjBO,aAAc,MACdC,MAAO,UACPM,QAAS,eACTL,SAAU,OACVC,WAAY,OACZN,QAAS,YACTW,eAAgB,OAChBJ,UAAW,SACXK,UAAW,MACb,CACF,EAgCAC,EA9BiE,CAAC,CAChE3O,MAAAA,CAAK,CACN,GACC,GAAAyM,EAAAC,IAAA,EAACkC,EAAAA,CAAIA,CAAAA,WACH,GAAAnC,EAAAI,GAAA,EAACgC,EAAAA,CAAIA,CAAAA,CAAAA,GACL,GAAApC,EAAAI,GAAA,EAACiC,EAAAA,CAAOA,CAAAA,UAAC,8BACT,GAAArC,EAAAI,GAAA,EAACkC,EAAAA,CAAIA,CAAAA,CAACC,MAAOvB,EAAO7M,IAAI,UACtB,GAAA6L,EAAAI,GAAA,EAACoC,EAAAA,CAASA,CAAAA,CAACD,MAAOvB,EAAOG,SAAS,UAChC,GAAAnB,EAAAC,IAAA,EAACwC,EAAAA,CAAOA,CAAAA,CAACF,MAAOvB,EAAOO,OAAO,WAC5B,GAAAvB,EAAAI,GAAA,EAACsC,EAAAA,CAAIA,CAAAA,CAACH,MAAOvB,EAAOX,EAAE,UAAE,oBACxB,GAAAL,EAAAC,IAAA,EAACyC,EAAAA,CAAIA,CAAAA,CAACH,MAAOvB,EAAOjJ,IAAI,WAAE,uCACkBxE,EAAM,OAElD,GAAAyM,EAAAI,GAAA,EAACsC,EAAAA,CAAIA,CAAAA,CAACH,MAAOvB,EAAOjJ,IAAI,UAAE,wFAG1B,GAAAiI,EAAAI,GAAA,EAACqC,EAAAA,CAAOA,CAAAA,CAACF,MAAO,CAAEX,UAAW,QAAS,WACpC,GAAA5B,EAAAI,GAAA,EAACuC,EAAAA,CAAIA,CAAAA,CACHC,KAAK,kCACLL,MAAOvB,EAAOc,MAAM,UACrB,6DC/Db,OAAMe,EAGL,MAAapP,mBAAmBqP,CAAc,CAAE,CAC/C,GAAM,CAAExO,KAAAA,CAAI,CAAEgB,MAAAA,CAAK,CAAE,CAAG,MAAM,IAAI,CAACkK,MAAM,CAACuD,MAAM,CAACC,IAAI,CAAC,CACrDC,KAAMC,EAAAA,CAAMA,CAAC1D,MAAM,CAACC,SAAS,CAC7B0D,GAAI,CAACL,EAAO,CACZM,QAASF,EAAAA,CAAMA,CAAC1D,MAAM,CAACG,gBAAgB,CACvC0D,QAASH,EAAAA,CAAMA,CAAC1D,MAAM,CAACI,QAAQ,CAACC,QAAQ,CACxCyD,MAAOC,EAAiB,CAAEhQ,MAAOuP,CAAO,EACzC,GAEA,GAAIxN,EACH,MAAMA,EAGP,OAAOhB,CACR,CAEA,MAAakP,YAAYV,CAAc,CAAEW,CAAe,CAAE,CACzD,GAAM,CAAEnP,KAAAA,CAAI,CAAEgB,MAAAA,CAAK,CAAE,CAAG,MAAM,IAAI,CAACkK,MAAM,CAACuD,MAAM,CAACC,IAAI,CAAC,CACrDC,KAAMC,EAAAA,CAAMA,CAAC1D,MAAM,CAACC,SAAS,CAC7B0D,GAAI,CAACL,EAAO,CACZM,QAASF,EAAAA,CAAMA,CAAC1D,MAAM,CAACG,gBAAgB,CACvC0D,QAAS,YAAcI,EAAW9Q,EAAE,CACpC2Q,MAAOI,EAAgBD,EACxB,GAEA,GAAInO,EACH,MAAMA,EAGP,OAAOhB,CACR,CAEA,MAAaqP,mBAAmBpQ,CAAa,CAAE,CAC9C,IAAMqQ,EAAW,MAAM,IAAI,CAACC,cAAc,GAC1C,OAAO,IAAI,CAACrE,MAAM,CAACsE,QAAQ,CAACpN,MAAM,CAAC,CAClCnD,MAAAA,EACAwQ,aAAc,GACdC,WAAYJ,EAASK,SAAS,EAEhC,CAEA,MAAcJ,gBAAiB,CAC9B,IAAMD,EAAW,MAAM/P,EAAAA,CAAMA,CAACqQ,SAAS,CAACnQ,SAAS,GAEjD,GAAI6P,EACH,OAAOA,EAMR,GAAM,CACLtP,KAAM,CAAE3B,GAAAA,CAAE,CAAEwR,KAAAA,CAAI,CAAE,CAClB,CALsB,MAAM,IAAI,CAAC3E,MAAM,CAAC0E,SAAS,CAACxN,MAAM,CAAC,CACzDyN,KAAM,cACP,GAIA,OAAOtQ,EAAAA,CAAMA,CAACqQ,SAAS,CAACxN,MAAM,CAAC,CAC9BpC,KAAM,CACL2P,UAAWtR,EACXwR,KAAAA,CACD,CACD,EACD,oBA9DQ3E,MAAAA,CAAS,IAAI4E,EAAAA,CAAMA,CAAChS,QAAQC,GAAG,CAACgS,cAAc,EA+DvD,CAEO,IAAM7Q,EAAgB,IAAIqP,6DCtEjC,OAAMyB,EAMJ,MAAaC,qBAAqB5P,CAAkB,CAAE6P,CAAiB,CAAmB,CAKxF,MAAOC,CAJe,MAAM,IAAI,CAACvS,MAAM,CAACwS,aAAa,CAACC,QAAQ,CAACjO,MAAM,CAAC,CACpE9B,SAAUD,EACViQ,WAAYJ,CACd,IACqBK,GAAG,CAI1B,MAAanQ,oBAAoBoQ,CAAiB,CAAE,CACpD,GAAI,CAIJ,OAHgB,MAAM,IAAI,CAAC5S,MAAM,CAAC6S,QAAQ,CAACJ,QAAQ,CAAClP,QAAQ,CAACqP,EAAW,CACtEE,OAAQ,CAAC,aAAa,EAG1B,CAAE,MAAOrM,EAAG,CAEV,OADAjD,QAAQJ,KAAK,CAACqD,GACP,IACN,CACD,CAEA,MAAasM,gBAAgBC,CAAa,CAAE,CAC1C,OAAO,IAAI,CAAChT,MAAM,CAACkF,aAAa,CAAC3B,QAAQ,CAACyP,EAC5C,CAEA,MAAaC,mBAAmBC,CAAY,CAAE,CAC5C,OAAO,IAAI,CAAClT,MAAM,CAAC6S,QAAQ,CAACJ,QAAQ,CAAClP,QAAQ,CAAC2P,EAChD,oBAhCQlT,MAAAA,CAAS,IAAIC,EAAAA,CAAMA,CAACC,QAAQC,GAAG,CAACC,iBAAiB,CAAE,CACzDC,WAAY,EACd,GAgCF,CAEO,IAAMkC,EAAgB,IAAI6P", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/ignored|C:\\Projects\\PersonalPortal\\node_modules\\crypto-js|crypto", "webpack://_N_E/ignored|C:\\Projects\\PersonalPortal\\node_modules\\object-inspect|./util.inspect", "webpack://_N_E/./src/constants/stripe.ts", "webpack://_N_E/./node_modules/next/dist/esm/build/webpack/loaders/next-flight-loader/server-reference.js", "webpack://_N_E/./node_modules/next/dist/esm/server/stream-utils/encodedTags.js", "webpack://_N_E/./node_modules/next/dist/esm/server/stream-utils/node-web-streams-helper.js", "webpack://_N_E/./node_modules/next/dist/esm/server/app-render/encryption-utils.js", "webpack://_N_E/./node_modules/next/dist/esm/server/app-render/encryption.js", "webpack://_N_E/external commonjs \"node:events\"", "webpack://_N_E/./src/events/email-events.ts", "webpack://_N_E/../../src/build/webpack/loaders/next-flight-loader/action-validate.ts", "webpack://_N_E/./src/app/api/actions.ts", "webpack://_N_E/./src/app/api/webhook/stripe/route.ts", "webpack://_N_E/./src/app/api/webhook/stripe/route.ts?eeb5", "webpack://_N_E/?f0e0", "webpack://_N_E/./node_modules/next/dist/compiled/react-server-dom-webpack/cjs/react-server-dom-webpack-client.edge.production.min.js", "webpack://_N_E/./node_modules/next/dist/compiled/react-server-dom-webpack/client.edge.js", "webpack://_N_E/./src/config.ts", "webpack://_N_E/./src/libs/prisma.ts", "webpack://_N_E/./src/components/email-templates/Invoice.tsx", "webpack://_N_E/./src/components/email-templates/ThanksYouTemplate.tsx", "webpack://_N_E/./src/libs/resend.ts", "webpack://_N_E/./src/libs/stripe.ts"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "/* (ignored) */", "/* (ignored) */", "import Stripe from \"stripe\";\r\n\r\nexport const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {\r\n  typescript: true,\r\n});\r\n\r\nexport const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;\r\n", "/* eslint-disable import/no-extraneous-dependencies */ import { registerServerReference as flightRegisterServerReference } from \"react-server-dom-webpack/server.edge\";\nexport function registerServerReference(id, action) {\n    return flightRegisterServerReference(action, id, null);\n}\n\n//# sourceMappingURL=server-reference.js.map", "export const ENCODED_TAGS = {\n    // opening tags do not have the closing `>` since they can contain other attributes such as `<body className=''>`\n    OPENING: {\n        // <html\n        HTML: new Uint8Array([\n            60,\n            104,\n            116,\n            109,\n            108\n        ]),\n        // <body\n        BODY: new Uint8Array([\n            60,\n            98,\n            111,\n            100,\n            121\n        ])\n    },\n    CLOSED: {\n        // </head>\n        HEAD: new Uint8Array([\n            60,\n            47,\n            104,\n            101,\n            97,\n            100,\n            62\n        ]),\n        // </body>\n        BODY: new Uint8Array([\n            60,\n            47,\n            98,\n            111,\n            100,\n            121,\n            62\n        ]),\n        // </html>\n        HTML: new Uint8Array([\n            60,\n            47,\n            104,\n            116,\n            109,\n            108,\n            62\n        ]),\n        // </body></html>\n        BODY_AND_HTML: new Uint8Array([\n            60,\n            47,\n            98,\n            111,\n            100,\n            121,\n            62,\n            60,\n            47,\n            104,\n            116,\n            109,\n            108,\n            62\n        ])\n    }\n};\n\n//# sourceMappingURL=encodedTags.js.map", "import { getTracer } from \"../lib/trace/tracer\";\nimport { AppRenderSpan } from \"../lib/trace/constants\";\nimport { DetachedPromise } from \"../../lib/detached-promise\";\nimport { scheduleImmediate, atLeastOneTask } from \"../../lib/scheduler\";\nimport { ENCODED_TAGS } from \"./encodedTags\";\nimport { indexOfUint8Array, isEquivalentUint8Arrays, removeFromUint8Array } from \"./uint8array-helpers\";\nfunction voidCatch() {\n// this catcher is designed to be used with pipeTo where we expect the underlying\n// pipe implementation to forward errors but we don't want the pipeTo promise to reject\n// and be unhandled\n}\n// We can share the same encoder instance everywhere\n// Notably we cannot do the same for TextDecoder because it is stateful\n// when handling streaming data\nconst encoder = new TextEncoder();\nexport function chainStreams(...streams) {\n    // We could encode this invariant in the arguments but current uses of this function pass\n    // use spread so it would be missed by\n    if (streams.length === 0) {\n        throw new Error(\"Invariant: chainStreams requires at least one stream\");\n    }\n    // If we only have 1 stream we fast path it by returning just this stream\n    if (streams.length === 1) {\n        return streams[0];\n    }\n    const { readable, writable } = new TransformStream();\n    // We always initiate pipeTo immediately. We know we have at least 2 streams\n    // so we need to avoid closing the writable when this one finishes.\n    let promise = streams[0].pipeTo(writable, {\n        preventClose: true\n    });\n    let i = 1;\n    for(; i < streams.length - 1; i++){\n        const nextStream = streams[i];\n        promise = promise.then(()=>nextStream.pipeTo(writable, {\n                preventClose: true\n            }));\n    }\n    // We can omit the length check because we halted before the last stream and there\n    // is at least two streams so the lastStream here will always be defined\n    const lastStream = streams[i];\n    promise = promise.then(()=>lastStream.pipeTo(writable));\n    // Catch any errors from the streams and ignore them, they will be handled\n    // by whatever is consuming the readable stream.\n    promise.catch(voidCatch);\n    return readable;\n}\nexport function streamFromString(str) {\n    return new ReadableStream({\n        start (controller) {\n            controller.enqueue(encoder.encode(str));\n            controller.close();\n        }\n    });\n}\nexport async function streamToString(stream) {\n    const decoder = new TextDecoder(\"utf-8\", {\n        fatal: true\n    });\n    let string = \"\";\n    // @ts-expect-error TypeScript gets this wrong (https://nodejs.org/api/webstreams.html#async-iteration)\n    for await (const chunk of stream){\n        string += decoder.decode(chunk, {\n            stream: true\n        });\n    }\n    string += decoder.decode();\n    return string;\n}\nexport function createBufferedTransformStream() {\n    let bufferedChunks = [];\n    let bufferByteLength = 0;\n    let pending;\n    const flush = (controller)=>{\n        // If we already have a pending flush, then return early.\n        if (pending) return;\n        const detached = new DetachedPromise();\n        pending = detached;\n        scheduleImmediate(()=>{\n            try {\n                const chunk = new Uint8Array(bufferByteLength);\n                let copiedBytes = 0;\n                for(let i = 0; i < bufferedChunks.length; i++){\n                    const bufferedChunk = bufferedChunks[i];\n                    chunk.set(bufferedChunk, copiedBytes);\n                    copiedBytes += bufferedChunk.byteLength;\n                }\n                // We just wrote all the buffered chunks so we need to reset the bufferedChunks array\n                // and our bufferByteLength to prepare for the next round of buffered chunks\n                bufferedChunks.length = 0;\n                bufferByteLength = 0;\n                controller.enqueue(chunk);\n            } catch  {\n            // If an error occurs while enqueuing it can't be due to this\n            // transformers fault. It's likely due to the controller being\n            // errored due to the stream being cancelled.\n            } finally{\n                pending = undefined;\n                detached.resolve();\n            }\n        });\n    };\n    return new TransformStream({\n        transform (chunk, controller) {\n            // Combine the previous buffer with the new chunk.\n            bufferedChunks.push(chunk);\n            bufferByteLength += chunk.byteLength;\n            // Flush the buffer to the controller.\n            flush(controller);\n        },\n        flush () {\n            if (!pending) return;\n            return pending.promise;\n        }\n    });\n}\nfunction createInsertedHTMLStream(getServerInsertedHTML) {\n    return new TransformStream({\n        transform: async (chunk, controller)=>{\n            const html = await getServerInsertedHTML();\n            if (html) {\n                controller.enqueue(encoder.encode(html));\n            }\n            controller.enqueue(chunk);\n        }\n    });\n}\nexport function renderToInitialFizzStream({ ReactDOMServer, element, streamOptions }) {\n    return getTracer().trace(AppRenderSpan.renderToReadableStream, async ()=>ReactDOMServer.renderToReadableStream(element, streamOptions));\n}\nfunction createHeadInsertionTransformStream(insert) {\n    let inserted = false;\n    let freezing = false;\n    // We need to track if this transform saw any bytes because if it didn't\n    // we won't want to insert any server HTML at all\n    let hasBytes = false;\n    return new TransformStream({\n        async transform (chunk, controller) {\n            hasBytes = true;\n            // While react is flushing chunks, we don't apply insertions\n            if (freezing) {\n                controller.enqueue(chunk);\n                return;\n            }\n            const insertion = await insert();\n            if (inserted) {\n                if (insertion) {\n                    const encodedInsertion = encoder.encode(insertion);\n                    controller.enqueue(encodedInsertion);\n                }\n                controller.enqueue(chunk);\n                freezing = true;\n            } else {\n                // TODO (@Ethan-Arrowood): Replace the generic `indexOfUint8Array` method with something finely tuned for the subset of things actually being checked for.\n                const index = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.HEAD);\n                if (index !== -1) {\n                    if (insertion) {\n                        const encodedInsertion = encoder.encode(insertion);\n                        const insertedHeadContent = new Uint8Array(chunk.length + encodedInsertion.length);\n                        insertedHeadContent.set(chunk.slice(0, index));\n                        insertedHeadContent.set(encodedInsertion, index);\n                        insertedHeadContent.set(chunk.slice(index), index + encodedInsertion.length);\n                        controller.enqueue(insertedHeadContent);\n                    } else {\n                        controller.enqueue(chunk);\n                    }\n                    freezing = true;\n                    inserted = true;\n                }\n            }\n            if (!inserted) {\n                controller.enqueue(chunk);\n            } else {\n                scheduleImmediate(()=>{\n                    freezing = false;\n                });\n            }\n        },\n        async flush (controller) {\n            // Check before closing if there's anything remaining to insert.\n            if (hasBytes) {\n                const insertion = await insert();\n                if (insertion) {\n                    controller.enqueue(encoder.encode(insertion));\n                }\n            }\n        }\n    });\n}\n// Suffix after main body content - scripts before </body>,\n// but wait for the major chunks to be enqueued.\nfunction createDeferredSuffixStream(suffix) {\n    let flushed = false;\n    let pending;\n    const flush = (controller)=>{\n        const detached = new DetachedPromise();\n        pending = detached;\n        scheduleImmediate(()=>{\n            try {\n                controller.enqueue(encoder.encode(suffix));\n            } catch  {\n            // If an error occurs while enqueuing it can't be due to this\n            // transformers fault. It's likely due to the controller being\n            // errored due to the stream being cancelled.\n            } finally{\n                pending = undefined;\n                detached.resolve();\n            }\n        });\n    };\n    return new TransformStream({\n        transform (chunk, controller) {\n            controller.enqueue(chunk);\n            // If we've already flushed, we're done.\n            if (flushed) return;\n            // Schedule the flush to happen.\n            flushed = true;\n            flush(controller);\n        },\n        flush (controller) {\n            if (pending) return pending.promise;\n            if (flushed) return;\n            // Flush now.\n            controller.enqueue(encoder.encode(suffix));\n        }\n    });\n}\n// Merge two streams into one. Ensure the final transform stream is closed\n// when both are finished.\nfunction createMergedTransformStream(stream) {\n    let pull = null;\n    let donePulling = false;\n    async function startPulling(controller) {\n        if (pull) {\n            return;\n        }\n        const reader = stream.getReader();\n        // NOTE: streaming flush\n        // We are buffering here for the inlined data stream because the\n        // \"shell\" stream might be chunkenized again by the underlying stream\n        // implementation, e.g. with a specific high-water mark. To ensure it's\n        // the safe timing to pipe the data stream, this extra tick is\n        // necessary.\n        // We don't start reading until we've left the current Task to ensure\n        // that it's inserted after flushing the shell. Note that this implementation\n        // might get stale if impl details of Fizz change in the future.\n        await atLeastOneTask();\n        try {\n            while(true){\n                const { done, value } = await reader.read();\n                if (done) {\n                    donePulling = true;\n                    return;\n                }\n                controller.enqueue(value);\n            }\n        } catch (err) {\n            controller.error(err);\n        }\n    }\n    return new TransformStream({\n        transform (chunk, controller) {\n            controller.enqueue(chunk);\n            // Start the streaming if it hasn't already been started yet.\n            if (!pull) {\n                pull = startPulling(controller);\n            }\n        },\n        flush (controller) {\n            if (donePulling) {\n                return;\n            }\n            return pull || startPulling(controller);\n        }\n    });\n}\n/**\n * This transform stream moves the suffix to the end of the stream, so results\n * like `</body></html><script>...</script>` will be transformed to\n * `<script>...</script></body></html>`.\n */ function createMoveSuffixStream(suffix) {\n    let foundSuffix = false;\n    const encodedSuffix = encoder.encode(suffix);\n    return new TransformStream({\n        transform (chunk, controller) {\n            if (foundSuffix) {\n                return controller.enqueue(chunk);\n            }\n            const index = indexOfUint8Array(chunk, encodedSuffix);\n            if (index > -1) {\n                foundSuffix = true;\n                // If the whole chunk is the suffix, then don't write anything, it will\n                // be written in the flush.\n                if (chunk.length === suffix.length) {\n                    return;\n                }\n                // Write out the part before the suffix.\n                const before = chunk.slice(0, index);\n                controller.enqueue(before);\n                // In the case where the suffix is in the middle of the chunk, we need\n                // to split the chunk into two parts.\n                if (chunk.length > suffix.length + index) {\n                    // Write out the part after the suffix.\n                    const after = chunk.slice(index + suffix.length);\n                    controller.enqueue(after);\n                }\n            } else {\n                controller.enqueue(chunk);\n            }\n        },\n        flush (controller) {\n            // Even if we didn't find the suffix, the HTML is not valid if we don't\n            // add it, so insert it at the end.\n            controller.enqueue(encodedSuffix);\n        }\n    });\n}\nfunction createStripDocumentClosingTagsTransform() {\n    return new TransformStream({\n        transform (chunk, controller) {\n            // We rely on the assumption that chunks will never break across a code unit.\n            // This is reasonable because we currently concat all of React's output from a single\n            // flush into one chunk before streaming it forward which means the chunk will represent\n            // a single coherent utf-8 string. This is not safe to use if we change our streaming to no\n            // longer do this large buffered chunk\n            if (isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY_AND_HTML) || isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY) || isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.HTML)) {\n                // the entire chunk is the closing tags; return without enqueueing anything.\n                return;\n            }\n            // We assume these tags will go at together at the end of the document and that\n            // they won't appear anywhere else in the document. This is not really a safe assumption\n            // but until we revamp our streaming infra this is a performant way to string the tags\n            chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.BODY);\n            chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.HTML);\n            controller.enqueue(chunk);\n        }\n    });\n}\n/*\n * Checks if the root layout is missing the html or body tags\n * and if so, it will inject a script tag to throw an error in the browser, showing the user\n * the error message in the error overlay.\n */ export function createRootLayoutValidatorStream() {\n    let foundHtml = false;\n    let foundBody = false;\n    return new TransformStream({\n        async transform (chunk, controller) {\n            // Peek into the streamed chunk to see if the tags are present.\n            if (!foundHtml && indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.HTML) > -1) {\n                foundHtml = true;\n            }\n            if (!foundBody && indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.BODY) > -1) {\n                foundBody = true;\n            }\n            controller.enqueue(chunk);\n        },\n        flush (controller) {\n            const missingTags = [];\n            if (!foundHtml) missingTags.push(\"html\");\n            if (!foundBody) missingTags.push(\"body\");\n            if (!missingTags.length) return;\n            controller.enqueue(encoder.encode(`<script>self.__next_root_layout_missing_tags=${JSON.stringify(missingTags)}</script>`));\n        }\n    });\n}\nfunction chainTransformers(readable, transformers) {\n    let stream = readable;\n    for (const transformer of transformers){\n        if (!transformer) continue;\n        stream = stream.pipeThrough(transformer);\n    }\n    return stream;\n}\nexport async function continueFizzStream(renderStream, { suffix, inlinedDataStream, isStaticGeneration, getServerInsertedHTML, serverInsertedHTMLToHead, validateRootLayout }) {\n    const closeTag = \"</body></html>\";\n    // Suffix itself might contain close tags at the end, so we need to split it.\n    const suffixUnclosed = suffix ? suffix.split(closeTag, 1)[0] : null;\n    // If we're generating static HTML and there's an `allReady` promise on the\n    // stream, we need to wait for it to resolve before continuing.\n    if (isStaticGeneration && \"allReady\" in renderStream) {\n        await renderStream.allReady;\n    }\n    return chainTransformers(renderStream, [\n        // Buffer everything to avoid flushing too frequently\n        createBufferedTransformStream(),\n        // Insert generated tags to head\n        getServerInsertedHTML && !serverInsertedHTMLToHead ? createInsertedHTMLStream(getServerInsertedHTML) : null,\n        // Insert suffix content\n        suffixUnclosed != null && suffixUnclosed.length > 0 ? createDeferredSuffixStream(suffixUnclosed) : null,\n        // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n        inlinedDataStream ? createMergedTransformStream(inlinedDataStream) : null,\n        // Validate the root layout for missing html or body tags\n        validateRootLayout ? createRootLayoutValidatorStream() : null,\n        // Close tags should always be deferred to the end\n        createMoveSuffixStream(closeTag),\n        // Special head insertions\n        // TODO-APP: Insert server side html to end of head in app layout rendering, to avoid\n        // hydration errors. Remove this once it's ready to be handled by react itself.\n        getServerInsertedHTML && serverInsertedHTMLToHead ? createHeadInsertionTransformStream(getServerInsertedHTML) : null\n    ]);\n}\nexport async function continueDynamicPrerender(prerenderStream, { getServerInsertedHTML }) {\n    return prerenderStream// Buffer everything to avoid flushing too frequently\n    .pipeThrough(createBufferedTransformStream()).pipeThrough(createStripDocumentClosingTagsTransform())// Insert generated tags to head\n    .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML));\n}\nexport async function continueStaticPrerender(prerenderStream, { inlinedDataStream, getServerInsertedHTML }) {\n    const closeTag = \"</body></html>\";\n    return prerenderStream// Buffer everything to avoid flushing too frequently\n    .pipeThrough(createBufferedTransformStream())// Insert generated tags to head\n    .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))// Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n    .pipeThrough(createMergedTransformStream(inlinedDataStream))// Close tags should always be deferred to the end\n    .pipeThrough(createMoveSuffixStream(closeTag));\n}\nexport async function continueDynamicHTMLResume(renderStream, { inlinedDataStream, getServerInsertedHTML }) {\n    const closeTag = \"</body></html>\";\n    return renderStream// Buffer everything to avoid flushing too frequently\n    .pipeThrough(createBufferedTransformStream())// Insert generated tags to head\n    .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))// Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n    .pipeThrough(createMergedTransformStream(inlinedDataStream))// Close tags should always be deferred to the end\n    .pipeThrough(createMoveSuffixStream(closeTag));\n}\nexport async function continueDynamicDataResume(renderStream, { inlinedDataStream }) {\n    const closeTag = \"</body></html>\";\n    return renderStream// Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n    .pipeThrough(createMergedTransformStream(inlinedDataStream))// Close tags should always be deferred to the end\n    .pipeThrough(createMoveSuffixStream(closeTag));\n}\n\n//# sourceMappingURL=node-web-streams-helper.js.map", "// Keep the key in memory as it should never change during the lifetime of the server in\n// both development and production.\nlet __next_encryption_key_generation_promise = null;\nlet __next_loaded_action_key;\nlet __next_internal_development_raw_action_key;\nexport function arrayBufferToString(buffer) {\n    const bytes = new Uint8Array(buffer);\n    const len = bytes.byteLength;\n    // @anonrig: V8 has a limit of 65535 arguments in a function.\n    // For len < 65535, this is faster.\n    // https://github.com/vercel/next.js/pull/56377#pullrequestreview-1656181623\n    if (len < 65535) {\n        return String.fromCharCode.apply(null, bytes);\n    }\n    let binary = \"\";\n    for(let i = 0; i < len; i++){\n        binary += String.fromCharCode(bytes[i]);\n    }\n    return binary;\n}\nexport function stringToUint8Array(binary) {\n    const len = binary.length;\n    const arr = new Uint8Array(len);\n    for(let i = 0; i < len; i++){\n        arr[i] = binary.charCodeAt(i);\n    }\n    return arr;\n}\nexport function encrypt(key, iv, data) {\n    return crypto.subtle.encrypt({\n        name: \"AES-GCM\",\n        iv\n    }, key, data);\n}\nexport function decrypt(key, iv, data) {\n    return crypto.subtle.decrypt({\n        name: \"AES-GCM\",\n        iv\n    }, key, data);\n}\nexport async function generateEncryptionKeyBase64(dev) {\n    // For development, we just keep one key in memory for all actions.\n    // This makes things faster.\n    if (dev) {\n        if (typeof __next_internal_development_raw_action_key !== \"undefined\") {\n            return __next_internal_development_raw_action_key;\n        }\n    }\n    // This avoids it being generated multiple times in parallel.\n    if (!__next_encryption_key_generation_promise) {\n        __next_encryption_key_generation_promise = new Promise(async (resolve, reject)=>{\n            try {\n                const key = await crypto.subtle.generateKey({\n                    name: \"AES-GCM\",\n                    length: 256\n                }, true, [\n                    \"encrypt\",\n                    \"decrypt\"\n                ]);\n                const exported = await crypto.subtle.exportKey(\"raw\", key);\n                const b64 = btoa(arrayBufferToString(exported));\n                resolve([\n                    key,\n                    b64\n                ]);\n            } catch (error) {\n                reject(error);\n            }\n        });\n    }\n    const [key, b64] = await __next_encryption_key_generation_promise;\n    __next_loaded_action_key = key;\n    if (dev) {\n        __next_internal_development_raw_action_key = b64;\n    }\n    return b64;\n}\n// This is a global singleton that is used to encode/decode the action bound args from\n// the closure. This can't be using a AsyncLocalStorage as it might happen on the module\n// level. Since the client reference manifest won't be mutated, let's use a global singleton\n// to keep it.\nconst SERVER_ACTION_MANIFESTS_SINGLETON = Symbol.for(\"next.server.action-manifests\");\nexport function setReferenceManifestsSingleton({ clientReferenceManifest, serverActionsManifest, serverModuleMap }) {\n    // @ts-ignore\n    globalThis[SERVER_ACTION_MANIFESTS_SINGLETON] = {\n        clientReferenceManifest,\n        serverActionsManifest,\n        serverModuleMap\n    };\n}\nexport function getServerModuleMap() {\n    const serverActionsManifestSingleton = globalThis[SERVER_ACTION_MANIFESTS_SINGLETON];\n    if (!serverActionsManifestSingleton) {\n        throw new Error(\"Missing manifest for Server Actions. This is a bug in Next.js\");\n    }\n    return serverActionsManifestSingleton.serverModuleMap;\n}\nexport function getClientReferenceManifestSingleton() {\n    const serverActionsManifestSingleton = globalThis[SERVER_ACTION_MANIFESTS_SINGLETON];\n    if (!serverActionsManifestSingleton) {\n        throw new Error(\"Missing manifest for Server Actions. This is a bug in Next.js\");\n    }\n    return serverActionsManifestSingleton.clientReferenceManifest;\n}\nexport async function getActionEncryptionKey() {\n    if (__next_loaded_action_key) {\n        return __next_loaded_action_key;\n    }\n    const serverActionsManifestSingleton = globalThis[SERVER_ACTION_MANIFESTS_SINGLETON];\n    if (!serverActionsManifestSingleton) {\n        throw new Error(\"Missing manifest for Server Actions. This is a bug in Next.js\");\n    }\n    const rawKey = process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY || serverActionsManifestSingleton.serverActionsManifest.encryptionKey;\n    if (rawKey === undefined) {\n        throw new Error(\"Missing encryption key for Server Actions\");\n    }\n    __next_loaded_action_key = await crypto.subtle.importKey(\"raw\", stringToUint8Array(atob(rawKey)), \"AES-GCM\", true, [\n        \"encrypt\",\n        \"decrypt\"\n    ]);\n    return __next_loaded_action_key;\n}\n\n//# sourceMappingURL=encryption-utils.js.map", "/* eslint-disable import/no-extraneous-dependencies */ import \"server-only\";\n/* eslint-disable import/no-extraneous-dependencies */ import { renderToReadableStream, decodeReply } from \"react-server-dom-webpack/server.edge\";\n/* eslint-disable import/no-extraneous-dependencies */ import { createFromReadableStream, encodeReply } from \"react-server-dom-webpack/client.edge\";\nimport { streamToString } from \"../stream-utils/node-web-streams-helper\";\nimport { arrayBufferToString, decrypt, encrypt, getActionEncryptionKey, getClientReferenceManifestSingleton, getServerModuleMap, stringToUint8Array } from \"./encryption-utils\";\nconst textEncoder = new TextEncoder();\nconst textDecoder = new TextDecoder();\nasync function decodeActionBoundArg(actionId, arg) {\n    const key = await getActionEncryptionKey();\n    if (typeof key === \"undefined\") {\n        throw new Error(`Missing encryption key for Server Action. This is a bug in Next.js`);\n    }\n    // Get the iv (16 bytes) and the payload from the arg.\n    const originalPayload = atob(arg);\n    const ivValue = originalPayload.slice(0, 16);\n    const payload = originalPayload.slice(16);\n    const decrypted = textDecoder.decode(await decrypt(key, stringToUint8Array(ivValue), stringToUint8Array(payload)));\n    if (!decrypted.startsWith(actionId)) {\n        throw new Error(\"Invalid Server Action payload: failed to decrypt.\");\n    }\n    return decrypted.slice(actionId.length);\n}\nasync function encodeActionBoundArg(actionId, arg) {\n    const key = await getActionEncryptionKey();\n    if (key === undefined) {\n        throw new Error(`Missing encryption key for Server Action. This is a bug in Next.js`);\n    }\n    // Get 16 random bytes as iv.\n    const randomBytes = new Uint8Array(16);\n    crypto.getRandomValues(randomBytes);\n    const ivValue = arrayBufferToString(randomBytes.buffer);\n    const encrypted = await encrypt(key, randomBytes, textEncoder.encode(actionId + arg));\n    return btoa(ivValue + arrayBufferToString(encrypted));\n}\n// Encrypts the action's bound args into a string.\nexport async function encryptActionBoundArgs(actionId, args) {\n    const clientReferenceManifestSingleton = getClientReferenceManifestSingleton();\n    // Using Flight to serialize the args into a string.\n    const serialized = await streamToString(renderToReadableStream(args, clientReferenceManifestSingleton.clientModules));\n    // Encrypt the serialized string with the action id as the salt.\n    // Add a prefix to later ensure that the payload is correctly decrypted, similar\n    // to a checksum.\n    const encrypted = await encodeActionBoundArg(actionId, serialized);\n    return encrypted;\n}\n// Decrypts the action's bound args from the encrypted string.\nexport async function decryptActionBoundArgs(actionId, encrypted) {\n    // Decrypt the serialized string with the action id as the salt.\n    const decryped = await decodeActionBoundArg(actionId, await encrypted);\n    // Using Flight to deserialize the args from the string.\n    const deserialized = await createFromReadableStream(new ReadableStream({\n        start (controller) {\n            controller.enqueue(textEncoder.encode(decryped));\n            controller.close();\n        }\n    }), {\n        ssrManifest: {\n            // TODO: We can't use the client reference manifest to resolve the modules\n            // on the server side - instead they need to be recovered as the module\n            // references (proxies) again.\n            // For now, we'll just use an empty module map.\n            moduleLoading: {},\n            moduleMap: {}\n        }\n    });\n    // This extra step ensures that the server references are recovered.\n    const serverModuleMap = getServerModuleMap();\n    const transformed = await decodeReply(await encodeReply(deserialized), serverModuleMap);\n    return transformed;\n}\n\n//# sourceMappingURL=encryption.js.map", "const __WEBPACK_NAMESPACE_OBJECT__ = require(\"node:events\");", "import { EventEmitter } from 'events';\r\nimport { resendService } from './../libs/resend'\r\n\r\nconst emailEvents = new EventEmitter();\r\n\r\nemailEvents.on('sendThanksYouEmail', async (email: string) => {\r\n    resendService.sendThanksYouEmail(email)\r\n});\r\n\r\nexport default emailEvents;\r\n", null, "\"use server\"\r\nimport prisma from \"@/libs/prisma\";\r\nimport { clerkClient } from '@clerk/nextjs/server';\r\nimport { NextResponse } from 'next/server';\r\nimport Stripe from 'stripe';\r\nimport configFile from \"@/config\";\r\nimport { stripeService } from '@/libs/stripe';\r\nimport emailEvents from \"@/events/email-events\";\r\n\r\nconst stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, { apiVersion: '2024-06-20' });\r\n\r\nexport async function getSubscriptionByUserId(userId: string) {\r\n    const existingSubscription = await prisma.subscription.findFirst({\r\n        where: { user_clerk_id: userId },\r\n    });\r\n    return existingSubscription\r\n}\r\n\r\nexport async function processCheckoutSuccessWebhook(body: any, event: Stripe.Event) {\r\n  const stripeObject: Stripe.Checkout.Session = event.data\r\n  .object as Stripe.Checkout.Session;\r\nconst session = await stripeService.findCheckoutSession(stripeObject.id);\r\nconst customerId = session?.customer;\r\nconst priceId = session?.line_items?.data[0]?.price.id;\r\nconst plan = configFile.stripe.products.find((p) => p.priceId === priceId);\r\nif (!plan) {\r\n  return NextResponse.json({ error: 'Plan not found' }, { status: 400 });\r\n}\r\nconst customer = (await stripe.customers.retrieve(\r\n  customerId as string\r\n)) as Stripe.Customer;\r\n\r\nif (!customer || !customer.email) {\r\n  console.error('No customer email found');\r\n  return NextResponse.json({ error: 'No customer email found for subscription' }, { status: 400 });\r\n}\r\n\r\nconst price = await stripe.prices.retrieve(priceId)\r\n\r\nif(!price) {\r\n  return new Response('Price not found', {\r\n    status: 500\r\n  })\r\n}\r\n\r\nconst product = await stripe.products.retrieve(\r\n  typeof price.product === 'string' ? price.product : price.product.id\r\n);\r\n\r\nif(!product) {\r\nreturn new Response('Product not found', {\r\n  status: 500\r\n})\r\n}\r\n\r\nconst subsType = product.metadata.subscription_type || 'default'\r\nconst customerEmail = customer.email;\r\nconst users = await clerkClient.users.getUserList({emailAddress:[customerEmail]})\r\n\r\nif (!users.data.length) {\r\n  console.error('Clerc user not found');\r\n  return NextResponse.json({ error: 'No customer email found for subscription' }, { status: 400 });\r\n}\r\n\r\nconst user = users.data[0]\r\nconst subsId = body?.data?.object?.subscription as string\r\n\r\nawait prisma.subscription.upsert({\r\n  where: {\r\n    user_clerk_id: user.id,\r\n  },\r\n  create: {\r\n      last_stripe_cs_id: stripeObject.id,\r\n    user_clerk_id: user.id,\r\n    ...(subsId ? {sub_stripe_id: subsId}: {}),\r\n    sub_status: 'active',\r\n    user_email: customerEmail,\r\n    sub_type: subsType,\r\n    stripe_customer_id: customer.id,\r\n  },\r\n  update: {\r\n      last_stripe_cs_id: stripeObject.id,\r\n    ...(subsId ? {sub_stripe_id: subsId}: {}),\r\n    sub_status: 'active',\r\n  }\r\n});\r\n\r\nemailEvents.emit('sendThanksYouEmail', customerEmail)\r\n\r\nreturn NextResponse.json({}, { status: 200 });\r\n}\r\n\r\nexport async function processSubscriptonDelete(event: Stripe.Event) {\r\n\r\n        // The customer subscription stopped\r\n        // ❌ Revoke access to the product\r\n        const stripeObject: Stripe.Subscription = event.data\r\n          .object as Stripe.Subscription;\r\n        const subscription = await stripe.subscriptions.retrieve(\r\n          stripeObject.id\r\n        );\r\n        const prismaSub = await prisma.subscription.findFirst({\r\n          where: { sub_stripe_id: subscription.id },\r\n        });\r\n        // Revoke access to your product\r\n        await prisma.subscription.update({\r\n          where: { id: prismaSub.id },\r\n          data: { sub_status: 'inactive', },\r\n        });\r\n}\r\n\r\nexport async function processInvoicePaid(body: any, event: Stripe.Event) {\r\n        // Customer just paid an invoice (for instance, a recurring payment for a subscription)\r\n        // ✅ Grant access to the product\r\n        const stripeObject: Stripe.Invoice = event.data\r\n          .object as Stripe.Invoice;\r\n        const subscription = await prisma.subscription.findFirst({\r\n          where: { stripe_customer_id: stripeObject.customer.toString() },\r\n        });\r\n\r\n        const subsId = body?.data?.object?.subscription as string\r\n  \r\n        if(!subsId) {\r\n          return new Response('Subscription is invalid', {\r\n            status: 400\r\n          })\r\n        }\r\n      \r\n        if(subscription?.sub_stripe_id !== subsId) {\r\n          return new Response('Subscription is invalid', {\r\n            status: 403\r\n          })\r\n        }\r\n\r\n        // Make sure the invoice is for the same plan (priceId) the user subscribed to\r\n        // Grant user access to your product. It's a boolean in the database, but could be a number of credits, etc...\r\n        await prisma.subscription.update({\r\n          where: { id: subscription.id },\r\n          data: { sub_status: 'active', },\r\n        });\r\n}", "import { NextResponse, NextRequest } from \"next/server\";\r\nimport { headers } from \"next/headers\";\r\nimport <PERSON><PERSON> from \"stripe\";\r\nimport { stripe, webhookSecret } from \"@/constants/stripe\";\r\nimport { processSubscriptonDelete, processInvoicePaid, processCheckoutSuccessWebhook } from \"../../actions\";\r\n// This is where we receive Stripe webhook events. / It used to update the user data, send emails, etc...\r\n// By default, it'll store the user in the database. \r\nexport const dynamic = 'force-dynamic'\r\nexport const runtime = 'edge'\r\n\r\nexport async function POST(req: NextRequest) {\r\n  const textParsedBody = await req.text();\r\n  const signature = headers().get(\"stripe-signature\");\r\n  let event: Stripe.Event;\r\n\r\n  console.log('1 POST', textParsedBody)\r\n  console.log('2 signature', signature)\r\n  try {\r\n    event = stripe.webhooks.constructEvent(textParsedBody, signature, webhookSecret);\r\n  } catch (err) {\r\n    console.error(`Webhook signature verification failed. ${err.message}`);\r\n    return NextResponse.json({ error: err.message }, { status: 400 });\r\n  }\r\n  try {\r\n    switch (event.type) {\r\n      case \"checkout.session.completed\": {\r\n        const body = JSON.parse(textParsedBody);\r\n        return processCheckoutSuccessWebhook(body, event)\r\n      }\r\n      case \"checkout.session.expired\": {\r\n        // User didn't complete the transaction\r\n        // You don't need to do anything here, by you can send an email to the user to remind him to complete the transaction, for instance\r\n        break;\r\n      }\r\n      case \"customer.subscription.updated\": {\r\n        // The customer might have changed the plan (higher or lower plan, cancel soon etc...)\r\n        // You don't need to do anything here, because Stripe will let us know when the subscription is canceled for good (at the end of the billing cycle) in the \"customer.subscription.deleted\" event\r\n        // You can update the user data to show a \"Cancel soon\" badge for instance\r\n        break;\r\n      }\r\n      case \"customer.subscription.deleted\": {\r\n        processSubscriptonDelete(event)\r\n        break;\r\n      }\r\n      case \"invoice.paid\": {\r\n        const body = JSON.parse(textParsedBody);\r\n        processInvoicePaid(body, event)\r\n        break;\r\n      }\r\n      case \"invoice.payment_failed\":\r\n        // A payment failed (for instance the customer does not have a valid payment method)\r\n        // ❌ Revoke access to the product\r\n        // ⏳ OR wait for the customer to pay (more friendly):\r\n        //      - Stripe will automatically email the customer (Smart Retries)\r\n        //      - We will receive a \"customer.subscription.deleted\" when all retries were made and the subscription has expired\r\n        break;\r\n      default:\r\n      // Unhandled event type\r\n    }\r\n  } catch (e) {\r\n    console.error(\"stripe error: \", e.message);\r\n  }\r\n  return NextResponse.json({});\r\n}\r\n", "import { AppRouteRouteModule } from \"next/dist/server/future/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/future/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Projects\\\\PersonalPortal\\\\src\\\\app\\\\api\\\\webhook\\\\stripe\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/webhook/stripe/route\",\n        pathname: \"/api/webhook/stripe\",\n        filename: \"route\",\n        bundlePath: \"app/api/webhook/stripe/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\PersonalPortal\\\\src\\\\app\\\\api\\\\webhook\\\\stripe\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/webhook/stripe/route\";\nfunction patchFetch() {\n    return _patchFetch({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\nexport { routeModule, requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, originalPathname, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "import { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fwebhook%2Fstripe%2Froute&page=%2Fapi%2Fwebhook%2Fstripe%2Froute&pagePath=private-next-app-dir%2Fapi%2Fwebhook%2Fstripe%2Froute.ts&appDir=C%3A%5CProjects%5CPersonalPortal%5Csrc%5Capp&appPaths=%2Fapi%2Fwebhook%2Fstripe%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/webhook/stripe/route.ts?__next_edge_ssr_entry__\";\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule);\n\n//# sourceMappingURL=edge-app-route.js.map", "/*\n React\n react-server-dom-webpack-client.edge.production.min.js\n\n Copyright (c) Meta Platforms, Inc. and affiliates.\n\n This source code is licensed under the MIT license found in the\n LICENSE file in the root directory of this source tree.\n*/\n'use strict';var r=require(\"react-dom\"),t={stream:!0};function u(a,b){if(a){var c=a[b[0]];if(a=c[b[2]])c=a.name;else{a=c[\"*\"];if(!a)throw Error('Could not find the module \"'+b[0]+'\" in the React SSR Manifest. This is probably a bug in the React Server Components bundler.');c=b[2]}return 4===b.length?[a.id,a.chunks,c,1]:[a.id,a.chunks,c]}return b}var v=new Map;\nfunction w(a){var b=globalThis.__next_require__(a);if(\"function\"!==typeof b.then||\"fulfilled\"===b.status)return null;b.then(function(c){b.status=\"fulfilled\";b.value=c},function(c){b.status=\"rejected\";b.reason=c});return b}function x(){}\nfunction aa(a){for(var b=a[1],c=[],d=0;d<b.length;){var f=b[d++];b[d++];var h=v.get(f);if(void 0===h){h=__webpack_chunk_load__(f);c.push(h);var n=v.set.bind(v,f,null);h.then(n,x);v.set(f,h)}else null!==h&&c.push(h)}return 4===a.length?0===c.length?w(a[0]):Promise.all(c).then(function(){return w(a[0])}):0<c.length?Promise.all(c):null}\nfunction ba(a,b,c){if(null!==a)for(var d=1;d<b.length;d+=2){var f=c,h=y.current;if(h){var n=h.preinitScript,k=a.prefix+b[d];var m=a.crossOrigin;m=\"string\"===typeof m?\"use-credentials\"===m?m:\"\":void 0;n.call(h,k,{crossOrigin:m,nonce:f})}}}var y=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,z=Symbol.for(\"react.element\"),ca=Symbol.for(\"react.lazy\"),A=Symbol.iterator;\nfunction da(a){if(null===a||\"object\"!==typeof a)return null;a=A&&a[A]||a[\"@@iterator\"];return\"function\"===typeof a?a:null}var ea=Array.isArray,B=Object.getPrototypeOf,fa=Object.prototype,C=new WeakMap;function ha(a){return Number.isFinite(a)?0===a&&-Infinity===1/a?\"$-0\":a:Infinity===a?\"$Infinity\":-Infinity===a?\"$-Infinity\":\"$NaN\"}\nfunction E(a,b,c,d){function f(m,e){if(null===e)return null;if(\"object\"===typeof e){if(\"function\"===typeof e.then){null===k&&(k=new FormData);n++;var l=h++;e.then(function(p){p=JSON.stringify(p,f);var q=k;q.append(b+l,p);n--;0===n&&c(q)},function(p){d(p)});return\"$@\"+l.toString(16)}if(ea(e))return e;if(e instanceof FormData){null===k&&(k=new FormData);var g=k;m=h++;var D=b+m+\"_\";e.forEach(function(p,q){g.append(D+q,p)});return\"$K\"+m.toString(16)}if(e instanceof Map)return e=JSON.stringify(Array.from(e),\nf),null===k&&(k=new FormData),m=h++,k.append(b+m,e),\"$Q\"+m.toString(16);if(e instanceof Set)return e=JSON.stringify(Array.from(e),f),null===k&&(k=new FormData),m=h++,k.append(b+m,e),\"$W\"+m.toString(16);if(da(e))return Array.from(e);m=B(e);if(m!==fa&&(null===m||null!==B(m)))throw Error(\"Only plain objects, and a few built-ins, can be passed to Server Actions. Classes or null prototypes are not supported.\");return e}if(\"string\"===typeof e){if(\"Z\"===e[e.length-1]&&this[m]instanceof Date)return\"$D\"+e;\ne=\"$\"===e[0]?\"$\"+e:e;return e}if(\"boolean\"===typeof e)return e;if(\"number\"===typeof e)return ha(e);if(\"undefined\"===typeof e)return\"$undefined\";if(\"function\"===typeof e){e=C.get(e);if(void 0!==e)return e=JSON.stringify(e,f),null===k&&(k=new FormData),m=h++,k.set(b+m,e),\"$F\"+m.toString(16);throw Error(\"Client Functions cannot be passed directly to Server Functions. Only Functions passed from the Server can be passed back again.\");}if(\"symbol\"===typeof e){m=e.description;if(Symbol.for(m)!==e)throw Error(\"Only global symbols received from Symbol.for(...) can be passed to Server Functions. The symbol Symbol.for(\"+\n(e.description+\") cannot be found among global symbols.\"));return\"$S\"+m}if(\"bigint\"===typeof e)return\"$n\"+e.toString(10);throw Error(\"Type \"+typeof e+\" is not supported as an argument to a Server Function.\");}var h=1,n=0,k=null;a=JSON.stringify(a,f);null===k?c(a):(k.set(b+\"0\",a),0===n&&c(k))}var F=new WeakMap;\nfunction ia(a){var b,c,d=new Promise(function(f,h){b=f;c=h});E(a,\"\",function(f){if(\"string\"===typeof f){var h=new FormData;h.append(\"0\",f);f=h}d.status=\"fulfilled\";d.value=f;b(f)},function(f){d.status=\"rejected\";d.reason=f;c(f)});return d}\nfunction ja(a){var b=C.get(this);if(!b)throw Error(\"Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.\");var c=null;if(null!==b.bound){c=F.get(b);c||(c=ia(b),F.set(b,c));if(\"rejected\"===c.status)throw c.reason;if(\"fulfilled\"!==c.status)throw c;b=c.value;var d=new FormData;b.forEach(function(f,h){d.append(\"$ACTION_\"+a+\":\"+h,f)});c=d;b=\"$ACTION_REF_\"+a}else b=\"$ACTION_ID_\"+b.id;return{name:b,method:\"POST\",encType:\"multipart/form-data\",data:c}}\nfunction G(a,b){var c=C.get(this);if(!c)throw Error(\"Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.\");if(c.id!==a)return!1;var d=c.bound;if(null===d)return 0===b;switch(d.status){case \"fulfilled\":return d.value.length===b;case \"pending\":throw d;case \"rejected\":throw d.reason;default:throw\"string\"!==typeof d.status&&(d.status=\"pending\",d.then(function(f){d.status=\"fulfilled\";d.value=f},function(f){d.status=\"rejected\";d.reason=f})),d;}}\nfunction I(a,b,c){Object.defineProperties(a,{$$FORM_ACTION:{value:void 0===c?ja:function(){var d=C.get(this);if(!d)throw Error(\"Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.\");var f=d.bound;null===f&&(f=Promise.resolve([]));return c(d.id,f)}},$$IS_SIGNATURE_EQUAL:{value:G},bind:{value:J}});C.set(a,b)}var ka=Function.prototype.bind,la=Array.prototype.slice;\nfunction J(){var a=ka.apply(this,arguments),b=C.get(this);if(b){var c=la.call(arguments,1),d=null;d=null!==b.bound?Promise.resolve(b.bound).then(function(f){return f.concat(c)}):Promise.resolve(c);Object.defineProperties(a,{$$FORM_ACTION:{value:this.$$FORM_ACTION},$$IS_SIGNATURE_EQUAL:{value:G},bind:{value:J}});C.set(a,{id:b.id,bound:d})}return a}function ma(a,b,c){function d(){var f=Array.prototype.slice.call(arguments);return b(a,f)}I(d,{id:a,bound:null},c);return d}\nfunction K(a,b,c,d){this.status=a;this.value=b;this.reason=c;this._response=d}K.prototype=Object.create(Promise.prototype);K.prototype.then=function(a,b){switch(this.status){case \"resolved_model\":L(this);break;case \"resolved_module\":M(this)}switch(this.status){case \"fulfilled\":a(this.value);break;case \"pending\":case \"blocked\":case \"cyclic\":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};\nfunction na(a){switch(a.status){case \"resolved_model\":L(a);break;case \"resolved_module\":M(a)}switch(a.status){case \"fulfilled\":return a.value;case \"pending\":case \"blocked\":case \"cyclic\":throw a;default:throw a.reason;}}function N(a,b){for(var c=0;c<a.length;c++)(0,a[c])(b)}function O(a,b,c){switch(a.status){case \"fulfilled\":N(b,a.value);break;case \"pending\":case \"blocked\":case \"cyclic\":a.value=b;a.reason=c;break;case \"rejected\":c&&N(c,a.reason)}}\nfunction P(a,b){if(\"pending\"===a.status||\"blocked\"===a.status){var c=a.reason;a.status=\"rejected\";a.reason=b;null!==c&&N(c,b)}}function Q(a,b){if(\"pending\"===a.status||\"blocked\"===a.status){var c=a.value,d=a.reason;a.status=\"resolved_module\";a.value=b;null!==c&&(M(a),O(a,c,d))}}var R=null,S=null;\nfunction L(a){var b=R,c=S;R=a;S=null;var d=a.value;a.status=\"cyclic\";a.value=null;a.reason=null;try{var f=JSON.parse(d,a._response._fromJSON);if(null!==S&&0<S.deps)S.value=f,a.status=\"blocked\",a.value=null,a.reason=null;else{var h=a.value;a.status=\"fulfilled\";a.value=f;null!==h&&N(h,f)}}catch(n){a.status=\"rejected\",a.reason=n}finally{R=b,S=c}}\nfunction M(a){try{var b=a.value,c=globalThis.__next_require__(b[0]);if(4===b.length&&\"function\"===typeof c.then)if(\"fulfilled\"===c.status)c=c.value;else throw c.reason;var d=\"*\"===b[2]?c:\"\"===b[2]?c.__esModule?c.default:c:c[b[2]];a.status=\"fulfilled\";a.value=d}catch(f){a.status=\"rejected\",a.reason=f}}function T(a,b){a._chunks.forEach(function(c){\"pending\"===c.status&&P(c,b)})}function U(a,b){var c=a._chunks,d=c.get(b);d||(d=new K(\"pending\",null,null,a),c.set(b,d));return d}\nfunction oa(a,b,c,d){if(S){var f=S;d||f.deps++}else f=S={deps:d?0:1,value:null};return function(h){b[c]=h;f.deps--;0===f.deps&&\"blocked\"===a.status&&(h=a.value,a.status=\"fulfilled\",a.value=f.value,null!==h&&N(h,f.value))}}function pa(a){return function(b){return P(a,b)}}\nfunction qa(a,b){function c(){var f=Array.prototype.slice.call(arguments),h=b.bound;return h?\"fulfilled\"===h.status?d(b.id,h.value.concat(f)):Promise.resolve(h).then(function(n){return d(b.id,n.concat(f))}):d(b.id,f)}var d=a._callServer;I(c,b,a._encodeFormAction);return c}function W(a,b){a=U(a,b);switch(a.status){case \"resolved_model\":L(a)}switch(a.status){case \"fulfilled\":return a.value;default:throw a.reason;}}\nfunction ra(a,b,c,d){if(\"$\"===d[0]){if(\"$\"===d)return z;switch(d[1]){case \"$\":return d.slice(1);case \"L\":return b=parseInt(d.slice(2),16),a=U(a,b),{$$typeof:ca,_payload:a,_init:na};case \"@\":if(2===d.length)return new Promise(function(){});b=parseInt(d.slice(2),16);return U(a,b);case \"S\":return Symbol.for(d.slice(2));case \"F\":return b=parseInt(d.slice(2),16),b=W(a,b),qa(a,b);case \"Q\":return b=parseInt(d.slice(2),16),a=W(a,b),new Map(a);case \"W\":return b=parseInt(d.slice(2),16),a=W(a,b),new Set(a);\ncase \"I\":return Infinity;case \"-\":return\"$-0\"===d?-0:-Infinity;case \"N\":return NaN;case \"u\":return;case \"D\":return new Date(Date.parse(d.slice(2)));case \"n\":return BigInt(d.slice(2));default:d=parseInt(d.slice(1),16);a=U(a,d);switch(a.status){case \"resolved_model\":L(a);break;case \"resolved_module\":M(a)}switch(a.status){case \"fulfilled\":return a.value;case \"pending\":case \"blocked\":case \"cyclic\":return d=R,a.then(oa(d,b,c,\"cyclic\"===a.status),pa(d)),null;default:throw a.reason;}}}return d}\nfunction sa(){throw Error('Trying to call a function from \"use server\" but the callServer option was not implemented in your router runtime.');}function ta(a,b,c){var d=a._chunks,f=d.get(b);c=JSON.parse(c,a._fromJSON);var h=u(a._bundlerConfig,c);ba(a._moduleLoading,c[1],a._nonce);if(c=aa(h)){if(f){var n=f;n.status=\"blocked\"}else n=new K(\"blocked\",null,null,a),d.set(b,n);c.then(function(){return Q(n,h)},function(k){return P(n,k)})}else f?Q(f,h):d.set(b,new K(\"resolved_module\",h,null,a))}\nfunction ua(a){return function(b,c){return\"string\"===typeof c?ra(a,this,b,c):\"object\"===typeof c&&null!==c?(b=c[0]===z?{$$typeof:z,type:c[1],key:c[2],ref:null,props:c[3],_owner:null}:c,b):c}}function X(){throw Error(\"Server Functions cannot be called during initial render. This would create a fetch waterfall. Try to use a Server Component to pass data to Client Components instead.\");}\nfunction Y(a){var b=a.ssrManifest.moduleMap,c=a.ssrManifest.moduleLoading,d=a.encodeFormAction;a=\"string\"===typeof a.nonce?a.nonce:void 0;var f=new Map;b={_bundlerConfig:b,_moduleLoading:c,_callServer:void 0!==X?X:sa,_encodeFormAction:d,_nonce:a,_chunks:f,_stringDecoder:new TextDecoder,_fromJSON:null,_rowState:0,_rowID:0,_rowTag:0,_rowLength:0,_buffer:[]};b._fromJSON=ua(b);return b}\nfunction Z(a,b){function c(h){var n=h.value;if(h.done)T(a,Error(\"Connection closed.\"));else{var k=0,m=a._rowState,e=a._rowID,l=a._rowTag,g=a._rowLength;h=a._buffer;for(var D=n.length;k<D;){var p=-1;switch(m){case 0:p=n[k++];58===p?m=1:e=e<<4|(96<p?p-87:p-48);continue;case 1:m=n[k];84===m?(l=m,m=2,k++):64<m&&91>m?(l=m,m=3,k++):(l=0,m=3);continue;case 2:p=n[k++];44===p?m=4:g=g<<4|(96<p?p-87:p-48);continue;case 3:p=n.indexOf(10,k);break;case 4:p=k+g,p>n.length&&(p=-1)}var q=n.byteOffset+k;if(-1<p){k=\nnew Uint8Array(n.buffer,q,p-k);g=a;q=l;var V=g._stringDecoder;l=\"\";for(var H=0;H<h.length;H++)l+=V.decode(h[H],t);l+=V.decode(k);switch(q){case 73:ta(g,e,l);break;case 72:e=l[0];l=l.slice(1);g=JSON.parse(l,g._fromJSON);if(l=y.current)switch(e){case \"D\":l.prefetchDNS(g);break;case \"C\":\"string\"===typeof g?l.preconnect(g):l.preconnect(g[0],g[1]);break;case \"L\":e=g[0];k=g[1];3===g.length?l.preload(e,k,g[2]):l.preload(e,k);break;case \"m\":\"string\"===typeof g?l.preloadModule(g):l.preloadModule(g[0],g[1]);\nbreak;case \"S\":\"string\"===typeof g?l.preinitStyle(g):l.preinitStyle(g[0],0===g[1]?void 0:g[1],3===g.length?g[2]:void 0);break;case \"X\":\"string\"===typeof g?l.preinitScript(g):l.preinitScript(g[0],g[1]);break;case \"M\":\"string\"===typeof g?l.preinitModuleScript(g):l.preinitModuleScript(g[0],g[1])}break;case 69:l=JSON.parse(l);k=l.digest;l=Error(\"An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.\");\nl.stack=\"Error: \"+l.message;l.digest=k;k=g._chunks;(q=k.get(e))?P(q,l):k.set(e,new K(\"rejected\",null,l,g));break;case 84:g._chunks.set(e,new K(\"fulfilled\",l,null,g));break;case 68:case 87:throw Error(\"Failed to read a RSC payload created by a development version of React on the server while using a production version on the client. Always use matching versions on the server and the client.\");default:k=g._chunks,(q=k.get(e))?(g=q,e=l,\"pending\"===g.status&&(l=g.value,k=g.reason,g.status=\"resolved_model\",\ng.value=e,null!==l&&(L(g),O(g,l,k)))):k.set(e,new K(\"resolved_model\",l,null,g))}k=p;3===m&&k++;g=e=l=m=0;h.length=0}else{n=new Uint8Array(n.buffer,q,n.byteLength-k);h.push(n);g-=n.byteLength;break}}a._rowState=m;a._rowID=e;a._rowTag=l;a._rowLength=g;return f.read().then(c).catch(d)}}function d(h){T(a,h)}var f=b.getReader();f.read().then(c).catch(d)}exports.createFromFetch=function(a,b){var c=Y(b);a.then(function(d){Z(c,d.body)},function(d){T(c,d)});return U(c,0)};\nexports.createFromReadableStream=function(a,b){b=Y(b);Z(b,a);return U(b,0)};exports.createServerReference=function(a){return ma(a,X)};exports.encodeReply=function(a){return new Promise(function(b,c){E(a,\"\",b,c)})};\n\n//# sourceMappingURL=react-server-dom-webpack-client.edge.production.min.js.map\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-server-dom-webpack-client.edge.production.min.js');\n} else {\n  module.exports = require('./cjs/react-server-dom-webpack-client.edge.development.js');\n}\n", "import { ConfigProps } from '@/types'\r\nimport themes from 'daisyui/src/theming/themes'\r\n\r\nconst config: ConfigProps = {\r\n\t// REQUIRED\r\n\tappName: 'Javian Picardo',\r\n\t// REQUIRED: a short description of your app for SEO tags (can be overwritten)\r\n\tappDescription:\r\n\t\t'Portal for the services provided by <PERSON><PERSON><PERSON>.',\r\n\t// REQUIRED (no https://, not trialing slash at the end, just the naked domain)\r\n\tdomainName: 'www.javianpicardo.com',\r\n\t// Add required robots configuration\r\n\trobots: {\r\n\t\tindex: true,\r\n\t\tfollow: true,\r\n\t\t'max-image-preview': 'large',\r\n\t\t'max-snippet': -1,\r\n\t\tgoogleBot: {\r\n\t\t\tindex: true,\r\n\t\t\tfollow: true,\r\n\t\t\t'max-image-preview': 'large',\r\n\t\t\t'max-snippet': -1,\r\n\t\t},\r\n\t},\r\n\tstripe: {\r\n\t\t// Create multiple products in your Stripe dashboard, then add them here. You can add as many plans as you want, just make sure to add the priceId\r\n\t\tproducts: [\r\n\t\t\t{\r\n\t\t\t\ttype: 'one-time',\r\n\t\t\t\ttitle: 'Quick Shoots',\r\n\t\t\t\tproductId: 'prod_S0c6NHMe0vINXA',\r\n\t\t\t\tsubtitle: 'Professional photography session',\r\n\t\t\t\tprice: 300,\r\n\t\t\t\tisBest: false,\r\n\t\t\t\tlinkTitle: 'Book Quick Shoots',\r\n\t\t\t\tfeaturesTitle: 'Package Features',\r\n\t\t\t\tpriceId: 'price_1R6aslP82YH9JfOlJZcweC4d',\r\n\t\t\t\tfeatures: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: 'One hour of footage time',\r\n\t\t\t\t\t\tdisabled: false,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '50 edited photos',\r\n\t\t\t\t\t\tdisabled: false,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: 'Help with posing',\r\n\t\t\t\t\t\tdisabled: false,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: 'Location list to pick from',\r\n\t\t\t\t\t\tdisabled: false,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '7 business days turn around time',\r\n\t\t\t\t\t\tdisabled: false,\r\n\t\t\t\t\t},\r\n\t\t\t\t],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\ttype: 'one-time',\r\n\t\t\t\ttitle: 'Mini Shoots',\r\n\t\t\t\tproductId: 'prod_S0c9zDLLtK9rnS',\r\n\t\t\t\tsubtitle: 'Perfect for professional portraits',\r\n\t\t\t\tprice: 150,\r\n\t\t\t\tisBest: true,\r\n\t\t\t\tlinkTitle: 'Book Mini Shoots',\r\n\t\t\t\tfeaturesTitle: 'Package Features',\r\n\t\t\t\tpriceId: 'price_1R6avXP82YH9JfOlIGpAfwjU',\r\n\t\t\t\tfeatures: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '30 minutes of footage time',\r\n\t\t\t\t\t\tdisabled: false,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '25 edited photos',\r\n\t\t\t\t\t\tdisabled: false,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: 'Help with posing',\r\n\t\t\t\t\t\tdisabled: false,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: 'Location list to pick from',\r\n\t\t\t\t\t\tdisabled: false,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '7 business days turn around time',\r\n\t\t\t\t\t\tdisabled: false,\r\n\t\t\t\t\t},\r\n\t\t\t\t],\r\n\t\t\t},\r\n\t\t],\r\n\t},\r\n\tcolors: {\r\n\t\t// REQUIRED — The DaisyUI theme to use (added to the main layout.js). Leave blank for default (light & dark mode). If you any other theme than light/dark, you need to add it in config.tailwind.js in daisyui.themes.\r\n\t\ttheme: 'light',\r\n\t\t// REQUIRED — This color will be reflected on the whole app outside of the document (loading bar, Chrome tabs, etc..). By default it takes the primary color from your DaisyUI theme (make sure to update your the theme name after \"data-theme=\")\r\n\t\t// OR you can just do this to use a custom color: main: \"#f37055\". HEX only.\r\n\t\tmain: themes['light']['primary'],\r\n\t},\r\n\tresend: {\r\n\t\t// REQUIRED — Email 'From' field to be used when sending other emails, like abandoned carts, updates etc..\r\n\t\tfromAdmin: `Javian Picardo <<EMAIL>>`,\r\n\t\t// Email shown to customer if need support. Leave empty if not needed => if empty, set up Crisp above, otherwise you won't be able to offer customer support.\"\r\n\t\tsupportEmail: '<EMAIL>',\r\n\t\t// When someone replies to supportEmail sent by the app, forward it to the email below (otherwise it's lost). If you set supportEmail to empty, this will be ignored.\r\n\t\tforwardRepliesTo: '<EMAIL>',\r\n\t\tsubjects: {\r\n\t\t\tthankYou: 'Welcome to Javian Picardo',\r\n\t\t},\r\n\t},\r\n}\r\n\r\nexport default config\r\n", "import { PrismaClient } from \"@prisma/client\";\r\n\r\nconst prisma = new PrismaClient();\r\n\r\nexport default prisma;\r\n", "import React from 'react';\r\n\r\nconst InvoiceTemplate = (props: any) => {\r\n    return (\r\n        <div className=\"max-w-3xl mx-auto p-8 bg-white\">\r\n            <div className=\"mb-8\">\r\n                <h1 className=\"text-3xl font-bold text-gray-900 mb-6\">Invoice</h1>\r\n\r\n                <div className=\"grid grid-cols-2 gap-4\">\r\n                    <div className=\"space-y-2\">\r\n                        <p className=\"text-sm text-gray-600\">\r\n                            <span className=\"font-medium\">Invoice number: </span>\r\n                            {props.id}\r\n                        </p>\r\n                        <p className=\"text-sm text-gray-600\">\r\n                            <span className=\"font-medium\">Date paid: </span>\r\n                            {props.paid_date}\r\n                        </p>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Address Section */}\r\n            <div className=\"grid grid-cols-2 gap-8 mb-8\">\r\n                <div>\r\n                    <h2 className=\"text-lg font-semibold mb-3\">From:</h2>\r\n                    <div className=\"text-sm text-gray-600 space-y-1\">\r\n                        <p className=\"font-medium\">DB2 Software Ltd</p>\r\n                        <p>38 Fawkner Way</p>\r\n                        <p>Stanford In The Vale</p>\r\n                        <p>Faringdon</p>\r\n                        <p>United Kingdom</p>\r\n                        <p>SN7 8FF</p>\r\n                    </div>\r\n                </div>\r\n\r\n                <div>\r\n                    <h2 className=\"text-lg font-semibold mb-3\">To:</h2>\r\n                    <div className=\"text-sm text-gray-600 h-24 border border-dashed border-gray-300 rounded-md p-3\">\r\n                        <span>\r\n                            {props.data}\r\n                        </span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Invoice Table */}\r\n            <div className=\"mb-8\">\r\n                <table className=\"w-full\">\r\n                    <thead>\r\n                    <tr className=\"border-b border-gray-200\">\r\n                        <th className=\"py-3 text-left font-semibold text-gray-700\">Description</th>\r\n                        <th className=\"py-3 text-center font-semibold text-gray-700\">Qty</th>\r\n                        <th className=\"py-3 text-right font-semibold text-gray-700\">Unit price</th>\r\n                        <th className=\"py-3 text-right font-semibold text-gray-700\">Amount</th>\r\n                    </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                    <tr className=\"border-b border-gray-200\">\r\n                        <td className=\"py-4 text-sm text-gray-600\">Nextjs code boilerplate</td>\r\n                        <td className=\"py-4 text-center text-sm text-gray-600\">1</td>\r\n                        <td className=\"py-4 text-right text-sm text-gray-600\">{props.price}</td>\r\n                        <td className=\"py-4 text-right text-sm text-gray-600\">{props.price}</td>\r\n                    </tr>\r\n                    </tbody>\r\n                </table>\r\n            </div>\r\n\r\n            {/* Totals */}\r\n            <div className=\"border-t border-gray-200 pt-4\">\r\n                <div className=\"space-y-2\">\r\n                    <div className=\"flex justify-between text-sm text-gray-600\">\r\n                        <span>Subtotal:</span>\r\n                        <span></span>\r\n                    </div>\r\n                    <div className=\"flex justify-between text-sm font-medium text-gray-800\">\r\n                        <span>Total:</span>\r\n                        <span></span>\r\n                    </div>\r\n                    <div className=\"flex justify-between text-sm font-medium text-green-600\">\r\n                        <span>Total amount paid:</span>\r\n                        <span></span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default InvoiceTemplate;", "import React from 'react';\r\nimport { Html, Head, Body, Container, Section, Text, Link, Preview } from '@react-email/components';\r\n\r\ninterface EmailTemplateProps {\r\n  email: string;\r\n}\r\n\r\nconst styles = {\r\n  body: {\r\n    backgroundColor: '#f3f4f6',\r\n    fontFamily: 'Arial, sans-serif',\r\n  },\r\n  container: {\r\n    margin: '0 auto',\r\n    padding: '20px 0 48px',\r\n    width: '580px',\r\n  },\r\n  section: {\r\n    backgroundColor: '#ffffff',\r\n    borderRadius: '5px',\r\n    padding: '40px',\r\n  },\r\n  h1: {\r\n    color: '#3b82f6',\r\n    fontSize: '32px',\r\n    fontWeight: 'bold',\r\n    textAlign: 'center' as const,\r\n    margin: '0 0 20px',\r\n  },\r\n  text: {\r\n    color: '#374151',\r\n    fontSize: '16px',\r\n    lineHeight: '24px',\r\n    textAlign: 'center' as const,\r\n  },\r\n  button: {\r\n    backgroundColor: '#3b82f6',\r\n    borderRadius: '5px',\r\n    color: '#ffffff',\r\n    display: 'inline-block',\r\n    fontSize: '16px',\r\n    fontWeight: 'bold',\r\n    padding: '12px 24px',\r\n    textDecoration: 'none',\r\n    textAlign: 'center' as const,\r\n    marginTop: '32px',\r\n  },\r\n};\r\n\r\nconst ThankYouTemplate: React.FC<Readonly<EmailTemplateProps>> = ({\r\n  email,\r\n}) => (\r\n  <Html>\r\n    <Head />\r\n    <Preview>Welcome to our community!</Preview>\r\n    <Body style={styles.body}>\r\n      <Container style={styles.container}>\r\n        <Section style={styles.section}>\r\n          <Text style={styles.h1}>Welcome aboard!</Text>\r\n          <Text style={styles.text}>\r\n            We&apos;re thrilled to have you join us, {email}!\r\n          </Text>\r\n          <Text style={styles.text}>\r\n            Get ready for an amazing journey. We can&apos;t wait to see what you&apos;ll achieve with us.\r\n          </Text>\r\n          <Section style={{ textAlign: 'center' }}>\r\n            <Link\r\n              href=\"http://localhost:3000/dashboard\"\r\n              style={styles.button}\r\n            >\r\n              Get Started\r\n            </Link>\r\n          </Section>\r\n        </Section>\r\n      </Container>\r\n    </Body>\r\n  </Html>\r\n);\r\n\r\nexport default ThankYouTemplate;", "import InvoiceTemplate from '@/components/email-templates/Invoice'\r\nimport ThankYouTemplate from '@/components/email-templates/ThanksYouTemplate'\r\nimport config from '@/config'\r\nimport prisma from '@/libs/prisma'\r\nimport { Resend } from 'resend'\r\n\r\nclass ResendService {\r\n\tprivate resend = new Resend(process.env.RESEND_API_KEY)\r\n\r\n\tpublic async sendThanksYouEmail(toMail: string) {\r\n\t\tconst { data, error } = await this.resend.emails.send({\r\n\t\t\tfrom: config.resend.fromAdmin,\r\n\t\t\tto: [toMail],\r\n\t\t\treplyTo: config.resend.forwardRepliesTo,\r\n\t\t\tsubject: config.resend.subjects.thankYou,\r\n\t\t\treact: ThankYouTemplate({ email: toMail }),\r\n\t\t})\r\n\r\n\t\tif (error) {\r\n\t\t\tthrow error\r\n\t\t}\r\n\r\n\t\treturn data\r\n\t}\r\n\r\n\tpublic async sendInvoice(toMail: string, renderData: any) {\r\n\t\tconst { data, error } = await this.resend.emails.send({\r\n\t\t\tfrom: config.resend.fromAdmin,\r\n\t\t\tto: [toMail],\r\n\t\t\treplyTo: config.resend.forwardRepliesTo,\r\n\t\t\tsubject: 'Invoice: ' + renderData.id,\r\n\t\t\treact: InvoiceTemplate(renderData),\r\n\t\t})\r\n\r\n\t\tif (error) {\r\n\t\t\tthrow error\r\n\t\t}\r\n\r\n\t\treturn data\r\n\t}\r\n\r\n\tpublic async addNewEmailAddress(email: string) {\r\n\t\tconst audience = await this.upsertAudience()\r\n\t\treturn this.resend.contacts.create({\r\n\t\t\temail,\r\n\t\t\tunsubscribed: false,\r\n\t\t\taudienceId: audience.resend_id,\r\n\t\t})\r\n\t}\r\n\r\n\tprivate async upsertAudience() {\r\n\t\tconst audience = await prisma.audiences.findFirst()\r\n\r\n\t\tif (audience) {\r\n\t\t\treturn audience\r\n\t\t}\r\n\r\n\t\tconst resendAudience = await this.resend.audiences.create({\r\n\t\t\tname: 'Waiting List',\r\n\t\t})\r\n\t\tconst {\r\n\t\t\tdata: { id, name },\r\n\t\t} = resendAudience\r\n\t\treturn prisma.audiences.create({\r\n\t\t\tdata: {\r\n\t\t\t\tresend_id: id,\r\n\t\t\t\tname,\r\n\t\t\t},\r\n\t\t})\r\n\t}\r\n}\r\n\r\nexport const resendService = new ResendService()\r\n", "import Stripe from \"stripe\";\r\n\r\nclass StripeService {\r\n  private stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {\r\n    typescript: true,\r\n  });\r\n\r\n  // This is used to create Customer Portal sessions, so users can manage their subscriptions (payment methods, cancel, etc..)\r\n  public async createCustomerPortal(customerId: string, returnUrl: string): Promise<string> {\r\n    const portalSession = await this.stripe.billingPortal.sessions.create({\r\n      customer: customerId,\r\n      return_url: returnUrl,\r\n    });\r\n    return portalSession.url;\r\n  }\r\n\r\n// This is used to get the uesr checkout session and populate the data so we get the planId the user subscribed to\r\n  public async findCheckoutSession(sessionId: string) {\r\n  try {\r\n  const session = await this.stripe.checkout.sessions.retrieve(sessionId, {\r\n    expand: [\"line_items\"],\r\n  });\r\n  return session;\r\n} catch (e) {\r\n  console.error(e);\r\n  return null;\r\n   }\r\n  }\r\n\r\n  public async getSubscription(subId: string) {\r\n    return this.stripe.subscriptions.retrieve(subId)\r\n  }\r\n\r\n  public async getCheckoutSession(csId: string) {\r\n    return this.stripe.checkout.sessions.retrieve(csId)\r\n  }\r\n\r\n}\r\n\r\nexport const stripeService = new StripeService()"], "names": ["module", "exports", "require", "stripe", "Stripe", "process", "env", "STRIPE_SECRET_KEY", "typescript", "webhookSecret", "STRIPE_WEBHOOK_SECRET", "registerServerReference", "id", "action", "server_edge", "Uint8Array", "TextEncoder", "Symbol", "for", "TextDecoder", "external_node_events_namespaceObject", "emailEvents", "EventEmitter", "on", "email", "resendService", "sendThanksYouEmail", "apiVersion", "getSubscriptionByUserId", "userId", "prisma", "subscription", "<PERSON><PERSON><PERSON><PERSON>", "where", "user_clerk_id", "processCheckoutSuccessWebhook", "body", "event", "stripeObject", "data", "object", "session", "stripeService", "findCheckoutSession", "customerId", "customer", "priceId", "line_items", "price", "configFile", "products", "find", "p", "NextResponse", "json", "error", "status", "customers", "retrieve", "console", "prices", "Response", "product", "subsType", "metadata", "subscription_type", "customerEmail", "users", "clerkClient", "getUserList", "emailAddress", "length", "user", "subsId", "upsert", "create", "last_stripe_cs_id", "sub_stripe_id", "sub_status", "user_email", "sub_type", "stripe_customer_id", "update", "emit", "processSubscriptonDelete", "subscriptions", "prismaSub", "processInvoicePaid", "toString", "actions", "i", "dynamic", "runtime", "POST", "req", "textParsedBody", "text", "signature", "headers", "get", "log", "webhooks", "constructEvent", "err", "message", "type", "JSON", "parse", "e", "routeModule", "module_compiled", "AppRouteRouteModule", "definition", "kind", "route_kind", "x", "APP_ROUTE", "page", "pathname", "filename", "bundlePath", "resolvedPagePath", "nextConfigOutput", "userland", "route_namespaceObject", "requestAsyncStorage", "staticGenerationAsyncStorage", "serverHooks", "originalPathname", "patchFetch", "patch_fetch", "XH", "ComponentMod", "route_next_edge_ssr_entry_namespaceObject", "next_edge_app_route_loaderabsolutePagePath_private_next_app_dir_2Fapi_2Fwebhook_2Fstripe_2Froute_ts_page_2Fapi_2Fwebhook_2Fstripe_2Froute_appDirLoader_bmV4dC1hcHAtbG9hZGVyP25hbWU9YXBwJTJGYXBpJTJGd2ViaG9vayUyRnN0cmlwZSUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGd2ViaG9vayUyRnN0cmlwZSUyRnJvdXRlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGd2ViaG9vayUyRnN0cmlwZSUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDUHJvamVjdHMlNUNQZXJzb25hbFBvcnRhbCU1Q3NyYyU1Q2FwcCZhcHBQYXRocz0lMkZhcGklMkZ3ZWJob29rJTJGc3RyaXBlJTJGcm91dGUmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0Qh_nextConfigOutput_preferredRegion_middlewareConfig_e30_3D_", "edge_route_module_wrapper", "a", "wrap", "r", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "Di<PERSON>atcher", "A", "iterator", "ea", "Array", "isArray", "B", "Object", "getPrototypeOf", "fa", "prototype", "C", "WeakMap", "F", "G", "b", "c", "Error", "d", "bound", "value", "reason", "then", "f", "ka", "Function", "bind", "la", "slice", "J", "apply", "arguments", "call", "Promise", "resolve", "concat", "defineProperties", "$$FORM_ACTION", "$$IS_SIGNATURE_EQUAL", "set", "K", "_response", "N", "L", "M", "push", "R", "S", "_fromJSON", "deps", "h", "n", "globalThis", "__next_require__", "__esModule", "default", "__webpack_require__", "__WEBPACK_DEFAULT_EXPORT__", "appName", "appDescription", "domainName", "robots", "index", "follow", "googleBot", "title", "productId", "subtitle", "isBest", "linkTitle", "featuresTitle", "features", "disabled", "colors", "theme", "main", "themes", "resend", "fromAdmin", "supportEmail", "forwardRepliesTo", "subjects", "thankYou", "PrismaClient", "Invoice", "jsx_runtime", "jsxs", "div", "className", "jsx", "h1", "span", "props", "paid_date", "h2", "table", "thead", "tr", "th", "tbody", "td", "styles", "backgroundColor", "fontFamily", "container", "margin", "padding", "width", "section", "borderRadius", "color", "fontSize", "fontWeight", "textAlign", "lineHeight", "button", "display", "textDecoration", "marginTop", "ThanksYouTemplate", "Html", "Head", "Preview", "Body", "style", "Container", "Section", "Text", "Link", "href", "ResendService", "toMail", "emails", "send", "from", "config", "to", "replyTo", "subject", "react", "ThankYouTemplate", "sendInvoice", "renderData", "InvoiceTemplate", "addNewEmailAddress", "audience", "upsertAudience", "contacts", "unsubscribed", "audienceId", "resend_id", "audiences", "name", "Resend", "RESEND_API_KEY", "StripeService", "createCustomerPortal", "returnUrl", "portalSession", "billingPortal", "sessions", "return_url", "url", "sessionId", "checkout", "expand", "getSubscription", "subId", "getCheckoutSession", "csId"], "sourceRoot": ""}