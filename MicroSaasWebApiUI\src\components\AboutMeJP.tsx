'use client'

import React, { useState } from 'react'
import { Logo } from '@/components'
import Link from 'next/link'
import { DATA } from "@/data/aboubemeJP";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const AboutMeJP = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  return (
    <div className="container relative mx-auto scroll-my-12 overflow-auto px-4 py-8">
      <div className="max-w-7xl">
        <div className="max-w-6xl">
          {/* <h1 className="text-4xl font-bold tracking-tight text-zinc-800 sm:text-5xl">
            {DATA.name}
          </h1> */}
          <div className="text-left mb-8 max-w-6xl px-0">
            <h1 className="text-3xl md:text-4xl font-bold tracking-tight mb-3 text-blue-600">
              {DATA.name}
            </h1>
            <h2 className="text-xl md:text-2xl font-semibold tracking-tight text-zinc-700 mb-4">
              {DATA.description}
            </h2>
            <div className="mt-4 text-base text-black dark:text-white leading-normal">
              <p>{DATA.summary}</p>
            </div>
          </div>
          <div className="mt-6 flex flex-wrap gap-4 mb-8 max-w-6xl px-0">
            {Object.entries(DATA.contact.social).map(([key, social]) => (
              <Link
                key={key}
                href={social.url}
                className="group flex items-center gap-2 rounded-full bg-zinc-100 px-4 py-1.5 h-8 text-sm font-medium text-zinc-800 transition hover:bg-zinc-200"
                target="_blank"
                rel="noopener noreferrer"
              >
                <social.icon className="h-5 w-5 text-blue-600" />
                <span>{social.name}</span>
              </Link>
            ))}
            <a
              href="/doc/CVJavianPicardo2025.pdf"
              download="CVJavianPicardo2025.pdf"
              className="inline-flex items-center gap-2 rounded-full bg-[#006fee] px-5 py-1.5 h-8 text-sm font-medium text-white transition hover:bg-blue-700"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Download CV
            </a>
          </div>
        </div>

        <div className="mt-6">
          <div className="flex flex-col gap-12 max-w-6xl">
            {/* Work Experience Section */}
            <section>
              <h2 className="text-2xl font-bold tracking-tight text-black dark:text-white border-b-2 border-blue-600 pb-2 inline-block">
                Work Experience
              </h2>
              <div className="mt-6 space-y-4">
                {DATA.work.map((job) => (
                  <Card key={job.company} className="p-6 hover:shadow-lg transition-shadow duration-300 border-l-4 border-blue-600">
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                      <Avatar className="h-12 w-12">
                        <AvatarImage
                          src={job.logoUrl}
                          alt={job.company}
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                          }}
                        />
                        <AvatarFallback className="bg-gray-100 text-gray-600">
                          {job.company[0]}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 mt-2 sm:mt-0">
                        <h3 className="font-semibold text-black dark:text-white">{job.title}</h3>
                        <div className="mb-1">
                          <Link href={job.href} className="text-sm text-black dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 block mb-1">
                            {job.company}
                          </Link>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {job.badges.map((badge) => (
                              <Badge key={badge} variant="secondary" className="text-xs">
                                {badge}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <p className="text-sm text-zinc-500">
                          {job.start} - {job.end} • {job.location}
                        </p>
                      </div>
                    </div>
                    <div className="mt-4 text-sm text-black dark:text-gray-300">
                      <p className="mb-3">
                        {job.description
                          // Replace abbreviations with placeholders to avoid splitting on them
                          .replace(/ASP\./g, 'ASP_PLACEHOLDER')
                          .replace(/C#\./g, 'CSHARP_PLACEHOLDER')
                          .replace(/Pvt\./g, 'PVT_PLACEHOLDER')
                          .replace(/Ltd\./g, 'LTD_PLACEHOLDER')
                          .replace(/\.NET/g, 'NET_PLACEHOLDER')

                          // Split by periods
                          .split('.')
                          // Get the first sentence
                          .slice(0, 1)
                          .join('.')
                          .trim()
                          // Restore placeholders
                          .replace(/ASP_PLACEHOLDER/g, 'ASP.')
                          .replace(/CSHARP_PLACEHOLDER/g, 'C#.')
                          .replace(/PVT_PLACEHOLDER/g, 'Pvt.')
                          .replace(/LTD_PLACEHOLDER/g, 'Ltd.')
                          .replace(/NET_PLACEHOLDER/g, '.NET')
 + '.'}
                      </p>
                      <ul className="list-disc pl-5 space-y-1">
                        {job.description
                          // Replace abbreviations with placeholders to avoid splitting on them
                          .replace(/ASP\./g, 'ASP_PLACEHOLDER')
                          .replace(/C#\./g, 'CSHARP_PLACEHOLDER')
                          .replace(/Pvt\./g, 'PVT_PLACEHOLDER')
                          .replace(/Ltd\./g, 'LTD_PLACEHOLDER')
                          .replace(/\.NET/g, 'NET_PLACEHOLDER')

                          // Split by periods
                          .split('.')
                          // Skip the first sentence (already displayed above)
                          .slice(1)
                          // Filter out empty strings
                          .filter(s => s.trim())
                          // Map to list items
                          .map((sentence, index) => {
                            // Restore placeholders
                            const processedSentence = sentence.trim()
                              .replace(/ASP_PLACEHOLDER/g, 'ASP.')
                              .replace(/CSHARP_PLACEHOLDER/g, 'C#.')
                              .replace(/PVT_PLACEHOLDER/g, 'Pvt.')
                              .replace(/LTD_PLACEHOLDER/g, 'Ltd.')
                              .replace(/NET_PLACEHOLDER/g, '.NET')


                            return (
                              <li key={index} className="max-w-4xl">
                                {processedSentence.endsWith('.') ? processedSentence : `${processedSentence}.`}
                              </li>
                            );
                          })}
                      </ul>
                    </div>
                  </Card>
                ))}
              </div>
            </section>

            {/* Education Section */}
            <section>
              <h2 className="text-2xl font-bold tracking-tight text-black dark:text-white border-b-2 border-blue-600 pb-2 inline-block">
                Education
              </h2>
              <div className="mt-6 space-y-4">
                {DATA.education.map((edu) => (
                  <Card key={edu.school} className="p-6 hover:shadow-lg transition-shadow duration-300 border-l-4 border-green-600">
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                      <Avatar className="h-12 w-12">
                        <AvatarImage
                          src={edu.logoUrl}
                          alt={edu.school}
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                          }}
                        />
                        <AvatarFallback className="bg-gray-100 text-gray-600">
                          {edu.school[0]}
                        </AvatarFallback>
                      </Avatar>
                      <div className="mt-2 sm:mt-0">
                        <h3 className="font-semibold">{edu.school}</h3>
                        <p className="text-sm text-zinc-600">{edu.degree}</p>
                        <p className="text-sm text-zinc-500">
                          {edu.start} - {edu.end} • {edu.location}
                        </p>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </section>

            {/* Skills Section */}
            <section>
              <h2 className="text-2xl font-bold tracking-tight text-black dark:text-white border-b-2 border-blue-600 pb-2 inline-block">
                Core Competencies
              </h2>
              <div className="mt-6 flex flex-wrap gap-2">
                {DATA.coreCompetencies.map((competency) => (
                  <Badge key={competency} variant="secondary" className="bg-blue-100 text-blue-800 hover:bg-blue-200 px-3 py-1.5 text-sm">
                    {competency}
                  </Badge>
                ))}
              </div>
            </section>

            {/* Skills Section */}
            <section>
              <h2 className="text-2xl font-bold tracking-tight text-black dark:text-white border-b-2 border-blue-600 pb-2 inline-block">
                Skills
              </h2>
              <div className="mt-6 flex flex-wrap gap-2">
                {DATA.skills.map((skill) => (
                  <Badge key={skill} variant="secondary" className="px-3 py-1.5 text-sm">
                    {skill}
                  </Badge>
                ))}
              </div>
            </section>

            {/* Certifications Section */}
            <section>
              <h2 className="text-2xl font-bold tracking-tight text-black dark:text-white border-b-2 border-blue-600 pb-2 inline-block">
                Certifications
              </h2>
              <div className="mt-6 space-y-4">
                {DATA.certifications.map((cert) => (
                  <Card key={cert.title} className="p-6 hover:shadow-lg transition-shadow duration-300 border-l-4 border-purple-600">
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                      <Avatar className="h-12 w-12">
                        <AvatarImage
                          src={cert.logoUrl}
                          alt={cert.organization}
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                          }}
                        />
                        <AvatarFallback className="bg-gray-100 text-gray-600">
                          {cert.organization[0]}
                        </AvatarFallback>
                      </Avatar>
                      <div className="mt-2 sm:mt-0">
                        <h3 className="font-semibold">{cert.title}</h3>
                        <p className="text-sm text-zinc-600">{cert.organization}</p>
                        <p className="text-sm text-zinc-500">{cert.date}</p>
                        {cert.id && (
                          <p className="text-sm text-zinc-500">ID: {cert.id}</p>
                        )}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </section>

            {/* Interests Section */}
            {DATA.interests && DATA.interests.length > 0 && (
              <section>
                <h2 className="text-2xl font-bold tracking-tight text-black dark:text-white border-b-2 border-blue-600 pb-2 inline-block">
                  Interests
                </h2>
                <div className="mt-6 flex flex-wrap gap-2">
                  {DATA.interests.map((interest) => (
                    <Badge key={interest} variant="secondary" className="bg-purple-100 text-purple-800 hover:bg-purple-200 px-3 py-1.5 text-sm">
                      {interest}
                    </Badge>
                  ))}
                </div>
              </section>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default AboutMeJP