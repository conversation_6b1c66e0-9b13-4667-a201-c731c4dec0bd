(()=>{var e={};e.id=244,e.ids=[244],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},85807:e=>{"use strict";e.exports=require("module")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},6005:e=>{"use strict";e.exports=require("node:crypto")},86521:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c}),s(56754),s(8130),s(7629),s(12523);var t=s(23191),i=s(88716),a=s(37922),o=s.n(a),n=s(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(r,l);let c=["",{children:["security-policy",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,56754)),"C:\\Projects\\PersonalPortal\\src\\app\\security-policy\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e),async e=>(await Promise.resolve().then(s.bind(s,71150))).default(e)],apple:[async e=>(await Promise.resolve().then(s.bind(s,63601))).default(e)],openGraph:[async e=>(await Promise.resolve().then(s.bind(s,51922))).default(e)],twitter:[async e=>(await Promise.resolve().then(s.bind(s,40680))).default(e)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,8130)),"C:\\Projects\\PersonalPortal\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,7629)),"C:\\Projects\\PersonalPortal\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"C:\\Projects\\PersonalPortal\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e),async e=>(await Promise.resolve().then(s.bind(s,71150))).default(e)],apple:[async e=>(await Promise.resolve().then(s.bind(s,63601))).default(e)],openGraph:[async e=>(await Promise.resolve().then(s.bind(s,51922))).default(e)],twitter:[async e=>(await Promise.resolve().then(s.bind(s,40680))).default(e)],manifest:void 0}}],d=["C:\\Projects\\PersonalPortal\\src\\app\\security-policy\\page.tsx"],u="/security-policy/page",p={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/security-policy/page",pathname:"/security-policy",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},35303:()=>{},56754:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>n,metadata:()=>o});var t=s(19510),i=s(7264),a=s(1542);let o=(0,i.A)({title:`Security Policy | ${a.Z.appName}`,description:"Security policy and vulnerability disclosure for javianpicardo.com",canonicalUrlRelative:"/security-policy"});function n(){return(0,t.jsxs)("div",{className:"container mx-auto px-4 py-16 max-w-4xl",children:[t.jsx("h1",{className:"text-3xl font-bold mb-8",children:"Security Policy"}),(0,t.jsxs)("section",{className:"mb-8",children:[t.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"Reporting Security Issues"}),t.jsx("p",{className:"mb-4",children:"The security of javianpicardo.com is a top priority. If you believe you've found a security vulnerability in our website, we encourage you to let us know right away."}),(0,t.jsxs)("p",{className:"mb-4",children:["Please email security concerns to ",t.jsx("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"<EMAIL>"}),"."]})]}),(0,t.jsxs)("section",{className:"mb-8",children:[t.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"What We Ask From You"}),(0,t.jsxs)("ul",{className:"list-disc pl-6 mb-4 space-y-2",children:[t.jsx("li",{children:"Provide detailed reports with reproducible steps"}),t.jsx("li",{children:"Allow a reasonable time for us to address the issue before public disclosure"}),t.jsx("li",{children:"Do not access or modify user data without explicit permission"}),t.jsx("li",{children:"Act in good faith and do not conduct denial of service attacks or other disruptive activities"})]})]}),(0,t.jsxs)("section",{className:"mb-8",children:[t.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"Our Commitment"}),t.jsx("p",{className:"mb-4",children:"When you report a vulnerability to us, we commit to:"}),(0,t.jsxs)("ul",{className:"list-disc pl-6 mb-4 space-y-2",children:[t.jsx("li",{children:"Acknowledge receipt of your vulnerability report in a timely manner"}),t.jsx("li",{children:"Provide an estimated timeframe for addressing the vulnerability"}),t.jsx("li",{children:"Notify you when the vulnerability is fixed"}),t.jsx("li",{children:"Recognize your contribution if you wish (unless you prefer to remain anonymous)"})]})]}),(0,t.jsxs)("section",{children:[t.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"Thank You"}),t.jsx("p",{children:"We appreciate your help in keeping javianpicardo.com and our users safe. Thank you for your contributions to our security."})]})]})}}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[948,70,251,746],()=>s(86521));module.exports=t})();