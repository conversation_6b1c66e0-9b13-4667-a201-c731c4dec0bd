﻿using FakeItEasy;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using MicroSaasWebApi.Middlewares;
using MicroSaasWebApi.Services.PXW.Interface;
using System.Text;
using Microsoft.Extensions.Configuration;

namespace MicroSaasWebApi.Tests.Unit.Middleware
{
    public class CustomRedirectMiddlewareTests
    {
        private readonly IConfiguration _configuration;
        private readonly IApplicationSettingsService _applicationSettingsService;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILoggerService _loggerService;
        private readonly ITenantProviderService _tenantProviderService;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly IServiceScope _serviceScope;
        public CustomRedirectMiddlewareTests()
        {
            _loggerService = A.Fake<ILoggerService>();
            _applicationSettingsService = A.Fake<IApplicationSettingsService>();
            _serviceScope = A.Fake<IServiceScope>();            
            _serviceScopeFactory = A.Fake<IServiceScopeFactory>();
            _serviceProvider = A.Fake<IServiceProvider>();
            _tenantProviderService = A.Fake<ITenantProviderService>();
            _configuration = A.Fake<IConfiguration>();
            var builder = new ConfigurationBuilder()
                               .SetBasePath(Directory.GetCurrentDirectory())
                               .AddJsonFile("appsettings.json");
            _configuration = builder.Build();
        }
        [Fact]
        public async Task InvokeAsync_WithValidRequest_ShouldUpdatePath()
        {
            // Arrange
            A.CallTo(() => _serviceProvider.GetService(typeof(IServiceScopeFactory))).Returns(_serviceScopeFactory);
            A.CallTo(() => _serviceScopeFactory.CreateScope()).Returns(_serviceScope);

            A.CallTo(() => _serviceScope.ServiceProvider.GetService(typeof(ILoggerService))).Returns(_loggerService);
            A.CallTo(() => _serviceScope.ServiceProvider.GetService(typeof(IApplicationSettingsService))).Returns(_applicationSettingsService);
            A.CallTo(() => _serviceScope.ServiceProvider.GetService(typeof(ITenantProviderService))).Returns(_tenantProviderService);
            A.CallTo(() => _serviceScope.ServiceProvider.GetService(typeof(IConfiguration))).Returns(_configuration);
            var middleware = new CustomRedirectMiddleware(ctx => Task.CompletedTask, _serviceProvider, _configuration);

            var context = new DefaultHttpContext();
            context.Request.Path = "/api";
            context.Request.Method = "POST";
            context.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes(@"{
                ""Component"": ""CurrenciesRetrieveDropdown"",
                ""TenantGuid"": ""43e4bbcd-81ea-4806-8919-c8634eae8e62"",
                ""Controller"": ""PXW/DataApi"",
                ""Action"": ""ProfilesCustodianAccountCodesRetrieve"",
                ""PathParameters"": {
                    ""id"" : ""sampleId""
                }
            }"));

            middleware.TenantGuids = new[] { "43e4bbcd-81ea-4806-8919-c8634eae8e62" };
            // Act
            await middleware.InvokeAsync(context);

            // Assert
            context.Request.Path.Value.Should().Be("/api/PXW/DataApi/ProfilesCustodianAccountCodesRetrieve");
            // Assertions go here. For example:
            A.CallTo(() => _applicationSettingsService.SetAppSettingsFilteredTenantInHttpContext("43e4bbcd-81ea-4806-8919-c8634eae8e62"))
             .MustHaveHappenedOnceExactly();
        }
    }
}
