{"id": "c863c7bd-b428-4d24-b266-5ede925344be", "name": "PXW-WEB-API-ENV-INFERNO-PROD", "values": [{"key": "domain", "value": "https://localhost:7163", "type": "default", "enabled": true}, {"key": "domain.uat", "value": "https://pxw-webapi-uat.azurewebsites.net", "type": "default", "enabled": true}, {"key": "profile_id", "value": "643f0db9-66c4-4e1c-9ad6-5e224ff25047", "type": "default", "enabled": true}, {"key": "account_codes", "value": "[\"5PXRYD\", \"5PXREY\", \"5PXKAD\", \"5PXWAX\", \"5PXKAR\", \"5PXMCA\", \"5PXKAE\"]", "type": "default", "enabled": false}, {"key": "TenantGuid", "value": "1c993dd6-f96e-4851-87b6-cec87e3a74c2", "type": "default", "enabled": true}, {"key": "account_code", "value": "5PXKAR", "type": "default", "enabled": false}, {"key": "as_of_date", "value": "2022-12-20", "type": "default", "enabled": false}, {"key": "b2c_client_secret", "value": "****************************************", "type": "default", "enabled": true}, {"key": "b2c_client_id", "value": "3db9f268-4860-43f3-8d6f-b90c874499da", "type": "default", "enabled": true}, {"key": "b2c_token_url", "value": "https://uatcansob2c.b2clogin.com/uatcansob2c.onmicrosoft.com/B2C_1A_SIGNIN_TOTP/oauth2/v2.0/token", "type": "default", "enabled": true}, {"key": "b2c_auth_url", "value": "https://uatcansob2c.b2clogin.com/uatcansob2c.onmicrosoft.com/B2C_1A_SIGNIN_TOTP/oauth2/v2.0/authorize", "type": "default", "enabled": true}, {"key": "baseUrl", "value": "*********************************************/v1.0", "type": "default", "enabled": true}, {"key": "key", "value": "2bcea5a3783748b495fc2beb9a91ba21", "type": "default", "enabled": true}, {"key": "organization", "value": "canso-01", "type": "default", "enabled": true}, {"key": "userEmail", "value": "<EMAIL>", "type": "default", "enabled": false}, {"key": "workflowKey", "value": "a3ede38a-855d-4924-8a85-ec6a83177db4", "type": "default", "enabled": false}, {"key": "security_id", "value": "2d252487-0d15-4178-b603-d60befbafed9", "type": "default", "enabled": false}, {"key": "transaction_id", "value": "3b65fdc9-e571-4ccf-b936-9c304f14a27a", "type": "default", "enabled": false}, {"key": "security_price_id", "value": "223227dd-3b42-4a4a-a154-ce029e2079bc", "type": "default", "enabled": false}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-04-17T05:05:12.128Z", "_postman_exported_using": "Postman/10.24.22"}