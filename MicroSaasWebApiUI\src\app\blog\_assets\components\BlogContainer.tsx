'use client'

import { useState, useEffect } from 'react'
import Blog<PERSON><PERSON>ing<PERSON>ew from './BlogListingNew'
import CategoryTreeView from './CategoryTreeView'
import AdManager from '@/components/AdManager'

interface BlogContainerProps {
  articles: any[]
  categories: any[]
  showAds?: boolean
}

const BlogContainer = ({ articles, categories, showAds = false }: BlogContainerProps) => {
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null)

  return (
    <div className='flex flex-col gap-4'>
      {/* Main content */}
      <div className='flex flex-col md:flex-row gap-8'>
        <div className='md:w-1/4 md:min-w-[250px] md:max-w-[300px]'>
          <div className='md:sticky md:top-4'>
            <CategoryTreeView
              categories={categories}
              onSelectCategory={setSelectedCategoryId}
              selectedCategoryId={selectedCategoryId}
            />

            {/* Sidebar ad (visible on desktop) */}
            {showAds && (
              <div className='hidden md:block mt-6 w-full max-w-[300px]'>
                <AdManager type="multiplex-vertical" className="sidebar-ad" />
              </div>
            )}
          </div>
        </div>
        <div className='md:flex-1'>
          <BlogListingNew
            articles={articles}
            selectedCategoryId={selectedCategoryId}
          />

          {/* In-feed ad after blog listings */}
          {showAds && (
            <div className='mt-8'>
              <AdManager type="in-feed" />
            </div>
          )}
        </div>
      </div>

      {/* Bottom horizontal ad */}
      {showAds && (
        <div className='w-full mt-8'>
          <AdManager type="display-horizontal" />
        </div>
      )}
    </div>
  )
}

export default BlogContainer
