(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[702],{40282:function(e,t,s){Promise.resolve().then(s.bind(s,51328)),Promise.resolve().then(s.bind(s,86400)),Promise.resolve().then(s.bind(s,43360))},27648:function(e,t,s){"use strict";s.d(t,{default:function(){return a.a}});var r=s(72972),a=s.n(r)},2:function(e,t,s){"use strict";var r=s(57437),a=s(2265),n=s(87770),l=s(38333);t.Z=e=>{let{priceId:t,disabled:s=!1}=e,[i,o]=(0,a.useState)(!1),[c,d]=(0,a.useState)(null),{isLoaded:u,isSignedIn:f,user:m}=(0,n.aF)(),x=async()=>{if(!u||!f){d("Please sign in to proceed with checkout");return}if(m){var e;await (0,l.D)(t,m.id,(null===(e=m.primaryEmailAddress)||void 0===e?void 0:e.emailAddress)||null,o,d)}};return u?(0,r.jsxs)("div",{children:[(0,r.jsx)("button",{className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition duration-300",onClick:x,disabled:i||!f||s,children:i?"Processing...":f?"Proceed to Checkout":"Sign in to Checkout"}),c&&(0,r.jsx)("p",{className:"text-red-500 mt-2",children:c})]}):(0,r.jsx)("div",{children:"Loading..."})}},51328:function(e,t,s){"use strict";s.d(t,{default:function(){return l}});var r=s(57437);let a=(0,s(79205).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);var n=s(2);function l(){return(0,r.jsx)("section",{className:"py-16",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-center mb-12 dark:text-white text-black1",children:"Photography Packages"}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto",children:[(0,r.jsxs)("div",{className:"dark:bg-[#131211] bg-white rounded-lg shadow-md p-8",children:[(0,r.jsx)("h3",{className:"text-2xl font-semibold mb-4",children:"Quick Shoots"}),(0,r.jsxs)("p",{className:"text-4xl font-bold mb-6",children:["$300",(0,r.jsx)("span",{className:"text-xl dark:text-gray-500 text-black1/70 font-normal",children:"/session"})]}),(0,r.jsxs)("ul",{className:"space-y-3 mb-8",children:[(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)(a,{className:"text-green-500 mr-2"}),(0,r.jsx)("span",{children:"One hour of footage time"})]}),(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)(a,{className:"text-green-500 mr-2"}),(0,r.jsx)("span",{children:"50 edited photos"})]}),(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)(a,{className:"text-green-500 mr-2"}),(0,r.jsx)("span",{children:"Help with posing"})]}),(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)(a,{className:"text-green-500 mr-2"}),(0,r.jsx)("span",{children:"Location list to pick from"})]}),(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)(a,{className:"text-green-500 mr-2"}),(0,r.jsx)("span",{children:"7 business days turn around time"})]})]}),(0,r.jsx)(n.Z,{priceId:"price_1R6aslP82YH9JfOlJZcweC4d"})]}),(0,r.jsxs)("div",{className:"dark:bg-[#131211] bg-white rounded-lg shadow-md p-8 border-4 border-blue-500 relative",children:[(0,r.jsx)("div",{className:"absolute top-0 right-0 bg-blue-500 text-white py-1 px-4 rounded-bl-lg text-sm font-semibold",children:"Most Popular"}),(0,r.jsx)("h3",{className:"text-2xl font-semibold mb-4",children:"Mini Shoots"}),(0,r.jsxs)("p",{className:"text-4xl font-bold mb-2",children:["$150",(0,r.jsx)("span",{className:"text-xl dark:text-gray-500 text-black1/70 font-normal",children:"/session"})]}),(0,r.jsx)("p",{className:"text-green-600 font-semibold mb-6",children:"Perfect for professional portraits"}),(0,r.jsxs)("ul",{className:"space-y-3 mb-8",children:[(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)(a,{className:"text-green-500 mr-2"}),(0,r.jsx)("span",{children:"30 minutes of footage time"})]}),(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)(a,{className:"text-green-500 mr-2"}),(0,r.jsx)("span",{children:"25 edited photos"})]}),(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)(a,{className:"text-green-500 mr-2"}),(0,r.jsx)("span",{children:"Help with posing"})]}),(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)(a,{className:"text-green-500 mr-2"}),(0,r.jsx)("span",{children:"Location list to pick from"})]}),(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)(a,{className:"text-green-500 mr-2"}),(0,r.jsx)("span",{children:"7 business days turn around time"})]})]}),(0,r.jsx)(n.Z,{priceId:"price_1R6avXP82YH9JfOlIGpAfwjU"})]})]})]})})}},86400:function(e,t,s){"use strict";s.d(t,{default:function(){return x}});var r=s(57437),a=s(12381),n=s(2265),l=s(10090);let i=n.forwardRef((e,t)=>{let{className:s,type:a,...n}=e;return(0,r.jsx)("input",{type:a,className:(0,l.cn)("flex h-10 w-full rounded-md border border-slate-200 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-slate-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-slate-800 dark:bg-slate-950 dark:ring-offset-slate-950 dark:placeholder:text-slate-400 dark:focus-visible:ring-slate-300",s),ref:t,...n})});i.displayName="Input";var o=s(83464),c=s(77314),d=s(33145),u=s(27648),f=s(34422);let m=e=>{let t=e.reduce((e,t)=>(e[t]=f.Z_().required("".concat(t," is required")),e),{});return f.Ry().shape(t)};var x=()=>{let[e,t]=(0,n.useState)([]),[s,l]=(0,n.useState)(!1),f=[{id:0,image:"/scenarios/make.png",name:"OpenAI Scenario Make",description:"This is a scenario template for OpenAI using Make",fields:["apiKey","apiOrg","assistantId"],route:"/api/scenarios/openAIAssistant"},{id:"your scenario id",image:"/scenarios/n8n.png",name:"OpenAI Scenario n8n",description:"This is a scenario template for OpenAI using n8n",fields:["apiKey","apiOrg","assistantId"],route:"/api/workflows/openAIAssistant"}],x=async()=>{try{let e=await o.Z.get("/api/projects");e.data.projects&&t(e.data.projects)}catch(e){console.error("Failed to fetch projects:",e)}},h=async e=>{try{(await o.Z.post("/api/active",{projectId:e})).data.success&&await x()}catch(e){console.error("Failed to activate project:",e)}},p=async(e,t)=>{try{l(!0),(await o.Z.post(t,{id:e.scenarioId,...e})).data.success&&await x()}catch(e){console.error("Failed to clone scenario:",e)}finally{l(!1)}};return(0,n.useEffect)(()=>{x()},[]),(0,r.jsxs)("div",{className:"flex flex-col w-full items-center justify-start gap-4",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold",children:"Scenarios Templates"}),(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 w-11/12 max-w-7xl",children:null==f?void 0:f.map(e=>{let t=e.fields.reduce((e,t)=>(e[t]="",e),{scenarioId:e.id});return(0,r.jsxs)("div",{className:"flex flex-col justify-between glass rounded-xl border border-gray-600 bg items-start p-4 min-h-40",children:[(0,r.jsx)(d.default,{src:e.image,alt:e.name,width:500,height:500,className:"w-full rounded-lg"}),(0,r.jsx)("p",{className:"text-2xl mt-2 text-center font-bold w-full truncate text-wrap",children:e.name}),(0,r.jsx)("p",{className:"text-sm w-full text-center text-gray-400",children:e.description}),(0,r.jsx)(c.J9,{initialValues:t,validationSchema:m(e.fields),onSubmit:t=>p(t,e.route),children:t=>{let{errors:n,touched:l}=t;return(0,r.jsxs)(c.l0,{className:"w-full space-y-2 mt-2",children:[e.fields.map(e=>(0,r.jsxs)("div",{children:[(0,r.jsx)(c.gN,{as:i,name:e,placeholder:e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase()),className:"w-full"}),n[e]&&l[e]&&"string"==typeof n[e]&&(0,r.jsx)("div",{className:"text-red-500 text-sm",children:n[e]})]},e)),(0,r.jsx)(a.z,{type:"submit",className:"w-full",disabled:s,children:s?"Creating...":"Create Agent"})]})}})]},e.id)})}),(0,r.jsx)("h1",{className:"text-4xl font-bold",children:"My Projects"}),(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 w-11/12 max-w-7xl",children:null==e?void 0:e.map(e=>(0,r.jsxs)("div",{className:"flex flex-col space-y-3 justify-between glass rounded-xl border border-gray-600 bg items-start p-4 min-h-40",children:[(0,r.jsxs)("div",{className:"flex w-full justify-between",children:[(0,r.jsx)("p",{className:"text-lg font-bold w-full truncate text-wrap",children:e.type}),(0,r.jsx)(a.z,{onClick:()=>h(e.id),variant:"outline",children:"active"==e.status?(0,r.jsx)("p",{className:"text-green-500",children:"Active"}):(0,r.jsx)("p",{className:"text-red-500",children:"Inactive"})})]}),(0,r.jsxs)("div",{className:"flex w-full flex-col gap-2",children:[(0,r.jsxs)("p",{className:"w-full truncate",children:["Assistant ID: ",e.assistant_id]}),(0,r.jsxs)("p",{className:"w-full truncate",children:["Webhook Link: ",e.webhookLink]})]}),(0,r.jsx)(u.default,{href:"/chat/".concat(e.id),className:"w-full",children:(0,r.jsx)(a.z,{className:"w-full mt-2",children:"Go to chat"})})]},e.id))})]})}},43360:function(e,t,s){"use strict";s.d(t,{default:function(){return l}});var r=s(57437),a=s(2265),n=s(99376);function l(){let e=(0,n.useRouter)(),t=(0,n.usePathname)(),s=(0,n.useSearchParams)().get("session_id"),[l,i]=(0,a.useState)(!!s);return((0,a.useEffect)(()=>{localStorage.removeItem("priceId")},[]),l)?(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"bg-white p-8 rounded-lg shadow-lg text-center",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Thank You!"}),(0,r.jsx)("p",{className:"mb-6",children:"We appreciate your support."}),(0,r.jsx)("button",{onClick:()=>{e.replace(t),i(!1)},className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-full text-lg transition duration-300 ease-in-out transform hover:scale-105",children:"Start"})]})}):null}},12381:function(e,t,s){"use strict";s.d(t,{z:function(){return c}});var r=s(57437),a=s(2265),n=s(37053),l=s(90535),i=s(10090);let o=(0,l.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 dark:ring-offset-slate-950 dark:focus-visible:ring-slate-300",{variants:{variant:{default:"bg-slate-900 text-slate-50 hover:bg-slate-900/90 dark:bg-slate-50 dark:text-slate-900 dark:hover:bg-slate-50/90",destructive:"bg-red-500 text-slate-50 hover:bg-red-500/90 dark:bg-red-900 dark:text-slate-50 dark:hover:bg-red-900/90",outline:"border border-slate-200 bg-white hover:bg-slate-100 hover:text-slate-900 dark:border-slate-800 dark:bg-slate-950 dark:hover:bg-slate-800 dark:hover:text-slate-50",secondary:"bg-slate-100 text-slate-900 hover:bg-slate-100/80 dark:bg-slate-800 dark:text-slate-50 dark:hover:bg-slate-800/80",ghost:"hover:bg-slate-100 hover:text-slate-900 dark:hover:bg-slate-800 dark:hover:text-slate-50",link:"text-slate-900 underline-offset-4 hover:underline dark:text-slate-50"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:s,variant:a,size:l,asChild:c=!1,...d}=e,u=c?n.g7:"button";return(0,r.jsx)(u,{className:(0,i.cn)(o({variant:a,size:l,className:s})),ref:t,...d})});c.displayName="Button"},38333:function(e,t,s){"use strict";s.d(t,{D:function(){return a}});let r=(0,s(48672).J)("pk_live_51Qfy9dP82YH9JfOlFmwTk0RHvE7l6sow7ImJBI4c1GzcbhFydoVpb3DMevP8B3C4gj95qCoEise8vEhgFgWxhLEN000RfInKNH"),a=async(e,t,s,a,n)=>{if(!s)throw Error("User email not available");a(!0),n(null);try{console.log("Initiating checkout for priceId:",e),console.log("User data:",{id:t,email:s});let a=await fetch("/api/stripe/create-checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({priceId:e,userId:t,email:s})});if(!a.ok){let e=await a.text();throw console.error("Server responded with an error:",a.status,e),Error("HTTP error! status: ".concat(a.status))}let n=await a.json();if(console.log("Received data from server:",n),!n.sessionId)throw console.error("Server response:",n),Error("No sessionId received from the server");let l=await r;if(!l)throw Error("Failed to load Stripe");let{error:i}=await l.redirectToCheckout({sessionId:n.sessionId});if(i)throw console.error("Stripe redirectToCheckout error:",i),i}catch(e){console.error("Checkout error:",e),n(e instanceof Error?e.message:"An unexpected error occurred")}finally{a(!1)}}},10090:function(e,t,s){"use strict";s.d(t,{cn:function(){return n}});var r=s(61994),a=s(53335);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.m6)((0,r.W)(t))}},48672:function(e,t,s){"use strict";s.d(t,{J:function(){return h}});var r,a="https://js.stripe.com/v3",n=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,l=function(){for(var e=document.querySelectorAll('script[src^="'.concat(a,'"]')),t=0;t<e.length;t++){var s=e[t];if(n.test(s.src))return s}return null},i=function(e){var t=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",s=document.createElement("script");s.src="".concat(a).concat(t);var r=document.head||document.body;if(!r)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return r.appendChild(s),s},o=function(e,t){e&&e._registerWrapper&&e._registerWrapper({name:"stripe-js",version:"4.6.0",startTime:t})},c=null,d=null,u=null,f=function(e,t,s){if(null===e)return null;var r=e.apply(void 0,t);return o(r,s),r},m=!1,x=function(){return r||(r=(null!==c?c:(c=new Promise(function(e,t){if("undefined"==typeof window||"undefined"==typeof document){e(null);return}if(window.Stripe,window.Stripe){e(window.Stripe);return}try{var s,r=l();r?r&&null!==u&&null!==d&&(r.removeEventListener("load",u),r.removeEventListener("error",d),null===(s=r.parentNode)||void 0===s||s.removeChild(r),r=i(null)):r=i(null),u=function(){window.Stripe?e(window.Stripe):t(Error("Stripe.js not available"))},d=function(){t(Error("Failed to load Stripe.js"))},r.addEventListener("load",u),r.addEventListener("error",d)}catch(e){t(e);return}})).catch(function(e){return c=null,Promise.reject(e)})).catch(function(e){return r=null,Promise.reject(e)}))};Promise.resolve().then(function(){return x()}).catch(function(e){m||console.warn(e)});var h=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];m=!0;var r=Date.now();return x().then(function(e){return f(e,t,r)})}}},function(e){e.O(0,[972,563,145,203,376,971,117,744],function(){return e(e.s=40282)}),_N_E=e.O()}]);