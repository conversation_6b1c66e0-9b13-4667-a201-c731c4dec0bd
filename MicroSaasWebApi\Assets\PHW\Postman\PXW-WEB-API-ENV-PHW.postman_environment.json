{"id": "********-0673-4fae-865c-1aa6fec511e6", "name": "PXW-WEB-API-ENV-PHW", "values": [{"key": "domain", "value": "https://localhost:7163", "type": "default", "enabled": true}, {"key": "domain.uat", "value": "https://pxw-webapi-uat.azurewebsites.net", "type": "default", "enabled": true}, {"key": "profile_id", "value": "2ce10701-01ca-4ed2-bd70-08b3fd356f43", "type": "default", "enabled": true}, {"key": "account_codes", "value": "[\"5PXKAR\",\"5PXAHP\"]", "type": "default", "enabled": true}, {"key": "TenantGuid", "value": "ddb47824-e626-4d26-97da-97eb6b23e16d", "type": "default", "enabled": true}, {"key": "account_code", "value": "5PXKAR", "type": "default", "enabled": true}, {"key": "as_of_date", "value": "2022-12-20", "type": "default", "enabled": true}, {"key": "b2c_client_secret", "value": "****************************************", "type": "default", "enabled": true}, {"key": "b2c_client_id", "value": "245bfee9-7eee-4402-b281-0991a5737b48", "type": "default", "enabled": true}, {"key": "b2c_token_url", "value": "https://pxwuat.b2clogin.com/pxwuat.onmicrosoft.com/B2C_1A_SIGNIN_TOTP/oauth2/v2.0/token", "type": "default", "enabled": true}, {"key": "b2c_auth_url", "value": "https://pxwuat.b2clogin.com/pxwuat.onmicrosoft.com/B2C_1A_SIGNIN_TOTP/oauth2/v2.0/authorize", "type": "default", "enabled": true}, {"key": "baseUrl", "value": "*************************************/v1.0", "type": "default", "enabled": true}, {"key": "key", "value": "db03e1278327479096e75e278952a751", "type": "default", "enabled": true}, {"key": "organization", "value": "pxw-01", "type": "default", "enabled": true}, {"key": "userEmail", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "workflowId", "value": "5645d594-1622-4f9c-9f15-a3b74a5a3d82", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2023-07-31T18:00:36.003Z", "_postman_exported_using": "Postman/10.16.3"}