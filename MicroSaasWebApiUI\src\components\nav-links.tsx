import { ScrollToSection } from "@/utils/scroll-to-section";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";

interface NavLink {
  icon?: JSX.Element;
  title: string;
  link: string;
  download?: boolean;
}

interface PropsTypes {
  nav_links: NavLink[];
  isFooter?: boolean;
}

const NavLinks = ({ nav_links, isFooter = false }: PropsTypes) => {
  const router = useRouter();
  const pathname = usePathname();

  const handleNavigate = (item: NavLink, index: number) => {
    if (item.download) {
      // Don't do anything for download links
      return;
    }
    if (pathname === "/" && item?.link === "/") {
      ScrollToSection(index.toString());
    } else {
      router.push(item?.link);
      setTimeout(() => {
        ScrollToSection(index.toString());
      }, 1500);
    }
  };

  return (
    <div
      className={`flex ${
        isFooter ? "flex-col" : "flex-col lg:flex-row lg:items-center justify-end"
      } gap-x-6 gap-y-4`}
    >
      {nav_links?.map((item: NavLink, index: number) =>
        pathname === "/" && item?.link === "/" ? (
          <p
            key={index}
            className={`text-black1 dark:text-white cursor-pointer hover:text-[#006fee] dark:hover:text-[#006fee] transition-colors duration-300 ease-in-out ${
              !isFooter
                ? "text-center flex items-center font-medium text-base gap-2"
                : "font-medium"
            }`}
            style={{
              lineHeight: "normal",
            }}
            onClick={() => handleNavigate(item, index)}
          >
            <span>{item?.icon}</span> {item?.title}
          </p>
        ) : (
          <Link
            key={index}
            href={item?.link}
            download={item?.download}
            className={`text-black1 dark:text-white cursor-pointer hover:text-[#006fee] dark:hover:text-[#006fee] transition-colors duration-300 ease-in-out ${
              !isFooter
                ? "text-center flex items-center font-medium text-base gap-2"
                : "font-medium"
            }`}
            style={{
              lineHeight: "normal",
            }}
            onClick={() => handleNavigate(item, index)}
          >
            <span>{item?.icon}</span> {item?.title}
          </Link>
        )
      )}
    </div>
  );
};

export default NavLinks;
