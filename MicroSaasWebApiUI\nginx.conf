# HTTP redirect to HTTPS
server {
	listen 80 default_server;
	listen [::]:80 default_server;
	server_name javianpicardo.com www.javianpicardo.com;

	# Redirect all HTTP requests to HTTPS with a 301 Moved Permanently response
	return 301 https://$host$request_uri;
}

# HTTPS server
server {
	listen 443 ssl http2;
	listen [::]:443 ssl http2;
	server_name javianpicardo.com www.javianpicardo.com;

	# SSL configuration - Update these paths to your certificate files
	ssl_certificate /etc/letsencrypt/live/javianpicardo.com/fullchain.pem;
	ssl_certificate_key /etc/letsencrypt/live/javianpicardo.com/privkey.pem;

	# Enable HSTS (HTTP Strict Transport Security) with preload
	add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;

	# Security headers to prevent common attacks
	add_header X-Content-Type-Options "nosniff" always;
	add_header X-Frame-Options "SAMEORIGIN" always;
	add_header X-XSS-Protection "1; mode=block" always;
	add_header Referrer-Policy "strict-origin-when-cross-origin" always;
	add_header Referrer-Policy "no-referrer-when-downgrade" always;
	# Relaxed cross-origin policies to allow AdSense to work
	# add_header Cross-Origin-Resource-Policy "same-origin" always;
	# add_header Cross-Origin-Opener-Policy "same-origin" always;
	# add_header Cross-Origin-Embedder-Policy "require-corp" always;
	add_header Permissions-Policy "accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()" always;

	# Comprehensive Content Security Policy with AdSense support
	# Temporarily commented out to allow AdSense to work properly
	# add_header Content-Security-Policy "default-src 'self';
	# 	script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com https://ssl.google-analytics.com https://www.googleadservices.com https://googleads.g.doubleclick.net https://www.google.com https://pagead2.googlesyndication.com https://adservice.google.com https://tpc.googlesyndication.com https://www.gstatic.com https://cse.google.com https://fonts.googleapis.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://*.googlesyndication.com https://*.doubleclick.net;
	# 	style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://*.googlesyndication.com;
	# 	img-src 'self' data: https: http:;
	# 	font-src 'self' https://fonts.gstatic.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com;
	# 	connect-src 'self' https://www.google-analytics.com https://analytics.google.com https://stats.g.doubleclick.net https://*.googlesyndication.com https://*.doubleclick.net;
	# 	frame-src 'self' https://www.google.com https://googleads.g.doubleclick.net https://tpc.googlesyndication.com https://*.googlesyndication.com https://*.doubleclick.net;
	# 	frame-ancestors 'self';
	# 	base-uri 'self';
	# 	form-action 'self';
	# 	manifest-src 'self';
	# 	object-src 'none';
	# 	worker-src 'self' blob: https://*.googlesyndication.com;
	# 	block-all-mixed-content;
	# 	upgrade-insecure-requests;" always;

	# Cookie security - commented out to allow AdSense cookies
	# proxy_cookie_path / "/; HTTPOnly; Secure; SameSite=Strict";

	# Set secure cookies - commented out to allow AdSense cookies
	# add_header Set-Cookie "Path=/; HttpOnly; Secure" always;
	# add_header Set-Cookie "__client_uat=; Path=/; HttpOnly; Secure; SameSite=Strict" always;
	# add_header Set-Cookie "__clerk_db_jwt=; Path=/; HttpOnly; Secure; SameSite=Strict" always;
	# add_header Set-Cookie "__clerk_redirect_count=; Path=/; HttpOnly; Secure; SameSite=Strict" always;

	gzip on;
	gzip_min_length 1000;
	gzip_types text/plain text/xml application/javascript text/css;

	root /app;

	# normal routes
	# serve given url and default to index.html if not found
	# e.g. /, /user and /foo/bar will return index.html
	location / {
		add_header Cache-Control "no-store";
		try_files $uri /index.html;
	}
    location ~ ^/auth/ {
        # Add any specific directives you need for /auth here
        # For example, you might not want any caching for this route
        # add_header Cache-Control "no-cache, no-store, must-revalidate";
		add_header Cache-Control "no-store";
        try_files $uri /index.html;
    }
	# files
	# for all routes matching a dot, check for files and return 404 if not found
	# e.g. /file.js returns a 404 if not found
	location ~ \.(?!html) {
		add_header Cache-Control "public, max-age=2678400";
		try_files $uri =404;
	}
}
