import { Heading } from "@/components";
import {
  Auth,
  Blog,
  Clerk,
  Database,
  Email,
  Landing,
  Licence,
  Licence2,
  NextJs,
  Payment,
  Postgres,
  Prisma,
  Resend,
  Seo,
  Shadecn,
  Stripe,
  Tailwind,
  Typescript,
  Ui,
  Wordpress,
} from "@/icons";
import Image from "next/image";
import StopCodingBg from "@/assets/images/stop-coding-bg1.svg";
import StopCodingBgDark from "@/assets/images/stop-coding-bg2.svg";

import LandingImg from "@/assets/images/landing.svg";
import AuthImg from "@/assets/images/auth.svg";
import EmailImg from "@/assets/images/email.svg";
import PaymentImg from "@/assets/images/payment.svg";
import SeoImg from "@/assets/images/seo.svg";
import BlogImg from "@/assets/images/blog.svg";
import DatabaseImg from "@/assets/images/database.svg";
import UiImg from "@/assets/images/ui.svg";
import LicenceImg from "@/assets/images/licence.svg";

const LandingImgUrl = LandingImg.src;
const AuthImgUrl = AuthImg.src;
const EmailImgUrl = EmailImg.src;
const PaymentImgUrl = PaymentImg.src;
const SeoImgUrl = SeoImg.src;
const BlogImgUrl = BlogImg.src;
const DatabaseImgUrl = DatabaseImg.src;
const UiImgUrl = UiImg.src;
const LicenceImgUrl = LicenceImg.src;

const data = [
  {
    icon: <Landing />,
    image: `${LandingImgUrl}`,
    title: "Enterprise Authentication",
    desc: "Complete authentication system with JWT tokens, Azure AD B2C integration, role-based access control, and comprehensive security middleware",
    features: [
      "JWT Authentication",
      "Azure AD B2C Integration",
      "Role-based Authorization",
      "Security Middleware",
    ],
    time: "2 MONTHS SAVED",
    technologies: [
      {
        icon: <Typescript />,
        text: ".NET 8/9",
      },
      {
        icon: <NextJs />,
        text: "JWT Tokens",
      },
      {
        icon: <Tailwind />,
        text: "Azure AD B2C",
      },
      {
        icon: <Shadecn />,
        text: "RBAC",
      },
    ],
  },
  {
    icon: <Auth />,
    image: `${AuthImgUrl}`,
    title: "Database & ORM",
    desc: "Complete database setup with Entity Framework, PostgreSQL integration, migrations, and data access patterns",
    features: [
      "Entity Framework Core",
      "PostgreSQL Integration",
      "Database Migrations",
      "Repository Pattern",
      "Data Seeding",
    ],
    time: "1 MONTH SAVED",
    technologies: [
      {
        icon: <Database />,
        text: "Entity Framework",
      },
      {
        icon: <Postgres />,
        text: "PostgreSQL",
      },
    ],
  },
  {
    icon: <Email />,
    image: `${EmailImgUrl}`,
    title: "Cloud Integration",
    desc: "Azure services integration with App Configuration, Key Vault, and deployment-ready containerization",
    features: [
      "Azure App Configuration",
      "Azure Key Vault",
      "Docker Containerization",
      "Health Checks",
      "Monitoring & Logging",
    ],
    time: "3 WEEKS SAVED",
    technologies: [
      {
        icon: <Resend />,
        text: "Azure Services",
      },
    ],
  },
  {
    icon: <Payment />,
    image: `${PaymentImgUrl}`,
    title: "Payment Integration",
    desc: "Complete payment system with Stripe integration, subscription management, and webhook handling",
    features: [
      "Stripe Payment Processing",
      "Subscription Management",
      "Webhook Handlers",
      "Payment Models",
      "Invoice Generation",
    ],
    time: "2 WEEKS SAVED",
    technologies: [
      {
        icon: <Stripe />,
        text: "Stripe",
      },
    ],
  },
  {
    icon: <Seo />,
    image: `${SeoImgUrl}`,
    title: "API Documentation",
    desc: "Comprehensive API documentation with Swagger/OpenAPI, interactive testing, and detailed endpoint specifications",
    features: [
      "Swagger/OpenAPI Integration",
      "Interactive API Testing",
      "Endpoint Documentation",
      "Model Schemas",
      "Authentication Examples",
    ],
    time: "1 WEEK SAVED",
  },
  {
    icon: <Blog />,
    image: `${BlogImgUrl}`,
    title: "Testing Framework",
    desc: "Comprehensive testing setup with unit tests, integration tests, and test utilities for reliable code quality",
    features: [
      "Unit Test Framework",
      "Integration Tests",
      "Test Utilities",
      "Mocking Setup",
      "Code Coverage",
    ],
    time: "1 WEEK SAVED",
    technologies: [
      {
        icon: <Wordpress />,
        text: "xUnit",
      },
    ],
  },
  {
    icon: <Database />,
    image: `${DatabaseImgUrl}`,
    title: "Security & Middleware",
    desc: "Enterprise-grade security with comprehensive middleware, CORS configuration, and security best practices",
    features: [
      "Security Middleware",
      "CORS Configuration",
      "Request Validation",
      "Error Handling",
      "Rate Limiting",
    ],
    time: "2 WEEKS SAVED",
    technologies: [
      {
        icon: <Prisma />,
        text: "Security",
      },
      {
        icon: <Postgres />,
        text: "Middleware",
      },
    ],
  },
  {
    icon: <Ui />,
    image: `${UiImgUrl}`,
    title: "DevOps & Deployment",
    desc: "Production-ready deployment configuration with Docker, Azure pipelines, and infrastructure as code",
    features: [
      "Docker Configuration",
      "Azure Pipelines",
      "Environment Management",
      "CI/CD Setup",
      "Infrastructure Templates",
    ],
    time: "1 MONTH SAVED",
    technologies: [
      {
        icon: <Shadecn />,
        text: "Docker",
      },
      {
        icon: <Tailwind />,
        text: "Azure DevOps",
      },
    ],
  },
  {
    icon: <Licence />,
    image: `${LicenceImgUrl}`,
    title: "Complete Source Code",
    desc: "Get the complete .NET Web API template source code with full commercial license. Build unlimited projects and customize as needed",
    features: ["Complete Source Code", "Commercial License", "Unlimited Projects", "Full Documentation"],
    time: "6 MONTHS SAVED",
    technologies: [
      {
        icon: <Licence2 />,
        text: "Full License",
      },
    ],
    isBlue: true,
  },
];

const Features = () => {
  return (
    <div className="relative flex justify-center items-center w-full">
      <Image
        src={StopCodingBg}
        alt="background"
        fill
        objectFit="cover"
        priority={true}
        className="z-0 block dark:hidden"
      />
      <Image
        src={StopCodingBgDark}
        alt="background"
        fill
        objectFit="cover"
        priority={true}
        className="z-0 hidden dark:block"
      />
      <div className="relative z-10 max-w-[1440px] w-full px-4 sm:px-12 py-12">
        <div className="max-w-[528px] mx-auto mb-24">
          <Heading
            title="Enterprise-Ready .NET API Template"
            desc="Production-ready .NET Web API template with authentication, payments, database integration, Azure deployment, and comprehensive testing. Save months of development time."
          />
        </div>
        {data?.map((item, index) => (
          <div
            key={index}
            className={`flex gap-8 lg:gap-4 justify-between mb-16 ${index % 2 === 0
              ? "flex-col-reverse lg:flex-row-reverse"
              : "flex-col-reverse lg:flex-row"
              }`}
          >
            <Image
              src={item.image}
              alt={item.title}
              width={590}
              height={315}
              className="rounded-[16px] lg:w-[50%]"
            />
            <div className="lg:w-[45%]">
              <div className="flex gap-2">
                <div className="text-[#1364FF] dark:text-white mt-2">
                  {item.icon}
                </div>
                <div className="text-[32px] font-bold text-black1 dark:text-white">
                  {item.title}
                </div>
              </div>
              <p className="text-lg font-medium text-[#7B7E83] dark:text-[#808389] my-4">
                {item.desc}
              </p>
              <ul
                className={`list-disc ml-4 mb-6 ${item?.isBlue
                  ? "marker:text-[#1364FF] "
                  : "marker:text-[#1AAB12]"
                  }`}
              >
                {item.features.map((feature, index) => (
                  <li
                    key={index}
                    className="text-base font-medium text-black1 dark:text-white mb-2"
                  >
                    {feature}
                  </li>
                ))}
              </ul>
              <div
                className={`text-white mb-4 text-sm font-semibold w-fit rounded-[6px] px-3 py-[6px] ${item?.isBlue ? "bg-[#1364FF] " : "bg-[#1AAB12]"
                  }`}
              >
                {item.time}
              </div>
              {item?.technologies && (
                <div className="flex flex-wrap py-2 border border-[#4D525A] rounded-[4px] w-fit">
                  {item?.technologies?.map((tech, index) => (
                    <div
                      key={index}
                      className={`flex items-center gap-2 text-black1 dark:text-white font-xs font-medium px-4 ${index !== 0 && "border-l border-[#4D525A]"
                        }`}
                    >
                      {tech.icon} {tech.text}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Features;
