﻿using Azure;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.Azure.KeyVault;
using MicroSaasWebApi.Models.AlphaCCO;
using MicroSaasWebApi.Models.PXW.AppSettings;
using MicroSaasWebApi.Properties.PXW;
using MicroSaasWebApi.Services.PXW.Interface;
using System.IdentityModel.Tokens.Jwt;
using System.Text.Json;

namespace MicroSaasWebApi.Services.PXW
{
    public class ApplicationSettingsService : IApplicationSettingsService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<ApplicationSettingsService> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;
        public ApplicationSettingsService(IHttpContextAccessor httpContextAccessor, ILogger<ApplicationSettingsService> logger,
            IConfiguration configuration)
        {
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
            _configuration = configuration;
        }

        public async Task SetAppConfigurationWithKeyVault()
        {
            Tenants? tenants = new Tenants();
            //tenants = GetTenants();
            _configuration.Bind($"{Constants.Tenants}", tenants);
            if (tenants == null)
            {
                // Handle case when no tenant matches the ClientId. You can modify this to suit your needs.
                var responseObj = new { message = "Application Settings are not loaded." };
                var responseJson = JsonSerializer.Serialize(responseObj);
                if (_httpContextAccessor?.HttpContext?.Response != null)
                {
                    _httpContextAccessor.HttpContext.Response.StatusCode = StatusCodes.Status404NotFound;
                    _httpContextAccessor.HttpContext.Response.ContentType = "application/json";
                    await _httpContextAccessor.HttpContext.Response.WriteAsync(responseJson);
                }
                return;
            }
            //Check if we can retreive KV values
            var azConfigEndpoint = Environment.GetEnvironmentVariable("APPCONFIGURATION_ENDPOINT");
            var azConfigConnectionString = Environment.GetEnvironmentVariable("APPCONFIGURATION_CONNECTIONSTRING");
            var azConfigEnvironmentLabel = Environment.GetEnvironmentVariable("APPCONFIGURATION_ENVIRONMENT_LABEL");
            var kvUri = Environment.GetEnvironmentVariable("KEYVAULT_URI");
            var tenantId = Environment.GetEnvironmentVariable("AZURE_TENANT_ID");
            var clientId = Environment.GetEnvironmentVariable("AZURE_CLIENT_ID");
            var clientSecret = Environment.GetEnvironmentVariable("AZURE_CLIENT_SECRET");

            if (string.IsNullOrEmpty(azConfigEndpoint) && string.IsNullOrEmpty(azConfigConnectionString) && string.IsNullOrEmpty(azConfigEnvironmentLabel)
            && !string.IsNullOrEmpty(kvUri) && !string.IsNullOrEmpty(tenantId) && !string.IsNullOrEmpty(clientId) && !string.IsNullOrEmpty(clientSecret))
            {

                var keyVaultClient = new SecretClient(new Uri(kvUri), new ClientSecretCredential(tenantId, clientId, clientSecret));

                try
                {
                    // Retrieve secrets from Key Vault with individual handling for missing secrets
                    KeyVaultSecret? secretApimBaseUrl = null;
                    KeyVaultSecret? secretApimKey = null;
                    KeyVaultSecret? secretApimSecretKey = null;
                    KeyVaultSecret? secretBearerTokenClientId = null;
                    KeyVaultSecret? secretBearerTokenClientSecret = null;
                    KeyVaultSecret? secretPortfolioDataApi = null;
                    KeyVaultSecret? secretPortfolioDocumentApi = null;
                    KeyVaultSecret? secretPortfolioPermissionApi = null;
                    KeyVaultSecret? secretPortfolioDataInterchangeApi = null;

                    try
                    {
                        secretApimBaseUrl = keyVaultClient.GetSecret("APIM-BASE-URL");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Warning: APIM-BASE-URL secret missing or empty: {ex.Message}");
                    }

                    try
                    {
                        secretApimKey = keyVaultClient.GetSecret("APIM-KEY");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Warning: APIM-KEY secret missing or empty: {ex.Message}");
                    }

                    try
                    {
                        secretApimSecretKey = keyVaultClient.GetSecret("APIM-SECRET-KEY");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Warning: APIM-SECRET-KEY secret missing or empty: {ex.Message}");
                    }

                    try
                    {
                        secretBearerTokenClientId = keyVaultClient.GetSecret("BEARER-TOKEN-CLIENTID");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Warning: BEARER-TOKEN-CLIENTID secret missing or empty: {ex.Message}");
                    }

                    try
                    {
                        secretBearerTokenClientSecret = keyVaultClient.GetSecret("BEARER-TOKEN-CLIENT-SECRET");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Warning: BEARER-TOKEN-CLIENT-SECRET secret missing or empty: {ex.Message}");
                    }

                    try
                    {
                        secretPortfolioDataApi = keyVaultClient.GetSecret("PORTFOLIO-DATA-API-DOMAIN");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Warning: PORTFOLIO-DATA-API-DOMAIN secret missing or empty: {ex.Message}");
                    }

                    try
                    {
                        secretPortfolioDocumentApi = keyVaultClient.GetSecret("PORTFOLIO-DOCUMENT-API-DOMAIN");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Warning: PORTFOLIO-DOCUMENT-API-DOMAIN secret missing or empty: {ex.Message}");
                    }

                    try
                    {
                        secretPortfolioPermissionApi = keyVaultClient.GetSecret("PORTFOLIO-PERMISSION-API-DOMAIN");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Warning: PORTFOLIO-PERMISSION-API-DOMAIN secret missing or empty: {ex.Message}");
                    }

                    try
                    {
                        secretPortfolioDataInterchangeApi = keyVaultClient.GetSecret("PORTFOLIO-DATAINTERCHANGE-API-DOMAIN");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Warning: PORTFOLIO-DATAINTERCHANGE-API-DOMAIN secret missing or empty: {ex.Message}");
                    }

                }
                catch (RequestFailedException ex)
                {
                    // Log the error message
                    Console.WriteLine($"Error occurred while retrieving secrets from Key Vault: {ex.Message}");

                    // Handle the error appropriately
                    if (ex.Status == 404)
                    {
                        // Secret not found
                        Console.WriteLine("One or more secrets were not found in Key Vault.");
                    }
                    else if (ex.Status == 403)
                    {
                        // Access denied
                        Console.WriteLine("Access to Key Vault is denied. Make sure the service principal has the necessary permissions.");
                    }
                    else
                    {
                        // Other errors
                        Console.WriteLine("An error occurred while accessing Key Vault.");
                    }
                }
            }
        }

        public async Task SetAppSettingsFilteredTenantInHttpContext(string tenantGuid)
        {
            var tenants = new Tenants();
            _configuration.Bind($"{Constants.Tenants}", tenants);
            var filteredTenant = tenants.FirstOrDefault(t => t.TenantGuid == tenantGuid);
            if (filteredTenant == null)
            {
                // Handle case when no tenant matches the ClientId. You can modify this to suit your needs.
                var responseObj = new { message = "Tenant is not found." };
                var responseJson = JsonSerializer.Serialize(responseObj);
                if (_httpContextAccessor?.HttpContext?.Response != null)
                {
                    _httpContextAccessor.HttpContext.Response.StatusCode = StatusCodes.Status404NotFound;
                    _httpContextAccessor.HttpContext.Response.ContentType = "application/json";
                    await _httpContextAccessor.HttpContext.Response.WriteAsync(responseJson);
                }
                return;
            }
            // Add filtered tenant to HttpContext.Items so it can be used in other parts of your application
            if (_httpContextAccessor?.HttpContext != null)
            {
                var azConfigEndpoint = Environment.GetEnvironmentVariable("APPCONFIGURATION_ENDPOINT");
                var azConfigConnectionString = Environment.GetEnvironmentVariable("APPCONFIGURATION_CONNECTIONSTRING");
                var azConfigEnvironmentLabel = Environment.GetEnvironmentVariable("APPCONFIGURATION_ENVIRONMENT_LABEL");
                var kvUri = Environment.GetEnvironmentVariable("KEYVAULT_URI");
                var tenantId = Environment.GetEnvironmentVariable("AZURE_TENANT_ID");
                var clientId = Environment.GetEnvironmentVariable("AZURE_CLIENT_ID");
                var clientSecret = Environment.GetEnvironmentVariable("AZURE_CLIENT_SECRET");

                if (string.IsNullOrEmpty(azConfigEndpoint) && string.IsNullOrEmpty(azConfigConnectionString) &&
                !string.IsNullOrEmpty(kvUri) && !string.IsNullOrEmpty(tenantId) && !string.IsNullOrEmpty(clientId) && !string.IsNullOrEmpty(clientSecret))
                {
                    // Replace "your-secret-name" with the name of the secret you want to retrieve.
                    var keyVaultClient = new SecretClient(new Uri(kvUri), new ClientSecretCredential(tenantId, clientId, clientSecret));

                    try
                    {
                        // Retrieve secrets from Key Vault with individual handling for missing secrets
                        KeyVaultSecret secretApimBaseUrl = null;
                        KeyVaultSecret secretApimKey = null;
                        KeyVaultSecret secretApimSecretKey = null;
                        KeyVaultSecret secretBearerTokenClientId = null;
                        KeyVaultSecret secretBearerTokenClientSecret = null;
                        KeyVaultSecret secretPortfolioDataApi = null;
                        KeyVaultSecret secretPortfolioDocumentApi = null;
                        KeyVaultSecret secretPortfolioPermissionApi = null;
                        KeyVaultSecret secretPortfolioDataInterchangeApi = null;

                        try
                        {
                            secretApimBaseUrl = keyVaultClient.GetSecret("APIM-BASE-URL");
                            filteredTenant.Settings.AzureAdB2C.BaseURL = secretApimBaseUrl?.Value ?? throw new Exception("APIM-BASE-URL not found");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Warning: APIM-BASE-URL secret missing or empty: {ex.Message}");
                        }

                        try
                        {
                            secretApimKey = keyVaultClient.GetSecret("APIM-KEY");
                            filteredTenant.Settings.AzureAdB2C.APIMKey = secretApimKey?.Value ?? throw new Exception("APIM-KEY not found");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Warning: APIM-KEY secret missing or empty: {ex.Message}");
                        }

                        try
                        {
                            secretApimSecretKey = keyVaultClient.GetSecret("APIM-SECRET-KEY");
                            filteredTenant.Settings.AzureAdB2C.APIMSecretKey = secretApimSecretKey?.Value ?? throw new Exception("APIM-SECRET-KEY not found");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Warning: APIM-SECRET-KEY secret missing or empty: {ex.Message}");
                        }

                        try
                        {
                            secretBearerTokenClientId = keyVaultClient.GetSecret("BEARER-TOKEN-CLIENTID");
                            filteredTenant.Settings.AzureAdB2C.BearerTokenClientID = secretBearerTokenClientId?.Value ?? throw new Exception("BEARER-TOKEN-CLIENTID not found");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Warning: BEARER-TOKEN-CLIENTID secret missing or empty: {ex.Message}");
                        }

                        try
                        {
                            secretBearerTokenClientSecret = keyVaultClient.GetSecret("BEARER-TOKEN-CLIENT-SECRET");
                            filteredTenant.Settings.AzureAdB2C.BearerTokenClientSecret = secretBearerTokenClientSecret?.Value ?? throw new Exception("BEARER-TOKEN-CLIENT-SECRET not found");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Warning: BEARER-TOKEN-CLIENT-SECRET secret missing or empty: {ex.Message}");
                        }

                        try
                        {
                            secretPortfolioDataApi = keyVaultClient.GetSecret("PORTFOLIO-DATA-API-DOMAIN");
                            filteredTenant.Settings.PortfolioDataApi.Domain = secretPortfolioDataApi?.Value ?? throw new Exception("PORTFOLIO-DATA-API-DOMAIN not found");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Warning: PORTFOLIO-DATA-API-DOMAIN secret missing or empty: {ex.Message}");
                        }

                        try
                        {
                            secretPortfolioDocumentApi = keyVaultClient.GetSecret("PORTFOLIO-DOCUMENT-API-DOMAIN");
                            filteredTenant.Settings.PortfolioDocumentApi.Domain = secretPortfolioDocumentApi?.Value ?? throw new Exception("PORTFOLIO-DOCUMENT-API-DOMAIN not found");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Warning: PORTFOLIO-DOCUMENT-API-DOMAIN secret missing or empty: {ex.Message}");
                        }

                        try
                        {
                            secretPortfolioPermissionApi = keyVaultClient.GetSecret("PORTFOLIO-PERMISSION-API-DOMAIN");
                            filteredTenant.Settings.PortfolioPermissionApi.Domain = secretPortfolioPermissionApi?.Value ?? throw new Exception("PORTFOLIO-PERMISSION-API-DOMAIN not found");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Warning: PORTFOLIO-PERMISSION-API-DOMAIN secret missing or empty: {ex.Message}");
                        }

                        try
                        {
                            secretPortfolioDataInterchangeApi = keyVaultClient.GetSecret("PORTFOLIO-DATAINTERCHANGE-API-DOMAIN");
                            filteredTenant.Settings.PortfolioDataInterchangeApi.Domain = secretPortfolioDataInterchangeApi?.Value ?? throw new Exception("PORTFOLIO-DATAINTERCHANGE-API-DOMAIN not found");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Warning: PORTFOLIO-DATAINTERCHANGE-API-DOMAIN secret missing or empty: {ex.Message}");
                        }
                    }
                    catch (RequestFailedException ex)
                    {
                        // Log the error message
                        Console.WriteLine($"Error occurred while retrieving secrets from Key Vault: {ex.Message}");

                        // Handle the error appropriately
                        if (ex.Status == 404)
                        {
                            // Secret not found
                            Console.WriteLine("One or more secrets were not found in Key Vault.");
                        }
                        else if (ex.Status == 403)
                        {
                            // Access denied
                            Console.WriteLine("Access to Key Vault is denied. Make sure the service principal has the necessary permissions.");
                        }
                        else
                        {
                            // Other errors
                            Console.WriteLine("An error occurred while accessing Key Vault.");
                        }
                    }
                }
                _httpContextAccessor.HttpContext.Items["FilteredTenant"] = filteredTenant;
                _httpContextAccessor.HttpContext.Items["TenantId"] = filteredTenant.TenantId;
            }
        }
        public Tenant GetFilteredTenant()
        {
            var context = _httpContextAccessor.HttpContext;
            var filteredTenantJson = context?.Items["FilteredTenant"] as string;
            if (string.IsNullOrEmpty(filteredTenantJson))
            {
                _logger.LogError("FilteredTenant not found in HttpContextAccessor.");
                throw new Exception("FilteredTenant not found in HttpContextAccessor.");
            }
            else
            {
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                };
                var objTenant = JsonSerializer.Deserialize<Tenant>(filteredTenantJson, options);
                _httpContextAccessor.HttpContext.Items["TenantId"] = objTenant?.TenantId;
                return objTenant;
            }
        }

        public async Task SetAppSettingsEndPointsInHttpContext()
        {
            //Using IConfiguration
            var endpoints = new Endpoints();
            _configuration.Bind($"{Constants.Configurations}:{Constants.Endpoints}", endpoints);
            // Check if a specific property of 'endpoints' is still its default value
            // Replace 'SomeProperty' with the actual property you want to check.
            if (endpoints.PortfolioDataApiEndpoints == null || endpoints.PortfolioDocumentApiEndpoints == null)
            {
                var responseObj = new { message = "Endpoints in AppSettings not found." };
                var responseJson = JsonSerializer.Serialize(responseObj);
                if (_httpContextAccessor?.HttpContext?.Response != null)
                {
                    _httpContextAccessor.HttpContext.Response.StatusCode = StatusCodes.Status404NotFound;
                    _httpContextAccessor.HttpContext.Response.ContentType = "application/json";
                    await _httpContextAccessor.HttpContext.Response.WriteAsync(responseJson);
                }
                return;
            }
            // Add endpoints to HttpContext.Items so it can be used in other parts of your application
            if (_httpContextAccessor?.HttpContext != null)
            {
                _httpContextAccessor.HttpContext.Items["Endpoints"] = endpoints;
            }
        }

        public async Task SetAlphaCCOWorkflowsInHttpContext()
        {
            Tenant? filteredTenantJson = _httpContextAccessor.HttpContext?.Items["FilteredTenant"] as Tenant;
            // Check if a specific property of 'endpoints' is still its default value
            // Replace 'SomeProperty' with the actual property you want to check.
            if (filteredTenantJson?.Settings?.AlphaCCOWorkflows == null)
            {
                var responseObj = new { message = "AlphaCCOWorkflows in AppSettings is not found." };
                var responseJson = JsonSerializer.Serialize(responseObj);
                if (_httpContextAccessor?.HttpContext?.Response != null)
                {
                    _httpContextAccessor.HttpContext.Response.StatusCode = StatusCodes.Status404NotFound;
                    _httpContextAccessor.HttpContext.Response.ContentType = "application/json";
                    await _httpContextAccessor.HttpContext.Response.WriteAsync(responseJson);
                }
                return;
            }
            // Add endpoints to HttpContext.Items so it can be used in other parts of your application
            if (_httpContextAccessor?.HttpContext != null)
            {
                _httpContextAccessor.HttpContext.Items["AlphaCCOWorkflows"] = filteredTenantJson?.Settings?.AlphaCCOWorkflows;
            }
        }
        public AlphaCCOWorkflows GetAlphaCCOWorkflows()
        {
            var context = _httpContextAccessor.HttpContext;
            var alphaCCOWorkflows = context?.Items["AlphaCCOWorkflows"] as AlphaCCOWorkflows;
            if (alphaCCOWorkflows == null || !alphaCCOWorkflows.Any())
            {
                _logger.LogError("AlphaCCOWorkflows not found in system.");
                throw new Exception("AlphaCCOWorkflows not found in system.");
            }
            else
            {
                return alphaCCOWorkflows;
            }
        }
        public void SetTokenValuesInHttpContext(JwtSecurityToken decodedToken)
        {
            var iss = decodedToken.Issuer;
            //var tfp = decodedToken.Payload["tfp"].ToString(); // Sign-in policy name
            //var metadataEndpoint = $"{iss}.well-known/openid-configuration?p={tfp}";
            string UserId = decodedToken?.Claims?.FirstOrDefault(ee => ee.Type.Contains("oid"))?.Value;
            // Add endpoints to HttpContext.Items so it can be used in other parts of your application
            if (_httpContextAccessor?.HttpContext != null && UserId != string.Empty)
            {
                _httpContextAccessor.HttpContext.Items["UserId"] = UserId;
            }
        }
        public void SetLanguageInHttpContext(string? language)
        {
            if (_httpContextAccessor?.HttpContext != null && language != string.Empty)
            {
                _httpContextAccessor.HttpContext.Items["Language"] = ApiHelper.GetLanguageId(language);
            }
        }
    }
}
