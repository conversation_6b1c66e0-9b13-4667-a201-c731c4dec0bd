﻿using System.ComponentModel;

namespace MicroSaasWebApi.Properties.Enums
{
    /// <summary>
    /// Summary description for eAccountMemberType
    /// </summary>
    public enum eAccountMemberType
    {
        Individual = 0,
        Institutional = 1
    }
    /// <summary>
    /// Summary description for eAccountProfileType
    /// </summary>
    public enum eAccountProfileType
    {
        Owner,
        Manager,
        Viewer
    }
    public enum eAccountType
    {
        Unknown, Cash, TFSA, RRSP, LIRS, RESP, CASHCAD, CASHUSD, TFSACAD, TFSAUSD, RRSPCAD, RRSPUSD, LIRSCAD, LIRSUSD, RRSPSPOUSECAD, RRSPSPOUSEUSD, RIF, RESPCAD, RESPUSD, SAVING
    }
    /// <summary>
    /// Summary description for eCurrency
    /// </summary>
    public enum eCurrency
    {
        CAD, USD, EUR
    }

    /// <summary>
    /// Summary description for eAccountType
    /// </summary>
    /// 
    [Serializable]
    public enum eWorkflowStatus
    {
        [Description("Completed")]
        Completed,
        [Description("Error Occurred")]
        Error,
        [Description("Scheduled")]
        Scheduled,
        [Description("Not Started")]
        NotStart,
        [Description("In Process")]
        InProcess,
        [Description("Suspended")]
        Suspended,
        [Description("Awaiting Approval")]
        Awaiting,
        [Description("Stopped by user")]
        Stopped,
        [Description("Disabled")]
        Disabled,
        [Description("Pending")]
        Pending,
        [Description("Skipped")]
        Skipped
    }
}
