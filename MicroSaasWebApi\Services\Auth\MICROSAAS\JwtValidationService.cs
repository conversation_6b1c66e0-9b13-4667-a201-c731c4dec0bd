﻿using Microsoft.IdentityModel.Protocols;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Microsoft.IdentityModel.Tokens;
using MicroSaasWebApi.Models.PXW.AppSettings;
using MicroSaasWebApi.Services.Auth.PXW.Interface;
using MicroSaasWebApi.Services.PXW.Interface;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Constants = MicroSaasWebApi.Properties.PXW.Constants;

namespace MicroSaasWebApi.Services.Auth.PXW
{
    public class JwtValidationService : IJwtValidationService
    {
        private readonly IConfiguration _configuration;
        private readonly ILoggerService _loggerService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public JwtValidationService(IConfiguration configuration, ILoggerService responseLoggerService,
            IHttpContextAccessor httpContextAccessor)
        {
            _configuration = configuration;
            _loggerService = responseLoggerService;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<TokenValidationParameters> GetTokenValidationParametersAsync(string decodedTokenClientId, bool is_using_magic_link = false)
        {
            // Assuming the filteredTenant value is in JSON format, you can use a JSON serializer to deserialize it into the Tenants object
            Tenant? filteredTenant = _httpContextAccessor?.HttpContext?.Items["FilteredTenant"] as Tenant;
            AzureAdB2C? azureAdB2C = filteredTenant?.Settings?.AzureAdB2C;
            string? clientId = azureAdB2C?.ClientId;
            string? instance = azureAdB2C?.Instance;
            string? tenantId = azureAdB2C?.TenantId;
            var signUpSignIn = azureAdB2C?.SignUpSignInPolicyId;
            var metadataAddress = azureAdB2C?.MetadataUrl;
            if (is_using_magic_link == true)
            {
                signUpSignIn = Constants.B2C_MAGIC_LINK_POLICY;
                metadataAddress = $"{azureAdB2C?.Instance}/{azureAdB2C?.Domain}/{signUpSignIn}/v2.0/.well-known/openid-configuration";
            }
            var authority = $"{azureAdB2C?.Instance}/{azureAdB2C?.Domain}/{signUpSignIn}/oauth2/v2.0/authorize";
            var claimsIssuer = $"{azureAdB2C?.Instance}/tfp/{tenantId}/{signUpSignIn}/v2.0/";
            //var claimsIssuerWithoutTFP = $"{azureAdB2C?.Instance}/{tenantId}/v2.0/";
            var claimsIssuerWithoutTFP = azureAdB2C?.Issuer;
            var configurationManager = new ConfigurationManager<OpenIdConnectConfiguration>(metadataAddress,
                new OpenIdConnectConfigurationRetriever(),
                new HttpDocumentRetriever());

            var discoveryDocument = await configurationManager.GetConfigurationAsync();
            var signingKeys = discoveryDocument.SigningKeys;

            // Logic to get tenant-specific JWT validation parameters, such as the issuer and signing keys
            TokenValidationParameters? tokenValidationParameters = new TokenValidationParameters()
            {
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = true,
                ValidateIssuerSigningKey = true,
                ValidIssuer = claimsIssuerWithoutTFP,
                ValidAudience = clientId,
                IssuerSigningKeys = signingKeys,
                ClockSkew = TimeSpan.FromMinutes(2),
                ValidTypes = new[] { "at+jwt", "jwt", "JWT" },
                ValidAlgorithms = new[] { "RS256", "RS384", "RS512", "ES256", "ES384", "ES512" }
            };
            return await Task.FromResult(tokenValidationParameters);
        }

        public async Task<ClaimsPrincipal> ValidateToken(string token, string decodedTokenClientId)
        {
            SecurityToken? validatedToken;
            ClaimsPrincipal? claimsPrincipal = null;
            try
            {
                var jwtSecurityTokenHandler = new JwtSecurityTokenHandler();
                var decodedToken = jwtSecurityTokenHandler.ReadJwtToken(token);
                var isUsingMagicLinkValue = decodedToken.Claims.FirstOrDefault(c => c.Type == "is_using_magic_link")?.Value;
                bool isUsingMagicLink;
                bool.TryParse(isUsingMagicLinkValue, out isUsingMagicLink);
                TokenValidationParameters? validationParameters = await GetTokenValidationParametersAsync(decodedTokenClientId, isUsingMagicLink);

                claimsPrincipal = jwtSecurityTokenHandler.ValidateToken(token, validationParameters, out validatedToken);
            }
            catch (InvalidCastException ex)
            {
                await _loggerService.LogInformationAsync($"Invalid Cast.Token validation failed! Message : {ex.Message}");
                throw new Exception($"Invalid Cast.Token validation failed : {ex.Message}");
            }
            catch (SecurityTokenException ex)
            {
                await _loggerService.LogInformationAsync($"Token validation failed! Message : {ex.Message}");
                throw new Exception($"Security Token Exception.Token validation failed : {ex.Message}");
            }
            catch (Exception ex)
            {
                await _loggerService.LogInformationAsync($"An error occurred while validating the token. Message : {ex.Message}");
                throw new Exception($"An error occurred while validating the token : {ex.Message}");
            }
            return claimsPrincipal;
        }
    }

}
