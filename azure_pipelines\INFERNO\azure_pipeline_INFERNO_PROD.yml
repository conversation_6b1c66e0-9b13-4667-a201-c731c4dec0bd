extends:
  template: ../../azure_pipeline_template.yml
  parameters:
    environment: PROD
    client: INFERNO
    dockerRegistryServiceConnection: "prod-inferno-acr-azdvp-sc"
    imageRepository: "prod-canso-webapi"
    containerRegistry: "prodcansoacr.azurecr.io"
    dockerfilePath: "$(Build.SourcesDirectory)/Dockerfile"
    tag: "INFERNO-PROD-$(Build.BuildId)"
    poolName: "terraform-pool"
    azureSubscription: "prod-inferno-apps-azdvp-sc"
    webAppName: "prod-canso-webapi-appService"
    appSettingsEnvironment: "-ASPNETCORE_ENVIRONMENT Staging"
