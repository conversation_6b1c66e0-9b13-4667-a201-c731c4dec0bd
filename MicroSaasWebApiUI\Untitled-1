import itertools
import operator

ops = [('+', operator.add), ('-', operator.sub), ('*', operator.mul), ('/', operator.truediv)]
       
def four_four(target):
    num = [4, 4, 4, 4]
    op_syms = [op[0] for op in ops]
    op_funcs = [op[1] for op in ops]
    results = set()

    for op_combo in itertools.product(range(len(ops)), repeat=3):
        symbols = [op_syms[i] for i in op_combo]

        funcs = [op_funcs[i] for i in op_combo]
        print(symbols, funcs)
        for perm in itertools.permutations(num):
            expr = f"{perm[0]}{symbols[0]}{perm[1]}{symbols[1]}{perm[2]}{symbols[2]}{perm[3]}"
            try:
                result = eval(expr)
                if result == target:
                    results.add(expr)
            except ZeroDivisionError:
                pass
    return results