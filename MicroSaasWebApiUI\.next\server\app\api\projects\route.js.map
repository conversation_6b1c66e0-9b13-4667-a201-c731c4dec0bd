{"version": 3, "file": "app/api/projects/route.js", "mappings": "oFAAAA,CAAAA,EAAAC,OAAA,CAAAC,QAAA,0CCAAF,CAAAA,EAAAC,OAAA,CAAAC,QAAA,iZEKO,IAAMC,EAAU,gBACVC,EAAU,OAEhB,eAAeC,IACrB,GAAI,CACH,IAAMC,EAAO,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,IAEnB,GAAI,CAACD,EACJ,OAAOE,EAAAA,EAAYA,CAACC,IAAI,CACvB,CAAEC,MAAO,cAAe,EACxB,CAAEC,OAAQ,GAAI,GAIhB,IAAMC,EAAW,MAAMC,EAAAA,CAAMA,CAACC,OAAO,CAACC,QAAQ,CAAC,CAC9CC,MAAO,CACNC,cAAeX,EAAKY,EAAE,CAExB,GAEA,GAAI,CAACN,GAAYA,IAAAA,EAASO,MAAM,CAC/B,OAAOX,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACxBW,QAAS,GACTR,SAAU,EAAE,CACZS,QAAS,mBACV,GAGD,OAAOb,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACxBW,QAAS,GACTR,SAAAA,CACD,EACD,CAAE,MAAOU,EAAK,CAEb,OADAC,QAAQb,KAAK,CAAC,oBAAqBY,GAC5Bd,EAAAA,EAAYA,CAACC,IAAI,CACvB,CAAEC,MAAOY,GAAKD,SAAW,8BAA+B,EACxD,CAAEV,OAAQ,GAAI,EAEhB,CACD,CCrCA,IAAAa,EAAA,IAAwBC,EAAAC,mBAAmB,EAC3CC,WAAA,CACAC,KAAcC,EAAAC,CAAS,CAAAC,SAAA,CACvBC,KAAA,sBACAC,SAAA,gBACAC,SAAA,QACAC,WAAA,wBACA,EACAC,iBAAA,kEACAC,iBAVA,GAWAC,SAAYC,CACZ,GAIA,CAAQC,oBAAAA,CAAA,CAAAC,6BAAAA,CAAA,CAAAC,YAAAA,CAAA,EAAiElB,EACzEmB,EAAA,sBACA,SAAAC,IACA,MAAW,GAAAC,EAAAC,EAAA,EAAW,CACtBJ,YAAAA,EACAD,6BAAAA,CACA,EACA,CC1BO,IAAAM,EAAqBC,EAC5BC,EAAeC,EAAAC,CAAsB,CAAAC,IAAA,CAAM5B,wECF3C,eAAAjB,IACE8C,EAAQ,MACV,IAAUC,OAAAA,CAAA,EAAW,GAAAC,EAAAC,CAAA,WACrB,EAGS,GAAAC,EAAAC,CAAA,IAAWC,KAAA,CAAAC,OAAA,CAAAN,GAFpB,IAGA,gDCLA,IAAAO,EAFe,GAAIC,UAAAA,YAAYA", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/ignored|C:\\Projects\\PersonalPortal\\node_modules\\crypto-js|crypto", "webpack://_N_E/./src/app/api/projects/route.ts", "webpack://_N_E/./src/app/api/projects/route.ts?b005", "webpack://_N_E/", "webpack://_N_E/./node_modules/@clerk/nextjs/dist/esm/app-router/server/currentUser.js", "webpack://_N_E/./src/libs/prisma.ts"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "/* (ignored) */", "import prisma from '@/libs/prisma'\r\nimport { currentUser } from '@clerk/nextjs/server'\r\nimport { NextResponse } from 'next/server'\r\n\r\n// Mark this route as dynamic\r\nexport const dynamic = 'force-dynamic'\r\nexport const runtime = 'edge'\r\n\r\nexport async function GET() {\r\n\ttry {\r\n\t\tconst user = await currentUser()\r\n\r\n\t\tif (!user) {\r\n\t\t\treturn NextResponse.json(\r\n\t\t\t\t{ error: 'Unauthorized' },\r\n\t\t\t\t{ status: 401 }\r\n\t\t\t)\r\n\t\t}\r\n\r\n\t\tconst projects = await prisma.project.findMany({\r\n\t\t\twhere: {\r\n\t\t\t\tuser_clerk_id: user.id,\r\n\t\t\t},\r\n\t\t})\r\n\r\n\t\tif (!projects || projects.length === 0) {\r\n\t\t\treturn NextResponse.json({\r\n\t\t\t\tsuccess: true,\r\n\t\t\t\tprojects: [],\r\n\t\t\t\tmessage: 'No projects found'\r\n\t\t\t})\r\n\t\t}\r\n\r\n\t\treturn NextResponse.json({ \r\n\t\t\tsuccess: true, \r\n\t\t\tprojects \r\n\t\t})\r\n\t} catch (err) {\r\n\t\tconsole.error('Connection error:', err)\r\n\t\treturn NextResponse.json(\r\n\t\t\t{ error: err?.message || 'An unexpected error occurred' },\r\n\t\t\t{ status: 500 }\r\n\t\t)\r\n\t}\r\n}\r\n", "import { AppRouteRouteModule } from \"next/dist/server/future/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/future/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Projects\\\\PersonalPortal\\\\src\\\\app\\\\api\\\\projects\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/projects/route\",\n        pathname: \"/api/projects\",\n        filename: \"route\",\n        bundlePath: \"app/api/projects/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\PersonalPortal\\\\src\\\\app\\\\api\\\\projects\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/projects/route\";\nfunction patchFetch() {\n    return _patchFetch({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\nexport { routeModule, requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, originalPathname, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "import { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fprojects%2Froute&page=%2Fapi%2Fprojects%2Froute&pagePath=private-next-app-dir%2Fapi%2Fprojects%2Froute.ts&appDir=C%3A%5CProjects%5CPersonalPortal%5Csrc%5Capp&appPaths=%2Fapi%2Fprojects%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/projects/route.ts?__next_edge_ssr_entry__\";\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule);\n\n//# sourceMappingURL=edge-app-route.js.map", "import { clerkClient } from \"../../server/clerkClient\";\nimport { auth } from \"./auth\";\nasync function currentUser() {\n  require(\"server-only\");\n  const { userId } = auth();\n  if (!userId) {\n    return null;\n  }\n  return clerkClient().users.getUser(userId);\n}\nexport {\n  currentUser\n};\n//# sourceMappingURL=currentUser.js.map", "import { PrismaClient } from \"@prisma/client\";\r\n\r\nconst prisma = new PrismaClient();\r\n\r\nexport default prisma;\r\n"], "names": ["module", "exports", "require", "dynamic", "runtime", "GET", "user", "currentUser", "NextResponse", "json", "error", "status", "projects", "prisma", "project", "find<PERSON>any", "where", "user_clerk_id", "id", "length", "success", "message", "err", "console", "routeModule", "module_compiled", "AppRouteRouteModule", "definition", "kind", "route_kind", "x", "APP_ROUTE", "page", "pathname", "filename", "bundlePath", "resolvedPagePath", "nextConfigOutput", "userland", "route_namespaceObject", "requestAsyncStorage", "staticGenerationAsyncStorage", "serverHooks", "originalPathname", "patchFetch", "patch_fetch", "XH", "ComponentMod", "route_next_edge_ssr_entry_namespaceObject", "next_edge_app_route_loaderabsolutePagePath_private_next_app_dir_2Fapi_2Fprojects_2Froute_ts_page_2Fapi_2Fprojects_2Froute_appDirLoader_bmV4dC1hcHAtbG9hZGVyP25hbWU9YXBwJTJGYXBpJTJGcHJvamVjdHMlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRnByb2plY3RzJTJGcm91dGUmcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZwcm9qZWN0cyUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDUHJvamVjdHMlNUNQZXJzb25hbFBvcnRhbCU1Q3NyYyU1Q2FwcCZhcHBQYXRocz0lMkZhcGklMkZwcm9qZWN0cyUyRnJvdXRlJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEIQ_3D_3D_nextConfigOutput_preferredRegion_middlewareConfig_e30_3D_", "edge_route_module_wrapper", "a", "wrap", "__webpack_require__", "userId", "_auth__WEBPACK_IMPORTED_MODULE_0__", "I", "_server_clerkClient__WEBPACK_IMPORTED_MODULE_1__", "N", "users", "getUser", "__WEBPACK_DEFAULT_EXPORT__", "PrismaClient"], "sourceRoot": ""}