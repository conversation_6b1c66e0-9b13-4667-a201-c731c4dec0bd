(()=>{var e={};e.id=702,e.ids=[702],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},61212:e=>{"use strict";e.exports=require("async_hooks")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},85807:e=>{"use strict";e.exports=require("module")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},6005:e=>{"use strict";e.exports=require("node:crypto")},84492:e=>{"use strict";e.exports=require("node:stream")},27596:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>h,originalPathname:()=>d,pages:()=>c,routeModule:()=>m,tree:()=>u}),r(95752),r(8130),r(7629),r(12523);var s=r(23191),n=r(88716),i=r(37922),a=r.n(i),o=r(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let u=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,95752)),"C:\\Projects\\PersonalPortal\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e),async e=>(await Promise.resolve().then(r.bind(r,71150))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,63601))).default(e)],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,51922))).default(e)],twitter:[async e=>(await Promise.resolve().then(r.bind(r,40680))).default(e)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,8130)),"C:\\Projects\\PersonalPortal\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,7629)),"C:\\Projects\\PersonalPortal\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Projects\\PersonalPortal\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e),async e=>(await Promise.resolve().then(r.bind(r,71150))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,63601))).default(e)],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,51922))).default(e)],twitter:[async e=>(await Promise.resolve().then(r.bind(r,40680))).default(e)],manifest:void 0}}],c=["C:\\Projects\\PersonalPortal\\src\\app\\dashboard\\page.tsx"],d="/dashboard/page",h={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},30783:(e,t,r)=>{let s={"1c548c926b5e17db6a6e381a3f15e16b07a70dca":()=>Promise.resolve().then(r.bind(r,44393)).then(e=>e.getSubscriptionByUserId),"78d93be0f3171de0df85ce5e42f8418a9c8f37e4":()=>Promise.resolve().then(r.bind(r,44393)).then(e=>e.processCheckoutSuccessWebhook),cca4f3aba61e31f11eb6718d2992c5e458bb5109:()=>Promise.resolve().then(r.bind(r,44393)).then(e=>e.processInvoicePaid),f377f06404a2b61735b4a2be973e74b304c9cb4d:()=>Promise.resolve().then(r.bind(r,44393)).then(e=>e.processSubscriptonDelete)};async function n(e,...t){return(await s[e]()).apply(null,t)}e.exports={"1c548c926b5e17db6a6e381a3f15e16b07a70dca":n.bind(null,"1c548c926b5e17db6a6e381a3f15e16b07a70dca"),"78d93be0f3171de0df85ce5e42f8418a9c8f37e4":n.bind(null,"78d93be0f3171de0df85ce5e42f8418a9c8f37e4"),cca4f3aba61e31f11eb6718d2992c5e458bb5109:n.bind(null,"cca4f3aba61e31f11eb6718d2992c5e458bb5109"),f377f06404a2b61735b4a2be973e74b304c9cb4d:n.bind(null,"f377f06404a2b61735b4a2be973e74b304c9cb4d")}},51137:(e,t,r)=>{Promise.resolve().then(r.bind(r,90837)),Promise.resolve().then(r.bind(r,57263)),Promise.resolve().then(r.bind(r,492))},36049:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var s=r(10326),n=r(17577),i=r(66775),a=r(93699);let o=({priceId:e,disabled:t=!1})=>{let[r,o]=(0,n.useState)(!1),[l,u]=(0,n.useState)(null),{isLoaded:c,isSignedIn:d,user:h}=(0,i.aF)(),m=async()=>{if(!c||!d){u("Please sign in to proceed with checkout");return}h&&await (0,a.D)(e,h.id,h.primaryEmailAddress?.emailAddress||null,o,u)};return c?(0,s.jsxs)("div",{children:[s.jsx("button",{className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition duration-300",onClick:m,disabled:r||!d||t,children:r?"Processing...":d?"Proceed to Checkout":"Sign in to Checkout"}),l&&s.jsx("p",{className:"text-red-500 mt-2",children:l})]}):s.jsx("div",{children:"Loading..."})}},90837:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var s=r(10326);let n=(0,r(62881).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);var i=r(36049);function a(){return s.jsx("section",{className:"py-16",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[s.jsx("h2",{className:"text-3xl font-bold text-center mb-12 dark:text-white text-black1",children:"Photography Packages"}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:"dark:bg-[#131211] bg-white rounded-lg shadow-md p-8",children:[s.jsx("h3",{className:"text-2xl font-semibold mb-4",children:"Quick Shoots"}),(0,s.jsxs)("p",{className:"text-4xl font-bold mb-6",children:["$300",s.jsx("span",{className:"text-xl dark:text-gray-500 text-black1/70 font-normal",children:"/session"})]}),(0,s.jsxs)("ul",{className:"space-y-3 mb-8",children:[(0,s.jsxs)("li",{className:"flex items-center",children:[s.jsx(n,{className:"text-green-500 mr-2"}),s.jsx("span",{children:"One hour of footage time"})]}),(0,s.jsxs)("li",{className:"flex items-center",children:[s.jsx(n,{className:"text-green-500 mr-2"}),s.jsx("span",{children:"50 edited photos"})]}),(0,s.jsxs)("li",{className:"flex items-center",children:[s.jsx(n,{className:"text-green-500 mr-2"}),s.jsx("span",{children:"Help with posing"})]}),(0,s.jsxs)("li",{className:"flex items-center",children:[s.jsx(n,{className:"text-green-500 mr-2"}),s.jsx("span",{children:"Location list to pick from"})]}),(0,s.jsxs)("li",{className:"flex items-center",children:[s.jsx(n,{className:"text-green-500 mr-2"}),s.jsx("span",{children:"7 business days turn around time"})]})]}),s.jsx(i.Z,{priceId:"price_1R6aslP82YH9JfOlJZcweC4d"})]}),(0,s.jsxs)("div",{className:"dark:bg-[#131211] bg-white rounded-lg shadow-md p-8 border-4 border-blue-500 relative",children:[s.jsx("div",{className:"absolute top-0 right-0 bg-blue-500 text-white py-1 px-4 rounded-bl-lg text-sm font-semibold",children:"Most Popular"}),s.jsx("h3",{className:"text-2xl font-semibold mb-4",children:"Mini Shoots"}),(0,s.jsxs)("p",{className:"text-4xl font-bold mb-2",children:["$150",s.jsx("span",{className:"text-xl dark:text-gray-500 text-black1/70 font-normal",children:"/session"})]}),s.jsx("p",{className:"text-green-600 font-semibold mb-6",children:"Perfect for professional portraits"}),(0,s.jsxs)("ul",{className:"space-y-3 mb-8",children:[(0,s.jsxs)("li",{className:"flex items-center",children:[s.jsx(n,{className:"text-green-500 mr-2"}),s.jsx("span",{children:"30 minutes of footage time"})]}),(0,s.jsxs)("li",{className:"flex items-center",children:[s.jsx(n,{className:"text-green-500 mr-2"}),s.jsx("span",{children:"25 edited photos"})]}),(0,s.jsxs)("li",{className:"flex items-center",children:[s.jsx(n,{className:"text-green-500 mr-2"}),s.jsx("span",{children:"Help with posing"})]}),(0,s.jsxs)("li",{className:"flex items-center",children:[s.jsx(n,{className:"text-green-500 mr-2"}),s.jsx("span",{children:"Location list to pick from"})]}),(0,s.jsxs)("li",{className:"flex items-center",children:[s.jsx(n,{className:"text-green-500 mr-2"}),s.jsx("span",{children:"7 business days turn around time"})]})]}),s.jsx(i.Z,{priceId:"price_1R6avXP82YH9JfOlIGpAfwjU"})]})]})]})})}},57263:(e,t,r)=>{"use strict";r.d(t,{default:()=>p});var s=r(10326),n=r(91664),i=r(17577),a=r(12358);let o=i.forwardRef(({className:e,type:t,...r},n)=>s.jsx("input",{type:t,className:(0,a.cn)("flex h-10 w-full rounded-md border border-slate-200 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-slate-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-slate-800 dark:bg-slate-950 dark:ring-offset-slate-950 dark:placeholder:text-slate-400 dark:focus-visible:ring-slate-300",e),ref:n,...r}));o.displayName="Input";var l=r(44099),u=r(23703),c=r(46226),d=r(90434),h=r(10123);let m=e=>{let t=e.reduce((e,t)=>(e[t]=h.Z_().required(`${t} is required`),e),{});return h.Ry().shape(t)},p=()=>{let[e,t]=(0,i.useState)([]),[r,a]=(0,i.useState)(!1),h=async()=>{try{let e=await l.Z.get("/api/projects");e.data.projects&&t(e.data.projects)}catch(e){console.error("Failed to fetch projects:",e)}},p=async e=>{try{(await l.Z.post("/api/active",{projectId:e})).data.success&&await h()}catch(e){console.error("Failed to activate project:",e)}},f=async(e,t)=>{try{a(!0),(await l.Z.post(t,{id:e.scenarioId,...e})).data.success&&await h()}catch(e){console.error("Failed to clone scenario:",e)}finally{a(!1)}};return(0,i.useEffect)(()=>{h()},[]),(0,s.jsxs)("div",{className:"flex flex-col w-full items-center justify-start gap-4",children:[s.jsx("h1",{className:"text-4xl font-bold",children:"Scenarios Templates"}),s.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 w-11/12 max-w-7xl",children:[{id:0,image:"/scenarios/make.png",name:"OpenAI Scenario Make",description:"This is a scenario template for OpenAI using Make",fields:["apiKey","apiOrg","assistantId"],route:"/api/scenarios/openAIAssistant"},{id:"your scenario id",image:"/scenarios/n8n.png",name:"OpenAI Scenario n8n",description:"This is a scenario template for OpenAI using n8n",fields:["apiKey","apiOrg","assistantId"],route:"/api/workflows/openAIAssistant"}].map(e=>{let t=e.fields.reduce((e,t)=>(e[t]="",e),{scenarioId:e.id});return s.jsxs("div",{className:"flex flex-col justify-between glass rounded-xl border border-gray-600 bg items-start p-4 min-h-40",children:[s.jsx(c.default,{src:e.image,alt:e.name,width:500,height:500,className:"w-full rounded-lg"}),s.jsx("p",{className:"text-2xl mt-2 text-center font-bold w-full truncate text-wrap",children:e.name}),s.jsx("p",{className:"text-sm w-full text-center text-gray-400",children:e.description}),s.jsx(u.J9,{initialValues:t,validationSchema:m(e.fields),onSubmit:t=>f(t,e.route),children:({errors:t,touched:i})=>s.jsxs(u.l0,{className:"w-full space-y-2 mt-2",children:[e.fields.map(e=>s.jsxs("div",{children:[s.jsx(u.gN,{as:o,name:e,placeholder:e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase()),className:"w-full"}),t[e]&&i[e]&&"string"==typeof t[e]&&s.jsx("div",{className:"text-red-500 text-sm",children:t[e]})]},e)),s.jsx(n.z,{type:"submit",className:"w-full",disabled:r,children:r?"Creating...":"Create Agent"})]})})]},e.id)})}),s.jsx("h1",{className:"text-4xl font-bold",children:"My Projects"}),s.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 w-11/12 max-w-7xl",children:e?.map(e=>s.jsxs("div",{className:"flex flex-col space-y-3 justify-between glass rounded-xl border border-gray-600 bg items-start p-4 min-h-40",children:[s.jsxs("div",{className:"flex w-full justify-between",children:[s.jsx("p",{className:"text-lg font-bold w-full truncate text-wrap",children:e.type}),s.jsx(n.z,{onClick:()=>p(e.id),variant:"outline",children:"active"==e.status?s.jsx("p",{className:"text-green-500",children:"Active"}):s.jsx("p",{className:"text-red-500",children:"Inactive"})})]}),s.jsxs("div",{className:"flex w-full flex-col gap-2",children:[s.jsxs("p",{className:"w-full truncate",children:["Assistant ID: ",e.assistant_id]}),s.jsxs("p",{className:"w-full truncate",children:["Webhook Link: ",e.webhookLink]})]}),s.jsx(d.default,{href:`/chat/${e.id}`,className:"w-full",children:s.jsx(n.z,{className:"w-full mt-2",children:"Go to chat"})})]},e.id))})]})}},492:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var s=r(10326),n=r(17577),i=r(35047);function a(){let e=(0,i.useRouter)(),t=(0,i.usePathname)(),r=(0,i.useSearchParams)().get("session_id"),[a,o]=(0,n.useState)(!!r);return a?s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"bg-white p-8 rounded-lg shadow-lg text-center",children:[s.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Thank You!"}),s.jsx("p",{className:"mb-6",children:"We appreciate your support."}),s.jsx("button",{onClick:()=>{e.replace(t),o(!1)},className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-full text-lg transition duration-300 ease-in-out transform hover:scale-105",children:"Start"})]})}):null}},93699:(e,t,r)=>{"use strict";r.d(t,{D:()=>v});var s,n="https://js.stripe.com/v3",i=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,a=function(){for(var e=document.querySelectorAll('script[src^="'.concat(n,'"]')),t=0;t<e.length;t++){var r=e[t];if(i.test(r.src))return r}return null},o=function(e){var t=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",r=document.createElement("script");r.src="".concat(n).concat(t);var s=document.head||document.body;if(!s)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return s.appendChild(r),r},l=function(e,t){e&&e._registerWrapper&&e._registerWrapper({name:"stripe-js",version:"4.6.0",startTime:t})},u=null,c=null,d=null,h=function(e,t,r){if(null===e)return null;var s=e.apply(void 0,t);return l(s,r),s},m=!1,p=function(){return s||(s=(null!==u?u:(u=new Promise(function(e,t){if("undefined"==typeof window||"undefined"==typeof document){e(null);return}if(window.Stripe,window.Stripe){e(window.Stripe);return}try{var r,s=a();s?s&&null!==d&&null!==c&&(s.removeEventListener("load",d),s.removeEventListener("error",c),null===(r=s.parentNode)||void 0===r||r.removeChild(s),s=o(null)):s=o(null),d=function(){window.Stripe?e(window.Stripe):t(Error("Stripe.js not available"))},c=function(){t(Error("Failed to load Stripe.js"))},s.addEventListener("load",d),s.addEventListener("error",c)}catch(e){t(e);return}})).catch(function(e){return u=null,Promise.reject(e)})).catch(function(e){return s=null,Promise.reject(e)}))};Promise.resolve().then(function(){return p()}).catch(function(e){m||console.warn(e)});let f=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];m=!0;var s=Date.now();return p().then(function(e){return h(e,t,s)})}("pk_live_51Qfy9dP82YH9JfOlFmwTk0RHvE7l6sow7ImJBI4c1GzcbhFydoVpb3DMevP8B3C4gj95qCoEise8vEhgFgWxhLEN000RfInKNH"),v=async(e,t,r,s,n)=>{if(!r)throw Error("User email not available");s(!0),n(null);try{console.log("Initiating checkout for priceId:",e),console.log("User data:",{id:t,email:r});let s=await fetch("/api/stripe/create-checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({priceId:e,userId:t,email:r})});if(!s.ok){let e=await s.text();throw console.error("Server responded with an error:",s.status,e),Error(`HTTP error! status: ${s.status}`)}let n=await s.json();if(console.log("Received data from server:",n),!n.sessionId)throw console.error("Server response:",n),Error("No sessionId received from the server");let i=await f;if(!i)throw Error("Failed to load Stripe");let{error:a}=await i.redirectToCheckout({sessionId:n.sessionId});if(a)throw console.error("Stripe redirectToCheckout error:",a),a}catch(e){console.error("Checkout error:",e),n(e instanceof Error?e.message:"An unexpected error occurred")}finally{s(!1)}}},40618:(e,t)=>{"use strict";function r(e){for(let t=0;t<e.length;t++){let r=e[t];if("function"!=typeof r)throw Error(`A "use server" file can only export async functions, found ${typeof r}.
Read more: https://nextjs.org/docs/messages/invalid-use-server-value`)}}Object.defineProperty(t,"h",{enumerable:!0,get:function(){return r}})},24330:(e,t,r)=>{"use strict";Object.defineProperty(t,"j",{enumerable:!0,get:function(){return n}});let s=r(51749);function n(e,t){return(0,s.registerServerReference)(t,e,null)}},37971:(e,t,r)=>{"use strict";var s=r(97049),n={stream:!0},i=new Map;function a(e){var t=globalThis.__next_require__(e);return"function"!=typeof t.then||"fulfilled"===t.status?null:(t.then(function(e){t.status="fulfilled",t.value=e},function(e){t.status="rejected",t.reason=e}),t)}function o(){}var l=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,u=Symbol.for("react.element"),c=Symbol.for("react.lazy"),d=Symbol.iterator,h=Array.isArray,m=Object.getPrototypeOf,p=Object.prototype,f=new WeakMap;function v(e,t,r,s){var n=1,i=0,a=null;e=JSON.stringify(e,function e(o,l){if(null===l)return null;if("object"==typeof l){if("function"==typeof l.then){null===a&&(a=new FormData),i++;var u,c,v=n++;return l.then(function(s){s=JSON.stringify(s,e);var n=a;n.append(t+v,s),0==--i&&r(n)},function(e){s(e)}),"$@"+v.toString(16)}if(h(l))return l;if(l instanceof FormData){null===a&&(a=new FormData);var P=a,y=t+(o=n++)+"_";return l.forEach(function(e,t){P.append(y+t,e)}),"$K"+o.toString(16)}if(l instanceof Map)return l=JSON.stringify(Array.from(l),e),null===a&&(a=new FormData),o=n++,a.append(t+o,l),"$Q"+o.toString(16);if(l instanceof Set)return l=JSON.stringify(Array.from(l),e),null===a&&(a=new FormData),o=n++,a.append(t+o,l),"$W"+o.toString(16);if(null===(c=l)||"object"!=typeof c?null:"function"==typeof(c=d&&c[d]||c["@@iterator"])?c:null)return Array.from(l);if((o=m(l))!==p&&(null===o||null!==m(o)))throw Error("Only plain objects, and a few built-ins, can be passed to Server Actions. Classes or null prototypes are not supported.");return l}if("string"==typeof l)return"Z"===l[l.length-1]&&this[o]instanceof Date?"$D"+l:l="$"===l[0]?"$"+l:l;if("boolean"==typeof l)return l;if("number"==typeof l)return Number.isFinite(u=l)?0===u&&-1/0==1/u?"$-0":u:1/0===u?"$Infinity":-1/0===u?"$-Infinity":"$NaN";if(void 0===l)return"$undefined";if("function"==typeof l){if(void 0!==(l=f.get(l)))return l=JSON.stringify(l,e),null===a&&(a=new FormData),o=n++,a.set(t+o,l),"$F"+o.toString(16);throw Error("Client Functions cannot be passed directly to Server Functions. Only Functions passed from the Server can be passed back again.")}if("symbol"==typeof l){if(Symbol.for(o=l.description)!==l)throw Error("Only global symbols received from Symbol.for(...) can be passed to Server Functions. The symbol Symbol.for("+l.description+") cannot be found among global symbols.");return"$S"+o}if("bigint"==typeof l)return"$n"+l.toString(10);throw Error("Type "+typeof l+" is not supported as an argument to a Server Function.")}),null===a?r(e):(a.set(t+"0",e),0===i&&r(a))}var P=new WeakMap;function y(e){var t=f.get(this);if(!t)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var r=null;if(null!==t.bound){if((r=P.get(t))||(s=t,a=new Promise(function(e,t){n=e,i=t}),v(s,"",function(e){if("string"==typeof e){var t=new FormData;t.append("0",e),e=t}a.status="fulfilled",a.value=e,n(e)},function(e){a.status="rejected",a.reason=e,i(e)}),r=a,P.set(t,r)),"rejected"===r.status)throw r.reason;if("fulfilled"!==r.status)throw r;t=r.value;var s,n,i,a,o=new FormData;t.forEach(function(t,r){o.append("$ACTION_"+e+":"+r,t)}),r=o,t="$ACTION_REF_"+e}else t="$ACTION_ID_"+t.id;return{name:t,method:"POST",encType:"multipart/form-data",data:r}}function g(e,t){var r=f.get(this);if(!r)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");if(r.id!==e)return!1;var s=r.bound;if(null===s)return 0===t;switch(s.status){case"fulfilled":return s.value.length===t;case"pending":throw s;case"rejected":throw s.reason;default:throw"string"!=typeof s.status&&(s.status="pending",s.then(function(e){s.status="fulfilled",s.value=e},function(e){s.status="rejected",s.reason=e})),s}}function T(e,t,r){Object.defineProperties(e,{$$FORM_ACTION:{value:void 0===r?y:function(){var e=f.get(this);if(!e)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var t=e.bound;return null===t&&(t=Promise.resolve([])),r(e.id,t)}},$$IS_SIGNATURE_EQUAL:{value:g},bind:{value:x}}),f.set(e,t)}var _=Function.prototype.bind,b=Array.prototype.slice;function x(){var e=_.apply(this,arguments),t=f.get(this);if(t){var r=b.call(arguments,1),s=null;s=null!==t.bound?Promise.resolve(t.bound).then(function(e){return e.concat(r)}):Promise.resolve(r),Object.defineProperties(e,{$$FORM_ACTION:{value:this.$$FORM_ACTION},$$IS_SIGNATURE_EQUAL:{value:g},bind:{value:x}}),f.set(e,{id:t.id,bound:s})}return e}function E(e,t,r,s){this.status=e,this.value=t,this.reason=r,this._response=s}function S(e){switch(e.status){case"resolved_model":N(e);break;case"resolved_module":R(e)}switch(e.status){case"fulfilled":return e.value;case"pending":case"blocked":case"cyclic":throw e;default:throw e.reason}}function w(e,t){for(var r=0;r<e.length;r++)(0,e[r])(t)}function O(e,t,r){switch(e.status){case"fulfilled":w(t,e.value);break;case"pending":case"blocked":case"cyclic":e.value=t,e.reason=r;break;case"rejected":r&&w(r,e.reason)}}function A(e,t){if("pending"===e.status||"blocked"===e.status){var r=e.reason;e.status="rejected",e.reason=t,null!==r&&w(r,t)}}function j(e,t){if("pending"===e.status||"blocked"===e.status){var r=e.value,s=e.reason;e.status="resolved_module",e.value=t,null!==r&&(R(e),O(e,r,s))}}E.prototype=Object.create(Promise.prototype),E.prototype.then=function(e,t){switch(this.status){case"resolved_model":N(this);break;case"resolved_module":R(this)}switch(this.status){case"fulfilled":e(this.value);break;case"pending":case"blocked":case"cyclic":e&&(null===this.value&&(this.value=[]),this.value.push(e)),t&&(null===this.reason&&(this.reason=[]),this.reason.push(t));break;default:t(this.reason)}};var k=null,C=null;function N(e){var t=k,r=C;k=e,C=null;var s=e.value;e.status="cyclic",e.value=null,e.reason=null;try{var n=JSON.parse(s,e._response._fromJSON);if(null!==C&&0<C.deps)C.value=n,e.status="blocked",e.value=null,e.reason=null;else{var i=e.value;e.status="fulfilled",e.value=n,null!==i&&w(i,n)}}catch(t){e.status="rejected",e.reason=t}finally{k=t,C=r}}function R(e){try{var t=e.value,r=globalThis.__next_require__(t[0]);if(4===t.length&&"function"==typeof r.then){if("fulfilled"===r.status)r=r.value;else throw r.reason}var s="*"===t[2]?r:""===t[2]?r.__esModule?r.default:r:r[t[2]];e.status="fulfilled",e.value=s}catch(t){e.status="rejected",e.reason=t}}function G(e,t){e._chunks.forEach(function(e){"pending"===e.status&&A(e,t)})}function I(e,t){var r=e._chunks,s=r.get(t);return s||(s=new E("pending",null,null,e),r.set(t,s)),s}function D(e,t){if("resolved_model"===(e=I(e,t)).status&&N(e),"fulfilled"===e.status)return e.value;throw e.reason}function q(){throw Error('Trying to call a function from "use server" but the callServer option was not implemented in your router runtime.')}function M(){throw Error("Server Functions cannot be called during initial render. This would create a fetch waterfall. Try to use a Server Component to pass data to Client Components instead.")}function U(e){var t,r=e.ssrManifest.moduleMap;return(r={_bundlerConfig:r,_moduleLoading:e.ssrManifest.moduleLoading,_callServer:void 0!==M?M:q,_encodeFormAction:e.encodeFormAction,_nonce:e="string"==typeof e.nonce?e.nonce:void 0,_chunks:new Map,_stringDecoder:new TextDecoder,_fromJSON:null,_rowState:0,_rowID:0,_rowTag:0,_rowLength:0,_buffer:[]})._fromJSON=(t=r,function(e,r){return"string"==typeof r?function(e,t,r,s){if("$"===s[0]){if("$"===s)return u;switch(s[1]){case"$":return s.slice(1);case"L":return{$$typeof:c,_payload:e=I(e,t=parseInt(s.slice(2),16)),_init:S};case"@":if(2===s.length)return new Promise(function(){});return I(e,t=parseInt(s.slice(2),16));case"S":return Symbol.for(s.slice(2));case"F":return t=D(e,t=parseInt(s.slice(2),16)),function(e,t){function r(){var e=Array.prototype.slice.call(arguments),r=t.bound;return r?"fulfilled"===r.status?s(t.id,r.value.concat(e)):Promise.resolve(r).then(function(r){return s(t.id,r.concat(e))}):s(t.id,e)}var s=e._callServer;return T(r,t,e._encodeFormAction),r}(e,t);case"Q":return new Map(e=D(e,t=parseInt(s.slice(2),16)));case"W":return new Set(e=D(e,t=parseInt(s.slice(2),16)));case"I":return 1/0;case"-":return"$-0"===s?-0:-1/0;case"N":return NaN;case"u":return;case"D":return new Date(Date.parse(s.slice(2)));case"n":return BigInt(s.slice(2));default:switch((e=I(e,s=parseInt(s.slice(1),16))).status){case"resolved_model":N(e);break;case"resolved_module":R(e)}switch(e.status){case"fulfilled":return e.value;case"pending":case"blocked":case"cyclic":var n;return s=k,e.then(function(e,t,r,s){if(C){var n=C;s||n.deps++}else n=C={deps:s?0:1,value:null};return function(s){t[r]=s,n.deps--,0===n.deps&&"blocked"===e.status&&(s=e.value,e.status="fulfilled",e.value=n.value,null!==s&&w(s,n.value))}}(s,t,r,"cyclic"===e.status),(n=s,function(e){return A(n,e)})),null;default:throw e.reason}}}return s}(t,this,e,r):"object"==typeof r&&null!==r?e=r[0]===u?{$$typeof:u,type:r[1],key:r[2],ref:null,props:r[3],_owner:null}:r:r}),r}function L(e,t){function s(t){G(e,t)}var u=t.getReader();u.read().then(function t(c){var d=c.value;if(c.done)G(e,Error("Connection closed."));else{var h=0,m=e._rowState,p=e._rowID,f=e._rowTag,v=e._rowLength;c=e._buffer;for(var P=d.length;h<P;){var y=-1;switch(m){case 0:58===(y=d[h++])?m=1:p=p<<4|(96<y?y-87:y-48);continue;case 1:84===(m=d[h])?(f=m,m=2,h++):64<m&&91>m?(f=m,m=3,h++):(f=0,m=3);continue;case 2:44===(y=d[h++])?m=4:v=v<<4|(96<y?y-87:y-48);continue;case 3:y=d.indexOf(10,h);break;case 4:(y=h+v)>d.length&&(y=-1)}var g=d.byteOffset+h;if(-1<y){h=new Uint8Array(d.buffer,g,y-h),v=e,g=f;var T=v._stringDecoder;f="";for(var _=0;_<c.length;_++)f+=T.decode(c[_],n);switch(f+=T.decode(h),g){case 73:!function(e,t,s){var n=e._chunks,u=n.get(t);s=JSON.parse(s,e._fromJSON);var c=function(e,t){if(e){var r=e[t[0]];if(e=r[t[2]])r=e.name;else{if(!(e=r["*"]))throw Error('Could not find the module "'+t[0]+'" in the React SSR Manifest. This is probably a bug in the React Server Components bundler.');r=t[2]}return 4===t.length?[e.id,e.chunks,r,1]:[e.id,e.chunks,r]}return t}(e._bundlerConfig,s);if(function(e,t,r){if(null!==e)for(var s=1;s<t.length;s+=2){var n=l.current;if(n){var i=n.preinitScript,a=e.prefix+t[s],o=e.crossOrigin;o="string"==typeof o?"use-credentials"===o?o:"":void 0,i.call(n,a,{crossOrigin:o,nonce:r})}}}(e._moduleLoading,s[1],e._nonce),s=function(e){for(var t=e[1],s=[],n=0;n<t.length;){var l=t[n++];t[n++];var u=i.get(l);if(void 0===u){u=r.e(l),s.push(u);var c=i.set.bind(i,l,null);u.then(c,o),i.set(l,u)}else null!==u&&s.push(u)}return 4===e.length?0===s.length?a(e[0]):Promise.all(s).then(function(){return a(e[0])}):0<s.length?Promise.all(s):null}(c)){if(u){var d=u;d.status="blocked"}else d=new E("blocked",null,null,e),n.set(t,d);s.then(function(){return j(d,c)},function(e){return A(d,e)})}else u?j(u,c):n.set(t,new E("resolved_module",c,null,e))}(v,p,f);break;case 72:if(p=f[0],v=JSON.parse(f=f.slice(1),v._fromJSON),f=l.current)switch(p){case"D":f.prefetchDNS(v);break;case"C":"string"==typeof v?f.preconnect(v):f.preconnect(v[0],v[1]);break;case"L":p=v[0],h=v[1],3===v.length?f.preload(p,h,v[2]):f.preload(p,h);break;case"m":"string"==typeof v?f.preloadModule(v):f.preloadModule(v[0],v[1]);break;case"S":"string"==typeof v?f.preinitStyle(v):f.preinitStyle(v[0],0===v[1]?void 0:v[1],3===v.length?v[2]:void 0);break;case"X":"string"==typeof v?f.preinitScript(v):f.preinitScript(v[0],v[1]);break;case"M":"string"==typeof v?f.preinitModuleScript(v):f.preinitModuleScript(v[0],v[1])}break;case 69:h=(f=JSON.parse(f)).digest,(f=Error("An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.")).stack="Error: "+f.message,f.digest=h,(g=(h=v._chunks).get(p))?A(g,f):h.set(p,new E("rejected",null,f,v));break;case 84:v._chunks.set(p,new E("fulfilled",f,null,v));break;case 68:case 87:throw Error("Failed to read a RSC payload created by a development version of React on the server while using a production version on the client. Always use matching versions on the server and the client.");default:(g=(h=v._chunks).get(p))?(v=g,p=f,"pending"===v.status&&(f=v.value,h=v.reason,v.status="resolved_model",v.value=p,null!==f&&(N(v),O(v,f,h)))):h.set(p,new E("resolved_model",f,null,v))}h=y,3===m&&h++,v=p=f=m=0,c.length=0}else{d=new Uint8Array(d.buffer,g,d.byteLength-h),c.push(d),v-=d.byteLength;break}}return e._rowState=m,e._rowID=p,e._rowTag=f,e._rowLength=v,u.read().then(t).catch(s)}}).catch(s)}t.createFromFetch=function(e,t){var r=U(t);return e.then(function(e){L(r,e.body)},function(e){G(r,e)}),I(r,0)},t.createFromReadableStream=function(e,t){return L(t=U(t),e),I(t,0)},t.createServerReference=function(e){return function(e,t,r){function s(){var r=Array.prototype.slice.call(arguments);return t(e,r)}return T(s,{id:e,bound:null},r),s}(e,M)},t.encodeReply=function(e){return new Promise(function(t,r){v(e,"",t,r)})}},30561:(e,t,r)=>{"use strict";e.exports=r(37971)},24672:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DetachedPromise",{enumerable:!0,get:function(){return r}});class r{constructor(){let e,t;this.promise=new Promise((r,s)=>{e=r,t=s}),this.resolve=e,this.reject=t}}},21890:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{atLeastOneTask:function(){return n},scheduleImmediate:function(){return s},scheduleOnNextTick:function(){return r}});let r=e=>{Promise.resolve().then(()=>{process.nextTick(e)})},s=e=>{setImmediate(e)};function n(){return new Promise(e=>s(e))}},9702:(e,t)=>{"use strict";let r,s;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{arrayBufferToString:function(){return i},decrypt:function(){return l},encrypt:function(){return o},generateEncryptionKeyBase64:function(){return u},getActionEncryptionKey:function(){return p},getClientReferenceManifestSingleton:function(){return m},getServerModuleMap:function(){return h},setReferenceManifestsSingleton:function(){return d},stringToUint8Array:function(){return a}});let n=null;function i(e){let t=new Uint8Array(e),r=t.byteLength;if(r<65535)return String.fromCharCode.apply(null,t);let s="";for(let e=0;e<r;e++)s+=String.fromCharCode(t[e]);return s}function a(e){let t=e.length,r=new Uint8Array(t);for(let s=0;s<t;s++)r[s]=e.charCodeAt(s);return r}function o(e,t,r){return crypto.subtle.encrypt({name:"AES-GCM",iv:t},e,r)}function l(e,t,r){return crypto.subtle.decrypt({name:"AES-GCM",iv:t},e,r)}async function u(e){if(e&&void 0!==s)return s;n||(n=new Promise(async(e,t)=>{try{let t=await crypto.subtle.generateKey({name:"AES-GCM",length:256},!0,["encrypt","decrypt"]),r=await crypto.subtle.exportKey("raw",t),s=btoa(i(r));e([t,s])}catch(e){t(e)}}));let[t,a]=await n;return r=t,e&&(s=a),a}let c=Symbol.for("next.server.action-manifests");function d({clientReferenceManifest:e,serverActionsManifest:t,serverModuleMap:r}){globalThis[c]={clientReferenceManifest:e,serverActionsManifest:t,serverModuleMap:r}}function h(){let e=globalThis[c];if(!e)throw Error("Missing manifest for Server Actions. This is a bug in Next.js");return e.serverModuleMap}function m(){let e=globalThis[c];if(!e)throw Error("Missing manifest for Server Actions. This is a bug in Next.js");return e.clientReferenceManifest}async function p(){if(r)return r;let e=globalThis[c];if(!e)throw Error("Missing manifest for Server Actions. This is a bug in Next.js");let t=process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY||e.serverActionsManifest.encryptionKey;if(void 0===t)throw Error("Missing encryption key for Server Actions");return r=await crypto.subtle.importKey("raw",a(atob(t)),"AES-GCM",!0,["encrypt","decrypt"])}},60166:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{decryptActionBoundArgs:function(){return h},encryptActionBoundArgs:function(){return d}}),r(88769);let s=r(51749),n=r(30561),i=r(18175),a=r(9702),o=new TextEncoder,l=new TextDecoder;async function u(e,t){let r=await (0,a.getActionEncryptionKey)();if(void 0===r)throw Error("Missing encryption key for Server Action. This is a bug in Next.js");let s=atob(t),n=s.slice(0,16),i=s.slice(16),o=l.decode(await (0,a.decrypt)(r,(0,a.stringToUint8Array)(n),(0,a.stringToUint8Array)(i)));if(!o.startsWith(e))throw Error("Invalid Server Action payload: failed to decrypt.");return o.slice(e.length)}async function c(e,t){let r=await (0,a.getActionEncryptionKey)();if(void 0===r)throw Error("Missing encryption key for Server Action. This is a bug in Next.js");let s=new Uint8Array(16);crypto.getRandomValues(s);let n=(0,a.arrayBufferToString)(s.buffer),i=await (0,a.encrypt)(r,s,o.encode(e+t));return btoa(n+(0,a.arrayBufferToString)(i))}async function d(e,t){let r=(0,a.getClientReferenceManifestSingleton)(),n=await (0,i.streamToString)((0,s.renderToReadableStream)(t,r.clientModules));return await c(e,n)}async function h(e,t){let r=await u(e,await t),i=await (0,n.createFromReadableStream)(new ReadableStream({start(e){e.enqueue(o.encode(r)),e.close()}}),{ssrManifest:{moduleLoading:{},moduleMap:{}}}),l=(0,a.getServerModuleMap)();return await (0,s.decodeReply)(await (0,n.encodeReply)(i),l)}},63502:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ENCODED_TAGS",{enumerable:!0,get:function(){return r}});let r={OPENING:{HTML:new Uint8Array([60,104,116,109,108]),BODY:new Uint8Array([60,98,111,100,121])},CLOSED:{HEAD:new Uint8Array([60,47,104,101,97,100,62]),BODY:new Uint8Array([60,47,98,111,100,121,62]),HTML:new Uint8Array([60,47,104,116,109,108,62]),BODY_AND_HTML:new Uint8Array([60,47,98,111,100,121,62,60,47,104,116,109,108,62])}}},18175:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{chainStreams:function(){return d},continueDynamicDataResume:function(){return E},continueDynamicHTMLResume:function(){return x},continueDynamicPrerender:function(){return _},continueFizzStream:function(){return T},continueStaticPrerender:function(){return b},createBufferedTransformStream:function(){return p},createRootLayoutValidatorStream:function(){return g},renderToInitialFizzStream:function(){return f},streamFromString:function(){return h},streamToString:function(){return m}});let s=r(64994),n=r(71376),i=r(24672),a=r(21890),o=r(63502),l=r(5893);function u(){}let c=new TextEncoder;function d(...e){if(0===e.length)throw Error("Invariant: chainStreams requires at least one stream");if(1===e.length)return e[0];let{readable:t,writable:r}=new TransformStream,s=e[0].pipeTo(r,{preventClose:!0}),n=1;for(;n<e.length-1;n++){let t=e[n];s=s.then(()=>t.pipeTo(r,{preventClose:!0}))}let i=e[n];return(s=s.then(()=>i.pipeTo(r))).catch(u),t}function h(e){return new ReadableStream({start(t){t.enqueue(c.encode(e)),t.close()}})}async function m(e){let t=new TextDecoder("utf-8",{fatal:!0}),r="";for await(let s of e)r+=t.decode(s,{stream:!0});return r+t.decode()}function p(){let e,t=[],r=0,s=s=>{if(e)return;let n=new i.DetachedPromise;e=n,(0,a.scheduleImmediate)(()=>{try{let e=new Uint8Array(r),n=0;for(let r=0;r<t.length;r++){let s=t[r];e.set(s,n),n+=s.byteLength}t.length=0,r=0,s.enqueue(e)}catch{}finally{e=void 0,n.resolve()}})};return new TransformStream({transform(e,n){t.push(e),r+=e.byteLength,s(n)},flush(){if(e)return e.promise}})}function f({ReactDOMServer:e,element:t,streamOptions:r}){return(0,s.getTracer)().trace(n.AppRenderSpan.renderToReadableStream,async()=>e.renderToReadableStream(t,r))}function v(e){let t=!1,r=!1,s=!1;return new TransformStream({async transform(n,i){if(s=!0,r){i.enqueue(n);return}let u=await e();if(t){if(u){let e=c.encode(u);i.enqueue(e)}i.enqueue(n),r=!0}else{let e=(0,l.indexOfUint8Array)(n,o.ENCODED_TAGS.CLOSED.HEAD);if(-1!==e){if(u){let t=c.encode(u),r=new Uint8Array(n.length+t.length);r.set(n.slice(0,e)),r.set(t,e),r.set(n.slice(e),e+t.length),i.enqueue(r)}else i.enqueue(n);r=!0,t=!0}}t?(0,a.scheduleImmediate)(()=>{r=!1}):i.enqueue(n)},async flush(t){if(s){let r=await e();r&&t.enqueue(c.encode(r))}}})}function P(e){let t=null,r=!1;async function s(s){if(t)return;let n=e.getReader();await (0,a.atLeastOneTask)();try{for(;;){let{done:e,value:t}=await n.read();if(e){r=!0;return}s.enqueue(t)}}catch(e){s.error(e)}}return new TransformStream({transform(e,r){r.enqueue(e),t||(t=s(r))},flush(e){if(!r)return t||s(e)}})}function y(e){let t=!1,r=c.encode(e);return new TransformStream({transform(s,n){if(t)return n.enqueue(s);let i=(0,l.indexOfUint8Array)(s,r);if(i>-1){if(t=!0,s.length===e.length)return;let r=s.slice(0,i);if(n.enqueue(r),s.length>e.length+i){let t=s.slice(i+e.length);n.enqueue(t)}}else n.enqueue(s)},flush(e){e.enqueue(r)}})}function g(){let e=!1,t=!1;return new TransformStream({async transform(r,s){!e&&(0,l.indexOfUint8Array)(r,o.ENCODED_TAGS.OPENING.HTML)>-1&&(e=!0),!t&&(0,l.indexOfUint8Array)(r,o.ENCODED_TAGS.OPENING.BODY)>-1&&(t=!0),s.enqueue(r)},flush(r){let s=[];e||s.push("html"),t||s.push("body"),s.length&&r.enqueue(c.encode(`<script>self.__next_root_layout_missing_tags=${JSON.stringify(s)}</script>`))}})}async function T(e,{suffix:t,inlinedDataStream:r,isStaticGeneration:s,getServerInsertedHTML:n,serverInsertedHTMLToHead:o,validateRootLayout:l}){let u="</body></html>",d=t?t.split(u,1)[0]:null;return s&&"allReady"in e&&await e.allReady,function(e,t){let r=e;for(let e of t)e&&(r=r.pipeThrough(e));return r}(e,[p(),n&&!o?new TransformStream({transform:async(e,t)=>{let r=await n();r&&t.enqueue(c.encode(r)),t.enqueue(e)}}):null,null!=d&&d.length>0?function(e){let t,r=!1,s=r=>{let s=new i.DetachedPromise;t=s,(0,a.scheduleImmediate)(()=>{try{r.enqueue(c.encode(e))}catch{}finally{t=void 0,s.resolve()}})};return new TransformStream({transform(e,t){t.enqueue(e),r||(r=!0,s(t))},flush(s){if(t)return t.promise;r||s.enqueue(c.encode(e))}})}(d):null,r?P(r):null,l?g():null,y(u),n&&o?v(n):null])}async function _(e,{getServerInsertedHTML:t}){return e.pipeThrough(p()).pipeThrough(new TransformStream({transform(e,t){(0,l.isEquivalentUint8Arrays)(e,o.ENCODED_TAGS.CLOSED.BODY_AND_HTML)||(0,l.isEquivalentUint8Arrays)(e,o.ENCODED_TAGS.CLOSED.BODY)||(0,l.isEquivalentUint8Arrays)(e,o.ENCODED_TAGS.CLOSED.HTML)||(e=(0,l.removeFromUint8Array)(e,o.ENCODED_TAGS.CLOSED.BODY),e=(0,l.removeFromUint8Array)(e,o.ENCODED_TAGS.CLOSED.HTML),t.enqueue(e))}})).pipeThrough(v(t))}async function b(e,{inlinedDataStream:t,getServerInsertedHTML:r}){return e.pipeThrough(p()).pipeThrough(v(r)).pipeThrough(P(t)).pipeThrough(y("</body></html>"))}async function x(e,{inlinedDataStream:t,getServerInsertedHTML:r}){return e.pipeThrough(p()).pipeThrough(v(r)).pipeThrough(P(t)).pipeThrough(y("</body></html>"))}async function E(e,{inlinedDataStream:t}){return e.pipeThrough(P(t)).pipeThrough(y("</body></html>"))}},5893:(e,t)=>{"use strict";function r(e,t){if(0===t.length)return 0;if(0===e.length||t.length>e.length)return -1;for(let r=0;r<=e.length-t.length;r++){let s=!0;for(let n=0;n<t.length;n++)if(e[r+n]!==t[n]){s=!1;break}if(s)return r}return -1}function s(e,t){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}function n(e,t){let s=r(e,t);if(0===s)return e.subarray(t.length);if(!(s>-1))return e;{let r=new Uint8Array(e.length-t.length);return r.set(e.slice(0,s)),r.set(e.slice(s+t.length),s),r}}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{indexOfUint8Array:function(){return r},isEquivalentUint8Arrays:function(){return s},removeFromUint8Array:function(){return n}})},44393:(e,t,r)=>{"use strict";r.r(t),r.d(t,{getSubscriptionByUserId:()=>n9,processCheckoutSuccessWebhook:()=>ie,processInvoicePaid:()=>ir,processSubscriptonDelete:()=>it});var s,n,i,a,o,l,u,c,d,h,m,p,f,v,P,y,g,T,_,b={};r.r(b),r.d(b,{StripeAPIError:()=>eg,StripeAuthenticationError:()=>eT,StripeCardError:()=>eP,StripeConnectionError:()=>ex,StripeError:()=>ev,StripeIdempotencyError:()=>eS,StripeInvalidGrantError:()=>ew,StripeInvalidRequestError:()=>ey,StripePermissionError:()=>e_,StripeRateLimitError:()=>eb,StripeSignatureVerificationError:()=>eE,StripeUnknownError:()=>eO,generate:()=>ef});var x={};r.r(x),r.d(x,{Account:()=>r0,AccountLinks:()=>r4,AccountSessions:()=>r6,Accounts:()=>r0,ApplePayDomains:()=>r5,ApplicationFees:()=>r9,Apps:()=>nN,Balance:()=>st,BalanceTransactions:()=>ss,Billing:()=>nR,BillingPortal:()=>nG,Charges:()=>si,Checkout:()=>nI,Climate:()=>nD,ConfirmationTokens:()=>so,CountrySpecs:()=>su,Coupons:()=>sd,CreditNotes:()=>sm,CustomerSessions:()=>sf,Customers:()=>sP,Disputes:()=>sg,Entitlements:()=>nq,EphemeralKeys:()=>s_,Events:()=>sx,ExchangeRates:()=>sS,FileLinks:()=>sO,Files:()=>sk,FinancialConnections:()=>nM,Forwarding:()=>nU,Identity:()=>nL,InvoiceItems:()=>sN,InvoiceRenderingTemplates:()=>sG,Invoices:()=>sD,Issuing:()=>nF,Mandates:()=>sM,OAuth:()=>sF,PaymentIntents:()=>sH,PaymentLinks:()=>sK,PaymentMethodConfigurations:()=>sW,PaymentMethodDomains:()=>sJ,PaymentMethods:()=>sZ,Payouts:()=>sX,Plans:()=>s0,Prices:()=>s4,Products:()=>s6,PromotionCodes:()=>s5,Quotes:()=>s9,Radar:()=>n$,Refunds:()=>nt,Reporting:()=>nH,Reviews:()=>ns,SetupAttempts:()=>ni,SetupIntents:()=>no,ShippingRates:()=>nu,Sigma:()=>nz,Sources:()=>nd,SubscriptionItems:()=>nm,SubscriptionSchedules:()=>nf,Subscriptions:()=>nP,Tax:()=>nK,TaxCodes:()=>ng,TaxIds:()=>n_,TaxRates:()=>nx,Terminal:()=>nB,TestHelpers:()=>nW,Tokens:()=>nS,Topups:()=>nO,Transfers:()=>nj,Treasury:()=>nV,WebhookEndpoints:()=>nC});var E=r(24330);r(60166);var S=r(22247),w=r(9571),O=r(46454);r(50938),r(91208);var A=r(8035),j=r(7655),k=r(47877),C=class{constructor(){(0,k.Ko)(this,i),(0,k.Ko)(this,s,"clerk_telemetry_throttler"),(0,k.Ko)(this,n,864e5)}isEventThrottled(e){var t;if(!(0,k.ac)(this,i,l))return!1;let r=Date.now(),u=(0,k.U9)(this,i,a).call(this,e),c=null==(t=(0,k.ac)(this,i,o))?void 0:t[u];if(!c){let e={...(0,k.ac)(this,i,o),[u]:r};localStorage.setItem((0,k.ac)(this,s),JSON.stringify(e))}if(c&&r-c>(0,k.ac)(this,n)){let e=(0,k.ac)(this,i,o);delete e[u],localStorage.setItem((0,k.ac)(this,s),JSON.stringify(e))}return!!c}};s=new WeakMap,n=new WeakMap,i=new WeakSet,a=function(e){let{sk:t,pk:r,payload:s,...n}=e,i={...s,...n};return JSON.stringify(Object.keys({...s,...n}).sort().map(e=>i[e]))},o=function(){let e=localStorage.getItem((0,k.ac)(this,s));return e?JSON.parse(e):{}},l=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem((0,k.ac)(this,s)),!1}};var N={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},R=class{constructor(e){var t,r,s,n,i,a;(0,k.Ko)(this,p),(0,k.Ko)(this,u),(0,k.Ko)(this,c),(0,k.Ko)(this,d,{}),(0,k.Ko)(this,h,[]),(0,k.Ko)(this,m),(0,k.qx)(this,u,{maxBufferSize:null!=(t=e.maxBufferSize)?t:N.maxBufferSize,samplingRate:null!=(r=e.samplingRate)?r:N.samplingRate,disabled:null!=(s=e.disabled)&&s,debug:null!=(n=e.debug)&&n,endpoint:N.endpoint}),e.clerkVersion||"undefined"!=typeof window?(0,k.ac)(this,d).clerkVersion=null!=(i=e.clerkVersion)?i:"":(0,k.ac)(this,d).clerkVersion="",(0,k.ac)(this,d).sdk=e.sdk,(0,k.ac)(this,d).sdkVersion=e.sdkVersion,(0,k.ac)(this,d).publishableKey=null!=(a=e.publishableKey)?a:"";let o=(0,j.nQ)(e.publishableKey);o&&((0,k.ac)(this,d).instanceType=o.instanceType),e.secretKey&&((0,k.ac)(this,d).secretKey=e.secretKey.substring(0,16)),(0,k.qx)(this,c,new C)}get isEnabled(){var e;return!("development"!==(0,k.ac)(this,d).instanceType||(0,k.ac)(this,u).disabled||"undefined"!=typeof process&&(0,A.fQ)(process.env.CLERK_TELEMETRY_DISABLED))&&("undefined"==typeof window||null==(e=null==window?void 0:window.navigator)||!e.webdriver)}get isDebug(){return(0,k.ac)(this,u).debug||"undefined"!=typeof process&&(0,A.fQ)(process.env.CLERK_TELEMETRY_DEBUG)}record(e){let t=(0,k.U9)(this,p,_).call(this,e.event,e.payload);(0,k.U9)(this,p,g).call(this,t.event,t),(0,k.U9)(this,p,f).call(this,t,e.eventSamplingRate)&&((0,k.ac)(this,h).push(t),(0,k.U9)(this,p,P).call(this))}};function G(e){let t={...e},r=(0,w.gf)(t),s=(0,w.sA)({options:t,apiClient:r}),n=new R({...e.telemetry,publishableKey:t.publishableKey,secretKey:t.secretKey,...t.sdkMetadata?{sdk:t.sdkMetadata.name,sdkVersion:t.sdkMetadata.version}:{}});return{...r,...s,telemetry:n}}u=new WeakMap,c=new WeakMap,d=new WeakMap,h=new WeakMap,m=new WeakMap,p=new WeakSet,f=function(e,t){return this.isEnabled&&!this.isDebug&&(0,k.U9)(this,p,v).call(this,e,t)},v=function(e,t){let r=Math.random();return!(0,k.ac)(this,c).isEventThrottled(e)&&r<=(0,k.ac)(this,u).samplingRate&&(void 0===t||r<=t)},P=function(){if("undefined"==typeof window){(0,k.U9)(this,p,y).call(this);return}if((0,k.ac)(this,h).length>=(0,k.ac)(this,u).maxBufferSize){(0,k.ac)(this,m)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)((0,k.ac)(this,m)),(0,k.U9)(this,p,y).call(this);return}(0,k.ac)(this,m)||("requestIdleCallback"in window?(0,k.qx)(this,m,requestIdleCallback(()=>{(0,k.U9)(this,p,y).call(this)})):(0,k.qx)(this,m,setTimeout(()=>{(0,k.U9)(this,p,y).call(this)},0)))},y=function(){fetch(new URL("/v1/event",(0,k.ac)(this,u).endpoint),{method:"POST",body:JSON.stringify({events:(0,k.ac)(this,h)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,k.qx)(this,h,[])}).catch(()=>void 0)},g=function(e,t){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",e),console.log(t),console.groupEnd()):console.log("[clerk/telemetry]",e,t))},T=function(){let e={name:(0,k.ac)(this,d).sdk,version:(0,k.ac)(this,d).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e},_=function(e,t){var r,s;let n=(0,k.U9)(this,p,T).call(this);return{event:e,cv:null!=(r=(0,k.ac)(this,d).clerkVersion)?r:"",it:null!=(s=(0,k.ac)(this,d).instanceType)?s:"",sdk:n.name,sdkv:n.version,...(0,k.ac)(this,d).publishableKey?{pk:(0,k.ac)(this,d).publishableKey}:{},...(0,k.ac)(this,d).secretKey?{sk:(0,k.ac)(this,d).secretKey}:{},payload:t}},(0,O.A)(w.WX);var I=r(2800),D=r(3002),q=r(17487);let M=require("node:async_hooks");var U=r(87070);let L=new M.AsyncLocalStorage;var F=r(99968),$=r(4758);let H={secretKey:F.Cn,publishableKey:F.Am,apiUrl:F.T5,apiVersion:F.Gn,userAgent:"@clerk/nextjs@5.7.5",proxyUrl:F.NM,domain:F.yK,isSatellite:F.lo,sdkMetadata:F.nE,telemetry:{disabled:F.CY,debug:F.w4}},z=e=>G({...H,...e}),K=G(H),B=new Proxy(Object.assign(()=>{var e,t;let r;try{let e=(0,q.vX)(),t=(0,$.Pg)(e,I._G.Headers.ClerkRequestData);r=(0,$.om)(t)}catch(e){if(e&&(0,q.mx)(e))throw e}let s=null!=(t=null==(e=L.getStore())?void 0:e.get("requestData"))?t:r;return(null==s?void 0:s.secretKey)||(null==s?void 0:s.publishableKey)?z(s):K},K),{get:(e,t,r)=>((0,D.x9)("clerkClient singleton","Use `clerkClient()` as a function instead."),Reflect.get(e,t,r))});var W=r(84770),V=r(17702);class J{computeHMACSignature(e,t){throw Error("computeHMACSignature not implemented.")}computeHMACSignatureAsync(e,t){throw Error("computeHMACSignatureAsync not implemented.")}}class Y extends Error{}class Z extends J{computeHMACSignature(e,t){return W.createHmac("sha256",t).update(e,"utf8").digest("hex")}async computeHMACSignatureAsync(e,t){return await this.computeHMACSignature(e,t)}}var Q=r(32615),X=r.t(Q,2),ee=r(35240),et=r.t(ee,2);class er{getClientName(){throw Error("getClientName not implemented.")}makeRequest(e,t,r,s,n,i,a,o){throw Error("makeRequest not implemented.")}static makeTimeoutError(){let e=TypeError(er.TIMEOUT_ERROR_CODE);return e.code=er.TIMEOUT_ERROR_CODE,e}}er.CONNECTION_CLOSED_ERROR_CODES=["ECONNRESET","EPIPE"],er.TIMEOUT_ERROR_CODE="ETIMEDOUT";class es{constructor(e,t){this._statusCode=e,this._headers=t}getStatusCode(){return this._statusCode}getHeaders(){return this._headers}getRawResponse(){throw Error("getRawResponse not implemented.")}toStream(e){throw Error("toStream not implemented.")}toJSON(){throw Error("toJSON not implemented.")}}let en=Q||X,ei=ee||et,ea=new en.Agent({keepAlive:!0}),eo=new ei.Agent({keepAlive:!0});class el extends er{constructor(e){super(),this._agent=e}getClientName(){return"node"}makeRequest(e,t,r,s,n,i,a,o){let l="http"===a,u=this._agent;return u||(u=l?ea:eo),new Promise((a,c)=>{let d=(l?en:ei).request({host:e,port:t,path:r,method:s,agent:u,headers:n,ciphers:"DEFAULT:!aNULL:!eNULL:!LOW:!EXPORT:!SSLv2:!MD5"});d.setTimeout(o,()=>{d.destroy(er.makeTimeoutError())}),d.on("response",e=>{a(new eu(e))}),d.on("error",e=>{c(e)}),d.once("socket",e=>{e.connecting?e.once(l?"connect":"secureConnect",()=>{d.write(i),d.end()}):(d.write(i),d.end())})})}}class eu extends es{constructor(e){super(e.statusCode,e.headers||{}),this._res=e}getRawResponse(){return this._res}toStream(e){return this._res.once("end",()=>e()),this._res}toJSON(){return new Promise((e,t)=>{let r="";this._res.setEncoding("utf8"),this._res.on("data",e=>{r+=e}),this._res.once("end",()=>{try{e(JSON.parse(r))}catch(e){t(e)}})})}}class ec extends er{constructor(e){if(super(),!e){if(!globalThis.fetch)throw Error("fetch() function not provided and is not defined in the global scope. You must provide a fetch implementation.");e=globalThis.fetch}globalThis.AbortController?this._fetchFn=ec.makeFetchWithAbortTimeout(e):this._fetchFn=ec.makeFetchWithRaceTimeout(e)}static makeFetchWithRaceTimeout(e){return(t,r,s)=>{let n;let i=new Promise((e,t)=>{n=setTimeout(()=>{n=null,t(er.makeTimeoutError())},s)});return Promise.race([e(t,r),i]).finally(()=>{n&&clearTimeout(n)})}}static makeFetchWithAbortTimeout(e){return async(t,r,s)=>{let n=new AbortController,i=setTimeout(()=>{i=null,n.abort(er.makeTimeoutError())},s);try{return await e(t,Object.assign(Object.assign({},r),{signal:n.signal}))}catch(e){if("AbortError"===e.name)throw er.makeTimeoutError();throw e}finally{i&&clearTimeout(i)}}}getClientName(){return"fetch"}async makeRequest(e,t,r,s,n,i,a,o){let l=new URL(r,`${"http"===a?"http":"https"}://${e}`);l.port=t;let u="POST"==s||"PUT"==s||"PATCH"==s;return new ed(await this._fetchFn(l.toString(),{method:s,headers:n,body:i||(u?"":void 0)},o))}}class ed extends es{constructor(e){super(e.status,ed._transformHeadersToObject(e.headers)),this._res=e}getRawResponse(){return this._res}toStream(e){return e(),this._res.body}toJSON(){return this._res.json()}static _transformHeadersToObject(e){let t={};for(let r of e){if(!Array.isArray(r)||2!=r.length)throw Error("Response objects produced by the fetch function given to FetchHttpClient do not have an iterable headers map. Response#headers should be an iterable object.");t[r[0]]=r[1]}return t}}class eh extends J{constructor(e){super(),this.subtleCrypto=e||crypto.subtle}computeHMACSignature(e,t){throw new Y("SubtleCryptoProvider cannot be used in a synchronous context.")}async computeHMACSignatureAsync(e,t){let r=new TextEncoder,s=await this.subtleCrypto.importKey("raw",r.encode(t),{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]),n=new Uint8Array(await this.subtleCrypto.sign("hmac",s,r.encode(e))),i=Array(n.length);for(let e=0;e<n.length;e++)i[e]=em[n[e]];return i.join("")}}let em=Array(256);for(let e=0;e<em.length;e++)em[e]=e.toString(16).padStart(2,"0");class ep{constructor(){this._fetchFn=null,this._agent=null}getUname(){throw Error("getUname not implemented.")}uuid4(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}secureCompare(e,t){if(e.length!==t.length)return!1;let r=e.length,s=0;for(let n=0;n<r;++n)s|=e.charCodeAt(n)^t.charCodeAt(n);return 0===s}createEmitter(){throw Error("createEmitter not implemented.")}tryBufferData(e){throw Error("tryBufferData not implemented.")}createNodeHttpClient(e){throw Error("createNodeHttpClient not implemented.")}createFetchHttpClient(e){return new ec(e)}createDefaultHttpClient(){throw Error("createDefaultHttpClient not implemented.")}createNodeCryptoProvider(){throw Error("createNodeCryptoProvider not implemented.")}createSubtleCryptoProvider(e){return new eh(e)}createDefaultCryptoProvider(){throw Error("createDefaultCryptoProvider not implemented.")}}let ef=e=>{switch(e.type){case"card_error":return new eP(e);case"invalid_request_error":return new ey(e);case"api_error":return new eg(e);case"authentication_error":return new eT(e);case"rate_limit_error":return new eb(e);case"idempotency_error":return new eS(e);case"invalid_grant":return new ew(e);default:return new eO(e)}};class ev extends Error{constructor(e={},t=null){super(e.message),this.type=t||this.constructor.name,this.raw=e,this.rawType=e.type,this.code=e.code,this.doc_url=e.doc_url,this.param=e.param,this.detail=e.detail,this.headers=e.headers,this.requestId=e.requestId,this.statusCode=e.statusCode,this.message=e.message,this.charge=e.charge,this.decline_code=e.decline_code,this.payment_intent=e.payment_intent,this.payment_method=e.payment_method,this.payment_method_type=e.payment_method_type,this.setup_intent=e.setup_intent,this.source=e.source}}ev.generate=ef;class eP extends ev{constructor(e={}){super(e,"StripeCardError")}}class ey extends ev{constructor(e={}){super(e,"StripeInvalidRequestError")}}class eg extends ev{constructor(e={}){super(e,"StripeAPIError")}}class eT extends ev{constructor(e={}){super(e,"StripeAuthenticationError")}}class e_ extends ev{constructor(e={}){super(e,"StripePermissionError")}}class eb extends ev{constructor(e={}){super(e,"StripeRateLimitError")}}class ex extends ev{constructor(e={}){super(e,"StripeConnectionError")}}class eE extends ev{constructor(e,t,r={}){super(r,"StripeSignatureVerificationError"),this.header=e,this.payload=t}}class eS extends ev{constructor(e={}){super(e,"StripeIdempotencyError")}}class ew extends ev{constructor(e={}){super(e,"StripeInvalidGrantError")}}class eO extends ev{constructor(e={}){super(e,"StripeUnknownError")}}var eA=r(6684);let ej=["apiKey","idempotencyKey","stripeAccount","apiVersion","maxNetworkRetries","timeout","host"];function ek(e){return e&&"object"==typeof e&&ej.some(t=>Object.prototype.hasOwnProperty.call(e,t))}function eC(e){return eA.stringify(e,{serializeDate:e=>Math.floor(e.getTime()/1e3).toString()}).replace(/%5B/g,"[").replace(/%5D/g,"]")}let eN=(()=>{let e={"\n":"\\n",'"':'\\"',"\u2028":"\\u2028","\u2029":"\\u2029"};return t=>{let r=t.replace(/["\n\r\u2028\u2029]/g,t=>e[t]);return e=>r.replace(/\{([\s\S]+?)\}/g,(t,r)=>encodeURIComponent(e[r]||""))}})();function eR(e){if(!Array.isArray(e)||!e[0]||"object"!=typeof e[0])return{};if(!ek(e[0]))return e.shift();let t=Object.keys(e[0]),r=t.filter(e=>ej.includes(e));return r.length>0&&r.length!==t.length&&eD(`Options found in arguments (${r.join(", ")}). Did you mean to pass an options object? See https://github.com/stripe/stripe-node/wiki/Passing-Options.`),{}}function eG(e){if("object"!=typeof e)throw Error("Argument must be an object");return Object.keys(e).reduce((t,r)=>(null!=e[r]&&(t[r]=e[r]),t),{})}function eI(e,t){return t?e.then(e=>{setTimeout(()=>{t(null,e)},0)},e=>{setTimeout(()=>{t(e,null)},0)}):e}function eD(e){return"function"!=typeof process.emitWarning?console.warn(`Stripe: ${e}`):process.emitWarning(e,"Stripe")}function eq(e,t,r){if(!Number.isInteger(t)){if(void 0!==r)return r;throw Error(`${e} must be an integer`)}return t}let eM=require("child_process");class eU extends ev{}class eL extends ep{constructor(){super(),this._exec=eM.exec,this._UNAME_CACHE=null}uuid4(){return W.randomUUID?W.randomUUID():super.uuid4()}getUname(){return this._UNAME_CACHE||(this._UNAME_CACHE=new Promise((e,t)=>{try{this._exec("uname -a",(t,r)=>{if(t)return e(null);e(r)})}catch(t){e(null)}})),this._UNAME_CACHE}secureCompare(e,t){if(!e||!t)throw Error("secureCompare must receive two arguments");if(e.length!==t.length)return!1;if(W.timingSafeEqual){let r=new TextEncoder,s=r.encode(e),n=r.encode(t);return W.timingSafeEqual(s,n)}return super.secureCompare(e,t)}createEmitter(){return new V.EventEmitter}tryBufferData(e){if(!(e.file.data instanceof V.EventEmitter))return Promise.resolve(e);let t=[];return new Promise((r,s)=>{e.file.data.on("data",e=>{t.push(e)}).once("end",()=>{let s=Object.assign({},e);s.file.data=function(e){let t=new Uint8Array(e.reduce((e,t)=>e+t.length,0)),r=0;return e.forEach(e=>{t.set(e,r),r+=e.length}),t}(t),r(s)}).on("error",e=>{s(new eU({message:"An error occurred while attempting to process the file for upload.",detail:e}))})})}createNodeHttpClient(e){return new el(e)}createDefaultHttpClient(){return new el}createNodeCryptoProvider(){return new Z}createDefaultCryptoProvider(){return this.createNodeCryptoProvider()}}class eF{constructor(e,t){this._stripe=e,this._maxBufferedRequestMetric=t}_addHeadersDirectlyToObject(e,t){e.requestId=t["request-id"],e.stripeAccount=e.stripeAccount||t["stripe-account"],e.apiVersion=e.apiVersion||t["stripe-version"],e.idempotencyKey=e.idempotencyKey||t["idempotency-key"]}_makeResponseEvent(e,t,r){let s=Date.now(),n=s-e.request_start_time;return eG({api_version:r["stripe-version"],account:r["stripe-account"],idempotency_key:r["idempotency-key"],method:e.method,path:e.path,status:t,request_id:this._getRequestId(r),elapsed:n,request_start_time:e.request_start_time,request_end_time:s})}_getRequestId(e){return e["request-id"]}_streamingResponseHandler(e,t,r){return s=>{let n=s.getHeaders(),i=s.toStream(()=>{let r=this._makeResponseEvent(e,s.getStatusCode(),n);this._stripe._emitter.emit("response",r),this._recordRequestMetrics(this._getRequestId(n),r.elapsed,t)});return this._addHeadersDirectlyToObject(i,n),r(null,i)}}_jsonResponseHandler(e,t,r){return s=>{let n=s.getHeaders(),i=this._getRequestId(n),a=s.getStatusCode(),o=this._makeResponseEvent(e,a,n);this._stripe._emitter.emit("response",o),s.toJSON().then(e=>{if(e.error)throw"string"==typeof e.error&&(e.error={type:e.error,message:e.error_description}),e.error.headers=n,e.error.statusCode=a,e.error.requestId=i,401===a?new eT(e.error):403===a?new e_(e.error):429===a?new eb(e.error):ev.generate(e.error);return e},e=>{throw new eg({message:"Invalid JSON received from the Stripe API",exception:e,requestId:n["request-id"]})}).then(e=>{this._recordRequestMetrics(i,o.elapsed,t);let a=s.getRawResponse();this._addHeadersDirectlyToObject(a,n),Object.defineProperty(e,"lastResponse",{enumerable:!1,writable:!1,value:a}),r(null,e)},e=>r(e,null))}}static _generateConnectionErrorMessage(e){return`An error occurred with our connection to Stripe.${e>0?` Request was retried ${e} times.`:""}`}static _shouldRetry(e,t,r,s){return!!(s&&0===t&&er.CONNECTION_CLOSED_ERROR_CODES.includes(s.code))||!(t>=r)&&(!e||"false"!==e.getHeaders()["stripe-should-retry"]&&!!("true"===e.getHeaders()["stripe-should-retry"]||409===e.getStatusCode()||e.getStatusCode()>=500))}_getSleepTimeInMS(e,t=null){let r=this._stripe.getInitialNetworkRetryDelay(),s=Math.min(r*Math.pow(e-1,2),this._stripe.getMaxNetworkRetryDelay());return s*=.5*(1+Math.random()),s=Math.max(r,s),Number.isInteger(t)&&t<=60&&(s=Math.max(s,t)),1e3*s}_getMaxNetworkRetries(e={}){return void 0!==e.maxNetworkRetries&&Number.isInteger(e.maxNetworkRetries)?e.maxNetworkRetries:this._stripe.getMaxNetworkRetries()}_defaultIdempotencyKey(e,t){let r=this._getMaxNetworkRetries(t);return"POST"===e&&r>0?`stripe-node-retry-${this._stripe._platformFunctions.uuid4()}`:null}_makeHeaders(e,t,r,s,n,i,a){let o={Authorization:e?`Bearer ${e}`:this._stripe.getApiField("auth"),Accept:"application/json","Content-Type":"application/x-www-form-urlencoded","User-Agent":this._getUserAgentString(),"X-Stripe-Client-User-Agent":s,"X-Stripe-Client-Telemetry":this._getTelemetryHeader(),"Stripe-Version":r,"Stripe-Account":this._stripe.getApiField("stripeAccount"),"Idempotency-Key":this._defaultIdempotencyKey(n,a)},l="POST"==n||"PUT"==n||"PATCH"==n;return(l||t)&&(l||eD(`${n} method had non-zero contentLength but no payload is expected for this verb`),o["Content-Length"]=t),Object.assign(eG(o),i&&"object"==typeof i?Object.keys(i).reduce((e,t)=>(e[t.split("-").map(e=>e.charAt(0).toUpperCase()+e.substr(1).toLowerCase()).join("-")]=i[t],e),{}):i)}_getUserAgentString(){let e=this._stripe.getConstant("PACKAGE_VERSION"),t=this._stripe._appInfo?this._stripe.getAppInfoAsString():"";return`Stripe/v1 NodeBindings/${e} ${t}`.trim()}_getTelemetryHeader(){if(this._stripe.getTelemetryEnabled()&&this._stripe._prevRequestMetrics.length>0)return JSON.stringify({last_request_metrics:this._stripe._prevRequestMetrics.shift()})}_recordRequestMetrics(e,t,r){if(this._stripe.getTelemetryEnabled()&&e){if(this._stripe._prevRequestMetrics.length>this._maxBufferedRequestMetric)eD("Request metrics buffer is full, dropping telemetry message.");else{let s={request_id:e,request_duration_ms:t};r&&r.length>0&&(s.usage=r),this._stripe._prevRequestMetrics.push(s)}}}_request(e,t,r,s,n,i={},a=[],o,l=null){let u;let c=(e,t,r,s,n)=>setTimeout(e,this._getSleepTimeInMS(s,n),t,r,s+1),d=(s,n,l)=>{let h=i.settings&&i.settings.timeout&&Number.isInteger(i.settings.timeout)&&i.settings.timeout>=0?i.settings.timeout:this._stripe.getApiField("timeout"),m=this._stripe.getApiField("httpClient").makeRequest(t||this._stripe.getApiField("host"),this._stripe.getApiField("port"),r,e,n,u,this._stripe.getApiField("protocol"),h),p=Date.now(),f=eG({api_version:s,account:n["Stripe-Account"],idempotency_key:n["Idempotency-Key"],method:e,path:r,request_start_time:p}),v=l||0,P=this._getMaxNetworkRetries(i.settings||{});this._stripe._emitter.emit("request",f),m.then(e=>eF._shouldRetry(e,v,P)?c(d,s,n,v,e.getHeaders()["retry-after"]):i.streaming&&400>e.getStatusCode()?this._streamingResponseHandler(f,a,o)(e):this._jsonResponseHandler(f,a,o)(e)).catch(e=>eF._shouldRetry(null,v,P,e)?c(d,s,n,v,null):o(new ex({message:e.code&&e.code===er.TIMEOUT_ERROR_CODE?`Request aborted due to timeout being reached (${h}ms)`:eF._generateConnectionErrorMessage(v),detail:e})))},h=(t,r)=>{if(t)return o(t);u=r,this._stripe.getClientUserAgent(t=>{var r,s;let a=this._stripe.getApiField("version"),o=this._makeHeaders(n,u.length,a,t,e,null!==(r=i.headers)&&void 0!==r?r:null,null!==(s=i.settings)&&void 0!==s?s:{});d(a,o,0)})};l?l(e,s,i.headers,h):h(null,eC(s||{}))}}class e${constructor(e,t,r,s){this.index=0,this.pagePromise=e,this.promiseCache={currentPromise:null},this.requestArgs=t,this.spec=r,this.stripeResource=s}async iterate(e){if(!(e&&e.data&&"number"==typeof e.data.length))throw Error("Unexpected: Stripe API response does not have a well-formed `data` array.");let t=eW(this.requestArgs);if(this.index<e.data.length){let r=t?e.data.length-1-this.index:this.index,s=e.data[r];return this.index+=1,{value:s,done:!1}}if(e.has_more){this.index=0,this.pagePromise=this.getNextPage(e);let t=await this.pagePromise;return this.iterate(t)}return{done:!0,value:void 0}}getNextPage(e){throw Error("Unimplemented")}async _next(){return this.iterate(await this.pagePromise)}next(){if(this.promiseCache.currentPromise)return this.promiseCache.currentPromise;let e=(async()=>{let e=await this._next();return this.promiseCache.currentPromise=null,e})();return this.promiseCache.currentPromise=e,e}}class eH extends e${getNextPage(e){let t=eW(this.requestArgs),r=function(e,t){let r=t?0:e.data.length-1,s=e.data[r],n=s&&s.id;if(!n)throw Error("Unexpected: No `id` found on the last item while auto-paging a list.");return n}(e,t);return this.stripeResource._makeRequest(this.requestArgs,this.spec,{[t?"ending_before":"starting_after"]:r})}}class ez extends e${getNextPage(e){if(!e.next_page)throw Error("Unexpected: Stripe API response does not have a well-formed `next_page` field, but `has_more` was true.");return this.stripeResource._makeRequest(this.requestArgs,this.spec,{page:e.next_page})}}let eK=(e,t,r,s)=>"search"===r.methodType?eB(new ez(s,t,r,e)):"list"===r.methodType?eB(new eH(s,t,r,e)):null,eB=e=>{var t;let r=(t=(...t)=>e.next(...t),function(){let e=[].slice.call(arguments),r=function(e){if(0===e.length)return;let t=e[0];if("function"!=typeof t)throw Error(`The first argument to autoPagingEach, if present, must be a callback function; received ${typeof t}`);if(2===t.length)return t;if(t.length>2)throw Error(`The \`onItem\` callback function passed to autoPagingEach must accept at most two arguments; got ${t}`);return function(e,r){r(t(e))}}(e),s=function(e){if(e.length<2)return null;let t=e[1];if("function"!=typeof t)throw Error(`The second argument to autoPagingEach, if present, must be a callback function; received ${typeof t}`);return t}(e);if(e.length>2)throw Error(`autoPagingEach takes up to two arguments; received ${e}`);return eI(new Promise((e,s)=>{t().then(function s(n){if(n.done){e();return}let i=n.value;return new Promise(e=>{r(i,e)}).then(e=>!1===e?s({done:!0,value:void 0}):t().then(s))}).catch(s)}),s)}),s={autoPagingEach:r,autoPagingToArray:function(e,t){let s=e&&e.limit;if(!s)throw Error("You must pass a `limit` option to autoPagingToArray, e.g., `autoPagingToArray({limit: 1000});`.");if(s>1e4)throw Error("You cannot specify a limit of more than 10,000 items to fetch in `autoPagingToArray`; use `autoPagingEach` to iterate through longer lists.");return eI(new Promise((e,t)=>{let n=[];r(e=>{if(n.push(e),n.length>=s)return!1}).then(()=>{e(n)}).catch(t)}),t)},next:()=>e.next(),return:()=>({}),["undefined"!=typeof Symbol&&Symbol.asyncIterator?Symbol.asyncIterator:"@@asyncIterator"]:()=>s};return s};function eW(e){return!!eR([].slice.call(e)).ending_before}function eV(e,t){if(this._stripe=e,t)throw Error("Support for curried url params was dropped in stripe-node v7.0.0. Instead, pass two ids.");this.basePath=eN(this.basePath||e.getApiField("basePath")),this.resourcePath=this.path,this.path=eN(this.path),this.initialize(...arguments)}function eJ(e){let t={DEFAULT_TOLERANCE:300,signature:null,constructEvent(e,r,s,n,i,a){try{this.signature.verifyHeader(e,r,s,n||t.DEFAULT_TOLERANCE,i,a)}catch(e){throw e instanceof Y&&(e.message+="\nUse `await constructEventAsync(...)` instead of `constructEvent(...)`"),e}return e instanceof Uint8Array?JSON.parse(new TextDecoder("utf8").decode(e)):JSON.parse(e)},async constructEventAsync(e,r,s,n,i,a){return await this.signature.verifyHeaderAsync(e,r,s,n||t.DEFAULT_TOLERANCE,i,a),e instanceof Uint8Array?JSON.parse(new TextDecoder("utf8").decode(e)):JSON.parse(e)},generateTestHeaderString:function(e){let t=l(e),r=t.signature||t.cryptoProvider.computeHMACSignature(t.payloadString,t.secret);return t.generateHeaderString(r)},generateTestHeaderStringAsync:async function(e){let t=l(e),r=t.signature||await t.cryptoProvider.computeHMACSignatureAsync(t.payloadString,t.secret);return t.generateHeaderString(r)}},r={EXPECTED_SCHEME:"v1",verifyHeader(e,t,r,a,l,u){let{decodedHeader:c,decodedPayload:d,details:h,suspectPayloadType:m}=n(e,t,this.EXPECTED_SCHEME),p=/\s/.test(r),f=(l=l||o()).computeHMACSignature(s(d,h),r);return i(d,c,h,f,a,m,p,u),!0},async verifyHeaderAsync(e,t,r,a,l,u){let{decodedHeader:c,decodedPayload:d,details:h,suspectPayloadType:m}=n(e,t,this.EXPECTED_SCHEME),p=/\s/.test(r);l=l||o();let f=await l.computeHMACSignatureAsync(s(d,h),r);return i(d,c,h,f,a,m,p,u)}};function s(e,t){return`${t.timestamp}.${e}`}function n(e,t,r){if(!e)throw new eE(t,e,{message:"No webhook payload was provided."});let s="string"!=typeof e&&!(e instanceof Uint8Array),n=new TextDecoder("utf8"),i=e instanceof Uint8Array?n.decode(e):e;if(Array.isArray(t))throw Error("Unexpected: An array was passed as a header, which should not be possible for the stripe-signature header.");if(null==t||""==t)throw new eE(t,e,{message:"No stripe-signature header value was provided."});let a=t instanceof Uint8Array?n.decode(t):t,o="string"!=typeof a?null:a.split(",").reduce((e,t)=>{let s=t.split("=");return"t"===s[0]&&(e.timestamp=parseInt(s[1],10)),s[0]===r&&e.signatures.push(s[1]),e},{timestamp:-1,signatures:[]});if(!o||-1===o.timestamp)throw new eE(a,i,{message:"Unable to extract timestamp and signatures from header"});if(!o.signatures.length)throw new eE(a,i,{message:"No signatures found with expected scheme"});return{decodedPayload:i,decodedHeader:a,details:o,suspectPayloadType:s}}function i(t,r,s,n,i,a,o,l){let u=!!s.signatures.filter(e.secureCompare.bind(e,n)).length,c="\nLearn more about webhook signing and explore webhook integration examples for various frameworks at https://github.com/stripe/stripe-node#webhook-signing",d=o?"\n\nNote: The provided signing secret contains whitespace. This often indicates an extra newline or space is in the value":"";if(!u){if(a)throw new eE(r,t,{message:"Webhook payload must be provided as a string or a Buffer (https://nodejs.org/api/buffer.html) instance representing the _raw_ request body.Payload was provided as a parsed JavaScript object instead. \nSignature verification is impossible without access to the original signed material. \n"+c+"\n"+d});throw new eE(r,t,{message:"No signatures found matching the expected signature for payload. Are you passing the raw request body you received from Stripe? \n If a webhook request is being forwarded by a third-party tool, ensure that the exact request body, including JSON formatting and new line style, is preserved.\n"+c+"\n"+d})}let h=Math.floor(("number"==typeof l?l:Date.now())/1e3)-s.timestamp;if(i>0&&h>i)throw new eE(r,t,{message:"Timestamp outside the tolerance zone"});return!0}let a=null;function o(){return a||(a=e.createDefaultCryptoProvider()),a}function l(e){if(!e)throw new ev({message:"Options are required"});let t=Math.floor(e.timestamp)||Math.floor(Date.now()/1e3),s=e.scheme||r.EXPECTED_SCHEME,n=e.cryptoProvider||o(),i=`${t}.${e.payload}`;return Object.assign(Object.assign({},e),{timestamp:t,scheme:s,cryptoProvider:n,payloadString:i,generateHeaderString:e=>`t=${t},${s}=${e}`})}return t.signature=r,t}function eY(e,t){for(let r in t){if(!Object.prototype.hasOwnProperty.call(t,r))continue;let s=r[0].toLowerCase()+r.substring(1),n=new t[r](e);this[s]=n}}function eZ(e,t){return function(e){return new eY(e,t)}}eV.extend=function(e){let t=this,r=Object.prototype.hasOwnProperty.call(e,"constructor")?e.constructor:function(...e){t.apply(this,e)};return Object.assign(r,t),r.prototype=Object.create(t.prototype),Object.assign(r.prototype,e),r},eV.method=function(e){if(void 0!==e.path&&void 0!==e.fullPath)throw Error(`Method spec specified both a 'path' (${e.path}) and a 'fullPath' (${e.fullPath}).`);return function(...t){let r="function"==typeof t[t.length-1]&&t.pop();e.urlParams=function(e){let t=e.match(/\{\w+\}/g);return t?t.map(e=>e.replace(/[{}]/g,"")):[]}(e.fullPath||this.createResourcePathWithSymbols(e.path||""));let s=eI(this._makeRequest(t,e,{}),r);return Object.assign(s,eK(this,t,e,s)),s}},eV.MAX_BUFFERED_REQUEST_METRICS=100,eV.prototype={_stripe:null,path:"",resourcePath:"",basePath:null,initialize(){},requestDataProcessor:null,validateRequest:null,createFullPath(e,t){let r=[this.basePath(t),this.path(t)];if("function"==typeof e){let s=e(t);s&&r.push(s)}else r.push(e);return this._joinUrlParts(r)},createResourcePathWithSymbols(e){return e?`/${this._joinUrlParts([this.resourcePath,e])}`:`/${this.resourcePath}`},_joinUrlParts:e=>e.join("/").replace(/\/{2,}/g,"/"),_getRequestOpts(e,t,r){let s=(t.method||"GET").toUpperCase(),n=t.usage||[],i=t.urlParams||[],a=t.encode||(e=>e),o=!!t.fullPath,l=eN(o?t.fullPath:t.path||""),u=o?t.fullPath:this.createResourcePathWithSymbols(t.path),c=[].slice.call(e),d=i.reduce((e,t)=>{let r=c.shift();if("string"!=typeof r)throw Error(`Stripe: Argument "${t}" must be a string, but got: ${r} (on API request to \`${s} ${u}\`)`);return e[t]=r,e},{}),h=a(Object.assign({},eR(c),r)),m=function(e){let t={auth:null,host:null,headers:{},settings:{}};if(e.length>0){let r=e[e.length-1];if("string"==typeof r)t.auth=e.pop();else if(ek(r)){let r=Object.assign({},e.pop()),s=Object.keys(r).filter(e=>!ej.includes(e));s.length&&eD(`Invalid options found (${s.join(", ")}); ignoring.`),r.apiKey&&(t.auth=r.apiKey),r.idempotencyKey&&(t.headers["Idempotency-Key"]=r.idempotencyKey),r.stripeAccount&&(t.headers["Stripe-Account"]=r.stripeAccount),r.apiVersion&&(t.headers["Stripe-Version"]=r.apiVersion),Number.isInteger(r.maxNetworkRetries)&&(t.settings.maxNetworkRetries=r.maxNetworkRetries),Number.isInteger(r.timeout)&&(t.settings.timeout=r.timeout),r.host&&(t.host=r.host)}}return t}(c),p=m.host||t.host,f=!!t.streaming;if(c.filter(e=>null!=e).length)throw Error(`Stripe: Unknown arguments (${c}). Did you mean to pass an options object? See https://github.com/stripe/stripe-node/wiki/Passing-Options. (on API request to ${s} \`${u}\`)`);let v=o?l(d):this.createFullPath(l,d),P=Object.assign(m.headers,t.headers);t.validator&&t.validator(h,{headers:P});let y="GET"===t.method||"DELETE"===t.method;return{requestMethod:s,requestPath:v,bodyData:y?null:h,queryData:y?h:{},auth:m.auth,headers:P,host:null!=p?p:null,streaming:f,settings:m.settings,usage:n}},_makeRequest(e,t,r){return new Promise((s,n)=>{var i;let a;try{a=this._getRequestOpts(e,t,r)}catch(e){n(e);return}let o=0===Object.keys(a.queryData).length,l=[a.requestPath,o?"":"?",eC(a.queryData)].join(""),{headers:u,settings:c}=a;this._stripe._requestSender._request(a.requestMethod,a.host,l,a.bodyData,a.auth,{headers:u,settings:c,streaming:a.streaming},a.usage,function(e,r){e?n(e):s(t.transformResponseData?t.transformResponseData(r):r)},null===(i=this.requestDataProcessor)||void 0===i?void 0:i.bind(this))})}};let eQ=eV.method,eX=eV.extend({retrieve:eQ({method:"GET",fullPath:"/v1/financial_connections/accounts/{account}"}),list:eQ({method:"GET",fullPath:"/v1/financial_connections/accounts",methodType:"list"}),disconnect:eQ({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/disconnect"}),listOwners:eQ({method:"GET",fullPath:"/v1/financial_connections/accounts/{account}/owners",methodType:"list"}),refresh:eQ({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/refresh"}),subscribe:eQ({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/subscribe"}),unsubscribe:eQ({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/unsubscribe"})}),e1=eV.method,e0=eV.extend({retrieve:e1({method:"GET",fullPath:"/v1/entitlements/active_entitlements/{id}"}),list:e1({method:"GET",fullPath:"/v1/entitlements/active_entitlements",methodType:"list"})}),e2=eV.method,e4=eV.extend({create:e2({method:"POST",fullPath:"/v1/billing/alerts"}),retrieve:e2({method:"GET",fullPath:"/v1/billing/alerts/{id}"}),list:e2({method:"GET",fullPath:"/v1/billing/alerts",methodType:"list"}),activate:e2({method:"POST",fullPath:"/v1/billing/alerts/{id}/activate"}),archive:e2({method:"POST",fullPath:"/v1/billing/alerts/{id}/archive"}),deactivate:e2({method:"POST",fullPath:"/v1/billing/alerts/{id}/deactivate"})}),e8=eV.method,e6=eV.extend({create:e8({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations"}),capture:e8({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/capture"}),expire:e8({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/expire"}),finalizeAmount:e8({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/finalize_amount"}),increment:e8({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/increment"}),reverse:e8({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/reverse"})}),e7=eV.method,e5=eV.extend({retrieve:e7({method:"GET",fullPath:"/v1/issuing/authorizations/{authorization}"}),update:e7({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}"}),list:e7({method:"GET",fullPath:"/v1/issuing/authorizations",methodType:"list"}),approve:e7({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}/approve"}),decline:e7({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}/decline"})}),e3=eV.method,e9=eV.extend({create:e3({method:"POST",fullPath:"/v1/tax/calculations"}),retrieve:e3({method:"GET",fullPath:"/v1/tax/calculations/{calculation}"}),listLineItems:e3({method:"GET",fullPath:"/v1/tax/calculations/{calculation}/line_items",methodType:"list"})}),te=eV.method,tt=eV.extend({create:te({method:"POST",fullPath:"/v1/issuing/cardholders"}),retrieve:te({method:"GET",fullPath:"/v1/issuing/cardholders/{cardholder}"}),update:te({method:"POST",fullPath:"/v1/issuing/cardholders/{cardholder}"}),list:te({method:"GET",fullPath:"/v1/issuing/cardholders",methodType:"list"})}),tr=eV.method,ts=eV.extend({deliverCard:tr({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/deliver"}),failCard:tr({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/fail"}),returnCard:tr({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/return"}),shipCard:tr({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/ship"})}),tn=eV.method,ti=eV.extend({create:tn({method:"POST",fullPath:"/v1/issuing/cards"}),retrieve:tn({method:"GET",fullPath:"/v1/issuing/cards/{card}"}),update:tn({method:"POST",fullPath:"/v1/issuing/cards/{card}"}),list:tn({method:"GET",fullPath:"/v1/issuing/cards",methodType:"list"})}),ta=eV.method,to=eV.extend({create:ta({method:"POST",fullPath:"/v1/billing_portal/configurations"}),retrieve:ta({method:"GET",fullPath:"/v1/billing_portal/configurations/{configuration}"}),update:ta({method:"POST",fullPath:"/v1/billing_portal/configurations/{configuration}"}),list:ta({method:"GET",fullPath:"/v1/billing_portal/configurations",methodType:"list"})}),tl=eV.method,tu=eV.extend({create:tl({method:"POST",fullPath:"/v1/terminal/configurations"}),retrieve:tl({method:"GET",fullPath:"/v1/terminal/configurations/{configuration}"}),update:tl({method:"POST",fullPath:"/v1/terminal/configurations/{configuration}"}),list:tl({method:"GET",fullPath:"/v1/terminal/configurations",methodType:"list"}),del:tl({method:"DELETE",fullPath:"/v1/terminal/configurations/{configuration}"})}),tc=eV.method,td=eV.extend({create:tc({method:"POST",fullPath:"/v1/test_helpers/confirmation_tokens"})}),th=eV.method,tm=eV.extend({create:th({method:"POST",fullPath:"/v1/terminal/connection_tokens"})}),tp=eV.method,tf=eV.extend({create:tp({method:"POST",fullPath:"/v1/treasury/credit_reversals"}),retrieve:tp({method:"GET",fullPath:"/v1/treasury/credit_reversals/{credit_reversal}"}),list:tp({method:"GET",fullPath:"/v1/treasury/credit_reversals",methodType:"list"})}),tv=eV.method,tP=eV.extend({fundCashBalance:tv({method:"POST",fullPath:"/v1/test_helpers/customers/{customer}/fund_cash_balance"})}),ty=eV.method,tg=eV.extend({create:ty({method:"POST",fullPath:"/v1/treasury/debit_reversals"}),retrieve:ty({method:"GET",fullPath:"/v1/treasury/debit_reversals/{debit_reversal}"}),list:ty({method:"GET",fullPath:"/v1/treasury/debit_reversals",methodType:"list"})}),tT=eV.method,t_=eV.extend({create:tT({method:"POST",fullPath:"/v1/issuing/disputes"}),retrieve:tT({method:"GET",fullPath:"/v1/issuing/disputes/{dispute}"}),update:tT({method:"POST",fullPath:"/v1/issuing/disputes/{dispute}"}),list:tT({method:"GET",fullPath:"/v1/issuing/disputes",methodType:"list"}),submit:tT({method:"POST",fullPath:"/v1/issuing/disputes/{dispute}/submit"})}),tb=eV.method,tx=eV.extend({retrieve:tb({method:"GET",fullPath:"/v1/radar/early_fraud_warnings/{early_fraud_warning}"}),list:tb({method:"GET",fullPath:"/v1/radar/early_fraud_warnings",methodType:"list"})}),tE=eV.method,tS=eV.extend({create:tE({method:"POST",fullPath:"/v1/entitlements/features"}),retrieve:tE({method:"GET",fullPath:"/v1/entitlements/features/{id}"}),update:tE({method:"POST",fullPath:"/v1/entitlements/features/{id}"}),list:tE({method:"GET",fullPath:"/v1/entitlements/features",methodType:"list"})}),tw=eV.method,tO=eV.extend({create:tw({method:"POST",fullPath:"/v1/treasury/financial_accounts"}),retrieve:tw({method:"GET",fullPath:"/v1/treasury/financial_accounts/{financial_account}"}),update:tw({method:"POST",fullPath:"/v1/treasury/financial_accounts/{financial_account}"}),list:tw({method:"GET",fullPath:"/v1/treasury/financial_accounts",methodType:"list"}),retrieveFeatures:tw({method:"GET",fullPath:"/v1/treasury/financial_accounts/{financial_account}/features"}),updateFeatures:tw({method:"POST",fullPath:"/v1/treasury/financial_accounts/{financial_account}/features"})}),tA=eV.method,tj=eV.extend({fail:tA({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/fail"}),returnInboundTransfer:tA({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/return"}),succeed:tA({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/succeed"})}),tk=eV.method,tC=eV.extend({create:tk({method:"POST",fullPath:"/v1/treasury/inbound_transfers"}),retrieve:tk({method:"GET",fullPath:"/v1/treasury/inbound_transfers/{id}"}),list:tk({method:"GET",fullPath:"/v1/treasury/inbound_transfers",methodType:"list"}),cancel:tk({method:"POST",fullPath:"/v1/treasury/inbound_transfers/{inbound_transfer}/cancel"})}),tN=eV.method,tR=eV.extend({create:tN({method:"POST",fullPath:"/v1/terminal/locations"}),retrieve:tN({method:"GET",fullPath:"/v1/terminal/locations/{location}"}),update:tN({method:"POST",fullPath:"/v1/terminal/locations/{location}"}),list:tN({method:"GET",fullPath:"/v1/terminal/locations",methodType:"list"}),del:tN({method:"DELETE",fullPath:"/v1/terminal/locations/{location}"})}),tG=eV.method,tI=eV.extend({create:tG({method:"POST",fullPath:"/v1/billing/meter_event_adjustments"})}),tD=eV.method,tq=eV.extend({create:tD({method:"POST",fullPath:"/v1/billing/meter_events"})}),tM=eV.method,tU=eV.extend({create:tM({method:"POST",fullPath:"/v1/billing/meters"}),retrieve:tM({method:"GET",fullPath:"/v1/billing/meters/{id}"}),update:tM({method:"POST",fullPath:"/v1/billing/meters/{id}"}),list:tM({method:"GET",fullPath:"/v1/billing/meters",methodType:"list"}),deactivate:tM({method:"POST",fullPath:"/v1/billing/meters/{id}/deactivate"}),listEventSummaries:tM({method:"GET",fullPath:"/v1/billing/meters/{id}/event_summaries",methodType:"list"}),reactivate:tM({method:"POST",fullPath:"/v1/billing/meters/{id}/reactivate"})}),tL=eV.method,tF=eV.extend({create:tL({method:"POST",fullPath:"/v1/climate/orders"}),retrieve:tL({method:"GET",fullPath:"/v1/climate/orders/{order}"}),update:tL({method:"POST",fullPath:"/v1/climate/orders/{order}"}),list:tL({method:"GET",fullPath:"/v1/climate/orders",methodType:"list"}),cancel:tL({method:"POST",fullPath:"/v1/climate/orders/{order}/cancel"})}),t$=eV.method,tH=eV.extend({update:t$({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}"}),fail:t$({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/fail"}),post:t$({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/post"}),returnOutboundPayment:t$({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/return"})}),tz=eV.method,tK=eV.extend({create:tz({method:"POST",fullPath:"/v1/treasury/outbound_payments"}),retrieve:tz({method:"GET",fullPath:"/v1/treasury/outbound_payments/{id}"}),list:tz({method:"GET",fullPath:"/v1/treasury/outbound_payments",methodType:"list"}),cancel:tz({method:"POST",fullPath:"/v1/treasury/outbound_payments/{id}/cancel"})}),tB=eV.method,tW=eV.extend({update:tB({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}"}),fail:tB({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/fail"}),post:tB({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/post"}),returnOutboundTransfer:tB({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/return"})}),tV=eV.method,tJ=eV.extend({create:tV({method:"POST",fullPath:"/v1/treasury/outbound_transfers"}),retrieve:tV({method:"GET",fullPath:"/v1/treasury/outbound_transfers/{outbound_transfer}"}),list:tV({method:"GET",fullPath:"/v1/treasury/outbound_transfers",methodType:"list"}),cancel:tV({method:"POST",fullPath:"/v1/treasury/outbound_transfers/{outbound_transfer}/cancel"})}),tY=eV.method,tZ=eV.extend({activate:tY({method:"POST",fullPath:"/v1/test_helpers/issuing/personalization_designs/{personalization_design}/activate"}),deactivate:tY({method:"POST",fullPath:"/v1/test_helpers/issuing/personalization_designs/{personalization_design}/deactivate"}),reject:tY({method:"POST",fullPath:"/v1/test_helpers/issuing/personalization_designs/{personalization_design}/reject"})}),tQ=eV.method,tX=eV.extend({create:tQ({method:"POST",fullPath:"/v1/issuing/personalization_designs"}),retrieve:tQ({method:"GET",fullPath:"/v1/issuing/personalization_designs/{personalization_design}"}),update:tQ({method:"POST",fullPath:"/v1/issuing/personalization_designs/{personalization_design}"}),list:tQ({method:"GET",fullPath:"/v1/issuing/personalization_designs",methodType:"list"})}),t1=eV.method,t0=eV.extend({retrieve:t1({method:"GET",fullPath:"/v1/issuing/physical_bundles/{physical_bundle}"}),list:t1({method:"GET",fullPath:"/v1/issuing/physical_bundles",methodType:"list"})}),t2=eV.method,t4=eV.extend({retrieve:t2({method:"GET",fullPath:"/v1/climate/products/{product}"}),list:t2({method:"GET",fullPath:"/v1/climate/products",methodType:"list"})}),t8=eV.method,t6=eV.extend({presentPaymentMethod:t8({method:"POST",fullPath:"/v1/test_helpers/terminal/readers/{reader}/present_payment_method"})}),t7=eV.method,t5=eV.extend({create:t7({method:"POST",fullPath:"/v1/terminal/readers"}),retrieve:t7({method:"GET",fullPath:"/v1/terminal/readers/{reader}"}),update:t7({method:"POST",fullPath:"/v1/terminal/readers/{reader}"}),list:t7({method:"GET",fullPath:"/v1/terminal/readers",methodType:"list"}),del:t7({method:"DELETE",fullPath:"/v1/terminal/readers/{reader}"}),cancelAction:t7({method:"POST",fullPath:"/v1/terminal/readers/{reader}/cancel_action"}),processPaymentIntent:t7({method:"POST",fullPath:"/v1/terminal/readers/{reader}/process_payment_intent"}),processSetupIntent:t7({method:"POST",fullPath:"/v1/terminal/readers/{reader}/process_setup_intent"}),refundPayment:t7({method:"POST",fullPath:"/v1/terminal/readers/{reader}/refund_payment"}),setReaderDisplay:t7({method:"POST",fullPath:"/v1/terminal/readers/{reader}/set_reader_display"})}),t3=eV.method,t9=eV.extend({create:t3({method:"POST",fullPath:"/v1/test_helpers/treasury/received_credits"})}),re=eV.method,rt=eV.extend({retrieve:re({method:"GET",fullPath:"/v1/treasury/received_credits/{id}"}),list:re({method:"GET",fullPath:"/v1/treasury/received_credits",methodType:"list"})}),rr=eV.method,rs=eV.extend({create:rr({method:"POST",fullPath:"/v1/test_helpers/treasury/received_debits"})}),rn=eV.method,ri=eV.extend({retrieve:rn({method:"GET",fullPath:"/v1/treasury/received_debits/{id}"}),list:rn({method:"GET",fullPath:"/v1/treasury/received_debits",methodType:"list"})}),ra=eV.method,ro=eV.extend({expire:ra({method:"POST",fullPath:"/v1/test_helpers/refunds/{refund}/expire"})}),rl=eV.method,ru=eV.extend({create:rl({method:"POST",fullPath:"/v1/tax/registrations"}),retrieve:rl({method:"GET",fullPath:"/v1/tax/registrations/{id}"}),update:rl({method:"POST",fullPath:"/v1/tax/registrations/{id}"}),list:rl({method:"GET",fullPath:"/v1/tax/registrations",methodType:"list"})}),rc=eV.method,rd=eV.extend({create:rc({method:"POST",fullPath:"/v1/reporting/report_runs"}),retrieve:rc({method:"GET",fullPath:"/v1/reporting/report_runs/{report_run}"}),list:rc({method:"GET",fullPath:"/v1/reporting/report_runs",methodType:"list"})}),rh=eV.method,rm=eV.extend({retrieve:rh({method:"GET",fullPath:"/v1/reporting/report_types/{report_type}"}),list:rh({method:"GET",fullPath:"/v1/reporting/report_types",methodType:"list"})}),rp=eV.method,rf=eV.extend({create:rp({method:"POST",fullPath:"/v1/forwarding/requests"}),retrieve:rp({method:"GET",fullPath:"/v1/forwarding/requests/{id}"}),list:rp({method:"GET",fullPath:"/v1/forwarding/requests",methodType:"list"})}),rv=eV.method,rP=eV.extend({retrieve:rv({method:"GET",fullPath:"/v1/sigma/scheduled_query_runs/{scheduled_query_run}"}),list:rv({method:"GET",fullPath:"/v1/sigma/scheduled_query_runs",methodType:"list"})}),ry=eV.method,rg=eV.extend({create:ry({method:"POST",fullPath:"/v1/apps/secrets"}),list:ry({method:"GET",fullPath:"/v1/apps/secrets",methodType:"list"}),deleteWhere:ry({method:"POST",fullPath:"/v1/apps/secrets/delete"}),find:ry({method:"GET",fullPath:"/v1/apps/secrets/find"})}),rT=eV.method,r_=eV.extend({create:rT({method:"POST",fullPath:"/v1/billing_portal/sessions"})}),rb=eV.method,rx=eV.extend({create:rb({method:"POST",fullPath:"/v1/checkout/sessions"}),retrieve:rb({method:"GET",fullPath:"/v1/checkout/sessions/{session}"}),update:rb({method:"POST",fullPath:"/v1/checkout/sessions/{session}"}),list:rb({method:"GET",fullPath:"/v1/checkout/sessions",methodType:"list"}),expire:rb({method:"POST",fullPath:"/v1/checkout/sessions/{session}/expire"}),listLineItems:rb({method:"GET",fullPath:"/v1/checkout/sessions/{session}/line_items",methodType:"list"})}),rE=eV.method,rS=eV.extend({create:rE({method:"POST",fullPath:"/v1/financial_connections/sessions"}),retrieve:rE({method:"GET",fullPath:"/v1/financial_connections/sessions/{session}"})}),rw=eV.method,rO=eV.extend({retrieve:rw({method:"GET",fullPath:"/v1/tax/settings"}),update:rw({method:"POST",fullPath:"/v1/tax/settings"})}),rA=eV.method,rj=eV.extend({retrieve:rA({method:"GET",fullPath:"/v1/climate/suppliers/{supplier}"}),list:rA({method:"GET",fullPath:"/v1/climate/suppliers",methodType:"list"})}),rk=eV.method,rC=eV.extend({create:rk({method:"POST",fullPath:"/v1/test_helpers/test_clocks"}),retrieve:rk({method:"GET",fullPath:"/v1/test_helpers/test_clocks/{test_clock}"}),list:rk({method:"GET",fullPath:"/v1/test_helpers/test_clocks",methodType:"list"}),del:rk({method:"DELETE",fullPath:"/v1/test_helpers/test_clocks/{test_clock}"}),advance:rk({method:"POST",fullPath:"/v1/test_helpers/test_clocks/{test_clock}/advance"})}),rN=eV.method,rR=eV.extend({retrieve:rN({method:"GET",fullPath:"/v1/issuing/tokens/{token}"}),update:rN({method:"POST",fullPath:"/v1/issuing/tokens/{token}"}),list:rN({method:"GET",fullPath:"/v1/issuing/tokens",methodType:"list"})}),rG=eV.method,rI=eV.extend({retrieve:rG({method:"GET",fullPath:"/v1/treasury/transaction_entries/{id}"}),list:rG({method:"GET",fullPath:"/v1/treasury/transaction_entries",methodType:"list"})}),rD=eV.method,rq=eV.extend({createForceCapture:rD({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/create_force_capture"}),createUnlinkedRefund:rD({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/create_unlinked_refund"}),refund:rD({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/{transaction}/refund"})}),rM=eV.method,rU=eV.extend({retrieve:rM({method:"GET",fullPath:"/v1/financial_connections/transactions/{transaction}"}),list:rM({method:"GET",fullPath:"/v1/financial_connections/transactions",methodType:"list"})}),rL=eV.method,rF=eV.extend({retrieve:rL({method:"GET",fullPath:"/v1/issuing/transactions/{transaction}"}),update:rL({method:"POST",fullPath:"/v1/issuing/transactions/{transaction}"}),list:rL({method:"GET",fullPath:"/v1/issuing/transactions",methodType:"list"})}),r$=eV.method,rH=eV.extend({retrieve:r$({method:"GET",fullPath:"/v1/tax/transactions/{transaction}"}),createFromCalculation:r$({method:"POST",fullPath:"/v1/tax/transactions/create_from_calculation"}),createReversal:r$({method:"POST",fullPath:"/v1/tax/transactions/create_reversal"}),listLineItems:r$({method:"GET",fullPath:"/v1/tax/transactions/{transaction}/line_items",methodType:"list"})}),rz=eV.method,rK=eV.extend({retrieve:rz({method:"GET",fullPath:"/v1/treasury/transactions/{id}"}),list:rz({method:"GET",fullPath:"/v1/treasury/transactions",methodType:"list"})}),rB=eV.method,rW=eV.extend({create:rB({method:"POST",fullPath:"/v1/radar/value_list_items"}),retrieve:rB({method:"GET",fullPath:"/v1/radar/value_list_items/{item}"}),list:rB({method:"GET",fullPath:"/v1/radar/value_list_items",methodType:"list"}),del:rB({method:"DELETE",fullPath:"/v1/radar/value_list_items/{item}"})}),rV=eV.method,rJ=eV.extend({create:rV({method:"POST",fullPath:"/v1/radar/value_lists"}),retrieve:rV({method:"GET",fullPath:"/v1/radar/value_lists/{value_list}"}),update:rV({method:"POST",fullPath:"/v1/radar/value_lists/{value_list}"}),list:rV({method:"GET",fullPath:"/v1/radar/value_lists",methodType:"list"}),del:rV({method:"DELETE",fullPath:"/v1/radar/value_lists/{value_list}"})}),rY=eV.method,rZ=eV.extend({retrieve:rY({method:"GET",fullPath:"/v1/identity/verification_reports/{report}"}),list:rY({method:"GET",fullPath:"/v1/identity/verification_reports",methodType:"list"})}),rQ=eV.method,rX=eV.extend({create:rQ({method:"POST",fullPath:"/v1/identity/verification_sessions"}),retrieve:rQ({method:"GET",fullPath:"/v1/identity/verification_sessions/{session}"}),update:rQ({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}"}),list:rQ({method:"GET",fullPath:"/v1/identity/verification_sessions",methodType:"list"}),cancel:rQ({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}/cancel"}),redact:rQ({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}/redact"})}),r1=eV.method,r0=eV.extend({create:r1({method:"POST",fullPath:"/v1/accounts"}),retrieve(e,...t){return"string"==typeof e?r1({method:"GET",fullPath:"/v1/accounts/{id}"}).apply(this,[e,...t]):(null==e&&[].shift.apply([e,...t]),r1({method:"GET",fullPath:"/v1/account"}).apply(this,[e,...t]))},update:r1({method:"POST",fullPath:"/v1/accounts/{account}"}),list:r1({method:"GET",fullPath:"/v1/accounts",methodType:"list"}),del:r1({method:"DELETE",fullPath:"/v1/accounts/{account}"}),createExternalAccount:r1({method:"POST",fullPath:"/v1/accounts/{account}/external_accounts"}),createLoginLink:r1({method:"POST",fullPath:"/v1/accounts/{account}/login_links"}),createPerson:r1({method:"POST",fullPath:"/v1/accounts/{account}/persons"}),deleteExternalAccount:r1({method:"DELETE",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),deletePerson:r1({method:"DELETE",fullPath:"/v1/accounts/{account}/persons/{person}"}),listCapabilities:r1({method:"GET",fullPath:"/v1/accounts/{account}/capabilities",methodType:"list"}),listExternalAccounts:r1({method:"GET",fullPath:"/v1/accounts/{account}/external_accounts",methodType:"list"}),listPersons:r1({method:"GET",fullPath:"/v1/accounts/{account}/persons",methodType:"list"}),reject:r1({method:"POST",fullPath:"/v1/accounts/{account}/reject"}),retrieveCurrent:r1({method:"GET",fullPath:"/v1/account"}),retrieveCapability:r1({method:"GET",fullPath:"/v1/accounts/{account}/capabilities/{capability}"}),retrieveExternalAccount:r1({method:"GET",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),retrievePerson:r1({method:"GET",fullPath:"/v1/accounts/{account}/persons/{person}"}),updateCapability:r1({method:"POST",fullPath:"/v1/accounts/{account}/capabilities/{capability}"}),updateExternalAccount:r1({method:"POST",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),updatePerson:r1({method:"POST",fullPath:"/v1/accounts/{account}/persons/{person}"})}),r2=eV.method,r4=eV.extend({create:r2({method:"POST",fullPath:"/v1/account_links"})}),r8=eV.method,r6=eV.extend({create:r8({method:"POST",fullPath:"/v1/account_sessions"})}),r7=eV.method,r5=eV.extend({create:r7({method:"POST",fullPath:"/v1/apple_pay/domains"}),retrieve:r7({method:"GET",fullPath:"/v1/apple_pay/domains/{domain}"}),list:r7({method:"GET",fullPath:"/v1/apple_pay/domains",methodType:"list"}),del:r7({method:"DELETE",fullPath:"/v1/apple_pay/domains/{domain}"})}),r3=eV.method,r9=eV.extend({retrieve:r3({method:"GET",fullPath:"/v1/application_fees/{id}"}),list:r3({method:"GET",fullPath:"/v1/application_fees",methodType:"list"}),createRefund:r3({method:"POST",fullPath:"/v1/application_fees/{id}/refunds"}),listRefunds:r3({method:"GET",fullPath:"/v1/application_fees/{id}/refunds",methodType:"list"}),retrieveRefund:r3({method:"GET",fullPath:"/v1/application_fees/{fee}/refunds/{id}"}),updateRefund:r3({method:"POST",fullPath:"/v1/application_fees/{fee}/refunds/{id}"})}),se=eV.method,st=eV.extend({retrieve:se({method:"GET",fullPath:"/v1/balance"})}),sr=eV.method,ss=eV.extend({retrieve:sr({method:"GET",fullPath:"/v1/balance_transactions/{id}"}),list:sr({method:"GET",fullPath:"/v1/balance_transactions",methodType:"list"})}),sn=eV.method,si=eV.extend({create:sn({method:"POST",fullPath:"/v1/charges"}),retrieve:sn({method:"GET",fullPath:"/v1/charges/{charge}"}),update:sn({method:"POST",fullPath:"/v1/charges/{charge}"}),list:sn({method:"GET",fullPath:"/v1/charges",methodType:"list"}),capture:sn({method:"POST",fullPath:"/v1/charges/{charge}/capture"}),search:sn({method:"GET",fullPath:"/v1/charges/search",methodType:"search"})}),sa=eV.method,so=eV.extend({retrieve:sa({method:"GET",fullPath:"/v1/confirmation_tokens/{confirmation_token}"})}),sl=eV.method,su=eV.extend({retrieve:sl({method:"GET",fullPath:"/v1/country_specs/{country}"}),list:sl({method:"GET",fullPath:"/v1/country_specs",methodType:"list"})}),sc=eV.method,sd=eV.extend({create:sc({method:"POST",fullPath:"/v1/coupons"}),retrieve:sc({method:"GET",fullPath:"/v1/coupons/{coupon}"}),update:sc({method:"POST",fullPath:"/v1/coupons/{coupon}"}),list:sc({method:"GET",fullPath:"/v1/coupons",methodType:"list"}),del:sc({method:"DELETE",fullPath:"/v1/coupons/{coupon}"})}),sh=eV.method,sm=eV.extend({create:sh({method:"POST",fullPath:"/v1/credit_notes"}),retrieve:sh({method:"GET",fullPath:"/v1/credit_notes/{id}"}),update:sh({method:"POST",fullPath:"/v1/credit_notes/{id}"}),list:sh({method:"GET",fullPath:"/v1/credit_notes",methodType:"list"}),listLineItems:sh({method:"GET",fullPath:"/v1/credit_notes/{credit_note}/lines",methodType:"list"}),listPreviewLineItems:sh({method:"GET",fullPath:"/v1/credit_notes/preview/lines",methodType:"list"}),preview:sh({method:"GET",fullPath:"/v1/credit_notes/preview"}),voidCreditNote:sh({method:"POST",fullPath:"/v1/credit_notes/{id}/void"})}),sp=eV.method,sf=eV.extend({create:sp({method:"POST",fullPath:"/v1/customer_sessions"})}),sv=eV.method,sP=eV.extend({create:sv({method:"POST",fullPath:"/v1/customers"}),retrieve:sv({method:"GET",fullPath:"/v1/customers/{customer}"}),update:sv({method:"POST",fullPath:"/v1/customers/{customer}"}),list:sv({method:"GET",fullPath:"/v1/customers",methodType:"list"}),del:sv({method:"DELETE",fullPath:"/v1/customers/{customer}"}),createBalanceTransaction:sv({method:"POST",fullPath:"/v1/customers/{customer}/balance_transactions"}),createFundingInstructions:sv({method:"POST",fullPath:"/v1/customers/{customer}/funding_instructions"}),createSource:sv({method:"POST",fullPath:"/v1/customers/{customer}/sources"}),createTaxId:sv({method:"POST",fullPath:"/v1/customers/{customer}/tax_ids"}),deleteDiscount:sv({method:"DELETE",fullPath:"/v1/customers/{customer}/discount"}),deleteSource:sv({method:"DELETE",fullPath:"/v1/customers/{customer}/sources/{id}"}),deleteTaxId:sv({method:"DELETE",fullPath:"/v1/customers/{customer}/tax_ids/{id}"}),listBalanceTransactions:sv({method:"GET",fullPath:"/v1/customers/{customer}/balance_transactions",methodType:"list"}),listCashBalanceTransactions:sv({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance_transactions",methodType:"list"}),listPaymentMethods:sv({method:"GET",fullPath:"/v1/customers/{customer}/payment_methods",methodType:"list"}),listSources:sv({method:"GET",fullPath:"/v1/customers/{customer}/sources",methodType:"list"}),listTaxIds:sv({method:"GET",fullPath:"/v1/customers/{customer}/tax_ids",methodType:"list"}),retrieveBalanceTransaction:sv({method:"GET",fullPath:"/v1/customers/{customer}/balance_transactions/{transaction}"}),retrieveCashBalance:sv({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance"}),retrieveCashBalanceTransaction:sv({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance_transactions/{transaction}"}),retrievePaymentMethod:sv({method:"GET",fullPath:"/v1/customers/{customer}/payment_methods/{payment_method}"}),retrieveSource:sv({method:"GET",fullPath:"/v1/customers/{customer}/sources/{id}"}),retrieveTaxId:sv({method:"GET",fullPath:"/v1/customers/{customer}/tax_ids/{id}"}),search:sv({method:"GET",fullPath:"/v1/customers/search",methodType:"search"}),updateBalanceTransaction:sv({method:"POST",fullPath:"/v1/customers/{customer}/balance_transactions/{transaction}"}),updateCashBalance:sv({method:"POST",fullPath:"/v1/customers/{customer}/cash_balance"}),updateSource:sv({method:"POST",fullPath:"/v1/customers/{customer}/sources/{id}"}),verifySource:sv({method:"POST",fullPath:"/v1/customers/{customer}/sources/{id}/verify"})}),sy=eV.method,sg=eV.extend({retrieve:sy({method:"GET",fullPath:"/v1/disputes/{dispute}"}),update:sy({method:"POST",fullPath:"/v1/disputes/{dispute}"}),list:sy({method:"GET",fullPath:"/v1/disputes",methodType:"list"}),close:sy({method:"POST",fullPath:"/v1/disputes/{dispute}/close"})}),sT=eV.method,s_=eV.extend({create:sT({method:"POST",fullPath:"/v1/ephemeral_keys",validator:(e,t)=>{if(!t.headers||!t.headers["Stripe-Version"])throw Error("Passing apiVersion in a separate options hash is required to create an ephemeral key. See https://stripe.com/docs/api/versioning?lang=node")}}),del:sT({method:"DELETE",fullPath:"/v1/ephemeral_keys/{key}"})}),sb=eV.method,sx=eV.extend({retrieve:sb({method:"GET",fullPath:"/v1/events/{id}"}),list:sb({method:"GET",fullPath:"/v1/events",methodType:"list"})}),sE=eV.method,sS=eV.extend({retrieve:sE({method:"GET",fullPath:"/v1/exchange_rates/{rate_id}"}),list:sE({method:"GET",fullPath:"/v1/exchange_rates",methodType:"list"})}),sw=eV.method,sO=eV.extend({create:sw({method:"POST",fullPath:"/v1/file_links"}),retrieve:sw({method:"GET",fullPath:"/v1/file_links/{link}"}),update:sw({method:"POST",fullPath:"/v1/file_links/{link}"}),list:sw({method:"GET",fullPath:"/v1/file_links",methodType:"list"})}),sA=(e,t,r)=>{let s=(Math.round(1e16*Math.random())+Math.round(1e16*Math.random())).toString();r["Content-Type"]=`multipart/form-data; boundary=${s}`;let n=new TextEncoder,i=new Uint8Array(0),a=n.encode("\r\n");function o(e){let t=i,r=e instanceof Uint8Array?e:new Uint8Array(n.encode(e));(i=new Uint8Array(t.length+r.length+2)).set(t),i.set(r,t.length),i.set(a,i.length-2)}function l(e){return`"${e.replace(/"|"/g,"%22").replace(/\r\n|\r|\n/g," ")}"`}let u=function(e){let t={},r=(e,s)=>{Object.entries(e).forEach(([e,n])=>{let i=s?`${s}[${e}]`:e;if(function(e){let t=typeof e;return("function"===t||"object"===t)&&!!e}(n)){if(!(n instanceof Uint8Array)&&!Object.prototype.hasOwnProperty.call(n,"data"))return r(n,i);t[i]=n}else t[i]=String(n)})};return r(e,null),t}(t);for(let e in u){if(!Object.prototype.hasOwnProperty.call(u,e))continue;let t=u[e];o(`--${s}`),Object.prototype.hasOwnProperty.call(t,"data")?(o(`Content-Disposition: form-data; name=${l(e)}; filename=${l(t.name||"blob")}`),o(`Content-Type: ${t.type||"application/octet-stream"}`),o(""),o(t.data)):(o(`Content-Disposition: form-data; name=${l(e)}`),o(""),o(t))}return o(`--${s}--`),i},sj=eV.method,sk=eV.extend({create:sj({method:"POST",fullPath:"/v1/files",headers:{"Content-Type":"multipart/form-data"},host:"files.stripe.com"}),retrieve:sj({method:"GET",fullPath:"/v1/files/{file}"}),list:sj({method:"GET",fullPath:"/v1/files",methodType:"list"}),requestDataProcessor:function(e,t,r,s){if(t=t||{},"POST"!==e)return s(null,eC(t));this._stripe._platformFunctions.tryBufferData(t).then(t=>s(null,sA(e,t,r))).catch(e=>s(e,null))}}),sC=eV.method,sN=eV.extend({create:sC({method:"POST",fullPath:"/v1/invoiceitems"}),retrieve:sC({method:"GET",fullPath:"/v1/invoiceitems/{invoiceitem}"}),update:sC({method:"POST",fullPath:"/v1/invoiceitems/{invoiceitem}"}),list:sC({method:"GET",fullPath:"/v1/invoiceitems",methodType:"list"}),del:sC({method:"DELETE",fullPath:"/v1/invoiceitems/{invoiceitem}"})}),sR=eV.method,sG=eV.extend({retrieve:sR({method:"GET",fullPath:"/v1/invoice_rendering_templates/{template}"}),list:sR({method:"GET",fullPath:"/v1/invoice_rendering_templates",methodType:"list"}),archive:sR({method:"POST",fullPath:"/v1/invoice_rendering_templates/{template}/archive"}),unarchive:sR({method:"POST",fullPath:"/v1/invoice_rendering_templates/{template}/unarchive"})}),sI=eV.method,sD=eV.extend({create:sI({method:"POST",fullPath:"/v1/invoices"}),retrieve:sI({method:"GET",fullPath:"/v1/invoices/{invoice}"}),update:sI({method:"POST",fullPath:"/v1/invoices/{invoice}"}),list:sI({method:"GET",fullPath:"/v1/invoices",methodType:"list"}),del:sI({method:"DELETE",fullPath:"/v1/invoices/{invoice}"}),addLines:sI({method:"POST",fullPath:"/v1/invoices/{invoice}/add_lines"}),createPreview:sI({method:"POST",fullPath:"/v1/invoices/create_preview"}),finalizeInvoice:sI({method:"POST",fullPath:"/v1/invoices/{invoice}/finalize"}),listLineItems:sI({method:"GET",fullPath:"/v1/invoices/{invoice}/lines",methodType:"list"}),listUpcomingLines:sI({method:"GET",fullPath:"/v1/invoices/upcoming/lines",methodType:"list"}),markUncollectible:sI({method:"POST",fullPath:"/v1/invoices/{invoice}/mark_uncollectible"}),pay:sI({method:"POST",fullPath:"/v1/invoices/{invoice}/pay"}),removeLines:sI({method:"POST",fullPath:"/v1/invoices/{invoice}/remove_lines"}),retrieveUpcoming:sI({method:"GET",fullPath:"/v1/invoices/upcoming"}),search:sI({method:"GET",fullPath:"/v1/invoices/search",methodType:"search"}),sendInvoice:sI({method:"POST",fullPath:"/v1/invoices/{invoice}/send"}),updateLines:sI({method:"POST",fullPath:"/v1/invoices/{invoice}/update_lines"}),updateLineItem:sI({method:"POST",fullPath:"/v1/invoices/{invoice}/lines/{line_item_id}"}),voidInvoice:sI({method:"POST",fullPath:"/v1/invoices/{invoice}/void"})}),sq=eV.method,sM=eV.extend({retrieve:sq({method:"GET",fullPath:"/v1/mandates/{mandate}"})}),sU=eV.method,sL="connect.stripe.com",sF=eV.extend({basePath:"/",authorizeUrl(e,t){e=e||{};let r="oauth/authorize";return(t=t||{}).express&&(r=`express/${r}`),e.response_type||(e.response_type="code"),e.client_id||(e.client_id=this._stripe.getClientId()),e.scope||(e.scope="read_write"),`https://${sL}/${r}?${eC(e)}`},token:sU({method:"POST",path:"oauth/token",host:sL}),deauthorize(e,...t){return e.client_id||(e.client_id=this._stripe.getClientId()),sU({method:"POST",path:"oauth/deauthorize",host:sL}).apply(this,[e,...t])}}),s$=eV.method,sH=eV.extend({create:s$({method:"POST",fullPath:"/v1/payment_intents"}),retrieve:s$({method:"GET",fullPath:"/v1/payment_intents/{intent}"}),update:s$({method:"POST",fullPath:"/v1/payment_intents/{intent}"}),list:s$({method:"GET",fullPath:"/v1/payment_intents",methodType:"list"}),applyCustomerBalance:s$({method:"POST",fullPath:"/v1/payment_intents/{intent}/apply_customer_balance"}),cancel:s$({method:"POST",fullPath:"/v1/payment_intents/{intent}/cancel"}),capture:s$({method:"POST",fullPath:"/v1/payment_intents/{intent}/capture"}),confirm:s$({method:"POST",fullPath:"/v1/payment_intents/{intent}/confirm"}),incrementAuthorization:s$({method:"POST",fullPath:"/v1/payment_intents/{intent}/increment_authorization"}),search:s$({method:"GET",fullPath:"/v1/payment_intents/search",methodType:"search"}),verifyMicrodeposits:s$({method:"POST",fullPath:"/v1/payment_intents/{intent}/verify_microdeposits"})}),sz=eV.method,sK=eV.extend({create:sz({method:"POST",fullPath:"/v1/payment_links"}),retrieve:sz({method:"GET",fullPath:"/v1/payment_links/{payment_link}"}),update:sz({method:"POST",fullPath:"/v1/payment_links/{payment_link}"}),list:sz({method:"GET",fullPath:"/v1/payment_links",methodType:"list"}),listLineItems:sz({method:"GET",fullPath:"/v1/payment_links/{payment_link}/line_items",methodType:"list"})}),sB=eV.method,sW=eV.extend({create:sB({method:"POST",fullPath:"/v1/payment_method_configurations"}),retrieve:sB({method:"GET",fullPath:"/v1/payment_method_configurations/{configuration}"}),update:sB({method:"POST",fullPath:"/v1/payment_method_configurations/{configuration}"}),list:sB({method:"GET",fullPath:"/v1/payment_method_configurations",methodType:"list"})}),sV=eV.method,sJ=eV.extend({create:sV({method:"POST",fullPath:"/v1/payment_method_domains"}),retrieve:sV({method:"GET",fullPath:"/v1/payment_method_domains/{payment_method_domain}"}),update:sV({method:"POST",fullPath:"/v1/payment_method_domains/{payment_method_domain}"}),list:sV({method:"GET",fullPath:"/v1/payment_method_domains",methodType:"list"}),validate:sV({method:"POST",fullPath:"/v1/payment_method_domains/{payment_method_domain}/validate"})}),sY=eV.method,sZ=eV.extend({create:sY({method:"POST",fullPath:"/v1/payment_methods"}),retrieve:sY({method:"GET",fullPath:"/v1/payment_methods/{payment_method}"}),update:sY({method:"POST",fullPath:"/v1/payment_methods/{payment_method}"}),list:sY({method:"GET",fullPath:"/v1/payment_methods",methodType:"list"}),attach:sY({method:"POST",fullPath:"/v1/payment_methods/{payment_method}/attach"}),detach:sY({method:"POST",fullPath:"/v1/payment_methods/{payment_method}/detach"})}),sQ=eV.method,sX=eV.extend({create:sQ({method:"POST",fullPath:"/v1/payouts"}),retrieve:sQ({method:"GET",fullPath:"/v1/payouts/{payout}"}),update:sQ({method:"POST",fullPath:"/v1/payouts/{payout}"}),list:sQ({method:"GET",fullPath:"/v1/payouts",methodType:"list"}),cancel:sQ({method:"POST",fullPath:"/v1/payouts/{payout}/cancel"}),reverse:sQ({method:"POST",fullPath:"/v1/payouts/{payout}/reverse"})}),s1=eV.method,s0=eV.extend({create:s1({method:"POST",fullPath:"/v1/plans"}),retrieve:s1({method:"GET",fullPath:"/v1/plans/{plan}"}),update:s1({method:"POST",fullPath:"/v1/plans/{plan}"}),list:s1({method:"GET",fullPath:"/v1/plans",methodType:"list"}),del:s1({method:"DELETE",fullPath:"/v1/plans/{plan}"})}),s2=eV.method,s4=eV.extend({create:s2({method:"POST",fullPath:"/v1/prices"}),retrieve:s2({method:"GET",fullPath:"/v1/prices/{price}"}),update:s2({method:"POST",fullPath:"/v1/prices/{price}"}),list:s2({method:"GET",fullPath:"/v1/prices",methodType:"list"}),search:s2({method:"GET",fullPath:"/v1/prices/search",methodType:"search"})}),s8=eV.method,s6=eV.extend({create:s8({method:"POST",fullPath:"/v1/products"}),retrieve:s8({method:"GET",fullPath:"/v1/products/{id}"}),update:s8({method:"POST",fullPath:"/v1/products/{id}"}),list:s8({method:"GET",fullPath:"/v1/products",methodType:"list"}),del:s8({method:"DELETE",fullPath:"/v1/products/{id}"}),createFeature:s8({method:"POST",fullPath:"/v1/products/{product}/features"}),deleteFeature:s8({method:"DELETE",fullPath:"/v1/products/{product}/features/{id}"}),listFeatures:s8({method:"GET",fullPath:"/v1/products/{product}/features",methodType:"list"}),retrieveFeature:s8({method:"GET",fullPath:"/v1/products/{product}/features/{id}"}),search:s8({method:"GET",fullPath:"/v1/products/search",methodType:"search"})}),s7=eV.method,s5=eV.extend({create:s7({method:"POST",fullPath:"/v1/promotion_codes"}),retrieve:s7({method:"GET",fullPath:"/v1/promotion_codes/{promotion_code}"}),update:s7({method:"POST",fullPath:"/v1/promotion_codes/{promotion_code}"}),list:s7({method:"GET",fullPath:"/v1/promotion_codes",methodType:"list"})}),s3=eV.method,s9=eV.extend({create:s3({method:"POST",fullPath:"/v1/quotes"}),retrieve:s3({method:"GET",fullPath:"/v1/quotes/{quote}"}),update:s3({method:"POST",fullPath:"/v1/quotes/{quote}"}),list:s3({method:"GET",fullPath:"/v1/quotes",methodType:"list"}),accept:s3({method:"POST",fullPath:"/v1/quotes/{quote}/accept"}),cancel:s3({method:"POST",fullPath:"/v1/quotes/{quote}/cancel"}),finalizeQuote:s3({method:"POST",fullPath:"/v1/quotes/{quote}/finalize"}),listComputedUpfrontLineItems:s3({method:"GET",fullPath:"/v1/quotes/{quote}/computed_upfront_line_items",methodType:"list"}),listLineItems:s3({method:"GET",fullPath:"/v1/quotes/{quote}/line_items",methodType:"list"}),pdf:s3({method:"GET",fullPath:"/v1/quotes/{quote}/pdf",host:"files.stripe.com",streaming:!0})}),ne=eV.method,nt=eV.extend({create:ne({method:"POST",fullPath:"/v1/refunds"}),retrieve:ne({method:"GET",fullPath:"/v1/refunds/{refund}"}),update:ne({method:"POST",fullPath:"/v1/refunds/{refund}"}),list:ne({method:"GET",fullPath:"/v1/refunds",methodType:"list"}),cancel:ne({method:"POST",fullPath:"/v1/refunds/{refund}/cancel"})}),nr=eV.method,ns=eV.extend({retrieve:nr({method:"GET",fullPath:"/v1/reviews/{review}"}),list:nr({method:"GET",fullPath:"/v1/reviews",methodType:"list"}),approve:nr({method:"POST",fullPath:"/v1/reviews/{review}/approve"})}),nn=eV.method,ni=eV.extend({list:nn({method:"GET",fullPath:"/v1/setup_attempts",methodType:"list"})}),na=eV.method,no=eV.extend({create:na({method:"POST",fullPath:"/v1/setup_intents"}),retrieve:na({method:"GET",fullPath:"/v1/setup_intents/{intent}"}),update:na({method:"POST",fullPath:"/v1/setup_intents/{intent}"}),list:na({method:"GET",fullPath:"/v1/setup_intents",methodType:"list"}),cancel:na({method:"POST",fullPath:"/v1/setup_intents/{intent}/cancel"}),confirm:na({method:"POST",fullPath:"/v1/setup_intents/{intent}/confirm"}),verifyMicrodeposits:na({method:"POST",fullPath:"/v1/setup_intents/{intent}/verify_microdeposits"})}),nl=eV.method,nu=eV.extend({create:nl({method:"POST",fullPath:"/v1/shipping_rates"}),retrieve:nl({method:"GET",fullPath:"/v1/shipping_rates/{shipping_rate_token}"}),update:nl({method:"POST",fullPath:"/v1/shipping_rates/{shipping_rate_token}"}),list:nl({method:"GET",fullPath:"/v1/shipping_rates",methodType:"list"})}),nc=eV.method,nd=eV.extend({create:nc({method:"POST",fullPath:"/v1/sources"}),retrieve:nc({method:"GET",fullPath:"/v1/sources/{source}"}),update:nc({method:"POST",fullPath:"/v1/sources/{source}"}),listSourceTransactions:nc({method:"GET",fullPath:"/v1/sources/{source}/source_transactions",methodType:"list"}),verify:nc({method:"POST",fullPath:"/v1/sources/{source}/verify"})}),nh=eV.method,nm=eV.extend({create:nh({method:"POST",fullPath:"/v1/subscription_items"}),retrieve:nh({method:"GET",fullPath:"/v1/subscription_items/{item}"}),update:nh({method:"POST",fullPath:"/v1/subscription_items/{item}"}),list:nh({method:"GET",fullPath:"/v1/subscription_items",methodType:"list"}),del:nh({method:"DELETE",fullPath:"/v1/subscription_items/{item}"}),createUsageRecord:nh({method:"POST",fullPath:"/v1/subscription_items/{subscription_item}/usage_records"}),listUsageRecordSummaries:nh({method:"GET",fullPath:"/v1/subscription_items/{subscription_item}/usage_record_summaries",methodType:"list"})}),np=eV.method,nf=eV.extend({create:np({method:"POST",fullPath:"/v1/subscription_schedules"}),retrieve:np({method:"GET",fullPath:"/v1/subscription_schedules/{schedule}"}),update:np({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}"}),list:np({method:"GET",fullPath:"/v1/subscription_schedules",methodType:"list"}),cancel:np({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}/cancel"}),release:np({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}/release"})}),nv=eV.method,nP=eV.extend({create:nv({method:"POST",fullPath:"/v1/subscriptions"}),retrieve:nv({method:"GET",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),update:nv({method:"POST",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),list:nv({method:"GET",fullPath:"/v1/subscriptions",methodType:"list"}),cancel:nv({method:"DELETE",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),deleteDiscount:nv({method:"DELETE",fullPath:"/v1/subscriptions/{subscription_exposed_id}/discount"}),resume:nv({method:"POST",fullPath:"/v1/subscriptions/{subscription}/resume"}),search:nv({method:"GET",fullPath:"/v1/subscriptions/search",methodType:"search"})}),ny=eV.method,ng=eV.extend({retrieve:ny({method:"GET",fullPath:"/v1/tax_codes/{id}"}),list:ny({method:"GET",fullPath:"/v1/tax_codes",methodType:"list"})}),nT=eV.method,n_=eV.extend({create:nT({method:"POST",fullPath:"/v1/tax_ids"}),retrieve:nT({method:"GET",fullPath:"/v1/tax_ids/{id}"}),list:nT({method:"GET",fullPath:"/v1/tax_ids",methodType:"list"}),del:nT({method:"DELETE",fullPath:"/v1/tax_ids/{id}"})}),nb=eV.method,nx=eV.extend({create:nb({method:"POST",fullPath:"/v1/tax_rates"}),retrieve:nb({method:"GET",fullPath:"/v1/tax_rates/{tax_rate}"}),update:nb({method:"POST",fullPath:"/v1/tax_rates/{tax_rate}"}),list:nb({method:"GET",fullPath:"/v1/tax_rates",methodType:"list"})}),nE=eV.method,nS=eV.extend({create:nE({method:"POST",fullPath:"/v1/tokens"}),retrieve:nE({method:"GET",fullPath:"/v1/tokens/{token}"})}),nw=eV.method,nO=eV.extend({create:nw({method:"POST",fullPath:"/v1/topups"}),retrieve:nw({method:"GET",fullPath:"/v1/topups/{topup}"}),update:nw({method:"POST",fullPath:"/v1/topups/{topup}"}),list:nw({method:"GET",fullPath:"/v1/topups",methodType:"list"}),cancel:nw({method:"POST",fullPath:"/v1/topups/{topup}/cancel"})}),nA=eV.method,nj=eV.extend({create:nA({method:"POST",fullPath:"/v1/transfers"}),retrieve:nA({method:"GET",fullPath:"/v1/transfers/{transfer}"}),update:nA({method:"POST",fullPath:"/v1/transfers/{transfer}"}),list:nA({method:"GET",fullPath:"/v1/transfers",methodType:"list"}),createReversal:nA({method:"POST",fullPath:"/v1/transfers/{id}/reversals"}),listReversals:nA({method:"GET",fullPath:"/v1/transfers/{id}/reversals",methodType:"list"}),retrieveReversal:nA({method:"GET",fullPath:"/v1/transfers/{transfer}/reversals/{id}"}),updateReversal:nA({method:"POST",fullPath:"/v1/transfers/{transfer}/reversals/{id}"})}),nk=eV.method,nC=eV.extend({create:nk({method:"POST",fullPath:"/v1/webhook_endpoints"}),retrieve:nk({method:"GET",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"}),update:nk({method:"POST",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"}),list:nk({method:"GET",fullPath:"/v1/webhook_endpoints",methodType:"list"}),del:nk({method:"DELETE",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"})}),nN=eZ("apps",{Secrets:rg}),nR=eZ("billing",{Alerts:e4,MeterEventAdjustments:tI,MeterEvents:tq,Meters:tU}),nG=eZ("billingPortal",{Configurations:to,Sessions:r_}),nI=eZ("checkout",{Sessions:rx}),nD=eZ("climate",{Orders:tF,Products:t4,Suppliers:rj}),nq=eZ("entitlements",{ActiveEntitlements:e0,Features:tS}),nM=eZ("financialConnections",{Accounts:eX,Sessions:rS,Transactions:rU}),nU=eZ("forwarding",{Requests:rf}),nL=eZ("identity",{VerificationReports:rZ,VerificationSessions:rX}),nF=eZ("issuing",{Authorizations:e5,Cardholders:tt,Cards:ti,Disputes:t_,PersonalizationDesigns:tX,PhysicalBundles:t0,Tokens:rR,Transactions:rF}),n$=eZ("radar",{EarlyFraudWarnings:tx,ValueListItems:rW,ValueLists:rJ}),nH=eZ("reporting",{ReportRuns:rd,ReportTypes:rm}),nz=eZ("sigma",{ScheduledQueryRuns:rP}),nK=eZ("tax",{Calculations:e9,Registrations:ru,Settings:rO,Transactions:rH}),nB=eZ("terminal",{Configurations:tu,ConnectionTokens:tm,Locations:tR,Readers:t5}),nW=eZ("testHelpers",{ConfirmationTokens:td,Customers:tP,Refunds:ro,TestClocks:rC,Issuing:eZ("issuing",{Authorizations:e6,Cards:ts,PersonalizationDesigns:tZ,Transactions:rq}),Terminal:eZ("terminal",{Readers:t6}),Treasury:eZ("treasury",{InboundTransfers:tj,OutboundPayments:tH,OutboundTransfers:tW,ReceivedCredits:t9,ReceivedDebits:rs})}),nV=eZ("treasury",{CreditReversals:tf,DebitReversals:tg,FinancialAccounts:tO,InboundTransfers:tC,OutboundPayments:tK,OutboundTransfers:tJ,ReceivedCredits:rt,ReceivedDebits:ri,TransactionEntries:rI,Transactions:rK}),nJ="api.stripe.com",nY="/v1/",nZ="2024-06-20",nQ=["name","version","url","partner_id"],nX=["apiVersion","typescript","maxNetworkRetries","httpAgent","httpClient","timeout","host","port","protocol","telemetry","appInfo","stripeAccount"],n1=e=>new eF(e,eV.MAX_BUFFERED_REQUEST_METRICS),n0=function(e,t=n1){function r(t=e){return eJ(t)}function s(n,i={}){if(!(this instanceof s))return new s(n,i);let a=this._getPropsFromConfig(i);this._platformFunctions=e,Object.defineProperty(this,"_emitter",{value:this._platformFunctions.createEmitter(),enumerable:!1,configurable:!1,writable:!1}),this.VERSION=s.PACKAGE_VERSION,this.on=this._emitter.on.bind(this._emitter),this.once=this._emitter.once.bind(this._emitter),this.off=this._emitter.removeListener.bind(this._emitter);let o=a.httpAgent||null;this._api={auth:null,host:a.host||nJ,port:a.port||"443",protocol:a.protocol||"https",basePath:nY,version:a.apiVersion||nZ,timeout:eq("timeout",a.timeout,8e4),maxNetworkRetries:eq("maxNetworkRetries",a.maxNetworkRetries,1),agent:o,httpClient:a.httpClient||(o?this._platformFunctions.createNodeHttpClient(o):this._platformFunctions.createDefaultHttpClient()),dev:!1,stripeAccount:a.stripeAccount||null};let l=a.typescript||!1;l!==s.USER_AGENT.typescript&&(s.USER_AGENT.typescript=l),a.appInfo&&this._setAppInfo(a.appInfo),this._prepResources(),this._setApiKey(n),this.errors=b,this.webhooks=r(),this._prevRequestMetrics=[],this._enableTelemetry=!1!==a.telemetry,this._requestSender=t(this),this.StripeResource=s.StripeResource}return s.PACKAGE_VERSION="16.12.0",s.USER_AGENT=Object.assign({bindings_version:s.PACKAGE_VERSION,lang:"node",publisher:"stripe",uname:null,typescript:!1},"undefined"==typeof process?{}:{lang_version:process.version,platform:process.platform}),s.StripeResource=eV,s.resources=x,s.HttpClient=er,s.HttpClientResponse=es,s.CryptoProvider=J,s.webhooks=Object.assign(r,eJ(e)),s.errors=b,s.createNodeHttpClient=e.createNodeHttpClient,s.createFetchHttpClient=e.createFetchHttpClient,s.createNodeCryptoProvider=e.createNodeCryptoProvider,s.createSubtleCryptoProvider=e.createSubtleCryptoProvider,s.prototype={_appInfo:void 0,on:null,off:null,once:null,VERSION:null,StripeResource:null,webhooks:null,errors:null,_api:null,_prevRequestMetrics:null,_emitter:null,_enableTelemetry:null,_requestSender:null,_platformFunctions:null,_setApiKey(e){e&&this._setApiField("auth",`Bearer ${e}`)},_setAppInfo(e){if(e&&"object"!=typeof e)throw Error("AppInfo must be an object.");if(e&&!e.name)throw Error("AppInfo.name is required");e=e||{},this._appInfo=nQ.reduce((t,r)=>("string"==typeof e[r]&&((t=t||{})[r]=e[r]),t),void 0)},_setApiField(e,t){this._api[e]=t},getApiField(e){return this._api[e]},setClientId(e){this._clientId=e},getClientId(){return this._clientId},getConstant:e=>{switch(e){case"DEFAULT_HOST":return nJ;case"DEFAULT_PORT":return"443";case"DEFAULT_BASE_PATH":return nY;case"DEFAULT_API_VERSION":return nZ;case"DEFAULT_TIMEOUT":return 8e4;case"MAX_NETWORK_RETRY_DELAY_SEC":return 2;case"INITIAL_NETWORK_RETRY_DELAY_SEC":return .5}return s[e]},getMaxNetworkRetries(){return this.getApiField("maxNetworkRetries")},_setApiNumberField(e,t,r){let s=eq(e,t,r);this._setApiField(e,s)},getMaxNetworkRetryDelay:()=>2,getInitialNetworkRetryDelay:()=>.5,getClientUserAgent(e){return this.getClientUserAgentSeeded(s.USER_AGENT,e)},getClientUserAgentSeeded(e,t){this._platformFunctions.getUname().then(r=>{var s;let n={};for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[t]=encodeURIComponent(null!==(s=e[t])&&void 0!==s?s:"null"));n.uname=encodeURIComponent(r||"UNKNOWN");let i=this.getApiField("httpClient");i&&(n.httplib=encodeURIComponent(i.getClientName())),this._appInfo&&(n.application=this._appInfo),t(JSON.stringify(n))})},getAppInfoAsString(){if(!this._appInfo)return"";let e=this._appInfo.name;return this._appInfo.version&&(e+=`/${this._appInfo.version}`),this._appInfo.url&&(e+=` (${this._appInfo.url})`),e},getTelemetryEnabled(){return this._enableTelemetry},_prepResources(){for(let e in x)Object.prototype.hasOwnProperty.call(x,e)&&(this["OAuth"===e?"oauth":e[0].toLowerCase()+e.substring(1)]=new x[e](this))},_getPropsFromConfig(e){if(!e)return{};let t="string"==typeof e;if(!(e===Object(e)&&!Array.isArray(e))&&!t)throw Error("Config must either be an object or a string");if(t)return{apiVersion:e};if(Object.keys(e).filter(e=>!nX.includes(e)).length>0)throw Error(`Config object may only contain the following: ${nX.join(", ")}`);return e}},s}(new eL);var n2=r(1542);class n4{async createCustomerPortal(e,t){return(await this.stripe.billingPortal.sessions.create({customer:e,return_url:t})).url}async findCheckoutSession(e){try{return await this.stripe.checkout.sessions.retrieve(e,{expand:["line_items"]})}catch(e){return console.error(e),null}}async getSubscription(e){return this.stripe.subscriptions.retrieve(e)}async getCheckoutSession(e){return this.stripe.checkout.sessions.retrieve(e)}constructor(){this.stripe=new n0(process.env.STRIPE_SECRET_KEY,{typescript:!0})}}let n8=new n4;var n6=r(12250);let n7=new V.EventEmitter;n7.on("sendThanksYouEmail",async e=>{n6.g.sendThanksYouEmail(e)});var n5=r(40618);let n3=new n0(process.env.STRIPE_SECRET_KEY,{apiVersion:"2024-06-20"});async function n9(e){return await S.Z.subscription.findFirst({where:{user_clerk_id:e}})}async function ie(e,t){let r=t.data.object,s=await n8.findCheckoutSession(r.id),n=s?.customer,i=s?.line_items?.data[0]?.price.id;if(!n2.Z.stripe.products.find(e=>e.priceId===i))return U.NextResponse.json({error:"Plan not found"},{status:400});let a=await n3.customers.retrieve(n);if(!a||!a.email)return console.error("No customer email found"),U.NextResponse.json({error:"No customer email found for subscription"},{status:400});let o=await n3.prices.retrieve(i);if(!o)return new Response("Price not found",{status:500});let l=await n3.products.retrieve("string"==typeof o.product?o.product:o.product.id);if(!l)return new Response("Product not found",{status:500});let u=l.metadata.subscription_type||"default",c=a.email,d=await B.users.getUserList({emailAddress:[c]});if(!d.data.length)return console.error("Clerc user not found"),U.NextResponse.json({error:"No customer email found for subscription"},{status:400});let h=d.data[0],m=e?.data?.object?.subscription;return await S.Z.subscription.upsert({where:{user_clerk_id:h.id},create:{last_stripe_cs_id:r.id,user_clerk_id:h.id,...m?{sub_stripe_id:m}:{},sub_status:"active",user_email:c,sub_type:u,stripe_customer_id:a.id},update:{last_stripe_cs_id:r.id,...m?{sub_stripe_id:m}:{},sub_status:"active"}}),n7.emit("sendThanksYouEmail",c),U.NextResponse.json({},{status:200})}async function it(e){let t=e.data.object,r=await n3.subscriptions.retrieve(t.id),s=await S.Z.subscription.findFirst({where:{sub_stripe_id:r.id}});await S.Z.subscription.update({where:{id:s.id},data:{sub_status:"inactive"}})}async function ir(e,t){let r=t.data.object,s=await S.Z.subscription.findFirst({where:{stripe_customer_id:r.customer.toString()}}),n=e?.data?.object?.subscription;return n?s?.sub_stripe_id!==n?new Response("Subscription is invalid",{status:403}):void await S.Z.subscription.update({where:{id:s.id},data:{sub_status:"active"}}):new Response("Subscription is invalid",{status:400})}(0,n5.h)([n9,ie,it,ir]),(0,E.j)("1c548c926b5e17db6a6e381a3f15e16b07a70dca",n9),(0,E.j)("78d93be0f3171de0df85ce5e42f8418a9c8f37e4",ie),(0,E.j)("f377f06404a2b61735b4a2be973e74b304c9cb4d",it),(0,E.j)("cca4f3aba61e31f11eb6718d2992c5e458bb5109",ir)},95752:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(19510),n=r(68570);let i=(0,n.createProxy)(String.raw`C:\Projects\PersonalPortal\src\components\Scenarios.tsx#default`),a=(0,n.createProxy)(String.raw`C:\Projects\PersonalPortal\src\components\ThankyouPopUp.tsx#default`);var o=r(78459),l=r(58585),u=r(44393);let c=(0,n.createProxy)(String.raw`C:\Projects\PersonalPortal\src\components\PricingSectionPhotography.tsx#default`);async function d(){let{userId:e}=(0,o.I)();e||(0,l.redirect)("/sign-in");let t=await (0,u.getSubscriptionByUserId)(e),r=!t||t?.sub_status!=="active";return r&&(0,l.redirect)("/processing-page"),s.jsx("div",{children:r?s.jsx(c,{}):(0,s.jsxs)("div",{children:[s.jsx(i,{}),s.jsx(a,{})]})})}},22247:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=new(require("@prisma/client")).PrismaClient},12250:(e,t,r)=>{"use strict";r.d(t,{g:()=>g});var s=r(19510);r(71159);let n=e=>(0,s.jsxs)("div",{className:"max-w-3xl mx-auto p-8 bg-white",children:[(0,s.jsxs)("div",{className:"mb-8",children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Invoice"}),s.jsx("div",{className:"grid grid-cols-2 gap-4",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:[s.jsx("span",{className:"font-medium",children:"Invoice number: "}),e.id]}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:[s.jsx("span",{className:"font-medium",children:"Date paid: "}),e.paid_date]})]})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-8 mb-8",children:[(0,s.jsxs)("div",{children:[s.jsx("h2",{className:"text-lg font-semibold mb-3",children:"From:"}),(0,s.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[s.jsx("p",{className:"font-medium",children:"DB2 Software Ltd"}),s.jsx("p",{children:"38 Fawkner Way"}),s.jsx("p",{children:"Stanford In The Vale"}),s.jsx("p",{children:"Faringdon"}),s.jsx("p",{children:"United Kingdom"}),s.jsx("p",{children:"SN7 8FF"})]})]}),(0,s.jsxs)("div",{children:[s.jsx("h2",{className:"text-lg font-semibold mb-3",children:"To:"}),s.jsx("div",{className:"text-sm text-gray-600 h-24 border border-dashed border-gray-300 rounded-md p-3",children:s.jsx("span",{children:e.data})})]})]}),s.jsx("div",{className:"mb-8",children:(0,s.jsxs)("table",{className:"w-full",children:[s.jsx("thead",{children:(0,s.jsxs)("tr",{className:"border-b border-gray-200",children:[s.jsx("th",{className:"py-3 text-left font-semibold text-gray-700",children:"Description"}),s.jsx("th",{className:"py-3 text-center font-semibold text-gray-700",children:"Qty"}),s.jsx("th",{className:"py-3 text-right font-semibold text-gray-700",children:"Unit price"}),s.jsx("th",{className:"py-3 text-right font-semibold text-gray-700",children:"Amount"})]})}),s.jsx("tbody",{children:(0,s.jsxs)("tr",{className:"border-b border-gray-200",children:[s.jsx("td",{className:"py-4 text-sm text-gray-600",children:"Nextjs code boilerplate"}),s.jsx("td",{className:"py-4 text-center text-sm text-gray-600",children:"1"}),s.jsx("td",{className:"py-4 text-right text-sm text-gray-600",children:e.price}),s.jsx("td",{className:"py-4 text-right text-sm text-gray-600",children:e.price})]})})]})}),s.jsx("div",{className:"border-t border-gray-200 pt-4",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm text-gray-600",children:[s.jsx("span",{children:"Subtotal:"}),s.jsx("span",{})]}),(0,s.jsxs)("div",{className:"flex justify-between text-sm font-medium text-gray-800",children:[s.jsx("span",{children:"Total:"}),s.jsx("span",{})]}),(0,s.jsxs)("div",{className:"flex justify-between text-sm font-medium text-green-600",children:[s.jsx("span",{children:"Total amount paid:"}),s.jsx("span",{})]})]})})]});var i=r(52801),a=r(15904),o=r(70168),l=r(45128),u=r(35565),c=r(15087),d=r(56777),h=r(86901);let m={body:{backgroundColor:"#f3f4f6",fontFamily:"Arial, sans-serif"},container:{margin:"0 auto",padding:"20px 0 48px",width:"580px"},section:{backgroundColor:"#ffffff",borderRadius:"5px",padding:"40px"},h1:{color:"#3b82f6",fontSize:"32px",fontWeight:"bold",textAlign:"center",margin:"0 0 20px"},text:{color:"#374151",fontSize:"16px",lineHeight:"24px",textAlign:"center"},button:{backgroundColor:"#3b82f6",borderRadius:"5px",color:"#ffffff",display:"inline-block",fontSize:"16px",fontWeight:"bold",padding:"12px 24px",textDecoration:"none",textAlign:"center",marginTop:"32px"}},p=({email:e})=>(0,s.jsxs)(i.V,{children:[s.jsx(a.F,{}),s.jsx(o.M,{children:"Welcome to our community!"}),s.jsx(l.u,{style:m.body,children:s.jsx(u.W,{style:m.container,children:(0,s.jsxs)(c.$,{style:m.section,children:[s.jsx(d.x,{style:m.h1,children:"Welcome aboard!"}),(0,s.jsxs)(d.x,{style:m.text,children:["We're thrilled to have you join us, ",e,"!"]}),s.jsx(d.x,{style:m.text,children:"Get ready for an amazing journey. We can't wait to see what you'll achieve with us."}),s.jsx(c.$,{style:{textAlign:"center"},children:s.jsx(h.r,{href:"http://localhost:3000/dashboard",style:m.button,children:"Get Started"})})]})})})]});var f=r(1542),v=r(22247),P=r(2723);class y{async sendThanksYouEmail(e){let{data:t,error:r}=await this.resend.emails.send({from:f.Z.resend.fromAdmin,to:[e],replyTo:f.Z.resend.forwardRepliesTo,subject:f.Z.resend.subjects.thankYou,react:p({email:e})});if(r)throw r;return t}async sendInvoice(e,t){let{data:r,error:s}=await this.resend.emails.send({from:f.Z.resend.fromAdmin,to:[e],replyTo:f.Z.resend.forwardRepliesTo,subject:"Invoice: "+t.id,react:n(t)});if(s)throw s;return r}async addNewEmailAddress(e){let t=await this.upsertAudience();return this.resend.contacts.create({email:e,unsubscribed:!1,audienceId:t.resend_id})}async upsertAudience(){let e=await v.Z.audiences.findFirst();if(e)return e;let{data:{id:t,name:r}}=await this.resend.audiences.create({name:"Waiting List"});return v.Z.audiences.create({data:{resend_id:t,name:r}})}constructor(){this.resend=new P.R(process.env.RESEND_API_KEY)}}let g=new y}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[948,70,251,684,777,174,746],()=>r(27596));module.exports=s})();