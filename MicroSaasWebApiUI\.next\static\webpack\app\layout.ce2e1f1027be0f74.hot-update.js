"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/assets/styles/globals.scss":
/*!****************************************!*\
  !*** ./src/assets/styles/globals.scss ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"2f8fd9166745\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hc3NldHMvc3R5bGVzL2dsb2JhbHMuc2NzcyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2Fzc2V0cy9zdHlsZXMvZ2xvYmFscy5zY3NzPzU2YjIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIyZjhmZDkxNjY3NDVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/assets/styles/globals.scss\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/logo.tsx":
/*!*********************************!*\
  !*** ./src/components/logo.tsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _assets_images_logo_svg__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/assets/images/logo.svg */ \"(app-pages-browser)/./src/assets/images/logo.svg\");\n\n\n\nconst Logo = (param)=>{\n    let { isLarge = false } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-1\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                src: _assets_images_logo_svg__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n                width: 24,\n                height: 24,\n                layout: \"lazy\",\n                alt: \"logo\",\n                className: \"\".concat(isLarge ? \"hidden\" : \"\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\MicroSaasWebApiRoot\\\\MicroSaasWebApiUI\\\\src\\\\components\\\\logo.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-1 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-black1 dark:text-white \".concat(isLarge ? \"text-[20px] sm:text-[32px] leading-[20px] sm:leading-[32px] font-bold\" : \"text-[20px] leading-[20px] font-semibold\"),\n                        children: \"MicroSaas\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\MicroSaasWebApiRoot\\\\MicroSaasWebApiUI\\\\src\\\\components\\\\logo.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white dark:text-black1 bg-blue-600 dark:bg-blue-500 font-semibold px-2 rounded-[3px] pb-[3px] pt-[2px] \".concat(isLarge ? \"text-[20px] sm:text-[32px] leading-[20px] sm:leading-[32px]\" : \"text-[20px] leading-[20px]\"),\n                        children: \"API\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\MicroSaasWebApiRoot\\\\MicroSaasWebApiUI\\\\src\\\\components\\\\logo.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\MicroSaasWebApiRoot\\\\MicroSaasWebApiUI\\\\src\\\\components\\\\logo.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\MicroSaasWebApiRoot\\\\MicroSaasWebApiUI\\\\src\\\\components\\\\logo.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Logo;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Logo);\nvar _c;\n$RefreshReg$(_c, \"Logo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/logo.tsx\n"));

/***/ })

});