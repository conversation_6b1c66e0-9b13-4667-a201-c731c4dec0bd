using FakeItEasy;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using MicroSaasWebApi.Controllers.PXW;
using MicroSaasWebApi.Models.PXW.AppSettings;
using MicroSaasWebApi.Properties.PXW;
using MicroSaasWebApi.Services.PXW.Interface;
using System.Net;

namespace MicroSaasWebApi.Tests.Unit.Controller
{
    public class PermissionApiControllerTests
    {
        private PermissionApiController _permissionApiController;
        private readonly IServiceProvider _serviceProvider = A.Fake<IServiceProvider>();
        private readonly IBaseService _baseService = A.Fake<IBaseService>();
        private readonly ILoggerService _loggerService = A.Fake<ILoggerService>();
        private readonly IExternalApiService _externalApiService = A.Fake<IExternalApiService>();

        private readonly IServiceScopeFactory _serviceScopeFactory = A.Fake<IServiceScopeFactory>();
        private readonly IServiceScope _serviceScope = A.Fake<IServiceScope>();

        const string fakeAuthHeader = "Bearer someValidToken";
        const string fakeUrl = "http://fakedataApiUrl.com";
        const string jwtToken = "eyJhbGciOiJSUzI1NiIsImtpZCI6Im1fZmN2QU9XNHZoWXdJTnRYYW0zbFNtamRJdkRQT004bER2aHNTNWdkY3MiLCJ0eXAiOiJKV1QifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YSBt7aLlhCdSbDkb8d5XzPlIgu1owNU--UKxI0mAKKr1RoWsQ52J2Na4knfDiAWzXegHMMl-CvyUIbg7o5Tyq2qPzBcVRA4hfpTuQVwxEofc2-3mJX0sjsu47dwwYKAMGGj8mkHFwZZ_gUr35wtWMbXI-6fii_V52HTbu7ukiQSRXVXl0zkeC9jUt7MFkhVvm6NRei-vNeVK1eX7i05kaT5PVq8IjovC2zlcX69QrzcxJEkPgtMO1qfbDJBVJPl1-YPcwolD6wStdS0rzYWJam1rM0jeR-vOTBXDXf46P7I_SvkKYKE8LBfiCY6Ns-MKlHa2WTRI6w0Z2p26Nkc81w";
        const string responseBody = "{ \"success\": true }";

        public PermissionApiControllerTests()
        {
            A.CallTo(() => _serviceProvider.GetService(typeof(IServiceScopeFactory))).Returns(_serviceScopeFactory);
            A.CallTo(() => _serviceScopeFactory.CreateScope()).Returns(_serviceScope);

            // Use GetRequiredService to match the actual calls in your constructor
            A.CallTo(() => _serviceScope.ServiceProvider.GetService(typeof(ILoggerService))).Returns(_loggerService);
            A.CallTo(() => _serviceScope.ServiceProvider.GetService(typeof(IExternalApiService))).Returns(_externalApiService);

            _permissionApiController = new PermissionApiController(_baseService, _loggerService);
            _permissionApiController.ControllerContext = new ControllerContext()
            {
                HttpContext = new DefaultHttpContext(),
            };
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task ListUsers_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.ListUsers();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task GetUser_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.GetUser();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task CreateUser_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.CreateUser();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task DeleteUser_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.DeleteUser();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task SendInviteUser_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.SendInviteUser();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task CurrentUser_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.CurrentUser();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task ListUserProfiles_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.ListUserProfiles();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task AssignUserProfile_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.AssignUserProfile();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task ListUserRoles_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.ListUserRoles();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task ListProfiles_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.ListProfiles();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task GetProfile_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.GetProfile();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task CreateProfile_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.CreateProfile();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task UpdateProfile_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.UpdateProfile();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task DeleteProfile_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.DeleteProfile();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task ListProfileAccounts_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.ListProfileAccounts();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task AssignProfileAccount_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.AssignProfileAccount();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task ListAccounts_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.ListAccounts();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task GetAccount_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.GetAccount();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task CreateAccount_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.CreateAccount();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task DeleteAccount_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.DeleteAccount();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task ListRoles_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.ListRoles();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task GetRole_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.GetRole();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task CreateRole_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.CreateRole();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task UpdateRole_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.UpdateRole();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task DeleteRole_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.DeleteRole();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task ListProfileGroups_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.ListProfileGroups();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task CreateProfileGroup_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.CreateProfileGroup();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task GetProfileGroup_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.GetProfileGroup();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task PartiallyUpdateProfileGroup_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.PartiallyUpdateProfileGroup();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task DeleteProfileGroup_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.DeleteProfileGroup();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task AssignProfilesToProfileGroup_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.AssignProfilesToProfileGroup();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task UnassignProfilesToProfileGroup_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.UnassignProfilesToProfileGroup();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task ListUserProfileGroups_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.ListUserProfileGroups();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task AssignProfileGroupToUser_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.AssignProfileGroupToUser();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "PermissionAPI")]
        public async Task UnassignProfileGroupToUser_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioPermissionApi = new PortfolioPermissionApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, "SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _permissionApiController.UnassignProfileGroupToUser();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
    }
}
