"use client";
import Image from "next/image";
import { Logo } from "@/components";
import Link from "next/link";
import NavLinks from "@/components/nav-links";
import { Icons } from "@/components/icons";
import footerBg from "@/assets/images/footer-bg1.svg";
import footerBgDark from "@/assets/images/footer-bg2.svg";

const nav_links2 = [
  {
    title: "Terms of services",
    link: "https://elegant-nightshade-79e.notion.site/Terms-Services-4335df481a50486ca26c81e699a2fbf1",
  },
  {
    title: "Privacy Policy",
    link: "https://elegant-nightshade-79e.notion.site/Privacy-policy-f2a3142bfb8c4b16b80369c19263c23a",
  },
  {
    title: "Licences",
    link: "#",
  },
];

const socialLinks = [
  {
    name: "LinkedIn",
    link: "https://linkedin.com/in/javianpicardo",
    icon: Icons.linkedin,
  },
  {
    name: "YouTube",
    link: "https://www.youtube.com/@javianpicardo",
    icon: Icons.youtube,
  },
  {
    name: "X",
    link: "https://x.com/javian_picardo",
    icon: Icons.x,
  },
];

const Footer = () => {
  return (
    <div className="relative flex justify-center items-center w-full">
      <Image
        src={footerBg}
        alt="background"
        fill
        objectFit="cover"
        className="z-0 block dark:hidden"
      />
      <Image
        src={footerBgDark}
        alt="background"
        fill
        objectFit="cover"
        className="z-0 hidden dark:block"
      />
      <div className="relative z-10 max-w-[1440px] w-full h-full px-4 sm:px-12 py-12">
        <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 h-full gap-x-4 gap-y-12">
          <div className="flex flex-col !min-h-[inherit] justify-between gap-8">
            <Link href="/">
              <Logo />
            </Link>
          </div>
          <div className="sm:order-3 xl:order-2">
            <p className="uppercase text-[#86898E] dark:text-[#808389] font-semibold text-base mb-8">
              Legal
            </p>
            <NavLinks nav_links={nav_links2} isFooter={true} />
          </div>
          <div className="sm:order-2 xl:order-last">
            <p className="uppercase text-[#86898E] dark:text-[#808389] font-semibold text-base mb-8">
              Connect With Me
            </p>
            <div className="flex items-center gap-4">
              {socialLinks.map((item) => (
                <Link
                  key={item.name}
                  href={item.link}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <div className="shadow-lg bg-white h-[32px] w-[32px] flex items-center justify-center rounded-[8px] hover:bg-gray-100 transition-colors">
                    <item.icon className="h-5 w-5 text-zinc-600" />
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Footer;
