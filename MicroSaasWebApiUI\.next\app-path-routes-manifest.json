{"/_not-found/page": "/_not-found", "/chat/[projectID]/page": "/chat/[projectID]", "/page": "/", "/dashboard/page": "/dashboard", "/apple-icon.png/route": "/apple-icon.png", "/processing-page/[[...processing-page]]/page": "/processing-page/[[...processing-page]]", "/sign-up/[[...sign-up]]/page": "/sign-up/[[...sign-up]]", "/icon.png/route": "/icon.png", "/test-email/page": "/test-email", "/favicon.ico/route": "/favicon.ico", "/opengraph-image.png/route": "/opengraph-image.png", "/sign-in/[[...sign-in]]/page": "/sign-in/[[...sign-in]]", "/success/page": "/success", "/sitemap.xml/route": "/sitemap.xml", "/twitter-image.png/route": "/twitter-image.png", "/ad-demo/page": "/ad-demo", "/api/webhook/resend/route": "/api/webhook/resend", "/api/waiting-list/route": "/api/waiting-list", "/privacy-policy/page": "/privacy-policy", "/cv/route": "/cv", "/tos/page": "/tos", "/security-policy/page": "/security-policy", "/blog/[articleId]/page": "/blog/[articleId]", "/blog/page": "/blog", "/waiting-list/page": "/waiting-list", "/api/(make)/active/route": "/api/active", "/api/(make)/link/route": "/api/link", "/api/(n8n)/workflows/openAIAssistant/route": "/api/workflows/openAIAssistant", "/api/projects/route": "/api/projects", "/api/stripe/create-checkout/route": "/api/stripe/create-checkout", "/api/(make)/scenarios/route": "/api/scenarios", "/api/(make)/scenarios/openAIAssistant/route": "/api/scenarios/openAIAssistant", "/api/stripe/create-portal/route": "/api/stripe/create-portal", "/api/test-email/route": "/api/test-email", "/api/webhook/stripe/route": "/api/webhook/stripe"}