(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[207],{96505:function(e,t,s){Promise.resolve().then(s.bind(s,94362))},94362:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return r}});var a=s(57437),n=s(2265),l=s(69064);function r(){let[e,t]=(0,n.useState)(""),[s,r]=(0,n.useState)(!1),[i,o]=(0,n.useState)(null),c=async t=>{t.preventDefault(),r(!0);try{let t=await fetch("/api/test-email",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e})}),s=await t.json();t.ok?l.Am.success("Email sent successfully!"):l.Am.error(s.error||"Failed to send email"),o(s)}catch(e){l.Am.error("An error occurred"),console.error(e)}finally{r(!1)}};return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-12 max-w-md",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Test Resend Email"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"This is a public page to test the Resend email <NAME_EMAIL>. No authentication required."}),(0,a.jsxs)("form",{onSubmit:c,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium mb-1",children:"Email Address"}),(0,a.jsx)("input",{type:"email",id:"email",value:e,onChange:e=>t(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter recipient email",required:!0})]}),(0,a.jsx)("button",{type:"submit",disabled:s,className:"w-full py-2 px-4 rounded-md bg-blue-600 text-white font-medium ".concat(s?"opacity-70 cursor-not-allowed":"hover:bg-blue-700"),children:s?"Sending...":"Send Test Email"})]}),i&&(0,a.jsxs)("div",{className:"mt-6 p-4 bg-gray-100 rounded-md",children:[(0,a.jsx)("h2",{className:"text-lg font-medium mb-2",children:"Result:"}),(0,a.jsx)("pre",{className:"text-sm overflow-auto p-2 bg-gray-200 rounded",children:JSON.stringify(i,null,2)})]})]})}}},function(e){e.O(0,[64,971,117,744],function(){return e(e.s=96505)}),_N_E=e.O()}]);