﻿using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.IdentityModel.Logging;
using Microsoft.OpenApi.Models;
using MicroSaasWebApi.Filters;
using MicroSaasWebApi.Health;
using MicroSaasWebApi.Middlewares;
using MicroSaasWebApi.Models.PXW.AppSettings;
using MicroSaasWebApi.Services.Auth.PXW;
using MicroSaasWebApi.Services.Auth.PXW.Interface;
using MicroSaasWebApi.Services.PXW;
using MicroSaasWebApi.Services.PXW.Interface;
using System.IO.Compression;
using System.Reflection;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Diagnostics;
using MicroSaasWebApi.Models;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using System.Security.Authentication;
using System.Net.Security;
using Swashbuckle.AspNetCore.SwaggerUI;
using Azure.Identity;
using Microsoft.Extensions.Configuration.AzureAppConfiguration;
using Azure.Security.KeyVault.Secrets;

var builder = WebApplication.CreateBuilder(args);

var certPath = builder.Configuration["CertPath"];
var certKey = builder.Configuration["CertificateKey"];
var targetUrl = builder.Configuration["TargetUrl"];

var azConfigEndpoint = Environment.GetEnvironmentVariable("APPCONFIGURATION_ENDPOINT");
var azConfigConnectionString = Environment.GetEnvironmentVariable("APPCONFIGURATION_CONNECTIONSTRING");
var azConfigEnvironmentLabel = Environment.GetEnvironmentVariable("APPCONFIGURATION_ENVIRONMENT_LABEL");
var kvUri = Environment.GetEnvironmentVariable("KEYVAULT_URI");
var tenantId = Environment.GetEnvironmentVariable("AZURE_TENANT_ID");
var clientId = Environment.GetEnvironmentVariable("AZURE_CLIENT_ID");
var clientSecret = Environment.GetEnvironmentVariable("AZURE_CLIENT_SECRET");

if (string.IsNullOrEmpty(kvUri) && string.IsNullOrEmpty(tenantId) && string.IsNullOrEmpty(clientId) && string.IsNullOrEmpty(clientSecret))
{
    Console.WriteLine("One or more environment variables retrieved for Key Vault are null or empty.");
    //throw new InvalidOperationException("One or more environment variables retrieved for Key Vault are null or empty.");
}

// Configure Services
ConfigureServices(builder.Services, builder.Configuration, builder.Environment);

// Configure Kestrel 
ConfigureKestrel(builder.WebHost);

var app = builder.Build();

// Configure middleware
ConfigureMiddleware(app);

app.Run();

void ConfigureServices(IServiceCollection services, IConfiguration configuration, IWebHostEnvironment env)
{
    //Configure AzureAppConfiguration & KeyVault
    ConfigureAzureAppConfigurationAndKeyVault();

    // Http Client, Controllers, Health Checks, HttpContextAccessor, etc.
    //Enforce TLS 1.2 1.1
    services.AddHttpClient("MyClient", client =>
    {
        client.Timeout = TimeSpan.FromMinutes(1);
        //client.BaseAddress = new Uri(targetUrl);
    })
    .ConfigurePrimaryHttpMessageHandler(() =>
    {
        var httpClientHandler = new HttpClientHandler
        {
            SslProtocols = SslProtocols.Tls12 | SslProtocols.Tls13,
        };
        // Configure the custom server certificate validation
        httpClientHandler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) =>
        {
            if (errors == SslPolicyErrors.None)
            {
                return true; // No errors, certificate is valid
            }

            if (errors.HasFlag(SslPolicyErrors.RemoteCertificateNameMismatch))
            {
                // Here you can implement specific logic to handle name mismatches.
                // For example, check if the mismatch is expected and return true if it's acceptable.
                // Caution: Returning true here will bypass the name mismatch error.

                // Example: return true if you are in a development environment
                //return IsDevelopmentEnvironment(); // Replace with your own condition
                //return true;
            }

            return false; // Reject other issues or if conditions above are not met
        };
        // Uncomment if you need to add a client certificate
        // httpClientHandler.ClientCertificates.Add(new X509Certificate2(certPath, certKey));
        return httpClientHandler;
    });

    services.AddControllers().AddNewtonsoftJson(options =>
        options.SerializerSettings.ContractResolver = new Newtonsoft.Json.Serialization.DefaultContractResolver());

    services.ConfigureApplicationCookie(options =>
    {
        options.Cookie.HttpOnly = true; // Set HttpOnly to true for security
        options.Cookie.SecurePolicy = CookieSecurePolicy.Always; // Ensure cookies are always sent over HTTPS
        options.Cookie.SameSite = SameSiteMode.Strict; // Set SameSite attribute
        // Set the domain if your API and web application are on different domains
        // options.Cookie.Domain = ".yourdomain.com";
    });

    //Add Health Checks
    AddHealthChecks(services);

    // Add the IHttpContextAccessor interface
    services.AddHttpContextAccessor();
    AddResponseCompression(services);
    // Register manual services
    RegisterManualServices(services);
    // Logging, Tenants, Configurations, Endpoints, Directory Browser, CORS
    services.AddLogging();

    RegisterAppSettings(services, configuration);
    services.AddDirectoryBrowser();
    AddCorsPolicy(services);

    // Swagger/OpenAPI    
    // Configure Swagger only for non-production environments
    if (!env.IsProduction())
    {
        AddSwaggerGen(services);
    }
}

void ConfigureAzureAppConfigurationAndKeyVault()
{
    // Set up configuration to use Azure App Configuration and Azure Key Vault.

    if (!string.IsNullOrEmpty(azConfigEndpoint) && !string.IsNullOrEmpty(azConfigEnvironmentLabel) &&
    !string.IsNullOrEmpty(kvUri))
    {
        Console.WriteLine("Using Azure App Configuration with Managed Identity");
        try
        {
            builder.Configuration.AddAzureAppConfiguration(options =>
            {
                options.Connect(
                    new Uri(azConfigEndpoint),
                    new ManagedIdentityCredential())
                .ConfigureRefresh(refresh =>
                {
                    refresh.Register("Settings:Sentinel", refreshAll: true)
                           .SetCacheExpiration(new TimeSpan(0, 5, 0));
                })
                .ConfigureKeyVault(kv =>
                {
                    kv.SetCredential(new DefaultAzureCredential());
                })
                .Select(KeyFilter.Any, azConfigEnvironmentLabel);
            });
            // Log success message
            Console.WriteLine("Connected to Azure App Configuration successfully!");
        }
        catch (Exception ex)
        {
            // Log error message
            Console.WriteLine($"Error connecting to Azure App Configuration: {ex.Message}");
        }

    }
    else if (!string.IsNullOrEmpty(azConfigConnectionString) && !string.IsNullOrEmpty(azConfigEnvironmentLabel) &&
    !string.IsNullOrEmpty(kvUri))
    {
        Console.WriteLine("Using Azure App Configuration with Connection String");
        try
        {
            builder.Configuration.AddAzureAppConfiguration(options =>
            {
                options.Connect(azConfigConnectionString)
                    .ConfigureRefresh(refresh =>
                    {
                        refresh.Register("Settings:Sentinel", refreshAll: true)
                               .SetCacheExpiration(new TimeSpan(0, 5, 0));
                    })
                    .ConfigureKeyVault(kv => { kv.SetCredential(new DefaultAzureCredential()); })
                    .Select(KeyFilter.Any, azConfigEnvironmentLabel);
            });
            // Log success message
            Console.WriteLine("Connected to Azure App Configuration successfully!");
        }
        catch (Exception ex)
        {
            // Log error message
            Console.WriteLine($"Error connecting to Azure App Configuration: {ex.Message}");
        }
    }

    if (string.IsNullOrEmpty(azConfigEndpoint) && string.IsNullOrEmpty(azConfigConnectionString) &&
        !string.IsNullOrEmpty(kvUri) && !string.IsNullOrEmpty(tenantId) && !string.IsNullOrEmpty(clientId) && !string.IsNullOrEmpty(clientSecret))
    {
        try
        {
            // write code to connect and fetch all values from the Key vault.
            Console.WriteLine("Fetching values directly from Key Vault");

            // Add Azure Key Vault configuration
            builder.Configuration.AddAzureKeyVault(new Uri(kvUri), new ClientSecretCredential(tenantId, clientId, clientSecret));
            // Log success message
            Console.WriteLine("Connected to Azure App Configuration successfully!");
        }
        catch (Exception ex)
        {
            // Log error message
            Console.WriteLine($"Error connecting to Azure App Configuration: {ex.Message}");
            //throw new InvalidOperationException($"Error connecting to Azure App Configuration: {ex.Message}");            
        }
    }

}

void AddHealthChecks(IServiceCollection services)
{
    services.AddHealthChecks()
        .AddCheck<CustomHealthCheck>(nameof(CustomHealthCheck));
    //.AddCheck<DataApiHealthCheck>(nameof(DataApiHealthCheck))
    //.AddCheck<AlphaCCOApiHealthCheck>(nameof(AlphaCCOApiHealthCheck));

    services.AddHealthChecksUI(options =>
    {
        options.AddHealthCheckEndpoint("Healthcheck API", "/health");
        options.SetEvaluationTimeInSeconds(900);
    }).AddInMemoryStorage();
}

void AddResponseCompression(IServiceCollection services)
{
    services.AddResponseCompression(options =>
    {
        options.EnableForHttps = true;
        options.Providers.Add<BrotliCompressionProvider>();
        options.Providers.Add<GzipCompressionProvider>();
    });

    services.Configure<BrotliCompressionProviderOptions>(options =>
    {
        options.Level = CompressionLevel.Fastest;
    });

    services.Configure<GzipCompressionProviderOptions>(options =>
    {
        options.Level = CompressionLevel.SmallestSize;
    });
}
void RegisterManualServices(IServiceCollection services)
{
    services.AddScoped<IJwtValidationService, JwtValidationService>();
    services.AddScoped<IBaseService, BaseService>();
    services.AddScoped<IExternalApiService, ExternalApiService>(); // Uncomment if needed
    services.AddScoped<IApplicationSettingsService, ApplicationSettingsService>();
    services.AddScoped<ILoggerService, LoggerService>();
    services.AddSingleton<IActionContextAccessor, ActionContextAccessor>();
    services.AddScoped<ITenantProviderService, TenantProviderService>();
}
void RegisterAppSettings(IServiceCollection services, IConfiguration configuration)
{
    services.Configure<List<Tenants>>(configuration.GetSection(MicroSaasWebApi.Properties.PXW.Constants.Tenants));
    services.Configure<Configurations>(configuration.GetSection(MicroSaasWebApi.Properties.PXW.Constants.Configurations));
    services.Configure<Endpoints>(configuration.GetSection(MicroSaasWebApi.Properties.PXW.Constants.Endpoints));
}

void ConfigureKestrel(IWebHostBuilder webHostBuilder)
{
    webHostBuilder.ConfigureKestrel(serverOptions =>
    {
        serverOptions.Limits.MaxRequestBodySize = 100_000_000; // 100 MB
        //serverOptions.Limits.RequestTimeout = TimeSpan.FromMinutes(2);
        serverOptions.Limits.MaxConcurrentConnections = 100;
        serverOptions.Limits.MaxConcurrentUpgradedConnections = 100;
        //serverOptions.Listen(IPAddress.Loopback, 5000); // Listen on port 5000
        //serverOptions.Listen(IPAddress.Any, 5001, listenOptions =>
        //{
        //    listenOptions.UseHttps("certificate.pfx", "password");
        //});
        serverOptions.Limits.KeepAliveTimeout = TimeSpan.FromSeconds(130);
        //serverOptions.Limits.MaxRequestHeaderCount = 20;
        //serverOptions.Limits.MaxRequestHeadersTotalSize = 32 * 1024; // 32 KB
        //serverOptions.Limits.MinResponseDataRate = new MinDataRate(bytesPerSecond: 100, gracePeriod: TimeSpan.FromSeconds(10));
        //serverOptions.Limits.MaxResponseBufferSize = 64 * 1024; // 64 KB
        //serverOptions.ConfigureHttpsDefaults(httpsOptions =>
        //{
        //    httpsOptions.ClientCertificateMode = ClientCertificateMode.RequireCertificate;
        //});
        //serverOptions.ConfigureEndpointDefaults(endpointOptions =>
        //{
        //    endpointOptions.Protocols = HttpProtocols.Http1AndHttp2;
        //});
    });
}

void AddCorsPolicy(IServiceCollection services)
{
    services.AddCors(options =>
    {
        options.AddPolicy("AllowAll", builder =>
        {
            builder//.AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader();
        });
    });
}

void AddSwaggerGen(IServiceCollection services)
{
    services.AddEndpointsApiExplorer();
    services.AddSwaggerGen(c =>
    {
        c.SwaggerDoc("v1", new OpenApiInfo
        {
            Title = "PortXpressway Web Api Documentation",
            Version = "v1",
            Description = "Documentation for PortXpressway Web Api.",
            Contact = new OpenApiContact
            {
                Name = "PortfolioXpressway Support Team",
                Email = "<EMAIL>"
            }
        });

        c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
        {
            In = ParameterLocation.Header,
            Description = "Please enter the Bearer token",
            Name = "Authorization",
            Type = SecuritySchemeType.Http,
            Scheme = "Bearer",
            BearerFormat = "JWT"
        });
        c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type=ReferenceType.SecurityScheme,
                    Id="Bearer"
                }
            },
            Array.Empty<string>()
        }
    });

        c.OperationFilter<AuthorizeCheckOperationFilter>();

        var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
        var xmlPath = Path.Combine(System.AppContext.BaseDirectory, xmlFile);
        c.IncludeXmlComments(xmlPath);
        c.EnableAnnotations();
    });
}

void ConfigureMiddleware(WebApplication app)
{
    // Custom Middleware
    app.UseMiddleware<CustomRedirectMiddleware>();
    app.UseMiddleware<AzureB2CTokenAuthenticationMiddleware>();
    //app.UseMarkdownMiddleware("Documentation");

    // Environment Specific Configuration
    if (app.Environment.IsDevelopment() || app.Environment.IsStaging())
    {
        app.UseDeveloperExceptionPage();
        ConfigureSwaggerUI(app);
    }
    else
    {
        app.UseExceptionHandler(appBuilder =>
        {
            appBuilder.Run(async context =>
            {
                var exceptionHandlerFeature = context.Features.Get<IExceptionHandlerFeature>();
                if (exceptionHandlerFeature != null)
                {
                    var exception = exceptionHandlerFeature.Error;

                    context.Response.StatusCode = StatusCodes.Status500InternalServerError;
                    context.Response.ContentType = "application/json";

                    var errorResponse = new ErrorResponse
                    {
                        Message = "An error occurred while processing your request.",
                        Detail = exception.Message // In production, you might not want to expose detailed error messages
                    };

                    await context.Response.WriteAsJsonAsync(errorResponse);
                }
            });
        });
        // Uncomment if HSTS is required
        //app.UseHsts();
        //app.UseHsts(hsts => hsts.MaxAge(days: 365).IncludeSubdomains());
    }

    // Common Middleware
    app.UseHttpsRedirection();
    app.UseStaticFiles();
    app.UseRouting();
    app.UseCors("AllowAll");
    // Health Checks
    ConfigureHealthChecks(app);

    app.UseDirectoryBrowser();
    app.UseAuthentication();
    app.UseAuthorization();
    app.UseResponseCompression();
    //app.UseEndpoints(endpoints => { endpoints.MapControllers(); });
    app.MapControllers();
    //Security Features
    //adding-http-headers-to-improve-security-in-an-asp-net-mvc-core-application/
    app.UseHsts(hsts => hsts.MaxAge(365).IncludeSubdomains());
    app.UseXContentTypeOptions();
    app.UseReferrerPolicy(opts => opts.NoReferrer());
    app.UseXXssProtection(options => options.EnabledWithBlockMode());
    app.UseXfo(options => options.Deny());
    app.UseCsp(opts => opts
        .BlockAllMixedContent()
        .StyleSources(s => s.Self())
        .StyleSources(s => s.UnsafeInline()) // Be cautious with 'UnsafeInline'
        .FontSources(s => s.Self())
        .FormActions(s => s.Self())
        .FrameAncestors(s => s.Self())
        .ImageSources(s => s.Self())
        .ScriptSources(s => s.Self())
        );
    IdentityModelEventSource.ShowPII = true;
}

void ConfigureSwaggerUI(WebApplication app)
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "PortXpressway Web Api V1");
        c.SupportedSubmitMethods(Array.Empty<SubmitMethod>()); // Disables "Try it out" for all methods

        c.OAuthScopeSeparator(" ");
        c.OAuthUsePkce();
    });
    app.UseReDoc(options =>
    {
        options.DocumentTitle = "PortXpressway Web Api V1 Documentation";
        options.SpecUrl = "/swagger/v1/swagger.json";
    });
}

void ConfigureHealthChecks(WebApplication app)
{
    app.MapHealthChecks("/health", new HealthCheckOptions
    {
        Predicate = _ => true,
        ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse,
        ResultStatusCodes =
        {
            [HealthStatus.Healthy] = StatusCodes.Status200OK,
            [HealthStatus.Degraded] = StatusCodes.Status500InternalServerError,
            [HealthStatus.Unhealthy] = StatusCodes.Status503ServiceUnavailable,
        }
    });
    app.MapHealthChecksUI(options =>
    {
        options.AsideMenuOpened = false;
        options.ApiPath = "/health-api"; // API path for UI to poll
        options.UIPath = "/dashboard";
    });
}