(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[436],{2436:(e,t,r)=>{"use strict";r.d(t,{I:()=>S});var n=r(6449),a=r(6947),l=r(513),o=r(6068),i=r(2829),u=r(5036),s=r(9102);function d(e){let t=JSON.stringify(e),r=new TextEncoder().encode(t);return u.tH.stringify(r,{pad:!1})}async function c(e,t,r){if(!r.algorithm)throw Error("No algorithm specified");let n=new TextEncoder,a=(0,u.DO)(r.algorithm);if(!a)return{errors:[new s.fi(`Unsupported algorithm ${r.algorithm}`)]};let l=await (0,u.uI)(t,a,"sign"),o=r.header||{typ:"JWT"};o.alg=r.algorithm,e.iat=Math.floor(Date.now()/1e3);let i=d(o),c=d(e),g=`${i}.${c}`;try{let e=await u.ZE.crypto.subtle.sign(a,l,n.encode(g));return{data:`${g}.${u.tH.stringify(new Uint8Array(e),{pad:!1})}`}}catch(e){return{errors:[new s.fi(e?.message)]}}}(0,i.A)(u.$v);var g=(0,i.t)(u.t5);(0,i.A)(c),(0,i.A)(u.N7);let h={i8:"14.2.24"},p=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch(e){return""}},f=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?p(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,p(t)])),null,2)).join(", "),m=(e,t)=>()=>{let r=[],n=!1;return{enable:()=>{n=!0},debug:(...e)=>{n&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(n){for(let n of(console.log(`[clerk debug start: ${e}]`),r)){let e=t(n);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,n=new TextDecoder("utf-8"),a=r.encode(e).slice(0,4096);return n.decode(a).replace(/\uFFFD/g,"")}(e,0)),console.log(e)}console.log(`[clerk debug end: ${e}] (@clerk/nextjs=5.7.5,next=${h.i8})`)}}}},x=(e,t)=>(...r)=>{let n=("string"==typeof e?m(e,f):e)(),a=t(n);try{let e=a(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(n.commit(),e)).catch(e=>{throw n.commit(),e});return n.commit(),e}catch(e){throw n.commit(),e}};var b=r(1302),y=r(8802);let v=({noAuthStatusMessage:e,debugLoggerName:t})=>x(t,t=>(r,a)=>{"true"===(0,y.Pg)(r,n._G.Headers.EnableDebug)&&t.enable();let l=(0,y.to)(r,"AuthToken"),i=(0,y.to)(r,"AuthSignature"),u=(0,y.to)(r,"AuthMessage"),s=(0,y.to)(r,"AuthReason"),d=(0,y.to)(r,"AuthStatus");if(t.debug("Headers debug",{authStatus:d,authMessage:u,authReason:s}),!d)throw Error(e);let c=(0,y.Pg)(r,n._G.Headers.ClerkRequestData),h=(0,y.om)(c),p={authStatus:d,apiUrl:o.T5,apiVersion:o.Gn,authMessage:u,secretKey:(null==a?void 0:a.secretKey)||h.secretKey||o.Cn,authReason:s};if(t.debug("Options debug",p),d===n.tD.SignedIn){(0,y.ii)(l,p.secretKey,i);let e=g(l);return t.debug("JWT debug",e.raw.text),(0,n.B9)(p,e.raw.text,e.payload)}return(0,n.JH)(p)});v({debugLoggerName:"getAuth()",noAuthStatusMessage:(0,b.PL)()});let U={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},w=e=>{let{redirectToSignIn:t,authObject:r,redirect:n,notFound:a,request:l}=e;return(...e)=>{var o,i,u,s,d,c;let g=(null==(o=e[0])?void 0:o.unauthenticatedUrl)||(null==(i=e[0])?void 0:i.unauthorizedUrl)?void 0:e[0],h=(null==(u=e[0])?void 0:u.unauthenticatedUrl)||(null==(s=e[1])?void 0:s.unauthenticatedUrl),p=(null==(d=e[0])?void 0:d.unauthorizedUrl)||(null==(c=e[1])?void 0:c.unauthorizedUrl),f=()=>p?n(p):a();return r.userId?g?"function"==typeof g?g(r.has)?r:f():r.has(g)?r:f():r:h?n(h):A(l)?t():a()}},_=e=>{var t,r;return!!e.headers.get(U.Headers.NextUrl)&&((null==(t=e.headers.get(n._G.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(n._G.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(U.Headers.NextAction))},A=e=>{var t;return"document"===e.headers.get(n._G.Headers.SecFetchDest)||"iframe"===e.headers.get(n._G.Headers.SecFetchDest)||(null==(t=e.headers.get(n._G.Headers.Accept))?void 0:t.includes("text/html"))||k(e)||H(e)},k=e=>!!e.headers.get(U.Headers.NextUrl)&&!_(e)||N(),N=()=>{var e;let t=globalThis.fetch;return!!(function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(t)&&(null==(e=t.__nextGetStaticStore().getStore())?void 0:e.pagePath))},H=e=>!!e.headers.get(U.Headers.NextjsData);var D=r(2690);let S=()=>{r(7185);let e=(0,D.vX)(),t=v({debugLoggerName:"auth()",noAuthStatusMessage:(0,b.It)()})(e),i=(0,y.to)(e,"ClerkUrl"),u=(t={})=>{let r=(0,n.WL)(e),l=r.clerkUrl.searchParams.get(n._G.QueryParameters.DevBrowser)||r.cookies.get(n._G.Cookies.DevBrowser),u=(0,y.Pg)(e,n._G.Headers.ClerkRequestData),s=(0,y.om)(u);return(0,n.bI)({redirectAdapter:a.uX,devBrowserToken:l,baseUrl:r.clerkUrl.toString(),publishableKey:s.publishableKey||o.Am,signInUrl:s.signInUrl||o.O0,signUpUrl:s.signUpUrl||o.Dt}).redirectToSignIn({returnBackUrl:null===t.returnBackUrl?"":t.returnBackUrl||(null==i?void 0:i.toString())})},s=w({request:e,authObject:t,redirectToSignIn:u,notFound:l.v,redirect:a.uX});return Object.assign(t,{protect:s,redirectToSignIn:u})}},7185:()=>{}}]);
//# sourceMappingURL=436.js.map