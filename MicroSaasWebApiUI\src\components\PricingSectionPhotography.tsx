'use client'

import { Check } from 'lucide-react'
import CheckoutButton from './CheckoutButton'

export default function PricingSectionPhotography() {
	return (
		<section className='py-16'>
			<div className='container mx-auto px-4'>
				<h2 className='text-3xl font-bold text-center mb-12 dark:text-white text-black1'>
					Photography Packages
				</h2>
				<div className='grid md:grid-cols-2 gap-8 max-w-4xl mx-auto'>
					{/* Quick Shoots */}
					<div className='dark:bg-[#131211] bg-white rounded-lg shadow-md p-8'>
						<h3 className='text-2xl font-semibold mb-4'>Quick Shoots</h3>
						<p className='text-4xl font-bold mb-6'>
							$300
							<span className='text-xl dark:text-gray-500 text-black1/70 font-normal'>
								/session
							</span>
						</p>
						<ul className='space-y-3 mb-8'>
							<li className='flex items-center'>
								<Check className='text-green-500 mr-2' />
								<span>One hour of footage time</span>
							</li>
							<li className='flex items-center'>
								<Check className='text-green-500 mr-2' />
								<span>50 edited photos</span>
							</li>
							<li className='flex items-center'>
								<Check className='text-green-500 mr-2' />
								<span>Help with posing</span>
							</li>
							<li className='flex items-center'>
								<Check className='text-green-500 mr-2' />
								<span>Location list to pick from</span>
							</li>
							<li className='flex items-center'>
								<Check className='text-green-500 mr-2' />
								<span>7 business days turn around time</span>
							</li>
						</ul>

						<CheckoutButton priceId='price_1R6aslP82YH9JfOlJZcweC4d' />
					</div>

					{/* Mini Shoots */}
					<div className='dark:bg-[#131211] bg-white rounded-lg shadow-md p-8 border-4 border-blue-500 relative'>
						<div className='absolute top-0 right-0 bg-blue-500 text-white py-1 px-4 rounded-bl-lg text-sm font-semibold'>
							Most Popular
						</div>
						<h3 className='text-2xl font-semibold mb-4'>Mini Shoots</h3>
						<p className='text-4xl font-bold mb-2'>
							$150
							<span className='text-xl dark:text-gray-500 text-black1/70 font-normal'>
								/session
							</span>
						</p>
						<p className='text-green-600 font-semibold mb-6'>
							Perfect for professional portraits
						</p>
						<ul className='space-y-3 mb-8'>
							<li className='flex items-center'>
								<Check className='text-green-500 mr-2' />
								<span>30 minutes of footage time</span>
							</li>
							<li className='flex items-center'>
								<Check className='text-green-500 mr-2' />
								<span>25 edited photos</span>
							</li>
							<li className='flex items-center'>
								<Check className='text-green-500 mr-2' />
								<span>Help with posing</span>
							</li>
							<li className='flex items-center'>
								<Check className='text-green-500 mr-2' />
								<span>Location list to pick from</span>
							</li>
							<li className='flex items-center'>
								<Check className='text-green-500 mr-2' />
								<span>7 business days turn around time</span>
							</li>
						</ul>

						<CheckoutButton priceId='price_1R6avXP82YH9JfOlIGpAfwjU' />
					</div>
				</div>
			</div>
		</section>
	)
} 