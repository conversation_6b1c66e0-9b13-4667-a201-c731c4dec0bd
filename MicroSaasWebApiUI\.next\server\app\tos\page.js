"use strict";(()=>{var e={};e.id=465,e.ids=[465],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},85807:e=>{e.exports=require("module")},55315:e=>{e.exports=require("path")},17360:e=>{e.exports=require("url")},6005:e=>{e.exports=require("node:crypto")},59630:(e,a,t)=>{t.r(a),t.d(a,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>c,routeModule:()=>h,tree:()=>d}),t(56490),t(8130),t(7629),t(12523);var r=t(23191),s=t(88716),o=t(37922),i=t.n(o),n=t(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(a,l);let d=["",{children:["tos",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,56490)),"C:\\Projects\\PersonalPortal\\src\\app\\tos\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e),async e=>(await Promise.resolve().then(t.bind(t,71150))).default(e)],apple:[async e=>(await Promise.resolve().then(t.bind(t,63601))).default(e)],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,51922))).default(e)],twitter:[async e=>(await Promise.resolve().then(t.bind(t,40680))).default(e)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,8130)),"C:\\Projects\\PersonalPortal\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,7629)),"C:\\Projects\\PersonalPortal\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"C:\\Projects\\PersonalPortal\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e),async e=>(await Promise.resolve().then(t.bind(t,71150))).default(e)],apple:[async e=>(await Promise.resolve().then(t.bind(t,63601))).default(e)],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,51922))).default(e)],twitter:[async e=>(await Promise.resolve().then(t.bind(t,40680))).default(e)],manifest:void 0}}],c=["C:\\Projects\\PersonalPortal\\src\\app\\tos\\page.tsx"],p="/tos/page",u={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/tos/page",pathname:"/tos",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},56490:(e,a,t)=>{t.r(a),t.d(a,{default:()=>l,metadata:()=>n});var r=t(19510),s=t(57371),o=t(7264),i=t(1542);let n=(0,o.A)({title:`Terms and Conditions | ${i.Z.appName}`,canonicalUrlRelative:"/tos"}),l=()=>r.jsx("main",{className:"max-w-xl mx-auto",children:(0,r.jsxs)("div",{className:"p-5",children:[(0,r.jsxs)(s.default,{href:"/",className:"btn btn-ghost text-white",children:[r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"w-5 h-5",children:r.jsx("path",{fillRule:"evenodd",d:"M15 10a.75.75 0 01-.75.75H7.612l2.158 1.96a.75.75 0 11-1.04 1.08l-3.5-3.25a.75.75 0 010-1.08l3.5-3.25a.75.75 0 111.04 1.08L7.612 9.25h6.638A.75.75 0 0115 10z",clipRule:"evenodd"})}),"Back"]}),(0,r.jsxs)("h1",{className:"text-3xl font-extrabold pb-6 text-white",children:["Terms and Conditions for ",i.Z.appName]}),r.jsx("pre",{className:"leading-relaxed whitespace-pre-wrap text-white",style:{fontFamily:"sans-serif"},children:`Last Updated: September 26, 2023

Welcome to MicroSassFast!

These Terms of Service ("Terms") govern your use of the MicroSassFast website at https://micro.st ("Website") and the services provided by MicroSassFast. By using our Website and services, you agree to these Terms.

1. Description of MicroSassFast

MicroSassFast is a platform that offers a JavaScript code boilerplate to assist entrepreneurs in launching their startups more efficiently.

2. Ownership and Usage Rights

When you purchase a package from MicroSassFast, you gain the right to download and use the code provided for creating applications. You own the code you create but do not have the right to resell it. We offer a full refund within 7 days of purchase, as specified in our refund policy.

3. User Data and Privacy

We collect and store user data, including name, email, and payment information, as necessary to provide our services. For details on how we handle your data, please refer to our Privacy Policy at https://micro.st/privacy-policy.

4. Non-Personal Data Collection

We use web cookies to collect non-personal data for the purpose of improving our services and user experience.

5. Governing Law

These Terms are governed by the laws of France.

6. Updates to the Terms

We may update these Terms from time to time. Users will be notified of any changes via email.

For any questions or concerns regarding these Terms of Service, please contact <NAME_EMAIL>.

Thank you for using MicroSassFast!`})]})})}};var a=require("../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[948,70,251,746],()=>t(59630));module.exports=r})();