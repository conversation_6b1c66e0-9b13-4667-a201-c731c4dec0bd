﻿using MicroSaasWebApi.Models.Profile;
using MicroSaasWebApi.Models.PXW.AppSettings;
using MicroSaasWebApi.Services.PXW.Interface;

namespace MicroSaasWebApi.Services.PXW
{
    /// <summary>
    /// Summary description for WorkflowEngine
    /// </summary>
    public class EngineBase
    {
        #region Initialize

        protected int _domainId;

        protected int DomainId
        {
            get
            {
                _domainId = (int)_httpContextAccessor?.HttpContext?.Items["TenantId"];
                return _domainId;
            }
        }

        protected int _languageId;

        protected int LanguageId
        {
            get
            {
                _languageId = (int)_httpContextAccessor?.HttpContext?.Items["Language"];
                return _languageId;
            }
        }

        protected readonly IHttpContextAccessor _httpContextAccessor;

        protected readonly IApplicationSettingsService _applicationSettingsService;
        public AccountMember CurrentSignedInAccountMember
        {
            get
            {
                return _httpContextAccessor.HttpContext.Items["CurrentSignedInAccountMember"] as AccountMember;
            }
            set
            {
                _httpContextAccessor.HttpContext.Items["CurrentSignedInAccountMember"] = value;
            }
        }

        public Tenant? FilteredTenant
        {
            get
            {
                return _httpContextAccessor?.HttpContext?.Items["FilteredTenant"] as Tenant;
            }
            set
            {
                _httpContextAccessor.HttpContext.Items["FilteredTenant"] = _applicationSettingsService.GetFilteredTenant();
            }
        }

        public EngineBase(//HttpContext context, SQL currentSql,
            int domainId, int languageId, IApplicationSettingsService applicationSettingsService, IHttpContextAccessor httpContextAccessor)
        {
            _domainId = domainId;
            _languageId = languageId;
            _applicationSettingsService = applicationSettingsService;
            _httpContextAccessor = httpContextAccessor;
        }
        #endregion Initialize
    }
}
