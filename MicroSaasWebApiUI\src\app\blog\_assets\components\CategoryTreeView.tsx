'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'

interface Category {
  id: number
  name: string
  slug: string
  parent: number
  count: number
}

interface CategoryTreeProps {
  categories: Category[]
  onSelectCategory: (categoryId: number | null) => void
  selectedCategoryId: number | null
}

const CategoryTreeView = ({ categories, onSelectCategory, selectedCategoryId }: CategoryTreeProps) => {
  const [expandedCategories, setExpandedCategories] = useState<Set<number>>(new Set())

  // Create a hierarchical structure of categories
  const getCategoryTree = () => {
    const rootCategories: Category[] = []
    const childCategories: { [key: number]: Category[] } = {}

    // Group categories by parent
    categories.forEach(category => {
      if (category.parent === 0) {
        rootCategories.push(category)
      } else {
        if (!childCategories[category.parent]) {
          childCategories[category.parent] = []
        }
        childCategories[category.parent].push(category)
      }
    })

    // Sort root categories by name
    rootCategories.sort((a, b) => a.name.localeCompare(b.name))

    // Sort child categories by name
    Object.keys(childCategories).forEach(parentId => {
      childCategories[Number(parentId)].sort((a, b) => a.name.localeCompare(b.name))
    })

    return { rootCategories, childCategories }
  }

  const { rootCategories, childCategories } = getCategoryTree()

  const toggleExpand = (categoryId: number) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId)
    } else {
      newExpanded.add(categoryId)
    }
    setExpandedCategories(newExpanded)
  }

  const renderCategory = (category: Category, level = 0) => {
    const hasChildren = childCategories[category.id] && childCategories[category.id].length > 0
    const isExpanded = expandedCategories.has(category.id)
    const isSelected = selectedCategoryId === category.id

    return (
      <div key={category.id} className="category-item">
        <div 
          className={`flex items-center py-1 px-2 rounded cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 ${isSelected ? 'bg-blue-100 dark:bg-blue-900' : ''}`}
          style={{ paddingLeft: `${level * 12 + 8}px` }}
        >
          {hasChildren && (
            <button 
              onClick={() => toggleExpand(category.id)}
              className="mr-1 w-4 h-4 flex items-center justify-center text-gray-500"
            >
              {isExpanded ? '−' : '+'}
            </button>
          )}
          {!hasChildren && <span className="mr-1 w-4 h-4"></span>}
          <span 
            onClick={() => onSelectCategory(category.id)}
            className="flex-grow truncate"
          >
            {category.name} <span className="text-gray-500 text-xs">({category.count})</span>
          </span>
        </div>
        
        {hasChildren && isExpanded && (
          <div className="ml-2">
            {childCategories[category.id].map(child => renderCategory(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="category-tree bg-white dark:bg-[#1E232C] p-4 rounded-xl shadow-lg dark:border border-solid border-[#373C53] mb-6">
      <h3 className="text-lg font-semibold mb-3 dark:text-white">Categories</h3>
      
      <div 
        className={`flex items-center py-1 px-2 rounded cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 mb-2 ${selectedCategoryId === null ? 'bg-blue-100 dark:bg-blue-900' : ''}`}
        onClick={() => onSelectCategory(null)}
      >
        <span className="mr-1 w-4 h-4"></span>
        <span className="flex-grow">All Posts</span>
      </div>
      
      {rootCategories.map(category => renderCategory(category))}
    </div>
  )
}

export default CategoryTreeView
