using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Models.Payment;
using MicroSaasWebApi.Services.MICROSAAS.Interface;
using Swashbuckle.AspNetCore.Annotations;
using System.Security.Claims;

namespace MicroSaasWebApi.Controllers.MICROSAAS
{
    /// <summary>
    /// Payment controller for MicroSaaS template with Clerk integration
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class PaymentController : ControllerBase
    {
        private readonly IPaymentService _paymentService;
        private readonly ILogger<PaymentController> _logger;

        public PaymentController(
            IPaymentService paymentService,
            ILogger<PaymentController> logger)
        {
            _paymentService = paymentService;
            _logger = logger;
        }

        /// <summary>
        /// Get available subscription plans
        /// </summary>
        /// <returns>List of subscription plans</returns>
        [HttpGet("plans")]
        [AllowAnonymous]
        [SwaggerOperation(Summary = "Get subscription plans", Description = "Retrieves all available subscription plans")]
        [ProducesResponseType(typeof(IEnumerable<SubscriptionPlan>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPlans()
        {
            try
            {
                var plans = await _paymentService.GetSubscriptionPlansAsync();
                return Ok(plans);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving subscription plans");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Create a subscription
        /// </summary>
        /// <param name="request">Subscription creation request</param>
        /// <returns>Payment response with subscription details</returns>
        [HttpPost("subscribe")]
        [SwaggerOperation(Summary = "Create subscription", Description = "Creates a new subscription for the current user")]
        [ProducesResponseType(typeof(PaymentResponse), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> CreateSubscription([FromBody] CreateSubscriptionRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var userId = GetCurrentUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found in token");
                }

                var result = await _paymentService.CreateSubscriptionAsync(userId, request);
                
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                return CreatedAtAction(nameof(GetSubscriptionStatus), new { userId }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating subscription for user {UserId}", GetCurrentUserId());
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get current user's subscription status
        /// </summary>
        /// <returns>Subscription status information</returns>
        [HttpGet("subscription")]
        [SwaggerOperation(Summary = "Get subscription status", Description = "Retrieves the current user's subscription status")]
        [ProducesResponseType(typeof(SubscriptionStatus), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetSubscriptionStatus()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found in token");
                }

                var subscription = await _paymentService.GetSubscriptionStatusAsync(userId);
                if (subscription == null)
                {
                    return NotFound("No active subscription found");
                }

                return Ok(subscription);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving subscription status for user {UserId}", GetCurrentUserId());
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Cancel current subscription
        /// </summary>
        /// <returns>Success confirmation</returns>
        [HttpPost("subscription/cancel")]
        [SwaggerOperation(Summary = "Cancel subscription", Description = "Cancels the current user's subscription")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> CancelSubscription()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found in token");
                }

                var result = await _paymentService.CancelSubscriptionAsync(userId);
                if (!result)
                {
                    return NotFound("No active subscription found to cancel");
                }

                return Ok(new { message = "Subscription canceled successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error canceling subscription for user {UserId}", GetCurrentUserId());
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Create a one-time payment
        /// </summary>
        /// <param name="request">One-time payment request</param>
        /// <returns>Payment response</returns>
        [HttpPost("one-time")]
        [SwaggerOperation(Summary = "Create one-time payment", Description = "Creates a one-time payment for the current user")]
        [ProducesResponseType(typeof(PaymentResponse), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> CreateOneTimePayment([FromBody] OneTimePaymentRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var userId = GetCurrentUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found in token");
                }

                var result = await _paymentService.CreateOneTimePaymentAsync(userId, request);
                
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                return CreatedAtAction(nameof(CreateOneTimePayment), result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating one-time payment for user {UserId}", GetCurrentUserId());
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get payment history
        /// </summary>
        /// <returns>List of user's payments</returns>
        [HttpGet("history")]
        [SwaggerOperation(Summary = "Get payment history", Description = "Retrieves the current user's payment history")]
        [ProducesResponseType(typeof(IEnumerable<Invoice>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> GetPaymentHistory()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found in token");
                }

                var history = await _paymentService.GetPaymentHistoryAsync(userId);
                return Ok(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving payment history for user {UserId}", GetCurrentUserId());
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Handle Clerk webhooks
        /// </summary>
        /// <param name="webhookEvent">Webhook event data</param>
        /// <returns>Success confirmation</returns>
        [HttpPost("webhook")]
        [AllowAnonymous]
        [SwaggerOperation(Summary = "Handle payment webhooks", Description = "Handles webhook events from Clerk payment system")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> HandleWebhook([FromBody] ClerkWebhookEvent webhookEvent)
        {
            try
            {
                // Verify webhook signature here (implementation depends on Clerk's webhook verification)
                
                await _paymentService.ProcessWebhookAsync(webhookEvent);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing webhook event of type {EventType}", webhookEvent.Type);
                return BadRequest("Error processing webhook");
            }
        }

        /// <summary>
        /// Update payment method
        /// </summary>
        /// <param name="paymentMethodId">New payment method ID</param>
        /// <returns>Success confirmation</returns>
        [HttpPost("payment-method")]
        [SwaggerOperation(Summary = "Update payment method", Description = "Updates the current user's default payment method")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> UpdatePaymentMethod([FromBody] string paymentMethodId)
        {
            try
            {
                if (string.IsNullOrEmpty(paymentMethodId))
                {
                    return BadRequest("Payment method ID is required");
                }

                var userId = GetCurrentUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found in token");
                }

                var result = await _paymentService.UpdatePaymentMethodAsync(userId, paymentMethodId);
                if (!result)
                {
                    return BadRequest("Failed to update payment method");
                }

                return Ok(new { message = "Payment method updated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating payment method for user {UserId}", GetCurrentUserId());
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get current user ID from JWT token
        /// </summary>
        /// <returns>User ID</returns>
        private string? GetCurrentUserId()
        {
            return User.FindFirst(ClaimTypes.NameIdentifier)?.Value ??
                   User.FindFirst("sub")?.Value ??
                   User.FindFirst("user_id")?.Value;
        }
    }
}
