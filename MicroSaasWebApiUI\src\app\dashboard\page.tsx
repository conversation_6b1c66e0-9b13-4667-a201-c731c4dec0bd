import Dashboard from '@/components/Dashboard'
import { auth } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import { getSubscriptionByUserId } from '../api/actions'
import PricingSection from '@/components/PricingSection'

export default async function DashboardPage() {
	const { userId } = auth()

	if (!userId) {
		redirect('/sign-in')
	}

	const sub = await getSubscriptionByUserId(userId)
	const isInactive = sub ? sub?.sub_status !== 'active' : true

	if (isInactive) {
		return (
			<div className="min-h-screen bg-white dark:bg-[#010814] py-16">
				<div className="container mx-auto px-4">
					<div className="text-center mb-8">
						<h1 className="text-3xl font-bold text-black1 dark:text-white mb-4">
							Complete Your Purchase
						</h1>
						<p className="text-gray-600 dark:text-gray-400">
							Get instant access to the complete .NET Web API template
						</p>
					</div>
					<PricingSection />
				</div>
			</div>
		)
	}

	return (
		<div className="min-h-screen bg-white dark:bg-[#010814]">
			<Dashboard />
		</div>
	)
}
