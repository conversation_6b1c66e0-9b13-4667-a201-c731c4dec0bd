<svg width="1440" height="742" viewBox="0 0 1440 742" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_521_51948)">
<g clip-path="url(#clip1_521_51948)">
<rect width="1440" height="742" fill="#F8FAFF"/>
<g clip-path="url(#clip2_521_51948)">
<path d="M323.75 -388.48V691.52" stroke="#D5DBE1" stroke-width="0.5" stroke-dasharray="8 8"/>
<path d="M663.75 -388.48V691.52" stroke="#D5DBE1" stroke-width="0.5" stroke-dasharray="8 8"/>
<path d="M1003.75 -388.48V691.52" stroke="#D5DBE1" stroke-width="0.5" stroke-dasharray="8 8"/>
</g>
<g opacity="0.05" filter="url(#filter0_f_521_51948)">
<ellipse cx="280.059" cy="204.597" rx="280.059" ry="204.597" transform="matrix(0.282589 -0.959241 -0.959241 -0.282589 234.516 1114.92)" fill="#1364FF"/>
</g>
</g>
<g opacity="0.05" filter="url(#filter1_f_521_51948)">
<ellipse cx="1398.91" cy="859.144" rx="340.046" ry="266.499" transform="rotate(-120.392 1398.91 859.144)" fill="url(#paint0_linear_521_51948)"/>
</g>
<g filter="url(#filter2_f_521_51948)">
<ellipse cx="377.639" cy="387.907" rx="32.3806" ry="72.1625" transform="rotate(-90 377.639 387.907)" fill="#F6F9FC"/>
</g>
<g filter="url(#filter3_f_521_51948)">
<ellipse cx="1057.94" cy="387.907" rx="32.3806" ry="72.1625" transform="rotate(-90 1057.94 387.907)" fill="#F6F9FC"/>
</g>
<g filter="url(#filter4_f_521_51948)">
<ellipse cx="720.258" cy="387.907" rx="32.3806" ry="121.196" transform="rotate(-90 720.258 387.907)" fill="#F6F9FC"/>
</g>
<g filter="url(#filter5_f_521_51948)">
<circle cx="1008.29" cy="386.782" r="64.4528" fill="#F6F9FC"/>
</g>
</g>
<defs>
<filter id="filter0_f_521_51948" x="-244.271" y="363.609" width="723.34" height="849.705" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="75" result="effect1_foregroundBlur_521_51948"/>
</filter>
<filter id="filter1_f_521_51948" x="911.768" y="336.245" width="974.279" height="1045.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_521_51948"/>
</filter>
<filter id="filter2_f_521_51948" x="289.44" y="339.49" width="176.397" height="96.8334" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="8.01805" result="effect1_foregroundBlur_521_51948"/>
</filter>
<filter id="filter3_f_521_51948" x="969.745" y="339.49" width="176.396" height="96.8334" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="8.01805" result="effect1_foregroundBlur_521_51948"/>
</filter>
<filter id="filter4_f_521_51948" x="583.026" y="339.49" width="274.464" height="96.8334" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="8.01805" result="effect1_foregroundBlur_521_51948"/>
</filter>
<filter id="filter5_f_521_51948" x="920.869" y="299.359" width="174.847" height="174.847" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="11.4852" result="effect1_foregroundBlur_521_51948"/>
</filter>
<linearGradient id="paint0_linear_521_51948" x1="1058.86" y1="857.331" x2="1738.95" y2="857.331" gradientUnits="userSpaceOnUse">
<stop stop-color="#635BFF"/>
<stop offset="1" stop-color="#EA1C70"/>
</linearGradient>
<clipPath id="clip0_521_51948">
<rect width="1440" height="742" fill="white"/>
</clipPath>
<clipPath id="clip1_521_51948">
<rect width="1440" height="742" fill="white"/>
</clipPath>
<clipPath id="clip2_521_51948">
<rect width="1440" height="742" fill="white"/>
</clipPath>
</defs>
</svg>
