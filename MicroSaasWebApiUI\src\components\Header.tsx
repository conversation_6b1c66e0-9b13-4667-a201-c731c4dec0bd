'use client'
import { Logo } from '@/components'
import ButtonSignin from '@/components/ButtonSignin'
import NavLinks from '@/components/nav-links'
import {
	<PERSON>et,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON><PERSON>er,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	She<PERSON><PERSON><PERSON>ger,
} from '@/components/ui/sheet'
import { Blog, Moon, OpenNav, Sun } from '@/icons'
import { ScrollToSection } from '@/utils/scroll-to-section'
import { useClerk, useUser } from '@clerk/nextjs'
import { useTheme } from 'next-themes'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

const nav_links = [
	// {
	// 	icon: <Demo />,
	// 	title: 'Demo',
	// 	link: '/',
	// },
	// {
	// 	icon: <Pricing />,
	// 	title: 'Pricing',
	// 	link: '/',
	// },
	{
		icon: <Blog width={18} height={18} />,
		title: 'Blog',
		link: '/blog',
	},
	// {
	// 	icon: (
	// 		<svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
	// 			<path d="M13 9H7" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
	// 			<path d="M13 15H7" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
	// 			<path fillRule="evenodd" clipRule="evenodd" d="M17 3H7C5.34315 3 4 4.34315 4 6V18C4 19.6569 5.34315 21 7 21H17C18.6569 21 20 19.6569 20 18V6C20 4.34315 18.6569 3 17 3Z" stroke="currentColor" strokeWidth="1.5"/>
	// 			<path d="M17 14H17.002V14.002H17V14Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
	// 			<path d="M17 10H17.002V10.002H17V10Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
	// 		</svg>
	// 	),
	// 	title: 'Curriculum Vitae',
	// 	link: '/cv',
	// 	download: true
	// },
]

const ThemeSwitch = () => {
	const { setTheme, theme } = useTheme()

	// No longer forcing light theme on component mount

	useEffect(() => {
		// Set a default theme only if no theme is set
		if (theme !== 'light' && theme !== 'dark') {
			setTheme('light')
		}

		// Mark that theme has been initialized, but don't force light theme
		const isFirstLoad = sessionStorage.getItem('themeInitialized') !== 'true'
		if (isFirstLoad) {
			sessionStorage.setItem('themeInitialized', 'true')
		}

		// Apply theme classes and styles
		if (theme === 'dark') {
			document.documentElement.classList.add('dark')
			document.documentElement.classList.remove('light')
			document.body.style.backgroundColor = '#010814'
			document.body.style.color = 'white'
		} else {
			document.documentElement.classList.add('light')
			document.documentElement.classList.remove('dark')
			document.body.style.backgroundColor = 'white'
			document.body.style.color = 'black'
		}
	}, [theme, setTheme])

	// Toggle theme function
	const toggleTheme = () => {
		const newTheme = theme === 'dark' ? 'light' : 'dark'
		console.log('Theme toggled to:', newTheme)
		setTheme(newTheme)

		// Dispatch a custom event to notify components about the theme change
		window.dispatchEvent(new CustomEvent('themeChange', { detail: { theme: newTheme } }))
	}

	return (
		<div className="flex items-center gap-2">
			<button
				type="button"
				onClick={toggleTheme}
				className={`flex items-center justify-center w-8 h-8 rounded-md transition-colors ${theme === 'light' ? 'bg-blue-100 text-blue-800' : 'bg-gray-700 text-yellow-300'}`}
				aria-label={`Currently in ${theme === 'light' ? 'light' : 'dark'} mode. Click to switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
				title={`Currently in ${theme === 'light' ? 'light' : 'dark'} mode. Click to switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
			>
				{theme === 'light' ? (
					<Sun className="w-5 h-5" />
				) : (
					<Moon className="w-5 h-5" />
				)}
			</button>
		</div>
	)
}

const MobileNav = () => {
	const { isSignedIn, user } = useUser()
	const { signOut } = useClerk()
	const router = useRouter()

	return (
		<Sheet>
			<SheetTrigger>
				<div className='text-black1 dark:text-white'>
					<OpenNav />
				</div>
			</SheetTrigger>
			<SheetContent className='bg-white dark:bg-black1 px-0 pt-4 border-l-0 min-w-[320px]'>
				<SheetHeader>
					<SheetTitle className='text-black1 dark:text-white text-xl font-bold border-b border-[#b3b3b3] text-left pb-4 pl-4'>
						Menu
					</SheetTitle>
				</SheetHeader>
				<Link href='/' className='flex items-center gap-2 mt-8 mx-auto w-fit'>
					<Logo />
				</Link>
				<div className='my-8 mx-auto w-fit'>
					<NavLinks nav_links={nav_links} />
				</div>
				{isSignedIn && user ? (
					<div className='flex flex-col gap-4 mb-8 mx-auto w-fit'>
						<div className='flex items-center gap-2 mb-4 text-black1 dark:text-white'>
							{user.hasImage ? (
								<Image
									src={user.imageUrl}
									alt={user.firstName || 'Account'}
									className='w-6 h-6 rounded-full shrink-0'
									referrerPolicy='no-referrer'
									width={24}
									height={24}
								/>
							) : (
								<span className='w-6 h-6 bg-base-300 flex justify-center items-center rounded-full shrink-0'>
									{user.firstName
										? user.firstName.charAt(0)
										: user.primaryEmailAddress?.emailAddress || 'A'}
								</span>
							)}
							<span className='font-medium'>
								{user.firstName || user.primaryEmailAddress?.emailAddress || 'Account'}
							</span>
						</div>
						<div className='border-t border-gray-200 dark:border-gray-700 pt-4'>
							<Link href='/' className='block text-black1 dark:text-white hover:underline py-2'>
								Home
							</Link>
							<Link href='https://accounts.clerk.com/account' target='_blank' rel='noopener noreferrer' className='block text-black1 dark:text-white hover:underline py-2'>
								Manage Profile
							</Link>
							<button
								className='text-red-500 hover:underline text-left block w-full py-2'
								onClick={() => signOut(() => router.push('/'))}
							>
								Logout
							</button>
						</div>
					</div>
				) : (
					<div className='mb-8 mx-auto w-fit block'>
						<ButtonSignin text='Login/SignUp' />
					</div>
				)}
			</SheetContent>
		</Sheet>
	)
}

const Header = () => {
	return (
		<div className='w-full fixed top-0 z-50 bg-white dark:bg-[#010814] border-b border-gray-200 dark:border-gray-800 shadow-sm'>
			<div className='container mx-auto max-w-[1440px] w-full flex justify-between items-center gap-4 px-4 sm:px-12 py-1.5'>
				<Link href='/' className='flex items-center'>
					<Logo />
				</Link>

				<div className='flex items-center gap-6'>
					<div className='hidden lg:flex items-center justify-end'>
						<NavLinks nav_links={nav_links} />
					</div>

					<div className='hidden lg:flex items-center gap-4'>
						<div
							dangerouslySetInnerHTML={{
								__html: `<a href="https://www.buymeacoffee.com/javian" target="_blank"><img src="https://cdn.buymeacoffee.com/buttons/v2/default-yellow.png" alt="Buy Me A Coffee" style="height: 32px !important;width: auto !important;" ></a>`
							}}
						/>
						<ThemeSwitch />
						<ButtonSignin />
					</div>

					<div className='lg:hidden flex gap-2'>
						<ThemeSwitch />
						<MobileNav />
					</div>
				</div>
			</div>
		</div>
	)
}

export default Header

