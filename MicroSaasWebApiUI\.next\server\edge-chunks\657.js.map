{"version": 3, "file": "edge-chunks/657.js", "mappings": "gHAqBAA,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EA0FAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,wEAzFAC,EAAA,MACAC,aAAA,CACI,GAAAC,EAAAC,EAAA,EAAY,KAAApB,GACZ,GAAAmB,EAAAC,EAAA,EAAY,KAAAtB,EAAA,6BACZ,GAAAqB,EAAAC,EAAA,EAAY,KAAArB,EANhB,MAOA,CACAsB,iBAAAC,CAAA,EACA,IAAAC,EACA,IAAS,GAAAJ,EAAAK,EAAA,EAAY,KAAAxB,EAAAG,GACrB,SAEA,IAAAsB,EAAAC,KAAAD,GAAA,GACAE,EAAgB,GAAAR,EAAAS,EAAA,EAAe,KAAA5B,EAAAC,GAAA4B,IAAA,MAAAP,GAC/BQ,EAAA,MAAAP,CAAAA,EAAwB,GAAAJ,EAAAK,EAAA,EAAY,KAAAxB,EAAAE,EAAA,SAAAqB,CAAA,CAAAI,EAAA,CACpC,IAAAG,EAAA,CACA,IAAAC,EAAA,CACA,GAAW,GAAAZ,EAAAK,EAAA,EAAY,KAAAxB,EAAAE,EAAA,CACvB,CAAAyB,EAAA,CAAAF,CACA,EACAO,aAAAC,OAAA,CAA2B,GAAAd,EAAAK,EAAA,EAAY,KAAA1B,GAAAoC,KAAAC,SAAA,CAAAJ,GACvC,CAEA,GADAD,GAAAL,EAAAK,EAAoD,GAAAX,EAAAK,EAAA,EAAY,KAAAzB,GAChE,CACA,IAAAgC,EAA2B,GAAAZ,EAAAK,EAAA,EAAY,KAAAxB,EAAAE,EACvC,QAAA6B,CAAA,CAAAJ,EAAA,CACAK,aAAAC,OAAA,CAA2B,GAAAd,EAAAK,EAAA,EAAY,KAAA1B,GAAAoC,KAAAC,SAAA,CAAAJ,GACvC,CACA,QAAAD,CACA,CACA,EACAhC,EAAA,IAAAsC,QACArC,EAAA,IAAAqC,QACApC,EAAA,IAAAqC,QAKApC,EAAA,SAAAqC,CAAA,EACA,IAAUC,GAAAC,CAAA,CAAAC,GAAAC,CAAA,CAAApB,QAAAA,CAAA,IAAAqB,EAAA,CAAqCL,EAC/CM,EAAA,CACA,GAAAtB,CAAA,CACA,GAAAqB,CAAA,EAEA,OAAAT,KAAAC,SAAA,CACAU,OAAAC,IAAA,EACA,GAAAxB,CAAA,CACA,GAAAqB,CAAA,GACKI,IAAA,GAAAC,GAAA,IAAAJ,CAAA,CAAAjB,EAAA,EAEL,EACAzB,EAAA,WACA,IAAA+C,EAAAjB,aAAAkB,OAAA,CAA2C,GAAA/B,EAAAK,EAAA,EAAY,KAAA1B,WACvD,EAGAoC,KAAAiB,KAAA,CAAAF,GAFA,EAGA,EACA9C,EAAA,WACA,uBAAAiD,OACA,SAEA,IAAAC,EAAAD,OAAApB,YAAA,CACA,IAAAqB,EACA,SAEA,IACA,IAAAC,EAAA,OAGA,OAFAD,EAAApB,OAAA,CAAAqB,EAAAA,GACAD,EAAAE,UAAA,CAAAD,GACA,EACA,CAAI,MAAAE,EAAA,CAMJ,OALAA,aAAAC,cACAD,CAAAA,uBAAAA,EAAAE,IAAA,EAAAF,+BAAAA,EAAAE,IAAA,GACAL,EAAAM,MAAA,IACAN,EAAAE,UAAA,CAAyB,GAAApC,EAAAK,EAAA,EAAY,KAAA1B,IAErC,EACA,CACA,EAGA,IAAA8D,EAAA,CACAC,aAAA,EACAC,cAAA,EAIAC,SAAA,6BACA,EAEAC,EAAA,MACA9C,YAAA+C,CAAA,MAOA1C,EAAA2C,EAAAC,EAAAC,EAAAC,EAAAC,EANI,GAAAnD,EAAAC,EAAA,EAAY,KAAAX,GACZ,GAAAU,EAAAC,EAAA,EAAY,KAAAhB,GACZ,GAAAe,EAAAC,EAAA,EAAY,KAAAf,GACZ,GAAAc,EAAAC,EAAA,EAAY,KAAAd,EAAA,IACZ,GAAAa,EAAAC,EAAA,EAAY,KAAAb,EAAA,IACZ,GAAAY,EAAAC,EAAA,EAAY,KAAAZ,GAEZ,GAAAW,EAAAoD,EAAA,EAAY,KAAAnE,EAAA,CAChB0D,cAAA,MAAAvC,CAAAA,EAAA0C,EAAAH,aAAA,EAAAvC,EAAAqC,EAAAE,aAAA,CACAD,aAAA,MAAAK,CAAAA,EAAAD,EAAAJ,YAAA,EAAAK,EAAAN,EAAAC,YAAA,CACAW,SAAA,MAAAL,CAAAA,EAAAF,EAAAO,QAAA,GAAAL,EACAM,MAAA,MAAAL,CAAAA,EAAAH,EAAAQ,KAAA,GAAAL,EACAL,SAAAH,EAAAG,QAAA,GAEA,EAAAW,YAAA,sBAAAtB,OAGM,GAAAjC,EAAAK,EAAA,EAAY,KAAAlB,GAAAoE,YAAA,OAAAL,CAAAA,EAAAJ,EAAAS,YAAA,EAAAL,EAAA,GAFZ,GAAAlD,EAAAK,EAAA,EAAY,KAAAlB,GAAAoE,YAAA,IAId,GAAAvD,EAAAK,EAAA,EAAY,KAAAlB,GAAAqE,GAAA,CAAAV,EAAAU,GAAA,CACZ,GAAAxD,EAAAK,EAAA,EAAY,KAAAlB,GAAAsE,UAAA,CAAAX,EAAAW,UAAA,CACZ,GAAAzD,EAAAK,EAAA,EAAY,KAAAlB,GAAAuE,cAAA,OAAAP,CAAAA,EAAAL,EAAAY,cAAA,EAAAP,EAAA,GAChB,IAAAQ,EAAsB,GAAAC,EAAAC,EAAA,EAAmBf,EAAAY,cAAA,EACzCC,GACM,IAAA3D,EAAAK,EAAA,EAAY,KAAAlB,GAAA2E,YAAA,CAAAH,EAAAG,YAAA,EAElBhB,EAAAiB,SAAA,EACM,IAAA/D,EAAAK,EAAA,EAAY,KAAAlB,GAAA4E,SAAA,CAAAjB,EAAAiB,SAAA,CAAAC,SAAA,QAEd,GAAAhE,EAAAoD,EAAA,EAAY,KAAAlE,EAAA,IAAAY,EAChB,CACA,IAAAmE,WAAA,CACA,IAAA7D,UACoB,gBAAZ,GAAAJ,EAAAK,EAAA,EAAY,KAAAlB,GAAA2E,YAAA,EAGZ,GAAA9D,EAAAK,EAAA,EAAY,KAAApB,GAAAoE,QAAA,sBAAAa,SAA8D,GAAAC,EAAAC,EAAA,EAAQF,QAAAG,GAAA,CAAAC,wBAAA,IAG1F,qBAAArC,QAAA,MAAA7B,CAAAA,EAAA6B,MAAAA,OAAA,OAAAA,OAAAsC,SAAA,IAAAnE,EAAAoE,SAAA,CAIA,CACA,IAAAC,SAAA,CACA,MAAW,GAAAzE,EAAAK,EAAA,EAAY,KAAApB,GAAAqE,KAAA,sBAAAY,SAA2D,GAAAC,EAAAC,EAAA,EAAQF,QAAAG,GAAA,CAAAK,qBAAA,CAC1F,CACAC,OAAAxD,CAAA,EACA,IAAAyD,EAA4B,GAAA5E,EAAAS,EAAA,EAAe,KAAAnB,EAAAO,GAAAa,IAAA,MAAAS,EAAAA,KAAA,CAAAA,EAAAhB,OAAA,EACvC,GAAAH,EAAAS,EAAA,EAAe,KAAAnB,EAAAK,GAAAe,IAAA,MAAAkE,EAAAzD,KAAA,CAAAyD,GACV,GAAA5E,EAAAS,EAAA,EAAe,KAAAnB,EAAAC,GAAAmB,IAAA,MAAAkE,EAAAzD,EAAA0D,iBAAA,IAGpB,GAAA7E,EAAAK,EAAA,EAAY,KAAAjB,GAAA0F,IAAA,CAAAF,GACZ,GAAA5E,EAAAS,EAAA,EAAe,KAAAnB,EAAAG,GAAAiB,IAAA,OACnB,CACA,EC5JA,SAAAqE,EAAAjC,CAAA,EACA,IAAAkC,EAAA,CAAiB,GAAAlC,CAAA,EACjBmC,EAAoB,GAAAC,EAAAC,EAAA,EAAsBH,GAC1CI,EAAuB,GAAAF,EAAAG,EAAA,EAAyB,CAAGvC,QAAAkC,EAAAC,UAAAA,CAAA,GACnDK,EAAA,IAAwBzC,EAAkB,CAC1C,GAAAC,EAAAwC,SAAA,CACA5B,eAAAsB,EAAAtB,cAAA,CACAK,UAAAiB,EAAAjB,SAAA,CACA,GAAAiB,EAAAO,WAAA,EAA4B/B,IAAAwB,EAAAO,WAAA,CAAAhD,IAAA,CAAAkB,WAAAuB,EAAAO,WAAA,CAAAC,OAAA,EAAmE,KAE/F,OACA,GAAAP,CAAA,CACA,GAAAG,CAAA,CACAE,UAAAA,CACA,CACA,CD8IArG,EAAA,IAAAgC,QACA/B,EAAA,IAAA+B,QACA9B,EAAA,IAAA8B,QACA7B,EAAA,IAAA6B,QACA5B,EAAA,IAAA4B,QACA3B,EAAA,IAAA4B,QACA3B,EAAA,SAAAqF,CAAA,CAAAC,CAAA,EACA,YAAAZ,SAAA,QAAAQ,OAAA,EAA4C,GAAAzE,EAAAS,EAAA,EAAe,KAAAnB,EAAAE,GAAAkB,IAAA,MAAAkE,EAAAC,EAC3D,EACArF,EAAA,SAAAoF,CAAA,CAAAC,CAAA,EACA,IAAAY,EAAAC,KAAAC,MAAA,SACA,CAAM,GAAA3F,EAAAK,EAAA,EAAY,KAAAnB,GAAAgB,gBAAA,CAAA0E,IAGlBa,GAAuB,GAAAzF,EAAAK,EAAA,EAAY,KAAApB,GAAAyD,YAAA,YAAAmC,GAAAY,GAAAZ,CAAA,CACnC,EACApF,EAAA,WACA,uBAAAwC,OAAA,CACI,GAAAjC,EAAAS,EAAA,EAAe,KAAAnB,EAAAI,GAAAgB,IAAA,OACnB,MACA,CAEA,GADuB,GAAAV,EAAAK,EAAA,EAAY,KAAAjB,GAAAoD,MAAA,EAA0B,GAAAxC,EAAAK,EAAA,EAAY,KAAApB,GAAA0D,aAAA,CACzE,CACQ,GAAA3C,EAAAK,EAAA,EAAY,KAAAhB,IAEpBuG,CADA,oBAAAC,mBAAAA,mBAAAC,YAAA,EACa,GAAA9F,EAAAK,EAAA,EAAY,KAAAhB,IAErB,GAAAW,EAAAS,EAAA,EAAe,KAAAnB,EAAAI,GAAAgB,IAAA,OACnB,MACA,CACM,GAAAV,EAAAK,EAAA,EAAY,KAAAhB,KAGlB,wBAAA4C,OACI,GAAAjC,EAAAoD,EAAA,EAAY,KAAA/D,EAAA0G,oBAAA,KACV,GAAA/F,EAAAS,EAAA,EAAe,KAAAnB,EAAAI,GAAAgB,IAAA,MACrB,IAEI,GAAAV,EAAAoD,EAAA,EAAY,KAAA/D,EAAA2G,WAAA,KACV,GAAAhG,EAAAS,EAAA,EAAe,KAAAnB,EAAAI,GAAAgB,IAAA,MACrB,EAAK,IAEL,EACAhB,EAAA,WACAuG,MAAA,IAAAC,IAAA,YAA6B,GAAAlG,EAAAK,EAAA,EAAY,KAAApB,GAAA2D,QAAA,GACzCuD,OAAA,OAEAC,KAAArF,KAAAC,SAAA,EACAqF,OAAc,GAAArG,EAAAK,EAAA,EAAY,KAAAjB,EAC1B,GACAkH,QAAA,CACA,iCACA,CACA,GAAGC,KAAA,aAAAC,IAAA,MACC,GAAAxG,EAAAoD,EAAA,EAAY,KAAAhE,EAAA,GAChB,GAAGmH,KAAA,YACH,EAIA5G,EAAA,SAAAwB,CAAA,CAAAhB,CAAA,EACA,KAAAsE,OAAA,GAGA,SAAAgC,QAAAC,cAAA,EACAD,QAAAC,cAAA,qBAAAvF,GACAsF,QAAAE,GAAA,CAAAxG,GACAsG,QAAAG,QAAA,IAEAH,QAAAE,GAAA,qBAAAxF,EAAAhB,GAEA,EAMAP,EAAA,WACA,IAAA2F,EAAA,CACAhD,KAAU,GAAAvC,EAAAK,EAAA,EAAY,KAAAlB,GAAAqE,GAAA,CACtBgC,QAAa,GAAAxF,EAAAK,EAAA,EAAY,KAAAlB,GAAAsE,UAAA,EAKzB,MAHA,oBAAAxB,QAAAA,OAAA4E,KAAA,EACAtB,CAAAA,EAAA,CAAoB,GAAAA,CAAA,IAAAtD,OAAA4E,KAAA,CAAA9G,WAAA,CAAAwF,WAAA,GAEpBA,CACA,EAIA1F,EAAA,SAAAsB,CAAA,CAAAhB,CAAA,EACA,IAAAC,EAAA2C,EACA,IAAAwC,EAAsB,GAAAvF,EAAAS,EAAA,EAAe,KAAAnB,EAAAM,GAAAc,IAAA,OACrC,OACAS,MAAAA,EACA2F,GAAA,MAAA1G,CAAAA,EAAc,GAAAJ,EAAAK,EAAA,EAAY,KAAAlB,GAAAoE,YAAA,EAAAnD,EAAA,GAC1B2G,GAAA,MAAAhE,CAAAA,EAAc,GAAA/C,EAAAK,EAAA,EAAY,KAAAlB,GAAA2E,YAAA,EAAAf,EAAA,GAC1BS,IAAA+B,EAAAhD,IAAA,CACAyE,KAAAzB,EAAAC,OAAA,CACA,GAAO,GAAAxF,EAAAK,EAAA,EAAY,KAAAlB,GAAAuE,cAAA,EAAqCpC,GAAI,GAAAtB,EAAAK,EAAA,EAAY,KAAAlB,GAAAuE,cAAA,EAAmC,EAAI,CAC/G,GAAO,GAAA1D,EAAAK,EAAA,EAAY,KAAAlB,GAAA4E,SAAA,EAAgC3C,GAAI,GAAApB,EAAAK,EAAA,EAAY,KAAAlB,GAAA4E,SAAA,EAA8B,EAAI,CACrG5D,QAAAA,CACA,CACA,ECrQmB,GAAA8G,EAAAC,CAAA,EAAiBhC,EAAAiC,EAAW,sDCU/C,IAAAC,EAAA,IAA8CC,EAAAC,iBAAiB,yBCL/D,IAAAC,EAAA,CACAxD,UAAayD,EAAAC,EAAU,CACvB/D,eAAkB8D,EAAAE,EAAe,CACjCC,OAAUH,EAAAI,EAAO,CACjBC,WAAcL,EAAAM,EAAW,CACzBC,UAAA,sBACAC,SAAYR,EAAAS,EAAS,CACrBC,OAAUV,EAAAW,EAAM,CAChBC,YAAeZ,EAAAa,EAAY,CAC3B9C,YAAeiC,EAAAc,EAAY,CAC3BhD,UAAA,CACAjC,SAAcmE,EAAAe,EAAkB,CAChCjF,MAAWkE,EAAAgB,EAAe,CAE1B,EACAC,EAAA,GAAkD1D,EAAiB,CAAG,GAAAwC,CAAA,IAAAzE,CAAA,GACtE4F,EAA6B3D,EAAiBwC,GAmBxCoB,EAAW,IAAAC,MAAAlH,OAAAmH,MAAA,CAlBjB,SACAzI,EAAA2C,MACA+F,EACA,IACA,IAAAC,EAAoB,GAAAC,EAAAC,EAAA,IACpBC,EAAiC,GAAAC,EAAAC,EAAA,EAASL,EAAUM,EAAAC,EAAS,CAAAC,OAAA,CAAAC,gBAAA,EAC7DV,EAAkB,GAAAK,EAAAM,EAAA,EAAuBP,EACzC,CAAI,MAAA7G,EAAA,CACJ,GAAAA,GAAe,GAAA2G,EAAAU,EAAA,EAAqBrH,GACpC,MAAAA,CAEA,CACA,IAAAS,EAAA,MAAAC,CAAAA,EAAA,MAAA3C,CAAAA,EAA8BgH,EAAiCuC,QAAA,WAAAvJ,EAAAwJ,GAAA,iBAAA7G,EAAA+F,QAC/D,CAAAhG,MAAAA,EAAA,OAAAA,EAAAiB,SAAA,GAAAjB,CAAAA,MAAAA,EAAA,OAAAA,EAAAY,cAAA,EACA+E,EAAA3F,GAEA4F,CACA,EACiBA,GAAA,CACjBkB,IAAAA,CAAAC,EAAAC,EAAAC,KACI,GAAAC,EAAAC,EAAA,EAAU,sEACdC,QAAAN,GAAA,CAAAC,EAAAC,EAAAC,GAEA", "sources": ["webpack://_N_E/./node_modules/@clerk/shared/dist/telemetry.mjs?f72c", "webpack://_N_E/./node_modules/@clerk/backend/dist/index.mjs?34e3", "webpack://_N_E/./node_modules/@clerk/nextjs/dist/esm/server/clerkMiddleware.js?487b", "webpack://_N_E/./node_modules/@clerk/nextjs/dist/esm/server/clerkClient.js?3a68"], "sourcesContent": ["import {\n  eventMethodCalled\n} from \"./chunk-TUVJ3GI6.mjs\";\nimport {\n  isTruthy\n} from \"./chunk-QE2A7CJI.mjs\";\nimport {\n  parsePublishableKey\n} from \"./chunk-L2BNNARM.mjs\";\nimport \"./chunk-TETGTEI2.mjs\";\nimport \"./chunk-KOH7GTJO.mjs\";\nimport \"./chunk-I6MTSTOF.mjs\";\nimport {\n  __privateAdd,\n  __privateGet,\n  __privateMethod,\n  __privateSet\n} from \"./chunk-7ELT755Q.mjs\";\n\n// src/telemetry/throttler.ts\nvar DEFAULT_CACHE_TTL_MS = 864e5;\nvar _storageKey, _cacheTtl, _TelemetryEventThrottler_instances, generateKey_fn, cache_get, isValidBrowser_get;\nvar TelemetryEventThrottler = class {\n  constructor() {\n    __privateAdd(this, _TelemetryEventThrottler_instances);\n    __privateAdd(this, _storageKey, \"clerk_telemetry_throttler\");\n    __privateAdd(this, _cacheTtl, DEFAULT_CACHE_TTL_MS);\n  }\n  isEventThrottled(payload) {\n    var _a;\n    if (!__privateGet(this, _TelemetryEventThrottler_instances, isValidBrowser_get)) {\n      return false;\n    }\n    const now = Date.now();\n    const key = __privateMethod(this, _TelemetryEventThrottler_instances, generateKey_fn).call(this, payload);\n    const entry = (_a = __privateGet(this, _TelemetryEventThrottler_instances, cache_get)) == null ? void 0 : _a[key];\n    if (!entry) {\n      const updatedCache = {\n        ...__privateGet(this, _TelemetryEventThrottler_instances, cache_get),\n        [key]: now\n      };\n      localStorage.setItem(__privateGet(this, _storageKey), JSON.stringify(updatedCache));\n    }\n    const shouldInvalidate = entry && now - entry > __privateGet(this, _cacheTtl);\n    if (shouldInvalidate) {\n      const updatedCache = __privateGet(this, _TelemetryEventThrottler_instances, cache_get);\n      delete updatedCache[key];\n      localStorage.setItem(__privateGet(this, _storageKey), JSON.stringify(updatedCache));\n    }\n    return !!entry;\n  }\n};\n_storageKey = new WeakMap();\n_cacheTtl = new WeakMap();\n_TelemetryEventThrottler_instances = new WeakSet();\n/**\n * Generates a consistent unique key for telemetry events by sorting payload properties.\n * This ensures that payloads with identical content in different orders produce the same key.\n */\ngenerateKey_fn = function(event) {\n  const { sk: _sk, pk: _pk, payload, ...rest } = event;\n  const sanitizedEvent = {\n    ...payload,\n    ...rest\n  };\n  return JSON.stringify(\n    Object.keys({\n      ...payload,\n      ...rest\n    }).sort().map((key) => sanitizedEvent[key])\n  );\n};\ncache_get = function() {\n  const cacheString = localStorage.getItem(__privateGet(this, _storageKey));\n  if (!cacheString) {\n    return {};\n  }\n  return JSON.parse(cacheString);\n};\nisValidBrowser_get = function() {\n  if (typeof window === \"undefined\") {\n    return false;\n  }\n  const storage = window.localStorage;\n  if (!storage) {\n    return false;\n  }\n  try {\n    const testKey = \"test\";\n    storage.setItem(testKey, testKey);\n    storage.removeItem(testKey);\n    return true;\n  } catch (err) {\n    const isQuotaExceededError = err instanceof DOMException && // Check error names for different browsers\n    (err.name === \"QuotaExceededError\" || err.name === \"NS_ERROR_DOM_QUOTA_REACHED\");\n    if (isQuotaExceededError && storage.length > 0) {\n      storage.removeItem(__privateGet(this, _storageKey));\n    }\n    return false;\n  }\n};\n\n// src/telemetry/collector.ts\nvar DEFAULT_CONFIG = {\n  samplingRate: 1,\n  maxBufferSize: 5,\n  // Production endpoint: https://clerk-telemetry.com\n  // Staging endpoint: https://staging.clerk-telemetry.com\n  // Local: http://localhost:8787\n  endpoint: \"https://clerk-telemetry.com\"\n};\nvar _config, _eventThrottler, _metadata, _buffer, _pendingFlush, _TelemetryCollector_instances, shouldRecord_fn, shouldBeSampled_fn, scheduleFlush_fn, flush_fn, logEvent_fn, getSDKMetadata_fn, preparePayload_fn;\nvar TelemetryCollector = class {\n  constructor(options) {\n    __privateAdd(this, _TelemetryCollector_instances);\n    __privateAdd(this, _config);\n    __privateAdd(this, _eventThrottler);\n    __privateAdd(this, _metadata, {});\n    __privateAdd(this, _buffer, []);\n    __privateAdd(this, _pendingFlush);\n    var _a, _b, _c, _d, _e, _f;\n    __privateSet(this, _config, {\n      maxBufferSize: (_a = options.maxBufferSize) != null ? _a : DEFAULT_CONFIG.maxBufferSize,\n      samplingRate: (_b = options.samplingRate) != null ? _b : DEFAULT_CONFIG.samplingRate,\n      disabled: (_c = options.disabled) != null ? _c : false,\n      debug: (_d = options.debug) != null ? _d : false,\n      endpoint: DEFAULT_CONFIG.endpoint\n    });\n    if (!options.clerkVersion && typeof window === \"undefined\") {\n      __privateGet(this, _metadata).clerkVersion = \"\";\n    } else {\n      __privateGet(this, _metadata).clerkVersion = (_e = options.clerkVersion) != null ? _e : \"\";\n    }\n    __privateGet(this, _metadata).sdk = options.sdk;\n    __privateGet(this, _metadata).sdkVersion = options.sdkVersion;\n    __privateGet(this, _metadata).publishableKey = (_f = options.publishableKey) != null ? _f : \"\";\n    const parsedKey = parsePublishableKey(options.publishableKey);\n    if (parsedKey) {\n      __privateGet(this, _metadata).instanceType = parsedKey.instanceType;\n    }\n    if (options.secretKey) {\n      __privateGet(this, _metadata).secretKey = options.secretKey.substring(0, 16);\n    }\n    __privateSet(this, _eventThrottler, new TelemetryEventThrottler());\n  }\n  get isEnabled() {\n    var _a;\n    if (__privateGet(this, _metadata).instanceType !== \"development\") {\n      return false;\n    }\n    if (__privateGet(this, _config).disabled || typeof process !== \"undefined\" && isTruthy(process.env.CLERK_TELEMETRY_DISABLED)) {\n      return false;\n    }\n    if (typeof window !== \"undefined\" && !!((_a = window == null ? void 0 : window.navigator) == null ? void 0 : _a.webdriver)) {\n      return false;\n    }\n    return true;\n  }\n  get isDebug() {\n    return __privateGet(this, _config).debug || typeof process !== \"undefined\" && isTruthy(process.env.CLERK_TELEMETRY_DEBUG);\n  }\n  record(event) {\n    const preparedPayload = __privateMethod(this, _TelemetryCollector_instances, preparePayload_fn).call(this, event.event, event.payload);\n    __privateMethod(this, _TelemetryCollector_instances, logEvent_fn).call(this, preparedPayload.event, preparedPayload);\n    if (!__privateMethod(this, _TelemetryCollector_instances, shouldRecord_fn).call(this, preparedPayload, event.eventSamplingRate)) {\n      return;\n    }\n    __privateGet(this, _buffer).push(preparedPayload);\n    __privateMethod(this, _TelemetryCollector_instances, scheduleFlush_fn).call(this);\n  }\n};\n_config = new WeakMap();\n_eventThrottler = new WeakMap();\n_metadata = new WeakMap();\n_buffer = new WeakMap();\n_pendingFlush = new WeakMap();\n_TelemetryCollector_instances = new WeakSet();\nshouldRecord_fn = function(preparedPayload, eventSamplingRate) {\n  return this.isEnabled && !this.isDebug && __privateMethod(this, _TelemetryCollector_instances, shouldBeSampled_fn).call(this, preparedPayload, eventSamplingRate);\n};\nshouldBeSampled_fn = function(preparedPayload, eventSamplingRate) {\n  const randomSeed = Math.random();\n  if (__privateGet(this, _eventThrottler).isEventThrottled(preparedPayload)) {\n    return false;\n  }\n  return randomSeed <= __privateGet(this, _config).samplingRate && (typeof eventSamplingRate === \"undefined\" || randomSeed <= eventSamplingRate);\n};\nscheduleFlush_fn = function() {\n  if (typeof window === \"undefined\") {\n    __privateMethod(this, _TelemetryCollector_instances, flush_fn).call(this);\n    return;\n  }\n  const isBufferFull = __privateGet(this, _buffer).length >= __privateGet(this, _config).maxBufferSize;\n  if (isBufferFull) {\n    if (__privateGet(this, _pendingFlush)) {\n      const cancel = typeof cancelIdleCallback !== \"undefined\" ? cancelIdleCallback : clearTimeout;\n      cancel(__privateGet(this, _pendingFlush));\n    }\n    __privateMethod(this, _TelemetryCollector_instances, flush_fn).call(this);\n    return;\n  }\n  if (__privateGet(this, _pendingFlush)) {\n    return;\n  }\n  if (\"requestIdleCallback\" in window) {\n    __privateSet(this, _pendingFlush, requestIdleCallback(() => {\n      __privateMethod(this, _TelemetryCollector_instances, flush_fn).call(this);\n    }));\n  } else {\n    __privateSet(this, _pendingFlush, setTimeout(() => {\n      __privateMethod(this, _TelemetryCollector_instances, flush_fn).call(this);\n    }, 0));\n  }\n};\nflush_fn = function() {\n  fetch(new URL(\"/v1/event\", __privateGet(this, _config).endpoint), {\n    method: \"POST\",\n    // TODO: We send an array here with that idea that we can eventually send multiple events.\n    body: JSON.stringify({\n      events: __privateGet(this, _buffer)\n    }),\n    headers: {\n      \"Content-Type\": \"application/json\"\n    }\n  }).catch(() => void 0).then(() => {\n    __privateSet(this, _buffer, []);\n  }).catch(() => void 0);\n};\n/**\n * If running in debug mode, log the event and its payload to the console.\n */\nlogEvent_fn = function(event, payload) {\n  if (!this.isDebug) {\n    return;\n  }\n  if (typeof console.groupCollapsed !== \"undefined\") {\n    console.groupCollapsed(\"[clerk/telemetry]\", event);\n    console.log(payload);\n    console.groupEnd();\n  } else {\n    console.log(\"[clerk/telemetry]\", event, payload);\n  }\n};\n/**\n * If in browser, attempt to lazily grab the SDK metadata from the Clerk singleton, otherwise fallback to the initially passed in values.\n *\n * This is necessary because the sdkMetadata can be set by the host SDK after the TelemetryCollector is instantiated.\n */\ngetSDKMetadata_fn = function() {\n  let sdkMetadata = {\n    name: __privateGet(this, _metadata).sdk,\n    version: __privateGet(this, _metadata).sdkVersion\n  };\n  if (typeof window !== \"undefined\" && window.Clerk) {\n    sdkMetadata = { ...sdkMetadata, ...window.Clerk.constructor.sdkMetadata };\n  }\n  return sdkMetadata;\n};\n/**\n * Append relevant metadata from the Clerk singleton to the event payload.\n */\npreparePayload_fn = function(event, payload) {\n  var _a, _b;\n  const sdkMetadata = __privateMethod(this, _TelemetryCollector_instances, getSDKMetadata_fn).call(this);\n  return {\n    event,\n    cv: (_a = __privateGet(this, _metadata).clerkVersion) != null ? _a : \"\",\n    it: (_b = __privateGet(this, _metadata).instanceType) != null ? _b : \"\",\n    sdk: sdkMetadata.name,\n    sdkv: sdkMetadata.version,\n    ...__privateGet(this, _metadata).publishableKey ? { pk: __privateGet(this, _metadata).publishableKey } : {},\n    ...__privateGet(this, _metadata).secretKey ? { sk: __privateGet(this, _metadata).secretKey } : {},\n    payload\n  };\n};\n\n// src/telemetry/events/component-mounted.ts\nvar EVENT_COMPONENT_MOUNTED = \"COMPONENT_MOUNTED\";\nvar EVENT_SAMPLING_RATE = 0.1;\nfunction eventPrebuiltComponentMounted(component, props) {\n  var _a, _b, _c;\n  return {\n    event: EVENT_COMPONENT_MOUNTED,\n    eventSamplingRate: EVENT_SAMPLING_RATE,\n    payload: {\n      component,\n      appearanceProp: Boolean(props == null ? void 0 : props.appearance),\n      baseTheme: Boolean((_a = props == null ? void 0 : props.appearance) == null ? void 0 : _a.baseTheme),\n      elements: Boolean((_b = props == null ? void 0 : props.appearance) == null ? void 0 : _b.elements),\n      variables: Boolean((_c = props == null ? void 0 : props.appearance) == null ? void 0 : _c.variables)\n    }\n  };\n}\nfunction eventComponentMounted(component, props = {}) {\n  return {\n    event: EVENT_COMPONENT_MOUNTED,\n    eventSamplingRate: EVENT_SAMPLING_RATE,\n    payload: {\n      component,\n      ...props\n    }\n  };\n}\nexport {\n  TelemetryCollector,\n  eventComponentMounted,\n  eventMethodCalled,\n  eventPrebuiltComponentMounted\n};\n//# sourceMappingURL=telemetry.mjs.map", "import {\n  createAuthenticateRequest,\n  createBackendApiClient,\n  verifyToken\n} from \"./chunk-HGGLOBDA.mjs\";\nimport {\n  withLegacyReturn\n} from \"./chunk-P263NW7Z.mjs\";\nimport \"./chunk-PVHPEMF5.mjs\";\nimport \"./chunk-5JS2VYLU.mjs\";\n\n// src/index.ts\nimport { TelemetryCollector } from \"@clerk/shared/telemetry\";\nvar verifyToken2 = withLegacyReturn(verifyToken);\nfunction createClerkClient(options) {\n  const opts = { ...options };\n  const apiClient = createBackendApiClient(opts);\n  const requestState = createAuthenticateRequest({ options: opts, apiClient });\n  const telemetry = new TelemetryCollector({\n    ...options.telemetry,\n    publishableKey: opts.publishableKey,\n    secretKey: opts.secretKey,\n    ...opts.sdkMetadata ? { sdk: opts.sdkMetadata.name, sdkVersion: opts.sdkMetadata.version } : {}\n  });\n  return {\n    ...apiClient,\n    ...requestState,\n    telemetry\n  };\n}\nexport {\n  createClerkClient,\n  verifyToken2 as verifyToken\n};\n//# sourceMappingURL=index.mjs.map", "import { AsyncLocalStorage } from \"node:async_hooks\";\nimport { AuthStatus, constants, createClerkRequest, createRedirect } from \"@clerk/backend/internal\";\nimport { eventMethodCalled } from \"@clerk/shared/telemetry\";\nimport { NextResponse } from \"next/server\";\nimport { isRedirect, serverRedirectWithA<PERSON>, setHeader } from \"../utils\";\nimport { withLogger } from \"../utils/debugLogger\";\nimport { clerkClient } from \"./clerkClient\";\nimport { PUBLISHABLE_KEY, SECRET_KEY, SIGN_IN_URL, SIGN_UP_URL } from \"./constants\";\nimport { errorThrower } from \"./errorThrower\";\nimport { createProtect } from \"./protect\";\nimport {\n  assertKey,\n  decorateRequest,\n  handleMultiDomainAndProxy,\n  redirectAdapter,\n  setRequestHeadersOnNextResponse\n} from \"./utils\";\nconst CONTROL_FLOW_ERROR = {\n  FORCE_NOT_FOUND: \"CLERK_PROTECT_REWRITE\",\n  REDIRECT_TO_URL: \"CLERK_PROTECT_REDIRECT_TO_URL\",\n  REDIRECT_TO_SIGN_IN: \"CLERK_PROTECT_REDIRECT_TO_SIGN_IN\"\n};\nconst clerkMiddlewareRequestDataStore = /* @__PURE__ */ new Map();\nconst clerkMiddlewareRequestDataStorage = new AsyncLocalStorage();\nconst clerkMiddleware = (...args) => {\n  const [request, event] = parseRequestAndEvent(args);\n  const [handler, params] = parseHandlerAndOptions(args);\n  return clerkMiddlewareRequestDataStorage.run(clerkMiddlewareRequestDataStore, () => {\n    const nextMiddleware = withLogger(\"clerkMiddleware\", (logger) => async (request2, event2) => {\n      const resolvedParams = typeof params === \"function\" ? params(request2) : params;\n      const publishableKey = assertKey(\n        resolvedParams.publishableKey || PUBLISHABLE_KEY,\n        () => errorThrower.throwMissingPublishableKeyError()\n      );\n      const secretKey = assertKey(\n        resolvedParams.secretKey || SECRET_KEY,\n        () => errorThrower.throwMissingSecretKeyError()\n      );\n      const signInUrl = resolvedParams.signInUrl || SIGN_IN_URL;\n      const signUpUrl = resolvedParams.signUpUrl || SIGN_UP_URL;\n      const options = {\n        publishableKey,\n        secretKey,\n        signInUrl,\n        signUpUrl,\n        ...resolvedParams\n      };\n      clerkMiddlewareRequestDataStore.set(\"requestData\", options);\n      clerkClient().telemetry.record(\n        eventMethodCalled(\"clerkMiddleware\", {\n          handler: Boolean(handler),\n          satellite: Boolean(options.isSatellite),\n          proxy: Boolean(options.proxyUrl)\n        })\n      );\n      if (options.debug) {\n        logger.enable();\n      }\n      const clerkRequest = createClerkRequest(request2);\n      logger.debug(\"options\", options);\n      logger.debug(\"url\", () => clerkRequest.toJSON());\n      const requestState = await clerkClient().authenticateRequest(\n        clerkRequest,\n        createAuthenticateRequestOptions(clerkRequest, options)\n      );\n      logger.debug(\"requestState\", () => ({\n        status: requestState.status,\n        headers: JSON.stringify(Object.fromEntries(requestState.headers)),\n        reason: requestState.reason\n      }));\n      const locationHeader = requestState.headers.get(constants.Headers.Location);\n      if (locationHeader) {\n        return new Response(null, { status: 307, headers: requestState.headers });\n      } else if (requestState.status === AuthStatus.Handshake) {\n        throw new Error(\"Clerk: handshake status without redirect\");\n      }\n      const authObject = requestState.toAuth();\n      logger.debug(\"auth\", () => ({ auth: authObject, debug: authObject.debug() }));\n      const redirectToSignIn = createMiddlewareRedirectToSignIn(clerkRequest);\n      const protect = createMiddlewareProtect(clerkRequest, authObject, redirectToSignIn);\n      const authObjWithMethods = Object.assign(authObject, { protect, redirectToSignIn });\n      let handlerResult = NextResponse.next();\n      try {\n        const userHandlerResult = await clerkMiddlewareRequestDataStorage.run(\n          clerkMiddlewareRequestDataStore,\n          async () => handler == null ? void 0 : handler(() => authObjWithMethods, request2, event2)\n        );\n        handlerResult = userHandlerResult || handlerResult;\n      } catch (e) {\n        handlerResult = handleControlFlowErrors(e, clerkRequest, requestState);\n      }\n      if (requestState.headers) {\n        requestState.headers.forEach((value, key) => {\n          handlerResult.headers.append(key, value);\n        });\n      }\n      if (isRedirect(handlerResult)) {\n        logger.debug(\"handlerResult is redirect\");\n        return serverRedirectWithAuth(clerkRequest, handlerResult, options);\n      }\n      if (options.debug) {\n        setRequestHeadersOnNextResponse(handlerResult, clerkRequest, { [constants.Headers.EnableDebug]: \"true\" });\n      }\n      decorateRequest(clerkRequest, handlerResult, requestState, resolvedParams);\n      return handlerResult;\n    });\n    if (request && event) {\n      return nextMiddleware(request, event);\n    }\n    return nextMiddleware;\n  });\n};\nconst parseRequestAndEvent = (args) => {\n  return [args[0] instanceof Request ? args[0] : void 0, args[0] instanceof Request ? args[1] : void 0];\n};\nconst parseHandlerAndOptions = (args) => {\n  return [\n    typeof args[0] === \"function\" ? args[0] : void 0,\n    (args.length === 2 ? args[1] : typeof args[0] === \"function\" ? {} : args[0]) || {}\n  ];\n};\nconst createAuthenticateRequestOptions = (clerkRequest, options) => {\n  return {\n    ...options,\n    ...handleMultiDomainAndProxy(clerkRequest, options)\n  };\n};\nconst createMiddlewareRedirectToSignIn = (clerkRequest) => {\n  return (opts = {}) => {\n    const err = new Error(CONTROL_FLOW_ERROR.REDIRECT_TO_SIGN_IN);\n    err.returnBackUrl = opts.returnBackUrl === null ? \"\" : opts.returnBackUrl || clerkRequest.clerkUrl.toString();\n    throw err;\n  };\n};\nconst createMiddlewareProtect = (clerkRequest, authObject, redirectToSignIn) => {\n  return (params, options) => {\n    const notFound = () => {\n      throw new Error(CONTROL_FLOW_ERROR.FORCE_NOT_FOUND);\n    };\n    const redirect = (url) => {\n      const err = new Error(CONTROL_FLOW_ERROR.REDIRECT_TO_URL);\n      err.redirectUrl = url;\n      throw err;\n    };\n    return createProtect({ request: clerkRequest, redirect, notFound, authObject, redirectToSignIn })(params, options);\n  };\n};\nconst handleControlFlowErrors = (e, clerkRequest, requestState) => {\n  switch (e.message) {\n    case CONTROL_FLOW_ERROR.FORCE_NOT_FOUND:\n      return setHeader(\n        NextResponse.rewrite(`${clerkRequest.clerkUrl.origin}/clerk_${Date.now()}`),\n        constants.Headers.AuthReason,\n        \"protect-rewrite\"\n      );\n    case CONTROL_FLOW_ERROR.REDIRECT_TO_URL:\n      return redirectAdapter(e.redirectUrl);\n    case CONTROL_FLOW_ERROR.REDIRECT_TO_SIGN_IN:\n      return createRedirect({\n        redirectAdapter,\n        baseUrl: clerkRequest.clerkUrl,\n        signInUrl: requestState.signInUrl,\n        signUpUrl: requestState.signUpUrl,\n        publishableKey: requestState.publishableKey\n      }).redirectToSignIn({ returnBackUrl: e.returnBackUrl });\n    default:\n      throw e;\n  }\n};\nexport {\n  clerkMiddleware,\n  clerkMiddlewareRequestDataStorage,\n  createAuthenticateRequestOptions\n};\n//# sourceMappingURL=clerkMiddleware.js.map", "import { createClerk<PERSON>lient } from \"@clerk/backend\";\nimport { constants } from \"@clerk/backend/internal\";\nimport { deprecated } from \"@clerk/shared/deprecated\";\nimport { buildRequestLike, isPrerenderingBailout } from \"../app-router/server/utils\";\nimport { clerkMiddlewareRequestDataStorage } from \"./clerkMiddleware\";\nimport {\n  API_URL,\n  API_VERSION,\n  DOMAIN,\n  IS_SATELLITE,\n  PROXY_URL,\n  PUBLISHABLE_KEY,\n  SDK_METADATA,\n  SECRET_KEY,\n  TELEMETRY_DEBUG,\n  TELEMETRY_DISABLED\n} from \"./constants\";\nimport { decryptClerkRequestData, getHeader } from \"./utils\";\nconst clerkClientDefaultOptions = {\n  secretKey: SECRET_KEY,\n  publishableKey: PUBLISHABLE_KEY,\n  apiUrl: API_URL,\n  apiVersion: API_VERSION,\n  userAgent: `${\"@clerk/nextjs\"}@${\"5.7.5\"}`,\n  proxyUrl: PROXY_URL,\n  domain: DOMAIN,\n  isSatellite: IS_SATELLITE,\n  sdkMetadata: SDK_METADATA,\n  telemetry: {\n    disabled: TELEMETRY_DISABLED,\n    debug: TELEMETRY_DEBUG\n  }\n};\nconst createClerkClientWithOptions = (options) => createClerkClient({ ...clerkClientDefaultOptions, ...options });\nconst clerkClientSingleton = createClerkClient(clerkClientDefaultOptions);\nconst clerkClientForRequest = () => {\n  var _a, _b;\n  let requestData;\n  try {\n    const request = buildRequestLike();\n    const encryptedRequestData = getHeader(request, constants.Headers.ClerkRequestData);\n    requestData = decryptClerkRequestData(encryptedRequestData);\n  } catch (err) {\n    if (err && isPrerenderingBailout(err)) {\n      throw err;\n    }\n  }\n  const options = (_b = (_a = clerkMiddlewareRequestDataStorage.getStore()) == null ? void 0 : _a.get(\"requestData\")) != null ? _b : requestData;\n  if ((options == null ? void 0 : options.secretKey) || (options == null ? void 0 : options.publishableKey)) {\n    return createClerkClientWithOptions(options);\n  }\n  return clerkClientSingleton;\n};\nconst clerkClient = new Proxy(Object.assign(clerkClientForRequest, clerkClientSingleton), {\n  get(target, prop, receiver) {\n    deprecated(\"clerkClient singleton\", \"Use `clerkClient()` as a function instead.\");\n    return Reflect.get(target, prop, receiver);\n  }\n});\nexport {\n  clerkClient\n};\n//# sourceMappingURL=clerkClient.js.map"], "names": ["_storageKey", "_cacheTtl", "_TelemetryEventThrottler_instances", "generateKey_fn", "cache_get", "isValidBrowser_get", "_config", "_eventThrottler", "_metadata", "_buffer", "_pendingFlush", "_TelemetryCollector_instances", "shouldRecord_fn", "shouldBeSampled_fn", "scheduleFlush_fn", "flush_fn", "logEvent_fn", "getSDKMetadata_fn", "preparePayload_fn", "TelemetryEventThrottler", "constructor", "chunk_7ELT755Q", "Ko", "isEventThrottled", "payload", "_a", "ac", "now", "Date", "key", "U9", "call", "entry", "updatedCache", "localStorage", "setItem", "JSON", "stringify", "WeakMap", "WeakSet", "event", "sk", "_sk", "pk", "_pk", "rest", "sanitizedEvent", "Object", "keys", "sort", "map", "cacheString", "getItem", "parse", "window", "storage", "<PERSON><PERSON><PERSON>", "removeItem", "err", "DOMException", "name", "length", "DEFAULT_CONFIG", "samplingRate", "maxBufferSize", "endpoint", "TelemetryCollector", "options", "_b", "_c", "_d", "_e", "_f", "qx", "disabled", "debug", "clerkVersion", "sdk", "sdkVersion", "publishableKey", "parsed<PERSON><PERSON>", "chunk_L2BNNARM", "nQ", "instanceType", "secret<PERSON>ey", "substring", "isEnabled", "process", "chunk_QE2A7CJI", "fQ", "env", "CLERK_TELEMETRY_DISABLED", "navigator", "webdriver", "isDebug", "CLERK_TELEMETRY_DEBUG", "record", "preparedPayload", "eventSamplingRate", "push", "createClerkClient", "opts", "apiClient", "chunk_HGGLOBDA", "gf", "requestState", "sA", "telemetry", "sdkMetadata", "version", "randomSeed", "Math", "random", "cancel", "cancelIdleCallback", "clearTimeout", "requestIdleCallback", "setTimeout", "fetch", "URL", "method", "body", "events", "headers", "catch", "then", "console", "groupCollapsed", "log", "groupEnd", "Clerk", "cv", "it", "sdkv", "chunk_P263NW7Z", "A", "WX", "clerkMiddlewareRequestDataStorage", "external_node_async_hooks_", "AsyncLocalStorage", "clerkClientDefaultOptions", "server_constants", "Cn", "Am", "apiUrl", "T5", "apiVersion", "Gn", "userAgent", "proxyUrl", "NM", "domain", "yK", "isSatellite", "lo", "nE", "CY", "w4", "createClerkClientWithOptions", "clerkClientS<PERSON>leton", "clerkClient_clerkClient", "Proxy", "assign", "requestData", "request", "utils", "vX", "encryptedRequestData", "server_utils", "Pg", "internal", "_G", "Headers", "ClerkRequestData", "om", "mx", "getStore", "get", "target", "prop", "receiver", "deprecated", "x9", "Reflect"], "sourceRoot": ""}