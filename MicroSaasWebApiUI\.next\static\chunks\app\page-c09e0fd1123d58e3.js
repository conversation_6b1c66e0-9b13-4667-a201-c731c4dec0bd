(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{79234:function(e,t,a){Promise.resolve().then(a.bind(a,17126)),Promise.resolve().then(a.bind(a,16008)),Promise.resolve().then(a.bind(a,11297)),Promise.resolve().then(a.bind(a,83938))},17126:function(e,t,a){"use strict";a.d(t,{default:function(){return D}});var r=a(57437),i=a(2265),n=a(27648),s=a(50598),o=a(75135),l=a(42351),c=a(74767),d=a(89345);let m={name:"<PERSON><PERSON><PERSON>",initials:"JP",url:"https://javianpicardo.com",location:"Hamilton, ON. Canada",locationLink:"https://www.google.com/maps/place/Hamilton",description:"Principal Software Engineer | Application Architect | Solopreneur",summary:"Visionary and results-driven Application Architect and Principal Software Engineer with 16+ years of experience specializing in enterprise-grade Microsoft technology solutions.\n\nProven track record of transforming complex business requirements into scalable, innovative applications while leading cross-functional development teams. Expert in designing sophisticated cloud architectures and implementing security-focused solutions across Azure ecosystems.\n\nConsistently delivers digital transformation initiatives that drive business value for organizations in financial services, insurance, and specialized software sectors. Combines technical leadership with strategic vision to create resilient, future-proof technology platforms.",avatarUrl:"/javian.png",coreCompetencies:["Application Architecture","Cloud Architecture (Azure, AWS)","Microservices & Distributed Systems","Full Stack Development (.NET, React, Python)","DevOps & CI/CD Implementations","API Design & Integration","Security Implementation","Team Leadership & Agile Methodologies"],skills:[".NET 8","C#","REACT","MS SQL","WEB.API","NEXT.JS","ASP.NET MVC","AZURE","TYPESCRIPT","R","JQUERY","BOOTSTRAP","ANGULAR","CSS","HTML","GIT","NODEJS","PYTHON 3","AZURE APP SERVICE","AZURE FUNCTIONS","AZURE SQL DATABASE","AZURE DEVOPS & PIPELINES","POWER BI","ARM TEMPLATES","TERRAFORM","MULESOFT","AZURE ACTIVE DIRECTORY","AZURE AD B2C","AZURE KEY VAULT","AZURE MONITOR","AZURE APPLICATION INSIGHTS","AZURE API MANAGEMENT","DYNAMICS 365","AZURE BLOB STORAGE","AZURE KUBERNETES SERVICE(AKS)","AZURE VM","SSIS","WCF"],navbar:[{title:"About",href:"#about"},{title:"Experience",href:"#experience"},{title:"Education",href:"#education"},{title:"Core Competencies",href:"#core-competencies"},{title:"Skills",href:"#skills"},{title:"Certifications",href:"#certifications"}],contact:{email:"<EMAIL>",tel:"+16477715300",social:[{name:"LinkedIn",url:"https://www.linkedin.com/in/javian-picardo/",icon:s.Z},{name:"GitHub",url:"https://github.com/JavianDev",icon:o.Z},{name:"Twitter",url:"https://x.com/javian_picardo",icon:l.Z},{name:"YouTube",url:"https://www.youtube.com/@javianpicardo",icon:c.Z},{name:"Email",url:"mailto:<EMAIL>",icon:d.Z}]},work:[{company:"Ness USA Ltd. (CIBC Client)",title:"Principal Software Engineer | Application Architect",location:"Toronto, Ontario",start:"12/2024",end:"03/2025",description:"Working on shared data services project for internal reporting services. Architected and implemented a comprehensive shared data services portal using React and Azure App Services. Implemented advanced React.JS features including extensive use of Web Hooks (useState, useEffect, useContext, useReducer) for efficient state management across enterprise applications. Leveraged modern state management solutions within React, including Context API and Redux for managing global application state in complex scenarios.Created comprehensive architecture blueprint for shared data services including system context diagrams and component interactions. Established technical standards and design patterns for consistent implementation across banking applications. Designed secure API gateway architecture with centralized authentication, rate limiting, and monitoring capabilities. Developed robust Azure Functions APIs with C# & .NET 8, focusing on Open Banking authentication and authorization mechanisms. Engineered scalable microservices infrastructure with advanced security protocols ensuring compliance with banking industry standards.",badges:["React","Azure",".NET 8","C#","Microservices","Architecture"],logoUrl:"https://ui-avatars.com/api/?name=Ness&background=0D9488&color=fff",href:"#"},{company:"PortfolioXpressWay",title:"Lead Application Developer | Application Architect",location:"Richmond Hill, Ontario",start:"07/2022",end:"12/2024",description:"Portfolioxpressway provides investment professionals, portfolio managers with a fully digital 360-degree view of investor assets, no matter their location or nature, as well as the tools to source and recommend the very best investment opportunities. Collaborated directly with CTO to architect a comprehensive enterprise-grade solution addressing complex investment portfolio management requirements achieving SOC2. Partnered with program managers, business analysts, and scrum masters to translate business requirements into scalable technical architecture. Worked with UI/UX teams to create an intuitive portfolio management portal aligned with architectural capabilities. Orchestrated team expansion from 4 to 10 developers, establishing robust DevOps processes with CI/CD pipelines and testing frameworks. Designed and implemented a comprehensive investor asset portfolio website using cutting-edge technologies: Frontend: React, Next.JS, Storybook, Ant Design. Backend: C#.NET Core,.NET 7, Django, Python 3. Cloud Infrastructure: Azure ecosystem. Created high-performance React components with optimized rendering cycles, significantly improving user experience in data-intensive portfolio management interfaces & utilized Next.JS with React to deliver server-side rendering capabilities, improving initial load performance and SEO capabilities for client-facing applications. Developed centralized authentication and authorization framework using Azure B2C with custom policies for enhanced security. Created microservices using Django & Python 3 from Databricks imported data. Engineered complex microservices integrating Databricks data with Django APIs. Implemented advanced Azure cloud infrastructure incorporating multiple services: Azure App Service, Azure Functions, Azure SQL Database, Azure Storage, Azure DevOps & Pipelines, Azure Virtual Machines (VMs), Azure Active Directory (Azure AD), Azure Logic Apps, Azure B2C Custom Policies, Dynamics 365 Customer Service integration.",badges:["React","Next.JS","Python","Azure","Django","Architecture"],logoUrl:"https://ui-avatars.com/api/?name=PX&background=2563EB&color=fff",href:"#"},{company:"Actex Learning",title:"Lead Full Stack .NET Developer | Application Architect",location:"Portsmouth, NH (USA)",start:"04/2021",end:"07/2022",description:"Actuarial Learning helps actuarial candidates offering product GOAL; an Actuarial Exam Practice tool/textbooks, and the online learning platform Actuarial University used by many actuaries and students to help them pass their SOA and CAS Exams. Architected scalable cloud-based infrastructure leveraging Azure App Services with microservices architecture to modularize the actuarial exam platform for better scalability and maintenance. Implemented/Designed multi-platform solutions using: C# .NET 6, ASP.NET MVC Core/MVC 5, jQuery, Bootstrap, Azure App Services, R Language for custom API interfaces, Azure, SQL Server. Created innovative Azure Functions with custom R language handlers for statistical processing of actuarial data. Designed and implemented comprehensive security framework including identity management, role-based access control, and data encryption. Developed comprehensive web applications for actuarial exam preparation platform. Enhanced online learning platform increasing user engagement and exam preparation effectiveness.",badges:[".NET 6","Azure","R","SQL Server","Architecture"],logoUrl:"https://ui-avatars.com/api/?name=Actex&background=DC2626&color=fff",href:"#"},{company:"Xchanging Solutions [A DXC Technology Company]. Client: Liberty Mutual.",title:"Principal Software Engineer",location:"Dover, NH (USA)",start:"10/2015",end:"04/2021",description:"Liberty Mutual is a global insurance company, headquartered in Boston, MA, that offers a wide range of insurance products and services, including personal and commercial lines, and is known for its global presence and focus on risk mitigation. Created comprehensive application architecture documentation including system diagrams, data flow models, and integration specifications. Led technology stack modernization initiative, transitioning from monolithic applications to cloud-native microservices. Designed hybrid cloud architecture leveraging both Azure and AWS services for optimal performance and cost efficiency. Developed microservices/web applications using: C#, ASP.NET MVC, Web API, HTML,CSS, J-Query, Bootstrap, Azure & SQL Server & AWS cloud infrastructure. Created microservices in MuleSoft over Any Point Studio, implementing API-led connectivity approach. Successfully delivered critical SSIS project, demonstrating technical leadership and project management skills.",badges:[".NET","Azure","AWS","MuleSoft","Architecture"],logoUrl:"https://ui-avatars.com/api/?name=DXC&background=4F46E5&color=fff",href:"#"},{company:"(SYNTEL Inc.) Client: Humana",title:"Tech Lead/Senior Software .NET Engineer",location:"Louisville, KY (USA)",start:"05/2011",end:"08/2015",description:"Humana Inc is a US-based for-profit health insurance company offering healthcare benefits to individuals and employer groups. Developed Medicaid enrollment portal using C#.NET, ASP.NET, ASP.NET MVC, HTML, CSS, JQuery, Bootstrap, Angular and SQL Server. Led technical implementation of Obamacare healthcare enrollment systems. Created comprehensive reporting solutions: SQL Server Reporting Services (SSRS) SQL Server Integration Services (SSIS). Led technical implementation of Obamacare healthcare enrollment systems.",badges:[".NET","Angular","SQL Server","SSRS","SSIS"],logoUrl:"https://ui-avatars.com/api/?name=SYNTEL&background=059669&color=fff",href:"#"},{company:"Acty System India Pvt Ltd",title:"Senior Software Engineer",location:"Mumbai, India",start:"08/2009",end:"04/2011",description:"Acty System India Pvt. Ltd. is a Mumbai-based IT services company combining Indian engineering expertise with Japanese business management. Developed innovative software solutions for Japanese and Indian clients. Created Diakanri (Diamond Process Management System), a complex Windows Forms application using Observer/Singleton Design Pattern, C#, ASP.NET, Entity Framework, LINQ, and TFS for code management.",badges:[".NET","Entity Framework","LINQ","Windows Forms"],logoUrl:"https://ui-avatars.com/api/?name=Acty&background=7C3AED&color=fff",href:"#"},{company:"ISF MARITIME PVT LTD",title:"Software Engineer",location:"Mumbai, India",start:"12/2008",end:"08/2009",description:"Created training modules, assessment modules, chat room made available by particular client to their specified users to clear ORB (Oil Record Book) & Cadet Selection Test examination. (DLP) Distance Learning Program support containing e-learning contents created by (ISF) company to the DLP users. Design, development, coding and testing in C#, ASP.NET. Developed critical training and assessment modules for maritime industry. Designed database schemas and stored procedures in SQL Server. Created e-learning content and distance learning program support.",badges:[".NET","SQL Server","C#","ASP.NET"],logoUrl:"https://ui-avatars.com/api/?name=ISF&background=0EA5E9&color=fff",href:"#"}],education:[{school:"University of Mumbai",degree:"Bachelor's in Computer Engineering",start:"2004",end:"2008",location:"Mumbai, India",logoUrl:"https://ui-avatars.com/api/?name=UM&background=1E40AF&color=fff"}],certifications:[{title:"Microsoft Certified: Azure Developer Associate",organization:"Microsoft",date:"01/2023 - Present",id:"MS-AZ204",logoUrl:"https://ui-avatars.com/api/?name=MS&background=0078D4&color=fff"},{title:"Certified Ethical Hacker(CEH)-v6",organization:"EC Council",date:"06/2010 - 11/2010",id:"ECC944432",logoUrl:"https://ui-avatars.com/api/?name=EC&background=991B1B&color=fff"},{title:"Core Java",organization:"Rajesh Patkar Institute of Technology",date:"06/2008 - 10/2008",logoUrl:"https://ui-avatars.com/api/?name=RPIT&background=065F46&color=fff"}],languages:[{name:"English",level:"Native or Bilingual Proficiency"},{name:"Hindi",level:"Native or Bilingual Proficiency"}],interests:["Pool","Snooker","Photography"]};var p=a(10090);let u=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("div",{ref:t,className:(0,p.cn)("rounded-lg border border-slate-200 bg-white text-slate-950 shadow-sm dark:border-slate-800 dark:bg-slate-950 dark:text-slate-50",a),...i})});u.displayName="Card",i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("div",{ref:t,className:(0,p.cn)("flex flex-col space-y-1.5 p-6",a),...i})}).displayName="CardHeader",i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("h3",{ref:t,className:(0,p.cn)("text-2xl font-semibold leading-none tracking-tight",a),...i})}).displayName="CardTitle",i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("p",{ref:t,className:(0,p.cn)("text-sm text-slate-500 dark:text-slate-400",a),...i})}).displayName="CardDescription",i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("div",{ref:t,className:(0,p.cn)("p-6 pt-0",a),...i})}).displayName="CardContent",i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("div",{ref:t,className:(0,p.cn)("flex items-center p-6 pt-0",a),...i})}).displayName="CardFooter";let g=(0,a(90535).j)("inline-flex items-center rounded-full border border-slate-200 px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-slate-950 focus:ring-offset-2 dark:border-slate-800 dark:focus:ring-slate-300",{variants:{variant:{default:"border-transparent bg-slate-900 text-slate-50 hover:bg-slate-900/80 dark:bg-slate-50 dark:text-slate-900 dark:hover:bg-slate-50/80",secondary:"border-transparent bg-slate-100 text-slate-900 hover:bg-slate-100/80 dark:bg-slate-800 dark:text-slate-50 dark:hover:bg-slate-800/80",destructive:"border-transparent bg-red-500 text-slate-50 hover:bg-red-500/80 dark:bg-red-900 dark:text-slate-50 dark:hover:bg-red-900/80",outline:"text-slate-950 dark:text-slate-50"}},defaultVariants:{variant:"default"}});function h(e){let{className:t,variant:a,...i}=e;return(0,r.jsx)("div",{className:(0,p.cn)(g({variant:a}),t),...i})}var f=a(73966),x=a(26606),b=a(61188),v=a(66840),A="Avatar",[E,S]=(0,f.b)(A),[N,y]=E(A),w=i.forwardRef((e,t)=>{let{__scopeAvatar:a,...n}=e,[s,o]=i.useState("idle");return(0,r.jsx)(N,{scope:a,imageLoadingStatus:s,onImageLoadingStatusChange:o,children:(0,r.jsx)(v.WV.span,{...n,ref:t})})});w.displayName=A;var C="AvatarImage",k=i.forwardRef((e,t)=>{let{__scopeAvatar:a,src:n,onLoadingStatusChange:s=()=>{},...o}=e,l=y(C,a),c=function(e,t){let[a,r]=i.useState("idle");return(0,b.b)(()=>{if(!e){r("error");return}let a=!0,i=new window.Image,n=e=>()=>{a&&r(e)};return r("loading"),i.onload=n("loaded"),i.onerror=n("error"),i.src=e,t&&(i.referrerPolicy=t),()=>{a=!1}},[e,t]),a}(n,o.referrerPolicy),d=(0,x.W)(e=>{s(e),l.onImageLoadingStatusChange(e)});return(0,b.b)(()=>{"idle"!==c&&d(c)},[c,d]),"loaded"===c?(0,r.jsx)(v.WV.img,{...o,ref:t,src:n}):null});k.displayName=C;var j="AvatarFallback",P=i.forwardRef((e,t)=>{let{__scopeAvatar:a,delayMs:n,...s}=e,o=y(j,a),[l,c]=i.useState(void 0===n);return i.useEffect(()=>{if(void 0!==n){let e=window.setTimeout(()=>c(!0),n);return()=>window.clearTimeout(e)}},[n]),l&&"loaded"!==o.imageLoadingStatus?(0,r.jsx)(v.WV.span,{...s,ref:t}):null});P.displayName=j;let L=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)(w,{ref:t,className:(0,p.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",a),...i})});L.displayName=w.displayName;let R=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)(k,{ref:t,className:(0,p.cn)("aspect-square h-full w-full",a),...i})});R.displayName=k.displayName;let T=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)(P,{ref:t,className:(0,p.cn)("flex h-full w-full items-center justify-center rounded-full bg-slate-100 dark:bg-slate-800",a),...i})});T.displayName=P.displayName;var D=()=>{let[e,t]=(0,i.useState)(!1);return(0,r.jsx)("div",{className:"container relative mx-auto scroll-my-12 overflow-auto px-4 py-8",children:(0,r.jsxs)("div",{className:"max-w-7xl",children:[(0,r.jsxs)("div",{className:"max-w-6xl",children:[(0,r.jsxs)("div",{className:"text-left mb-8 max-w-6xl px-0",children:[(0,r.jsx)("h1",{className:"text-3xl md:text-4xl font-bold tracking-tight mb-3 text-blue-600",children:m.name}),(0,r.jsx)("h2",{className:"text-xl md:text-2xl font-semibold tracking-tight text-zinc-700 mb-4",children:m.description}),(0,r.jsx)("div",{className:"mt-4 text-base text-black dark:text-white leading-normal",children:(0,r.jsx)("p",{children:m.summary})})]}),(0,r.jsxs)("div",{className:"mt-6 flex flex-wrap gap-4 mb-8 max-w-6xl px-0",children:[Object.entries(m.contact.social).map(e=>{let[t,a]=e;return(0,r.jsxs)(n.default,{href:a.url,className:"group flex items-center gap-2 rounded-full bg-zinc-100 px-4 py-1.5 h-8 text-sm font-medium text-zinc-800 transition hover:bg-zinc-200",target:"_blank",rel:"noopener noreferrer",children:[(0,r.jsx)(a.icon,{className:"h-5 w-5 text-blue-600"}),(0,r.jsx)("span",{children:a.name})]},t)}),(0,r.jsxs)("a",{href:"/doc/CVJavianPicardo2025.pdf",download:"CVJavianPicardo2025.pdf",className:"inline-flex items-center gap-2 rounded-full bg-[#006fee] px-5 py-1.5 h-8 text-sm font-medium text-white transition hover:bg-blue-700",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"Download CV"]})]})]}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsxs)("div",{className:"flex flex-col gap-12 max-w-6xl",children:[(0,r.jsxs)("section",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold tracking-tight text-black dark:text-white border-b-2 border-blue-600 pb-2 inline-block",children:"Work Experience"}),(0,r.jsx)("div",{className:"mt-6 space-y-4",children:m.work.map(e=>(0,r.jsxs)(u,{className:"p-6 hover:shadow-lg transition-shadow duration-300 border-l-4 border-blue-600",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-4",children:[(0,r.jsxs)(L,{className:"h-12 w-12",children:[(0,r.jsx)(R,{src:e.logoUrl,alt:e.company,onError:e=>{e.target.style.display="none"}}),(0,r.jsx)(T,{className:"bg-gray-100 text-gray-600",children:e.company[0]})]}),(0,r.jsxs)("div",{className:"flex-1 mt-2 sm:mt-0",children:[(0,r.jsx)("h3",{className:"font-semibold text-black dark:text-white",children:e.title}),(0,r.jsxs)("div",{className:"mb-1",children:[(0,r.jsx)(n.default,{href:e.href,className:"text-sm text-black dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 block mb-1",children:e.company}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1 mt-1",children:e.badges.map(e=>(0,r.jsx)(h,{variant:"secondary",className:"text-xs",children:e},e))})]}),(0,r.jsxs)("p",{className:"text-sm text-zinc-500",children:[e.start," - ",e.end," • ",e.location]})]})]}),(0,r.jsxs)("div",{className:"mt-4 text-sm text-black dark:text-gray-300",children:[(0,r.jsx)("p",{className:"mb-3",children:e.description.replace(/ASP\./g,"ASP_PLACEHOLDER").replace(/C#\./g,"CSHARP_PLACEHOLDER").replace(/Pvt\./g,"PVT_PLACEHOLDER").replace(/Ltd\./g,"LTD_PLACEHOLDER").replace(/\.NET/g,"NET_PLACEHOLDER").split(".").slice(0,1).join(".").trim().replace(/ASP_PLACEHOLDER/g,"ASP.").replace(/CSHARP_PLACEHOLDER/g,"C#.").replace(/PVT_PLACEHOLDER/g,"Pvt.").replace(/LTD_PLACEHOLDER/g,"Ltd.").replace(/NET_PLACEHOLDER/g,".NET")+"."}),(0,r.jsx)("ul",{className:"list-disc pl-5 space-y-1",children:e.description.replace(/ASP\./g,"ASP_PLACEHOLDER").replace(/C#\./g,"CSHARP_PLACEHOLDER").replace(/Pvt\./g,"PVT_PLACEHOLDER").replace(/Ltd\./g,"LTD_PLACEHOLDER").replace(/\.NET/g,"NET_PLACEHOLDER").split(".").slice(1).filter(e=>e.trim()).map((e,t)=>{let a=e.trim().replace(/ASP_PLACEHOLDER/g,"ASP.").replace(/CSHARP_PLACEHOLDER/g,"C#.").replace(/PVT_PLACEHOLDER/g,"Pvt.").replace(/LTD_PLACEHOLDER/g,"Ltd.").replace(/NET_PLACEHOLDER/g,".NET");return(0,r.jsx)("li",{className:"max-w-4xl",children:a.endsWith(".")?a:"".concat(a,".")},t)})})]})]},e.company))})]}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold tracking-tight text-black dark:text-white border-b-2 border-blue-600 pb-2 inline-block",children:"Education"}),(0,r.jsx)("div",{className:"mt-6 space-y-4",children:m.education.map(e=>(0,r.jsx)(u,{className:"p-6 hover:shadow-lg transition-shadow duration-300 border-l-4 border-green-600",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-4",children:[(0,r.jsxs)(L,{className:"h-12 w-12",children:[(0,r.jsx)(R,{src:e.logoUrl,alt:e.school,onError:e=>{e.target.style.display="none"}}),(0,r.jsx)(T,{className:"bg-gray-100 text-gray-600",children:e.school[0]})]}),(0,r.jsxs)("div",{className:"mt-2 sm:mt-0",children:[(0,r.jsx)("h3",{className:"font-semibold",children:e.school}),(0,r.jsx)("p",{className:"text-sm text-zinc-600",children:e.degree}),(0,r.jsxs)("p",{className:"text-sm text-zinc-500",children:[e.start," - ",e.end," • ",e.location]})]})]})},e.school))})]}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold tracking-tight text-black dark:text-white border-b-2 border-blue-600 pb-2 inline-block",children:"Core Competencies"}),(0,r.jsx)("div",{className:"mt-6 flex flex-wrap gap-2",children:m.coreCompetencies.map(e=>(0,r.jsx)(h,{variant:"secondary",className:"bg-blue-100 text-blue-800 hover:bg-blue-200 px-3 py-1.5 text-sm",children:e},e))})]}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold tracking-tight text-black dark:text-white border-b-2 border-blue-600 pb-2 inline-block",children:"Skills"}),(0,r.jsx)("div",{className:"mt-6 flex flex-wrap gap-2",children:m.skills.map(e=>(0,r.jsx)(h,{variant:"secondary",className:"px-3 py-1.5 text-sm",children:e},e))})]}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold tracking-tight text-black dark:text-white border-b-2 border-blue-600 pb-2 inline-block",children:"Certifications"}),(0,r.jsx)("div",{className:"mt-6 space-y-4",children:m.certifications.map(e=>(0,r.jsx)(u,{className:"p-6 hover:shadow-lg transition-shadow duration-300 border-l-4 border-purple-600",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-4",children:[(0,r.jsxs)(L,{className:"h-12 w-12",children:[(0,r.jsx)(R,{src:e.logoUrl,alt:e.organization,onError:e=>{e.target.style.display="none"}}),(0,r.jsx)(T,{className:"bg-gray-100 text-gray-600",children:e.organization[0]})]}),(0,r.jsxs)("div",{className:"mt-2 sm:mt-0",children:[(0,r.jsx)("h3",{className:"font-semibold",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-zinc-600",children:e.organization}),(0,r.jsx)("p",{className:"text-sm text-zinc-500",children:e.date}),e.id&&(0,r.jsxs)("p",{className:"text-sm text-zinc-500",children:["ID: ",e.id]})]})]})},e.title))})]}),m.interests&&m.interests.length>0&&(0,r.jsxs)("section",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold tracking-tight text-black dark:text-white border-b-2 border-blue-600 pb-2 inline-block",children:"Interests"}),(0,r.jsx)("div",{className:"mt-6 flex flex-wrap gap-2",children:m.interests.map(e=>(0,r.jsx)(h,{variant:"secondary",className:"bg-purple-100 text-purple-800 hover:bg-purple-200 px-3 py-1.5 text-sm",children:e},e))})]})]})})]})})}},16008:function(e,t,a){"use strict";var r=a(57437),i=a(51928);t.default=e=>{let{type:t,className:a="",style:n={},children:s}=e,o=(()=>{switch(t){case"multiplex-vertical":return{position:"sidebar",slot:"5891334939",format:"autorelaxed"};case"in-feed":return{position:"in-feed",slot:"6889555982",format:"fluid"};case"in-article":return{position:"in-article",slot:"2944836887",format:"in-article"};default:return{position:"horizontal",slot:"4454393519",format:"auto"}}})();return(0,r.jsxs)("div",{className:"ad-manager-container ".concat("multiplex-vertical"===t?"":"w-full max-w-[90rem] mx-auto px-4 sm:px-6 lg:px-8"," ").concat(a),style:n,children:[(0,r.jsx)(i.Z,{position:o.position,slot:o.slot,format:o.format}),s]})}},51928:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});var r=a(57437),i=a(2265),n=e=>{let{slot:t="4454393519",format:a="auto",style:n={},className:s="",responsive:o=!0,layout:l="in-article",containerClassName:c=""}=e,d=(0,i.useRef)(null),[m,p]=(0,i.useState)(!1);return((0,i.useEffect)(()=>{p(!0)},[]),(0,i.useEffect)(()=>{if(m)try{{let e=()=>{if(window.adsbygoogle)try{(window.adsbygoogle=window.adsbygoogle||[]).push({})}catch(e){console.error("Error pushing ad:",e)}else setTimeout(e,500)};setTimeout(e,1e3)}o&&d.current&&(d.current.style.minWidth="300px",d.current.style.width="100%")}catch(e){console.error("Error loading Google Ads:",e)}},[m,o]),m)?(0,r.jsx)("div",{className:"".concat((()=>{switch(l){case"sidebar":return"adsense-container adsense-container-sidebar";case"horizontal":return"adsense-container-horizontal";default:return c||""}})()," ").concat(s),children:(0,r.jsx)("ins",{ref:d,className:"adsbygoogle",style:(()=>{let e={display:"block",textAlign:"left",background:"transparent",...n};switch(l){case"sidebar":return{...e,width:"300px",height:"600px",margin:"0 auto",maxWidth:"100%"};case"horizontal":return{...e,width:"100%",minHeight:"280px"};case"in-article":return{...e,minHeight:"280px"};default:return e}})(),"data-ad-client":"ca-pub-8317058180798912","data-ad-slot":t,"data-ad-format":a,"data-full-width-responsive":o})}):(0,r.jsx)("div",{className:"ad-placeholder ".concat(s),style:{minHeight:"sidebar"===l?"600px":"280px",...n}})},s=e=>{let{position:t,slot:a,className:s="",style:o={},format:l}=e,[c,d]=(0,i.useState)(!1);return((0,i.useEffect)(()=>{d(!0)},[]),c)?(0,r.jsx)(n,{slot:a||("sidebar"===t?"5891334939":"4454393519"),format:(()=>{if(l)return l;switch(t){case"sidebar":return"autorelaxed";case"horizontal":default:return"auto";case"in-article":return"in-article";case"in-feed":return"fluid"}})(),responsive:"sidebar"!==t,layout:t,className:s,style:o}):(0,r.jsx)("div",{className:"ad-placeholder ".concat(s),style:{minHeight:"sidebar"===t?"600px":"280px",...o}})}}},function(e){e.O(0,[337,505,972,563,145,203,64,330,150,832,971,117,744],function(){return e(e.s=79234)}),_N_E=e.O()}]);