/* eslint-disable @next/next/no-img-element */
'use client'

import { Button } from '@/components/ui/button'
import { useClerk, useUser } from '@clerk/nextjs'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

// A simple button to sign in with Clerk.
// It automatically redirects the user to callbackUrl (config.auth.callbackUrl) after login,
// which is normally a private page for users to manage their accounts.
// If the user is already logged in, it will show their profile picture & redirect them to callbackUrl immediately.
const ButtonSignin = ({
	text = 'Login/SignUp',
	extraStyle,
}: {
	text?: string
	extraStyle?: string
}) => {
	const router = useRouter()
	const { isSignedIn, user } = useUser()
	const { openSignIn, signOut } = useClerk()

	const handleClick = () => {
		if (isSignedIn) {
			router.push('/')
		} else {
			openSignIn({
				// Optionally, you can specify sign-in options here
				redirectUrl: '/', //'/dashboard'
			})
		}
	}

	if (isSignedIn && user) {
		return (
			<div className="relative group">
				<div
					className={`flex items-center gap-2 cursor-pointer rounded-full bg-[#006fee] text-white px-4 py-1.5 text-sm font-medium hover:bg-blue-700 transition-all duration-300 ${extraStyle ? extraStyle : ''}`}
				>
					{user.hasImage ? (
						<Image
							src={user.imageUrl}
							alt={user.firstName || 'Account'}
							className='w-6 h-6 rounded-full shrink-0'
							referrerPolicy='no-referrer'
							width={24}
							height={24}
						/>
					) : (
						<span className='w-6 h-6 bg-base-300 flex justify-center items-center rounded-full shrink-0'>
							{user.firstName
								? user.firstName.charAt(0)
								: user.primaryEmailAddress?.emailAddress || 'A'}
						</span>
					)}
					{user.firstName || user.primaryEmailAddress?.emailAddress || 'Account'}
				</div>

				{/* Dropdown Menu */}
				<div className="absolute right-0 mt-2 w-48 bg-white dark:bg-[#1E232C] border border-gray-200 dark:border-[#373C53] rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
					<div className="py-1">
						<Link href="/" className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-[#373C53]">
							Home
						</Link>
						{/* <Link href="https://accounts.clerk.com/account" target="_blank" rel="noopener noreferrer" className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-[#373C53]">
							Manage Profile
						</Link> */}
						<div className="border-t border-gray-200 dark:border-[#373C53] my-1"></div>
						<button
							className="block w-full text-left px-4 py-2 text-sm text-red-500 hover:bg-gray-100 dark:hover:bg-[#373C53]"
							onClick={() => signOut(() => router.push('/'))}
						>
							Logout
						</button>
					</div>
				</div>
			</div>
		)
	}

	return (
		<Button
			className={`bg-[#006fee] border-none transition-all duration-300 rounded-full px-5 py-1.5 h-8 text-sm font-medium hover:bg-blue-700 ${
				extraStyle ? extraStyle : ''
			}`}
			onClick={handleClick}
		>
			{text}
		</Button>
	)
}

export default ButtonSignin
