(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[288],{2690:(e,t,r)=>{"use strict";r.d(t,{mx:()=>n,vX:()=>s});var i=r(9985);let n=e=>{if(!(e instanceof Error)||!("message"in e))return!1;let{message:t}=e,r=t.toLowerCase(),i=r.includes("dynamic server usage"),n=r.includes("this page needs to bail out of prerendering");return/Route .*? needs to bail out of prerendering at this point because it used .*?./.test(t)||i||n},s=()=>{try{let{headers:e}=r(473);return new i.Im("https://placeholder.com",{headers:e()})}catch(e){if(e&&n(e))throw e;throw Error(`Clerk: auth() and currentUser() are only supported in App Router (/app directory).
If you're using /pages, try getAuth() instead.
Original error: ${e}`)}}},6068:(e,t,r)=>{"use strict";r.d(t,{T5:()=>d,Gn:()=>a,yK:()=>u,dr:()=>l,lo:()=>f,NM:()=>h,Am:()=>c,nE:()=>g,Cn:()=>o,O0:()=>p,Dt:()=>m,w4:()=>v,CY:()=>y});var i=r(1696),n=r(3967);r(3369);var s=r(1657);process.env.NEXT_PUBLIC_CLERK_JS_VERSION,process.env.NEXT_PUBLIC_CLERK_JS_URL;let a=process.env.CLERK_API_VERSION||"v1",o=process.env.CLERK_SECRET_KEY||"",c="pk_test_ZGlzY3JldGUtaGFyZS03My5jbGVyay5hY2NvdW50cy5kZXYk",l=process.env.CLERK_ENCRYPTION_KEY||"",d=process.env.CLERK_API_URL||(e=>{var t;let r=null==(t=(0,i.nQ)(e))?void 0:t.frontendApi;return(null==r?void 0:r.startsWith("clerk."))&&n.mv.some(e=>null==r?void 0:r.endsWith(e))?n.Xv:n.iF.some(e=>null==r?void 0:r.endsWith(e))?n.Fo:n.cM.some(e=>null==r?void 0:r.endsWith(e))?n.Iq:n.Xv})(c),u=process.env.NEXT_PUBLIC_CLERK_DOMAIN||"",h=process.env.NEXT_PUBLIC_CLERK_PROXY_URL||"",f=(0,s.fQ)(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE)||!1,p="/sign-in",m="/sign-up",g={name:"@clerk/nextjs",version:"5.7.5",environment:"production"},y=(0,s.fQ)(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED),v=(0,s.fQ)(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG)},1302:(e,t,r)=>{"use strict";r.d(t,{Cm:()=>a,I2:()=>s,It:()=>n,PL:()=>i});let i=()=>n("getAuth"),n=(e="auth")=>`Clerk: ${e}() was called but Clerk can't detect usage of clerkMiddleware() (or the deprecated authMiddleware()). Please ensure the following:
-  clerkMiddleware() (or the deprecated authMiddleware()) is used in your Next.js Middleware.
- Your Middleware matcher is configured to match this route or page.
- If you are using the src directory, make sure the Middleware file is inside of it.

For more details, see https://clerk.com/docs/quickstarts/nextjs
`,s="Clerk: Unable to verify request, this usually means the Clerk middleware did not run. Ensure Clerk's middleware is properly integrated and matches the current route. For more information, see: https://clerk.com/docs/nextjs/middleware. (code=auth_signature_invalid)",a="Clerk: Unable to decrypt request data, this usually means the encryption key is invalid. Ensure the encryption key is properly set. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)"},8802:(e,t,r)=>{"use strict";r.d(t,{ii:()=>p,om:()=>m,to:()=>h,Pg:()=>f});var i=r(6449);r(3369),r(9241);var n=r(8945),s=r.n(n),a=r(432),o=r.n(a),c=r(1245),l=r.n(c);r(9985);var d=r(6068),u=r(1302);function h(e,t){var r;return((r=i._G.Attributes[t])in e?e[r]:void 0)||f(e,i._G.Headers[t])}function f(e,t){var r,i;return!function(e){try{let{headers:t,nextUrl:r,cookies:i}=e||{};return"function"==typeof(null==t?void 0:t.get)&&"function"==typeof(null==r?void 0:r.searchParams.get)&&"function"==typeof(null==i?void 0:i.get)}catch(e){return!1}}(e)?e.headers[t]||e.headers[t.toLowerCase()]||(null==(i=null==(r=e.socket)?void 0:r._httpMessage)?void 0:i.getHeader(t)):e.headers.get(t)}function p(e,t,r){if(!r||l()(e,t).toString()!==r)throw Error(u.I2)}function m(e){if(!e)return{};try{let t=s().decrypt(e,d.dr||d.Cn).toString(o());return JSON.parse(t)}catch(e){throw Error(u.Cm)}}},8847:(e,t)=>{"use strict";function r(e,t,r){do{var i=e.charCodeAt(t);if(32!==i&&9!==i)return t}while(++t<r);return r}function i(e,t,r){for(;t>r;){var i=e.charCodeAt(--t);if(32!==i&&9!==i)return t+1}return r}function n(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}t.Q=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");var s={},a=e.length,o=a-2;if(o<0)return s;var c=t&&t.decode||n,l=0,d=0,u=0;do{if(-1===(d=e.indexOf("=",l)))break;if(-1===(u=e.indexOf(";",l)))u=a;else if(d>u){l=e.lastIndexOf(";",d-1)+1;continue}var h=r(e,l,d),f=i(e,d,h),p=e.slice(h,f);if(void 0===s[p]){var m=r(e,d+1,u),g=i(e,u,m);34===e.charCodeAt(m)&&34===e.charCodeAt(g-1)&&(m++,g--);var y=e.slice(m,g);s[p]=function(e,t){try{return t(e)}catch(t){return e}}(y,c)}l=u+1}while(l<o);return s},Object.prototype.toString},8945:function(e,t,r){var i;i=function(e){var t,r,i,n,s,a,o,c,l,d,u,h,f,p;return t=e.lib.BlockCipher,r=e.algo,i=[],n=[],s=[],a=[],o=[],c=[],l=[],d=[],u=[],h=[],function(){for(var e=[],t=0;t<256;t++)t<128?e[t]=t<<1:e[t]=t<<1^283;for(var r=0,f=0,t=0;t<256;t++){var p=f^f<<1^f<<2^f<<3^f<<4;p=p>>>8^255&p^99,i[r]=p,n[p]=r;var m=e[r],g=e[m],y=e[g],v=257*e[p]^16843008*p;s[r]=v<<24|v>>>8,a[r]=v<<16|v>>>16,o[r]=v<<8|v>>>24,c[r]=v;var v=16843009*y^65537*g^257*m^16843008*r;l[p]=v<<24|v>>>8,d[p]=v<<16|v>>>16,u[p]=v<<8|v>>>24,h[p]=v,r?(r=m^e[e[e[y^m]]],f^=e[e[f]]):r=f=1}}(),f=[0,1,2,4,8,16,32,64,128,27,54],p=r.AES=t.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e,t=this._keyPriorReset=this._key,r=t.words,n=t.sigBytes/4,s=((this._nRounds=n+6)+1)*4,a=this._keySchedule=[],o=0;o<s;o++)o<n?a[o]=r[o]:(e=a[o-1],o%n?n>6&&o%n==4&&(e=i[e>>>24]<<24|i[e>>>16&255]<<16|i[e>>>8&255]<<8|i[255&e]):e=(i[(e=e<<8|e>>>24)>>>24]<<24|i[e>>>16&255]<<16|i[e>>>8&255]<<8|i[255&e])^f[o/n|0]<<24,a[o]=a[o-n]^e);for(var c=this._invKeySchedule=[],p=0;p<s;p++){var o=s-p;if(p%4)var e=a[o];else var e=a[o-4];p<4||o<=4?c[p]=e:c[p]=l[i[e>>>24]]^d[i[e>>>16&255]]^u[i[e>>>8&255]]^h[i[255&e]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,s,a,o,c,i)},decryptBlock:function(e,t){var r=e[t+1];e[t+1]=e[t+3],e[t+3]=r,this._doCryptBlock(e,t,this._invKeySchedule,l,d,u,h,n);var r=e[t+1];e[t+1]=e[t+3],e[t+3]=r},_doCryptBlock:function(e,t,r,i,n,s,a,o){for(var c=this._nRounds,l=e[t]^r[0],d=e[t+1]^r[1],u=e[t+2]^r[2],h=e[t+3]^r[3],f=4,p=1;p<c;p++){var m=i[l>>>24]^n[d>>>16&255]^s[u>>>8&255]^a[255&h]^r[f++],g=i[d>>>24]^n[u>>>16&255]^s[h>>>8&255]^a[255&l]^r[f++],y=i[u>>>24]^n[h>>>16&255]^s[l>>>8&255]^a[255&d]^r[f++],v=i[h>>>24]^n[l>>>16&255]^s[d>>>8&255]^a[255&u]^r[f++];l=m,d=g,u=y,h=v}var m=(o[l>>>24]<<24|o[d>>>16&255]<<16|o[u>>>8&255]<<8|o[255&h])^r[f++],g=(o[d>>>24]<<24|o[u>>>16&255]<<16|o[h>>>8&255]<<8|o[255&l])^r[f++],y=(o[u>>>24]<<24|o[h>>>16&255]<<16|o[l>>>8&255]<<8|o[255&d])^r[f++],v=(o[h>>>24]<<24|o[l>>>16&255]<<16|o[d>>>8&255]<<8|o[255&u])^r[f++];e[t]=m,e[t+1]=g,e[t+2]=y,e[t+3]=v},keySize:8}),e.AES=t._createHelper(p),e.AES},e.exports=i(r(7728),r(12),r(7426),r(6030),r(6734))},6734:function(e,t,r){var i;i=function(e){var t,r,i,n,s,a,o,c,l,d,u,h,f,p,m,g,y;e.lib.Cipher||(r=(t=e.lib).Base,i=t.WordArray,n=t.BufferedBlockAlgorithm,(s=e.enc).Utf8,a=s.Base64,o=e.algo.EvpKDF,c=t.Cipher=n.extend({cfg:r.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,r){this.cfg=this.cfg.extend(r),this._xformMode=e,this._key=t,this.reset()},reset:function(){n.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function e(e){return"string"==typeof e?y:m}return function(t){return{encrypt:function(r,i,n){return e(i).encrypt(t,r,i,n)},decrypt:function(r,i,n){return e(i).decrypt(t,r,i,n)}}}}()}),t.StreamCipher=c.extend({_doFinalize:function(){return this._process(!0)},blockSize:1}),l=e.mode={},d=t.BlockCipherMode=r.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),u=l.CBC=function(){var e=d.extend();function t(e,t,r){var i,n=this._iv;n?(i=n,this._iv=void 0):i=this._prevBlock;for(var s=0;s<r;s++)e[t+s]^=i[s]}return e.Encryptor=e.extend({processBlock:function(e,r){var i=this._cipher,n=i.blockSize;t.call(this,e,r,n),i.encryptBlock(e,r),this._prevBlock=e.slice(r,r+n)}}),e.Decryptor=e.extend({processBlock:function(e,r){var i=this._cipher,n=i.blockSize,s=e.slice(r,r+n);i.decryptBlock(e,r),t.call(this,e,r,n),this._prevBlock=s}}),e}(),h=(e.pad={}).Pkcs7={pad:function(e,t){for(var r=4*t,n=r-e.sigBytes%r,s=n<<24|n<<16|n<<8|n,a=[],o=0;o<n;o+=4)a.push(s);var c=i.create(a,n);e.concat(c)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},t.BlockCipher=c.extend({cfg:c.cfg.extend({mode:u,padding:h}),reset:function(){c.reset.call(this);var e,t=this.cfg,r=t.iv,i=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=i.createEncryptor:(e=i.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==e?this._mode.init(this,r&&r.words):(this._mode=e.call(i,this,r&&r.words),this._mode.__creator=e)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4}),f=t.CipherParams=r.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}}),p=(e.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext,r=e.salt;return(r?i.create([1398893684,1701076831]).concat(r).concat(t):t).toString(a)},parse:function(e){var t,r=a.parse(e),n=r.words;return 1398893684==n[0]&&1701076831==n[1]&&(t=i.create(n.slice(2,4)),n.splice(0,4),r.sigBytes-=16),f.create({ciphertext:r,salt:t})}},m=t.SerializableCipher=r.extend({cfg:r.extend({format:p}),encrypt:function(e,t,r,i){i=this.cfg.extend(i);var n=e.createEncryptor(r,i),s=n.finalize(t),a=n.cfg;return f.create({ciphertext:s,key:r,iv:a.iv,algorithm:e,mode:a.mode,padding:a.padding,blockSize:e.blockSize,formatter:i.format})},decrypt:function(e,t,r,i){return i=this.cfg.extend(i),t=this._parse(t,i.format),e.createDecryptor(r,i).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),g=(e.kdf={}).OpenSSL={execute:function(e,t,r,n,s){if(n||(n=i.random(8)),s)var a=o.create({keySize:t+r,hasher:s}).compute(e,n);else var a=o.create({keySize:t+r}).compute(e,n);var c=i.create(a.words.slice(t),4*r);return a.sigBytes=4*t,f.create({key:a,iv:c,salt:n})}},y=t.PasswordBasedCipher=m.extend({cfg:m.cfg.extend({kdf:g}),encrypt:function(e,t,r,i){var n=(i=this.cfg.extend(i)).kdf.execute(r,e.keySize,e.ivSize,i.salt,i.hasher);i.iv=n.iv;var s=m.encrypt.call(this,e,t,n.key,i);return s.mixIn(n),s},decrypt:function(e,t,r,i){i=this.cfg.extend(i),t=this._parse(t,i.format);var n=i.kdf.execute(r,e.keySize,e.ivSize,t.salt,i.hasher);return i.iv=n.iv,m.decrypt.call(this,e,t,n.key,i)}}))},e.exports=i(r(7728),r(6030))},7728:function(e,t,r){var i;i=function(){var e=e||function(e,t){if("undefined"!=typeof window&&window.crypto&&(i=window.crypto),"undefined"!=typeof self&&self.crypto&&(i=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(i=globalThis.crypto),!i&&"undefined"!=typeof window&&window.msCrypto&&(i=window.msCrypto),!i&&void 0!==r.g&&r.g.crypto&&(i=r.g.crypto),!i)try{i=r(2480)}catch(e){}var i,n=function(){if(i){if("function"==typeof i.getRandomValues)try{return i.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof i.randomBytes)try{return i.randomBytes(4).readInt32LE()}catch(e){}}throw Error("Native crypto module could not be used to get secure random number.")},s=Object.create||function(){function e(){}return function(t){var r;return e.prototype=t,r=new e,e.prototype=null,r}}(),a={},o=a.lib={},c=o.Base={extend:function(e){var t=s(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},l=o.WordArray=c.extend({init:function(e,r){e=this.words=e||[],t!=r?this.sigBytes=r:this.sigBytes=4*e.length},toString:function(e){return(e||u).stringify(this)},concat:function(e){var t=this.words,r=e.words,i=this.sigBytes,n=e.sigBytes;if(this.clamp(),i%4)for(var s=0;s<n;s++){var a=r[s>>>2]>>>24-s%4*8&255;t[i+s>>>2]|=a<<24-(i+s)%4*8}else for(var o=0;o<n;o+=4)t[i+o>>>2]=r[o>>>2];return this.sigBytes+=n,this},clamp:function(){var t=this.words,r=this.sigBytes;t[r>>>2]&=4294967295<<32-r%4*8,t.length=e.ceil(r/4)},clone:function(){var e=c.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],r=0;r<e;r+=4)t.push(n());return new l.init(t,e)}}),d=a.enc={},u=d.Hex={stringify:function(e){for(var t=e.words,r=e.sigBytes,i=[],n=0;n<r;n++){var s=t[n>>>2]>>>24-n%4*8&255;i.push((s>>>4).toString(16)),i.push((15&s).toString(16))}return i.join("")},parse:function(e){for(var t=e.length,r=[],i=0;i<t;i+=2)r[i>>>3]|=parseInt(e.substr(i,2),16)<<24-i%8*4;return new l.init(r,t/2)}},h=d.Latin1={stringify:function(e){for(var t=e.words,r=e.sigBytes,i=[],n=0;n<r;n++){var s=t[n>>>2]>>>24-n%4*8&255;i.push(String.fromCharCode(s))}return i.join("")},parse:function(e){for(var t=e.length,r=[],i=0;i<t;i++)r[i>>>2]|=(255&e.charCodeAt(i))<<24-i%4*8;return new l.init(r,t)}},f=d.Utf8={stringify:function(e){try{return decodeURIComponent(escape(h.stringify(e)))}catch(e){throw Error("Malformed UTF-8 data")}},parse:function(e){return h.parse(unescape(encodeURIComponent(e)))}},p=o.BufferedBlockAlgorithm=c.extend({reset:function(){this._data=new l.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=f.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var r,i=this._data,n=i.words,s=i.sigBytes,a=this.blockSize,o=s/(4*a),c=(o=t?e.ceil(o):e.max((0|o)-this._minBufferSize,0))*a,d=e.min(4*c,s);if(c){for(var u=0;u<c;u+=a)this._doProcessBlock(n,u);r=n.splice(0,c),i.sigBytes-=d}return new l.init(r,d)},clone:function(){var e=c.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});o.Hasher=p.extend({cfg:c.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){p.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,r){return new e.init(r).finalize(t)}},_createHmacHelper:function(e){return function(t,r){return new m.HMAC.init(e,r).finalize(t)}}});var m=a.algo={};return a}(Math);return e},e.exports=i()},12:function(e,t,r){var i;i=function(e){var t;return t=e.lib.WordArray,e.enc.Base64={stringify:function(e){var t=e.words,r=e.sigBytes,i=this._map;e.clamp();for(var n=[],s=0;s<r;s+=3)for(var a=(t[s>>>2]>>>24-s%4*8&255)<<16|(t[s+1>>>2]>>>24-(s+1)%4*8&255)<<8|t[s+2>>>2]>>>24-(s+2)%4*8&255,o=0;o<4&&s+.75*o<r;o++)n.push(i.charAt(a>>>6*(3-o)&63));var c=i.charAt(64);if(c)for(;n.length%4;)n.push(c);return n.join("")},parse:function(e){var r=e.length,i=this._map,n=this._reverseMap;if(!n){n=this._reverseMap=[];for(var s=0;s<i.length;s++)n[i.charCodeAt(s)]=s}var a=i.charAt(64);if(a){var o=e.indexOf(a);-1!==o&&(r=o)}return function(e,r,i){for(var n=[],s=0,a=0;a<r;a++)if(a%4){var o=i[e.charCodeAt(a-1)]<<a%4*2|i[e.charCodeAt(a)]>>>6-a%4*2;n[s>>>2]|=o<<24-s%4*8,s++}return t.create(n,s)}(e,r,n)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},e.enc.Base64},e.exports=i(r(7728))},432:function(e,t,r){var i;i=function(e){return e.enc.Utf8},e.exports=i(r(7728))},6030:function(e,t,r){var i;i=function(e){var t,r,i,n,s,a;return r=(t=e.lib).Base,i=t.WordArray,s=(n=e.algo).MD5,a=n.EvpKDF=r.extend({cfg:r.extend({keySize:4,hasher:s,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r,n=this.cfg,s=n.hasher.create(),a=i.create(),o=a.words,c=n.keySize,l=n.iterations;o.length<c;){r&&s.update(r),r=s.update(e).finalize(t),s.reset();for(var d=1;d<l;d++)r=s.finalize(r),s.reset();a.concat(r)}return a.sigBytes=4*c,a}}),e.EvpKDF=function(e,t,r){return a.create(r).compute(e,t)},e.EvpKDF},e.exports=i(r(7728),r(5987),r(9735))},1245:function(e,t,r){var i;i=function(e){return e.HmacSHA1},e.exports=i(r(7728),r(5987),r(9735))},9735:function(e,t,r){var i;i=function(e){var t,r;t=e.lib.Base,r=e.enc.Utf8,e.algo.HMAC=t.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=r.parse(t));var i=e.blockSize,n=4*i;t.sigBytes>n&&(t=e.finalize(t)),t.clamp();for(var s=this._oKey=t.clone(),a=this._iKey=t.clone(),o=s.words,c=a.words,l=0;l<i;l++)o[l]^=1549556828,c[l]^=909522486;s.sigBytes=a.sigBytes=n,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}})},e.exports=i(r(7728))},7426:function(e,t,r){var i;i=function(e){return function(t){var r=e.lib,i=r.WordArray,n=r.Hasher,s=e.algo,a=[];!function(){for(var e=0;e<64;e++)a[e]=4294967296*t.abs(t.sin(e+1))|0}();var o=s.MD5=n.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var r=0;r<16;r++){var i=t+r,n=e[i];e[i]=(n<<8|n>>>24)&16711935|(n<<24|n>>>8)&4278255360}var s=this._hash.words,o=e[t+0],h=e[t+1],f=e[t+2],p=e[t+3],m=e[t+4],g=e[t+5],y=e[t+6],v=e[t+7],k=e[t+8],_=e[t+9],w=e[t+10],S=e[t+11],b=e[t+12],T=e[t+13],C=e[t+14],E=e[t+15],I=s[0],A=s[1],x=s[2],U=s[3];I=c(I,A,x,U,o,7,a[0]),U=c(U,I,A,x,h,12,a[1]),x=c(x,U,I,A,f,17,a[2]),A=c(A,x,U,I,p,22,a[3]),I=c(I,A,x,U,m,7,a[4]),U=c(U,I,A,x,g,12,a[5]),x=c(x,U,I,A,y,17,a[6]),A=c(A,x,U,I,v,22,a[7]),I=c(I,A,x,U,k,7,a[8]),U=c(U,I,A,x,_,12,a[9]),x=c(x,U,I,A,w,17,a[10]),A=c(A,x,U,I,S,22,a[11]),I=c(I,A,x,U,b,7,a[12]),U=c(U,I,A,x,T,12,a[13]),x=c(x,U,I,A,C,17,a[14]),A=c(A,x,U,I,E,22,a[15]),I=l(I,A,x,U,h,5,a[16]),U=l(U,I,A,x,y,9,a[17]),x=l(x,U,I,A,S,14,a[18]),A=l(A,x,U,I,o,20,a[19]),I=l(I,A,x,U,g,5,a[20]),U=l(U,I,A,x,w,9,a[21]),x=l(x,U,I,A,E,14,a[22]),A=l(A,x,U,I,m,20,a[23]),I=l(I,A,x,U,_,5,a[24]),U=l(U,I,A,x,C,9,a[25]),x=l(x,U,I,A,p,14,a[26]),A=l(A,x,U,I,k,20,a[27]),I=l(I,A,x,U,T,5,a[28]),U=l(U,I,A,x,f,9,a[29]),x=l(x,U,I,A,v,14,a[30]),A=l(A,x,U,I,b,20,a[31]),I=d(I,A,x,U,g,4,a[32]),U=d(U,I,A,x,k,11,a[33]),x=d(x,U,I,A,S,16,a[34]),A=d(A,x,U,I,C,23,a[35]),I=d(I,A,x,U,h,4,a[36]),U=d(U,I,A,x,m,11,a[37]),x=d(x,U,I,A,v,16,a[38]),A=d(A,x,U,I,w,23,a[39]),I=d(I,A,x,U,T,4,a[40]),U=d(U,I,A,x,o,11,a[41]),x=d(x,U,I,A,p,16,a[42]),A=d(A,x,U,I,y,23,a[43]),I=d(I,A,x,U,_,4,a[44]),U=d(U,I,A,x,b,11,a[45]),x=d(x,U,I,A,E,16,a[46]),A=d(A,x,U,I,f,23,a[47]),I=u(I,A,x,U,o,6,a[48]),U=u(U,I,A,x,v,10,a[49]),x=u(x,U,I,A,C,15,a[50]),A=u(A,x,U,I,g,21,a[51]),I=u(I,A,x,U,b,6,a[52]),U=u(U,I,A,x,p,10,a[53]),x=u(x,U,I,A,w,15,a[54]),A=u(A,x,U,I,h,21,a[55]),I=u(I,A,x,U,k,6,a[56]),U=u(U,I,A,x,E,10,a[57]),x=u(x,U,I,A,y,15,a[58]),A=u(A,x,U,I,T,21,a[59]),I=u(I,A,x,U,m,6,a[60]),U=u(U,I,A,x,S,10,a[61]),x=u(x,U,I,A,f,15,a[62]),A=u(A,x,U,I,_,21,a[63]),s[0]=s[0]+I|0,s[1]=s[1]+A|0,s[2]=s[2]+x|0,s[3]=s[3]+U|0},_doFinalize:function(){var e=this._data,r=e.words,i=8*this._nDataBytes,n=8*e.sigBytes;r[n>>>5]|=128<<24-n%32;var s=t.floor(i/4294967296);r[(n+64>>>9<<4)+15]=(s<<8|s>>>24)&16711935|(s<<24|s>>>8)&4278255360,r[(n+64>>>9<<4)+14]=(i<<8|i>>>24)&16711935|(i<<24|i>>>8)&4278255360,e.sigBytes=(r.length+1)*4,this._process();for(var a=this._hash,o=a.words,c=0;c<4;c++){var l=o[c];o[c]=(l<<8|l>>>24)&16711935|(l<<24|l>>>8)&4278255360}return a},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}});function c(e,t,r,i,n,s,a){var o=e+(t&r|~t&i)+n+a;return(o<<s|o>>>32-s)+t}function l(e,t,r,i,n,s,a){var o=e+(t&i|r&~i)+n+a;return(o<<s|o>>>32-s)+t}function d(e,t,r,i,n,s,a){var o=e+(t^r^i)+n+a;return(o<<s|o>>>32-s)+t}function u(e,t,r,i,n,s,a){var o=e+(r^(t|~i))+n+a;return(o<<s|o>>>32-s)+t}e.MD5=n._createHelper(o),e.HmacMD5=n._createHmacHelper(o)}(Math),e.MD5},e.exports=i(r(7728))},5987:function(e,t,r){var i;i=function(e){var t,r,i,n,s,a;return r=(t=e.lib).WordArray,i=t.Hasher,n=e.algo,s=[],a=n.SHA1=i.extend({_doReset:function(){this._hash=new r.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=this._hash.words,i=r[0],n=r[1],a=r[2],o=r[3],c=r[4],l=0;l<80;l++){if(l<16)s[l]=0|e[t+l];else{var d=s[l-3]^s[l-8]^s[l-14]^s[l-16];s[l]=d<<1|d>>>31}var u=(i<<5|i>>>27)+c+s[l];l<20?u+=(n&a|~n&o)+1518500249:l<40?u+=(n^a^o)+1859775393:l<60?u+=(n&a|n&o|a&o)-1894007588:u+=(n^a^o)-899497514,c=o,o=a,a=n<<30|n>>>2,n=i,i=u}r[0]=r[0]+i|0,r[1]=r[1]+n|0,r[2]=r[2]+a|0,r[3]=r[3]+o|0,r[4]=r[4]+c|0},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,i=8*e.sigBytes;return t[i>>>5]|=128<<24-i%32,t[(i+64>>>9<<4)+14]=Math.floor(r/4294967296),t[(i+64>>>9<<4)+15]=r,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}}),e.SHA1=i._createHelper(a),e.HmacSHA1=i._createHmacHelper(a),e.SHA1},e.exports=i(r(7728))},3388:e=>{"use strict";let t=e=>"object"==typeof e&&null!==e,r=Symbol("skip"),i=e=>t(e)&&!(e instanceof RegExp)&&!(e instanceof Error)&&!(e instanceof Date),n=(e,t,s,a=new WeakMap)=>{if(s={deep:!1,target:{},...s},a.has(e))return a.get(e);a.set(e,s.target);let{target:o}=s;delete s.target;let c=e=>e.map(e=>i(e)?n(e,t,s,a):e);if(Array.isArray(e))return c(e);for(let[l,d]of Object.entries(e)){let u=t(l,d,e);if(u===r)continue;let[h,f,{shouldRecurse:p=!0}={}]=u;"__proto__"!==h&&(s.deep&&p&&i(f)&&(f=Array.isArray(f)?c(f):n(f,t,s,a)),o[h]=f)}return o};e.exports=(e,r,i)=>{if(!t(e))throw TypeError(`Expected an object, got \`${e}\` (${typeof e})`);return n(e,r,i)},e.exports.mapObjectSkip=r},473:(e,t,r)=>{"use strict";r.r(t),r.d(t,{cookies:()=>h,draftMode:()=>f,headers:()=>u});var i=r(6776),n=r(3665),s=r(4101),a=r(5276),o=r(5405),c=r(828);class l{get isEnabled(){return this._provider.isEnabled}enable(){let e=o.A.getStore();return e&&(0,c.TP)(e,"draftMode().enable()"),this._provider.enable()}disable(){let e=o.A.getStore();return e&&(0,c.TP)(e,"draftMode().disable()"),this._provider.disable()}constructor(e){this._provider=e}}var d=r(7322);function u(){let e="headers",t=o.A.getStore();if(t){if(t.forceStatic)return n.h.seal(new Headers({}));(0,c.TP)(t,e)}return(0,d.F)(e).headers}function h(){let e="cookies",t=o.A.getStore();if(t){if(t.forceStatic)return i.Qb.seal(new s.qC(new Headers({})));(0,c.TP)(t,e)}let r=(0,d.F)(e),n=a.W.getStore();return(null==n?void 0:n.isAction)||(null==n?void 0:n.isAppRoute)?r.mutableCookies:r.cookies}function f(){return new l((0,d.F)("draftMode").draftMode)}},6633:(e,t,r)=>{"use strict";r.r(t),r.d(t,{snakeCase:()=>c});var i=function(){return(i=Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}).apply(this,arguments)};function n(e){return e.toLowerCase()}Object.create,Object.create;var s=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],a=/[^A-Z0-9]+/gi;function o(e,t,r){return t instanceof RegExp?e.replace(t,r):t.reduce(function(e,t){return e.replace(t,r)},e)}function c(e,t){var r;return void 0===t&&(t={}),void 0===(r=i({delimiter:"_"},t))&&(r={}),function(e,t){void 0===t&&(t={});for(var r=t.splitRegexp,i=t.stripRegexp,c=t.transform,l=t.delimiter,d=o(o(e,void 0===r?s:r,"$1\0$2"),void 0===i?a:i,"\0"),u=0,h=d.length;"\0"===d.charAt(u);)u++;for(;"\0"===d.charAt(h-1);)h--;return d.slice(u,h).split("\0").map(void 0===c?n:c).join(void 0===l?" ":l)}(e,i({delimiter:"."},r))}},7624:(e,t,r)=>{"use strict";let i=r(3388),{snakeCase:n}=r(6633);e.exports=function(e,t){return i(e,function(e,r){return[t.exclude.some(function(t){return"string"==typeof t?t===e:t.test(e)})?e:n(e,t.parsingOptions),r]},t=Object.assign({deep:!0,exclude:[],parsingOptions:{}},t))}},9102:(e,t,r)=>{"use strict";r.d(t,{YU:()=>n,fi:()=>o,h2:()=>s,oh:()=>a,rE:()=>i});var i={InvalidSecretKey:"clerk_key_invalid"},n={TokenExpired:"token-expired",TokenInvalid:"token-invalid",TokenInvalidAlgorithm:"token-invalid-algorithm",TokenInvalidAuthorizedParties:"token-invalid-authorized-parties",TokenInvalidSignature:"token-invalid-signature",TokenNotActiveYet:"token-not-active-yet",TokenIatInTheFuture:"token-iat-in-the-future",TokenVerificationFailed:"token-verification-failed",InvalidSecretKey:"secret-key-invalid",LocalJWKMissing:"jwk-local-missing",RemoteJWKFailedToLoad:"jwk-remote-failed-to-load",RemoteJWKInvalid:"jwk-remote-invalid",RemoteJWKMissing:"jwk-remote-missing",JWKFailedToResolve:"jwk-failed-to-resolve",JWKKidMismatch:"jwk-kid-mismatch"},s={ContactSupport:"Contact <EMAIL>",EnsureClerkJWT:"Make sure that this is a valid Clerk generate JWT.",SetClerkJWTKey:"Set the CLERK_JWT_KEY environment variable.",SetClerkSecretKey:"Set the CLERK_SECRET_KEY environment variable.",EnsureClockSync:"Make sure your system clock is in sync (e.g. turn off and on automatic time synchronization)."},a=class e extends Error{constructor({action:t,message:r,reason:i}){super(r),Object.setPrototypeOf(this,e.prototype),this.reason=i,this.message=r,this.action=t}getFullMessage(){return`${[this.message,this.action].filter(e=>e).join(" ")} (reason=${this.reason}, token-carrier=${this.tokenCarrier})`}},o=class extends Error{}},3916:(e,t,r)=>{"use strict";r.d(t,{tD:()=>eH,_G:()=>A,sA:()=>ts,gf:()=>eN,WL:()=>ej,RM:()=>er,nQ:()=>d.nQ,B9:()=>eq,JH:()=>eR,WX:()=>e1});var i=r(5036),n=r(9102);function s(e){var t,r,i,n,s;return{code:e.code,message:e.message,longMessage:e.long_message,meta:{paramName:null==(t=null==e?void 0:e.meta)?void 0:t.param_name,sessionId:null==(r=null==e?void 0:e.meta)?void 0:r.session_id,emailAddresses:null==(i=null==e?void 0:e.meta)?void 0:i.email_addresses,identifiers:null==(n=null==e?void 0:e.meta)?void 0:n.identifiers,zxcvbn:null==(s=null==e?void 0:e.meta)?void 0:s.zxcvbn}}}var a=class e extends Error{constructor(t,{data:r,status:i,clerkTraceId:n}){super(t),this.toString=()=>{let e=`[${this.name}]
Message:${this.message}
Status:${this.status}
Serialized errors: ${this.errors.map(e=>JSON.stringify(e))}`;return this.clerkTraceId&&(e+=`
Clerk Trace ID: ${this.clerkTraceId}`),e},Object.setPrototypeOf(this,e.prototype),this.status=i,this.message=t,this.clerkTraceId=n,this.clerkError=!0,this.errors=function(e=[]){return e.length>0?e.map(s):[]}(r)}},o=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});r(3369);var c=r(7624);async function l(e,t=1,r=5){try{return await e()}catch(n){var i;if(t>=r)throw n;return await (i=2**t*100,new Promise(e=>setTimeout(e,i))),l(e,t+1,r)}}var d=r(9241);r(6751);var u={veryStrict:{afterMinutes:10,level:"multiFactor"},strict:{afterMinutes:10,level:"secondFactor"},moderate:{afterMinutes:60,level:"secondFactor"},lax:{afterMinutes:1440,level:"secondFactor"}},h=new Set(["firstFactor","secondFactor","multiFactor"]),f=new Set(["veryStrict","strict","moderate","lax"]),p=e=>"number"==typeof e&&e>0,m=e=>h.has(e),g=e=>f.has(e),y=(e,t)=>{let{orgId:r,orgRole:i,orgPermissions:n}=t;return(e.role||e.permission)&&r&&i&&n?e.permission?n.includes(e.permission):e.role?i===e.role:null:null},v=e=>!!("string"==typeof e&&g(e)||"object"==typeof e&&m(e.level)&&p(e.afterMinutes))&&(e=>"string"==typeof e?u[e]:e).bind(null,e),k=(e,{__experimental_factorVerificationAge:t})=>{if(!e.__experimental_reverification||!t)return null;let r=v(e.__experimental_reverification);if(!r)return null;let{level:i,afterMinutes:n}=r(),[s,a]=t,o=-1!==s?n>s:null,c=-1!==a?n>a:null;switch(i){case"firstFactor":return o;case"secondFactor":return -1!==a?c:o;case"multiFactor":return -1===a?o:o&&c}},_=e=>t=>{if(!e.userId)return!1;let r=y(t,e),i=k(t,e);return[r,i].some(e=>null===e)?[r,i].some(e=>!0===e):[r,i].every(e=>!0===e)},w=r(8847);function S(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function b(e){return e&&e.sensitive?"":"i"}function T(e,t){try{var r,i,n,s,a;return r=[],i=function e(t,r,i){var n;return t instanceof RegExp?function(e,t){if(!t)return e;for(var r=/\((?:\?<(.*?)>)?(?!\?)/g,i=0,n=r.exec(e.source);n;)t.push({name:n[1]||i++,prefix:"",suffix:"",modifier:"",pattern:""}),n=r.exec(e.source);return e}(t,r):Array.isArray(t)?(n=t.map(function(t){return e(t,r,i).source}),new RegExp("(?:".concat(n.join("|"),")"),b(i))):function(e,t,r){void 0===r&&(r={});for(var i=r.strict,n=void 0!==i&&i,s=r.start,a=r.end,o=r.encode,c=void 0===o?function(e){return e}:o,l=r.delimiter,d=r.endsWith,u="[".concat(S(void 0===d?"":d),"]|$"),h="[".concat(S(void 0===l?"/#?":l),"]"),f=void 0===s||s?"^":"",p=0;p<e.length;p++){var m=e[p];if("string"==typeof m)f+=S(c(m));else{var g=S(c(m.prefix)),y=S(c(m.suffix));if(m.pattern){if(t&&t.push(m),g||y){if("+"===m.modifier||"*"===m.modifier){var v="*"===m.modifier?"?":"";f+="(?:".concat(g,"((?:").concat(m.pattern,")(?:").concat(y).concat(g,"(?:").concat(m.pattern,"))*)").concat(y,")").concat(v)}else f+="(?:".concat(g,"(").concat(m.pattern,")").concat(y,")").concat(m.modifier)}else{if("+"===m.modifier||"*"===m.modifier)throw TypeError('Can not repeat "'.concat(m.name,'" without a prefix and suffix'));f+="(".concat(m.pattern,")").concat(m.modifier)}}else f+="(?:".concat(g).concat(y,")").concat(m.modifier)}}if(void 0===a||a)n||(f+="".concat(h,"?")),f+=r.endsWith?"(?=".concat(u,")"):"$";else{var k=e[e.length-1],_="string"==typeof k?h.indexOf(k[k.length-1])>-1:void 0===k;n||(f+="(?:".concat(h,"(?=").concat(u,"))?")),_||(f+="(?=".concat(h,"|").concat(u,")"))}return new RegExp(f,b(r))}(function(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var i=e[r];if("*"===i||"+"===i||"?"===i){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===i){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===i){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===i){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===i){for(var n="",s=r+1;s<e.length;){var a=e.charCodeAt(s);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){n+=e[s++];continue}break}if(!n)throw TypeError("Missing parameter name at ".concat(r));t.push({type:"NAME",index:r,value:n}),r=s;continue}if("("===i){var o=1,c="",s=r+1;if("?"===e[s])throw TypeError('Pattern cannot start with "?" at '.concat(s));for(;s<e.length;){if("\\"===e[s]){c+=e[s++]+e[s++];continue}if(")"===e[s]){if(0==--o){s++;break}}else if("("===e[s]&&(o++,"?"!==e[s+1]))throw TypeError("Capturing groups are not allowed at ".concat(s));c+=e[s++]}if(o)throw TypeError("Unbalanced pattern at ".concat(r));if(!c)throw TypeError("Missing pattern at ".concat(r));t.push({type:"PATTERN",index:r,value:c}),r=s;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),i=t.prefixes,n=void 0===i?"./":i,s=t.delimiter,a=void 0===s?"/#?":s,o=[],c=0,l=0,d="",u=function(e){if(l<r.length&&r[l].type===e)return r[l++].value},h=function(e){var t=u(e);if(void 0!==t)return t;var i=r[l],n=i.type,s=i.index;throw TypeError("Unexpected ".concat(n," at ").concat(s,", expected ").concat(e))},f=function(){for(var e,t="";e=u("CHAR")||u("ESCAPED_CHAR");)t+=e;return t},p=function(e){for(var t=0;t<a.length;t++){var r=a[t];if(e.indexOf(r)>-1)return!0}return!1},m=function(e){var t=o[o.length-1],r=e||(t&&"string"==typeof t?t:"");if(t&&!r)throw TypeError('Must have text between two parameters, missing text after "'.concat(t.name,'"'));return!r||p(r)?"[^".concat(S(a),"]+?"):"(?:(?!".concat(S(r),")[^").concat(S(a),"])+?")};l<r.length;){var g=u("CHAR"),y=u("NAME"),v=u("PATTERN");if(y||v){var k=g||"";-1===n.indexOf(k)&&(d+=k,k=""),d&&(o.push(d),d=""),o.push({name:y||c++,prefix:k,suffix:"",pattern:v||m(k),modifier:u("MODIFIER")||""});continue}var _=g||u("ESCAPED_CHAR");if(_){d+=_;continue}if(d&&(o.push(d),d=""),u("OPEN")){var k=f(),w=u("NAME")||"",b=u("PATTERN")||"",T=f();h("CLOSE"),o.push({name:w||(b?c++:""),pattern:w&&!b?m(k):b,prefix:k,suffix:T,modifier:u("MODIFIER")||""});continue}h("END")}return o}(t,i),r,i)}(e,r,t),n=t,void 0===n&&(n={}),s=n.decode,a=void 0===s?function(e){return e}:s,function(e){var t=i.exec(e);if(!t)return!1;for(var n=t[0],s=t.index,o=Object.create(null),c=1;c<t.length;c++)!function(e){if(void 0!==t[e]){var i=r[e-1];"*"===i.modifier||"+"===i.modifier?o[i.name]=t[e].split(i.prefix+i.suffix).map(function(e){return a(e,i)}):o[i.name]=a(t[e],i)}}(c);return{path:n,index:s,params:o}}}catch(e){throw Error(`Invalid path and options: Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x
${e.message}`)}}var C="https://api.clerk.com",E={Session:"__session",Refresh:"__refresh",ClientUat:"__client_uat",Handshake:"__clerk_handshake",DevBrowser:"__clerk_db_jwt",RedirectCount:"__clerk_redirect_count"},I={ClerkSynced:"__clerk_synced",ClerkRedirectUrl:"__clerk_redirect_url",DevBrowser:E.DevBrowser,Handshake:E.Handshake,HandshakeHelp:"__clerk_help",LegacyDevBrowser:"__dev_session",HandshakeReason:"__clerk_hs_reason"},A={Attributes:{AuthToken:"__clerkAuthToken",AuthSignature:"__clerkAuthSignature",AuthStatus:"__clerkAuthStatus",AuthReason:"__clerkAuthReason",AuthMessage:"__clerkAuthMessage",ClerkUrl:"__clerkUrl"},Cookies:E,Headers:{AuthToken:"x-clerk-auth-token",AuthSignature:"x-clerk-auth-signature",AuthStatus:"x-clerk-auth-status",AuthReason:"x-clerk-auth-reason",AuthMessage:"x-clerk-auth-message",ClerkUrl:"x-clerk-clerk-url",EnableDebug:"x-clerk-debug",ClerkRequestData:"x-clerk-request-data",ClerkRedirectTo:"x-clerk-redirect-to",CloudFrontForwardedProto:"cloudfront-forwarded-proto",Authorization:"authorization",ForwardedPort:"x-forwarded-port",ForwardedProto:"x-forwarded-proto",ForwardedHost:"x-forwarded-host",Accept:"accept",Referrer:"referer",UserAgent:"user-agent",Origin:"origin",Host:"host",ContentType:"content-type",SecFetchDest:"sec-fetch-dest",Location:"location",CacheControl:"cache-control"},ContentTypes:{Json:"application/json"},QueryParameters:I},x=class{constructor(e){this.request=e}requireId(e){if(!e)throw Error("A valid resource ID is required.")}},U=RegExp("(?<!:)/{1,}","g");function P(...e){return e.filter(e=>e).join("/").replace(U,"/")}var O="/allowlist_identifiers",N=class extends x{async getAllowlistIdentifierList(){return this.request({method:"GET",path:O,queryParams:{paginated:!0}})}async createAllowlistIdentifier(e){return this.request({method:"POST",path:O,bodyParams:e})}async deleteAllowlistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:P(O,e)})}},M="/clients",q=class extends x{async getClientList(e={}){return this.request({method:"GET",path:M,queryParams:{...e,paginated:!0}})}async getClient(e){return this.requireId(e),this.request({method:"GET",path:P(M,e)})}verifyClient(e){return this.request({method:"POST",path:P(M,"verify"),bodyParams:{token:e}})}},R=class extends x{async deleteDomain(e){return this.request({method:"DELETE",path:P("/domains",e)})}},z="/email_addresses",H=class extends x{async getEmailAddress(e){return this.requireId(e),this.request({method:"GET",path:P(z,e)})}async createEmailAddress(e){return this.request({method:"POST",path:z,bodyParams:e})}async updateEmailAddress(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:P(z,e),bodyParams:t})}async deleteEmailAddress(e){return this.requireId(e),this.request({method:"DELETE",path:P(z,e)})}},J="/invitations",B=class extends x{async getInvitationList(e={}){return this.request({method:"GET",path:J,queryParams:{...e,paginated:!0}})}async createInvitation(e){return this.request({method:"POST",path:J,bodyParams:e})}async revokeInvitation(e){return this.requireId(e),this.request({method:"POST",path:P(J,e,"revoke")})}},D="/organizations",L=class extends x{async getOrganizationList(e){return this.request({method:"GET",path:D,queryParams:e})}async createOrganization(e){return this.request({method:"POST",path:D,bodyParams:e})}async getOrganization(e){let{includeMembersCount:t}=e,r="organizationId"in e?e.organizationId:e.slug;return this.requireId(r),this.request({method:"GET",path:P(D,r),queryParams:{includeMembersCount:t}})}async updateOrganization(e,t){return this.requireId(e),this.request({method:"PATCH",path:P(D,e),bodyParams:t})}async updateOrganizationLogo(e,t){this.requireId(e);let r=new i.ZE.FormData;return r.append("file",t?.file),t?.uploaderUserId&&r.append("uploader_user_id",t?.uploaderUserId),this.request({method:"PUT",path:P(D,e,"logo"),formData:r})}async deleteOrganizationLogo(e){return this.requireId(e),this.request({method:"DELETE",path:P(D,e,"logo")})}async updateOrganizationMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:P(D,e,"metadata"),bodyParams:t})}async deleteOrganization(e){return this.request({method:"DELETE",path:P(D,e)})}async getOrganizationMembershipList(e){let{organizationId:t,limit:r,offset:i}=e;return this.requireId(t),this.request({method:"GET",path:P(D,t,"memberships"),queryParams:{limit:r,offset:i}})}async createOrganizationMembership(e){let{organizationId:t,userId:r,role:i}=e;return this.requireId(t),this.request({method:"POST",path:P(D,t,"memberships"),bodyParams:{userId:r,role:i}})}async updateOrganizationMembership(e){let{organizationId:t,userId:r,role:i}=e;return this.requireId(t),this.request({method:"PATCH",path:P(D,t,"memberships",r),bodyParams:{role:i}})}async updateOrganizationMembershipMetadata(e){let{organizationId:t,userId:r,publicMetadata:i,privateMetadata:n}=e;return this.request({method:"PATCH",path:P(D,t,"memberships",r,"metadata"),bodyParams:{publicMetadata:i,privateMetadata:n}})}async deleteOrganizationMembership(e){let{organizationId:t,userId:r}=e;return this.requireId(t),this.request({method:"DELETE",path:P(D,t,"memberships",r)})}async getOrganizationInvitationList(e){let{organizationId:t,status:r,limit:i,offset:n}=e;return this.requireId(t),this.request({method:"GET",path:P(D,t,"invitations"),queryParams:{status:r,limit:i,offset:n}})}async createOrganizationInvitation(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:P(D,t,"invitations"),bodyParams:{...r}})}async getOrganizationInvitation(e){let{organizationId:t,invitationId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"GET",path:P(D,t,"invitations",r)})}async revokeOrganizationInvitation(e){let{organizationId:t,invitationId:r,requestingUserId:i}=e;return this.requireId(t),this.request({method:"POST",path:P(D,t,"invitations",r,"revoke"),bodyParams:{requestingUserId:i}})}async getOrganizationDomainList(e){let{organizationId:t,limit:r,offset:i}=e;return this.requireId(t),this.request({method:"GET",path:P(D,t,"domains"),queryParams:{limit:r,offset:i}})}async createOrganizationDomain(e){let{organizationId:t,name:r,enrollmentMode:i,verified:n=!0}=e;return this.requireId(t),this.request({method:"POST",path:P(D,t,"domains"),bodyParams:{name:r,enrollmentMode:i,verified:n}})}async updateOrganizationDomain(e){let{organizationId:t,domainId:r,...i}=e;return this.requireId(t),this.requireId(r),this.request({method:"PATCH",path:P(D,t,"domains",r),bodyParams:i})}async deleteOrganizationDomain(e){let{organizationId:t,domainId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"DELETE",path:P(D,t,"domains",r)})}},F="/phone_numbers",K=class extends x{async getPhoneNumber(e){return this.requireId(e),this.request({method:"GET",path:P(F,e)})}async createPhoneNumber(e){return this.request({method:"POST",path:F,bodyParams:e})}async updatePhoneNumber(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:P(F,e),bodyParams:t})}async deletePhoneNumber(e){return this.requireId(e),this.request({method:"DELETE",path:P(F,e)})}},W="/redirect_urls",j=class extends x{async getRedirectUrlList(){return this.request({method:"GET",path:W,queryParams:{paginated:!0}})}async getRedirectUrl(e){return this.requireId(e),this.request({method:"GET",path:P(W,e)})}async createRedirectUrl(e){return this.request({method:"POST",path:W,bodyParams:e})}async deleteRedirectUrl(e){return this.requireId(e),this.request({method:"DELETE",path:P(W,e)})}},$="/sessions",Y=class extends x{async getSessionList(e={}){return this.request({method:"GET",path:$,queryParams:{...e,paginated:!0}})}async getSession(e){return this.requireId(e),this.request({method:"GET",path:P($,e)})}async revokeSession(e){return this.requireId(e),this.request({method:"POST",path:P($,e,"revoke")})}async verifySession(e,t){return this.requireId(e),this.request({method:"POST",path:P($,e,"verify"),bodyParams:{token:t}})}async getToken(e,t){return this.requireId(e),this.request({method:"POST",path:P($,e,"tokens",t||"")})}async refreshSession(e,t){return this.requireId(e),this.request({method:"POST",path:P($,e,"refresh"),bodyParams:t})}},G="/sign_in_tokens",Q=class extends x{async createSignInToken(e){return this.request({method:"POST",path:G,bodyParams:e})}async revokeSignInToken(e){return this.requireId(e),this.request({method:"POST",path:P(G,e,"revoke")})}},V="/users",X=class extends x{async getUserList(e={}){let{limit:t,offset:r,orderBy:i,...n}=e,[s,a]=await Promise.all([this.request({method:"GET",path:V,queryParams:e}),this.getCount(n)]);return{data:s,totalCount:a}}async getUser(e){return this.requireId(e),this.request({method:"GET",path:P(V,e)})}async createUser(e){return this.request({method:"POST",path:V,bodyParams:e})}async updateUser(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:P(V,e),bodyParams:t})}async updateUserProfileImage(e,t){this.requireId(e);let r=new i.ZE.FormData;return r.append("file",t?.file),this.request({method:"POST",path:P(V,e,"profile_image"),formData:r})}async updateUserMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:P(V,e,"metadata"),bodyParams:t})}async deleteUser(e){return this.requireId(e),this.request({method:"DELETE",path:P(V,e)})}async getCount(e={}){return this.request({method:"GET",path:P(V,"count"),queryParams:e})}async getUserOauthAccessToken(e,t){return this.requireId(e),this.request({method:"GET",path:P(V,e,"oauth_access_tokens",t),queryParams:{paginated:!0}})}async disableUserMFA(e){return this.requireId(e),this.request({method:"DELETE",path:P(V,e,"mfa")})}async getOrganizationMembershipList(e){let{userId:t,limit:r,offset:i}=e;return this.requireId(t),this.request({method:"GET",path:P(V,t,"organization_memberships"),queryParams:{limit:r,offset:i}})}async verifyPassword(e){let{userId:t,password:r}=e;return this.requireId(t),this.request({method:"POST",path:P(V,t,"verify_password"),bodyParams:{password:r}})}async verifyTOTP(e){let{userId:t,code:r}=e;return this.requireId(t),this.request({method:"POST",path:P(V,t,"verify_totp"),bodyParams:{code:r}})}async banUser(e){return this.requireId(e),this.request({method:"POST",path:P(V,e,"ban")})}async unbanUser(e){return this.requireId(e),this.request({method:"POST",path:P(V,e,"unban")})}async lockUser(e){return this.requireId(e),this.request({method:"POST",path:P(V,e,"lock")})}async unlockUser(e){return this.requireId(e),this.request({method:"POST",path:P(V,e,"unlock")})}async deleteUserProfileImage(e){return this.requireId(e),this.request({method:"DELETE",path:P(V,e,"profile_image")})}},Z="/saml_connections",ee=class extends x{async getSamlConnectionList(e={}){return this.request({method:"GET",path:Z,queryParams:e})}async createSamlConnection(e){return this.request({method:"POST",path:Z,bodyParams:e})}async getSamlConnection(e){return this.requireId(e),this.request({method:"GET",path:P(Z,e)})}async updateSamlConnection(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:P(Z,e),bodyParams:t})}async deleteSamlConnection(e){return this.requireId(e),this.request({method:"DELETE",path:P(Z,e)})}},et=class extends x{async createTestingToken(){return this.request({method:"POST",path:"/testing_tokens"})}},er=function({packageName:e,customMessages:t}){let r=e,i={...o,...t};function n(e,t){if(!t)return`${r}: ${e}`;let i=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();i=i.replace(`{{${r[1]}}}`,e)}return`${r}: ${i}`}return{setPackageName({packageName:e}){return"string"==typeof e&&(r=e),this},setMessages({customMessages:e}){return Object.assign(i,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(n(i.InvalidPublishableKeyErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(n(i.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(n(i.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw Error(n(i.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(e){throw Error(n(i.MissingClerkProvider,e))},throw(e){throw Error(n(e))}}}({packageName:"@clerk/backend"}),{isDevOrStagingUrl:ei}=(0,d.MY)();function en(e){if(!e||"string"!=typeof e)throw Error("Missing Clerk Secret Key. Go to https://dashboard.clerk.com and get your key for your instance.")}var es=class e{constructor(e,t,r,i,n){this.id=e,this.identifier=t,this.createdAt=r,this.updatedAt=i,this.invitationId=n}static fromJSON(t){return new e(t.id,t.identifier,t.created_at,t.updated_at,t.invitation_id)}},ea=class e{constructor(e,t,r,i,n,s,a,o,c){this.id=e,this.clientId=t,this.userId=r,this.status=i,this.lastActiveAt=n,this.expireAt=s,this.abandonAt=a,this.createdAt=o,this.updatedAt=c}static fromJSON(t){return new e(t.id,t.client_id,t.user_id,t.status,t.last_active_at,t.expire_at,t.abandon_at,t.created_at,t.updated_at)}},eo=class e{constructor(e,t,r,i,n,s,a,o){this.id=e,this.sessionIds=t,this.sessions=r,this.signInId=i,this.signUpId=n,this.lastActiveSessionId=s,this.createdAt=a,this.updatedAt=o}static fromJSON(t){return new e(t.id,t.session_ids,t.sessions.map(e=>ea.fromJSON(e)),t.sign_in_id,t.sign_up_id,t.last_active_session_id,t.created_at,t.updated_at)}},ec=class e{constructor(e,t,r,i){this.object=e,this.id=t,this.slug=r,this.deleted=i}static fromJSON(t){return new e(t.object,t.id||null,t.slug||null,t.deleted)}},el=class e{constructor(e,t,r,i,n,s,a,o,c,l,d){this.id=e,this.fromEmailName=t,this.emailAddressId=r,this.toEmailAddress=i,this.subject=n,this.body=s,this.bodyPlain=a,this.status=o,this.slug=c,this.data=l,this.deliveredByClerk=d}static fromJSON(t){return new e(t.id,t.from_email_name,t.email_address_id,t.to_email_address,t.subject,t.body,t.body_plain,t.status,t.slug,t.data,t.delivered_by_clerk)}},ed=class e{constructor(e,t){this.id=e,this.type=t}static fromJSON(t){return new e(t.id,t.type)}},eu=class e{constructor(e,t,r=null,i=null,n=null,s=null,a=null){this.status=e,this.strategy=t,this.externalVerificationRedirectURL=r,this.attempts=i,this.expireAt=n,this.nonce=s,this.message=a}static fromJSON(t){return new e(t.status,t.strategy,t.external_verification_redirect_url?new URL(t.external_verification_redirect_url):null,t.attempts,t.expire_at,t.nonce)}},eh=class e{constructor(e,t,r,i){this.id=e,this.emailAddress=t,this.verification=r,this.linkedTo=i}static fromJSON(t){return new e(t.id,t.email_address,t.verification&&eu.fromJSON(t.verification),t.linked_to.map(e=>ed.fromJSON(e)))}},ef=class e{constructor(e,t,r,i,n,s,a,o,c,l,d={},u,h){this.id=e,this.provider=t,this.identificationId=r,this.externalId=i,this.approvedScopes=n,this.emailAddress=s,this.firstName=a,this.lastName=o,this.imageUrl=c,this.username=l,this.publicMetadata=d,this.label=u,this.verification=h}static fromJSON(t){return new e(t.id,t.provider,t.identification_id,t.provider_user_id,t.approved_scopes,t.email_address,t.first_name,t.last_name,t.image_url||"",t.username,t.public_metadata,t.label,t.verification&&eu.fromJSON(t.verification))}},ep=class e{constructor(e,t,r,i,n,s,a,o){this.id=e,this.emailAddress=t,this.publicMetadata=r,this.createdAt=i,this.updatedAt=n,this.status=s,this.url=a,this.revoked=o}static fromJSON(t){return new e(t.id,t.email_address,t.public_metadata,t.created_at,t.updated_at,t.status,t.url,t.revoked)}},em={AllowlistIdentifier:"allowlist_identifier",Client:"client",Email:"email",EmailAddress:"email_address",Invitation:"invitation",OauthAccessToken:"oauth_access_token",Organization:"organization",OrganizationInvitation:"organization_invitation",OrganizationMembership:"organization_membership",PhoneNumber:"phone_number",RedirectUrl:"redirect_url",Session:"session",SignInToken:"sign_in_token",SmsMessage:"sms_message",User:"user",Token:"token",TotalCount:"total_count"},eg=class e{constructor(e,t,r,i={},n,s,a){this.externalAccountId=e,this.provider=t,this.token=r,this.publicMetadata=i,this.label=n,this.scopes=s,this.tokenSecret=a}static fromJSON(t){return new e(t.external_account_id,t.provider,t.token,t.public_metadata,t.label||"",t.scopes,t.token_secret)}},ey=class e{constructor(e,t,r,i,n,s,a,o,c={},l={},d,u,h){this.id=e,this.name=t,this.slug=r,this.imageUrl=i,this.hasImage=n,this.createdBy=s,this.createdAt=a,this.updatedAt=o,this.publicMetadata=c,this.privateMetadata=l,this.maxAllowedMemberships=d,this.adminDeleteEnabled=u,this.membersCount=h}static fromJSON(t){return new e(t.id,t.name,t.slug,t.image_url||"",t.has_image,t.created_by,t.created_at,t.updated_at,t.public_metadata,t.private_metadata,t.max_allowed_memberships,t.admin_delete_enabled,t.members_count)}},ev=class e{constructor(e,t,r,i,n,s,a,o={},c={}){this.id=e,this.emailAddress=t,this.role=r,this.organizationId=i,this.createdAt=n,this.updatedAt=s,this.status=a,this.publicMetadata=o,this.privateMetadata=c}static fromJSON(t){return new e(t.id,t.email_address,t.role,t.organization_id,t.created_at,t.updated_at,t.status,t.public_metadata,t.private_metadata)}},ek=class e{constructor(e,t,r,i={},n={},s,a,o,c){this.id=e,this.role=t,this.permissions=r,this.publicMetadata=i,this.privateMetadata=n,this.createdAt=s,this.updatedAt=a,this.organization=o,this.publicUserData=c}static fromJSON(t){return new e(t.id,t.role,t.permissions,t.public_metadata,t.private_metadata,t.created_at,t.updated_at,ey.fromJSON(t.organization),e_.fromJSON(t.public_user_data))}},e_=class e{constructor(e,t,r,i,n,s){this.identifier=e,this.firstName=t,this.lastName=r,this.imageUrl=i,this.hasImage=n,this.userId=s}static fromJSON(t){return new e(t.identifier,t.first_name,t.last_name,t.image_url,t.has_image,t.user_id)}},ew=class e{constructor(e,t,r,i,n,s){this.id=e,this.phoneNumber=t,this.reservedForSecondFactor=r,this.defaultSecondFactor=i,this.verification=n,this.linkedTo=s}static fromJSON(t){return new e(t.id,t.phone_number,t.reserved_for_second_factor,t.default_second_factor,t.verification&&eu.fromJSON(t.verification),t.linked_to.map(e=>ed.fromJSON(e)))}},eS=class e{constructor(e,t,r,i){this.id=e,this.url=t,this.createdAt=r,this.updatedAt=i}static fromJSON(t){return new e(t.id,t.url,t.created_at,t.updated_at)}},eb=class e{constructor(e,t,r,i,n,s,a){this.id=e,this.userId=t,this.token=r,this.status=i,this.url=n,this.createdAt=s,this.updatedAt=a}static fromJSON(t){return new e(t.id,t.user_id,t.token,t.status,t.url,t.created_at,t.updated_at)}},eT=class e{constructor(e,t,r,i,n,s,a){this.id=e,this.fromPhoneNumber=t,this.toPhoneNumber=r,this.message=i,this.status=n,this.phoneNumberId=s,this.data=a}static fromJSON(t){return new e(t.id,t.from_phone_number,t.to_phone_number,t.message,t.status,t.phone_number_id,t.data)}},eC=class e{constructor(e){this.jwt=e}static fromJSON(t){return new e(t.jwt)}},eE=class e{constructor(e,t,r,i,n,s,a,o,c,l){this.id=e,this.name=t,this.domain=r,this.active=i,this.provider=n,this.syncUserAttributes=s,this.allowSubdomains=a,this.allowIdpInitiated=o,this.createdAt=c,this.updatedAt=l}static fromJSON(t){return new e(t.id,t.name,t.domain,t.active,t.provider,t.sync_user_attributes,t.allow_subdomains,t.allow_idp_initiated,t.created_at,t.updated_at)}},eI=class e{constructor(e,t,r,i,n,s,a,o,c){this.id=e,this.provider=t,this.providerUserId=r,this.active=i,this.emailAddress=n,this.firstName=s,this.lastName=a,this.verification=o,this.samlConnection=c}static fromJSON(t){return new e(t.id,t.provider,t.provider_user_id,t.active,t.email_address,t.first_name,t.last_name,t.verification&&eu.fromJSON(t.verification),t.saml_connection&&eE.fromJSON(t.saml_connection))}},eA=class e{constructor(e,t,r){this.id=e,this.web3Wallet=t,this.verification=r}static fromJSON(t){return new e(t.id,t.web3_wallet,t.verification&&eu.fromJSON(t.verification))}},ex=class e{constructor(e,t,r,i,n,s,a,o,c,l,d,u,h,f,p,m,g,y,v,k={},_={},w={},S=[],b=[],T=[],C=[],E=[],I,A,x=null,U){this.id=e,this.passwordEnabled=t,this.totpEnabled=r,this.backupCodeEnabled=i,this.twoFactorEnabled=n,this.banned=s,this.locked=a,this.createdAt=o,this.updatedAt=c,this.imageUrl=l,this.hasImage=d,this.primaryEmailAddressId=u,this.primaryPhoneNumberId=h,this.primaryWeb3WalletId=f,this.lastSignInAt=p,this.externalId=m,this.username=g,this.firstName=y,this.lastName=v,this.publicMetadata=k,this.privateMetadata=_,this.unsafeMetadata=w,this.emailAddresses=S,this.phoneNumbers=b,this.web3Wallets=T,this.externalAccounts=C,this.samlAccounts=E,this.lastActiveAt=I,this.createOrganizationEnabled=A,this.createOrganizationsLimit=x,this.deleteSelfEnabled=U}static fromJSON(t){return new e(t.id,t.password_enabled,t.totp_enabled,t.backup_code_enabled,t.two_factor_enabled,t.banned,t.locked,t.created_at,t.updated_at,t.image_url,t.has_image,t.primary_email_address_id,t.primary_phone_number_id,t.primary_web3_wallet_id,t.last_sign_in_at,t.external_id,t.username,t.first_name,t.last_name,t.public_metadata,t.private_metadata,t.unsafe_metadata,(t.email_addresses||[]).map(e=>eh.fromJSON(e)),(t.phone_numbers||[]).map(e=>ew.fromJSON(e)),(t.web3_wallets||[]).map(e=>eA.fromJSON(e)),(t.external_accounts||[]).map(e=>ef.fromJSON(e)),(t.saml_accounts||[]).map(e=>eI.fromJSON(e)),t.last_active_at,t.create_organization_enabled,t.create_organizations_limit,t.delete_self_enabled)}get primaryEmailAddress(){return this.emailAddresses.find(({id:e})=>e===this.primaryEmailAddressId)??null}get primaryPhoneNumber(){return this.phoneNumbers.find(({id:e})=>e===this.primaryPhoneNumberId)??null}get primaryWeb3Wallet(){return this.web3Wallets.find(({id:e})=>e===this.primaryWeb3WalletId)??null}get fullName(){return[this.firstName,this.lastName].join(" ").trim()||null}};function eU(e){if("string"!=typeof e&&"object"in e&&"deleted"in e)return ec.fromJSON(e);switch(e.object){case em.AllowlistIdentifier:return es.fromJSON(e);case em.Client:return eo.fromJSON(e);case em.EmailAddress:return eh.fromJSON(e);case em.Email:return el.fromJSON(e);case em.Invitation:return ep.fromJSON(e);case em.OauthAccessToken:return eg.fromJSON(e);case em.Organization:return ey.fromJSON(e);case em.OrganizationInvitation:return ev.fromJSON(e);case em.OrganizationMembership:return ek.fromJSON(e);case em.PhoneNumber:return ew.fromJSON(e);case em.RedirectUrl:return eS.fromJSON(e);case em.SignInToken:return eb.fromJSON(e);case em.Session:return ea.fromJSON(e);case em.SmsMessage:return eT.fromJSON(e);case em.Token:return eC.fromJSON(e);case em.TotalCount:return e.total_count;case em.User:return ex.fromJSON(e);default:return e}}function eP(e,t){return e&&"object"==typeof e&&"clerk_trace_id"in e&&"string"==typeof e.clerk_trace_id?e.clerk_trace_id:t?.get("cf-ray")||""}function eO(e){if(e&&"object"==typeof e&&"errors"in e){let t=e.errors;return t.length>0?t.map(s):[]}return[]}function eN(e){var t;let r=(t=async t=>{let r;let{secretKey:n,apiUrl:s=C,apiVersion:a="v1",userAgent:o="@clerk/backend@1.14.1"}=e,{path:l,method:d,queryParams:u,headerParams:h,bodyParams:f,formData:p}=t;en(n);let m=new URL(P(s,a,l));if(u)for(let[e,t]of Object.entries(c({...u})))t&&[t].flat().forEach(t=>m.searchParams.append(e,t));let g={Authorization:`Bearer ${n}`,"User-Agent":o,...h};try{if(p)r=await i.ZE.fetch(m.href,{method:d,headers:g,body:p});else{g["Content-Type"]="application/json";let e="GET"!==d&&f&&Object.keys(f).length>0?{body:JSON.stringify(c(f,{deep:!1}))}:null;r=await i.ZE.fetch(m.href,{method:d,headers:g,...e})}let e=r?.headers&&r.headers?.get(A.Headers.ContentType)===A.ContentTypes.Json,t=await (e?r.json():r.text());if(!r.ok)return{data:null,errors:eO(t),status:r?.status,statusText:r?.statusText,clerkTraceId:eP(t,r?.headers)};return{...Array.isArray(t)?{data:t.map(e=>eU(e))}:t&&"object"==typeof t&&"data"in t&&Array.isArray(t.data)&&void 0!==t.data?{data:t.data.map(e=>eU(e)),totalCount:t.total_count}:{data:eU(t)},errors:null}}catch(e){if(e instanceof Error)return{data:null,errors:[{code:"unexpected_error",message:e.message||"Unexpected error"}],clerkTraceId:eP(e,r?.headers)};return{data:null,errors:eO(e),status:r?.status,statusText:r?.statusText,clerkTraceId:eP(e,r?.headers)}}},async(...e)=>{let{data:r,errors:i,totalCount:n,status:s,statusText:o,clerkTraceId:c}=await t(...e);if(i){let e=new a(o||"",{data:[],status:s,clerkTraceId:c});throw e.errors=i,e}return void 0!==n?{data:r,totalCount:n}:r});return{allowlistIdentifiers:new N(r),clients:new q(r),emailAddresses:new H(r),invitations:new B(r),organizations:new L(r),phoneNumbers:new K(r),redirectUrls:new j(r),sessions:new Y(r),signInTokens:new Q(r),users:new X(r),domains:new R(r),samlConnections:new ee(r),testingTokens:new et(r)}}var eM=e=>()=>{let t={...e};return t.secretKey=(t.secretKey||"").substring(0,7),t.jwtKey=(t.jwtKey||"").substring(0,7),{...t}};function eq(e,t,r){let{act:i,sid:n,org_id:s,org_role:a,org_slug:o,org_permissions:c,sub:l,fva:d}=r,u=eN(e),h=ez({sessionId:n,sessionToken:t,fetcher:async(...e)=>(await u.sessions.getToken(...e)).jwt}),f=d??null;return{actor:i,sessionClaims:r,sessionId:n,userId:l,orgId:s,orgRole:a,orgSlug:o,orgPermissions:c,__experimental_factorVerificationAge:f,getToken:h,has:_({orgId:s,orgRole:a,orgPermissions:c,userId:l,__experimental_factorVerificationAge:f}),debug:eM({...e,sessionToken:t})}}function eR(e){return{sessionClaims:null,sessionId:null,userId:null,actor:null,orgId:null,orgRole:null,orgSlug:null,orgPermissions:null,__experimental_factorVerificationAge:null,getToken:()=>Promise.resolve(null),has:()=>!1,debug:eM(e)}}var ez=e=>{let{fetcher:t,sessionToken:r,sessionId:i}=e||{};return async(e={})=>i?e.template?t(i,e.template):r:null},eH={SignedIn:"signed-in",SignedOut:"signed-out",Handshake:"handshake"},eJ={ClientUATWithoutSessionToken:"client-uat-but-no-session-token",DevBrowserMissing:"dev-browser-missing",DevBrowserSync:"dev-browser-sync",PrimaryRespondsToSyncing:"primary-responds-to-syncing",SatelliteCookieNeedsSyncing:"satellite-needs-syncing",SessionTokenAndUATMissing:"session-token-and-uat-missing",SessionTokenMissing:"session-token-missing",SessionTokenExpired:"session-token-expired",SessionTokenIATBeforeClientUAT:"session-token-iat-before-client-uat",SessionTokenNBF:"session-token-nbf",SessionTokenIatInTheFuture:"session-token-iat-in-the-future",SessionTokenWithoutClientUAT:"session-token-but-no-client-uat",ActiveOrganizationMismatch:"active-organization-mismatch",UnexpectedError:"unexpected-error"};function eB(e,t,r=new Headers,i){let n=eq(e,i,t);return{status:eH.SignedIn,reason:null,message:null,proxyUrl:e.proxyUrl||"",publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!0,toAuth:()=>n,headers:r,token:i}}function eD(e,t,r="",i=new Headers){return eL({status:eH.SignedOut,reason:t,message:r,proxyUrl:e.proxyUrl||"",publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!1,headers:i,toAuth:()=>eR({...e,status:eH.SignedOut,reason:t,message:r}),token:null})}var eL=e=>{let t=new Headers(e.headers||{});if(e.message)try{t.set(A.Headers.AuthMessage,e.message)}catch(e){}if(e.reason)try{t.set(A.Headers.AuthReason,e.reason)}catch(e){}if(e.status)try{t.set(A.Headers.AuthStatus,e.status)}catch(e){}return e.headers=t,e},eF=class extends URL{isCrossOrigin(e){return this.origin!==new URL(e.toString()).origin}},eK=(...e)=>new eF(...e),eW=class extends Request{constructor(e,t){super("string"!=typeof e&&"url"in e?e.url:String(e),t||"string"==typeof e?void 0:e),this.clerkUrl=this.deriveUrlFromHeaders(this),this.cookies=this.parseCookies(this)}toJSON(){return{url:this.clerkUrl.href,method:this.method,headers:JSON.stringify(Object.fromEntries(this.headers)),clerkUrl:this.clerkUrl.toString(),cookies:JSON.stringify(Object.fromEntries(this.cookies))}}deriveUrlFromHeaders(e){let t=new URL(e.url),r=e.headers.get(A.Headers.ForwardedProto),i=e.headers.get(A.Headers.ForwardedHost),n=e.headers.get(A.Headers.Host),s=t.protocol,a=this.getFirstValueFromHeader(i)??n,o=this.getFirstValueFromHeader(r)??s?.replace(/[:/]/,""),c=a&&o?`${o}://${a}`:t.origin;return c===t.origin?eK(t):eK(t.pathname+t.search,c)}getFirstValueFromHeader(e){return e?.split(",")[0]}parseCookies(e){return new Map(Object.entries((0,w.Q)(this.decodeCookieValue(e.headers.get("cookie")||""))))}decodeCookieValue(e){return e?e.replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent):e}},ej=(...e)=>e[0]instanceof eW?e[0]:new eW(...e),e$={},eY=0;function eG(e,t=!0){e$[e.kid]=e,eY=t?Date.now():-1}var eQ="local";function eV(e){if(!e$[eQ]){if(!e)throw new n.oh({action:n.h2.SetClerkJWTKey,message:"Missing local JWK.",reason:n.YU.LocalJWKMissing});eG({kid:"local",kty:"RSA",alg:"RS256",n:e.replace(/(\r\n|\n|\r)/gm,"").replace("-----BEGIN PUBLIC KEY-----","").replace("-----END PUBLIC KEY-----","").replace("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA","").replace("IDAQAB","").replace(/\+/g,"-").replace(/\//g,"_"),e:"AQAB"},!1)}return e$[eQ]}async function eX({secretKey:e,apiUrl:t=C,apiVersion:r="v1",kid:i,skipJwksCache:s}){if(s||function(){if(-1===eY)return!1;let e=Date.now()-eY>=3e5;return e&&(e$={}),e}()||!e$[i]){if(!e)throw new n.oh({action:n.h2.ContactSupport,message:"Failed to load JWKS from Clerk Backend or Frontend API.",reason:n.YU.RemoteJWKFailedToLoad});let{keys:i}=await l(()=>eZ(t,e,r));if(!i||!i.length)throw new n.oh({action:n.h2.ContactSupport,message:"The JWKS endpoint did not contain any signing keys. Contact <EMAIL>.",reason:n.YU.RemoteJWKFailedToLoad});i.forEach(e=>eG(e))}let a=e$[i];if(!a){let e=Object.values(e$).map(e=>e.kid).sort().join(", ");throw new n.oh({action:`Go to your Dashboard and validate your secret and public keys are correct. ${n.h2.ContactSupport} if the issue persists.`,message:`Unable to find a signing key in JWKS that matches the kid='${i}' of the provided session token. Please make sure that the __session cookie or the HTTP authorization header contain a Clerk-generated session JWT. The following kid is available: ${e}`,reason:n.YU.JWKKidMismatch})}return a}async function eZ(e,t,r){if(!t)throw new n.oh({action:n.h2.SetClerkSecretKey,message:"Missing Clerk Secret Key or API Key. Go to https://dashboard.clerk.com and get your key for your instance.",reason:n.YU.RemoteJWKFailedToLoad});let s=new URL(e);s.pathname=P(s.pathname,r,"/jwks");let a=await i.ZE.fetch(s.href,{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}});if(!a.ok){let e=await a.json(),t=e0(e?.errors,n.rE.InvalidSecretKey);if(t){let e=n.YU.InvalidSecretKey;throw new n.oh({action:n.h2.ContactSupport,message:t.message,reason:e})}throw new n.oh({action:n.h2.ContactSupport,message:`Error loading Clerk JWKS from ${s.href} with code=${a.status}`,reason:n.YU.RemoteJWKFailedToLoad})}return a.json()}var e0=(e,t)=>e?e.find(e=>e.code===t):null;async function e1(e,t){let{data:r,errors:s}=(0,i.t5)(e);if(s)return{errors:s};let{header:a}=r,{kid:o}=a;try{let r;if(t.jwtKey)r=eV(t.jwtKey);else{if(!t.secretKey)return{errors:[new n.oh({action:n.h2.SetClerkJWTKey,message:"Failed to resolve JWK during verification.",reason:n.YU.JWKFailedToResolve})]};r=await eX({...t,kid:o})}return await (0,i.$v)(e,{...t,key:r})}catch(e){return{errors:[e]}}}var e2=class{constructor(e,t,r){this.cookieSuffix=e,this.clerkRequest=t,this.initPublishableKeyValues(r),this.initHeaderValues(),this.initCookieValues(),this.initHandshakeValues(),Object.assign(this,r),this.clerkUrl=this.clerkRequest.clerkUrl}get sessionToken(){return this.sessionTokenInCookie||this.sessionTokenInHeader}initPublishableKeyValues(e){var t;t=e.publishableKey,(0,d.nQ)(t,{fatal:!0}),this.publishableKey=e.publishableKey;let r=(0,d.nQ)(this.publishableKey,{fatal:!0,proxyUrl:e.proxyUrl,domain:e.domain});this.instanceType=r.instanceType,this.frontendApi=r.frontendApi}initHeaderValues(){this.sessionTokenInHeader=this.stripAuthorizationHeader(this.getHeader(A.Headers.Authorization)),this.origin=this.getHeader(A.Headers.Origin),this.host=this.getHeader(A.Headers.Host),this.forwardedHost=this.getHeader(A.Headers.ForwardedHost),this.forwardedProto=this.getHeader(A.Headers.CloudFrontForwardedProto)||this.getHeader(A.Headers.ForwardedProto),this.referrer=this.getHeader(A.Headers.Referrer),this.userAgent=this.getHeader(A.Headers.UserAgent),this.secFetchDest=this.getHeader(A.Headers.SecFetchDest),this.accept=this.getHeader(A.Headers.Accept)}initCookieValues(){this.suffixedCookies=this.shouldUseSuffixed(),this.sessionTokenInCookie=this.getSuffixedOrUnSuffixedCookie(A.Cookies.Session),this.refreshTokenInCookie=this.getSuffixedCookie(A.Cookies.Refresh),this.clientUat=Number.parseInt(this.getSuffixedOrUnSuffixedCookie(A.Cookies.ClientUat)||"")||0}initHandshakeValues(){this.devBrowserToken=this.getQueryParam(A.QueryParameters.DevBrowser)||this.getSuffixedOrUnSuffixedCookie(A.Cookies.DevBrowser),this.handshakeToken=this.getQueryParam(A.QueryParameters.Handshake)||this.getCookie(A.Cookies.Handshake),this.handshakeRedirectLoopCounter=Number(this.getCookie(A.Cookies.RedirectCount))||0}stripAuthorizationHeader(e){return e?.replace("Bearer ","")}getQueryParam(e){return this.clerkRequest.clerkUrl.searchParams.get(e)}getHeader(e){return this.clerkRequest.headers.get(e)||void 0}getCookie(e){return this.clerkRequest.cookies.get(e)||void 0}getSuffixedCookie(e){return this.getCookie((0,d._d)(e,this.cookieSuffix))||void 0}getSuffixedOrUnSuffixedCookie(e){return this.suffixedCookies?this.getSuffixedCookie(e):this.getCookie(e)}shouldUseSuffixed(){let e=this.getSuffixedCookie(A.Cookies.ClientUat),t=this.getCookie(A.Cookies.ClientUat),r=this.getSuffixedCookie(A.Cookies.Session)||"",n=this.getCookie(A.Cookies.Session)||"";if(n&&!this.tokenHasIssuer(n))return!1;if(n&&!this.tokenBelongsToInstance(n))return!0;if(!e&&!r)return!1;let{data:s}=(0,i.t5)(n),a=s?.payload.iat||0,{data:o}=(0,i.t5)(r),c=o?.payload.iat||0;if("0"!==e&&"0"!==t&&a>c||"0"===e&&"0"!==t)return!1;if("production"!==this.instanceType){let r=this.sessionExpired(o);if("0"!==e&&"0"===t&&r)return!1}return!!e||!r}tokenHasIssuer(e){let{data:t,errors:r}=(0,i.t5)(e);return!r&&!!t.payload.iss}tokenBelongsToInstance(e){if(!e)return!1;let{data:t,errors:r}=(0,i.t5)(e);if(r)return!1;let n=t.payload.iss.replace(/https?:\/\//gi,"");return this.frontendApi===n}sessionExpired(e){return!!e&&e?.payload.exp<=Date.now()/1e3>>0}},e5=async(e,t)=>new e2(t.publishableKey?await (0,d.P)(t.publishableKey,i.ZE.crypto.subtle):"",e,t),e4=e=>e.split(";")[0]?.split("=")[0],e3=e=>e.split(";")[0]?.split("=")[1];async function e8(e,{key:t}){let{data:r,errors:s}=(0,i.t5)(e);if(s)throw s[0];let{header:a,payload:o}=r,{typ:c,alg:l}=a;(0,i.jl)(c),(0,i.l_)(l);let{data:d,errors:u}=await (0,i.N7)(r,t);if(u)throw new n.oh({reason:n.YU.TokenVerificationFailed,message:`Error verifying handshake token. ${u[0]}`});if(!d)throw new n.oh({reason:n.YU.TokenInvalidSignature,message:"Handshake signature is invalid."});return o}async function e6(e,t){let r;let{secretKey:s,apiUrl:a,apiVersion:o,jwksCacheTtlInMs:c,jwtKey:l,skipJwksCache:d}=t,{data:u,errors:h}=(0,i.t5)(e);if(h)throw h[0];let{kid:f}=u.header;if(l)r=eV(l);else if(s)r=await eX({secretKey:s,apiUrl:a,apiVersion:o,kid:f,jwksCacheTtlInMs:c,skipJwksCache:d});else throw new n.oh({action:n.h2.SetClerkJWTKey,message:"Failed to resolve JWK during handshake verification.",reason:n.YU.JWKFailedToResolve});return await e8(e,{key:r})}var e7={NonEligibleNoCookie:"non-eligible-no-refresh-cookie",NonEligibleNonGet:"non-eligible-non-get",InvalidSessionToken:"invalid-session-token",MissingApiClient:"missing-api-client",MissingSessionToken:"missing-session-token",MissingRefreshToken:"missing-refresh-token",ExpiredSessionTokenDecodeFailed:"expired-session-token-decode-failed",ExpiredSessionTokenMissingSidClaim:"expired-session-token-missing-sid-claim",FetchError:"fetch-error",UnexpectedSDKError:"unexpected-sdk-error"};async function e9(e,t){let r=await e5(ej(e),t);en(r.secretKey),r.isSatellite&&(function(e,t){if(!e&&(0,d.CY)(t))throw Error("Missing signInUrl. Pass a signInUrl for dev instances if an app is satellite")}(r.signInUrl,r.secretKey),r.signInUrl&&r.origin&&function(e,t){let r;try{r=new URL(e)}catch{throw Error("The signInUrl needs to have a absolute url format.")}if(r.origin===t)throw Error("The signInUrl needs to be on a different origin than your satellite application.")}(r.signInUrl,r.origin),function(e){if(!e)throw Error("Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl")}(r.proxyUrl||r.domain));let s=function(e){let t=null;if(e?.personalAccountPatterns)try{t=T(e.personalAccountPatterns)}catch(t){throw Error(`Invalid personal account pattern "${e.personalAccountPatterns}": "${t}"`)}let r=null;if(e?.organizationPatterns)try{r=T(e.organizationPatterns)}catch(t){throw Error(`Clerk: Invalid organization pattern "${e.organizationPatterns}": "${t}"`)}return{OrganizationMatcher:r,PersonalAccountMatcher:t}}(t.organizationSyncOptions);async function a(){let e=new Headers({"Access-Control-Allow-Origin":"null","Access-Control-Allow-Credentials":"true"}),t=(await e6(r.handshakeToken,r)).handshake,i="";if(t.forEach(t=>{e.append("Set-Cookie",t),e4(t).startsWith(A.Cookies.Session)&&(i=e3(t))}),"development"===r.instanceType){let t=new URL(r.clerkUrl);t.searchParams.delete(A.QueryParameters.Handshake),t.searchParams.delete(A.QueryParameters.HandshakeHelp),e.append(A.Headers.Location,t.toString()),e.set(A.Headers.CacheControl,"no-store")}if(""===i)return eD(r,eJ.SessionTokenMissing,"",e);let{data:s,errors:[a]=[]}=await e1(i,r);if(s)return eB(r,s,e,i);if("development"===r.instanceType&&(a?.reason===n.YU.TokenExpired||a?.reason===n.YU.TokenNotActiveYet||a?.reason===n.YU.TokenIatInTheFuture)){a.tokenCarrier="cookie",console.error(`Clerk: Clock skew detected. This usually means that your system clock is inaccurate. Clerk will attempt to account for the clock skew in development.

To resolve this issue, make sure your system's clock is set to the correct time (e.g. turn off and on automatic time synchronization).

---

${a.getFullMessage()}`);let{data:t,errors:[n]=[]}=await e1(i,{...r,clockSkewInMs:864e5});if(t)return eB(r,t,e,i);throw n}throw a}async function o(r){if(!t.apiClient)return{data:null,error:{message:"An apiClient is needed to perform token refresh.",cause:{reason:e7.MissingApiClient}}};let{sessionToken:n,refreshTokenInCookie:s}=r;if(!n)return{data:null,error:{message:"Session token must be provided.",cause:{reason:e7.MissingSessionToken}}};if(!s)return{data:null,error:{message:"Refresh token must be provided.",cause:{reason:e7.MissingRefreshToken}}};let{data:a,errors:o}=(0,i.t5)(n);if(!a||o)return{data:null,error:{message:"Unable to decode the expired session token.",cause:{reason:e7.ExpiredSessionTokenDecodeFailed,errors:o}}};if(!a?.payload?.sid)return{data:null,error:{message:"Expired session token is missing the `sid` claim.",cause:{reason:e7.ExpiredSessionTokenMissingSidClaim}}};try{return{data:(await t.apiClient.sessions.refreshSession(a.payload.sid,{expired_token:n||"",refresh_token:s||"",request_origin:r.clerkUrl.origin,request_headers:Object.fromEntries(Array.from(e.headers.entries()).map(([e,t])=>[e,[t]]))})).jwt,error:null}}catch(e){if(!e?.errors?.length)return{data:null,error:e};if("unexpected_error"===e.errors[0].code)return{data:null,error:{message:"Fetch unexpected error",cause:{reason:e7.FetchError,errors:e.errors}}};return{data:null,error:{message:e.errors[0].code,cause:{reason:e.errors[0].code,errors:e.errors}}}}}async function c(e){let{data:t,error:r}=await o(e);if(!t)return{data:null,error:r};let{data:i,errors:n}=await e1(t,e);return n?{data:null,error:{message:"Clerk: unable to verify refreshed session token.",cause:{reason:e7.InvalidSessionToken,errors:n}}}:{data:{jwtPayload:i,sessionToken:t},error:null}}function l(e,i,n,a){if(function(e){let{accept:t,secFetchDest:r}=e;return!!("document"===r||"iframe"===r||!r&&t?.startsWith("text/html"))}(e)){let o=a??function({handshakeReason:e}){let i=function(e){let t=new URL(e);return t.searchParams.delete(A.QueryParameters.DevBrowser),t.searchParams.delete(A.QueryParameters.LegacyDevBrowser),t}(r.clerkUrl),n=r.frontendApi.replace(/http(s)?:\/\//,""),a=new URL(`https://${n}/v1/client/handshake`);a.searchParams.append("redirect_url",i?.href||""),a.searchParams.append("suffixed_cookies",r.suffixedCookies.toString()),a.searchParams.append(A.QueryParameters.HandshakeReason,e),"development"===r.instanceType&&r.devBrowserToken&&a.searchParams.append(A.QueryParameters.DevBrowser,r.devBrowserToken);let o=tt(r.clerkUrl,t.organizationSyncOptions,s);return o&&(function(e){let t=new Map;return"personalAccount"===e.type&&t.set("organization_id",""),"organization"===e.type&&(e.organizationId&&t.set("organization_id",e.organizationId),e.organizationSlug&&t.set("organization_id",e.organizationSlug)),t})(o).forEach((e,t)=>{a.searchParams.append(t,e)}),new Headers({[A.Headers.Location]:a.href})}({handshakeReason:i});return(o.get(A.Headers.Location)&&o.set(A.Headers.CacheControl,"no-store"),function(e){if(3===r.handshakeRedirectLoopCounter)return!0;let t=r.handshakeRedirectLoopCounter+1,i=A.Cookies.RedirectCount;return e.append("Set-Cookie",`${i}=${t}; SameSite=Lax; HttpOnly; Max-Age=3`),!1}(o))?(console.log("Clerk: Refreshing the session token resulted in an infinite redirect loop. This usually means that your Clerk instance keys do not match - make sure to copy the correct publishable and secret keys from the Clerk dashboard."),eD(e,i,n)):function(e,t,r="",i){return eL({status:eH.Handshake,reason:t,message:r,publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",proxyUrl:e.proxyUrl||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!1,headers:i,toAuth:()=>null,token:null})}(e,i,n,o)}return eD(e,i,n)}async function u(){let{sessionTokenInHeader:e}=r;try{let{data:t,errors:i}=await e1(e,r);if(i)throw i[0];return eB(r,t,void 0,e)}catch(e){return f(e,"header")}}async function h(){let e=r.clientUat,o=!!r.sessionTokenInCookie,c=!!r.devBrowserToken,d=r.isSatellite&&"document"===r.secFetchDest&&!r.clerkUrl.searchParams.has(A.QueryParameters.ClerkSynced);if(r.handshakeToken)try{return await a()}catch(e){e instanceof n.oh&&"development"===r.instanceType?function(e){if(e.reason===n.YU.TokenInvalidSignature)throw Error("Clerk: Handshake token verification failed due to an invalid signature. If you have switched Clerk keys locally, clear your cookies and try again.");throw Error(`Clerk: Handshake token verification failed: ${e.getFullMessage()}.`)}(e):console.error("Clerk: unable to resolve handshake:",e)}if("development"===r.instanceType&&r.clerkUrl.searchParams.has(A.QueryParameters.DevBrowser))return l(r,eJ.DevBrowserSync,"");if("production"===r.instanceType&&d)return l(r,eJ.SatelliteCookieNeedsSyncing,"");if("development"===r.instanceType&&d){let e=new URL(r.signInUrl);e.searchParams.append(A.QueryParameters.ClerkRedirectUrl,r.clerkUrl.toString());let t=eJ.SatelliteCookieNeedsSyncing;return e.searchParams.append(A.QueryParameters.HandshakeReason,t),l(r,t,"",new Headers({[A.Headers.Location]:e.toString()}))}let u=new URL(r.clerkUrl).searchParams.get(A.QueryParameters.ClerkRedirectUrl);if("development"===r.instanceType&&!r.isSatellite&&u){let e=new URL(u);r.devBrowserToken&&e.searchParams.append(A.QueryParameters.DevBrowser,r.devBrowserToken),e.searchParams.append(A.QueryParameters.ClerkSynced,"true");let t=eJ.PrimaryRespondsToSyncing;return e.searchParams.append(A.QueryParameters.HandshakeReason,t),l(r,t,"",new Headers({[A.Headers.Location]:e.toString()}))}if("development"===r.instanceType&&!c)return l(r,eJ.DevBrowserMissing,"");if(!e&&!o)return eD(r,eJ.SessionTokenAndUATMissing,"");if(!e&&o)return l(r,eJ.SessionTokenWithoutClientUAT,"");if(e&&!o)return l(r,eJ.ClientUATWithoutSessionToken,"");let{data:h,errors:p}=(0,i.t5)(r.sessionTokenInCookie);if(p)return f(p[0],"cookie");if(h.payload.iat<r.clientUat)return l(r,eJ.SessionTokenIATBeforeClientUAT,"");try{let{data:e,errors:i}=await e1(r.sessionTokenInCookie,r);if(i)throw i[0];let n=eB(r,e,void 0,r.sessionTokenInCookie),a=function(e,r){let i=tt(e.clerkUrl,t.organizationSyncOptions,s);if(!i)return null;let n=!1;if("organization"===i.type&&(i.organizationSlug&&i.organizationSlug!==r.orgSlug&&(n=!0),i.organizationId&&i.organizationId!==r.orgId&&(n=!0)),"personalAccount"===i.type&&r.orgId&&(n=!0),!n)return null;if(e.handshakeRedirectLoopCounter>0)return console.warn("Clerk: Organization activation handshake loop detected. This is likely due to an invalid organization ID or slug. Skipping organization activation."),null;let a=l(e,eJ.ActiveOrganizationMismatch,"");return"handshake"!==a.status?null:a}(r,n.toAuth());if(a)return a;return n}catch(e){return f(e,"cookie")}return eD(r,eJ.UnexpectedError)}async function f(t,i){let s;if(!(t instanceof n.oh))return eD(r,eJ.UnexpectedError);if(t.reason===n.YU.TokenExpired&&r.refreshTokenInCookie&&"GET"===e.method){let{data:e,error:t}=await c(r);if(e)return eB(r,e.jwtPayload,void 0,e.sessionToken);s=t?.cause?.reason?t.cause.reason:e7.UnexpectedSDKError}else s="GET"!==e.method?e7.NonEligibleNonGet:r.refreshTokenInCookie?null:e7.NonEligibleNoCookie;return(t.tokenCarrier=i,[n.YU.TokenExpired,n.YU.TokenNotActiveYet,n.YU.TokenIatInTheFuture].includes(t.reason))?l(r,tr({tokenError:t.reason,refreshError:s}),t.getFullMessage()):eD(r,t.reason,t.getFullMessage())}return r.sessionTokenInHeader?u():h()}var te=e=>{let{isSignedIn:t,proxyUrl:r,reason:i,message:n,publishableKey:s,isSatellite:a,domain:o}=e;return{isSignedIn:t,proxyUrl:r,reason:i,message:n,publishableKey:s,isSatellite:a,domain:o}};function tt(e,t,r){if(!t)return null;if(r.OrganizationMatcher){let i;try{i=r.OrganizationMatcher(e.pathname)}catch(e){return console.error(`Clerk: Failed to apply organization pattern "${t.organizationPatterns}" to a path`,e),null}if(i&&"params"in i){let e=i.params;if("id"in e&&"string"==typeof e.id)return{type:"organization",organizationId:e.id};if("slug"in e&&"string"==typeof e.slug)return{type:"organization",organizationSlug:e.slug};console.warn("Clerk: Detected an organization pattern match, but no organization ID or slug was found in the URL. Does the pattern include `:id` or `:slug`?")}}if(r.PersonalAccountMatcher){let i;try{i=r.PersonalAccountMatcher(e.pathname)}catch(e){return console.error(`Failed to apply personal account pattern "${t.personalAccountPatterns}" to a path`,e),null}if(i)return{type:"personalAccount"}}return null}var tr=({tokenError:e,refreshError:t})=>{switch(e){case n.YU.TokenExpired:return`${eJ.SessionTokenExpired}-refresh-${t}`;case n.YU.TokenNotActiveYet:return eJ.SessionTokenNBF;case n.YU.TokenIatInTheFuture:return eJ.SessionTokenIatInTheFuture;default:return eJ.UnexpectedError}};function ti(e,t){return Object.keys(e).reduce((e,r)=>({...e,[r]:t[r]||e[r]}),{...e})}var tn={secretKey:"",jwtKey:"",apiUrl:void 0,apiVersion:void 0,proxyUrl:"",publishableKey:"",isSatellite:!1,domain:"",audience:""};function ts(e){let t=ti(tn,e.options),r=e.apiClient;return{authenticateRequest:(e,i={})=>{let{apiUrl:n,apiVersion:s}=t,a=ti(t,i);return e9(e,{...i,...a,apiUrl:n,apiVersion:s,apiClient:r})},debugRequestState:te}}},2829:(e,t,r)=>{"use strict";function i(e){return async(...t)=>{let{data:r,errors:i}=await e(...t);if(i)throw i[0];return r}}function n(e){return(...t)=>{let{data:r,errors:i}=e(...t);if(i)throw i[0];return r}}r.d(t,{A:()=>i,t:()=>n})},5036:(e,t,r)=>{"use strict";r.d(t,{l_:()=>y,jl:()=>g,tH:()=>o,t5:()=>C,DO:()=>f,N7:()=>T,uI:()=>b,ZE:()=>a,$v:()=>E});var i=r(9102);let n=crypto;var s=r(588);r(3369);var a={crypto:n,fetch:fetch.bind(globalThis),AbortController:globalThis.AbortController,Blob:globalThis.Blob,FormData:globalThis.FormData,Headers:globalThis.Headers,Request:globalThis.Request,Response:globalThis.Response},o={parse:(e,t)=>(function(e,t,r={}){if(!t.codes){t.codes={};for(let e=0;e<t.chars.length;++e)t.codes[t.chars[e]]=e}if(!r.loose&&e.length*t.bits&7)throw SyntaxError("Invalid padding");let i=e.length;for(;"="===e[i-1];)if(--i,!r.loose&&!((e.length-i)*t.bits&7))throw SyntaxError("Invalid padding");let n=new(r.out??Uint8Array)(i*t.bits/8|0),s=0,a=0,o=0;for(let r=0;r<i;++r){let i=t.codes[e[r]];if(void 0===i)throw SyntaxError("Invalid character "+e[r]);a=a<<t.bits|i,(s+=t.bits)>=8&&(s-=8,n[o++]=255&a>>s)}if(s>=t.bits||255&a<<8-s)throw SyntaxError("Unexpected end of data");return n})(e,c,t),stringify:(e,t)=>(function(e,t,r={}){let{pad:i=!0}=r,n=(1<<t.bits)-1,s="",a=0,o=0;for(let r=0;r<e.length;++r)for(o=o<<8|255&e[r],a+=8;a>t.bits;)a-=t.bits,s+=t.chars[n&o>>a];if(a&&(s+=t.chars[n&o<<t.bits-a]),i)for(;s.length*t.bits&7;)s+="=";return s})(e,c,t)},c={chars:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bits:6},l={RS256:"SHA-256",RS384:"SHA-384",RS512:"SHA-512"},d="RSASSA-PKCS1-v1_5",u={RS256:d,RS384:d,RS512:d},h=Object.keys(l);function f(e){let t=l[e],r=u[e];if(!t||!r)throw Error(`Unsupported algorithm ${e}, expected one of ${h.join(",")}.`);return{hash:{name:l[e]},name:u[e]}}var p=e=>Array.isArray(e)&&e.length>0&&e.every(e=>"string"==typeof e),m=(e,t)=>{let r=[t].flat().filter(e=>!!e),n=[e].flat().filter(e=>!!e);if(r.length>0&&n.length>0){if("string"==typeof e){if(!r.includes(e))throw new i.oh({action:i.h2.EnsureClerkJWT,reason:i.YU.TokenVerificationFailed,message:`Invalid JWT audience claim (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}else if(p(e)&&!e.some(e=>r.includes(e)))throw new i.oh({action:i.h2.EnsureClerkJWT,reason:i.YU.TokenVerificationFailed,message:`Invalid JWT audience claim array (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}},g=e=>{if(void 0!==e&&"JWT"!==e)throw new i.oh({action:i.h2.EnsureClerkJWT,reason:i.YU.TokenInvalid,message:`Invalid JWT type ${JSON.stringify(e)}. Expected "JWT".`})},y=e=>{if(!h.includes(e))throw new i.oh({action:i.h2.EnsureClerkJWT,reason:i.YU.TokenInvalidAlgorithm,message:`Invalid JWT algorithm ${JSON.stringify(e)}. Supported: ${h}.`})},v=e=>{if("string"!=typeof e)throw new i.oh({action:i.h2.EnsureClerkJWT,reason:i.YU.TokenVerificationFailed,message:`Subject claim (sub) is required and must be a string. Received ${JSON.stringify(e)}.`})},k=(e,t)=>{if(e&&t&&0!==t.length&&!t.includes(e))throw new i.oh({reason:i.YU.TokenInvalidAuthorizedParties,message:`Invalid JWT Authorized party claim (azp) ${JSON.stringify(e)}. Expected "${t}".`})},_=(e,t)=>{if("number"!=typeof e)throw new i.oh({action:i.h2.EnsureClerkJWT,reason:i.YU.TokenVerificationFailed,message:`Invalid JWT expiry date claim (exp) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),n=new Date(0);if(n.setUTCSeconds(e),n.getTime()<=r.getTime()-t)throw new i.oh({reason:i.YU.TokenExpired,message:`JWT is expired. Expiry date: ${n.toUTCString()}, Current date: ${r.toUTCString()}.`})},w=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new i.oh({action:i.h2.EnsureClerkJWT,reason:i.YU.TokenVerificationFailed,message:`Invalid JWT not before date claim (nbf) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),n=new Date(0);if(n.setUTCSeconds(e),n.getTime()>r.getTime()+t)throw new i.oh({reason:i.YU.TokenNotActiveYet,message:`JWT cannot be used prior to not before date claim (nbf). Not before date: ${n.toUTCString()}; Current date: ${r.toUTCString()};`})},S=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new i.oh({action:i.h2.EnsureClerkJWT,reason:i.YU.TokenVerificationFailed,message:`Invalid JWT issued at date claim (iat) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),n=new Date(0);if(n.setUTCSeconds(e),n.getTime()>r.getTime()+t)throw new i.oh({reason:i.YU.TokenIatInTheFuture,message:`JWT issued at date claim (iat) is in the future. Issued at date: ${n.toUTCString()}; Current date: ${r.toUTCString()};`})};function b(e,t,r){if("object"==typeof e)return a.crypto.subtle.importKey("jwk",e,t,!1,[r]);let i=function(e){let t=e.replace(/-----BEGIN.*?-----/g,"").replace(/-----END.*?-----/g,"").replace(/\s/g,""),r=(0,s.S)(t),i=new Uint8Array(new ArrayBuffer(r.length));for(let e=0,t=r.length;e<t;e++)i[e]=r.charCodeAt(e);return i}(e),n="sign"===r?"pkcs8":"spki";return a.crypto.subtle.importKey(n,i,t,!1,[r])}async function T(e,t){let{header:r,signature:n,raw:s}=e,o=new TextEncoder().encode([s.header,s.payload].join(".")),c=f(r.alg);try{let e=await b(t,c,"verify");return{data:await a.crypto.subtle.verify(c.name,e,n,o)}}catch(e){return{errors:[new i.oh({reason:i.YU.TokenInvalidSignature,message:e?.message})]}}}function C(e){let t=(e||"").toString().split(".");if(3!==t.length)return{errors:[new i.oh({reason:i.YU.TokenInvalid,message:"Invalid JWT form. A JWT consists of three parts separated by dots."})]};let[r,n,s]=t,a=new TextDecoder,c=JSON.parse(a.decode(o.parse(r,{loose:!0})));return{data:{header:c,payload:JSON.parse(a.decode(o.parse(n,{loose:!0}))),signature:o.parse(s,{loose:!0}),raw:{header:r,payload:n,signature:s,text:e}}}}async function E(e,t){let{audience:r,authorizedParties:n,clockSkewInMs:s,key:a}=t,o=s||5e3,{data:c,errors:l}=C(e);if(l)return{errors:l};let{header:d,payload:u}=c;try{let{typ:e,alg:t}=d;g(e),y(t);let{azp:i,sub:s,aud:a,iat:c,exp:l,nbf:h}=u;v(s),m([a],[r]),k(i,n),_(l,o),w(h,o),S(c,o)}catch(e){return{errors:[e]}}let{data:h,errors:f}=await T(c,a);return f?{errors:[new i.oh({action:i.h2.EnsureClerkJWT,reason:i.YU.TokenVerificationFailed,message:`Error verifying JWT signature. ${f[0]}`})]}:h?{data:u}:{errors:[new i.oh({reason:i.YU.TokenInvalidSignature,message:"JWT signature is invalid."})]}}},6449:(e,t,r)=>{"use strict";r.d(t,{B9:()=>i.B9,JH:()=>i.JH,WL:()=>i.WL,_G:()=>i._G,bI:()=>o,tD:()=>i.tD});var i=r(3916);r(5036),r(9102);var n=(e,t,r,n)=>{if(""===e)return s(t.toString(),r?.toString());let a=new URL(e),o=r?new URL(r,a):void 0,c=new URL(t,a);return o&&c.searchParams.set("redirect_url",o.toString()),n&&a.hostname!==c.hostname&&c.searchParams.set(i._G.QueryParameters.DevBrowser,n),c.toString()},s=(e,t)=>{let r;if(e.startsWith("http"))r=new URL(e);else{if(!t||!t.startsWith("http"))throw Error("destination url or return back url should be an absolute path url!");let i=new URL(t);r=new URL(e,i.origin)}return t&&r.searchParams.set("redirect_url",t),r.toString()},a=e=>{if(!e)return"";let t=e.replace(/(clerk\.accountsstage\.)/,"accountsstage.").replace(/(clerk\.accounts\.|clerk\.)/,"accounts.");return`https://${t}`},o=e=>{let{publishableKey:t,redirectAdapter:r,signInUrl:s,signUpUrl:o,baseUrl:c}=e,l=(0,i.nQ)(t),d=l?.frontendApi,u=l?.instanceType==="development",h=a(d);return{redirectToSignUp:({returnBackUrl:t}={})=>{o||h||i.RM.throwMissingPublishableKeyError();let s=`${h}/sign-up`;return r(n(c,o||s,t,u?e.devBrowserToken:null))},redirectToSignIn:({returnBackUrl:t}={})=>{s||h||i.RM.throwMissingPublishableKeyError();let a=`${h}/sign-in`;return r(n(c,s||a,t,u?e.devBrowserToken:null))}}}},3369:(e,t,r)=>{"use strict";r.d(t,{Ko:()=>a,U9:()=>c,ac:()=>s,qx:()=>o}),Object.getOwnPropertyDescriptor,Object.getOwnPropertyNames,Object.prototype.hasOwnProperty;var i=e=>{throw TypeError(e)},n=(e,t,r)=>t.has(e)||i("Cannot "+r),s=(e,t,r)=>(n(e,t,"read from private field"),r?r.call(e):t.get(e)),a=(e,t,r)=>t.has(e)?i("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),o=(e,t,r,i)=>(n(e,t,"write to private field"),i?i.call(e,r):t.set(e,r),r),c=(e,t,r)=>(n(e,t,"access private method"),r)},3967:(e,t,r)=>{"use strict";r.d(t,{Fo:()=>o,Iq:()=>c,Xv:()=>l,cM:()=>a,iF:()=>s,mv:()=>i,vO:()=>n});var i=[".lcl.dev",".lclstage.dev",".lclclerk.com"],n=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],s=[".lcl.dev","lclstage.dev",".lclclerk.com",".accounts.lclclerk.com"],a=[".accountsstage.dev"],o="https://api.lclclerk.com",c="https://api.clerkstage.dev",l="https://api.clerk.com"},1696:(e,t,r)=>{"use strict";r.d(t,{MY:()=>c,P:()=>d,_d:()=>u,CY:()=>l,nQ:()=>o});var i=r(588),n=e=>"undefined"!=typeof btoa&&"function"==typeof btoa?btoa(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e).toString("base64"):e,s=r(3967),a="pk_live_";function o(e,t={}){if(!(e=e||"")||!function(e){let t=(e=e||"").startsWith(a)||e.startsWith("pk_test_"),r=(0,i.S)(e.split("_")[2]||"").endsWith("$");return t&&r}(e)){if(t.fatal)throw Error("Publishable key not valid.");return null}let r=e.startsWith(a)?"production":"development",n=(0,i.S)(e.split("_")[2]);return n=n.slice(0,-1),t.proxyUrl?n=t.proxyUrl:"development"!==r&&t.domain&&(n=`clerk.${t.domain}`),{instanceType:r,frontendApi:n}}function c(){let e=new Map;return{isDevOrStagingUrl:t=>{if(!t)return!1;let r="string"==typeof t?t:t.hostname,i=e.get(r);return void 0===i&&(i=s.vO.some(e=>r.endsWith(e)),e.set(r,i)),i}}}function l(e){return e.startsWith("test_")||e.startsWith("sk_test_")}async function d(e,t=globalThis.crypto.subtle){let r=new TextEncoder().encode(e);return n(String.fromCharCode(...new Uint8Array(await t.digest("sha-1",r)))).replace(/\+/gi,"-").replace(/\//gi,"_").substring(0,8)}var u=(e,t)=>`${e}_${t}`},1657:(e,t,r)=>{"use strict";r.d(t,{fQ:()=>n});var i=e=>{let t=r=>{if(!r)return r;if(Array.isArray(r))return r.map(e=>"object"==typeof e||Array.isArray(e)?t(e):e);let i={...r};for(let r of Object.keys(i)){let n=e(r.toString());n!==r&&(i[n]=i[r],delete i[r]),"object"==typeof i[n]&&(i[n]=t(i[n]))}return i};return t};function n(e){if("boolean"==typeof e)return e;if(null==e)return!1;if("string"==typeof e){if("true"===e.toLowerCase())return!0;if("false"===e.toLowerCase())return!1}let t=parseInt(e,10);return!isNaN(t)&&t>0}i(function(e){return e?e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`):""}),i(function(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,"")):""})},588:(e,t,r)=>{"use strict";r.d(t,{S:()=>i});var i=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e},6751:(e,t,r)=>{"use strict";r.d(t,{x9:()=>a});var i=()=>!1,n=()=>{try{return!0}catch(e){}return!1},s=new Set,a=(e,t,r)=>{let a=i()||n(),o=null!=r?r:e;s.has(o)||a||(s.add(o),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))};r(3369)},9241:(e,t,r)=>{"use strict";r.d(t,{CY:()=>i.CY,MY:()=>i.MY,P:()=>i.P,_d:()=>i._d,nQ:()=>i.nQ});var i=r(1696);r(3369)}}]);
//# sourceMappingURL=288.js.map