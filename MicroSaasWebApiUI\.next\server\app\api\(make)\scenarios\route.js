(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[284],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},6195:e=>{"use strict";e.exports=require("node:buffer")},2480:()=>{},9029:(e,r,a)=>{"use strict";a.r(r),a.d(r,{ComponentMod:()=>v,default:()=>g});var s={};a.r(s),a.d(s,{GET:()=>f,dynamic:()=>l,runtime:()=>k});var o={};a.r(o),a.d(o,{originalPathname:()=>A,patchFetch:()=>P,requestAsyncStorage:()=>m,routeModule:()=>_,serverHooks:()=>h,staticGenerationAsyncStorage:()=>E});var t=a(932),n=a(2561),i=a(4828),u=a(6631),c=a(462),p=a(1175),d=a(9985);let l="force-dynamic",k="edge";async function f(){try{if(!await (0,c.a)())return d.xk.json({error:"Unauthorized"},{status:401});let e=await p.Z.get(`${process.env.MAKE_API_URL}/scenarios?teamId=${process.env.MAKE_TEAM_ID}`,{headers:{Authorization:`Token ${process.env.MAKE_API_KEY}`}});if(!e.data?.scenarios)return d.xk.json({error:"No scenarios found"},{status:404});return d.xk.json({success:!0,scenarios:e.data.scenarios})}catch(e){return console.error("Connection error:",e),d.xk.json({error:e?.message||"An unexpected error occurred"},{status:500})}}let _=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/(make)/scenarios/route",pathname:"/api/scenarios",filename:"route",bundlePath:"app/api/(make)/scenarios/route"},resolvedPagePath:"C:\\Projects\\PersonalPortal\\src\\app\\api\\(make)\\scenarios\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:m,staticGenerationAsyncStorage:E,serverHooks:h}=_,A="/api/(make)/scenarios/route";function P(){return(0,u.XH)({serverHooks:h,staticGenerationAsyncStorage:E})}let v=o,g=t.a.wrap(_)}},e=>{var r=r=>e(e.s=r);e.O(0,[520,288,436,657,551],()=>r(9029));var a=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/(make)/scenarios/route"]=a}]);
//# sourceMappingURL=route.js.map