(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[735],{5305:(e,t,r)=>{"use strict";var o=r(5705),n=r(8754),i=r(5755),a=r(7939);e.exports=a||o.call(i,n)},4565:(e,t,r)=>{"use strict";var o=r(5705),n=r(8754),i=r(5305);e.exports=function(){return i(o,n,arguments)}},8754:e=>{"use strict";e.exports=Function.prototype.apply},5755:e=>{"use strict";e.exports=Function.prototype.call},7739:(e,t,r)=>{"use strict";var o=r(5705),n=r(1761),i=r(5755),a=r(5305);e.exports=function(e){if(e.length<1||"function"!=typeof e[0])throw new n("a function is required");return a(o,i,e)}},7939:e=>{"use strict";e.exports="function"==typeof Reflect&&Reflect.apply},7324:(e,t,r)=>{"use strict";var o=r(3424),n=r(9856),i=n(o("String.prototype.indexOf"));e.exports=function(e,t){var r=o(e,!!t);return"function"==typeof r&&i(e,".prototype.")>-1?n(r):r}},9856:(e,t,r)=>{"use strict";var o=r(7477),n=r(7747),i=r(7739),a=r(4565);e.exports=function(e){var t=i(arguments),r=e.length-(arguments.length-1);return o(t,1+(r>0?r:0),!0)},n?n(e.exports,"apply",{value:a}):e.exports.apply=a},510:(e,t,r)=>{"use strict";var o=r(7747),n=r(7531),i=r(1761),a=r(5489);e.exports=function(e,t,r){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new i("`obj` must be an object or a function`");if("string"!=typeof t&&"symbol"!=typeof t)throw new i("`property` must be a string or a symbol`");if(arguments.length>3&&"boolean"!=typeof arguments[3]&&null!==arguments[3])throw new i("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&"boolean"!=typeof arguments[4]&&null!==arguments[4])throw new i("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&"boolean"!=typeof arguments[5]&&null!==arguments[5])throw new i("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&"boolean"!=typeof arguments[6])throw new i("`loose`, if provided, must be a boolean");var s=arguments.length>3?arguments[3]:null,l=arguments.length>4?arguments[4]:null,u=arguments.length>5?arguments[5]:null,c=arguments.length>6&&arguments[6],h=!!a&&a(e,t);if(o)o(e,t,{configurable:null===u&&h?h.configurable:!u,enumerable:null===s&&h?h.enumerable:!s,value:r,writable:null===l&&h?h.writable:!l});else if(!c&&(s||l||u))throw new n("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.");else e[t]=r}},7747:(e,t,r)=>{"use strict";var o=r(3424)("%Object.defineProperty%",!0)||!1;if(o)try{o({},"a",{value:1})}catch(e){o=!1}e.exports=o},5634:e=>{"use strict";e.exports=EvalError},3032:e=>{"use strict";e.exports=Error},4160:e=>{"use strict";e.exports=RangeError},255:e=>{"use strict";e.exports=ReferenceError},7531:e=>{"use strict";e.exports=SyntaxError},1761:e=>{"use strict";e.exports=TypeError},744:e=>{"use strict";e.exports=URIError},186:e=>{"use strict";var t=Object.prototype.toString,r=Math.max,o=function(e,t){for(var r=[],o=0;o<e.length;o+=1)r[o]=e[o];for(var n=0;n<t.length;n+=1)r[n+e.length]=t[n];return r},n=function(e,t){for(var r=[],o=t||0,n=0;o<e.length;o+=1,n+=1)r[n]=e[o];return r},i=function(e,t){for(var r="",o=0;o<e.length;o+=1)r+=e[o],o+1<e.length&&(r+=t);return r};e.exports=function(e){var a,s=this;if("function"!=typeof s||"[object Function]"!==t.apply(s))throw TypeError("Function.prototype.bind called on incompatible "+s);for(var l=n(arguments,1),u=r(0,s.length-l.length),c=[],h=0;h<u;h++)c[h]="$"+h;if(a=Function("binder","return function ("+i(c,",")+"){ return binder.apply(this,arguments); }")(function(){if(this instanceof a){var t=s.apply(this,o(l,arguments));return Object(t)===t?t:this}return s.apply(e,o(l,arguments))}),s.prototype){var d=function(){};d.prototype=s.prototype,a.prototype=new d,d.prototype=null}return a}},5705:(e,t,r)=>{"use strict";var o=r(186);e.exports=Function.prototype.bind||o},3424:(e,t,r)=>{"use strict";var o,n=r(3032),i=r(5634),a=r(4160),s=r(255),l=r(7531),u=r(1761),c=r(744),h=Function,d=function(e){try{return h('"use strict"; return ('+e+").constructor;")()}catch(e){}},p=Object.getOwnPropertyDescriptor;if(p)try{p({},"")}catch(e){p=null}var m=function(){throw new u},f=p?function(){try{return arguments.callee,m}catch(e){try{return p(arguments,"callee").get}catch(e){return m}}}():m,y=r(8126)(),v=r(8860)(),P=Object.getPrototypeOf||(v?function(e){return e.__proto__}:null),g={},T="undefined"!=typeof Uint8Array&&P?P(Uint8Array):o,_={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?o:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?o:ArrayBuffer,"%ArrayIteratorPrototype%":y&&P?P([][Symbol.iterator]()):o,"%AsyncFromSyncIteratorPrototype%":o,"%AsyncFunction%":g,"%AsyncGenerator%":g,"%AsyncGeneratorFunction%":g,"%AsyncIteratorPrototype%":g,"%Atomics%":"undefined"==typeof Atomics?o:Atomics,"%BigInt%":"undefined"==typeof BigInt?o:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?o:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?o:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?o:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":n,"%eval%":eval,"%EvalError%":i,"%Float32Array%":"undefined"==typeof Float32Array?o:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?o:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?o:FinalizationRegistry,"%Function%":h,"%GeneratorFunction%":g,"%Int8Array%":"undefined"==typeof Int8Array?o:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?o:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?o:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":y&&P?P(P([][Symbol.iterator]())):o,"%JSON%":"object"==typeof JSON?JSON:o,"%Map%":"undefined"==typeof Map?o:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&y&&P?P(new Map()[Symbol.iterator]()):o,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?o:Promise,"%Proxy%":"undefined"==typeof Proxy?o:Proxy,"%RangeError%":a,"%ReferenceError%":s,"%Reflect%":"undefined"==typeof Reflect?o:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?o:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&y&&P?P(new Set()[Symbol.iterator]()):o,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?o:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":y&&P?P(""[Symbol.iterator]()):o,"%Symbol%":y?Symbol:o,"%SyntaxError%":l,"%ThrowTypeError%":f,"%TypedArray%":T,"%TypeError%":u,"%Uint8Array%":"undefined"==typeof Uint8Array?o:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?o:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?o:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?o:Uint32Array,"%URIError%":c,"%WeakMap%":"undefined"==typeof WeakMap?o:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?o:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?o:WeakSet};if(P)try{null.error}catch(e){var E=P(P(e));_["%Error.prototype%"]=E}var b=function e(t){var r;if("%AsyncFunction%"===t)r=d("async function () {}");else if("%GeneratorFunction%"===t)r=d("function* () {}");else if("%AsyncGeneratorFunction%"===t)r=d("async function* () {}");else if("%AsyncGenerator%"===t){var o=e("%AsyncGeneratorFunction%");o&&(r=o.prototype)}else if("%AsyncIteratorPrototype%"===t){var n=e("%AsyncGenerator%");n&&P&&(r=P(n.prototype))}return _[t]=r,r},S={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},O=r(5705),x=r(6376),w=O.call(Function.call,Array.prototype.concat),A=O.call(Function.apply,Array.prototype.splice),G=O.call(Function.call,String.prototype.replace),R=O.call(Function.call,String.prototype.slice),C=O.call(Function.call,RegExp.prototype.exec),I=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,k=/\\(\\)?/g,j=function(e){var t=R(e,0,1),r=R(e,-1);if("%"===t&&"%"!==r)throw new l("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==t)throw new l("invalid intrinsic syntax, expected opening `%`");var o=[];return G(e,I,function(e,t,r,n){o[o.length]=r?G(n,k,"$1"):t||e}),o},D=function(e,t){var r,o=e;if(x(S,o)&&(o="%"+(r=S[o])[0]+"%"),x(_,o)){var n=_[o];if(n===g&&(n=b(o)),void 0===n&&!t)throw new u("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:r,name:o,value:n}}throw new l("intrinsic "+e+" does not exist!")};e.exports=function(e,t){if("string"!=typeof e||0===e.length)throw new u("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new u('"allowMissing" argument must be a boolean');if(null===C(/^%?[^%]*%?$/,e))throw new l("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=j(e),o=r.length>0?r[0]:"",n=D("%"+o+"%",t),i=n.name,a=n.value,s=!1,c=n.alias;c&&(o=c[0],A(r,w([0,1],c)));for(var h=1,d=!0;h<r.length;h+=1){var m=r[h],f=R(m,0,1),y=R(m,-1);if(('"'===f||"'"===f||"`"===f||'"'===y||"'"===y||"`"===y)&&f!==y)throw new l("property names with quotes must have matching quotes");if("constructor"!==m&&d||(s=!0),o+="."+m,x(_,i="%"+o+"%"))a=_[i];else if(null!=a){if(!(m in a)){if(!t)throw new u("base intrinsic for "+e+" exists, but the property is not available.");return}if(p&&h+1>=r.length){var v=p(a,m);a=(d=!!v)&&"get"in v&&!("originalValue"in v.get)?v.get:a[m]}else d=x(a,m),a=a[m];d&&!s&&(_[i]=a)}}return a}},8362:e=>{"use strict";e.exports=Object.getOwnPropertyDescriptor},5489:(e,t,r)=>{"use strict";var o=r(8362);if(o)try{o([],"length")}catch(e){o=null}e.exports=o},5376:(e,t,r)=>{"use strict";var o=r(7747),n=function(){return!!o};n.hasArrayLengthDefineBug=function(){if(!o)return null;try{return 1!==o([],"length",{value:1}).length}catch(e){return!0}},e.exports=n},8860:e=>{"use strict";var t={__proto__:null,foo:{}},r={__proto__:t}.foo===t.foo&&!(t instanceof Object);e.exports=function(){return r}},8126:(e,t,r)=>{"use strict";var o="undefined"!=typeof Symbol&&Symbol,n=r(9128);e.exports=function(){return"function"==typeof o&&"function"==typeof Symbol&&"symbol"==typeof o("foo")&&"symbol"==typeof Symbol("bar")&&n()}},9128:e=>{"use strict";e.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),r=Object(t);if("string"==typeof t||"[object Symbol]"!==Object.prototype.toString.call(t)||"[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(var o in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length||"function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var n=Object.getOwnPropertySymbols(e);if(1!==n.length||n[0]!==t||!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var i=Object.getOwnPropertyDescriptor(e,t);if(42!==i.value||!0!==i.enumerable)return!1}return!0}},6376:(e,t,r)=>{"use strict";var o=Function.prototype.call,n=Object.prototype.hasOwnProperty,i=r(5705);e.exports=i.call(o,n)},9467:(e,t,r)=>{var o="function"==typeof Map&&Map.prototype,n=Object.getOwnPropertyDescriptor&&o?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,i=o&&n&&"function"==typeof n.get?n.get:null,a=o&&Map.prototype.forEach,s="function"==typeof Set&&Set.prototype,l=Object.getOwnPropertyDescriptor&&s?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,u=s&&l&&"function"==typeof l.get?l.get:null,c=s&&Set.prototype.forEach,h="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,d="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,p="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,m=Boolean.prototype.valueOf,f=Object.prototype.toString,y=Function.prototype.toString,v=String.prototype.match,P=String.prototype.slice,g=String.prototype.replace,T=String.prototype.toUpperCase,_=String.prototype.toLowerCase,E=RegExp.prototype.test,b=Array.prototype.concat,S=Array.prototype.join,O=Array.prototype.slice,x=Math.floor,w="function"==typeof BigInt?BigInt.prototype.valueOf:null,A=Object.getOwnPropertySymbols,G="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,R="function"==typeof Symbol&&"object"==typeof Symbol.iterator,C="function"==typeof Symbol&&Symbol.toStringTag&&(typeof Symbol.toStringTag===R?"object":"symbol")?Symbol.toStringTag:null,I=Object.prototype.propertyIsEnumerable,k=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function j(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||E.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var o=e<0?-x(-e):x(e);if(o!==e){var n=String(o),i=P.call(t,n.length+1);return g.call(n,r,"$&_")+"."+g.call(g.call(i,/([0-9]{3})/g,"$&_"),/_$/,"")}}return g.call(t,r,"$&_")}var D=r(4654),N=D.custom,F=$(N)?N:null,M={__proto__:null,double:'"',single:"'"},U={__proto__:null,double:/(["\\])/g,single:/(['\\])/g};function q(e,t,r){var o=M[r.quoteStyle||t];return o+e+o}function L(e){return"[object Array]"===W(e)&&(!C||!("object"==typeof e&&C in e))}function H(e){return"[object RegExp]"===W(e)&&(!C||!("object"==typeof e&&C in e))}function $(e){if(R)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!G)return!1;try{return G.call(e),!0}catch(e){}return!1}e.exports=function e(t,o,n,s){var l=o||{};if(B(l,"quoteStyle")&&!B(M,l.quoteStyle))throw TypeError('option "quoteStyle" must be "single" or "double"');if(B(l,"maxStringLength")&&("number"==typeof l.maxStringLength?l.maxStringLength<0&&l.maxStringLength!==1/0:null!==l.maxStringLength))throw TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var f=!B(l,"customInspect")||l.customInspect;if("boolean"!=typeof f&&"symbol"!==f)throw TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(B(l,"indent")&&null!==l.indent&&"	"!==l.indent&&!(parseInt(l.indent,10)===l.indent&&l.indent>0))throw TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(B(l,"numericSeparator")&&"boolean"!=typeof l.numericSeparator)throw TypeError('option "numericSeparator", if provided, must be `true` or `false`');var T=l.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return function e(t,r){if(t.length>r.maxStringLength){var o=t.length-r.maxStringLength;return e(P.call(t,0,r.maxStringLength),r)+"... "+o+" more character"+(o>1?"s":"")}var n=U[r.quoteStyle||"single"];return n.lastIndex=0,q(g.call(g.call(t,n,"\\$1"),/[\x00-\x1f]/g,V),"single",r)}(t,l);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var E=String(t);return T?j(t,E):E}if("bigint"==typeof t){var x=String(t)+"n";return T?j(t,x):x}var A=void 0===l.depth?5:l.depth;if(void 0===n&&(n=0),n>=A&&A>0&&"object"==typeof t)return L(t)?"[Array]":"[Object]";var N=function(e,t){var r;if("	"===e.indent)r="	";else{if("number"!=typeof e.indent||!(e.indent>0))return null;r=S.call(Array(e.indent+1)," ")}return{base:r,prev:S.call(Array(t+1),r)}}(l,n);if(void 0===s)s=[];else if(K(s,t)>=0)return"[Circular]";function z(t,r,o){if(r&&(s=O.call(s)).push(r),o){var i={depth:l.depth};return B(l,"quoteStyle")&&(i.quoteStyle=l.quoteStyle),e(t,i,n+1,s)}return e(t,l,n+1,s)}if("function"==typeof t&&!H(t)){var ee=function(e){if(e.name)return e.name;var t=v.call(y.call(e),/^function\s*([\w$]+)/);return t?t[1]:null}(t),et=Z(t,z);return"[Function"+(ee?": "+ee:" (anonymous)")+"]"+(et.length>0?" { "+S.call(et,", ")+" }":"")}if($(t)){var er=R?g.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):G.call(t);return"object"!=typeof t||R?er:J(er)}if(t&&"object"==typeof t&&("undefined"!=typeof HTMLElement&&t instanceof HTMLElement||"string"==typeof t.nodeName&&"function"==typeof t.getAttribute)){for(var eo,en="<"+_.call(String(t.nodeName)),ei=t.attributes||[],ea=0;ea<ei.length;ea++)en+=" "+ei[ea].name+"="+q((eo=ei[ea].value,g.call(String(eo),/"/g,"&quot;")),"double",l);return en+=">",t.childNodes&&t.childNodes.length&&(en+="..."),en+="</"+_.call(String(t.nodeName))+">"}if(L(t)){if(0===t.length)return"[]";var es=Z(t,z);return N&&!function(e){for(var t=0;t<e.length;t++)if(K(e[t],"\n")>=0)return!1;return!0}(es)?"["+Y(es,N)+"]":"[ "+S.call(es,", ")+" ]"}if("[object Error]"===W(t)&&(!C||!("object"==typeof t&&C in t))){var el=Z(t,z);return"cause"in Error.prototype||!("cause"in t)||I.call(t,"cause")?0===el.length?"["+String(t)+"]":"{ ["+String(t)+"] "+S.call(el,", ")+" }":"{ ["+String(t)+"] "+S.call(b.call("[cause]: "+z(t.cause),el),", ")+" }"}if("object"==typeof t&&f){if(F&&"function"==typeof t[F]&&D)return D(t,{depth:A-n});if("symbol"!==f&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!i||!e||"object"!=typeof e)return!1;try{i.call(e);try{u.call(e)}catch(e){return!0}return e instanceof Map}catch(e){}return!1}(t)){var eu=[];return a&&a.call(t,function(e,r){eu.push(z(r,t,!0)+" => "+z(e,t))}),X("Map",i.call(t),eu,N)}if(function(e){if(!u||!e||"object"!=typeof e)return!1;try{u.call(e);try{i.call(e)}catch(e){return!0}return e instanceof Set}catch(e){}return!1}(t)){var ec=[];return c&&c.call(t,function(e){ec.push(z(e,t))}),X("Set",u.call(t),ec,N)}if(function(e){if(!h||!e||"object"!=typeof e)return!1;try{h.call(e,h);try{d.call(e,d)}catch(e){return!0}return e instanceof WeakMap}catch(e){}return!1}(t))return Q("WeakMap");if(function(e){if(!d||!e||"object"!=typeof e)return!1;try{d.call(e,d);try{h.call(e,h)}catch(e){return!0}return e instanceof WeakSet}catch(e){}return!1}(t))return Q("WeakSet");if(function(e){if(!p||!e||"object"!=typeof e)return!1;try{return p.call(e),!0}catch(e){}return!1}(t))return Q("WeakRef");if("[object Number]"===W(t)&&(!C||!("object"==typeof t&&C in t)))return J(z(Number(t)));if(function(e){if(!e||"object"!=typeof e||!w)return!1;try{return w.call(e),!0}catch(e){}return!1}(t))return J(z(w.call(t)));if("[object Boolean]"===W(t)&&(!C||!("object"==typeof t&&C in t)))return J(m.call(t));if("[object String]"===W(t)&&(!C||!("object"==typeof t&&C in t)))return J(z(String(t)));if("undefined"!=typeof window&&t===window)return"{ [object Window] }";if("undefined"!=typeof globalThis&&t===globalThis||void 0!==r.g&&t===r.g)return"{ [object globalThis] }";if(!("[object Date]"===W(t)&&(!C||!("object"==typeof t&&C in t)))&&!H(t)){var eh=Z(t,z),ed=k?k(t)===Object.prototype:t instanceof Object||t.constructor===Object,ep=t instanceof Object?"":"null prototype",em=!ed&&C&&Object(t)===t&&C in t?P.call(W(t),8,-1):ep?"Object":"",ef=(ed||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(em||ep?"["+S.call(b.call([],em||[],ep||[]),": ")+"] ":"");return 0===eh.length?ef+"{}":N?ef+"{"+Y(eh,N)+"}":ef+"{ "+S.call(eh,", ")+" }"}return String(t)};var z=Object.prototype.hasOwnProperty||function(e){return e in this};function B(e,t){return z.call(e,t)}function W(e){return f.call(e)}function K(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,o=e.length;r<o;r++)if(e[r]===t)return r;return -1}function V(e){var t=e.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return r?"\\"+r:"\\x"+(t<16?"0":"")+T.call(t.toString(16))}function J(e){return"Object("+e+")"}function Q(e){return e+" { ? }"}function X(e,t,r,o){return e+" ("+t+") {"+(o?Y(r,o):S.call(r,", "))+"}"}function Y(e,t){if(0===e.length)return"";var r="\n"+t.prev+t.base;return r+S.call(e,","+r)+"\n"+t.prev}function Z(e,t){var r,o=L(e),n=[];if(o){n.length=e.length;for(var i=0;i<e.length;i++)n[i]=B(e,i)?t(e[i],e):""}var a="function"==typeof A?A(e):[];if(R){r={};for(var s=0;s<a.length;s++)r["$"+a[s]]=a[s]}for(var l in e)B(e,l)&&(!o||String(Number(l))!==l||!(l<e.length))&&(R&&r["$"+l]instanceof Symbol||(E.call(/[^\w$]/,l)?n.push(t(l,e)+": "+t(e[l],e)):n.push(l+": "+t(e[l],e))));if("function"==typeof A)for(var u=0;u<a.length;u++)I.call(e,a[u])&&n.push("["+t(a[u])+"]: "+t(e[a[u]],e));return n}},8618:e=>{"use strict";var t=String.prototype.replace,r=/%20/g,o={RFC1738:"RFC1738",RFC3986:"RFC3986"};e.exports={default:o.RFC3986,formatters:{RFC1738:function(e){return t.call(e,r,"+")},RFC3986:function(e){return String(e)}},RFC1738:o.RFC1738,RFC3986:o.RFC3986}},2874:(e,t,r)=>{"use strict";var o=r(4459),n=r(5395),i=r(8618);e.exports={formats:i,parse:n,stringify:o}},5395:(e,t,r)=>{"use strict";var o=r(5579),n=Object.prototype.hasOwnProperty,i=Array.isArray,a={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:o.decode,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictDepth:!1,strictNullHandling:!1},s=function(e,t){return e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1?e.split(","):e},l=function(e,t){var r={__proto__:null},l=t.ignoreQueryPrefix?e.replace(/^\?/,""):e;l=l.replace(/%5B/gi,"[").replace(/%5D/gi,"]");var u=t.parameterLimit===1/0?void 0:t.parameterLimit,c=l.split(t.delimiter,u),h=-1,d=t.charset;if(t.charsetSentinel)for(p=0;p<c.length;++p)0===c[p].indexOf("utf8=")&&("utf8=%E2%9C%93"===c[p]?d="utf-8":"utf8=%26%2310003%3B"===c[p]&&(d="iso-8859-1"),h=p,p=c.length);for(p=0;p<c.length;++p)if(p!==h){var p,m,f,y=c[p],v=y.indexOf("]="),P=-1===v?y.indexOf("="):v+1;-1===P?(m=t.decoder(y,a.decoder,d,"key"),f=t.strictNullHandling?null:""):(m=t.decoder(y.slice(0,P),a.decoder,d,"key"),f=o.maybeMap(s(y.slice(P+1),t),function(e){return t.decoder(e,a.decoder,d,"value")})),f&&t.interpretNumericEntities&&"iso-8859-1"===d&&(f=String(f).replace(/&#(\d+);/g,function(e,t){return String.fromCharCode(parseInt(t,10))})),y.indexOf("[]=")>-1&&(f=i(f)?[f]:f);var g=n.call(r,m);g&&"combine"===t.duplicates?r[m]=o.combine(r[m],f):g&&"last"!==t.duplicates||(r[m]=f)}return r},u=function(e,t,r,o){for(var n=o?t:s(t,r),i=e.length-1;i>=0;--i){var a,l=e[i];if("[]"===l&&r.parseArrays)a=r.allowEmptyArrays&&(""===n||r.strictNullHandling&&null===n)?[]:[].concat(n);else{a=r.plainObjects?{__proto__:null}:{};var u="["===l.charAt(0)&&"]"===l.charAt(l.length-1)?l.slice(1,-1):l,c=r.decodeDotInKeys?u.replace(/%2E/g,"."):u,h=parseInt(c,10);r.parseArrays||""!==c?!isNaN(h)&&l!==c&&String(h)===c&&h>=0&&r.parseArrays&&h<=r.arrayLimit?(a=[])[h]=n:"__proto__"!==c&&(a[c]=n):a={0:n}}n=a}return n},c=function(e,t,r,o){if(e){var i=r.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,a=/(\[[^[\]]*])/g,s=r.depth>0&&/(\[[^[\]]*])/.exec(i),l=s?i.slice(0,s.index):i,c=[];if(l){if(!r.plainObjects&&n.call(Object.prototype,l)&&!r.allowPrototypes)return;c.push(l)}for(var h=0;r.depth>0&&null!==(s=a.exec(i))&&h<r.depth;){if(h+=1,!r.plainObjects&&n.call(Object.prototype,s[1].slice(1,-1))&&!r.allowPrototypes)return;c.push(s[1])}if(s){if(!0===r.strictDepth)throw RangeError("Input depth exceeded depth option of "+r.depth+" and strictDepth is true");c.push("["+i.slice(s.index)+"]")}return u(c,t,r,o)}},h=function(e){if(!e)return a;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.decodeDotInKeys&&"boolean"!=typeof e.decodeDotInKeys)throw TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var t=void 0===e.charset?a.charset:e.charset,r=void 0===e.duplicates?a.duplicates:e.duplicates;if("combine"!==r&&"first"!==r&&"last"!==r)throw TypeError("The duplicates option must be either combine, first, or last");return{allowDots:void 0===e.allowDots?!0===e.decodeDotInKeys||a.allowDots:!!e.allowDots,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:a.allowEmptyArrays,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:a.allowPrototypes,allowSparse:"boolean"==typeof e.allowSparse?e.allowSparse:a.allowSparse,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:a.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:a.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:a.comma,decodeDotInKeys:"boolean"==typeof e.decodeDotInKeys?e.decodeDotInKeys:a.decodeDotInKeys,decoder:"function"==typeof e.decoder?e.decoder:a.decoder,delimiter:"string"==typeof e.delimiter||o.isRegExp(e.delimiter)?e.delimiter:a.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:a.depth,duplicates:r,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:a.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:a.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:a.plainObjects,strictDepth:"boolean"==typeof e.strictDepth?!!e.strictDepth:a.strictDepth,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:a.strictNullHandling}};e.exports=function(e,t){var r=h(t);if(""===e||null==e)return r.plainObjects?{__proto__:null}:{};for(var n="string"==typeof e?l(e,r):e,i=r.plainObjects?{__proto__:null}:{},a=Object.keys(n),s=0;s<a.length;++s){var u=a[s],d=c(u,n[u],r,"string"==typeof e);i=o.merge(i,d,r)}return!0===r.allowSparse?i:o.compact(i)}},4459:(e,t,r)=>{"use strict";var o=r(9402),n=r(5579),i=r(8618),a=Object.prototype.hasOwnProperty,s={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},l=Array.isArray,u=Array.prototype.push,c=function(e,t){u.apply(e,l(t)?t:[t])},h=Date.prototype.toISOString,d=i.default,p={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,commaRoundTrip:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:n.encode,encodeValuesOnly:!1,filter:void 0,format:d,formatter:i.formatters[d],indices:!1,serializeDate:function(e){return h.call(e)},skipNulls:!1,strictNullHandling:!1},m={},f=function e(t,r,i,a,s,u,h,d,f,y,v,P,g,T,_,E,b,S){for(var O,x,w=t,A=S,G=0,R=!1;void 0!==(A=A.get(m))&&!R;){var C=A.get(t);if(G+=1,void 0!==C){if(C===G)throw RangeError("Cyclic object value");R=!0}void 0===A.get(m)&&(G=0)}if("function"==typeof y?w=y(r,w):w instanceof Date?w=g(w):"comma"===i&&l(w)&&(w=n.maybeMap(w,function(e){return e instanceof Date?g(e):e})),null===w){if(u)return f&&!E?f(r,p.encoder,b,"key",T):r;w=""}if("string"==typeof(O=w)||"number"==typeof O||"boolean"==typeof O||"symbol"==typeof O||"bigint"==typeof O||n.isBuffer(w))return f?[_(E?r:f(r,p.encoder,b,"key",T))+"="+_(f(w,p.encoder,b,"value",T))]:[_(r)+"="+_(String(w))];var I=[];if(void 0===w)return I;if("comma"===i&&l(w))E&&f&&(w=n.maybeMap(w,f)),x=[{value:w.length>0?w.join(",")||null:void 0}];else if(l(y))x=y;else{var k=Object.keys(w);x=v?k.sort(v):k}var j=d?String(r).replace(/\./g,"%2E"):String(r),D=a&&l(w)&&1===w.length?j+"[]":j;if(s&&l(w)&&0===w.length)return D+"[]";for(var N=0;N<x.length;++N){var F=x[N],M="object"==typeof F&&F&&void 0!==F.value?F.value:w[F];if(!h||null!==M){var U=P&&d?String(F).replace(/\./g,"%2E"):String(F),q=l(w)?"function"==typeof i?i(D,U):D:D+(P?"."+U:"["+U+"]");S.set(t,G);var L=o();L.set(m,S),c(I,e(M,q,i,a,s,u,h,d,"comma"===i&&E&&l(w)?null:f,y,v,P,g,T,_,E,b,L))}}return I},y=function(e){if(!e)return p;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");var t,r=e.charset||p.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var o=i.default;if(void 0!==e.format){if(!a.call(i.formatters,e.format))throw TypeError("Unknown format option provided.");o=e.format}var n=i.formatters[o],u=p.filter;if(("function"==typeof e.filter||l(e.filter))&&(u=e.filter),t=e.arrayFormat in s?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":p.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");var c=void 0===e.allowDots?!0===e.encodeDotInKeys||p.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:p.addQueryPrefix,allowDots:c,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:p.allowEmptyArrays,arrayFormat:t,charset:r,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:p.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?p.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:p.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:p.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:p.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:p.encodeValuesOnly,filter:u,format:o,formatter:n,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:p.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:p.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:p.strictNullHandling}};e.exports=function(e,t){var r,n=e,i=y(t);"function"==typeof i.filter?n=(0,i.filter)("",n):l(i.filter)&&(r=i.filter);var a=[];if("object"!=typeof n||null===n)return"";var u=s[i.arrayFormat],h="comma"===u&&i.commaRoundTrip;r||(r=Object.keys(n)),i.sort&&r.sort(i.sort);for(var d=o(),p=0;p<r.length;++p){var m=r[p],v=n[m];i.skipNulls&&null===v||c(a,f(v,m,u,h,i.allowEmptyArrays,i.strictNullHandling,i.skipNulls,i.encodeDotInKeys,i.encode?i.encoder:null,i.filter,i.sort,i.allowDots,i.serializeDate,i.format,i.formatter,i.encodeValuesOnly,i.charset,d))}var P=a.join(i.delimiter),g=!0===i.addQueryPrefix?"?":"";return i.charsetSentinel&&("iso-8859-1"===i.charset?g+="utf8=%26%2310003%3B&":g+="utf8=%E2%9C%93&"),P.length>0?g+P:""}},5579:(e,t,r)=>{"use strict";var o=r(8618),n=Object.prototype.hasOwnProperty,i=Array.isArray,a=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),s=function(e){for(;e.length>1;){var t=e.pop(),r=t.obj[t.prop];if(i(r)){for(var o=[],n=0;n<r.length;++n)void 0!==r[n]&&o.push(r[n]);t.obj[t.prop]=o}}},l=function(e,t){for(var r=t&&t.plainObjects?{__proto__:null}:{},o=0;o<e.length;++o)void 0!==e[o]&&(r[o]=e[o]);return r};e.exports={arrayToObject:l,assign:function(e,t){return Object.keys(t).reduce(function(e,r){return e[r]=t[r],e},e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],r=[],o=0;o<t.length;++o)for(var n=t[o],i=n.obj[n.prop],a=Object.keys(i),l=0;l<a.length;++l){var u=a[l],c=i[u];"object"==typeof c&&null!==c&&-1===r.indexOf(c)&&(t.push({obj:i,prop:u}),r.push(c))}return s(t),e},decode:function(e,t,r){var o=e.replace(/\+/g," ");if("iso-8859-1"===r)return o.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(o)}catch(e){return o}},encode:function(e,t,r,n,i){if(0===e.length)return e;var s=e;if("symbol"==typeof e?s=Symbol.prototype.toString.call(e):"string"!=typeof e&&(s=String(e)),"iso-8859-1"===r)return escape(s).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});for(var l="",u=0;u<s.length;u+=1024){for(var c=s.length>=1024?s.slice(u,u+1024):s,h=[],d=0;d<c.length;++d){var p=c.charCodeAt(d);if(45===p||46===p||95===p||126===p||p>=48&&p<=57||p>=65&&p<=90||p>=97&&p<=122||i===o.RFC1738&&(40===p||41===p)){h[h.length]=c.charAt(d);continue}if(p<128){h[h.length]=a[p];continue}if(p<2048){h[h.length]=a[192|p>>6]+a[128|63&p];continue}if(p<55296||p>=57344){h[h.length]=a[224|p>>12]+a[128|p>>6&63]+a[128|63&p];continue}d+=1,p=65536+((1023&p)<<10|1023&c.charCodeAt(d)),h[h.length]=a[240|p>>18]+a[128|p>>12&63]+a[128|p>>6&63]+a[128|63&p]}l+=h.join("")}return l},isBuffer:function(e){return!!e&&"object"==typeof e&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(i(e)){for(var r=[],o=0;o<e.length;o+=1)r.push(t(e[o]));return r}return t(e)},merge:function e(t,r,o){if(!r)return t;if("object"!=typeof r&&"function"!=typeof r){if(i(t))t.push(r);else{if(!t||"object"!=typeof t)return[t,r];(o&&(o.plainObjects||o.allowPrototypes)||!n.call(Object.prototype,r))&&(t[r]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(r);var a=t;return(i(t)&&!i(r)&&(a=l(t,o)),i(t)&&i(r))?(r.forEach(function(r,i){if(n.call(t,i)){var a=t[i];a&&"object"==typeof a&&r&&"object"==typeof r?t[i]=e(a,r,o):t.push(r)}else t[i]=r}),t):Object.keys(r).reduce(function(t,i){var a=r[i];return n.call(t,i)?t[i]=e(t[i],a,o):t[i]=a,t},a)}}},7477:(e,t,r)=>{"use strict";var o=r(3424),n=r(510),i=r(5376)(),a=r(5489),s=r(1761),l=o("%Math.floor%");e.exports=function(e,t){if("function"!=typeof e)throw new s("`fn` is not a function");if("number"!=typeof t||t<0||t>4294967295||l(t)!==t)throw new s("`length` must be a positive 32-bit integer");var r=arguments.length>2&&!!arguments[2],o=!0,u=!0;if("length"in e&&a){var c=a(e,"length");c&&!c.configurable&&(o=!1),c&&!c.writable&&(u=!1)}return(o||u||!r)&&(i?n(e,"length",t,!0,!0):n(e,"length",t)),e}},9402:(e,t,r)=>{"use strict";var o=r(3424),n=r(7324),i=r(9467),a=r(1761),s=o("%WeakMap%",!0),l=o("%Map%",!0),u=n("WeakMap.prototype.get",!0),c=n("WeakMap.prototype.set",!0),h=n("WeakMap.prototype.has",!0),d=n("Map.prototype.get",!0),p=n("Map.prototype.set",!0),m=n("Map.prototype.has",!0),f=function(e,t){for(var r,o=e;null!==(r=o.next);o=r)if(r.key===t)return o.next=r.next,r.next=e.next,e.next=r,r},y=function(e,t){var r=f(e,t);return r&&r.value},v=function(e,t,r){var o=f(e,t);o?o.value=r:e.next={key:t,next:e.next,value:r}};e.exports=function(){var e,t,r,o={assert:function(e){if(!o.has(e))throw new a("Side channel does not contain "+i(e))},get:function(o){if(s&&o&&("object"==typeof o||"function"==typeof o)){if(e)return u(e,o)}else if(l){if(t)return d(t,o)}else if(r)return y(r,o)},has:function(o){if(s&&o&&("object"==typeof o||"function"==typeof o)){if(e)return h(e,o)}else if(l){if(t)return m(t,o)}else if(r)return!!f(r,o);return!1},set:function(o,n){s&&o&&("object"==typeof o||"function"==typeof o)?(e||(e=new s),c(e,o,n)):l?(t||(t=new l),p(t,o,n)):(r||(r={key:{},next:null}),v(r,o,n))}};return o}},1735:(e,t,r)=>{"use strict";r.d(t,{Z:()=>ne});var o={};r.r(o),r.d(o,{StripeAPIError:()=>_,StripeAuthenticationError:()=>E,StripeCardError:()=>g,StripeConnectionError:()=>O,StripeError:()=>P,StripeIdempotencyError:()=>w,StripeInvalidGrantError:()=>A,StripeInvalidRequestError:()=>T,StripePermissionError:()=>b,StripeRateLimitError:()=>S,StripeSignatureVerificationError:()=>x,StripeUnknownError:()=>G,generate:()=>v});var n={};r.r(n),r.d(n,{Account:()=>re,AccountLinks:()=>rr,AccountSessions:()=>rn,Accounts:()=>re,ApplePayDomains:()=>ra,ApplicationFees:()=>rl,Apps:()=>oL,Balance:()=>rc,BalanceTransactions:()=>rd,Billing:()=>oH,BillingPortal:()=>o$,Charges:()=>rm,Checkout:()=>oz,Climate:()=>oB,ConfirmationTokens:()=>ry,CountrySpecs:()=>rP,Coupons:()=>rT,CreditNotes:()=>rE,CustomerSessions:()=>rS,Customers:()=>rx,Disputes:()=>rA,Entitlements:()=>oW,EphemeralKeys:()=>rR,Events:()=>rI,ExchangeRates:()=>rj,FileLinks:()=>rN,Files:()=>rU,FinancialConnections:()=>oK,Forwarding:()=>oV,Identity:()=>oJ,InvoiceItems:()=>rL,InvoiceRenderingTemplates:()=>r$,Invoices:()=>rB,Issuing:()=>oQ,Mandates:()=>rK,OAuth:()=>rQ,PaymentIntents:()=>rY,PaymentLinks:()=>r1,PaymentMethodConfigurations:()=>r8,PaymentMethodDomains:()=>r5,PaymentMethods:()=>r6,Payouts:()=>r7,Plans:()=>oe,Prices:()=>or,Products:()=>on,PromotionCodes:()=>oa,Quotes:()=>ol,Radar:()=>oX,Refunds:()=>oc,Reporting:()=>oY,Reviews:()=>od,SetupAttempts:()=>om,SetupIntents:()=>oy,ShippingRates:()=>oP,Sigma:()=>oZ,Sources:()=>oT,SubscriptionItems:()=>oE,SubscriptionSchedules:()=>oS,Subscriptions:()=>ox,Tax:()=>o1,TaxCodes:()=>oA,TaxIds:()=>oR,TaxRates:()=>oI,Terminal:()=>o0,TestHelpers:()=>o8,Tokens:()=>oj,Topups:()=>oN,Transfers:()=>oM,Treasury:()=>o2,WebhookEndpoints:()=>oq});class i{getClientName(){throw Error("getClientName not implemented.")}makeRequest(e,t,r,o,n,i,a,s){throw Error("makeRequest not implemented.")}static makeTimeoutError(){let e=TypeError(i.TIMEOUT_ERROR_CODE);return e.code=i.TIMEOUT_ERROR_CODE,e}}i.CONNECTION_CLOSED_ERROR_CODES=["ECONNRESET","EPIPE"],i.TIMEOUT_ERROR_CODE="ETIMEDOUT";class a{constructor(e,t){this._statusCode=e,this._headers=t}getStatusCode(){return this._statusCode}getHeaders(){return this._headers}getRawResponse(){throw Error("getRawResponse not implemented.")}toStream(e){throw Error("toStream not implemented.")}toJSON(){throw Error("toJSON not implemented.")}}class s extends i{constructor(e){if(super(),!e){if(!globalThis.fetch)throw Error("fetch() function not provided and is not defined in the global scope. You must provide a fetch implementation.");e=globalThis.fetch}globalThis.AbortController?this._fetchFn=s.makeFetchWithAbortTimeout(e):this._fetchFn=s.makeFetchWithRaceTimeout(e)}static makeFetchWithRaceTimeout(e){return(t,r,o)=>{let n;let a=new Promise((e,t)=>{n=setTimeout(()=>{n=null,t(i.makeTimeoutError())},o)});return Promise.race([e(t,r),a]).finally(()=>{n&&clearTimeout(n)})}}static makeFetchWithAbortTimeout(e){return async(t,r,o)=>{let n=new AbortController,a=setTimeout(()=>{a=null,n.abort(i.makeTimeoutError())},o);try{return await e(t,Object.assign(Object.assign({},r),{signal:n.signal}))}catch(e){if("AbortError"===e.name)throw i.makeTimeoutError();throw e}finally{a&&clearTimeout(a)}}}getClientName(){return"fetch"}async makeRequest(e,t,r,o,n,i,a,s){let u=new URL(r,`${"http"===a?"http":"https"}://${e}`);u.port=t;let c="POST"==o||"PUT"==o||"PATCH"==o;return new l(await this._fetchFn(u.toString(),{method:o,headers:n,body:i||(c?"":void 0)},s))}}class l extends a{constructor(e){super(e.status,l._transformHeadersToObject(e.headers)),this._res=e}getRawResponse(){return this._res}toStream(e){return e(),this._res.body}toJSON(){return this._res.json()}static _transformHeadersToObject(e){let t={};for(let r of e){if(!Array.isArray(r)||2!=r.length)throw Error("Response objects produced by the fetch function given to FetchHttpClient do not have an iterable headers map. Response#headers should be an iterable object.");t[r[0]]=r[1]}return t}}class u{computeHMACSignature(e,t){throw Error("computeHMACSignature not implemented.")}computeHMACSignatureAsync(e,t){throw Error("computeHMACSignatureAsync not implemented.")}}class c extends Error{}class h extends u{constructor(e){super(),this.subtleCrypto=e||crypto.subtle}computeHMACSignature(e,t){throw new c("SubtleCryptoProvider cannot be used in a synchronous context.")}async computeHMACSignatureAsync(e,t){let r=new TextEncoder,o=await this.subtleCrypto.importKey("raw",r.encode(t),{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]),n=new Uint8Array(await this.subtleCrypto.sign("hmac",o,r.encode(e))),i=Array(n.length);for(let e=0;e<n.length;e++)i[e]=d[n[e]];return i.join("")}}let d=Array(256);for(let e=0;e<d.length;e++)d[e]=e.toString(16).padStart(2,"0");class p{constructor(){this._fetchFn=null,this._agent=null}getUname(){throw Error("getUname not implemented.")}uuid4(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}secureCompare(e,t){if(e.length!==t.length)return!1;let r=e.length,o=0;for(let n=0;n<r;++n)o|=e.charCodeAt(n)^t.charCodeAt(n);return 0===o}createEmitter(){throw Error("createEmitter not implemented.")}tryBufferData(e){throw Error("tryBufferData not implemented.")}createNodeHttpClient(e){throw Error("createNodeHttpClient not implemented.")}createFetchHttpClient(e){return new s(e)}createDefaultHttpClient(){throw Error("createDefaultHttpClient not implemented.")}createNodeCryptoProvider(){throw Error("createNodeCryptoProvider not implemented.")}createSubtleCryptoProvider(e){return new h(e)}createDefaultCryptoProvider(){throw Error("createDefaultCryptoProvider not implemented.")}}class m extends Event{constructor(e,t){super(e),this.data=t}}class f{constructor(){this.eventTarget=new EventTarget,this.listenerMapping=new Map}on(e,t){let r=e=>{t(e.data)};return this.listenerMapping.set(t,r),this.eventTarget.addEventListener(e,r)}removeListener(e,t){let r=this.listenerMapping.get(t);return this.listenerMapping.delete(t),this.eventTarget.removeEventListener(e,r)}once(e,t){let r=e=>{t(e.data)};return this.listenerMapping.set(t,r),this.eventTarget.addEventListener(e,r,{once:!0})}emit(e,t){return this.eventTarget.dispatchEvent(new m(e,t))}}class y extends p{getUname(){return Promise.resolve(null)}createEmitter(){return new f}tryBufferData(e){if(e.file.data instanceof ReadableStream)throw Error("Uploading a file as a stream is not supported in non-Node environments. Please open or upvote an issue at github.com/stripe/stripe-node if you use this, detailing your use-case.");return Promise.resolve(e)}createNodeHttpClient(){throw Error("Stripe: `createNodeHttpClient()` is not available in non-Node environments. Please use `createFetchHttpClient()` instead.")}createDefaultHttpClient(){return super.createFetchHttpClient()}createNodeCryptoProvider(){throw Error("Stripe: `createNodeCryptoProvider()` is not available in non-Node environments. Please use `createSubtleCryptoProvider()` instead.")}createDefaultCryptoProvider(){return this.createSubtleCryptoProvider()}}let v=e=>{switch(e.type){case"card_error":return new g(e);case"invalid_request_error":return new T(e);case"api_error":return new _(e);case"authentication_error":return new E(e);case"rate_limit_error":return new S(e);case"idempotency_error":return new w(e);case"invalid_grant":return new A(e);default:return new G(e)}};class P extends Error{constructor(e={},t=null){super(e.message),this.type=t||this.constructor.name,this.raw=e,this.rawType=e.type,this.code=e.code,this.doc_url=e.doc_url,this.param=e.param,this.detail=e.detail,this.headers=e.headers,this.requestId=e.requestId,this.statusCode=e.statusCode,this.message=e.message,this.charge=e.charge,this.decline_code=e.decline_code,this.payment_intent=e.payment_intent,this.payment_method=e.payment_method,this.payment_method_type=e.payment_method_type,this.setup_intent=e.setup_intent,this.source=e.source}}P.generate=v;class g extends P{constructor(e={}){super(e,"StripeCardError")}}class T extends P{constructor(e={}){super(e,"StripeInvalidRequestError")}}class _ extends P{constructor(e={}){super(e,"StripeAPIError")}}class E extends P{constructor(e={}){super(e,"StripeAuthenticationError")}}class b extends P{constructor(e={}){super(e,"StripePermissionError")}}class S extends P{constructor(e={}){super(e,"StripeRateLimitError")}}class O extends P{constructor(e={}){super(e,"StripeConnectionError")}}class x extends P{constructor(e,t,r={}){super(r,"StripeSignatureVerificationError"),this.header=e,this.payload=t}}class w extends P{constructor(e={}){super(e,"StripeIdempotencyError")}}class A extends P{constructor(e={}){super(e,"StripeInvalidGrantError")}}class G extends P{constructor(e={}){super(e,"StripeUnknownError")}}var R=r(2874);let C=["apiKey","idempotencyKey","stripeAccount","apiVersion","maxNetworkRetries","timeout","host"];function I(e){return e&&"object"==typeof e&&C.some(t=>Object.prototype.hasOwnProperty.call(e,t))}function k(e){return R.stringify(e,{serializeDate:e=>Math.floor(e.getTime()/1e3).toString()}).replace(/%5B/g,"[").replace(/%5D/g,"]")}let j=(()=>{let e={"\n":"\\n",'"':'\\"',"\u2028":"\\u2028","\u2029":"\\u2029"};return t=>{let r=t.replace(/["\n\r\u2028\u2029]/g,t=>e[t]);return e=>r.replace(/\{([\s\S]+?)\}/g,(t,r)=>encodeURIComponent(e[r]||""))}})();function D(e){if(!Array.isArray(e)||!e[0]||"object"!=typeof e[0])return{};if(!I(e[0]))return e.shift();let t=Object.keys(e[0]),r=t.filter(e=>C.includes(e));return r.length>0&&r.length!==t.length&&M(`Options found in arguments (${r.join(", ")}). Did you mean to pass an options object? See https://github.com/stripe/stripe-node/wiki/Passing-Options.`),{}}function N(e){if("object"!=typeof e)throw Error("Argument must be an object");return Object.keys(e).reduce((t,r)=>(null!=e[r]&&(t[r]=e[r]),t),{})}function F(e,t){return t?e.then(e=>{setTimeout(()=>{t(null,e)},0)},e=>{setTimeout(()=>{t(e,null)},0)}):e}function M(e){return"function"!=typeof process.emitWarning?console.warn(`Stripe: ${e}`):process.emitWarning(e,"Stripe")}function U(e,t,r){if(!Number.isInteger(t)){if(void 0!==r)return r;throw Error(`${e} must be an integer`)}return t}class q{constructor(e,t){this._stripe=e,this._maxBufferedRequestMetric=t}_addHeadersDirectlyToObject(e,t){e.requestId=t["request-id"],e.stripeAccount=e.stripeAccount||t["stripe-account"],e.apiVersion=e.apiVersion||t["stripe-version"],e.idempotencyKey=e.idempotencyKey||t["idempotency-key"]}_makeResponseEvent(e,t,r){let o=Date.now(),n=o-e.request_start_time;return N({api_version:r["stripe-version"],account:r["stripe-account"],idempotency_key:r["idempotency-key"],method:e.method,path:e.path,status:t,request_id:this._getRequestId(r),elapsed:n,request_start_time:e.request_start_time,request_end_time:o})}_getRequestId(e){return e["request-id"]}_streamingResponseHandler(e,t,r){return o=>{let n=o.getHeaders(),i=o.toStream(()=>{let r=this._makeResponseEvent(e,o.getStatusCode(),n);this._stripe._emitter.emit("response",r),this._recordRequestMetrics(this._getRequestId(n),r.elapsed,t)});return this._addHeadersDirectlyToObject(i,n),r(null,i)}}_jsonResponseHandler(e,t,r){return o=>{let n=o.getHeaders(),i=this._getRequestId(n),a=o.getStatusCode(),s=this._makeResponseEvent(e,a,n);this._stripe._emitter.emit("response",s),o.toJSON().then(e=>{if(e.error)throw"string"==typeof e.error&&(e.error={type:e.error,message:e.error_description}),e.error.headers=n,e.error.statusCode=a,e.error.requestId=i,401===a?new E(e.error):403===a?new b(e.error):429===a?new S(e.error):P.generate(e.error);return e},e=>{throw new _({message:"Invalid JSON received from the Stripe API",exception:e,requestId:n["request-id"]})}).then(e=>{this._recordRequestMetrics(i,s.elapsed,t);let a=o.getRawResponse();this._addHeadersDirectlyToObject(a,n),Object.defineProperty(e,"lastResponse",{enumerable:!1,writable:!1,value:a}),r(null,e)},e=>r(e,null))}}static _generateConnectionErrorMessage(e){return`An error occurred with our connection to Stripe.${e>0?` Request was retried ${e} times.`:""}`}static _shouldRetry(e,t,r,o){return!!(o&&0===t&&i.CONNECTION_CLOSED_ERROR_CODES.includes(o.code))||!(t>=r)&&(!e||"false"!==e.getHeaders()["stripe-should-retry"]&&!!("true"===e.getHeaders()["stripe-should-retry"]||409===e.getStatusCode()||e.getStatusCode()>=500))}_getSleepTimeInMS(e,t=null){let r=this._stripe.getInitialNetworkRetryDelay(),o=Math.min(r*Math.pow(e-1,2),this._stripe.getMaxNetworkRetryDelay());return o*=.5*(1+Math.random()),o=Math.max(r,o),Number.isInteger(t)&&t<=60&&(o=Math.max(o,t)),1e3*o}_getMaxNetworkRetries(e={}){return void 0!==e.maxNetworkRetries&&Number.isInteger(e.maxNetworkRetries)?e.maxNetworkRetries:this._stripe.getMaxNetworkRetries()}_defaultIdempotencyKey(e,t){let r=this._getMaxNetworkRetries(t);return"POST"===e&&r>0?`stripe-node-retry-${this._stripe._platformFunctions.uuid4()}`:null}_makeHeaders(e,t,r,o,n,i,a){let s={Authorization:e?`Bearer ${e}`:this._stripe.getApiField("auth"),Accept:"application/json","Content-Type":"application/x-www-form-urlencoded","User-Agent":this._getUserAgentString(),"X-Stripe-Client-User-Agent":o,"X-Stripe-Client-Telemetry":this._getTelemetryHeader(),"Stripe-Version":r,"Stripe-Account":this._stripe.getApiField("stripeAccount"),"Idempotency-Key":this._defaultIdempotencyKey(n,a)},l="POST"==n||"PUT"==n||"PATCH"==n;return(l||t)&&(l||M(`${n} method had non-zero contentLength but no payload is expected for this verb`),s["Content-Length"]=t),Object.assign(N(s),i&&"object"==typeof i?Object.keys(i).reduce((e,t)=>(e[t.split("-").map(e=>e.charAt(0).toUpperCase()+e.substr(1).toLowerCase()).join("-")]=i[t],e),{}):i)}_getUserAgentString(){let e=this._stripe.getConstant("PACKAGE_VERSION"),t=this._stripe._appInfo?this._stripe.getAppInfoAsString():"";return`Stripe/v1 NodeBindings/${e} ${t}`.trim()}_getTelemetryHeader(){if(this._stripe.getTelemetryEnabled()&&this._stripe._prevRequestMetrics.length>0)return JSON.stringify({last_request_metrics:this._stripe._prevRequestMetrics.shift()})}_recordRequestMetrics(e,t,r){if(this._stripe.getTelemetryEnabled()&&e){if(this._stripe._prevRequestMetrics.length>this._maxBufferedRequestMetric)M("Request metrics buffer is full, dropping telemetry message.");else{let o={request_id:e,request_duration_ms:t};r&&r.length>0&&(o.usage=r),this._stripe._prevRequestMetrics.push(o)}}}_request(e,t,r,o,n,a={},s=[],l,u=null){let c;let h=(e,t,r,o,n)=>setTimeout(e,this._getSleepTimeInMS(o,n),t,r,o+1),d=(o,n,u)=>{let p=a.settings&&a.settings.timeout&&Number.isInteger(a.settings.timeout)&&a.settings.timeout>=0?a.settings.timeout:this._stripe.getApiField("timeout"),m=this._stripe.getApiField("httpClient").makeRequest(t||this._stripe.getApiField("host"),this._stripe.getApiField("port"),r,e,n,c,this._stripe.getApiField("protocol"),p),f=Date.now(),y=N({api_version:o,account:n["Stripe-Account"],idempotency_key:n["Idempotency-Key"],method:e,path:r,request_start_time:f}),v=u||0,P=this._getMaxNetworkRetries(a.settings||{});this._stripe._emitter.emit("request",y),m.then(e=>q._shouldRetry(e,v,P)?h(d,o,n,v,e.getHeaders()["retry-after"]):a.streaming&&400>e.getStatusCode()?this._streamingResponseHandler(y,s,l)(e):this._jsonResponseHandler(y,s,l)(e)).catch(e=>q._shouldRetry(null,v,P,e)?h(d,o,n,v,null):l(new O({message:e.code&&e.code===i.TIMEOUT_ERROR_CODE?`Request aborted due to timeout being reached (${p}ms)`:q._generateConnectionErrorMessage(v),detail:e})))},p=(t,r)=>{if(t)return l(t);c=r,this._stripe.getClientUserAgent(t=>{var r,o;let i=this._stripe.getApiField("version"),s=this._makeHeaders(n,c.length,i,t,e,null!==(r=a.headers)&&void 0!==r?r:null,null!==(o=a.settings)&&void 0!==o?o:{});d(i,s,0)})};u?u(e,o,a.headers,p):p(null,k(o||{}))}}class L{constructor(e,t,r,o){this.index=0,this.pagePromise=e,this.promiseCache={currentPromise:null},this.requestArgs=t,this.spec=r,this.stripeResource=o}async iterate(e){if(!(e&&e.data&&"number"==typeof e.data.length))throw Error("Unexpected: Stripe API response does not have a well-formed `data` array.");let t=W(this.requestArgs);if(this.index<e.data.length){let r=t?e.data.length-1-this.index:this.index,o=e.data[r];return this.index+=1,{value:o,done:!1}}if(e.has_more){this.index=0,this.pagePromise=this.getNextPage(e);let t=await this.pagePromise;return this.iterate(t)}return{done:!0,value:void 0}}getNextPage(e){throw Error("Unimplemented")}async _next(){return this.iterate(await this.pagePromise)}next(){if(this.promiseCache.currentPromise)return this.promiseCache.currentPromise;let e=(async()=>{let e=await this._next();return this.promiseCache.currentPromise=null,e})();return this.promiseCache.currentPromise=e,e}}class H extends L{getNextPage(e){let t=W(this.requestArgs),r=function(e,t){let r=t?0:e.data.length-1,o=e.data[r],n=o&&o.id;if(!n)throw Error("Unexpected: No `id` found on the last item while auto-paging a list.");return n}(e,t);return this.stripeResource._makeRequest(this.requestArgs,this.spec,{[t?"ending_before":"starting_after"]:r})}}class $ extends L{getNextPage(e){if(!e.next_page)throw Error("Unexpected: Stripe API response does not have a well-formed `next_page` field, but `has_more` was true.");return this.stripeResource._makeRequest(this.requestArgs,this.spec,{page:e.next_page})}}let z=(e,t,r,o)=>"search"===r.methodType?B(new $(o,t,r,e)):"list"===r.methodType?B(new H(o,t,r,e)):null,B=e=>{var t;let r=(t=(...t)=>e.next(...t),function(){let e=[].slice.call(arguments),r=function(e){if(0===e.length)return;let t=e[0];if("function"!=typeof t)throw Error(`The first argument to autoPagingEach, if present, must be a callback function; received ${typeof t}`);if(2===t.length)return t;if(t.length>2)throw Error(`The \`onItem\` callback function passed to autoPagingEach must accept at most two arguments; got ${t}`);return function(e,r){r(t(e))}}(e),o=function(e){if(e.length<2)return null;let t=e[1];if("function"!=typeof t)throw Error(`The second argument to autoPagingEach, if present, must be a callback function; received ${typeof t}`);return t}(e);if(e.length>2)throw Error(`autoPagingEach takes up to two arguments; received ${e}`);return F(new Promise((e,o)=>{t().then(function o(n){if(n.done){e();return}let i=n.value;return new Promise(e=>{r(i,e)}).then(e=>!1===e?o({done:!0,value:void 0}):t().then(o))}).catch(o)}),o)}),o={autoPagingEach:r,autoPagingToArray:function(e,t){let o=e&&e.limit;if(!o)throw Error("You must pass a `limit` option to autoPagingToArray, e.g., `autoPagingToArray({limit: 1000});`.");if(o>1e4)throw Error("You cannot specify a limit of more than 10,000 items to fetch in `autoPagingToArray`; use `autoPagingEach` to iterate through longer lists.");return F(new Promise((e,t)=>{let n=[];r(e=>{if(n.push(e),n.length>=o)return!1}).then(()=>{e(n)}).catch(t)}),t)},next:()=>e.next(),return:()=>({}),["undefined"!=typeof Symbol&&Symbol.asyncIterator?Symbol.asyncIterator:"@@asyncIterator"]:()=>o};return o};function W(e){return!!D([].slice.call(e)).ending_before}function K(e,t){if(this._stripe=e,t)throw Error("Support for curried url params was dropped in stripe-node v7.0.0. Instead, pass two ids.");this.basePath=j(this.basePath||e.getApiField("basePath")),this.resourcePath=this.path,this.path=j(this.path),this.initialize(...arguments)}function V(e){let t={DEFAULT_TOLERANCE:300,signature:null,constructEvent(e,r,o,n,i,a){try{this.signature.verifyHeader(e,r,o,n||t.DEFAULT_TOLERANCE,i,a)}catch(e){throw e instanceof c&&(e.message+="\nUse `await constructEventAsync(...)` instead of `constructEvent(...)`"),e}return e instanceof Uint8Array?JSON.parse(new TextDecoder("utf8").decode(e)):JSON.parse(e)},async constructEventAsync(e,r,o,n,i,a){return await this.signature.verifyHeaderAsync(e,r,o,n||t.DEFAULT_TOLERANCE,i,a),e instanceof Uint8Array?JSON.parse(new TextDecoder("utf8").decode(e)):JSON.parse(e)},generateTestHeaderString:function(e){let t=l(e),r=t.signature||t.cryptoProvider.computeHMACSignature(t.payloadString,t.secret);return t.generateHeaderString(r)},generateTestHeaderStringAsync:async function(e){let t=l(e),r=t.signature||await t.cryptoProvider.computeHMACSignatureAsync(t.payloadString,t.secret);return t.generateHeaderString(r)}},r={EXPECTED_SCHEME:"v1",verifyHeader(e,t,r,a,l,u){let{decodedHeader:c,decodedPayload:h,details:d,suspectPayloadType:p}=n(e,t,this.EXPECTED_SCHEME),m=/\s/.test(r),f=(l=l||s()).computeHMACSignature(o(h,d),r);return i(h,c,d,f,a,p,m,u),!0},async verifyHeaderAsync(e,t,r,a,l,u){let{decodedHeader:c,decodedPayload:h,details:d,suspectPayloadType:p}=n(e,t,this.EXPECTED_SCHEME),m=/\s/.test(r);l=l||s();let f=await l.computeHMACSignatureAsync(o(h,d),r);return i(h,c,d,f,a,p,m,u)}};function o(e,t){return`${t.timestamp}.${e}`}function n(e,t,r){if(!e)throw new x(t,e,{message:"No webhook payload was provided."});let o="string"!=typeof e&&!(e instanceof Uint8Array),n=new TextDecoder("utf8"),i=e instanceof Uint8Array?n.decode(e):e;if(Array.isArray(t))throw Error("Unexpected: An array was passed as a header, which should not be possible for the stripe-signature header.");if(null==t||""==t)throw new x(t,e,{message:"No stripe-signature header value was provided."});let a=t instanceof Uint8Array?n.decode(t):t,s="string"!=typeof a?null:a.split(",").reduce((e,t)=>{let o=t.split("=");return"t"===o[0]&&(e.timestamp=parseInt(o[1],10)),o[0]===r&&e.signatures.push(o[1]),e},{timestamp:-1,signatures:[]});if(!s||-1===s.timestamp)throw new x(a,i,{message:"Unable to extract timestamp and signatures from header"});if(!s.signatures.length)throw new x(a,i,{message:"No signatures found with expected scheme"});return{decodedPayload:i,decodedHeader:a,details:s,suspectPayloadType:o}}function i(t,r,o,n,i,a,s,l){let u=!!o.signatures.filter(e.secureCompare.bind(e,n)).length,c="\nLearn more about webhook signing and explore webhook integration examples for various frameworks at https://github.com/stripe/stripe-node#webhook-signing",h=s?"\n\nNote: The provided signing secret contains whitespace. This often indicates an extra newline or space is in the value":"";if(!u){if(a)throw new x(r,t,{message:"Webhook payload must be provided as a string or a Buffer (https://nodejs.org/api/buffer.html) instance representing the _raw_ request body.Payload was provided as a parsed JavaScript object instead. \nSignature verification is impossible without access to the original signed material. \n"+c+"\n"+h});throw new x(r,t,{message:"No signatures found matching the expected signature for payload. Are you passing the raw request body you received from Stripe? \n If a webhook request is being forwarded by a third-party tool, ensure that the exact request body, including JSON formatting and new line style, is preserved.\n"+c+"\n"+h})}let d=Math.floor(("number"==typeof l?l:Date.now())/1e3)-o.timestamp;if(i>0&&d>i)throw new x(r,t,{message:"Timestamp outside the tolerance zone"});return!0}let a=null;function s(){return a||(a=e.createDefaultCryptoProvider()),a}function l(e){if(!e)throw new P({message:"Options are required"});let t=Math.floor(e.timestamp)||Math.floor(Date.now()/1e3),o=e.scheme||r.EXPECTED_SCHEME,n=e.cryptoProvider||s(),i=`${t}.${e.payload}`;return Object.assign(Object.assign({},e),{timestamp:t,scheme:o,cryptoProvider:n,payloadString:i,generateHeaderString:e=>`t=${t},${o}=${e}`})}return t.signature=r,t}function J(e,t){for(let r in t){if(!Object.prototype.hasOwnProperty.call(t,r))continue;let o=r[0].toLowerCase()+r.substring(1),n=new t[r](e);this[o]=n}}function Q(e,t){return function(e){return new J(e,t)}}K.extend=function(e){let t=this,r=Object.prototype.hasOwnProperty.call(e,"constructor")?e.constructor:function(...e){t.apply(this,e)};return Object.assign(r,t),r.prototype=Object.create(t.prototype),Object.assign(r.prototype,e),r},K.method=function(e){if(void 0!==e.path&&void 0!==e.fullPath)throw Error(`Method spec specified both a 'path' (${e.path}) and a 'fullPath' (${e.fullPath}).`);return function(...t){let r="function"==typeof t[t.length-1]&&t.pop();e.urlParams=function(e){let t=e.match(/\{\w+\}/g);return t?t.map(e=>e.replace(/[{}]/g,"")):[]}(e.fullPath||this.createResourcePathWithSymbols(e.path||""));let o=F(this._makeRequest(t,e,{}),r);return Object.assign(o,z(this,t,e,o)),o}},K.MAX_BUFFERED_REQUEST_METRICS=100,K.prototype={_stripe:null,path:"",resourcePath:"",basePath:null,initialize(){},requestDataProcessor:null,validateRequest:null,createFullPath(e,t){let r=[this.basePath(t),this.path(t)];if("function"==typeof e){let o=e(t);o&&r.push(o)}else r.push(e);return this._joinUrlParts(r)},createResourcePathWithSymbols(e){return e?`/${this._joinUrlParts([this.resourcePath,e])}`:`/${this.resourcePath}`},_joinUrlParts:e=>e.join("/").replace(/\/{2,}/g,"/"),_getRequestOpts(e,t,r){let o=(t.method||"GET").toUpperCase(),n=t.usage||[],i=t.urlParams||[],a=t.encode||(e=>e),s=!!t.fullPath,l=j(s?t.fullPath:t.path||""),u=s?t.fullPath:this.createResourcePathWithSymbols(t.path),c=[].slice.call(e),h=i.reduce((e,t)=>{let r=c.shift();if("string"!=typeof r)throw Error(`Stripe: Argument "${t}" must be a string, but got: ${r} (on API request to \`${o} ${u}\`)`);return e[t]=r,e},{}),d=a(Object.assign({},D(c),r)),p=function(e){let t={auth:null,host:null,headers:{},settings:{}};if(e.length>0){let r=e[e.length-1];if("string"==typeof r)t.auth=e.pop();else if(I(r)){let r=Object.assign({},e.pop()),o=Object.keys(r).filter(e=>!C.includes(e));o.length&&M(`Invalid options found (${o.join(", ")}); ignoring.`),r.apiKey&&(t.auth=r.apiKey),r.idempotencyKey&&(t.headers["Idempotency-Key"]=r.idempotencyKey),r.stripeAccount&&(t.headers["Stripe-Account"]=r.stripeAccount),r.apiVersion&&(t.headers["Stripe-Version"]=r.apiVersion),Number.isInteger(r.maxNetworkRetries)&&(t.settings.maxNetworkRetries=r.maxNetworkRetries),Number.isInteger(r.timeout)&&(t.settings.timeout=r.timeout),r.host&&(t.host=r.host)}}return t}(c),m=p.host||t.host,f=!!t.streaming;if(c.filter(e=>null!=e).length)throw Error(`Stripe: Unknown arguments (${c}). Did you mean to pass an options object? See https://github.com/stripe/stripe-node/wiki/Passing-Options. (on API request to ${o} \`${u}\`)`);let y=s?l(h):this.createFullPath(l,h),v=Object.assign(p.headers,t.headers);t.validator&&t.validator(d,{headers:v});let P="GET"===t.method||"DELETE"===t.method;return{requestMethod:o,requestPath:y,bodyData:P?null:d,queryData:P?d:{},auth:p.auth,headers:v,host:null!=m?m:null,streaming:f,settings:p.settings,usage:n}},_makeRequest(e,t,r){return new Promise((o,n)=>{var i;let a;try{a=this._getRequestOpts(e,t,r)}catch(e){n(e);return}let s=0===Object.keys(a.queryData).length,l=[a.requestPath,s?"":"?",k(a.queryData)].join(""),{headers:u,settings:c}=a;this._stripe._requestSender._request(a.requestMethod,a.host,l,a.bodyData,a.auth,{headers:u,settings:c,streaming:a.streaming},a.usage,function(e,r){e?n(e):o(t.transformResponseData?t.transformResponseData(r):r)},null===(i=this.requestDataProcessor)||void 0===i?void 0:i.bind(this))})}};let X=K.method,Y=K.extend({retrieve:X({method:"GET",fullPath:"/v1/financial_connections/accounts/{account}"}),list:X({method:"GET",fullPath:"/v1/financial_connections/accounts",methodType:"list"}),disconnect:X({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/disconnect"}),listOwners:X({method:"GET",fullPath:"/v1/financial_connections/accounts/{account}/owners",methodType:"list"}),refresh:X({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/refresh"}),subscribe:X({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/subscribe"}),unsubscribe:X({method:"POST",fullPath:"/v1/financial_connections/accounts/{account}/unsubscribe"})}),Z=K.method,ee=K.extend({retrieve:Z({method:"GET",fullPath:"/v1/entitlements/active_entitlements/{id}"}),list:Z({method:"GET",fullPath:"/v1/entitlements/active_entitlements",methodType:"list"})}),et=K.method,er=K.extend({create:et({method:"POST",fullPath:"/v1/billing/alerts"}),retrieve:et({method:"GET",fullPath:"/v1/billing/alerts/{id}"}),list:et({method:"GET",fullPath:"/v1/billing/alerts",methodType:"list"}),activate:et({method:"POST",fullPath:"/v1/billing/alerts/{id}/activate"}),archive:et({method:"POST",fullPath:"/v1/billing/alerts/{id}/archive"}),deactivate:et({method:"POST",fullPath:"/v1/billing/alerts/{id}/deactivate"})}),eo=K.method,en=K.extend({create:eo({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations"}),capture:eo({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/capture"}),expire:eo({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/expire"}),finalizeAmount:eo({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/finalize_amount"}),increment:eo({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/increment"}),reverse:eo({method:"POST",fullPath:"/v1/test_helpers/issuing/authorizations/{authorization}/reverse"})}),ei=K.method,ea=K.extend({retrieve:ei({method:"GET",fullPath:"/v1/issuing/authorizations/{authorization}"}),update:ei({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}"}),list:ei({method:"GET",fullPath:"/v1/issuing/authorizations",methodType:"list"}),approve:ei({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}/approve"}),decline:ei({method:"POST",fullPath:"/v1/issuing/authorizations/{authorization}/decline"})}),es=K.method,el=K.extend({create:es({method:"POST",fullPath:"/v1/tax/calculations"}),retrieve:es({method:"GET",fullPath:"/v1/tax/calculations/{calculation}"}),listLineItems:es({method:"GET",fullPath:"/v1/tax/calculations/{calculation}/line_items",methodType:"list"})}),eu=K.method,ec=K.extend({create:eu({method:"POST",fullPath:"/v1/issuing/cardholders"}),retrieve:eu({method:"GET",fullPath:"/v1/issuing/cardholders/{cardholder}"}),update:eu({method:"POST",fullPath:"/v1/issuing/cardholders/{cardholder}"}),list:eu({method:"GET",fullPath:"/v1/issuing/cardholders",methodType:"list"})}),eh=K.method,ed=K.extend({deliverCard:eh({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/deliver"}),failCard:eh({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/fail"}),returnCard:eh({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/return"}),shipCard:eh({method:"POST",fullPath:"/v1/test_helpers/issuing/cards/{card}/shipping/ship"})}),ep=K.method,em=K.extend({create:ep({method:"POST",fullPath:"/v1/issuing/cards"}),retrieve:ep({method:"GET",fullPath:"/v1/issuing/cards/{card}"}),update:ep({method:"POST",fullPath:"/v1/issuing/cards/{card}"}),list:ep({method:"GET",fullPath:"/v1/issuing/cards",methodType:"list"})}),ef=K.method,ey=K.extend({create:ef({method:"POST",fullPath:"/v1/billing_portal/configurations"}),retrieve:ef({method:"GET",fullPath:"/v1/billing_portal/configurations/{configuration}"}),update:ef({method:"POST",fullPath:"/v1/billing_portal/configurations/{configuration}"}),list:ef({method:"GET",fullPath:"/v1/billing_portal/configurations",methodType:"list"})}),ev=K.method,eP=K.extend({create:ev({method:"POST",fullPath:"/v1/terminal/configurations"}),retrieve:ev({method:"GET",fullPath:"/v1/terminal/configurations/{configuration}"}),update:ev({method:"POST",fullPath:"/v1/terminal/configurations/{configuration}"}),list:ev({method:"GET",fullPath:"/v1/terminal/configurations",methodType:"list"}),del:ev({method:"DELETE",fullPath:"/v1/terminal/configurations/{configuration}"})}),eg=K.method,eT=K.extend({create:eg({method:"POST",fullPath:"/v1/test_helpers/confirmation_tokens"})}),e_=K.method,eE=K.extend({create:e_({method:"POST",fullPath:"/v1/terminal/connection_tokens"})}),eb=K.method,eS=K.extend({create:eb({method:"POST",fullPath:"/v1/treasury/credit_reversals"}),retrieve:eb({method:"GET",fullPath:"/v1/treasury/credit_reversals/{credit_reversal}"}),list:eb({method:"GET",fullPath:"/v1/treasury/credit_reversals",methodType:"list"})}),eO=K.method,ex=K.extend({fundCashBalance:eO({method:"POST",fullPath:"/v1/test_helpers/customers/{customer}/fund_cash_balance"})}),ew=K.method,eA=K.extend({create:ew({method:"POST",fullPath:"/v1/treasury/debit_reversals"}),retrieve:ew({method:"GET",fullPath:"/v1/treasury/debit_reversals/{debit_reversal}"}),list:ew({method:"GET",fullPath:"/v1/treasury/debit_reversals",methodType:"list"})}),eG=K.method,eR=K.extend({create:eG({method:"POST",fullPath:"/v1/issuing/disputes"}),retrieve:eG({method:"GET",fullPath:"/v1/issuing/disputes/{dispute}"}),update:eG({method:"POST",fullPath:"/v1/issuing/disputes/{dispute}"}),list:eG({method:"GET",fullPath:"/v1/issuing/disputes",methodType:"list"}),submit:eG({method:"POST",fullPath:"/v1/issuing/disputes/{dispute}/submit"})}),eC=K.method,eI=K.extend({retrieve:eC({method:"GET",fullPath:"/v1/radar/early_fraud_warnings/{early_fraud_warning}"}),list:eC({method:"GET",fullPath:"/v1/radar/early_fraud_warnings",methodType:"list"})}),ek=K.method,ej=K.extend({create:ek({method:"POST",fullPath:"/v1/entitlements/features"}),retrieve:ek({method:"GET",fullPath:"/v1/entitlements/features/{id}"}),update:ek({method:"POST",fullPath:"/v1/entitlements/features/{id}"}),list:ek({method:"GET",fullPath:"/v1/entitlements/features",methodType:"list"})}),eD=K.method,eN=K.extend({create:eD({method:"POST",fullPath:"/v1/treasury/financial_accounts"}),retrieve:eD({method:"GET",fullPath:"/v1/treasury/financial_accounts/{financial_account}"}),update:eD({method:"POST",fullPath:"/v1/treasury/financial_accounts/{financial_account}"}),list:eD({method:"GET",fullPath:"/v1/treasury/financial_accounts",methodType:"list"}),retrieveFeatures:eD({method:"GET",fullPath:"/v1/treasury/financial_accounts/{financial_account}/features"}),updateFeatures:eD({method:"POST",fullPath:"/v1/treasury/financial_accounts/{financial_account}/features"})}),eF=K.method,eM=K.extend({fail:eF({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/fail"}),returnInboundTransfer:eF({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/return"}),succeed:eF({method:"POST",fullPath:"/v1/test_helpers/treasury/inbound_transfers/{id}/succeed"})}),eU=K.method,eq=K.extend({create:eU({method:"POST",fullPath:"/v1/treasury/inbound_transfers"}),retrieve:eU({method:"GET",fullPath:"/v1/treasury/inbound_transfers/{id}"}),list:eU({method:"GET",fullPath:"/v1/treasury/inbound_transfers",methodType:"list"}),cancel:eU({method:"POST",fullPath:"/v1/treasury/inbound_transfers/{inbound_transfer}/cancel"})}),eL=K.method,eH=K.extend({create:eL({method:"POST",fullPath:"/v1/terminal/locations"}),retrieve:eL({method:"GET",fullPath:"/v1/terminal/locations/{location}"}),update:eL({method:"POST",fullPath:"/v1/terminal/locations/{location}"}),list:eL({method:"GET",fullPath:"/v1/terminal/locations",methodType:"list"}),del:eL({method:"DELETE",fullPath:"/v1/terminal/locations/{location}"})}),e$=K.method,ez=K.extend({create:e$({method:"POST",fullPath:"/v1/billing/meter_event_adjustments"})}),eB=K.method,eW=K.extend({create:eB({method:"POST",fullPath:"/v1/billing/meter_events"})}),eK=K.method,eV=K.extend({create:eK({method:"POST",fullPath:"/v1/billing/meters"}),retrieve:eK({method:"GET",fullPath:"/v1/billing/meters/{id}"}),update:eK({method:"POST",fullPath:"/v1/billing/meters/{id}"}),list:eK({method:"GET",fullPath:"/v1/billing/meters",methodType:"list"}),deactivate:eK({method:"POST",fullPath:"/v1/billing/meters/{id}/deactivate"}),listEventSummaries:eK({method:"GET",fullPath:"/v1/billing/meters/{id}/event_summaries",methodType:"list"}),reactivate:eK({method:"POST",fullPath:"/v1/billing/meters/{id}/reactivate"})}),eJ=K.method,eQ=K.extend({create:eJ({method:"POST",fullPath:"/v1/climate/orders"}),retrieve:eJ({method:"GET",fullPath:"/v1/climate/orders/{order}"}),update:eJ({method:"POST",fullPath:"/v1/climate/orders/{order}"}),list:eJ({method:"GET",fullPath:"/v1/climate/orders",methodType:"list"}),cancel:eJ({method:"POST",fullPath:"/v1/climate/orders/{order}/cancel"})}),eX=K.method,eY=K.extend({update:eX({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}"}),fail:eX({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/fail"}),post:eX({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/post"}),returnOutboundPayment:eX({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_payments/{id}/return"})}),eZ=K.method,e1=K.extend({create:eZ({method:"POST",fullPath:"/v1/treasury/outbound_payments"}),retrieve:eZ({method:"GET",fullPath:"/v1/treasury/outbound_payments/{id}"}),list:eZ({method:"GET",fullPath:"/v1/treasury/outbound_payments",methodType:"list"}),cancel:eZ({method:"POST",fullPath:"/v1/treasury/outbound_payments/{id}/cancel"})}),e0=K.method,e8=K.extend({update:e0({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}"}),fail:e0({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/fail"}),post:e0({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/post"}),returnOutboundTransfer:e0({method:"POST",fullPath:"/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/return"})}),e2=K.method,e5=K.extend({create:e2({method:"POST",fullPath:"/v1/treasury/outbound_transfers"}),retrieve:e2({method:"GET",fullPath:"/v1/treasury/outbound_transfers/{outbound_transfer}"}),list:e2({method:"GET",fullPath:"/v1/treasury/outbound_transfers",methodType:"list"}),cancel:e2({method:"POST",fullPath:"/v1/treasury/outbound_transfers/{outbound_transfer}/cancel"})}),e3=K.method,e6=K.extend({activate:e3({method:"POST",fullPath:"/v1/test_helpers/issuing/personalization_designs/{personalization_design}/activate"}),deactivate:e3({method:"POST",fullPath:"/v1/test_helpers/issuing/personalization_designs/{personalization_design}/deactivate"}),reject:e3({method:"POST",fullPath:"/v1/test_helpers/issuing/personalization_designs/{personalization_design}/reject"})}),e4=K.method,e7=K.extend({create:e4({method:"POST",fullPath:"/v1/issuing/personalization_designs"}),retrieve:e4({method:"GET",fullPath:"/v1/issuing/personalization_designs/{personalization_design}"}),update:e4({method:"POST",fullPath:"/v1/issuing/personalization_designs/{personalization_design}"}),list:e4({method:"GET",fullPath:"/v1/issuing/personalization_designs",methodType:"list"})}),e9=K.method,te=K.extend({retrieve:e9({method:"GET",fullPath:"/v1/issuing/physical_bundles/{physical_bundle}"}),list:e9({method:"GET",fullPath:"/v1/issuing/physical_bundles",methodType:"list"})}),tt=K.method,tr=K.extend({retrieve:tt({method:"GET",fullPath:"/v1/climate/products/{product}"}),list:tt({method:"GET",fullPath:"/v1/climate/products",methodType:"list"})}),to=K.method,tn=K.extend({presentPaymentMethod:to({method:"POST",fullPath:"/v1/test_helpers/terminal/readers/{reader}/present_payment_method"})}),ti=K.method,ta=K.extend({create:ti({method:"POST",fullPath:"/v1/terminal/readers"}),retrieve:ti({method:"GET",fullPath:"/v1/terminal/readers/{reader}"}),update:ti({method:"POST",fullPath:"/v1/terminal/readers/{reader}"}),list:ti({method:"GET",fullPath:"/v1/terminal/readers",methodType:"list"}),del:ti({method:"DELETE",fullPath:"/v1/terminal/readers/{reader}"}),cancelAction:ti({method:"POST",fullPath:"/v1/terminal/readers/{reader}/cancel_action"}),processPaymentIntent:ti({method:"POST",fullPath:"/v1/terminal/readers/{reader}/process_payment_intent"}),processSetupIntent:ti({method:"POST",fullPath:"/v1/terminal/readers/{reader}/process_setup_intent"}),refundPayment:ti({method:"POST",fullPath:"/v1/terminal/readers/{reader}/refund_payment"}),setReaderDisplay:ti({method:"POST",fullPath:"/v1/terminal/readers/{reader}/set_reader_display"})}),ts=K.method,tl=K.extend({create:ts({method:"POST",fullPath:"/v1/test_helpers/treasury/received_credits"})}),tu=K.method,tc=K.extend({retrieve:tu({method:"GET",fullPath:"/v1/treasury/received_credits/{id}"}),list:tu({method:"GET",fullPath:"/v1/treasury/received_credits",methodType:"list"})}),th=K.method,td=K.extend({create:th({method:"POST",fullPath:"/v1/test_helpers/treasury/received_debits"})}),tp=K.method,tm=K.extend({retrieve:tp({method:"GET",fullPath:"/v1/treasury/received_debits/{id}"}),list:tp({method:"GET",fullPath:"/v1/treasury/received_debits",methodType:"list"})}),tf=K.method,ty=K.extend({expire:tf({method:"POST",fullPath:"/v1/test_helpers/refunds/{refund}/expire"})}),tv=K.method,tP=K.extend({create:tv({method:"POST",fullPath:"/v1/tax/registrations"}),retrieve:tv({method:"GET",fullPath:"/v1/tax/registrations/{id}"}),update:tv({method:"POST",fullPath:"/v1/tax/registrations/{id}"}),list:tv({method:"GET",fullPath:"/v1/tax/registrations",methodType:"list"})}),tg=K.method,tT=K.extend({create:tg({method:"POST",fullPath:"/v1/reporting/report_runs"}),retrieve:tg({method:"GET",fullPath:"/v1/reporting/report_runs/{report_run}"}),list:tg({method:"GET",fullPath:"/v1/reporting/report_runs",methodType:"list"})}),t_=K.method,tE=K.extend({retrieve:t_({method:"GET",fullPath:"/v1/reporting/report_types/{report_type}"}),list:t_({method:"GET",fullPath:"/v1/reporting/report_types",methodType:"list"})}),tb=K.method,tS=K.extend({create:tb({method:"POST",fullPath:"/v1/forwarding/requests"}),retrieve:tb({method:"GET",fullPath:"/v1/forwarding/requests/{id}"}),list:tb({method:"GET",fullPath:"/v1/forwarding/requests",methodType:"list"})}),tO=K.method,tx=K.extend({retrieve:tO({method:"GET",fullPath:"/v1/sigma/scheduled_query_runs/{scheduled_query_run}"}),list:tO({method:"GET",fullPath:"/v1/sigma/scheduled_query_runs",methodType:"list"})}),tw=K.method,tA=K.extend({create:tw({method:"POST",fullPath:"/v1/apps/secrets"}),list:tw({method:"GET",fullPath:"/v1/apps/secrets",methodType:"list"}),deleteWhere:tw({method:"POST",fullPath:"/v1/apps/secrets/delete"}),find:tw({method:"GET",fullPath:"/v1/apps/secrets/find"})}),tG=K.method,tR=K.extend({create:tG({method:"POST",fullPath:"/v1/billing_portal/sessions"})}),tC=K.method,tI=K.extend({create:tC({method:"POST",fullPath:"/v1/checkout/sessions"}),retrieve:tC({method:"GET",fullPath:"/v1/checkout/sessions/{session}"}),update:tC({method:"POST",fullPath:"/v1/checkout/sessions/{session}"}),list:tC({method:"GET",fullPath:"/v1/checkout/sessions",methodType:"list"}),expire:tC({method:"POST",fullPath:"/v1/checkout/sessions/{session}/expire"}),listLineItems:tC({method:"GET",fullPath:"/v1/checkout/sessions/{session}/line_items",methodType:"list"})}),tk=K.method,tj=K.extend({create:tk({method:"POST",fullPath:"/v1/financial_connections/sessions"}),retrieve:tk({method:"GET",fullPath:"/v1/financial_connections/sessions/{session}"})}),tD=K.method,tN=K.extend({retrieve:tD({method:"GET",fullPath:"/v1/tax/settings"}),update:tD({method:"POST",fullPath:"/v1/tax/settings"})}),tF=K.method,tM=K.extend({retrieve:tF({method:"GET",fullPath:"/v1/climate/suppliers/{supplier}"}),list:tF({method:"GET",fullPath:"/v1/climate/suppliers",methodType:"list"})}),tU=K.method,tq=K.extend({create:tU({method:"POST",fullPath:"/v1/test_helpers/test_clocks"}),retrieve:tU({method:"GET",fullPath:"/v1/test_helpers/test_clocks/{test_clock}"}),list:tU({method:"GET",fullPath:"/v1/test_helpers/test_clocks",methodType:"list"}),del:tU({method:"DELETE",fullPath:"/v1/test_helpers/test_clocks/{test_clock}"}),advance:tU({method:"POST",fullPath:"/v1/test_helpers/test_clocks/{test_clock}/advance"})}),tL=K.method,tH=K.extend({retrieve:tL({method:"GET",fullPath:"/v1/issuing/tokens/{token}"}),update:tL({method:"POST",fullPath:"/v1/issuing/tokens/{token}"}),list:tL({method:"GET",fullPath:"/v1/issuing/tokens",methodType:"list"})}),t$=K.method,tz=K.extend({retrieve:t$({method:"GET",fullPath:"/v1/treasury/transaction_entries/{id}"}),list:t$({method:"GET",fullPath:"/v1/treasury/transaction_entries",methodType:"list"})}),tB=K.method,tW=K.extend({createForceCapture:tB({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/create_force_capture"}),createUnlinkedRefund:tB({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/create_unlinked_refund"}),refund:tB({method:"POST",fullPath:"/v1/test_helpers/issuing/transactions/{transaction}/refund"})}),tK=K.method,tV=K.extend({retrieve:tK({method:"GET",fullPath:"/v1/financial_connections/transactions/{transaction}"}),list:tK({method:"GET",fullPath:"/v1/financial_connections/transactions",methodType:"list"})}),tJ=K.method,tQ=K.extend({retrieve:tJ({method:"GET",fullPath:"/v1/issuing/transactions/{transaction}"}),update:tJ({method:"POST",fullPath:"/v1/issuing/transactions/{transaction}"}),list:tJ({method:"GET",fullPath:"/v1/issuing/transactions",methodType:"list"})}),tX=K.method,tY=K.extend({retrieve:tX({method:"GET",fullPath:"/v1/tax/transactions/{transaction}"}),createFromCalculation:tX({method:"POST",fullPath:"/v1/tax/transactions/create_from_calculation"}),createReversal:tX({method:"POST",fullPath:"/v1/tax/transactions/create_reversal"}),listLineItems:tX({method:"GET",fullPath:"/v1/tax/transactions/{transaction}/line_items",methodType:"list"})}),tZ=K.method,t1=K.extend({retrieve:tZ({method:"GET",fullPath:"/v1/treasury/transactions/{id}"}),list:tZ({method:"GET",fullPath:"/v1/treasury/transactions",methodType:"list"})}),t0=K.method,t8=K.extend({create:t0({method:"POST",fullPath:"/v1/radar/value_list_items"}),retrieve:t0({method:"GET",fullPath:"/v1/radar/value_list_items/{item}"}),list:t0({method:"GET",fullPath:"/v1/radar/value_list_items",methodType:"list"}),del:t0({method:"DELETE",fullPath:"/v1/radar/value_list_items/{item}"})}),t2=K.method,t5=K.extend({create:t2({method:"POST",fullPath:"/v1/radar/value_lists"}),retrieve:t2({method:"GET",fullPath:"/v1/radar/value_lists/{value_list}"}),update:t2({method:"POST",fullPath:"/v1/radar/value_lists/{value_list}"}),list:t2({method:"GET",fullPath:"/v1/radar/value_lists",methodType:"list"}),del:t2({method:"DELETE",fullPath:"/v1/radar/value_lists/{value_list}"})}),t3=K.method,t6=K.extend({retrieve:t3({method:"GET",fullPath:"/v1/identity/verification_reports/{report}"}),list:t3({method:"GET",fullPath:"/v1/identity/verification_reports",methodType:"list"})}),t4=K.method,t7=K.extend({create:t4({method:"POST",fullPath:"/v1/identity/verification_sessions"}),retrieve:t4({method:"GET",fullPath:"/v1/identity/verification_sessions/{session}"}),update:t4({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}"}),list:t4({method:"GET",fullPath:"/v1/identity/verification_sessions",methodType:"list"}),cancel:t4({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}/cancel"}),redact:t4({method:"POST",fullPath:"/v1/identity/verification_sessions/{session}/redact"})}),t9=K.method,re=K.extend({create:t9({method:"POST",fullPath:"/v1/accounts"}),retrieve(e,...t){return"string"==typeof e?t9({method:"GET",fullPath:"/v1/accounts/{id}"}).apply(this,[e,...t]):(null==e&&[].shift.apply([e,...t]),t9({method:"GET",fullPath:"/v1/account"}).apply(this,[e,...t]))},update:t9({method:"POST",fullPath:"/v1/accounts/{account}"}),list:t9({method:"GET",fullPath:"/v1/accounts",methodType:"list"}),del:t9({method:"DELETE",fullPath:"/v1/accounts/{account}"}),createExternalAccount:t9({method:"POST",fullPath:"/v1/accounts/{account}/external_accounts"}),createLoginLink:t9({method:"POST",fullPath:"/v1/accounts/{account}/login_links"}),createPerson:t9({method:"POST",fullPath:"/v1/accounts/{account}/persons"}),deleteExternalAccount:t9({method:"DELETE",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),deletePerson:t9({method:"DELETE",fullPath:"/v1/accounts/{account}/persons/{person}"}),listCapabilities:t9({method:"GET",fullPath:"/v1/accounts/{account}/capabilities",methodType:"list"}),listExternalAccounts:t9({method:"GET",fullPath:"/v1/accounts/{account}/external_accounts",methodType:"list"}),listPersons:t9({method:"GET",fullPath:"/v1/accounts/{account}/persons",methodType:"list"}),reject:t9({method:"POST",fullPath:"/v1/accounts/{account}/reject"}),retrieveCurrent:t9({method:"GET",fullPath:"/v1/account"}),retrieveCapability:t9({method:"GET",fullPath:"/v1/accounts/{account}/capabilities/{capability}"}),retrieveExternalAccount:t9({method:"GET",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),retrievePerson:t9({method:"GET",fullPath:"/v1/accounts/{account}/persons/{person}"}),updateCapability:t9({method:"POST",fullPath:"/v1/accounts/{account}/capabilities/{capability}"}),updateExternalAccount:t9({method:"POST",fullPath:"/v1/accounts/{account}/external_accounts/{id}"}),updatePerson:t9({method:"POST",fullPath:"/v1/accounts/{account}/persons/{person}"})}),rt=K.method,rr=K.extend({create:rt({method:"POST",fullPath:"/v1/account_links"})}),ro=K.method,rn=K.extend({create:ro({method:"POST",fullPath:"/v1/account_sessions"})}),ri=K.method,ra=K.extend({create:ri({method:"POST",fullPath:"/v1/apple_pay/domains"}),retrieve:ri({method:"GET",fullPath:"/v1/apple_pay/domains/{domain}"}),list:ri({method:"GET",fullPath:"/v1/apple_pay/domains",methodType:"list"}),del:ri({method:"DELETE",fullPath:"/v1/apple_pay/domains/{domain}"})}),rs=K.method,rl=K.extend({retrieve:rs({method:"GET",fullPath:"/v1/application_fees/{id}"}),list:rs({method:"GET",fullPath:"/v1/application_fees",methodType:"list"}),createRefund:rs({method:"POST",fullPath:"/v1/application_fees/{id}/refunds"}),listRefunds:rs({method:"GET",fullPath:"/v1/application_fees/{id}/refunds",methodType:"list"}),retrieveRefund:rs({method:"GET",fullPath:"/v1/application_fees/{fee}/refunds/{id}"}),updateRefund:rs({method:"POST",fullPath:"/v1/application_fees/{fee}/refunds/{id}"})}),ru=K.method,rc=K.extend({retrieve:ru({method:"GET",fullPath:"/v1/balance"})}),rh=K.method,rd=K.extend({retrieve:rh({method:"GET",fullPath:"/v1/balance_transactions/{id}"}),list:rh({method:"GET",fullPath:"/v1/balance_transactions",methodType:"list"})}),rp=K.method,rm=K.extend({create:rp({method:"POST",fullPath:"/v1/charges"}),retrieve:rp({method:"GET",fullPath:"/v1/charges/{charge}"}),update:rp({method:"POST",fullPath:"/v1/charges/{charge}"}),list:rp({method:"GET",fullPath:"/v1/charges",methodType:"list"}),capture:rp({method:"POST",fullPath:"/v1/charges/{charge}/capture"}),search:rp({method:"GET",fullPath:"/v1/charges/search",methodType:"search"})}),rf=K.method,ry=K.extend({retrieve:rf({method:"GET",fullPath:"/v1/confirmation_tokens/{confirmation_token}"})}),rv=K.method,rP=K.extend({retrieve:rv({method:"GET",fullPath:"/v1/country_specs/{country}"}),list:rv({method:"GET",fullPath:"/v1/country_specs",methodType:"list"})}),rg=K.method,rT=K.extend({create:rg({method:"POST",fullPath:"/v1/coupons"}),retrieve:rg({method:"GET",fullPath:"/v1/coupons/{coupon}"}),update:rg({method:"POST",fullPath:"/v1/coupons/{coupon}"}),list:rg({method:"GET",fullPath:"/v1/coupons",methodType:"list"}),del:rg({method:"DELETE",fullPath:"/v1/coupons/{coupon}"})}),r_=K.method,rE=K.extend({create:r_({method:"POST",fullPath:"/v1/credit_notes"}),retrieve:r_({method:"GET",fullPath:"/v1/credit_notes/{id}"}),update:r_({method:"POST",fullPath:"/v1/credit_notes/{id}"}),list:r_({method:"GET",fullPath:"/v1/credit_notes",methodType:"list"}),listLineItems:r_({method:"GET",fullPath:"/v1/credit_notes/{credit_note}/lines",methodType:"list"}),listPreviewLineItems:r_({method:"GET",fullPath:"/v1/credit_notes/preview/lines",methodType:"list"}),preview:r_({method:"GET",fullPath:"/v1/credit_notes/preview"}),voidCreditNote:r_({method:"POST",fullPath:"/v1/credit_notes/{id}/void"})}),rb=K.method,rS=K.extend({create:rb({method:"POST",fullPath:"/v1/customer_sessions"})}),rO=K.method,rx=K.extend({create:rO({method:"POST",fullPath:"/v1/customers"}),retrieve:rO({method:"GET",fullPath:"/v1/customers/{customer}"}),update:rO({method:"POST",fullPath:"/v1/customers/{customer}"}),list:rO({method:"GET",fullPath:"/v1/customers",methodType:"list"}),del:rO({method:"DELETE",fullPath:"/v1/customers/{customer}"}),createBalanceTransaction:rO({method:"POST",fullPath:"/v1/customers/{customer}/balance_transactions"}),createFundingInstructions:rO({method:"POST",fullPath:"/v1/customers/{customer}/funding_instructions"}),createSource:rO({method:"POST",fullPath:"/v1/customers/{customer}/sources"}),createTaxId:rO({method:"POST",fullPath:"/v1/customers/{customer}/tax_ids"}),deleteDiscount:rO({method:"DELETE",fullPath:"/v1/customers/{customer}/discount"}),deleteSource:rO({method:"DELETE",fullPath:"/v1/customers/{customer}/sources/{id}"}),deleteTaxId:rO({method:"DELETE",fullPath:"/v1/customers/{customer}/tax_ids/{id}"}),listBalanceTransactions:rO({method:"GET",fullPath:"/v1/customers/{customer}/balance_transactions",methodType:"list"}),listCashBalanceTransactions:rO({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance_transactions",methodType:"list"}),listPaymentMethods:rO({method:"GET",fullPath:"/v1/customers/{customer}/payment_methods",methodType:"list"}),listSources:rO({method:"GET",fullPath:"/v1/customers/{customer}/sources",methodType:"list"}),listTaxIds:rO({method:"GET",fullPath:"/v1/customers/{customer}/tax_ids",methodType:"list"}),retrieveBalanceTransaction:rO({method:"GET",fullPath:"/v1/customers/{customer}/balance_transactions/{transaction}"}),retrieveCashBalance:rO({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance"}),retrieveCashBalanceTransaction:rO({method:"GET",fullPath:"/v1/customers/{customer}/cash_balance_transactions/{transaction}"}),retrievePaymentMethod:rO({method:"GET",fullPath:"/v1/customers/{customer}/payment_methods/{payment_method}"}),retrieveSource:rO({method:"GET",fullPath:"/v1/customers/{customer}/sources/{id}"}),retrieveTaxId:rO({method:"GET",fullPath:"/v1/customers/{customer}/tax_ids/{id}"}),search:rO({method:"GET",fullPath:"/v1/customers/search",methodType:"search"}),updateBalanceTransaction:rO({method:"POST",fullPath:"/v1/customers/{customer}/balance_transactions/{transaction}"}),updateCashBalance:rO({method:"POST",fullPath:"/v1/customers/{customer}/cash_balance"}),updateSource:rO({method:"POST",fullPath:"/v1/customers/{customer}/sources/{id}"}),verifySource:rO({method:"POST",fullPath:"/v1/customers/{customer}/sources/{id}/verify"})}),rw=K.method,rA=K.extend({retrieve:rw({method:"GET",fullPath:"/v1/disputes/{dispute}"}),update:rw({method:"POST",fullPath:"/v1/disputes/{dispute}"}),list:rw({method:"GET",fullPath:"/v1/disputes",methodType:"list"}),close:rw({method:"POST",fullPath:"/v1/disputes/{dispute}/close"})}),rG=K.method,rR=K.extend({create:rG({method:"POST",fullPath:"/v1/ephemeral_keys",validator:(e,t)=>{if(!t.headers||!t.headers["Stripe-Version"])throw Error("Passing apiVersion in a separate options hash is required to create an ephemeral key. See https://stripe.com/docs/api/versioning?lang=node")}}),del:rG({method:"DELETE",fullPath:"/v1/ephemeral_keys/{key}"})}),rC=K.method,rI=K.extend({retrieve:rC({method:"GET",fullPath:"/v1/events/{id}"}),list:rC({method:"GET",fullPath:"/v1/events",methodType:"list"})}),rk=K.method,rj=K.extend({retrieve:rk({method:"GET",fullPath:"/v1/exchange_rates/{rate_id}"}),list:rk({method:"GET",fullPath:"/v1/exchange_rates",methodType:"list"})}),rD=K.method,rN=K.extend({create:rD({method:"POST",fullPath:"/v1/file_links"}),retrieve:rD({method:"GET",fullPath:"/v1/file_links/{link}"}),update:rD({method:"POST",fullPath:"/v1/file_links/{link}"}),list:rD({method:"GET",fullPath:"/v1/file_links",methodType:"list"})}),rF=(e,t,r)=>{let o=(Math.round(1e16*Math.random())+Math.round(1e16*Math.random())).toString();r["Content-Type"]=`multipart/form-data; boundary=${o}`;let n=new TextEncoder,i=new Uint8Array(0),a=n.encode("\r\n");function s(e){let t=i,r=e instanceof Uint8Array?e:new Uint8Array(n.encode(e));(i=new Uint8Array(t.length+r.length+2)).set(t),i.set(r,t.length),i.set(a,i.length-2)}function l(e){return`"${e.replace(/"|"/g,"%22").replace(/\r\n|\r|\n/g," ")}"`}let u=function(e){let t={},r=(e,o)=>{Object.entries(e).forEach(([e,n])=>{let i=o?`${o}[${e}]`:e;if(function(e){let t=typeof e;return("function"===t||"object"===t)&&!!e}(n)){if(!(n instanceof Uint8Array)&&!Object.prototype.hasOwnProperty.call(n,"data"))return r(n,i);t[i]=n}else t[i]=String(n)})};return r(e,null),t}(t);for(let e in u){if(!Object.prototype.hasOwnProperty.call(u,e))continue;let t=u[e];s(`--${o}`),Object.prototype.hasOwnProperty.call(t,"data")?(s(`Content-Disposition: form-data; name=${l(e)}; filename=${l(t.name||"blob")}`),s(`Content-Type: ${t.type||"application/octet-stream"}`),s(""),s(t.data)):(s(`Content-Disposition: form-data; name=${l(e)}`),s(""),s(t))}return s(`--${o}--`),i},rM=K.method,rU=K.extend({create:rM({method:"POST",fullPath:"/v1/files",headers:{"Content-Type":"multipart/form-data"},host:"files.stripe.com"}),retrieve:rM({method:"GET",fullPath:"/v1/files/{file}"}),list:rM({method:"GET",fullPath:"/v1/files",methodType:"list"}),requestDataProcessor:function(e,t,r,o){if(t=t||{},"POST"!==e)return o(null,k(t));this._stripe._platformFunctions.tryBufferData(t).then(t=>o(null,rF(e,t,r))).catch(e=>o(e,null))}}),rq=K.method,rL=K.extend({create:rq({method:"POST",fullPath:"/v1/invoiceitems"}),retrieve:rq({method:"GET",fullPath:"/v1/invoiceitems/{invoiceitem}"}),update:rq({method:"POST",fullPath:"/v1/invoiceitems/{invoiceitem}"}),list:rq({method:"GET",fullPath:"/v1/invoiceitems",methodType:"list"}),del:rq({method:"DELETE",fullPath:"/v1/invoiceitems/{invoiceitem}"})}),rH=K.method,r$=K.extend({retrieve:rH({method:"GET",fullPath:"/v1/invoice_rendering_templates/{template}"}),list:rH({method:"GET",fullPath:"/v1/invoice_rendering_templates",methodType:"list"}),archive:rH({method:"POST",fullPath:"/v1/invoice_rendering_templates/{template}/archive"}),unarchive:rH({method:"POST",fullPath:"/v1/invoice_rendering_templates/{template}/unarchive"})}),rz=K.method,rB=K.extend({create:rz({method:"POST",fullPath:"/v1/invoices"}),retrieve:rz({method:"GET",fullPath:"/v1/invoices/{invoice}"}),update:rz({method:"POST",fullPath:"/v1/invoices/{invoice}"}),list:rz({method:"GET",fullPath:"/v1/invoices",methodType:"list"}),del:rz({method:"DELETE",fullPath:"/v1/invoices/{invoice}"}),addLines:rz({method:"POST",fullPath:"/v1/invoices/{invoice}/add_lines"}),createPreview:rz({method:"POST",fullPath:"/v1/invoices/create_preview"}),finalizeInvoice:rz({method:"POST",fullPath:"/v1/invoices/{invoice}/finalize"}),listLineItems:rz({method:"GET",fullPath:"/v1/invoices/{invoice}/lines",methodType:"list"}),listUpcomingLines:rz({method:"GET",fullPath:"/v1/invoices/upcoming/lines",methodType:"list"}),markUncollectible:rz({method:"POST",fullPath:"/v1/invoices/{invoice}/mark_uncollectible"}),pay:rz({method:"POST",fullPath:"/v1/invoices/{invoice}/pay"}),removeLines:rz({method:"POST",fullPath:"/v1/invoices/{invoice}/remove_lines"}),retrieveUpcoming:rz({method:"GET",fullPath:"/v1/invoices/upcoming"}),search:rz({method:"GET",fullPath:"/v1/invoices/search",methodType:"search"}),sendInvoice:rz({method:"POST",fullPath:"/v1/invoices/{invoice}/send"}),updateLines:rz({method:"POST",fullPath:"/v1/invoices/{invoice}/update_lines"}),updateLineItem:rz({method:"POST",fullPath:"/v1/invoices/{invoice}/lines/{line_item_id}"}),voidInvoice:rz({method:"POST",fullPath:"/v1/invoices/{invoice}/void"})}),rW=K.method,rK=K.extend({retrieve:rW({method:"GET",fullPath:"/v1/mandates/{mandate}"})}),rV=K.method,rJ="connect.stripe.com",rQ=K.extend({basePath:"/",authorizeUrl(e,t){e=e||{};let r="oauth/authorize";return(t=t||{}).express&&(r=`express/${r}`),e.response_type||(e.response_type="code"),e.client_id||(e.client_id=this._stripe.getClientId()),e.scope||(e.scope="read_write"),`https://${rJ}/${r}?${k(e)}`},token:rV({method:"POST",path:"oauth/token",host:rJ}),deauthorize(e,...t){return e.client_id||(e.client_id=this._stripe.getClientId()),rV({method:"POST",path:"oauth/deauthorize",host:rJ}).apply(this,[e,...t])}}),rX=K.method,rY=K.extend({create:rX({method:"POST",fullPath:"/v1/payment_intents"}),retrieve:rX({method:"GET",fullPath:"/v1/payment_intents/{intent}"}),update:rX({method:"POST",fullPath:"/v1/payment_intents/{intent}"}),list:rX({method:"GET",fullPath:"/v1/payment_intents",methodType:"list"}),applyCustomerBalance:rX({method:"POST",fullPath:"/v1/payment_intents/{intent}/apply_customer_balance"}),cancel:rX({method:"POST",fullPath:"/v1/payment_intents/{intent}/cancel"}),capture:rX({method:"POST",fullPath:"/v1/payment_intents/{intent}/capture"}),confirm:rX({method:"POST",fullPath:"/v1/payment_intents/{intent}/confirm"}),incrementAuthorization:rX({method:"POST",fullPath:"/v1/payment_intents/{intent}/increment_authorization"}),search:rX({method:"GET",fullPath:"/v1/payment_intents/search",methodType:"search"}),verifyMicrodeposits:rX({method:"POST",fullPath:"/v1/payment_intents/{intent}/verify_microdeposits"})}),rZ=K.method,r1=K.extend({create:rZ({method:"POST",fullPath:"/v1/payment_links"}),retrieve:rZ({method:"GET",fullPath:"/v1/payment_links/{payment_link}"}),update:rZ({method:"POST",fullPath:"/v1/payment_links/{payment_link}"}),list:rZ({method:"GET",fullPath:"/v1/payment_links",methodType:"list"}),listLineItems:rZ({method:"GET",fullPath:"/v1/payment_links/{payment_link}/line_items",methodType:"list"})}),r0=K.method,r8=K.extend({create:r0({method:"POST",fullPath:"/v1/payment_method_configurations"}),retrieve:r0({method:"GET",fullPath:"/v1/payment_method_configurations/{configuration}"}),update:r0({method:"POST",fullPath:"/v1/payment_method_configurations/{configuration}"}),list:r0({method:"GET",fullPath:"/v1/payment_method_configurations",methodType:"list"})}),r2=K.method,r5=K.extend({create:r2({method:"POST",fullPath:"/v1/payment_method_domains"}),retrieve:r2({method:"GET",fullPath:"/v1/payment_method_domains/{payment_method_domain}"}),update:r2({method:"POST",fullPath:"/v1/payment_method_domains/{payment_method_domain}"}),list:r2({method:"GET",fullPath:"/v1/payment_method_domains",methodType:"list"}),validate:r2({method:"POST",fullPath:"/v1/payment_method_domains/{payment_method_domain}/validate"})}),r3=K.method,r6=K.extend({create:r3({method:"POST",fullPath:"/v1/payment_methods"}),retrieve:r3({method:"GET",fullPath:"/v1/payment_methods/{payment_method}"}),update:r3({method:"POST",fullPath:"/v1/payment_methods/{payment_method}"}),list:r3({method:"GET",fullPath:"/v1/payment_methods",methodType:"list"}),attach:r3({method:"POST",fullPath:"/v1/payment_methods/{payment_method}/attach"}),detach:r3({method:"POST",fullPath:"/v1/payment_methods/{payment_method}/detach"})}),r4=K.method,r7=K.extend({create:r4({method:"POST",fullPath:"/v1/payouts"}),retrieve:r4({method:"GET",fullPath:"/v1/payouts/{payout}"}),update:r4({method:"POST",fullPath:"/v1/payouts/{payout}"}),list:r4({method:"GET",fullPath:"/v1/payouts",methodType:"list"}),cancel:r4({method:"POST",fullPath:"/v1/payouts/{payout}/cancel"}),reverse:r4({method:"POST",fullPath:"/v1/payouts/{payout}/reverse"})}),r9=K.method,oe=K.extend({create:r9({method:"POST",fullPath:"/v1/plans"}),retrieve:r9({method:"GET",fullPath:"/v1/plans/{plan}"}),update:r9({method:"POST",fullPath:"/v1/plans/{plan}"}),list:r9({method:"GET",fullPath:"/v1/plans",methodType:"list"}),del:r9({method:"DELETE",fullPath:"/v1/plans/{plan}"})}),ot=K.method,or=K.extend({create:ot({method:"POST",fullPath:"/v1/prices"}),retrieve:ot({method:"GET",fullPath:"/v1/prices/{price}"}),update:ot({method:"POST",fullPath:"/v1/prices/{price}"}),list:ot({method:"GET",fullPath:"/v1/prices",methodType:"list"}),search:ot({method:"GET",fullPath:"/v1/prices/search",methodType:"search"})}),oo=K.method,on=K.extend({create:oo({method:"POST",fullPath:"/v1/products"}),retrieve:oo({method:"GET",fullPath:"/v1/products/{id}"}),update:oo({method:"POST",fullPath:"/v1/products/{id}"}),list:oo({method:"GET",fullPath:"/v1/products",methodType:"list"}),del:oo({method:"DELETE",fullPath:"/v1/products/{id}"}),createFeature:oo({method:"POST",fullPath:"/v1/products/{product}/features"}),deleteFeature:oo({method:"DELETE",fullPath:"/v1/products/{product}/features/{id}"}),listFeatures:oo({method:"GET",fullPath:"/v1/products/{product}/features",methodType:"list"}),retrieveFeature:oo({method:"GET",fullPath:"/v1/products/{product}/features/{id}"}),search:oo({method:"GET",fullPath:"/v1/products/search",methodType:"search"})}),oi=K.method,oa=K.extend({create:oi({method:"POST",fullPath:"/v1/promotion_codes"}),retrieve:oi({method:"GET",fullPath:"/v1/promotion_codes/{promotion_code}"}),update:oi({method:"POST",fullPath:"/v1/promotion_codes/{promotion_code}"}),list:oi({method:"GET",fullPath:"/v1/promotion_codes",methodType:"list"})}),os=K.method,ol=K.extend({create:os({method:"POST",fullPath:"/v1/quotes"}),retrieve:os({method:"GET",fullPath:"/v1/quotes/{quote}"}),update:os({method:"POST",fullPath:"/v1/quotes/{quote}"}),list:os({method:"GET",fullPath:"/v1/quotes",methodType:"list"}),accept:os({method:"POST",fullPath:"/v1/quotes/{quote}/accept"}),cancel:os({method:"POST",fullPath:"/v1/quotes/{quote}/cancel"}),finalizeQuote:os({method:"POST",fullPath:"/v1/quotes/{quote}/finalize"}),listComputedUpfrontLineItems:os({method:"GET",fullPath:"/v1/quotes/{quote}/computed_upfront_line_items",methodType:"list"}),listLineItems:os({method:"GET",fullPath:"/v1/quotes/{quote}/line_items",methodType:"list"}),pdf:os({method:"GET",fullPath:"/v1/quotes/{quote}/pdf",host:"files.stripe.com",streaming:!0})}),ou=K.method,oc=K.extend({create:ou({method:"POST",fullPath:"/v1/refunds"}),retrieve:ou({method:"GET",fullPath:"/v1/refunds/{refund}"}),update:ou({method:"POST",fullPath:"/v1/refunds/{refund}"}),list:ou({method:"GET",fullPath:"/v1/refunds",methodType:"list"}),cancel:ou({method:"POST",fullPath:"/v1/refunds/{refund}/cancel"})}),oh=K.method,od=K.extend({retrieve:oh({method:"GET",fullPath:"/v1/reviews/{review}"}),list:oh({method:"GET",fullPath:"/v1/reviews",methodType:"list"}),approve:oh({method:"POST",fullPath:"/v1/reviews/{review}/approve"})}),op=K.method,om=K.extend({list:op({method:"GET",fullPath:"/v1/setup_attempts",methodType:"list"})}),of=K.method,oy=K.extend({create:of({method:"POST",fullPath:"/v1/setup_intents"}),retrieve:of({method:"GET",fullPath:"/v1/setup_intents/{intent}"}),update:of({method:"POST",fullPath:"/v1/setup_intents/{intent}"}),list:of({method:"GET",fullPath:"/v1/setup_intents",methodType:"list"}),cancel:of({method:"POST",fullPath:"/v1/setup_intents/{intent}/cancel"}),confirm:of({method:"POST",fullPath:"/v1/setup_intents/{intent}/confirm"}),verifyMicrodeposits:of({method:"POST",fullPath:"/v1/setup_intents/{intent}/verify_microdeposits"})}),ov=K.method,oP=K.extend({create:ov({method:"POST",fullPath:"/v1/shipping_rates"}),retrieve:ov({method:"GET",fullPath:"/v1/shipping_rates/{shipping_rate_token}"}),update:ov({method:"POST",fullPath:"/v1/shipping_rates/{shipping_rate_token}"}),list:ov({method:"GET",fullPath:"/v1/shipping_rates",methodType:"list"})}),og=K.method,oT=K.extend({create:og({method:"POST",fullPath:"/v1/sources"}),retrieve:og({method:"GET",fullPath:"/v1/sources/{source}"}),update:og({method:"POST",fullPath:"/v1/sources/{source}"}),listSourceTransactions:og({method:"GET",fullPath:"/v1/sources/{source}/source_transactions",methodType:"list"}),verify:og({method:"POST",fullPath:"/v1/sources/{source}/verify"})}),o_=K.method,oE=K.extend({create:o_({method:"POST",fullPath:"/v1/subscription_items"}),retrieve:o_({method:"GET",fullPath:"/v1/subscription_items/{item}"}),update:o_({method:"POST",fullPath:"/v1/subscription_items/{item}"}),list:o_({method:"GET",fullPath:"/v1/subscription_items",methodType:"list"}),del:o_({method:"DELETE",fullPath:"/v1/subscription_items/{item}"}),createUsageRecord:o_({method:"POST",fullPath:"/v1/subscription_items/{subscription_item}/usage_records"}),listUsageRecordSummaries:o_({method:"GET",fullPath:"/v1/subscription_items/{subscription_item}/usage_record_summaries",methodType:"list"})}),ob=K.method,oS=K.extend({create:ob({method:"POST",fullPath:"/v1/subscription_schedules"}),retrieve:ob({method:"GET",fullPath:"/v1/subscription_schedules/{schedule}"}),update:ob({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}"}),list:ob({method:"GET",fullPath:"/v1/subscription_schedules",methodType:"list"}),cancel:ob({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}/cancel"}),release:ob({method:"POST",fullPath:"/v1/subscription_schedules/{schedule}/release"})}),oO=K.method,ox=K.extend({create:oO({method:"POST",fullPath:"/v1/subscriptions"}),retrieve:oO({method:"GET",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),update:oO({method:"POST",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),list:oO({method:"GET",fullPath:"/v1/subscriptions",methodType:"list"}),cancel:oO({method:"DELETE",fullPath:"/v1/subscriptions/{subscription_exposed_id}"}),deleteDiscount:oO({method:"DELETE",fullPath:"/v1/subscriptions/{subscription_exposed_id}/discount"}),resume:oO({method:"POST",fullPath:"/v1/subscriptions/{subscription}/resume"}),search:oO({method:"GET",fullPath:"/v1/subscriptions/search",methodType:"search"})}),ow=K.method,oA=K.extend({retrieve:ow({method:"GET",fullPath:"/v1/tax_codes/{id}"}),list:ow({method:"GET",fullPath:"/v1/tax_codes",methodType:"list"})}),oG=K.method,oR=K.extend({create:oG({method:"POST",fullPath:"/v1/tax_ids"}),retrieve:oG({method:"GET",fullPath:"/v1/tax_ids/{id}"}),list:oG({method:"GET",fullPath:"/v1/tax_ids",methodType:"list"}),del:oG({method:"DELETE",fullPath:"/v1/tax_ids/{id}"})}),oC=K.method,oI=K.extend({create:oC({method:"POST",fullPath:"/v1/tax_rates"}),retrieve:oC({method:"GET",fullPath:"/v1/tax_rates/{tax_rate}"}),update:oC({method:"POST",fullPath:"/v1/tax_rates/{tax_rate}"}),list:oC({method:"GET",fullPath:"/v1/tax_rates",methodType:"list"})}),ok=K.method,oj=K.extend({create:ok({method:"POST",fullPath:"/v1/tokens"}),retrieve:ok({method:"GET",fullPath:"/v1/tokens/{token}"})}),oD=K.method,oN=K.extend({create:oD({method:"POST",fullPath:"/v1/topups"}),retrieve:oD({method:"GET",fullPath:"/v1/topups/{topup}"}),update:oD({method:"POST",fullPath:"/v1/topups/{topup}"}),list:oD({method:"GET",fullPath:"/v1/topups",methodType:"list"}),cancel:oD({method:"POST",fullPath:"/v1/topups/{topup}/cancel"})}),oF=K.method,oM=K.extend({create:oF({method:"POST",fullPath:"/v1/transfers"}),retrieve:oF({method:"GET",fullPath:"/v1/transfers/{transfer}"}),update:oF({method:"POST",fullPath:"/v1/transfers/{transfer}"}),list:oF({method:"GET",fullPath:"/v1/transfers",methodType:"list"}),createReversal:oF({method:"POST",fullPath:"/v1/transfers/{id}/reversals"}),listReversals:oF({method:"GET",fullPath:"/v1/transfers/{id}/reversals",methodType:"list"}),retrieveReversal:oF({method:"GET",fullPath:"/v1/transfers/{transfer}/reversals/{id}"}),updateReversal:oF({method:"POST",fullPath:"/v1/transfers/{transfer}/reversals/{id}"})}),oU=K.method,oq=K.extend({create:oU({method:"POST",fullPath:"/v1/webhook_endpoints"}),retrieve:oU({method:"GET",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"}),update:oU({method:"POST",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"}),list:oU({method:"GET",fullPath:"/v1/webhook_endpoints",methodType:"list"}),del:oU({method:"DELETE",fullPath:"/v1/webhook_endpoints/{webhook_endpoint}"})}),oL=Q("apps",{Secrets:tA}),oH=Q("billing",{Alerts:er,MeterEventAdjustments:ez,MeterEvents:eW,Meters:eV}),o$=Q("billingPortal",{Configurations:ey,Sessions:tR}),oz=Q("checkout",{Sessions:tI}),oB=Q("climate",{Orders:eQ,Products:tr,Suppliers:tM}),oW=Q("entitlements",{ActiveEntitlements:ee,Features:ej}),oK=Q("financialConnections",{Accounts:Y,Sessions:tj,Transactions:tV}),oV=Q("forwarding",{Requests:tS}),oJ=Q("identity",{VerificationReports:t6,VerificationSessions:t7}),oQ=Q("issuing",{Authorizations:ea,Cardholders:ec,Cards:em,Disputes:eR,PersonalizationDesigns:e7,PhysicalBundles:te,Tokens:tH,Transactions:tQ}),oX=Q("radar",{EarlyFraudWarnings:eI,ValueListItems:t8,ValueLists:t5}),oY=Q("reporting",{ReportRuns:tT,ReportTypes:tE}),oZ=Q("sigma",{ScheduledQueryRuns:tx}),o1=Q("tax",{Calculations:el,Registrations:tP,Settings:tN,Transactions:tY}),o0=Q("terminal",{Configurations:eP,ConnectionTokens:eE,Locations:eH,Readers:ta}),o8=Q("testHelpers",{ConfirmationTokens:eT,Customers:ex,Refunds:ty,TestClocks:tq,Issuing:Q("issuing",{Authorizations:en,Cards:ed,PersonalizationDesigns:e6,Transactions:tW}),Terminal:Q("terminal",{Readers:tn}),Treasury:Q("treasury",{InboundTransfers:eM,OutboundPayments:eY,OutboundTransfers:e8,ReceivedCredits:tl,ReceivedDebits:td})}),o2=Q("treasury",{CreditReversals:eS,DebitReversals:eA,FinancialAccounts:eN,InboundTransfers:eq,OutboundPayments:e1,OutboundTransfers:e5,ReceivedCredits:tc,ReceivedDebits:tm,TransactionEntries:tz,Transactions:t1}),o5="api.stripe.com",o3="/v1/",o6="2024-06-20",o4=["name","version","url","partner_id"],o7=["apiVersion","typescript","maxNetworkRetries","httpAgent","httpClient","timeout","host","port","protocol","telemetry","appInfo","stripeAccount"],o9=e=>new q(e,K.MAX_BUFFERED_REQUEST_METRICS),ne=function(e,t=o9){function r(t=e){return V(t)}function s(n,i={}){if(!(this instanceof s))return new s(n,i);let a=this._getPropsFromConfig(i);this._platformFunctions=e,Object.defineProperty(this,"_emitter",{value:this._platformFunctions.createEmitter(),enumerable:!1,configurable:!1,writable:!1}),this.VERSION=s.PACKAGE_VERSION,this.on=this._emitter.on.bind(this._emitter),this.once=this._emitter.once.bind(this._emitter),this.off=this._emitter.removeListener.bind(this._emitter);let l=a.httpAgent||null;this._api={auth:null,host:a.host||o5,port:a.port||"443",protocol:a.protocol||"https",basePath:o3,version:a.apiVersion||o6,timeout:U("timeout",a.timeout,8e4),maxNetworkRetries:U("maxNetworkRetries",a.maxNetworkRetries,1),agent:l,httpClient:a.httpClient||(l?this._platformFunctions.createNodeHttpClient(l):this._platformFunctions.createDefaultHttpClient()),dev:!1,stripeAccount:a.stripeAccount||null};let u=a.typescript||!1;u!==s.USER_AGENT.typescript&&(s.USER_AGENT.typescript=u),a.appInfo&&this._setAppInfo(a.appInfo),this._prepResources(),this._setApiKey(n),this.errors=o,this.webhooks=r(),this._prevRequestMetrics=[],this._enableTelemetry=!1!==a.telemetry,this._requestSender=t(this),this.StripeResource=s.StripeResource}return s.PACKAGE_VERSION="16.12.0",s.USER_AGENT=Object.assign({bindings_version:s.PACKAGE_VERSION,lang:"node",publisher:"stripe",uname:null,typescript:!1},"undefined"==typeof process?{}:{lang_version:process.version,platform:process.platform}),s.StripeResource=K,s.resources=n,s.HttpClient=i,s.HttpClientResponse=a,s.CryptoProvider=u,s.webhooks=Object.assign(r,V(e)),s.errors=o,s.createNodeHttpClient=e.createNodeHttpClient,s.createFetchHttpClient=e.createFetchHttpClient,s.createNodeCryptoProvider=e.createNodeCryptoProvider,s.createSubtleCryptoProvider=e.createSubtleCryptoProvider,s.prototype={_appInfo:void 0,on:null,off:null,once:null,VERSION:null,StripeResource:null,webhooks:null,errors:null,_api:null,_prevRequestMetrics:null,_emitter:null,_enableTelemetry:null,_requestSender:null,_platformFunctions:null,_setApiKey(e){e&&this._setApiField("auth",`Bearer ${e}`)},_setAppInfo(e){if(e&&"object"!=typeof e)throw Error("AppInfo must be an object.");if(e&&!e.name)throw Error("AppInfo.name is required");e=e||{},this._appInfo=o4.reduce((t,r)=>("string"==typeof e[r]&&((t=t||{})[r]=e[r]),t),void 0)},_setApiField(e,t){this._api[e]=t},getApiField(e){return this._api[e]},setClientId(e){this._clientId=e},getClientId(){return this._clientId},getConstant:e=>{switch(e){case"DEFAULT_HOST":return o5;case"DEFAULT_PORT":return"443";case"DEFAULT_BASE_PATH":return o3;case"DEFAULT_API_VERSION":return o6;case"DEFAULT_TIMEOUT":return 8e4;case"MAX_NETWORK_RETRY_DELAY_SEC":return 2;case"INITIAL_NETWORK_RETRY_DELAY_SEC":return .5}return s[e]},getMaxNetworkRetries(){return this.getApiField("maxNetworkRetries")},_setApiNumberField(e,t,r){let o=U(e,t,r);this._setApiField(e,o)},getMaxNetworkRetryDelay:()=>2,getInitialNetworkRetryDelay:()=>.5,getClientUserAgent(e){return this.getClientUserAgentSeeded(s.USER_AGENT,e)},getClientUserAgentSeeded(e,t){this._platformFunctions.getUname().then(r=>{var o;let n={};for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[t]=encodeURIComponent(null!==(o=e[t])&&void 0!==o?o:"null"));n.uname=encodeURIComponent(r||"UNKNOWN");let i=this.getApiField("httpClient");i&&(n.httplib=encodeURIComponent(i.getClientName())),this._appInfo&&(n.application=this._appInfo),t(JSON.stringify(n))})},getAppInfoAsString(){if(!this._appInfo)return"";let e=this._appInfo.name;return this._appInfo.version&&(e+=`/${this._appInfo.version}`),this._appInfo.url&&(e+=` (${this._appInfo.url})`),e},getTelemetryEnabled(){return this._enableTelemetry},_prepResources(){for(let e in n)Object.prototype.hasOwnProperty.call(n,e)&&(this["OAuth"===e?"oauth":e[0].toLowerCase()+e.substring(1)]=new n[e](this))},_getPropsFromConfig(e){if(!e)return{};let t="string"==typeof e;if(!(e===Object(e)&&!Array.isArray(e))&&!t)throw Error("Config must either be an object or a string");if(t)return{apiVersion:e};if(Object.keys(e).filter(e=>!o7.includes(e)).length>0)throw Error(`Config object may only contain the following: ${o7.join(", ")}`);return e}},s}(new y)}}]);
//# sourceMappingURL=735.js.map