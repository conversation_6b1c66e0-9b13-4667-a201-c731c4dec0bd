exports.id=777,exports.ids=[777],exports.modules={73930:(e,a,t)=>{e.exports={parallel:t(37638),serial:t(70545),serialOrdered:t(57779)}},89774:e=>{e.exports=function(e){Object.keys(e.jobs).forEach(a.bind(e)),e.jobs={}};function a(e){"function"==typeof this.jobs[e]&&this.jobs[e]()}},1281:(e,a,t)=>{var n=t(19336);e.exports=function(e){var a=!1;return n(function(){a=!0}),function(t,i){a?e(t,i):n(function(){e(t,i)})}}},19336:e=>{e.exports=function(e){var a="function"==typeof setImmediate?setImmediate:"object"==typeof process&&"function"==typeof process.nextTick?process.nextTick:null;a?a(e):setTimeout(e,0)}},72534:(e,a,t)=>{var n=t(1281),i=t(89774);e.exports=function(e,a,t,s){var o,r,c=t.keyedList?t.keyedList[t.index]:t.index;t.jobs[c]=(o=e[c],r=function(e,a){c in t.jobs&&(delete t.jobs[c],e?i(t):t.results[c]=a,s(e,t.results))},2==a.length?a(o,n(r)):a(o,c,n(r)))}},66807:e=>{e.exports=function(e,a){var t=!Array.isArray(e),n={index:0,keyedList:t||a?Object.keys(e):null,jobs:{},results:t?{}:[],size:t?Object.keys(e).length:e.length};return a&&n.keyedList.sort(t?a:function(t,n){return a(e[t],e[n])}),n}},19867:(e,a,t)=>{var n=t(89774),i=t(1281);e.exports=function(e){Object.keys(this.jobs).length&&(this.index=this.size,n(this),i(e)(null,this.results))}},37638:(e,a,t)=>{var n=t(72534),i=t(66807),s=t(19867);e.exports=function(e,a,t){for(var o=i(e);o.index<(o.keyedList||e).length;)n(e,a,o,function(e,a){if(e){t(e,a);return}if(0===Object.keys(o.jobs).length){t(null,o.results);return}}),o.index++;return s.bind(o,t)}},70545:(e,a,t)=>{var n=t(57779);e.exports=function(e,a,t){return n(e,a,null,t)}},57779:(e,a,t)=>{var n=t(72534),i=t(66807),s=t(19867);function o(e,a){return e<a?-1:e>a?1:0}e.exports=function(e,a,t,o){var r=i(e,t);return n(e,a,r,function t(i,s){if(i){o(i,s);return}if(r.index++,r.index<(r.keyedList||e).length){n(e,a,r,t);return}o(null,r.results)}),s.bind(r,o)},e.exports.ascending=o,e.exports.descending=function(e,a){return -1*o(e,a)}},38726:(e,a,t)=>{var n=t(21764),i=t(76162).Stream,s=t(4130);function o(){this.writable=!1,this.readable=!0,this.dataSize=0,this.maxDataSize=2097152,this.pauseStreams=!0,this._released=!1,this._streams=[],this._currentStream=null,this._insideLoop=!1,this._pendingNext=!1}e.exports=o,n.inherits(o,i),o.create=function(e){var a=new this;for(var t in e=e||{})a[t]=e[t];return a},o.isStreamLike=function(e){return"function"!=typeof e&&"string"!=typeof e&&"boolean"!=typeof e&&"number"!=typeof e&&!Buffer.isBuffer(e)},o.prototype.append=function(e){if(o.isStreamLike(e)){if(!(e instanceof s)){var a=s.create(e,{maxDataSize:1/0,pauseStream:this.pauseStreams});e.on("data",this._checkDataSize.bind(this)),e=a}this._handleErrors(e),this.pauseStreams&&e.pause()}return this._streams.push(e),this},o.prototype.pipe=function(e,a){return i.prototype.pipe.call(this,e,a),this.resume(),e},o.prototype._getNext=function(){if(this._currentStream=null,this._insideLoop){this._pendingNext=!0;return}this._insideLoop=!0;try{do this._pendingNext=!1,this._realGetNext();while(this._pendingNext)}finally{this._insideLoop=!1}},o.prototype._realGetNext=function(){var e=this._streams.shift();if(void 0===e){this.end();return}if("function"!=typeof e){this._pipeNext(e);return}e((function(e){o.isStreamLike(e)&&(e.on("data",this._checkDataSize.bind(this)),this._handleErrors(e)),this._pipeNext(e)}).bind(this))},o.prototype._pipeNext=function(e){if(this._currentStream=e,o.isStreamLike(e)){e.on("end",this._getNext.bind(this)),e.pipe(this,{end:!1});return}this.write(e),this._getNext()},o.prototype._handleErrors=function(e){var a=this;e.on("error",function(e){a._emitError(e)})},o.prototype.write=function(e){this.emit("data",e)},o.prototype.pause=function(){this.pauseStreams&&(this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.pause&&this._currentStream.pause(),this.emit("pause"))},o.prototype.resume=function(){this._released||(this._released=!0,this.writable=!0,this._getNext()),this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.resume&&this._currentStream.resume(),this.emit("resume")},o.prototype.end=function(){this._reset(),this.emit("end")},o.prototype.destroy=function(){this._reset(),this.emit("close")},o.prototype._reset=function(){this.writable=!1,this._streams=[],this._currentStream=null},o.prototype._checkDataSize=function(){if(this._updateDataSize(),!(this.dataSize<=this.maxDataSize)){var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this._emitError(Error(e))}},o.prototype._updateDataSize=function(){this.dataSize=0;var e=this;this._streams.forEach(function(a){a.dataSize&&(e.dataSize+=a.dataSize)}),this._currentStream&&this._currentStream.dataSize&&(this.dataSize+=this._currentStream.dataSize)},o.prototype._emitError=function(e){this._reset(),this.emit("error",e)}},39662:(e,a,t)=>{a.formatArgs=function(a){if(a[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+a[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let t="color: "+this.color;a.splice(1,0,t,"color: inherit");let n=0,i=0;a[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(n++,"%c"===e&&(i=n))}),a.splice(i,0,t)},a.save=function(e){try{e?a.storage.setItem("debug",e):a.storage.removeItem("debug")}catch(e){}},a.load=function(){let e;try{e=a.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},a.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},a.storage=function(){try{return localStorage}catch(e){}}(),a.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),a.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],a.log=console.debug||console.log||(()=>{}),e.exports=t(47507)(a);let{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},47507:(e,a,t)=>{e.exports=function(e){function a(e){let t,i,s;let o=null;function r(...e){if(!r.enabled)return;let n=Number(new Date),i=n-(t||n);r.diff=i,r.prev=t,r.curr=n,t=n,e[0]=a.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let s=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(t,n)=>{if("%%"===t)return"%";s++;let i=a.formatters[n];if("function"==typeof i){let a=e[s];t=i.call(r,a),e.splice(s,1),s--}return t}),a.formatArgs.call(r,e),(r.log||a.log).apply(r,e)}return r.namespace=e,r.useColors=a.useColors(),r.color=a.selectColor(e),r.extend=n,r.destroy=a.destroy,Object.defineProperty(r,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(i!==a.namespaces&&(i=a.namespaces,s=a.enabled(e)),s),set:e=>{o=e}}),"function"==typeof a.init&&a.init(r),r}function n(e,t){let n=a(this.namespace+(void 0===t?":":t)+e);return n.log=this.log,n}function i(e,a){let t=0,n=0,i=-1,s=0;for(;t<e.length;)if(n<a.length&&(a[n]===e[t]||"*"===a[n]))"*"===a[n]?(i=n,s=t):t++,n++;else{if(-1===i)return!1;n=i+1,t=++s}for(;n<a.length&&"*"===a[n];)n++;return n===a.length}return a.debug=a,a.default=a,a.coerce=function(e){return e instanceof Error?e.stack||e.message:e},a.disable=function(){let e=[...a.names,...a.skips.map(e=>"-"+e)].join(",");return a.enable(""),e},a.enable=function(e){for(let t of(a.save(e),a.namespaces=e,a.names=[],a.skips=[],("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean)))"-"===t[0]?a.skips.push(t.slice(1)):a.names.push(t)},a.enabled=function(e){for(let t of a.skips)if(i(e,t))return!1;for(let t of a.names)if(i(e,t))return!0;return!1},a.humanize=t(87914),a.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(t=>{a[t]=e[t]}),a.names=[],a.skips=[],a.formatters={},a.selectColor=function(e){let t=0;for(let a=0;a<e.length;a++)t=(t<<5)-t+e.charCodeAt(a)|0;return a.colors[Math.abs(t)%a.colors.length]},a.enable(a.load()),a}},8589:(e,a,t)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=t(39662):e.exports=t(56027)},56027:(e,a,t)=>{let n=t(74175),i=t(21764);a.init=function(e){e.inspectOpts={};let t=Object.keys(a.inspectOpts);for(let n=0;n<t.length;n++)e.inspectOpts[t[n]]=a.inspectOpts[t[n]]},a.log=function(...e){return process.stderr.write(i.formatWithOptions(a.inspectOpts,...e)+"\n")},a.formatArgs=function(t){let{namespace:n,useColors:i}=this;if(i){let a=this.color,i="\x1b[3"+(a<8?a:"8;5;"+a),s=`  ${i};1m${n} \u001B[0m`;t[0]=s+t[0].split("\n").join("\n"+s),t.push(i+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else t[0]=(a.inspectOpts.hideDate?"":new Date().toISOString()+" ")+n+" "+t[0]},a.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},a.load=function(){return process.env.DEBUG},a.useColors=function(){return"colors"in a.inspectOpts?!!a.inspectOpts.colors:n.isatty(process.stderr.fd)},a.destroy=i.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),a.colors=[6,2,3,4,5,1];try{let e=t(67495);e&&(e.stderr||e).level>=2&&(a.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}a.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,a)=>{let t=a.substring(6).toLowerCase().replace(/_([a-z])/g,(e,a)=>a.toUpperCase()),n=process.env[a];return n=!!/^(yes|on|true|enabled)$/i.test(n)||!/^(no|off|false|disabled)$/i.test(n)&&("null"===n?null:Number(n)),e[t]=n,e},{}),e.exports=t(47507)(a);let{formatters:s}=e.exports;s.o=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},s.O=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts)}},4130:(e,a,t)=>{var n=t(76162).Stream,i=t(21764);function s(){this.source=null,this.dataSize=0,this.maxDataSize=1048576,this.pauseStream=!0,this._maxDataSizeExceeded=!1,this._released=!1,this._bufferedEvents=[]}e.exports=s,i.inherits(s,n),s.create=function(e,a){var t=new this;for(var n in a=a||{})t[n]=a[n];t.source=e;var i=e.emit;return e.emit=function(){return t._handleEmit(arguments),i.apply(e,arguments)},e.on("error",function(){}),t.pauseStream&&e.pause(),t},Object.defineProperty(s.prototype,"readable",{configurable:!0,enumerable:!0,get:function(){return this.source.readable}}),s.prototype.setEncoding=function(){return this.source.setEncoding.apply(this.source,arguments)},s.prototype.resume=function(){this._released||this.release(),this.source.resume()},s.prototype.pause=function(){this.source.pause()},s.prototype.release=function(){this._released=!0,this._bufferedEvents.forEach((function(e){this.emit.apply(this,e)}).bind(this)),this._bufferedEvents=[]},s.prototype.pipe=function(){var e=n.prototype.pipe.apply(this,arguments);return this.resume(),e},s.prototype._handleEmit=function(e){if(this._released){this.emit.apply(this,e);return}"data"===e[0]&&(this.dataSize+=e[1].length,this._checkIfMaxDataSizeExceeded()),this._bufferedEvents.push(e)},s.prototype._checkIfMaxDataSizeExceeded=function(){if(!this._maxDataSizeExceeded&&!(this.dataSize<=this.maxDataSize)){this._maxDataSizeExceeded=!0;var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this.emit("error",Error(e))}}},59057:(e,a,t)=>{var n;e.exports=function(){if(!n){try{n=t(8589)("follow-redirects")}catch(e){}"function"!=typeof n&&(n=function(){})}n.apply(null,arguments)}},19831:(e,a,t)=>{var n=t(17360),i=n.URL,s=t(32615),o=t(35240),r=t(76162).Writable,c=t(27790),p=t(59057);!function(){var e="undefined"!=typeof process,a="undefined"!=typeof window&&"undefined"!=typeof document,t=C(Error.captureStackTrace);e||!a&&t||console.warn("The follow-redirects package should be excluded from browser builds.")}();var l=!1;try{c(new i(""))}catch(e){l="ERR_INVALID_URL"===e.code}var u=["auth","host","hostname","href","path","pathname","port","protocol","query","search","hash"],d=["abort","aborted","connect","error","socket","timeout"],m=Object.create(null);d.forEach(function(e){m[e]=function(a,t,n){this._redirectable.emit(e,a,t,n)}});var f=O("ERR_INVALID_URL","Invalid URL",TypeError),h=O("ERR_FR_REDIRECTION_FAILURE","Redirected request failed"),x=O("ERR_FR_TOO_MANY_REDIRECTS","Maximum number of redirects exceeded",h),v=O("ERR_FR_MAX_BODY_LENGTH_EXCEEDED","Request body larger than maxBodyLength limit"),b=O("ERR_STREAM_WRITE_AFTER_END","write after end"),g=r.prototype.destroy||_;function y(e,a){r.call(this),this._sanitizeOptions(e),this._options=e,this._ended=!1,this._ending=!1,this._redirectCount=0,this._redirects=[],this._requestBodyLength=0,this._requestBodyBuffers=[],a&&this.on("response",a);var t=this;this._onNativeResponse=function(e){try{t._processResponse(e)}catch(e){t.emit("error",e instanceof h?e:new h({cause:e}))}},this._performRequest()}function w(e){var a={maxRedirects:21,maxBodyLength:10485760},t={};return Object.keys(e).forEach(function(n){var s=n+":",o=t[s]=e[n],r=a[n]=Object.create(o);Object.defineProperties(r,{request:{value:function(e,n,o){var r;return(r=e,i&&r instanceof i)?e=E(e):F(e)?e=E(j(e)):(o=n,n=k(e),e={protocol:s}),C(n)&&(o=n,n=null),(n=Object.assign({maxRedirects:a.maxRedirects,maxBodyLength:a.maxBodyLength},e,n)).nativeProtocols=t,F(n.host)||F(n.hostname)||(n.hostname="::1"),c.equal(n.protocol,s,"protocol mismatch"),p("options",n),new y(n,o)},configurable:!0,enumerable:!0,writable:!0},get:{value:function(e,a,t){var n=r.request(e,a,t);return n.end(),n},configurable:!0,enumerable:!0,writable:!0}})}),a}function _(){}function j(e){var a;if(l)a=new i(e);else if(!F((a=k(n.parse(e))).protocol))throw new f({input:e});return a}function k(e){if(/^\[/.test(e.hostname)&&!/^\[[:0-9a-f]+\]$/i.test(e.hostname)||/^\[/.test(e.host)&&!/^\[[:0-9a-f]+\](:\d+)?$/i.test(e.host))throw new f({input:e.href||e});return e}function E(e,a){var t=a||{};for(var n of u)t[n]=e[n];return t.hostname.startsWith("[")&&(t.hostname=t.hostname.slice(1,-1)),""!==t.port&&(t.port=Number(t.port)),t.path=t.search?t.pathname+t.search:t.pathname,t}function S(e,a){var t;for(var n in a)e.test(n)&&(t=a[n],delete a[n]);return null==t?void 0:String(t).trim()}function O(e,a,t){function n(t){C(Error.captureStackTrace)&&Error.captureStackTrace(this,this.constructor),Object.assign(this,t||{}),this.code=e,this.message=this.cause?a+": "+this.cause.message:a}return n.prototype=new(t||Error),Object.defineProperties(n.prototype,{constructor:{value:n,enumerable:!1},name:{value:"Error ["+e+"]",enumerable:!1}}),n}function T(e,a){for(var t of d)e.removeListener(t,m[t]);e.on("error",_),e.destroy(a)}function F(e){return"string"==typeof e||e instanceof String}function C(e){return"function"==typeof e}y.prototype=Object.create(r.prototype),y.prototype.abort=function(){T(this._currentRequest),this._currentRequest.abort(),this.emit("abort")},y.prototype.destroy=function(e){return T(this._currentRequest,e),g.call(this,e),this},y.prototype.write=function(e,a,t){if(this._ending)throw new b;if(!F(e)&&!("object"==typeof e&&"length"in e))throw TypeError("data should be a string, Buffer or Uint8Array");if(C(a)&&(t=a,a=null),0===e.length){t&&t();return}this._requestBodyLength+e.length<=this._options.maxBodyLength?(this._requestBodyLength+=e.length,this._requestBodyBuffers.push({data:e,encoding:a}),this._currentRequest.write(e,a,t)):(this.emit("error",new v),this.abort())},y.prototype.end=function(e,a,t){if(C(e)?(t=e,e=a=null):C(a)&&(t=a,a=null),e){var n=this,i=this._currentRequest;this.write(e,a,function(){n._ended=!0,i.end(null,null,t)}),this._ending=!0}else this._ended=this._ending=!0,this._currentRequest.end(null,null,t)},y.prototype.setHeader=function(e,a){this._options.headers[e]=a,this._currentRequest.setHeader(e,a)},y.prototype.removeHeader=function(e){delete this._options.headers[e],this._currentRequest.removeHeader(e)},y.prototype.setTimeout=function(e,a){var t=this;function n(a){a.setTimeout(e),a.removeListener("timeout",a.destroy),a.addListener("timeout",a.destroy)}function i(a){t._timeout&&clearTimeout(t._timeout),t._timeout=setTimeout(function(){t.emit("timeout"),s()},e),n(a)}function s(){t._timeout&&(clearTimeout(t._timeout),t._timeout=null),t.removeListener("abort",s),t.removeListener("error",s),t.removeListener("response",s),t.removeListener("close",s),a&&t.removeListener("timeout",a),t.socket||t._currentRequest.removeListener("socket",i)}return a&&this.on("timeout",a),this.socket?i(this.socket):this._currentRequest.once("socket",i),this.on("socket",n),this.on("abort",s),this.on("error",s),this.on("response",s),this.on("close",s),this},["flushHeaders","getHeader","setNoDelay","setSocketKeepAlive"].forEach(function(e){y.prototype[e]=function(a,t){return this._currentRequest[e](a,t)}}),["aborted","connection","socket"].forEach(function(e){Object.defineProperty(y.prototype,e,{get:function(){return this._currentRequest[e]}})}),y.prototype._sanitizeOptions=function(e){if(e.headers||(e.headers={}),e.host&&(e.hostname||(e.hostname=e.host),delete e.host),!e.pathname&&e.path){var a=e.path.indexOf("?");a<0?e.pathname=e.path:(e.pathname=e.path.substring(0,a),e.search=e.path.substring(a))}},y.prototype._performRequest=function(){var e=this._options.protocol,a=this._options.nativeProtocols[e];if(!a)throw TypeError("Unsupported protocol "+e);if(this._options.agents){var t=e.slice(0,-1);this._options.agent=this._options.agents[t]}var i=this._currentRequest=a.request(this._options,this._onNativeResponse);for(var s of(i._redirectable=this,d))i.on(s,m[s]);if(this._currentUrl=/^\//.test(this._options.path)?n.format(this._options):this._options.path,this._isRedirect){var o=0,r=this,c=this._requestBodyBuffers;!function e(a){if(i===r._currentRequest){if(a)r.emit("error",a);else if(o<c.length){var t=c[o++];i.finished||i.write(t.data,t.encoding,e)}else r._ended&&i.end()}}()}},y.prototype._processResponse=function(e){var a,t,s,o=e.statusCode;this._options.trackRedirects&&this._redirects.push({url:this._currentUrl,headers:e.headers,statusCode:o});var r=e.headers.location;if(!r||!1===this._options.followRedirects||o<300||o>=400){e.responseUrl=this._currentUrl,e.redirects=this._redirects,this.emit("response",e),this._requestBodyBuffers=[];return}if(T(this._currentRequest),e.destroy(),++this._redirectCount>this._options.maxRedirects)throw new x;var u=this._options.beforeRedirect;u&&(s=Object.assign({Host:e.req.getHeader("host")},this._options.headers));var d=this._options.method;(301!==o&&302!==o||"POST"!==this._options.method)&&(303!==o||/^(?:GET|HEAD)$/.test(this._options.method))||(this._options.method="GET",this._requestBodyBuffers=[],S(/^content-/i,this._options.headers));var m=S(/^host$/i,this._options.headers),f=j(this._currentUrl),h=m||f.host,v=/^\w+:/.test(r)?this._currentUrl:n.format(Object.assign(f,{host:h})),b=l?new i(r,v):j(n.resolve(v,r));if(p("redirecting to",b.href),this._isRedirect=!0,E(b,this._options),(b.protocol===f.protocol||"https:"===b.protocol)&&(b.host===h||(c(F(a=b.host)&&F(h)),(t=a.length-h.length-1)>0&&"."===a[t]&&a.endsWith(h)))||S(/^(?:(?:proxy-)?authorization|cookie)$/i,this._options.headers),C(u)){var g={headers:e.headers,statusCode:o},y={url:v,method:d,headers:s};u(this._options,g,y),this._sanitizeOptions(this._options)}this._performRequest()},e.exports=w({http:s,https:o}),e.exports.wrap=w},54127:(e,a,t)=>{var n=t(38726),i=t(21764),s=t(55315),o=t(32615),r=t(35240),c=t(17360).parse,p=t(92048),l=t(76162).Stream,u=t(70045),d=t(73930),m=t(6578);function f(e){if(!(this instanceof f))return new f(e);for(var a in this._overheadLength=0,this._valueLength=0,this._valuesToMeasure=[],n.call(this),e=e||{})this[a]=e[a]}e.exports=f,i.inherits(f,n),f.LINE_BREAK="\r\n",f.DEFAULT_CONTENT_TYPE="application/octet-stream",f.prototype.append=function(e,a,t){"string"==typeof(t=t||{})&&(t={filename:t});var i=n.prototype.append.bind(this);if("number"==typeof a&&(a=""+a),Array.isArray(a)){this._error(Error("Arrays are not supported."));return}var s=this._multiPartHeader(e,a,t),o=this._multiPartFooter();i(s),i(a),i(o),this._trackLength(s,a,t)},f.prototype._trackLength=function(e,a,t){var n=0;null!=t.knownLength?n+=+t.knownLength:Buffer.isBuffer(a)?n=a.length:"string"==typeof a&&(n=Buffer.byteLength(a)),this._valueLength+=n,this._overheadLength+=Buffer.byteLength(e)+f.LINE_BREAK.length,a&&(a.path||a.readable&&a.hasOwnProperty("httpVersion")||a instanceof l)&&(t.knownLength||this._valuesToMeasure.push(a))},f.prototype._lengthRetriever=function(e,a){e.hasOwnProperty("fd")?void 0!=e.end&&e.end!=1/0&&void 0!=e.start?a(null,e.end+1-(e.start?e.start:0)):p.stat(e.path,function(t,n){if(t){a(t);return}a(null,n.size-(e.start?e.start:0))}):e.hasOwnProperty("httpVersion")?a(null,+e.headers["content-length"]):e.hasOwnProperty("httpModule")?(e.on("response",function(t){e.pause(),a(null,+t.headers["content-length"])}),e.resume()):a("Unknown stream")},f.prototype._multiPartHeader=function(e,a,t){if("string"==typeof t.header)return t.header;var n,i=this._getContentDisposition(a,t),s=this._getContentType(a,t),o="",r={"Content-Disposition":["form-data",'name="'+e+'"'].concat(i||[]),"Content-Type":[].concat(s||[])};for(var c in"object"==typeof t.header&&m(r,t.header),r)if(r.hasOwnProperty(c)){if(null==(n=r[c]))continue;Array.isArray(n)||(n=[n]),n.length&&(o+=c+": "+n.join("; ")+f.LINE_BREAK)}return"--"+this.getBoundary()+f.LINE_BREAK+o+f.LINE_BREAK},f.prototype._getContentDisposition=function(e,a){var t,n;return"string"==typeof a.filepath?t=s.normalize(a.filepath).replace(/\\/g,"/"):a.filename||e.name||e.path?t=s.basename(a.filename||e.name||e.path):e.readable&&e.hasOwnProperty("httpVersion")&&(t=s.basename(e.client._httpMessage.path||"")),t&&(n='filename="'+t+'"'),n},f.prototype._getContentType=function(e,a){var t=a.contentType;return!t&&e.name&&(t=u.lookup(e.name)),!t&&e.path&&(t=u.lookup(e.path)),!t&&e.readable&&e.hasOwnProperty("httpVersion")&&(t=e.headers["content-type"]),!t&&(a.filepath||a.filename)&&(t=u.lookup(a.filepath||a.filename)),t||"object"!=typeof e||(t=f.DEFAULT_CONTENT_TYPE),t},f.prototype._multiPartFooter=function(){return(function(e){var a=f.LINE_BREAK;0===this._streams.length&&(a+=this._lastBoundary()),e(a)}).bind(this)},f.prototype._lastBoundary=function(){return"--"+this.getBoundary()+"--"+f.LINE_BREAK},f.prototype.getHeaders=function(e){var a,t={"content-type":"multipart/form-data; boundary="+this.getBoundary()};for(a in e)e.hasOwnProperty(a)&&(t[a.toLowerCase()]=e[a]);return t},f.prototype.setBoundary=function(e){this._boundary=e},f.prototype.getBoundary=function(){return this._boundary||this._generateBoundary(),this._boundary},f.prototype.getBuffer=function(){for(var e=new Buffer.alloc(0),a=this.getBoundary(),t=0,n=this._streams.length;t<n;t++)"function"!=typeof this._streams[t]&&(Buffer.isBuffer(this._streams[t])?e=Buffer.concat([e,this._streams[t]]):e=Buffer.concat([e,Buffer.from(this._streams[t])]),("string"!=typeof this._streams[t]||this._streams[t].substring(2,a.length+2)!==a)&&(e=Buffer.concat([e,Buffer.from(f.LINE_BREAK)])));return Buffer.concat([e,Buffer.from(this._lastBoundary())])},f.prototype._generateBoundary=function(){for(var e="--------------------------",a=0;a<24;a++)e+=Math.floor(10*Math.random()).toString(16);this._boundary=e},f.prototype.getLengthSync=function(){var e=this._overheadLength+this._valueLength;return this._streams.length&&(e+=this._lastBoundary().length),this.hasKnownLength()||this._error(Error("Cannot calculate proper length in synchronous way.")),e},f.prototype.hasKnownLength=function(){var e=!0;return this._valuesToMeasure.length&&(e=!1),e},f.prototype.getLength=function(e){var a=this._overheadLength+this._valueLength;if(this._streams.length&&(a+=this._lastBoundary().length),!this._valuesToMeasure.length){process.nextTick(e.bind(this,null,a));return}d.parallel(this._valuesToMeasure,this._lengthRetriever,function(t,n){if(t){e(t);return}n.forEach(function(e){a+=e}),e(null,a)})},f.prototype.submit=function(e,a){var t,n,i={method:"post"};return"string"==typeof e?n=m({port:(e=c(e)).port,path:e.pathname,host:e.hostname,protocol:e.protocol},i):(n=m(e,i)).port||(n.port="https:"==n.protocol?443:80),n.headers=this.getHeaders(e.headers),t="https:"==n.protocol?r.request(n):o.request(n),this.getLength((function(e,n){if(e&&"Unknown stream"!==e){this._error(e);return}if(n&&t.setHeader("Content-Length",n),this.pipe(t),a){var i,s=function(e,n){return t.removeListener("error",s),t.removeListener("response",i),a.call(this,e,n)};i=s.bind(this,null),t.on("error",s),t.on("response",i)}}).bind(this)),t},f.prototype._error=function(e){this.error||(this.error=e,this.pause(),this.emit("error",e))},f.prototype.toString=function(){return"[object FormData]"}},6578:e=>{e.exports=function(e,a){return Object.keys(a).forEach(function(t){e[t]=e[t]||a[t]}),e}},23703:(e,a,t)=>{"use strict";t.d(a,{gN:()=>to,l0:()=>tr,J9:()=>tt});var n=function(e){var a;return!!e&&"object"==typeof e&&"[object RegExp]"!==(a=Object.prototype.toString.call(e))&&"[object Date]"!==a&&e.$$typeof!==i},i="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function s(e,a){return!1!==a.clone&&a.isMergeableObject(e)?r(Array.isArray(e)?[]:{},e,a):e}function o(e,a,t){return e.concat(a).map(function(e){return s(e,t)})}function r(e,a,t){(t=t||{}).arrayMerge=t.arrayMerge||o,t.isMergeableObject=t.isMergeableObject||n;var i,c,p=Array.isArray(a);return p!==Array.isArray(e)?s(a,t):p?t.arrayMerge(e,a,t):(c={},(i=t).isMergeableObject(e)&&Object.keys(e).forEach(function(a){c[a]=s(e[a],i)}),Object.keys(a).forEach(function(t){i.isMergeableObject(a[t])&&e[t]?c[t]=r(e[t],a[t],i):c[t]=s(a[t],i)}),c)}r.all=function(e,a){if(!Array.isArray(e))throw Error("first argument should be an array");return e.reduce(function(e,t){return r(e,t,a)},{})};let c=r;var p="object"==typeof global&&global&&global.Object===Object&&global,l="object"==typeof self&&self&&self.Object===Object&&self,u=p||l||Function("return this")(),d=u.Symbol,m=Object.prototype,f=m.hasOwnProperty,h=m.toString,x=d?d.toStringTag:void 0;let v=function(e){var a=f.call(e,x),t=e[x];try{e[x]=void 0;var n=!0}catch(e){}var i=h.call(e);return n&&(a?e[x]=t:delete e[x]),i};var b=Object.prototype.toString,g=d?d.toStringTag:void 0;let y=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":g&&g in Object(e)?v(e):b.call(e)},w=function(e,a){return function(t){return e(a(t))}};var _=w(Object.getPrototypeOf,Object);let j=function(e){return null!=e&&"object"==typeof e};var k=Object.prototype,E=Function.prototype.toString,S=k.hasOwnProperty,O=E.call(Object);let T=function(e){if(!j(e)||"[object Object]"!=y(e))return!1;var a=_(e);if(null===a)return!0;var t=S.call(a,"constructor")&&a.constructor;return"function"==typeof t&&t instanceof t&&E.call(t)==O},F=function(e,a){return e===a||e!=e&&a!=a},C=function(e,a){for(var t=e.length;t--;)if(F(e[t][0],a))return t;return -1};var R=Array.prototype.splice;function A(e){var a=-1,t=null==e?0:e.length;for(this.clear();++a<t;){var n=e[a];this.set(n[0],n[1])}}A.prototype.clear=function(){this.__data__=[],this.size=0},A.prototype.delete=function(e){var a=this.__data__,t=C(a,e);return!(t<0)&&(t==a.length-1?a.pop():R.call(a,t,1),--this.size,!0)},A.prototype.get=function(e){var a=this.__data__,t=C(a,e);return t<0?void 0:a[t][1]},A.prototype.has=function(e){return C(this.__data__,e)>-1},A.prototype.set=function(e,a){var t=this.__data__,n=C(t,e);return n<0?(++this.size,t.push([e,a])):t[n][1]=a,this};let z=function(e){var a=typeof e;return null!=e&&("object"==a||"function"==a)},L=function(e){if(!z(e))return!1;var a=y(e);return"[object Function]"==a||"[object GeneratorFunction]"==a||"[object AsyncFunction]"==a||"[object Proxy]"==a};var D=u["__core-js_shared__"],U=function(){var e=/[^.]+$/.exec(D&&D.keys&&D.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),N=Function.prototype.toString;let P=function(e){if(null!=e){try{return N.call(e)}catch(e){}try{return e+""}catch(e){}}return""};var B=/^\[object .+?Constructor\]$/,q=Object.prototype,$=Function.prototype.toString,I=q.hasOwnProperty,M=RegExp("^"+$.call(I).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");let V=function(e,a){var t,n=null==e?void 0:e[a];return z(t=n)&&(!U||!(U in t))&&(L(t)?M:B).test(P(t))?n:void 0};var H=V(u,"Map"),W=V(Object,"create"),G=Object.prototype.hasOwnProperty,K=Object.prototype.hasOwnProperty;function J(e){var a=-1,t=null==e?0:e.length;for(this.clear();++a<t;){var n=e[a];this.set(n[0],n[1])}}J.prototype.clear=function(){this.__data__=W?W(null):{},this.size=0},J.prototype.delete=function(e){var a=this.has(e)&&delete this.__data__[e];return this.size-=a?1:0,a},J.prototype.get=function(e){var a=this.__data__;if(W){var t=a[e];return"__lodash_hash_undefined__"===t?void 0:t}return G.call(a,e)?a[e]:void 0},J.prototype.has=function(e){var a=this.__data__;return W?void 0!==a[e]:K.call(a,e)},J.prototype.set=function(e,a){var t=this.__data__;return this.size+=this.has(e)?0:1,t[e]=W&&void 0===a?"__lodash_hash_undefined__":a,this};let Z=function(e){var a=typeof e;return"string"==a||"number"==a||"symbol"==a||"boolean"==a?"__proto__"!==e:null===e},Y=function(e,a){var t=e.__data__;return Z(a)?t["string"==typeof a?"string":"hash"]:t.map};function X(e){var a=-1,t=null==e?0:e.length;for(this.clear();++a<t;){var n=e[a];this.set(n[0],n[1])}}function Q(e){var a=this.__data__=new A(e);this.size=a.size}X.prototype.clear=function(){this.size=0,this.__data__={hash:new J,map:new(H||A),string:new J}},X.prototype.delete=function(e){var a=Y(this,e).delete(e);return this.size-=a?1:0,a},X.prototype.get=function(e){return Y(this,e).get(e)},X.prototype.has=function(e){return Y(this,e).has(e)},X.prototype.set=function(e,a){var t=Y(this,e),n=t.size;return t.set(e,a),this.size+=t.size==n?0:1,this},Q.prototype.clear=function(){this.__data__=new A,this.size=0},Q.prototype.delete=function(e){var a=this.__data__,t=a.delete(e);return this.size=a.size,t},Q.prototype.get=function(e){return this.__data__.get(e)},Q.prototype.has=function(e){return this.__data__.has(e)},Q.prototype.set=function(e,a){var t=this.__data__;if(t instanceof A){var n=t.__data__;if(!H||n.length<199)return n.push([e,a]),this.size=++t.size,this;t=this.__data__=new X(n)}return t.set(e,a),this.size=t.size,this};let ee=function(e,a){for(var t=-1,n=null==e?0:e.length;++t<n&&!1!==a(e[t],t,e););return e};var ea=function(){try{var e=V(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();let et=function(e,a,t){"__proto__"==a&&ea?ea(e,a,{configurable:!0,enumerable:!0,value:t,writable:!0}):e[a]=t};var en=Object.prototype.hasOwnProperty;let ei=function(e,a,t){var n=e[a];en.call(e,a)&&F(n,t)&&(void 0!==t||a in e)||et(e,a,t)},es=function(e,a,t,n){var i=!t;t||(t={});for(var s=-1,o=a.length;++s<o;){var r=a[s],c=n?n(t[r],e[r],r,t,e):void 0;void 0===c&&(c=e[r]),i?et(t,r,c):ei(t,r,c)}return t},eo=function(e,a){for(var t=-1,n=Array(e);++t<e;)n[t]=a(t);return n},er=function(e){return j(e)&&"[object Arguments]"==y(e)};var ec=Object.prototype,ep=ec.hasOwnProperty,el=ec.propertyIsEnumerable,eu=er(function(){return arguments}())?er:function(e){return j(e)&&ep.call(e,"callee")&&!el.call(e,"callee")},ed=Array.isArray,em="object"==typeof exports&&exports&&!exports.nodeType&&exports,ef=em&&"object"==typeof module&&module&&!module.nodeType&&module,eh=ef&&ef.exports===em?u.Buffer:void 0;let ex=(eh?eh.isBuffer:void 0)||function(){return!1};var ev=/^(?:0|[1-9]\d*)$/;let eb=function(e,a){var t=typeof e;return!!(a=null==a?9007199254740991:a)&&("number"==t||"symbol"!=t&&ev.test(e))&&e>-1&&e%1==0&&e<a},eg=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991};var ey={};ey["[object Float32Array]"]=ey["[object Float64Array]"]=ey["[object Int8Array]"]=ey["[object Int16Array]"]=ey["[object Int32Array]"]=ey["[object Uint8Array]"]=ey["[object Uint8ClampedArray]"]=ey["[object Uint16Array]"]=ey["[object Uint32Array]"]=!0,ey["[object Arguments]"]=ey["[object Array]"]=ey["[object ArrayBuffer]"]=ey["[object Boolean]"]=ey["[object DataView]"]=ey["[object Date]"]=ey["[object Error]"]=ey["[object Function]"]=ey["[object Map]"]=ey["[object Number]"]=ey["[object Object]"]=ey["[object RegExp]"]=ey["[object Set]"]=ey["[object String]"]=ey["[object WeakMap]"]=!1;let ew=function(e){return function(a){return e(a)}};var e_="object"==typeof exports&&exports&&!exports.nodeType&&exports,ej=e_&&"object"==typeof module&&module&&!module.nodeType&&module,ek=ej&&ej.exports===e_&&p.process,eE=function(){try{var e=ej&&ej.require&&ej.require("util").types;if(e)return e;return ek&&ek.binding&&ek.binding("util")}catch(e){}}(),eS=eE&&eE.isTypedArray,eO=eS?ew(eS):function(e){return j(e)&&eg(e.length)&&!!ey[y(e)]},eT=Object.prototype.hasOwnProperty;let eF=function(e,a){var t=ed(e),n=!t&&eu(e),i=!t&&!n&&ex(e),s=!t&&!n&&!i&&eO(e),o=t||n||i||s,r=o?eo(e.length,String):[],c=r.length;for(var p in e)(a||eT.call(e,p))&&!(o&&("length"==p||i&&("offset"==p||"parent"==p)||s&&("buffer"==p||"byteLength"==p||"byteOffset"==p)||eb(p,c)))&&r.push(p);return r};var eC=Object.prototype;let eR=function(e){var a=e&&e.constructor;return e===("function"==typeof a&&a.prototype||eC)};var eA=w(Object.keys,Object),ez=Object.prototype.hasOwnProperty;let eL=function(e){if(!eR(e))return eA(e);var a=[];for(var t in Object(e))ez.call(e,t)&&"constructor"!=t&&a.push(t);return a},eD=function(e){return null!=e&&eg(e.length)&&!L(e)},eU=function(e){return eD(e)?eF(e):eL(e)},eN=function(e){var a=[];if(null!=e)for(var t in Object(e))a.push(t);return a};var eP=Object.prototype.hasOwnProperty;let eB=function(e){if(!z(e))return eN(e);var a=eR(e),t=[];for(var n in e)"constructor"==n&&(a||!eP.call(e,n))||t.push(n);return t},eq=function(e){return eD(e)?eF(e,!0):eB(e)};var e$="object"==typeof exports&&exports&&!exports.nodeType&&exports,eI=e$&&"object"==typeof module&&module&&!module.nodeType&&module,eM=eI&&eI.exports===e$?u.Buffer:void 0,eV=eM?eM.allocUnsafe:void 0;let eH=function(e,a){if(a)return e.slice();var t=e.length,n=eV?eV(t):new e.constructor(t);return e.copy(n),n},eW=function(e,a){var t=-1,n=e.length;for(a||(a=Array(n));++t<n;)a[t]=e[t];return a},eG=function(e,a){for(var t=-1,n=null==e?0:e.length,i=0,s=[];++t<n;){var o=e[t];a(o,t,e)&&(s[i++]=o)}return s},eK=function(){return[]};var eJ=Object.prototype.propertyIsEnumerable,eZ=Object.getOwnPropertySymbols,eY=eZ?function(e){return null==e?[]:eG(eZ(e=Object(e)),function(a){return eJ.call(e,a)})}:eK;let eX=function(e,a){for(var t=-1,n=a.length,i=e.length;++t<n;)e[i+t]=a[t];return e};var eQ=Object.getOwnPropertySymbols?function(e){for(var a=[];e;)eX(a,eY(e)),e=_(e);return a}:eK;let e0=function(e,a,t){var n=a(e);return ed(e)?n:eX(n,t(e))},e1=function(e){return e0(e,eU,eY)},e2=function(e){return e0(e,eq,eQ)};var e3=V(u,"DataView"),e6=V(u,"Promise"),e7=V(u,"Set"),e4=V(u,"WeakMap"),e8="[object Map]",e9="[object Promise]",e5="[object Set]",ae="[object WeakMap]",aa="[object DataView]",at=P(e3),an=P(H),ai=P(e6),as=P(e7),ao=P(e4),ar=y;(e3&&ar(new e3(new ArrayBuffer(1)))!=aa||H&&ar(new H)!=e8||e6&&ar(e6.resolve())!=e9||e7&&ar(new e7)!=e5||e4&&ar(new e4)!=ae)&&(ar=function(e){var a=y(e),t="[object Object]"==a?e.constructor:void 0,n=t?P(t):"";if(n)switch(n){case at:return aa;case an:return e8;case ai:return e9;case as:return e5;case ao:return ae}return a});let ac=ar;var ap=Object.prototype.hasOwnProperty;let al=function(e){var a=e.length,t=new e.constructor(a);return a&&"string"==typeof e[0]&&ap.call(e,"index")&&(t.index=e.index,t.input=e.input),t};var au=u.Uint8Array;let ad=function(e){var a=new e.constructor(e.byteLength);return new au(a).set(new au(e)),a},am=function(e,a){var t=a?ad(e.buffer):e.buffer;return new e.constructor(t,e.byteOffset,e.byteLength)};var af=/\w*$/;let ah=function(e){var a=new e.constructor(e.source,af.exec(e));return a.lastIndex=e.lastIndex,a};var ax=d?d.prototype:void 0,av=ax?ax.valueOf:void 0;let ab=function(e,a){var t=a?ad(e.buffer):e.buffer;return new e.constructor(t,e.byteOffset,e.length)},ag=function(e,a,t){var n=e.constructor;switch(a){case"[object ArrayBuffer]":return ad(e);case"[object Boolean]":case"[object Date]":return new n(+e);case"[object DataView]":return am(e,t);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return ab(e,t);case"[object Map]":case"[object Set]":return new n;case"[object Number]":case"[object String]":return new n(e);case"[object RegExp]":return ah(e);case"[object Symbol]":return av?Object(av.call(e)):{}}};var ay=Object.create,aw=function(){function e(){}return function(a){if(!z(a))return{};if(ay)return ay(a);e.prototype=a;var t=new e;return e.prototype=void 0,t}}(),a_=eE&&eE.isMap,aj=a_?ew(a_):function(e){return j(e)&&"[object Map]"==ac(e)},ak=eE&&eE.isSet,aE=ak?ew(ak):function(e){return j(e)&&"[object Set]"==ac(e)},aS="[object Arguments]",aO="[object Function]",aT="[object Object]",aF={};aF[aS]=aF["[object Array]"]=aF["[object ArrayBuffer]"]=aF["[object DataView]"]=aF["[object Boolean]"]=aF["[object Date]"]=aF["[object Float32Array]"]=aF["[object Float64Array]"]=aF["[object Int8Array]"]=aF["[object Int16Array]"]=aF["[object Int32Array]"]=aF["[object Map]"]=aF["[object Number]"]=aF[aT]=aF["[object RegExp]"]=aF["[object Set]"]=aF["[object String]"]=aF["[object Symbol]"]=aF["[object Uint8Array]"]=aF["[object Uint8ClampedArray]"]=aF["[object Uint16Array]"]=aF["[object Uint32Array]"]=!0,aF["[object Error]"]=aF[aO]=aF["[object WeakMap]"]=!1;let aC=function e(a,t,n,i,s,o){var r,c=1&t,p=2&t,l=4&t;if(n&&(r=s?n(a,i,s,o):n(a)),void 0!==r)return r;if(!z(a))return a;var u=ed(a);if(u){if(r=al(a),!c)return eW(a,r)}else{var d,m,f,h,x=ac(a),v=x==aO||"[object GeneratorFunction]"==x;if(ex(a))return eH(a,c);if(x==aT||x==aS||v&&!s){if(r=p||v?{}:"function"!=typeof a.constructor||eR(a)?{}:aw(_(a)),!c)return p?(m=(d=r)&&es(a,eq(a),d),es(a,eQ(a),m)):(h=(f=r)&&es(a,eU(a),f),es(a,eY(a),h))}else{if(!aF[x])return s?a:{};r=ag(a,x,c)}}o||(o=new Q);var b=o.get(a);if(b)return b;o.set(a,r),aE(a)?a.forEach(function(i){r.add(e(i,t,n,i,a,o))}):aj(a)&&a.forEach(function(i,s){r.set(s,e(i,t,n,s,a,o))});var g=l?p?e2:e1:p?eq:eU,y=u?void 0:g(a);return ee(y||a,function(i,s){y&&(i=a[s=i]),ei(r,s,e(i,t,n,s,a,o))}),r},aR=function(e){return aC(e,5)};var aA=t(17577),az=t(86725),aL=t.n(az);let aD=function(e,a){},aU=function(e){return aC(e,4)},aN=function(e,a){for(var t=-1,n=null==e?0:e.length,i=Array(n);++t<n;)i[t]=a(e[t],t,e);return i},aP=function(e){return"symbol"==typeof e||j(e)&&"[object Symbol]"==y(e)};function aB(e,a){if("function"!=typeof e||null!=a&&"function"!=typeof a)throw TypeError("Expected a function");var t=function(){var n=arguments,i=a?a.apply(this,n):n[0],s=t.cache;if(s.has(i))return s.get(i);var o=e.apply(this,n);return t.cache=s.set(i,o)||s,o};return t.cache=new(aB.Cache||X),t}aB.Cache=X;var aq=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,a$=/\\(\\)?/g,aI=function(e){var a=aB(e,function(e){return 500===t.size&&t.clear(),e}),t=a.cache;return a}(function(e){var a=[];return 46===e.charCodeAt(0)&&a.push(""),e.replace(aq,function(e,t,n,i){a.push(n?i.replace(a$,"$1"):t||e)}),a}),aM=1/0;let aV=function(e){if("string"==typeof e||aP(e))return e;var a=e+"";return"0"==a&&1/e==-aM?"-0":a};var aH=1/0,aW=d?d.prototype:void 0,aG=aW?aW.toString:void 0;let aK=function e(a){if("string"==typeof a)return a;if(ed(a))return aN(a,e)+"";if(aP(a))return aG?aG.call(a):"";var t=a+"";return"0"==t&&1/a==-aH?"-0":t},aJ=function(e){return ed(e)?aN(e,aV):aP(e)?[e]:eW(aI(null==e?"":aK(e)))};function aZ(){return(aZ=Object.assign||function(e){for(var a=1;a<arguments.length;a++){var t=arguments[a];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}function aY(e,a){if(null==e)return{};var t,n,i={},s=Object.keys(e);for(n=0;n<s.length;n++)t=s[n],a.indexOf(t)>=0||(i[t]=e[t]);return i}function aX(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}t(79997);var aQ=(0,aA.createContext)(void 0);aQ.displayName="FormikContext";var a0=aQ.Provider;function a1(){var e=(0,aA.useContext)(aQ);return e||aD(!1),e}aQ.Consumer;var a2=function(e){return Array.isArray(e)&&0===e.length},a3=function(e){return"function"==typeof e},a6=function(e){return null!==e&&"object"==typeof e},a7=function(e){return"[object String]"===Object.prototype.toString.call(e)},a4=function(e){return 0===aA.Children.count(e)},a8=function(e){return a6(e)&&a3(e.then)};function a9(e,a,t,n){void 0===n&&(n=0);for(var i=aJ(a);e&&n<i.length;)e=e[i[n++]];return n===i.length||e?void 0===e?t:e:t}function a5(e,a,t){for(var n=aU(e),i=n,s=0,o=aJ(a);s<o.length-1;s++){var r=o[s],c=a9(e,o.slice(0,s+1));if(c&&(a6(c)||Array.isArray(c)))i=i[r]=aU(c);else{var p=o[s+1];i=i[r]=String(Math.floor(Number(p)))===p&&Number(p)>=0?[]:{}}}return(0===s?e:i)[o[s]]===t?e:(void 0===t?delete i[o[s]]:i[o[s]]=t,0===s&&void 0===t&&delete n[o[s]],n)}var te={},ta={};function tt(e){var a,t,n,i,s,o,r,p,l,u,d,m,f,h,x,v,b,g,y,w,_,j,k,E,S,O,F,C,R,A,z,L,D,U,N,P,B,q,$,I,M,V,H,W,G,K,J,Z,Y,X,Q,ee,ea,et,en,ei=(t=void 0===(a=e.validateOnChange)||a,i=void 0===(n=e.validateOnBlur)||n,o=void 0!==(s=e.validateOnMount)&&s,r=e.isInitialValid,l=void 0!==(p=e.enableReinitialize)&&p,u=e.onSubmit,d=aY(e,["validateOnChange","validateOnBlur","validateOnMount","isInitialValid","enableReinitialize","onSubmit"]),m=aZ({validateOnChange:t,validateOnBlur:i,validateOnMount:o,onSubmit:u},d),f=(0,aA.useRef)(m.initialValues),h=(0,aA.useRef)(m.initialErrors||te),x=(0,aA.useRef)(m.initialTouched||ta),v=(0,aA.useRef)(m.initialStatus),b=(0,aA.useRef)(!1),g=(0,aA.useRef)({}),(0,aA.useEffect)(function(){return b.current=!0,function(){b.current=!1}},[]),y=(0,aA.useState)(0)[1],_=(w=(0,aA.useRef)({values:aR(m.initialValues),errors:aR(m.initialErrors)||te,touched:aR(m.initialTouched)||ta,status:aR(m.initialStatus),isSubmitting:!1,isValidating:!1,submitCount:0})).current,j=(0,aA.useCallback)(function(e){var a=w.current;w.current=function(e,a){switch(a.type){case"SET_VALUES":return aZ({},e,{values:a.payload});case"SET_TOUCHED":return aZ({},e,{touched:a.payload});case"SET_ERRORS":if(aL()(e.errors,a.payload))return e;return aZ({},e,{errors:a.payload});case"SET_STATUS":return aZ({},e,{status:a.payload});case"SET_ISSUBMITTING":return aZ({},e,{isSubmitting:a.payload});case"SET_ISVALIDATING":return aZ({},e,{isValidating:a.payload});case"SET_FIELD_VALUE":return aZ({},e,{values:a5(e.values,a.payload.field,a.payload.value)});case"SET_FIELD_TOUCHED":return aZ({},e,{touched:a5(e.touched,a.payload.field,a.payload.value)});case"SET_FIELD_ERROR":return aZ({},e,{errors:a5(e.errors,a.payload.field,a.payload.value)});case"RESET_FORM":return aZ({},e,a.payload);case"SET_FORMIK_STATE":return a.payload(e);case"SUBMIT_ATTEMPT":return aZ({},e,{touched:function e(a,t,n,i){void 0===n&&(n=new WeakMap),void 0===i&&(i={});for(var s=0,o=Object.keys(a);s<o.length;s++){var r=o[s],c=a[r];a6(c)?n.get(c)||(n.set(c,!0),i[r]=Array.isArray(c)?[]:{},e(c,t,n,i[r])):i[r]=t}return i}(e.values,!0),isSubmitting:!0,submitCount:e.submitCount+1});case"SUBMIT_FAILURE":case"SUBMIT_SUCCESS":return aZ({},e,{isSubmitting:!1});default:return e}}(a,e),a!==w.current&&y(function(e){return e+1})},[]),k=(0,aA.useCallback)(function(e,a){return new Promise(function(t,n){var i=m.validate(e,a);null==i?t(te):a8(i)?i.then(function(e){t(e||te)},function(e){n(e)}):t(i)})},[m.validate]),E=(0,aA.useCallback)(function(e,a){var t,n,i=m.validationSchema,s=a3(i)?i(a):i,o=a&&s.validateAt?s.validateAt(a,e):(void 0===t&&(t=!1),n=function e(a){var t=Array.isArray(a)?[]:{};for(var n in a)if(Object.prototype.hasOwnProperty.call(a,n)){var i=String(n);!0===Array.isArray(a[i])?t[i]=a[i].map(function(a){return!0===Array.isArray(a)||T(a)?e(a):""!==a?a:void 0}):T(a[i])?t[i]=e(a[i]):t[i]=""!==a[i]?a[i]:void 0}return t}(e),s[t?"validateSync":"validate"](n,{abortEarly:!1,context:n}));return new Promise(function(e,a){o.then(function(){e(te)},function(t){"ValidationError"===t.name?e(function(e){var a={};if(e.inner){if(0===e.inner.length)return a5(a,e.path,e.message);for(var t=e.inner,n=Array.isArray(t),i=0,t=n?t:t[Symbol.iterator]();;){if(n){if(i>=t.length)break;s=t[i++]}else{if((i=t.next()).done)break;s=i.value}var s,o=s;a9(a,o.path)||(a=a5(a,o.path,o.message))}}return a}(t)):a(t)})})},[m.validationSchema]),S=(0,aA.useCallback)(function(e,a){return new Promise(function(t){return t(g.current[e].validate(a))})},[]),O=(0,aA.useCallback)(function(e){var a=Object.keys(g.current).filter(function(e){return a3(g.current[e].validate)});return Promise.all(a.length>0?a.map(function(a){return S(a,a9(e,a))}):[Promise.resolve("DO_NOT_DELETE_YOU_WILL_BE_FIRED")]).then(function(e){return e.reduce(function(e,t,n){return"DO_NOT_DELETE_YOU_WILL_BE_FIRED"===t||t&&(e=a5(e,a[n],t)),e},{})})},[S]),F=(0,aA.useCallback)(function(e){return Promise.all([O(e),m.validationSchema?E(e):{},m.validate?k(e):{}]).then(function(e){var a=e[0],t=e[1],n=e[2];return c.all([a,t,n],{arrayMerge:tn})})},[m.validate,m.validationSchema,O,k,E]),C=ts(function(e){return void 0===e&&(e=_.values),j({type:"SET_ISVALIDATING",payload:!0}),F(e).then(function(e){return b.current&&(j({type:"SET_ISVALIDATING",payload:!1}),j({type:"SET_ERRORS",payload:e})),e})}),(0,aA.useEffect)(function(){o&&!0===b.current&&aL()(f.current,m.initialValues)&&C(f.current)},[o,C]),R=(0,aA.useCallback)(function(e){var a=e&&e.values?e.values:f.current,t=e&&e.errors?e.errors:h.current?h.current:m.initialErrors||{},n=e&&e.touched?e.touched:x.current?x.current:m.initialTouched||{},i=e&&e.status?e.status:v.current?v.current:m.initialStatus;f.current=a,h.current=t,x.current=n,v.current=i;var s=function(){j({type:"RESET_FORM",payload:{isSubmitting:!!e&&!!e.isSubmitting,errors:t,touched:n,status:i,values:a,isValidating:!!e&&!!e.isValidating,submitCount:e&&e.submitCount&&"number"==typeof e.submitCount?e.submitCount:0}})};if(m.onReset){var o=m.onReset(_.values,Z);a8(o)?o.then(s):s()}else s()},[m.initialErrors,m.initialStatus,m.initialTouched,m.onReset]),(0,aA.useEffect)(function(){!0===b.current&&!aL()(f.current,m.initialValues)&&l&&(f.current=m.initialValues,R(),o&&C(f.current))},[l,m.initialValues,R,o,C]),(0,aA.useEffect)(function(){l&&!0===b.current&&!aL()(h.current,m.initialErrors)&&(h.current=m.initialErrors||te,j({type:"SET_ERRORS",payload:m.initialErrors||te}))},[l,m.initialErrors]),(0,aA.useEffect)(function(){l&&!0===b.current&&!aL()(x.current,m.initialTouched)&&(x.current=m.initialTouched||ta,j({type:"SET_TOUCHED",payload:m.initialTouched||ta}))},[l,m.initialTouched]),(0,aA.useEffect)(function(){l&&!0===b.current&&!aL()(v.current,m.initialStatus)&&(v.current=m.initialStatus,j({type:"SET_STATUS",payload:m.initialStatus}))},[l,m.initialStatus,m.initialTouched]),A=ts(function(e){if(g.current[e]&&a3(g.current[e].validate)){var a=a9(_.values,e),t=g.current[e].validate(a);return a8(t)?(j({type:"SET_ISVALIDATING",payload:!0}),t.then(function(e){return e}).then(function(a){j({type:"SET_FIELD_ERROR",payload:{field:e,value:a}}),j({type:"SET_ISVALIDATING",payload:!1})})):(j({type:"SET_FIELD_ERROR",payload:{field:e,value:t}}),Promise.resolve(t))}return m.validationSchema?(j({type:"SET_ISVALIDATING",payload:!0}),E(_.values,e).then(function(e){return e}).then(function(a){j({type:"SET_FIELD_ERROR",payload:{field:e,value:a9(a,e)}}),j({type:"SET_ISVALIDATING",payload:!1})})):Promise.resolve()}),z=(0,aA.useCallback)(function(e,a){var t=a.validate;g.current[e]={validate:t}},[]),L=(0,aA.useCallback)(function(e){delete g.current[e]},[]),D=ts(function(e,a){return j({type:"SET_TOUCHED",payload:e}),(void 0===a?i:a)?C(_.values):Promise.resolve()}),U=(0,aA.useCallback)(function(e){j({type:"SET_ERRORS",payload:e})},[]),N=ts(function(e,a){var n=a3(e)?e(_.values):e;return j({type:"SET_VALUES",payload:n}),(void 0===a?t:a)?C(n):Promise.resolve()}),P=(0,aA.useCallback)(function(e,a){j({type:"SET_FIELD_ERROR",payload:{field:e,value:a}})},[]),B=ts(function(e,a,n){return j({type:"SET_FIELD_VALUE",payload:{field:e,value:a}}),(void 0===n?t:n)?C(a5(_.values,e,a)):Promise.resolve()}),q=(0,aA.useCallback)(function(e,a){var t,n=a,i=e;if(!a7(e)){e.persist&&e.persist();var s=e.target?e.target:e.currentTarget,o=s.type,r=s.name,c=s.id,p=s.value,l=s.checked,u=(s.outerHTML,s.options),d=s.multiple;n=a||r||c,i=/number|range/.test(o)?isNaN(t=parseFloat(p))?"":t:/checkbox/.test(o)?function(e,a,t){if("boolean"==typeof e)return!!a;var n=[],i=!1,s=-1;if(Array.isArray(e))n=e,i=(s=e.indexOf(t))>=0;else if(!t||"true"==t||"false"==t)return!!a;return a&&t&&!i?n.concat(t):i?n.slice(0,s).concat(n.slice(s+1)):n}(a9(_.values,n),l,p):u&&d?Array.from(u).filter(function(e){return e.selected}).map(function(e){return e.value}):p}n&&B(n,i)},[B,_.values]),$=ts(function(e){if(a7(e))return function(a){return q(a,e)};q(e)}),I=ts(function(e,a,t){return void 0===a&&(a=!0),j({type:"SET_FIELD_TOUCHED",payload:{field:e,value:a}}),(void 0===t?i:t)?C(_.values):Promise.resolve()}),M=(0,aA.useCallback)(function(e,a){e.persist&&e.persist();var t=e.target,n=t.name,i=t.id;t.outerHTML,I(a||n||i,!0)},[I]),V=ts(function(e){if(a7(e))return function(a){return M(a,e)};M(e)}),H=(0,aA.useCallback)(function(e){a3(e)?j({type:"SET_FORMIK_STATE",payload:e}):j({type:"SET_FORMIK_STATE",payload:function(){return e}})},[]),W=(0,aA.useCallback)(function(e){j({type:"SET_STATUS",payload:e})},[]),G=(0,aA.useCallback)(function(e){j({type:"SET_ISSUBMITTING",payload:e})},[]),K=ts(function(){return j({type:"SUBMIT_ATTEMPT"}),C().then(function(e){var a,t=e instanceof Error;if(!t&&0===Object.keys(e).length){try{if(a=Y(),void 0===a)return}catch(e){throw e}return Promise.resolve(a).then(function(e){return b.current&&j({type:"SUBMIT_SUCCESS"}),e}).catch(function(e){if(b.current)throw j({type:"SUBMIT_FAILURE"}),e})}if(b.current&&(j({type:"SUBMIT_FAILURE"}),t))throw e})}),J=ts(function(e){e&&e.preventDefault&&a3(e.preventDefault)&&e.preventDefault(),e&&e.stopPropagation&&a3(e.stopPropagation)&&e.stopPropagation(),K().catch(function(e){console.warn("Warning: An unhandled error was caught from submitForm()",e)})}),Z={resetForm:R,validateForm:C,validateField:A,setErrors:U,setFieldError:P,setFieldTouched:I,setFieldValue:B,setStatus:W,setSubmitting:G,setTouched:D,setValues:N,setFormikState:H,submitForm:K},Y=ts(function(){return u(_.values,Z)}),X=ts(function(e){e&&e.preventDefault&&a3(e.preventDefault)&&e.preventDefault(),e&&e.stopPropagation&&a3(e.stopPropagation)&&e.stopPropagation(),R()}),Q=(0,aA.useCallback)(function(e){return{value:a9(_.values,e),error:a9(_.errors,e),touched:!!a9(_.touched,e),initialValue:a9(f.current,e),initialTouched:!!a9(x.current,e),initialError:a9(h.current,e)}},[_.errors,_.touched,_.values]),ee=(0,aA.useCallback)(function(e){return{setValue:function(a,t){return B(e,a,t)},setTouched:function(a,t){return I(e,a,t)},setError:function(a){return P(e,a)}}},[B,I,P]),ea=(0,aA.useCallback)(function(e){var a=a6(e),t=a?e.name:e,n=a9(_.values,t),i={name:t,value:n,onChange:$,onBlur:V};if(a){var s=e.type,o=e.value,r=e.as,c=e.multiple;"checkbox"===s?void 0===o?i.checked=!!n:(i.checked=!!(Array.isArray(n)&&~n.indexOf(o)),i.value=o):"radio"===s?(i.checked=n===o,i.value=o):"select"===r&&c&&(i.value=i.value||[],i.multiple=!0)}return i},[V,$,_.values]),et=(0,aA.useMemo)(function(){return!aL()(f.current,_.values)},[f.current,_.values]),en=(0,aA.useMemo)(function(){return void 0!==r?et?_.errors&&0===Object.keys(_.errors).length:!1!==r&&a3(r)?r(m):r:_.errors&&0===Object.keys(_.errors).length},[r,et,_.errors,m]),aZ({},_,{initialValues:f.current,initialErrors:h.current,initialTouched:x.current,initialStatus:v.current,handleBlur:V,handleChange:$,handleReset:X,handleSubmit:J,resetForm:R,setErrors:U,setFormikState:H,setFieldTouched:I,setFieldValue:B,setFieldError:P,setStatus:W,setSubmitting:G,setTouched:D,setValues:N,submitForm:K,validateForm:C,validateField:A,isValid:en,dirty:et,unregisterField:L,registerField:z,getFieldProps:ea,getFieldMeta:Q,getFieldHelpers:ee,validateOnBlur:i,validateOnChange:t,validateOnMount:o})),es=e.component,eo=e.children,er=e.render,ec=e.innerRef;return(0,aA.useImperativeHandle)(ec,function(){return ei}),(0,aA.createElement)(a0,{value:ei},es?(0,aA.createElement)(es,ei):er?er(ei):eo?a3(eo)?eo(ei):a4(eo)?null:aA.Children.only(eo):null)}function tn(e,a,t){var n=e.slice();return a.forEach(function(a,i){if(void 0===n[i]){var s=!1!==t.clone&&t.isMergeableObject(a);n[i]=s?c(Array.isArray(a)?[]:{},a,t):a}else t.isMergeableObject(a)?n[i]=c(e[i],a,t):-1===e.indexOf(a)&&n.push(a)}),n}var ti="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?aA.useLayoutEffect:aA.useEffect;function ts(e){var a=(0,aA.useRef)(e);return ti(function(){a.current=e}),(0,aA.useCallback)(function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.current.apply(void 0,t)},[])}function to(e){var a=e.validate,t=e.name,n=e.render,i=e.children,s=e.as,o=e.component,r=e.className,c=aY(e,["validate","name","render","children","as","component","className"]),p=aY(a1(),["validate","validationSchema"]),l=p.registerField,u=p.unregisterField;(0,aA.useEffect)(function(){return l(t,{validate:a}),function(){u(t)}},[l,u,t,a]);var d=p.getFieldProps(aZ({name:t},c)),m=p.getFieldMeta(t),f={field:d,form:p};if(n)return n(aZ({},f,{meta:m}));if(a3(i))return i(aZ({},f,{meta:m}));if(o){if("string"==typeof o){var h=c.innerRef,x=aY(c,["innerRef"]);return(0,aA.createElement)(o,aZ({ref:h},d,x,{className:r}),i)}return(0,aA.createElement)(o,aZ({field:d,form:p},c,{className:r}),i)}var v=s||"input";if("string"==typeof v){var b=c.innerRef,g=aY(c,["innerRef"]);return(0,aA.createElement)(v,aZ({ref:b},d,g,{className:r}),i)}return(0,aA.createElement)(v,aZ({},d,c,{className:r}),i)}var tr=(0,aA.forwardRef)(function(e,a){var t=e.action,n=aY(e,["action"]),i=a1(),s=i.handleReset,o=i.handleSubmit;return(0,aA.createElement)("form",aZ({onSubmit:o,ref:a,onReset:s,action:null!=t?t:"#"},n))});tr.displayName="Form";var tc=function(e,a,t){var n=td(e),i=n[a];return n.splice(a,1),n.splice(t,0,i),n},tp=function(e,a,t){var n=td(e),i=n[a];return n[a]=n[t],n[t]=i,n},tl=function(e,a,t){var n=td(e);return n.splice(a,0,t),n},tu=function(e,a,t){var n=td(e);return n[a]=t,n},td=function(e){if(!e)return[];if(Array.isArray(e))return[].concat(e);var a=Object.keys(e).map(function(e){return parseInt(e)}).reduce(function(e,a){return a>e?a:e},0);return Array.from(aZ({},e,{length:a+1}))},tm=function(e,a){var t="function"==typeof e?e:a;return function(e){return Array.isArray(e)||a6(e)?t(td(e)):e}};(function(e){function a(a){var t;return(t=e.call(this,a)||this).updateArrayField=function(e,a,n){var i=t.props,s=i.name;(0,i.formik.setFormikState)(function(t){var i=tm(n,e),o=tm(a,e),r=a5(t.values,s,e(a9(t.values,s))),c=n?i(a9(t.errors,s)):void 0,p=a?o(a9(t.touched,s)):void 0;return a2(c)&&(c=void 0),a2(p)&&(p=void 0),aZ({},t,{values:r,errors:n?a5(t.errors,s,c):t.errors,touched:a?a5(t.touched,s,p):t.touched})})},t.push=function(e){return t.updateArrayField(function(a){return[].concat(td(a),[aR(e)])},!1,!1)},t.handlePush=function(e){return function(){return t.push(e)}},t.swap=function(e,a){return t.updateArrayField(function(t){return tp(t,e,a)},!0,!0)},t.handleSwap=function(e,a){return function(){return t.swap(e,a)}},t.move=function(e,a){return t.updateArrayField(function(t){return tc(t,e,a)},!0,!0)},t.handleMove=function(e,a){return function(){return t.move(e,a)}},t.insert=function(e,a){return t.updateArrayField(function(t){return tl(t,e,a)},function(a){return tl(a,e,null)},function(a){return tl(a,e,null)})},t.handleInsert=function(e,a){return function(){return t.insert(e,a)}},t.replace=function(e,a){return t.updateArrayField(function(t){return tu(t,e,a)},!1,!1)},t.handleReplace=function(e,a){return function(){return t.replace(e,a)}},t.unshift=function(e){var a=-1;return t.updateArrayField(function(t){var n=t?[e].concat(t):[e];return a=n.length,n},function(e){return e?[null].concat(e):[null]},function(e){return e?[null].concat(e):[null]}),a},t.handleUnshift=function(e){return function(){return t.unshift(e)}},t.handleRemove=function(e){return function(){return t.remove(e)}},t.handlePop=function(){return function(){return t.pop()}},t.remove=t.remove.bind(aX(t)),t.pop=t.pop.bind(aX(t)),t}a.prototype=Object.create(e.prototype),a.prototype.constructor=a,a.__proto__=e;var t=a.prototype;return t.componentDidUpdate=function(e){this.props.validateOnChange&&this.props.formik.validateOnChange&&!aL()(a9(e.formik.values,e.name),a9(this.props.formik.values,this.props.name))&&this.props.formik.validateForm(this.props.formik.values)},t.remove=function(e){var a;return this.updateArrayField(function(t){var n=t?td(t):[];return a||(a=n[e]),a3(n.splice)&&n.splice(e,1),a3(n.every)&&n.every(function(e){return void 0===e})?[]:n},!0,!0),a},t.pop=function(){var e;return this.updateArrayField(function(a){var t=a.slice();return e||(e=t&&t.pop&&t.pop()),t},!0,!0),e},t.render=function(){var e={push:this.push,pop:this.pop,swap:this.swap,move:this.move,insert:this.insert,replace:this.replace,unshift:this.unshift,remove:this.remove,handlePush:this.handlePush,handlePop:this.handlePop,handleSwap:this.handleSwap,handleMove:this.handleMove,handleInsert:this.handleInsert,handleReplace:this.handleReplace,handleUnshift:this.handleUnshift,handleRemove:this.handleRemove},a=this.props,t=a.component,n=a.render,i=a.children,s=a.name,o=aY(a.formik,["validate","validationSchema"]),r=aZ({},e,{form:o,name:s});return t?(0,aA.createElement)(t,r):n?n(r):i?"function"==typeof i?i(r):a4(i)?null:aA.Children.only(i):null},a})(aA.Component).defaultProps={validateOnChange:!0}},53285:e=>{"use strict";e.exports=(e,a=process.argv)=>{let t=e.startsWith("-")?"":1===e.length?"-":"--",n=a.indexOf(t+e),i=a.indexOf("--");return -1!==n&&(-1===i||n<i)}},79997:(e,a,t)=>{"use strict";var n=t(16777),i={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},s={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},o={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},r={};function c(e){return n.isMemo(e)?o:r[e.$$typeof]||i}r[n.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},r[n.Memo]=o;var p=Object.defineProperty,l=Object.getOwnPropertyNames,u=Object.getOwnPropertySymbols,d=Object.getOwnPropertyDescriptor,m=Object.getPrototypeOf,f=Object.prototype;e.exports=function e(a,t,n){if("string"!=typeof t){if(f){var i=m(t);i&&i!==f&&e(a,i,n)}var o=l(t);u&&(o=o.concat(u(t)));for(var r=c(a),h=c(t),x=0;x<o.length;++x){var v=o[x];if(!s[v]&&!(n&&n[v])&&!(h&&h[v])&&!(r&&r[v])){var b=d(t,v);try{p(a,v,b)}catch(e){}}}}return a}},46866:(e,a,t)=>{e.exports=t(40572)},70045:(e,a,t)=>{"use strict";var n=t(46866),i=t(55315).extname,s=/^\s*([^;\s]*)(?:;|\s|$)/,o=/^text\//i;function r(e){if(!e||"string"!=typeof e)return!1;var a=s.exec(e),t=a&&n[a[1].toLowerCase()];return t&&t.charset?t.charset:!!(a&&o.test(a[1]))&&"UTF-8"}a.charset=r,a.charsets={lookup:r},a.contentType=function(e){if(!e||"string"!=typeof e)return!1;var t=-1===e.indexOf("/")?a.lookup(e):e;if(!t)return!1;if(-1===t.indexOf("charset")){var n=a.charset(t);n&&(t+="; charset="+n.toLowerCase())}return t},a.extension=function(e){if(!e||"string"!=typeof e)return!1;var t=s.exec(e),n=t&&a.extensions[t[1].toLowerCase()];return!!n&&!!n.length&&n[0]},a.extensions=Object.create(null),a.lookup=function(e){if(!e||"string"!=typeof e)return!1;var t=i("x."+e).toLowerCase().substr(1);return!!t&&(a.types[t]||!1)},a.types=Object.create(null),function(e,a){var t=["nginx","apache",void 0,"iana"];Object.keys(n).forEach(function(i){var s=n[i],o=s.extensions;if(o&&o.length){e[i]=o;for(var r=0;r<o.length;r++){var c=o[r];if(a[c]){var p=t.indexOf(n[a[c]].source),l=t.indexOf(s.source);if("application/octet-stream"!==a[c]&&(p>l||p===l&&"application/"===a[c].substr(0,12)))continue}a[c]=i}}})}(a.extensions,a.types)},87914:e=>{function a(e,a,t,n){return Math.round(e/t)+" "+n+(a>=1.5*t?"s":"")}e.exports=function(e,t){t=t||{};var n,i,s=typeof e;if("string"===s&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var a=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(a){var t=parseFloat(a[1]);switch((a[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*t;case"weeks":case"week":case"w":return 6048e5*t;case"days":case"day":case"d":return 864e5*t;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*t;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*t;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return t;default:return}}}}(e);if("number"===s&&isFinite(e))return t.long?(n=Math.abs(e))>=864e5?a(e,n,864e5,"day"):n>=36e5?a(e,n,36e5,"hour"):n>=6e4?a(e,n,6e4,"minute"):n>=1e3?a(e,n,1e3,"second"):e+" ms":(i=Math.abs(e))>=864e5?Math.round(e/864e5)+"d":i>=36e5?Math.round(e/36e5)+"h":i>=6e4?Math.round(e/6e4)+"m":i>=1e3?Math.round(e/1e3)+"s":e+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},76500:e=>{"use strict";function a(e){this._maxSize=e,this.clear()}a.prototype.clear=function(){this._size=0,this._values=Object.create(null)},a.prototype.get=function(e){return this._values[e]},a.prototype.set=function(e,a){return this._size>=this._maxSize&&this.clear(),!(e in this._values)&&this._size++,this._values[e]=a};var t=/[^.^\]^[]+|(?=\[\]|\.\.)/g,n=/^\d+$/,i=/^\d/,s=/[~`!#$%\^&*+=\-\[\]\\';,/{}|\\":<>\?]/g,o=/^\s*(['"]?)(.*?)(\1)\s*$/,r=new a(512),c=new a(512),p=new a(512);function l(e){return r.get(e)||r.set(e,u(e).map(function(e){return e.replace(o,"$2")}))}function u(e){return e.match(t)||[""]}function d(e){return"string"==typeof e&&e&&-1!==["'",'"'].indexOf(e.charAt(0))}e.exports={Cache:a,split:u,normalizePath:l,setter:function(e){var a=l(e);return c.get(e)||c.set(e,function(e,t){for(var n=0,i=a.length,s=e;n<i-1;){var o=a[n];if("__proto__"===o||"constructor"===o||"prototype"===o)return e;s=s[a[n++]]}s[a[n]]=t})},getter:function(e,a){var t=l(e);return p.get(e)||p.set(e,function(e){for(var n=0,i=t.length;n<i;){if(null==e&&a)return;e=e[t[n++]]}return e})},join:function(e){return e.reduce(function(e,a){return e+(d(a)||n.test(a)?"["+a+"]":(e?".":"")+a)},"")},forEach:function(e,a,t){(function(e,a,t){var o,r,c,p,l,u=e.length;for(c=0;c<u;c++){(r=e[c])&&(!d(o=r)&&(o.match(i)&&!o.match(n)||s.test(o))&&(r='"'+r+'"'),p=!(l=d(r))&&/^\d+$/.test(r),a.call(t,r,l,p,c,e))}})(Array.isArray(e)?e:u(e),a,t)}}},13304:(e,a,t)=>{"use strict";var n=t(17360).parse,i={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},s=String.prototype.endsWith||function(e){return e.length<=this.length&&-1!==this.indexOf(e,this.length-e.length)};function o(e){return process.env[e.toLowerCase()]||process.env[e.toUpperCase()]||""}a.getProxyForUrl=function(e){var a,t,r,c="string"==typeof e?n(e):e||{},p=c.protocol,l=c.host,u=c.port;if("string"!=typeof l||!l||"string"!=typeof p||(p=p.split(":",1)[0],a=l=l.replace(/:\d*$/,""),t=u=parseInt(u)||i[p]||0,!(!(r=(o("npm_config_no_proxy")||o("no_proxy")).toLowerCase())||"*"!==r&&r.split(/[,\s]/).every(function(e){if(!e)return!0;var n=e.match(/^(.+):(\d+)$/),i=n?n[1]:e,o=n?parseInt(n[2]):0;return!!o&&o!==t||(/^[.*]/.test(i)?("*"===i.charAt(0)&&(i=i.slice(1)),!s.call(a,i)):a!==i)}))))return"";var d=o("npm_config_"+p+"_proxy")||o(p+"_proxy")||o("npm_config_proxy")||o("all_proxy");return d&&-1===d.indexOf("://")&&(d=p+"://"+d),d}},86725:e=>{"use strict";var a=Array.isArray,t=Object.keys,n=Object.prototype.hasOwnProperty,i="undefined"!=typeof Element;e.exports=function(e,s){try{return function e(s,o){if(s===o)return!0;if(s&&o&&"object"==typeof s&&"object"==typeof o){var r,c,p,l=a(s),u=a(o);if(l&&u){if((c=s.length)!=o.length)return!1;for(r=c;0!=r--;)if(!e(s[r],o[r]))return!1;return!0}if(l!=u)return!1;var d=s instanceof Date,m=o instanceof Date;if(d!=m)return!1;if(d&&m)return s.getTime()==o.getTime();var f=s instanceof RegExp,h=o instanceof RegExp;if(f!=h)return!1;if(f&&h)return s.toString()==o.toString();var x=t(s);if((c=x.length)!==t(o).length)return!1;for(r=c;0!=r--;)if(!n.call(o,x[r]))return!1;if(i&&s instanceof Element&&o instanceof Element)return s===o;for(r=c;0!=r--;)if(("_owner"!==(p=x[r])||!s.$$typeof)&&!e(s[p],o[p]))return!1;return!0}return s!=s&&o!=o}(e,s)}catch(e){if(e.message&&e.message.match(/stack|recursion/i)||-**********===e.number)return console.warn("Warning: react-fast-compare does not handle circular references.",e.name,e.message),!1;throw e}}},20745:(e,a)=>{"use strict";var t="function"==typeof Symbol&&Symbol.for,n=t?Symbol.for("react.element"):60103,i=t?Symbol.for("react.portal"):60106,s=t?Symbol.for("react.fragment"):60107,o=t?Symbol.for("react.strict_mode"):60108,r=t?Symbol.for("react.profiler"):60114,c=t?Symbol.for("react.provider"):60109,p=t?Symbol.for("react.context"):60110,l=t?Symbol.for("react.async_mode"):60111,u=t?Symbol.for("react.concurrent_mode"):60111,d=t?Symbol.for("react.forward_ref"):60112,m=t?Symbol.for("react.suspense"):60113,f=t?Symbol.for("react.suspense_list"):60120,h=t?Symbol.for("react.memo"):60115,x=t?Symbol.for("react.lazy"):60116,v=t?Symbol.for("react.block"):60121,b=t?Symbol.for("react.fundamental"):60117,g=t?Symbol.for("react.responder"):60118,y=t?Symbol.for("react.scope"):60119;function w(e){if("object"==typeof e&&null!==e){var a=e.$$typeof;switch(a){case n:switch(e=e.type){case l:case u:case s:case r:case o:case m:return e;default:switch(e=e&&e.$$typeof){case p:case d:case x:case h:case c:return e;default:return a}}case i:return a}}}function _(e){return w(e)===u}a.AsyncMode=l,a.ConcurrentMode=u,a.ContextConsumer=p,a.ContextProvider=c,a.Element=n,a.ForwardRef=d,a.Fragment=s,a.Lazy=x,a.Memo=h,a.Portal=i,a.Profiler=r,a.StrictMode=o,a.Suspense=m,a.isAsyncMode=function(e){return _(e)||w(e)===l},a.isConcurrentMode=_,a.isContextConsumer=function(e){return w(e)===p},a.isContextProvider=function(e){return w(e)===c},a.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===n},a.isForwardRef=function(e){return w(e)===d},a.isFragment=function(e){return w(e)===s},a.isLazy=function(e){return w(e)===x},a.isMemo=function(e){return w(e)===h},a.isPortal=function(e){return w(e)===i},a.isProfiler=function(e){return w(e)===r},a.isStrictMode=function(e){return w(e)===o},a.isSuspense=function(e){return w(e)===m},a.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===s||e===u||e===r||e===o||e===m||e===f||"object"==typeof e&&null!==e&&(e.$$typeof===x||e.$$typeof===h||e.$$typeof===c||e.$$typeof===p||e.$$typeof===d||e.$$typeof===b||e.$$typeof===g||e.$$typeof===y||e.$$typeof===v)},a.typeOf=w},16777:(e,a,t)=>{"use strict";e.exports=t(20745)},67495:(e,a,t)=>{"use strict";let n;let i=t(19801),s=t(74175),o=t(53285),{env:r}=process;function c(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function p(e,a){if(0===n)return 0;if(o("color=16m")||o("color=full")||o("color=truecolor"))return 3;if(o("color=256"))return 2;if(e&&!a&&void 0===n)return 0;let t=n||0;if("dumb"===r.TERM)return t;if("win32"===process.platform){let e=i.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in r)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(e=>e in r)||"codeship"===r.CI_NAME?1:t;if("TEAMCITY_VERSION"in r)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(r.TEAMCITY_VERSION)?1:0;if("truecolor"===r.COLORTERM)return 3;if("TERM_PROGRAM"in r){let e=parseInt((r.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(r.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(r.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(r.TERM)||"COLORTERM"in r?1:t}o("no-color")||o("no-colors")||o("color=false")||o("color=never")?n=0:(o("color")||o("colors")||o("color=true")||o("color=always"))&&(n=1),"FORCE_COLOR"in r&&(n="true"===r.FORCE_COLOR?1:"false"===r.FORCE_COLOR?0:0===r.FORCE_COLOR.length?1:Math.min(parseInt(r.FORCE_COLOR,10),3)),e.exports={supportsColor:function(e){return c(p(e,e&&e.isTTY))},stdout:c(p(!0,s.isatty(1))),stderr:c(p(!0,s.isatty(2)))}},7352:e=>{let a=/[A-Z\xc0-\xd6\xd8-\xde]?[a-z\xdf-\xf6\xf8-\xff]+(?:['’](?:d|ll|m|re|s|t|ve))?(?=[\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000]|[A-Z\xc0-\xd6\xd8-\xde]|$)|(?:[A-Z\xc0-\xd6\xd8-\xde]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])+(?:['’](?:D|LL|M|RE|S|T|VE))?(?=[\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000]|[A-Z\xc0-\xd6\xd8-\xde](?:[a-z\xdf-\xf6\xf8-\xff]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])|$)|[A-Z\xc0-\xd6\xd8-\xde]?(?:[a-z\xdf-\xf6\xf8-\xff]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])+(?:['’](?:d|ll|m|re|s|t|ve))?|[A-Z\xc0-\xd6\xd8-\xde]+(?:['’](?:D|LL|M|RE|S|T|VE))?|\d*(?:1ST|2ND|3RD|(?![123])\dTH)(?=\b|[a-z_])|\d*(?:1st|2nd|3rd|(?![123])\dth)(?=\b|[A-Z_])|\d+|(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|\ud83c[\udffb-\udfff])?(?:\u200d(?:[^\ud800-\udfff]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|\ud83c[\udffb-\udfff])?)*/g,t=e=>e.match(a)||[],n=e=>e[0].toUpperCase()+e.slice(1),i=(e,a)=>t(e).join(a).toLowerCase(),s=e=>t(e).reduce((e,a)=>`${e}${e?a[0].toUpperCase()+a.slice(1).toLowerCase():a.toLowerCase()}`,"");e.exports={words:t,upperFirst:n,camelCase:s,pascalCase:e=>n(s(e)),snakeCase:e=>i(e,"_"),kebabCase:e=>i(e,"-"),sentenceCase:e=>n(i(e," ")),titleCase:e=>t(e).map(n).join(" ")}},96673:e=>{function a(e,a){var t=e.length,n=Array(t),i={},s=t,o=function(e){for(var a=new Map,t=0,n=e.length;t<n;t++){var i=e[t];a.has(i[0])||a.set(i[0],new Set),a.has(i[1])||a.set(i[1],new Set),a.get(i[0]).add(i[1])}return a}(a),r=function(e){for(var a=new Map,t=0,n=e.length;t<n;t++)a.set(e[t],t);return a}(e);for(a.forEach(function(e){if(!r.has(e[0])||!r.has(e[1]))throw Error("Unknown node. There is an unknown node in the supplied edges.")});s--;)i[s]||function e(a,s,c){if(c.has(a)){var p;try{p=", node was:"+JSON.stringify(a)}catch(e){p=""}throw Error("Cyclic dependency"+p)}if(!r.has(a))throw Error("Found unknown node. Make sure to provided all involved nodes. Unknown node: "+JSON.stringify(a));if(!i[s]){i[s]=!0;var l=o.get(a)||new Set;if(s=(l=Array.from(l)).length){c.add(a);do{var u=l[--s];e(u,r.get(u),c)}while(s);c.delete(a)}n[--t]=a}}(e[s],s,new Set);return n}e.exports=function(e){return a(function(e){for(var a=new Set,t=0,n=e.length;t<n;t++){var i=e[t];a.add(i[0]),a.add(i[1])}return Array.from(a)}(e),e)},e.exports.array=a},10123:(e,a,t)=>{"use strict";let n,i,s;t.d(a,{Ry:()=>eu,Z_:()=>Z});var o=t(76500),r=t(7352),c=t(96673),p=t.n(c);let l=Object.prototype.toString,u=Error.prototype.toString,d=RegExp.prototype.toString,m="undefined"!=typeof Symbol?Symbol.prototype.toString:()=>"",f=/^Symbol\((.*)\)(.*)$/;function h(e,a=!1){if(null==e||!0===e||!1===e)return""+e;let t=typeof e;if("number"===t)return e!=+e?"NaN":0===e&&1/e<0?"-0":""+e;if("string"===t)return a?`"${e}"`:e;if("function"===t)return"[Function "+(e.name||"anonymous")+"]";if("symbol"===t)return m.call(e).replace(f,"Symbol($1)");let n=l.call(e).slice(8,-1);return"Date"===n?isNaN(e.getTime())?""+e:e.toISOString(e):"Error"===n||e instanceof Error?"["+u.call(e)+"]":"RegExp"===n?d.call(e):null}function x(e,a){let t=h(e,a);return null!==t?t:JSON.stringify(e,function(e,t){let n=h(this[e],a);return null!==n?n:t},2)}function v(e){return null==e?[]:[].concat(e)}let b=/\$\{\s*(\w+)\s*\}/g;n=Symbol.toStringTag;class g{constructor(e,a,t,i){this.name=void 0,this.message=void 0,this.value=void 0,this.path=void 0,this.type=void 0,this.params=void 0,this.errors=void 0,this.inner=void 0,this[n]="Error",this.name="ValidationError",this.value=a,this.path=t,this.type=i,this.errors=[],this.inner=[],v(e).forEach(e=>{if(y.isError(e)){this.errors.push(...e.errors);let a=e.inner.length?e.inner:[e];this.inner.push(...a)}else this.errors.push(e)}),this.message=this.errors.length>1?`${this.errors.length} errors occurred`:this.errors[0]}}i=Symbol.hasInstance,s=Symbol.toStringTag;class y extends Error{static formatError(e,a){let t=a.label||a.path||"this";return(a=Object.assign({},a,{path:t,originalPath:a.path}),"string"==typeof e)?e.replace(b,(e,t)=>x(a[t])):"function"==typeof e?e(a):e}static isError(e){return e&&"ValidationError"===e.name}constructor(e,a,t,n,i){let o=new g(e,a,t,n);if(i)return o;super(),this.value=void 0,this.path=void 0,this.type=void 0,this.params=void 0,this.errors=[],this.inner=[],this[s]="Error",this.name=o.name,this.message=o.message,this.type=o.type,this.value=o.value,this.path=o.path,this.errors=o.errors,this.inner=o.inner,Error.captureStackTrace&&Error.captureStackTrace(this,y)}static[i](e){return g[Symbol.hasInstance](e)||super[Symbol.hasInstance](e)}}let w={default:"${path} is invalid",required:"${path} is a required field",defined:"${path} must be defined",notNull:"${path} cannot be null",oneOf:"${path} must be one of the following values: ${values}",notOneOf:"${path} must not be one of the following values: ${values}",notType:({path:e,type:a,value:t,originalValue:n})=>{let i=null!=n&&n!==t?` (cast from the value \`${x(n,!0)}\`).`:".";return"mixed"!==a?`${e} must be a \`${a}\` type, but the final value was: \`${x(t,!0)}\``+i:`${e} must match the configured type. The validated value was: \`${x(t,!0)}\``+i}},_={length:"${path} must be exactly ${length} characters",min:"${path} must be at least ${min} characters",max:"${path} must be at most ${max} characters",matches:'${path} must match the following: "${regex}"',email:"${path} must be a valid email",url:"${path} must be a valid URL",uuid:"${path} must be a valid UUID",datetime:"${path} must be a valid ISO date-time",datetime_precision:"${path} must be a valid ISO date-time with a sub-second precision of exactly ${precision} digits",datetime_offset:'${path} must be a valid ISO date-time with UTC "Z" timezone',trim:"${path} must be a trimmed string",lowercase:"${path} must be a lowercase string",uppercase:"${path} must be a upper case string"},j={min:"${path} must be greater than or equal to ${min}",max:"${path} must be less than or equal to ${max}",lessThan:"${path} must be less than ${less}",moreThan:"${path} must be greater than ${more}",positive:"${path} must be a positive number",negative:"${path} must be a negative number",integer:"${path} must be an integer"},k={min:"${path} field must be later than ${min}",max:"${path} field must be at earlier than ${max}"},E={isValue:"${path} field must be ${value}"},S={noUnknown:"${path} field has unspecified keys: ${unknown}",exact:"${path} object contains unknown properties: ${properties}"},O={min:"${path} field must have at least ${min} items",max:"${path} field must have less than or equal to ${max} items",length:"${path} must have ${length} items"},T={notType:e=>{let{path:a,value:t,spec:n}=e,i=n.types.length;if(Array.isArray(t)){if(t.length<i)return`${a} tuple value has too few items, expected a length of ${i} but got ${t.length} for value: \`${x(t,!0)}\``;if(t.length>i)return`${a} tuple value has too many items, expected a length of ${i} but got ${t.length} for value: \`${x(t,!0)}\``}return y.formatError(w.notType,e)}};Object.assign(Object.create(null),{mixed:w,string:_,number:j,date:k,object:S,array:O,boolean:E,tuple:T});let F=e=>e&&e.__isYupSchema__;class C{static fromOptions(e,a){if(!a.then&&!a.otherwise)throw TypeError("either `then:` or `otherwise:` is required for `when()` conditions");let{is:t,then:n,otherwise:i}=a,s="function"==typeof t?t:(...e)=>e.every(e=>e===t);return new C(e,(e,a)=>{var t;let o=s(...e)?n:i;return null!=(t=null==o?void 0:o(a))?t:a})}constructor(e,a){this.fn=void 0,this.refs=e,this.refs=e,this.fn=a}resolve(e,a){let t=this.refs.map(e=>e.getValue(null==a?void 0:a.value,null==a?void 0:a.parent,null==a?void 0:a.context)),n=this.fn(t,e,a);if(void 0===n||n===e)return e;if(!F(n))throw TypeError("conditions must return a schema object");return n.resolve(a)}}let R={context:"$",value:"."};class A{constructor(e,a={}){if(this.key=void 0,this.isContext=void 0,this.isValue=void 0,this.isSibling=void 0,this.path=void 0,this.getter=void 0,this.map=void 0,"string"!=typeof e)throw TypeError("ref must be a string, got: "+e);if(this.key=e.trim(),""===e)throw TypeError("ref must be a non-empty string");this.isContext=this.key[0]===R.context,this.isValue=this.key[0]===R.value,this.isSibling=!this.isContext&&!this.isValue;let t=this.isContext?R.context:this.isValue?R.value:"";this.path=this.key.slice(t.length),this.getter=this.path&&(0,o.getter)(this.path,!0),this.map=a.map}getValue(e,a,t){let n=this.isContext?t:this.isValue?e:a;return this.getter&&(n=this.getter(n||{})),this.map&&(n=this.map(n)),n}cast(e,a){return this.getValue(e,null==a?void 0:a.parent,null==a?void 0:a.context)}resolve(){return this}describe(){return{type:"ref",key:this.key}}toString(){return`Ref(${this.key})`}static isRef(e){return e&&e.__isYupRef}}A.prototype.__isYupRef=!0;let z=e=>null==e;function L(e){function a({value:a,path:t="",options:n,originalValue:i,schema:s},o,r){let c;let{name:p,test:l,params:u,message:d,skipAbsent:m}=e,{parent:f,context:h,abortEarly:x=s.spec.abortEarly,disableStackTrace:v=s.spec.disableStackTrace}=n;function b(e){return A.isRef(e)?e.getValue(a,f,h):e}function g(e={}){let n=Object.assign({value:a,originalValue:i,label:s.spec.label,path:e.path||t,spec:s.spec,disableStackTrace:e.disableStackTrace||v},u,e.params);for(let e of Object.keys(n))n[e]=b(n[e]);let o=new y(y.formatError(e.message||d,n),a,n.path,e.type||p,n.disableStackTrace);return o.params=n,o}let w=x?o:r,_={path:t,parent:f,type:p,from:n.from,createError:g,resolve:b,options:n,originalValue:i,schema:s},j=e=>{y.isError(e)?w(e):e?r(null):w(g())},k=e=>{y.isError(e)?w(e):o(e)};if(m&&z(a))return j(!0);try{var E;if(c=l.call(_,a,_),"function"==typeof(null==(E=c)?void 0:E.then)){if(n.sync)throw Error(`Validation test of type: "${_.type}" returned a Promise during a synchronous validate. This test will finish after the validate call has returned`);return Promise.resolve(c).then(j,k)}}catch(e){k(e);return}j(c)}return a.OPTIONS=e,a}class D extends Set{describe(){let e=[];for(let a of this.values())e.push(A.isRef(a)?a.describe():a);return e}resolveAll(e){let a=[];for(let t of this.values())a.push(e(t));return a}clone(){return new D(this.values())}merge(e,a){let t=this.clone();return e.forEach(e=>t.add(e)),a.forEach(e=>t.delete(e)),t}}function U(e,a=new Map){let t;if(F(e)||!e||"object"!=typeof e)return e;if(a.has(e))return a.get(e);if(e instanceof Date)t=new Date(e.getTime()),a.set(e,t);else if(e instanceof RegExp)t=new RegExp(e),a.set(e,t);else if(Array.isArray(e)){t=Array(e.length),a.set(e,t);for(let n=0;n<e.length;n++)t[n]=U(e[n],a)}else if(e instanceof Map)for(let[n,i]of(t=new Map,a.set(e,t),e.entries()))t.set(n,U(i,a));else if(e instanceof Set)for(let n of(t=new Set,a.set(e,t),e))t.add(U(n,a));else if(e instanceof Object)for(let[n,i]of(t={},a.set(e,t),Object.entries(e)))t[n]=U(i,a);else throw Error(`Unable to clone ${e}`);return t}class N{constructor(e){this.type=void 0,this.deps=[],this.tests=void 0,this.transforms=void 0,this.conditions=[],this._mutate=void 0,this.internalTests={},this._whitelist=new D,this._blacklist=new D,this.exclusiveTests=Object.create(null),this._typeCheck=void 0,this.spec=void 0,this.tests=[],this.transforms=[],this.withMutation(()=>{this.typeError(w.notType)}),this.type=e.type,this._typeCheck=e.check,this.spec=Object.assign({strip:!1,strict:!1,abortEarly:!0,recursive:!0,disableStackTrace:!1,nullable:!1,optional:!0,coerce:!0},null==e?void 0:e.spec),this.withMutation(e=>{e.nonNullable()})}get _type(){return this.type}clone(e){if(this._mutate)return e&&Object.assign(this.spec,e),this;let a=Object.create(Object.getPrototypeOf(this));return a.type=this.type,a._typeCheck=this._typeCheck,a._whitelist=this._whitelist.clone(),a._blacklist=this._blacklist.clone(),a.internalTests=Object.assign({},this.internalTests),a.exclusiveTests=Object.assign({},this.exclusiveTests),a.deps=[...this.deps],a.conditions=[...this.conditions],a.tests=[...this.tests],a.transforms=[...this.transforms],a.spec=U(Object.assign({},this.spec,e)),a}label(e){let a=this.clone();return a.spec.label=e,a}meta(...e){if(0===e.length)return this.spec.meta;let a=this.clone();return a.spec.meta=Object.assign(a.spec.meta||{},e[0]),a}withMutation(e){let a=this._mutate;this._mutate=!0;let t=e(this);return this._mutate=a,t}concat(e){if(!e||e===this)return this;if(e.type!==this.type&&"mixed"!==this.type)throw TypeError(`You cannot \`concat()\` schema's of different types: ${this.type} and ${e.type}`);let a=e.clone(),t=Object.assign({},this.spec,a.spec);return a.spec=t,a.internalTests=Object.assign({},this.internalTests,a.internalTests),a._whitelist=this._whitelist.merge(e._whitelist,e._blacklist),a._blacklist=this._blacklist.merge(e._blacklist,e._whitelist),a.tests=this.tests,a.exclusiveTests=this.exclusiveTests,a.withMutation(a=>{e.tests.forEach(e=>{a.test(e.OPTIONS)})}),a.transforms=[...this.transforms,...a.transforms],a}isType(e){return null==e?!!this.spec.nullable&&null===e||!!this.spec.optional&&void 0===e:this._typeCheck(e)}resolve(e){let a=this;if(a.conditions.length){let t=a.conditions;(a=a.clone()).conditions=[],a=(a=t.reduce((a,t)=>t.resolve(a,e),a)).resolve(e)}return a}resolveOptions(e){var a,t,n,i;return Object.assign({},e,{from:e.from||[],strict:null!=(a=e.strict)?a:this.spec.strict,abortEarly:null!=(t=e.abortEarly)?t:this.spec.abortEarly,recursive:null!=(n=e.recursive)?n:this.spec.recursive,disableStackTrace:null!=(i=e.disableStackTrace)?i:this.spec.disableStackTrace})}cast(e,a={}){let t=this.resolve(Object.assign({value:e},a)),n="ignore-optionality"===a.assert,i=t._cast(e,a);if(!1!==a.assert&&!t.isType(i)){if(n&&z(i))return i;let s=x(e),o=x(i);throw TypeError(`The value of ${a.path||"field"} could not be cast to a value that satisfies the schema type: "${t.type}". 

attempted value: ${s} 
`+(o!==s?`result of cast: ${o}`:""))}return i}_cast(e,a){let t=void 0===e?e:this.transforms.reduce((a,t)=>t.call(this,a,e,this),e);return void 0===t&&(t=this.getDefault(a)),t}_validate(e,a={},t,n){let{path:i,originalValue:s=e,strict:o=this.spec.strict}=a,r=e;o||(r=this._cast(r,Object.assign({assert:!1},a)));let c=[];for(let e of Object.values(this.internalTests))e&&c.push(e);this.runTests({path:i,value:r,originalValue:s,options:a,tests:c},t,e=>{if(e.length)return n(e,r);this.runTests({path:i,value:r,originalValue:s,options:a,tests:this.tests},t,n)})}runTests(e,a,t){let n=!1,{tests:i,value:s,originalValue:o,path:r,options:c}=e,p=e=>{n||(n=!0,a(e,s))},l=e=>{n||(n=!0,t(e,s))},u=i.length,d=[];if(!u)return l([]);let m={value:s,originalValue:o,path:r,options:c,schema:this};for(let e=0;e<i.length;e++)(0,i[e])(m,p,function(e){e&&(Array.isArray(e)?d.push(...e):d.push(e)),--u<=0&&l(d)})}asNestedTest({key:e,index:a,parent:t,parentPath:n,originalParent:i,options:s}){let o=null!=e?e:a;if(null==o)throw TypeError("Must include `key` or `index` for nested validations");let r="number"==typeof o,c=t[o],p=Object.assign({},s,{strict:!0,parent:t,value:c,originalValue:i[o],key:void 0,[r?"index":"key"]:o,path:r||o.includes(".")?`${n||""}[${r?o:`"${o}"`}]`:(n?`${n}.`:"")+e});return(e,a,t)=>this.resolve(p)._validate(c,p,a,t)}validate(e,a){var t;let n=this.resolve(Object.assign({},a,{value:e})),i=null!=(t=null==a?void 0:a.disableStackTrace)?t:n.spec.disableStackTrace;return new Promise((t,s)=>n._validate(e,a,(e,a)=>{y.isError(e)&&(e.value=a),s(e)},(e,a)=>{e.length?s(new y(e,a,void 0,void 0,i)):t(a)}))}validateSync(e,a){var t;let n;let i=this.resolve(Object.assign({},a,{value:e})),s=null!=(t=null==a?void 0:a.disableStackTrace)?t:i.spec.disableStackTrace;return i._validate(e,Object.assign({},a,{sync:!0}),(e,a)=>{throw y.isError(e)&&(e.value=a),e},(a,t)=>{if(a.length)throw new y(a,e,void 0,void 0,s);n=t}),n}isValid(e,a){return this.validate(e,a).then(()=>!0,e=>{if(y.isError(e))return!1;throw e})}isValidSync(e,a){try{return this.validateSync(e,a),!0}catch(e){if(y.isError(e))return!1;throw e}}_getDefault(e){let a=this.spec.default;return null==a?a:"function"==typeof a?a.call(this,e):U(a)}getDefault(e){return this.resolve(e||{})._getDefault(e)}default(e){return 0==arguments.length?this._getDefault():this.clone({default:e})}strict(e=!0){return this.clone({strict:e})}nullability(e,a){let t=this.clone({nullable:e});return t.internalTests.nullable=L({message:a,name:"nullable",test(e){return null!==e||this.schema.spec.nullable}}),t}optionality(e,a){let t=this.clone({optional:e});return t.internalTests.optionality=L({message:a,name:"optionality",test(e){return void 0!==e||this.schema.spec.optional}}),t}optional(){return this.optionality(!0)}defined(e=w.defined){return this.optionality(!1,e)}nullable(){return this.nullability(!0)}nonNullable(e=w.notNull){return this.nullability(!1,e)}required(e=w.required){return this.clone().withMutation(a=>a.nonNullable(e).defined(e))}notRequired(){return this.clone().withMutation(e=>e.nullable().optional())}transform(e){let a=this.clone();return a.transforms.push(e),a}test(...e){let a;if(void 0===(a=1===e.length?"function"==typeof e[0]?{test:e[0]}:e[0]:2===e.length?{name:e[0],test:e[1]}:{name:e[0],message:e[1],test:e[2]}).message&&(a.message=w.default),"function"!=typeof a.test)throw TypeError("`test` is a required parameters");let t=this.clone(),n=L(a),i=a.exclusive||a.name&&!0===t.exclusiveTests[a.name];if(a.exclusive&&!a.name)throw TypeError("Exclusive tests must provide a unique `name` identifying the test");return a.name&&(t.exclusiveTests[a.name]=!!a.exclusive),t.tests=t.tests.filter(e=>e.OPTIONS.name!==a.name||!i&&e.OPTIONS.test!==n.OPTIONS.test),t.tests.push(n),t}when(e,a){Array.isArray(e)||"string"==typeof e||(a=e,e=".");let t=this.clone(),n=v(e).map(e=>new A(e));return n.forEach(e=>{e.isSibling&&t.deps.push(e.key)}),t.conditions.push("function"==typeof a?new C(n,a):C.fromOptions(n,a)),t}typeError(e){let a=this.clone();return a.internalTests.typeError=L({message:e,name:"typeError",skipAbsent:!0,test(e){return!!this.schema._typeCheck(e)||this.createError({params:{type:this.schema.type}})}}),a}oneOf(e,a=w.oneOf){let t=this.clone();return e.forEach(e=>{t._whitelist.add(e),t._blacklist.delete(e)}),t.internalTests.whiteList=L({message:a,name:"oneOf",skipAbsent:!0,test(e){let a=this.schema._whitelist,t=a.resolveAll(this.resolve);return!!t.includes(e)||this.createError({params:{values:Array.from(a).join(", "),resolved:t}})}}),t}notOneOf(e,a=w.notOneOf){let t=this.clone();return e.forEach(e=>{t._blacklist.add(e),t._whitelist.delete(e)}),t.internalTests.blacklist=L({message:a,name:"notOneOf",test(e){let a=this.schema._blacklist,t=a.resolveAll(this.resolve);return!t.includes(e)||this.createError({params:{values:Array.from(a).join(", "),resolved:t}})}}),t}strip(e=!0){let a=this.clone();return a.spec.strip=e,a}describe(e){let a=(e?this.resolve(e):this).clone(),{label:t,meta:n,optional:i,nullable:s}=a.spec;return{meta:n,label:t,optional:i,nullable:s,default:a.getDefault(e),type:a.type,oneOf:a._whitelist.describe(),notOneOf:a._blacklist.describe(),tests:a.tests.map(e=>({name:e.OPTIONS.name,params:e.OPTIONS.params})).filter((e,a,t)=>t.findIndex(a=>a.name===e.name)===a)}}}for(let e of(N.prototype.__isYupSchema__=!0,["validate","validateSync"]))N.prototype[`${e}At`]=function(a,t,n={}){let{parent:i,parentPath:s,schema:r}=function(e,a,t,n=t){let i,s,r;return a?((0,o.forEach)(a,(o,c,p)=>{let l=c?o.slice(1,o.length-1):o,u="tuple"===(e=e.resolve({context:n,parent:i,value:t})).type,d=p?parseInt(l,10):0;if(e.innerType||u){if(u&&!p)throw Error(`Yup.reach cannot implicitly index into a tuple type. the path part "${r}" must contain an index to the tuple element, e.g. "${r}[0]"`);if(t&&d>=t.length)throw Error(`Yup.reach cannot resolve an array item at index: ${o}, in the path: ${a}. because there is no value at that index. `);i=t,t=t&&t[d],e=u?e.spec.types[d]:e.innerType}if(!p){if(!e.fields||!e.fields[l])throw Error(`The schema does not contain the path: ${a}. (failed at: ${r} which is a type: "${e.type}")`);i=t,t=t&&t[l],e=e.fields[l]}s=l,r=c?"["+o+"]":"."+o}),{schema:e,parent:i,parentPath:s}):{parent:i,parentPath:a,schema:e}}(this,a,t,n.context);return r[e](i&&i[s],Object.assign({},n,{parent:i,path:a}))};for(let e of["equals","is"])N.prototype[e]=N.prototype.oneOf;for(let e of["not","nope"])N.prototype[e]=N.prototype.notOneOf;let P=()=>!0;class B extends N{constructor(e){super("function"==typeof e?{type:"mixed",check:e}:Object.assign({type:"mixed",check:P},e))}}B.prototype;class q extends N{constructor(){super({type:"boolean",check:e=>(e instanceof Boolean&&(e=e.valueOf()),"boolean"==typeof e)}),this.withMutation(()=>{this.transform((e,a,t)=>{if(t.spec.coerce&&!t.isType(e)){if(/^(true|1)$/i.test(String(e)))return!0;if(/^(false|0)$/i.test(String(e)))return!1}return e})})}isTrue(e=E.isValue){return this.test({message:e,name:"is-value",exclusive:!0,params:{value:"true"},test:e=>z(e)||!0===e})}isFalse(e=E.isValue){return this.test({message:e,name:"is-value",exclusive:!0,params:{value:"false"},test:e=>z(e)||!1===e})}default(e){return super.default(e)}defined(e){return super.defined(e)}optional(){return super.optional()}required(e){return super.required(e)}notRequired(){return super.notRequired()}nullable(){return super.nullable()}nonNullable(e){return super.nonNullable(e)}strip(e){return super.strip(e)}}q.prototype;let $=/^(\d{4}|[+-]\d{6})(?:-?(\d{2})(?:-?(\d{2}))?)?(?:[ T]?(\d{2}):?(\d{2})(?::?(\d{2})(?:[,.](\d{1,}))?)?(?:(Z)|([+-])(\d{2})(?::?(\d{2}))?)?)?$/;function I(e){var a,t;let n=$.exec(e);return n?{year:M(n[1]),month:M(n[2],1)-1,day:M(n[3],1),hour:M(n[4]),minute:M(n[5]),second:M(n[6]),millisecond:n[7]?M(n[7].substring(0,3)):0,precision:null!=(a=null==(t=n[7])?void 0:t.length)?a:void 0,z:n[8]||void 0,plusMinus:n[9]||void 0,hourOffset:M(n[10]),minuteOffset:M(n[11])}:null}function M(e,a=0){return Number(e)||a}let V=/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,H=/^((https?|ftp):)?\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(\#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i,W=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,G=RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?(([+-]\\d{2}(:?\\d{2})?)|Z)$"),K=e=>z(e)||e===e.trim(),J=({}).toString();function Z(){return new Y}class Y extends N{constructor(){super({type:"string",check:e=>(e instanceof String&&(e=e.valueOf()),"string"==typeof e)}),this.withMutation(()=>{this.transform((e,a,t)=>{if(!t.spec.coerce||t.isType(e)||Array.isArray(e))return e;let n=null!=e&&e.toString?e.toString():e;return n===J?e:n})})}required(e){return super.required(e).withMutation(a=>a.test({message:e||w.required,name:"required",skipAbsent:!0,test:e=>!!e.length}))}notRequired(){return super.notRequired().withMutation(e=>(e.tests=e.tests.filter(e=>"required"!==e.OPTIONS.name),e))}length(e,a=_.length){return this.test({message:a,name:"length",exclusive:!0,params:{length:e},skipAbsent:!0,test(a){return a.length===this.resolve(e)}})}min(e,a=_.min){return this.test({message:a,name:"min",exclusive:!0,params:{min:e},skipAbsent:!0,test(a){return a.length>=this.resolve(e)}})}max(e,a=_.max){return this.test({name:"max",exclusive:!0,message:a,params:{max:e},skipAbsent:!0,test(a){return a.length<=this.resolve(e)}})}matches(e,a){let t,n,i=!1;return a&&("object"==typeof a?{excludeEmptyString:i=!1,message:t,name:n}=a:t=a),this.test({name:n||"matches",message:t||_.matches,params:{regex:e},skipAbsent:!0,test:a=>""===a&&i||-1!==a.search(e)})}email(e=_.email){return this.matches(V,{name:"email",message:e,excludeEmptyString:!0})}url(e=_.url){return this.matches(H,{name:"url",message:e,excludeEmptyString:!0})}uuid(e=_.uuid){return this.matches(W,{name:"uuid",message:e,excludeEmptyString:!1})}datetime(e){let a,t,n="";return e&&("object"==typeof e?{message:n="",allowOffset:a=!1,precision:t}=e:n=e),this.matches(G,{name:"datetime",message:n||_.datetime,excludeEmptyString:!0}).test({name:"datetime_offset",message:n||_.datetime_offset,params:{allowOffset:a},skipAbsent:!0,test:e=>{if(!e||a)return!0;let t=I(e);return!!t&&!!t.z}}).test({name:"datetime_precision",message:n||_.datetime_precision,params:{precision:t},skipAbsent:!0,test:e=>{if(!e||void 0==t)return!0;let a=I(e);return!!a&&a.precision===t}})}ensure(){return this.default("").transform(e=>null===e?"":e)}trim(e=_.trim){return this.transform(e=>null!=e?e.trim():e).test({message:e,name:"trim",test:K})}lowercase(e=_.lowercase){return this.transform(e=>z(e)?e:e.toLowerCase()).test({message:e,name:"string_case",exclusive:!0,skipAbsent:!0,test:e=>z(e)||e===e.toLowerCase()})}uppercase(e=_.uppercase){return this.transform(e=>z(e)?e:e.toUpperCase()).test({message:e,name:"string_case",exclusive:!0,skipAbsent:!0,test:e=>z(e)||e===e.toUpperCase()})}}Z.prototype=Y.prototype;let X=e=>e!=+e;class Q extends N{constructor(){super({type:"number",check:e=>(e instanceof Number&&(e=e.valueOf()),"number"==typeof e&&!X(e))}),this.withMutation(()=>{this.transform((e,a,t)=>{if(!t.spec.coerce)return e;let n=e;if("string"==typeof n){if(""===(n=n.replace(/\s/g,"")))return NaN;n=+n}return t.isType(n)||null===n?n:parseFloat(n)})})}min(e,a=j.min){return this.test({message:a,name:"min",exclusive:!0,params:{min:e},skipAbsent:!0,test(a){return a>=this.resolve(e)}})}max(e,a=j.max){return this.test({message:a,name:"max",exclusive:!0,params:{max:e},skipAbsent:!0,test(a){return a<=this.resolve(e)}})}lessThan(e,a=j.lessThan){return this.test({message:a,name:"max",exclusive:!0,params:{less:e},skipAbsent:!0,test(a){return a<this.resolve(e)}})}moreThan(e,a=j.moreThan){return this.test({message:a,name:"min",exclusive:!0,params:{more:e},skipAbsent:!0,test(a){return a>this.resolve(e)}})}positive(e=j.positive){return this.moreThan(0,e)}negative(e=j.negative){return this.lessThan(0,e)}integer(e=j.integer){return this.test({name:"integer",message:e,skipAbsent:!0,test:e=>Number.isInteger(e)})}truncate(){return this.transform(e=>z(e)?e:0|e)}round(e){var a;let t=["ceil","floor","round","trunc"];if("trunc"===(e=(null==(a=e)?void 0:a.toLowerCase())||"round"))return this.truncate();if(-1===t.indexOf(e.toLowerCase()))throw TypeError("Only valid options for round() are: "+t.join(", "));return this.transform(a=>z(a)?a:Math[e](a))}}Q.prototype;let ee=new Date(""),ea=e=>"[object Date]"===Object.prototype.toString.call(e);function et(){return new en}class en extends N{constructor(){super({type:"date",check:e=>ea(e)&&!isNaN(e.getTime())}),this.withMutation(()=>{this.transform((e,a,t)=>!t.spec.coerce||t.isType(e)||null===e?e:isNaN(e=function(e){let a=I(e);if(!a)return Date.parse?Date.parse(e):Number.NaN;if(void 0===a.z&&void 0===a.plusMinus)return new Date(a.year,a.month,a.day,a.hour,a.minute,a.second,a.millisecond).valueOf();let t=0;return"Z"!==a.z&&void 0!==a.plusMinus&&(t=60*a.hourOffset+a.minuteOffset,"+"===a.plusMinus&&(t=0-t)),Date.UTC(a.year,a.month,a.day,a.hour,a.minute+t,a.second,a.millisecond)}(e))?en.INVALID_DATE:new Date(e))})}prepareParam(e,a){let t;if(A.isRef(e))t=e;else{let n=this.cast(e);if(!this._typeCheck(n))throw TypeError(`\`${a}\` must be a Date or a value that can be \`cast()\` to a Date`);t=n}return t}min(e,a=k.min){let t=this.prepareParam(e,"min");return this.test({message:a,name:"min",exclusive:!0,params:{min:e},skipAbsent:!0,test(e){return e>=this.resolve(t)}})}max(e,a=k.max){let t=this.prepareParam(e,"max");return this.test({message:a,name:"max",exclusive:!0,params:{max:e},skipAbsent:!0,test(e){return e<=this.resolve(t)}})}}function ei(e,a){let t=1/0;return e.some((e,n)=>{var i;if(null!=(i=a.path)&&i.includes(e))return t=n,!0}),t}function es(e){return(a,t)=>ei(e,a)-ei(e,t)}en.INVALID_DATE=ee,et.prototype=en.prototype,et.INVALID_DATE=ee;let eo=(e,a,t)=>{if("string"!=typeof e)return e;let n=e;try{n=JSON.parse(e)}catch(e){}return t.isType(n)?n:e},er=(e,a)=>{let t=[...(0,o.normalizePath)(a)];if(1===t.length)return t[0]in e;let n=t.pop(),i=(0,o.getter)((0,o.join)(t),!0)(e);return!!(i&&n in i)},ec=e=>"[object Object]"===Object.prototype.toString.call(e);function ep(e,a){let t=Object.keys(e.fields);return Object.keys(a).filter(e=>-1===t.indexOf(e))}let el=es([]);function eu(e){return new ed(e)}class ed extends N{constructor(e){super({type:"object",check:e=>ec(e)||"function"==typeof e}),this.fields=Object.create(null),this._sortErrors=el,this._nodes=[],this._excludedEdges=[],this.withMutation(()=>{e&&this.shape(e)})}_cast(e,a={}){var t;let n=super._cast(e,a);if(void 0===n)return this.getDefault(a);if(!this._typeCheck(n))return n;let i=this.fields,s=null!=(t=a.stripUnknown)?t:this.spec.noUnknown,o=[].concat(this._nodes,Object.keys(n).filter(e=>!this._nodes.includes(e))),r={},c=Object.assign({},a,{parent:r,__validating:a.__validating||!1}),p=!1;for(let e of o){let t=i[e],o=e in n;if(t){let i;let s=n[e];c.path=(a.path?`${a.path}.`:"")+e;let o=(t=t.resolve({value:s,context:a.context,parent:r}))instanceof N?t.spec:void 0,l=null==o?void 0:o.strict;if(null!=o&&o.strip){p=p||e in n;continue}void 0!==(i=a.__validating&&l?n[e]:t.cast(n[e],c))&&(r[e]=i)}else o&&!s&&(r[e]=n[e]);(o!==e in r||r[e]!==n[e])&&(p=!0)}return p?r:n}_validate(e,a={},t,n){let{from:i=[],originalValue:s=e,recursive:o=this.spec.recursive}=a;a.from=[{schema:this,value:s},...i],a.__validating=!0,a.originalValue=s,super._validate(e,a,t,(e,i)=>{if(!o||!ec(i)){n(e,i);return}s=s||i;let r=[];for(let e of this._nodes){let t=this.fields[e];!t||A.isRef(t)||r.push(t.asNestedTest({options:a,key:e,parent:i,parentPath:a.path,originalParent:s}))}this.runTests({tests:r,value:i,originalValue:s,options:a},t,a=>{n(a.sort(this._sortErrors).concat(e),i)})})}clone(e){let a=super.clone(e);return a.fields=Object.assign({},this.fields),a._nodes=this._nodes,a._excludedEdges=this._excludedEdges,a._sortErrors=this._sortErrors,a}concat(e){let a=super.concat(e),t=a.fields;for(let[e,a]of Object.entries(this.fields)){let n=t[e];t[e]=void 0===n?a:n}return a.withMutation(a=>a.setFields(t,[...this._excludedEdges,...e._excludedEdges]))}_getDefault(e){if("default"in this.spec)return super._getDefault(e);if(!this._nodes.length)return;let a={};return this._nodes.forEach(t=>{var n;let i=this.fields[t],s=e;null!=(n=s)&&n.value&&(s=Object.assign({},s,{parent:s.value,value:s.value[t]})),a[t]=i&&"getDefault"in i?i.getDefault(s):void 0}),a}setFields(e,a){let t=this.clone();return t.fields=e,t._nodes=function(e,a=[]){let t=[],n=new Set,i=new Set(a.map(([e,a])=>`${e}-${a}`));function s(e,a){let s=(0,o.split)(e)[0];n.add(s),i.has(`${a}-${s}`)||t.push([a,s])}for(let a of Object.keys(e)){let t=e[a];n.add(a),A.isRef(t)&&t.isSibling?s(t.path,a):F(t)&&"deps"in t&&t.deps.forEach(e=>s(e,a))}return p().array(Array.from(n),t).reverse()}(e,a),t._sortErrors=es(Object.keys(e)),a&&(t._excludedEdges=a),t}shape(e,a=[]){return this.clone().withMutation(t=>{let n=t._excludedEdges;return a.length&&(Array.isArray(a[0])||(a=[a]),n=[...t._excludedEdges,...a]),t.setFields(Object.assign(t.fields,e),n)})}partial(){let e={};for(let[a,t]of Object.entries(this.fields))e[a]="optional"in t&&t.optional instanceof Function?t.optional():t;return this.setFields(e)}deepPartial(){return function e(a){if("fields"in a){let t={};for(let[n,i]of Object.entries(a.fields))t[n]=e(i);return a.setFields(t)}if("array"===a.type){let t=a.optional();return t.innerType&&(t.innerType=e(t.innerType)),t}return"tuple"===a.type?a.optional().clone({types:a.spec.types.map(e)}):"optional"in a?a.optional():a}(this)}pick(e){let a={};for(let t of e)this.fields[t]&&(a[t]=this.fields[t]);return this.setFields(a,this._excludedEdges.filter(([a,t])=>e.includes(a)&&e.includes(t)))}omit(e){let a=[];for(let t of Object.keys(this.fields))e.includes(t)||a.push(t);return this.pick(a)}from(e,a,t){let n=(0,o.getter)(e,!0);return this.transform(i=>{if(!i)return i;let s=i;return er(i,e)&&(s=Object.assign({},i),t||delete s[e],s[a]=n(i)),s})}json(){return this.transform(eo)}exact(e){return this.test({name:"exact",exclusive:!0,message:e||S.exact,test(e){if(null==e)return!0;let a=ep(this.schema,e);return 0===a.length||this.createError({params:{properties:a.join(", ")}})}})}stripUnknown(){return this.clone({noUnknown:!0})}noUnknown(e=!0,a=S.noUnknown){"boolean"!=typeof e&&(a=e,e=!0);let t=this.test({name:"noUnknown",exclusive:!0,message:a,test(a){if(null==a)return!0;let t=ep(this.schema,a);return!e||0===t.length||this.createError({params:{unknown:t.join(", ")}})}});return t.spec.noUnknown=e,t}unknown(e=!0,a=S.noUnknown){return this.noUnknown(!e,a)}transformKeys(e){return this.transform(a=>{if(!a)return a;let t={};for(let n of Object.keys(a))t[e(n)]=a[n];return t})}camelCase(){return this.transformKeys(r.camelCase)}snakeCase(){return this.transformKeys(r.snakeCase)}constantCase(){return this.transformKeys(e=>(0,r.snakeCase)(e).toUpperCase())}describe(e){let a=(e?this.resolve(e):this).clone(),t=super.describe(e);for(let[i,s]of(t.fields={},Object.entries(a.fields))){var n;let a=e;null!=(n=a)&&n.value&&(a=Object.assign({},a,{parent:a.value,value:a.value[i]})),t.fields[i]=s.describe(a)}return t}}eu.prototype=ed.prototype;class em extends N{constructor(e){super({type:"array",spec:{types:e},check:e=>Array.isArray(e)}),this.innerType=void 0,this.innerType=e}_cast(e,a){let t=super._cast(e,a);if(!this._typeCheck(t)||!this.innerType)return t;let n=!1,i=t.map((e,t)=>{let i=this.innerType.cast(e,Object.assign({},a,{path:`${a.path||""}[${t}]`}));return i!==e&&(n=!0),i});return n?i:t}_validate(e,a={},t,n){var i;let s=this.innerType,o=null!=(i=a.recursive)?i:this.spec.recursive;null!=a.originalValue&&a.originalValue,super._validate(e,a,t,(i,r)=>{var c,p;if(!o||!s||!this._typeCheck(r)){n(i,r);return}let l=Array(r.length);for(let t=0;t<r.length;t++)l[t]=s.asNestedTest({options:a,index:t,parent:r,parentPath:a.path,originalParent:null!=(p=a.originalValue)?p:e});this.runTests({value:r,tests:l,originalValue:null!=(c=a.originalValue)?c:e,options:a},t,e=>n(e.concat(i),r))})}clone(e){let a=super.clone(e);return a.innerType=this.innerType,a}json(){return this.transform(eo)}concat(e){let a=super.concat(e);return a.innerType=this.innerType,e.innerType&&(a.innerType=a.innerType?a.innerType.concat(e.innerType):e.innerType),a}of(e){let a=this.clone();if(!F(e))throw TypeError("`array.of()` sub-schema must be a valid yup schema not: "+x(e));return a.innerType=e,a.spec=Object.assign({},a.spec,{types:e}),a}length(e,a=O.length){return this.test({message:a,name:"length",exclusive:!0,params:{length:e},skipAbsent:!0,test(a){return a.length===this.resolve(e)}})}min(e,a){return a=a||O.min,this.test({message:a,name:"min",exclusive:!0,params:{min:e},skipAbsent:!0,test(a){return a.length>=this.resolve(e)}})}max(e,a){return a=a||O.max,this.test({message:a,name:"max",exclusive:!0,params:{max:e},skipAbsent:!0,test(a){return a.length<=this.resolve(e)}})}ensure(){return this.default(()=>[]).transform((e,a)=>this._typeCheck(e)?e:null==a?[]:[].concat(a))}compact(e){let a=e?(a,t,n)=>!e(a,t,n):e=>!!e;return this.transform(e=>null!=e?e.filter(a):e)}describe(e){let a=(e?this.resolve(e):this).clone(),t=super.describe(e);if(a.innerType){var n;let i=e;null!=(n=i)&&n.value&&(i=Object.assign({},i,{parent:i.value,value:i.value[0]})),t.innerType=a.innerType.describe(i)}return t}}em.prototype;class ef extends N{constructor(e){super({type:"tuple",spec:{types:e},check(e){let a=this.spec.types;return Array.isArray(e)&&e.length===a.length}}),this.withMutation(()=>{this.typeError(T.notType)})}_cast(e,a){let{types:t}=this.spec,n=super._cast(e,a);if(!this._typeCheck(n))return n;let i=!1,s=t.map((e,t)=>{let s=e.cast(n[t],Object.assign({},a,{path:`${a.path||""}[${t}]`}));return s!==n[t]&&(i=!0),s});return i?s:n}_validate(e,a={},t,n){let i=this.spec.types;super._validate(e,a,t,(s,o)=>{var r,c;if(!this._typeCheck(o)){n(s,o);return}let p=[];for(let[t,n]of i.entries())p[t]=n.asNestedTest({options:a,index:t,parent:o,parentPath:a.path,originalParent:null!=(c=a.originalValue)?c:e});this.runTests({value:o,tests:p,originalValue:null!=(r=a.originalValue)?r:e,options:a},t,e=>n(e.concat(s),o))})}describe(e){let a=(e?this.resolve(e):this).clone(),t=super.describe(e);return t.innerType=a.spec.types.map((a,t)=>{var n;let i=e;return null!=(n=i)&&n.value&&(i=Object.assign({},i,{parent:i.value,value:i.value[t]})),a.describe(i)}),t}}ef.prototype;class eh{constructor(e){this.type="lazy",this.__isYupSchema__=!0,this.spec=void 0,this._resolve=(e,a={})=>{let t=this.builder(e,a);if(!F(t))throw TypeError("lazy() functions must return a valid schema");return this.spec.optional&&(t=t.optional()),t.resolve(a)},this.builder=e,this.spec={meta:void 0,optional:!1}}clone(e){let a=new eh(this.builder);return a.spec=Object.assign({},this.spec,e),a}optionality(e){return this.clone({optional:e})}optional(){return this.optionality(!0)}resolve(e){return this._resolve(e.value,e)}cast(e,a){return this._resolve(e,a).cast(e,a)}asNestedTest(e){let{key:a,index:t,parent:n,options:i}=e,s=n[null!=t?t:a];return this._resolve(s,Object.assign({},i,{value:s,parent:n})).asNestedTest(e)}validate(e,a){return this._resolve(e,a).validate(e,a)}validateSync(e,a){return this._resolve(e,a).validateSync(e,a)}validateAt(e,a,t){return this._resolve(a,t).validateAt(e,a,t)}validateSyncAt(e,a,t){return this._resolve(a,t).validateSyncAt(e,a,t)}isValid(e,a){return this._resolve(e,a).isValid(e,a)}isValidSync(e,a){return this._resolve(e,a).isValidSync(e,a)}describe(e){return e?this.resolve(e).describe(e):{type:"lazy",meta:this.spec.meta,label:void 0}}meta(...e){if(0===e.length)return this.spec.meta;let a=this.clone();return a.spec.meta=Object.assign(a.spec.meta||{},e[0]),a}}},44099:(e,a,t)=>{"use strict";let n,i,s,o,r,c,p;t.d(a,{Z:()=>aZ});var l,u,d,m={};function f(e,a){return function(){return e.apply(a,arguments)}}t.r(m),t.d(m,{hasBrowserEnv:()=>eb,hasStandardBrowserEnv:()=>ey,hasStandardBrowserWebWorkerEnv:()=>ew,navigator:()=>eg,origin:()=>e_});let{toString:h}=Object.prototype,{getPrototypeOf:x}=Object,v=(n=Object.create(null),e=>{let a=h.call(e);return n[a]||(n[a]=a.slice(8,-1).toLowerCase())}),b=e=>(e=e.toLowerCase(),a=>v(a)===e),g=e=>a=>typeof a===e,{isArray:y}=Array,w=g("undefined"),_=b("ArrayBuffer"),j=g("string"),k=g("function"),E=g("number"),S=e=>null!==e&&"object"==typeof e,O=e=>{if("object"!==v(e))return!1;let a=x(e);return(null===a||a===Object.prototype||null===Object.getPrototypeOf(a))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},T=b("Date"),F=b("File"),C=b("Blob"),R=b("FileList"),A=b("URLSearchParams"),[z,L,D,U]=["ReadableStream","Request","Response","Headers"].map(b);function N(e,a,{allOwnKeys:t=!1}={}){let n,i;if(null!=e){if("object"!=typeof e&&(e=[e]),y(e))for(n=0,i=e.length;n<i;n++)a.call(null,e[n],n,e);else{let i;let s=t?Object.getOwnPropertyNames(e):Object.keys(e),o=s.length;for(n=0;n<o;n++)i=s[n],a.call(null,e[i],i,e)}}}function P(e,a){let t;a=a.toLowerCase();let n=Object.keys(e),i=n.length;for(;i-- >0;)if(a===(t=n[i]).toLowerCase())return t;return null}let B="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,q=e=>!w(e)&&e!==B,$=(i="undefined"!=typeof Uint8Array&&x(Uint8Array),e=>i&&e instanceof i),I=b("HTMLFormElement"),M=(({hasOwnProperty:e})=>(a,t)=>e.call(a,t))(Object.prototype),V=b("RegExp"),H=(e,a)=>{let t=Object.getOwnPropertyDescriptors(e),n={};N(t,(t,i)=>{let s;!1!==(s=a(t,i,e))&&(n[i]=s||t)}),Object.defineProperties(e,n)},W=b("AsyncFunction"),G=(l="function"==typeof setImmediate,u=k(B.postMessage),l?setImmediate:u?(c=`axios@${Math.random()}`,p=[],B.addEventListener("message",({source:e,data:a})=>{e===B&&a===c&&p.length&&p.shift()()},!1),e=>{p.push(e),B.postMessage(c,"*")}):e=>setTimeout(e)),K="undefined"!=typeof queueMicrotask?queueMicrotask.bind(B):"undefined"!=typeof process&&process.nextTick||G,J={isArray:y,isArrayBuffer:_,isBuffer:function(e){return null!==e&&!w(e)&&null!==e.constructor&&!w(e.constructor)&&k(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let a;return e&&("function"==typeof FormData&&e instanceof FormData||k(e.append)&&("formdata"===(a=v(e))||"object"===a&&k(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&_(e.buffer)},isString:j,isNumber:E,isBoolean:e=>!0===e||!1===e,isObject:S,isPlainObject:O,isReadableStream:z,isRequest:L,isResponse:D,isHeaders:U,isUndefined:w,isDate:T,isFile:F,isBlob:C,isRegExp:V,isFunction:k,isStream:e=>S(e)&&k(e.pipe),isURLSearchParams:A,isTypedArray:$,isFileList:R,forEach:N,merge:function e(){let{caseless:a}=q(this)&&this||{},t={},n=(n,i)=>{let s=a&&P(t,i)||i;O(t[s])&&O(n)?t[s]=e(t[s],n):O(n)?t[s]=e({},n):y(n)?t[s]=n.slice():t[s]=n};for(let e=0,a=arguments.length;e<a;e++)arguments[e]&&N(arguments[e],n);return t},extend:(e,a,t,{allOwnKeys:n}={})=>(N(a,(a,n)=>{t&&k(a)?e[n]=f(a,t):e[n]=a},{allOwnKeys:n}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,a,t,n)=>{e.prototype=Object.create(a.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:a.prototype}),t&&Object.assign(e.prototype,t)},toFlatObject:(e,a,t,n)=>{let i,s,o;let r={};if(a=a||{},null==e)return a;do{for(s=(i=Object.getOwnPropertyNames(e)).length;s-- >0;)o=i[s],(!n||n(o,e,a))&&!r[o]&&(a[o]=e[o],r[o]=!0);e=!1!==t&&x(e)}while(e&&(!t||t(e,a))&&e!==Object.prototype);return a},kindOf:v,kindOfTest:b,endsWith:(e,a,t)=>{e=String(e),(void 0===t||t>e.length)&&(t=e.length),t-=a.length;let n=e.indexOf(a,t);return -1!==n&&n===t},toArray:e=>{if(!e)return null;if(y(e))return e;let a=e.length;if(!E(a))return null;let t=Array(a);for(;a-- >0;)t[a]=e[a];return t},forEachEntry:(e,a)=>{let t;let n=(e&&e[Symbol.iterator]).call(e);for(;(t=n.next())&&!t.done;){let n=t.value;a.call(e,n[0],n[1])}},matchAll:(e,a)=>{let t;let n=[];for(;null!==(t=e.exec(a));)n.push(t);return n},isHTMLForm:I,hasOwnProperty:M,hasOwnProp:M,reduceDescriptors:H,freezeMethods:e=>{H(e,(a,t)=>{if(k(e)&&-1!==["arguments","caller","callee"].indexOf(t))return!1;if(k(e[t])){if(a.enumerable=!1,"writable"in a){a.writable=!1;return}a.set||(a.set=()=>{throw Error("Can not rewrite read-only method '"+t+"'")})}})},toObjectSet:(e,a)=>{let t={};return(e=>{e.forEach(e=>{t[e]=!0})})(y(e)?e:String(e).split(a)),t},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,a,t){return a.toUpperCase()+t}),noop:()=>{},toFiniteNumber:(e,a)=>null!=e&&Number.isFinite(e=+e)?e:a,findKey:P,global:B,isContextDefined:q,isSpecCompliantForm:function(e){return!!(e&&k(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])},toJSONObject:e=>{let a=Array(10),t=(e,n)=>{if(S(e)){if(a.indexOf(e)>=0)return;if(!("toJSON"in e)){a[n]=e;let i=y(e)?[]:{};return N(e,(e,a)=>{let s=t(e,n+1);w(s)||(i[a]=s)}),a[n]=void 0,i}}return e};return t(e,0)},isAsyncFn:W,isThenable:e=>e&&(S(e)||k(e))&&k(e.then)&&k(e.catch),setImmediate:G,asap:K};function Z(e,a,t,n,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack,this.message=e,this.name="AxiosError",a&&(this.code=a),t&&(this.config=t),n&&(this.request=n),i&&(this.response=i,this.status=i.status?i.status:null)}J.inherits(Z,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:J.toJSONObject(this.config),code:this.code,status:this.status}}});let Y=Z.prototype,X={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{X[e]={value:e}}),Object.defineProperties(Z,X),Object.defineProperty(Y,"isAxiosError",{value:!0}),Z.from=(e,a,t,n,i,s)=>{let o=Object.create(Y);return J.toFlatObject(e,o,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),Z.call(o,e.message,a,t,n,i),o.cause=e,o.name=e.name,s&&Object.assign(o,s),o};var Q=t(54127);function ee(e){return J.isPlainObject(e)||J.isArray(e)}function ea(e){return J.endsWith(e,"[]")?e.slice(0,-2):e}function et(e,a,t){return e?e.concat(a).map(function(e,a){return e=ea(e),!t&&a?"["+e+"]":e}).join(t?".":""):a}let en=J.toFlatObject(J,{},null,function(e){return/^is[A-Z]/.test(e)}),ei=function(e,a,t){if(!J.isObject(e))throw TypeError("target must be an object");a=a||new(Q||FormData);let n=(t=J.toFlatObject(t,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,a){return!J.isUndefined(a[e])})).metaTokens,i=t.visitor||p,s=t.dots,o=t.indexes,r=(t.Blob||"undefined"!=typeof Blob&&Blob)&&J.isSpecCompliantForm(a);if(!J.isFunction(i))throw TypeError("visitor must be a function");function c(e){if(null===e)return"";if(J.isDate(e))return e.toISOString();if(!r&&J.isBlob(e))throw new Z("Blob is not supported. Use a Buffer instead.");return J.isArrayBuffer(e)||J.isTypedArray(e)?r&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function p(e,t,i){let r=e;if(e&&!i&&"object"==typeof e){if(J.endsWith(t,"{}"))t=n?t:t.slice(0,-2),e=JSON.stringify(e);else{var p;if(J.isArray(e)&&(p=e,J.isArray(p)&&!p.some(ee))||(J.isFileList(e)||J.endsWith(t,"[]"))&&(r=J.toArray(e)))return t=ea(t),r.forEach(function(e,n){J.isUndefined(e)||null===e||a.append(!0===o?et([t],n,s):null===o?t:t+"[]",c(e))}),!1}}return!!ee(e)||(a.append(et(i,t,s),c(e)),!1)}let l=[],u=Object.assign(en,{defaultVisitor:p,convertValue:c,isVisitable:ee});if(!J.isObject(e))throw TypeError("data must be an object");return function e(t,n){if(!J.isUndefined(t)){if(-1!==l.indexOf(t))throw Error("Circular reference detected in "+n.join("."));l.push(t),J.forEach(t,function(t,s){!0===(!(J.isUndefined(t)||null===t)&&i.call(a,t,J.isString(s)?s.trim():s,n,u))&&e(t,n?n.concat(s):[s])}),l.pop()}}(e),a};function es(e){let a={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return a[e]})}function eo(e,a){this._pairs=[],e&&ei(e,this,a)}let er=eo.prototype;function ec(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ep(e,a,t){let n;if(!a)return e;let i=t&&t.encode||ec;J.isFunction(t)&&(t={serialize:t});let s=t&&t.serialize;if(n=s?s(a,t):J.isURLSearchParams(a)?a.toString():new eo(a,t).toString(i)){let a=e.indexOf("#");-1!==a&&(e=e.slice(0,a)),e+=(-1===e.indexOf("?")?"?":"&")+n}return e}er.append=function(e,a){this._pairs.push([e,a])},er.toString=function(e){let a=e?function(a){return e.call(this,a,es)}:es;return this._pairs.map(function(e){return a(e[0])+"="+a(e[1])},"").join("&")};class el{constructor(){this.handlers=[]}use(e,a,t){return this.handlers.push({fulfilled:e,rejected:a,synchronous:!!t&&t.synchronous,runWhen:t?t.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){J.forEach(this.handlers,function(a){null!==a&&e(a)})}}let eu={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1};var ed=t(84770);let em=t(17360).URLSearchParams,ef="abcdefghijklmnopqrstuvwxyz",eh="0123456789",ex={DIGIT:eh,ALPHA:ef,ALPHA_DIGIT:ef+ef.toUpperCase()+eh},ev={isNode:!0,classes:{URLSearchParams:em,FormData:Q,Blob:"undefined"!=typeof Blob&&Blob||null},ALPHABET:ex,generateString:(e=16,a=ex.ALPHA_DIGIT)=>{let t="",{length:n}=a,i=new Uint32Array(e);ed.randomFillSync(i);for(let s=0;s<e;s++)t+=a[i[s]%n];return t},protocols:["http","https","file","data"]},eb="undefined"!=typeof window&&"undefined"!=typeof document,eg="object"==typeof navigator&&navigator||void 0,ey=eb&&(!eg||0>["ReactNative","NativeScript","NS"].indexOf(eg.product)),ew="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,e_=eb&&window.location.href||"http://localhost",ej={...m,...ev},ek=function(e){if(J.isFormData(e)&&J.isFunction(e.entries)){let a={};return J.forEachEntry(e,(e,t)=>{!function e(a,t,n,i){let s=a[i++];if("__proto__"===s)return!0;let o=Number.isFinite(+s),r=i>=a.length;return(s=!s&&J.isArray(n)?n.length:s,r)?J.hasOwnProp(n,s)?n[s]=[n[s],t]:n[s]=t:(n[s]&&J.isObject(n[s])||(n[s]=[]),e(a,t,n[s],i)&&J.isArray(n[s])&&(n[s]=function(e){let a,t;let n={},i=Object.keys(e),s=i.length;for(a=0;a<s;a++)n[t=i[a]]=e[t];return n}(n[s]))),!o}(J.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0]),t,a,0)}),a}return null},eE={transitional:eu,adapter:["xhr","http","fetch"],transformRequest:[function(e,a){let t;let n=a.getContentType()||"",i=n.indexOf("application/json")>-1,s=J.isObject(e);if(s&&J.isHTMLForm(e)&&(e=new FormData(e)),J.isFormData(e))return i?JSON.stringify(ek(e)):e;if(J.isArrayBuffer(e)||J.isBuffer(e)||J.isStream(e)||J.isFile(e)||J.isBlob(e)||J.isReadableStream(e))return e;if(J.isArrayBufferView(e))return e.buffer;if(J.isURLSearchParams(e))return a.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();if(s){if(n.indexOf("application/x-www-form-urlencoded")>-1){var o,r;return(o=e,r=this.formSerializer,ei(o,new ej.classes.URLSearchParams,Object.assign({visitor:function(e,a,t,n){return ej.isNode&&J.isBuffer(e)?(this.append(a,e.toString("base64")),!1):n.defaultVisitor.apply(this,arguments)}},r))).toString()}if((t=J.isFileList(e))||n.indexOf("multipart/form-data")>-1){let a=this.env&&this.env.FormData;return ei(t?{"files[]":e}:e,a&&new a,this.formSerializer)}}return s||i?(a.setContentType("application/json",!1),function(e,a,t){if(J.isString(e))try{return(0,JSON.parse)(e),J.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){let a=this.transitional||eE.transitional,t=a&&a.forcedJSONParsing,n="json"===this.responseType;if(J.isResponse(e)||J.isReadableStream(e))return e;if(e&&J.isString(e)&&(t&&!this.responseType||n)){let t=a&&a.silentJSONParsing;try{return JSON.parse(e)}catch(e){if(!t&&n){if("SyntaxError"===e.name)throw Z.from(e,Z.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ej.classes.FormData,Blob:ej.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};J.forEach(["delete","get","head","post","put","patch"],e=>{eE.headers[e]={}});let eS=J.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),eO=e=>{let a,t,n;let i={};return e&&e.split("\n").forEach(function(e){n=e.indexOf(":"),a=e.substring(0,n).trim().toLowerCase(),t=e.substring(n+1).trim(),!a||i[a]&&eS[a]||("set-cookie"===a?i[a]?i[a].push(t):i[a]=[t]:i[a]=i[a]?i[a]+", "+t:t)}),i},eT=Symbol("internals");function eF(e){return e&&String(e).trim().toLowerCase()}function eC(e){return!1===e||null==e?e:J.isArray(e)?e.map(eC):String(e)}let eR=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function eA(e,a,t,n,i){if(J.isFunction(n))return n.call(this,a,t);if(i&&(a=t),J.isString(a)){if(J.isString(n))return -1!==a.indexOf(n);if(J.isRegExp(n))return n.test(a)}}class ez{constructor(e){e&&this.set(e)}set(e,a,t){let n=this;function i(e,a,t){let i=eF(a);if(!i)throw Error("header name must be a non-empty string");let s=J.findKey(n,i);s&&void 0!==n[s]&&!0!==t&&(void 0!==t||!1===n[s])||(n[s||a]=eC(e))}let s=(e,a)=>J.forEach(e,(e,t)=>i(e,t,a));if(J.isPlainObject(e)||e instanceof this.constructor)s(e,a);else if(J.isString(e)&&(e=e.trim())&&!eR(e))s(eO(e),a);else if(J.isHeaders(e))for(let[a,n]of e.entries())i(n,a,t);else null!=e&&i(a,e,t);return this}get(e,a){if(e=eF(e)){let t=J.findKey(this,e);if(t){let e=this[t];if(!a)return e;if(!0===a)return function(e){let a;let t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;for(;a=n.exec(e);)t[a[1]]=a[2];return t}(e);if(J.isFunction(a))return a.call(this,e,t);if(J.isRegExp(a))return a.exec(e);throw TypeError("parser must be boolean|regexp|function")}}}has(e,a){if(e=eF(e)){let t=J.findKey(this,e);return!!(t&&void 0!==this[t]&&(!a||eA(this,this[t],t,a)))}return!1}delete(e,a){let t=this,n=!1;function i(e){if(e=eF(e)){let i=J.findKey(t,e);i&&(!a||eA(t,t[i],i,a))&&(delete t[i],n=!0)}}return J.isArray(e)?e.forEach(i):i(e),n}clear(e){let a=Object.keys(this),t=a.length,n=!1;for(;t--;){let i=a[t];(!e||eA(this,this[i],i,e,!0))&&(delete this[i],n=!0)}return n}normalize(e){let a=this,t={};return J.forEach(this,(n,i)=>{let s=J.findKey(t,i);if(s){a[s]=eC(n),delete a[i];return}let o=e?i.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,a,t)=>a.toUpperCase()+t):String(i).trim();o!==i&&delete a[i],a[o]=eC(n),t[o]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let a=Object.create(null);return J.forEach(this,(t,n)=>{null!=t&&!1!==t&&(a[n]=e&&J.isArray(t)?t.join(", "):t)}),a}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,a])=>e+": "+a).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...a){let t=new this(e);return a.forEach(e=>t.set(e)),t}static accessor(e){let a=(this[eT]=this[eT]={accessors:{}}).accessors,t=this.prototype;function n(e){let n=eF(e);a[n]||(function(e,a){let t=J.toCamelCase(" "+a);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+t,{value:function(e,t,i){return this[n].call(this,a,e,t,i)},configurable:!0})})}(t,e),a[n]=!0)}return J.isArray(e)?e.forEach(n):n(e),this}}function eL(e,a){let t=this||eE,n=a||t,i=ez.from(n.headers),s=n.data;return J.forEach(e,function(e){s=e.call(t,s,i.normalize(),a?a.status:void 0)}),i.normalize(),s}function eD(e){return!!(e&&e.__CANCEL__)}function eU(e,a,t){Z.call(this,null==e?"canceled":e,Z.ERR_CANCELED,a,t),this.name="CanceledError"}function eN(e,a,t){let n=t.config.validateStatus;!t.status||!n||n(t.status)?e(t):a(new Z("Request failed with status code "+t.status,[Z.ERR_BAD_REQUEST,Z.ERR_BAD_RESPONSE][Math.floor(t.status/100)-4],t.config,t.request,t))}function eP(e,a,t){let n=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(a);return e&&n||!1==t?a?e.replace(/\/?\/$/,"")+"/"+a.replace(/^\/+/,""):e:a}ez.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),J.reduceDescriptors(ez.prototype,({value:e},a)=>{let t=a[0].toUpperCase()+a.slice(1);return{get:()=>e,set(e){this[t]=e}}}),J.freezeMethods(ez),J.inherits(eU,Z,{__CANCEL__:!0});var eB=t(13304),eq=t(32615),e$=t(35240),eI=t(21764),eM=t(19831),eV=t(71568);let eH="1.8.3";function eW(e){let a=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return a&&a[1]||""}let eG=/^(?:([^;]+);)?(?:[^;]+;)?(base64|),([\s\S]*)$/;var eK=t(76162);let eJ=Symbol("internals");class eZ extends eK.Transform{constructor(e){super({readableHighWaterMark:(e=J.toFlatObject(e,{maxRate:0,chunkSize:65536,minChunkSize:100,timeWindow:500,ticksRate:2,samplesCount:15},null,(e,a)=>!J.isUndefined(a[e]))).chunkSize});let a=this[eJ]={timeWindow:e.timeWindow,chunkSize:e.chunkSize,maxRate:e.maxRate,minChunkSize:e.minChunkSize,bytesSeen:0,isCaptured:!1,notifiedBytesLoaded:0,ts:Date.now(),bytes:0,onReadCallback:null};this.on("newListener",e=>{"progress"!==e||a.isCaptured||(a.isCaptured=!0)})}_read(e){let a=this[eJ];return a.onReadCallback&&a.onReadCallback(),super._read(e)}_transform(e,a,t){let n=this[eJ],i=n.maxRate,s=this.readableHighWaterMark,o=n.timeWindow,r=i/(1e3/o),c=!1!==n.minChunkSize?Math.max(n.minChunkSize,.01*r):0,p=(e,a)=>{let t=Buffer.byteLength(e);n.bytesSeen+=t,n.bytes+=t,n.isCaptured&&this.emit("progress",n.bytesSeen),this.push(e)?process.nextTick(a):n.onReadCallback=()=>{n.onReadCallback=null,process.nextTick(a)}},l=(e,a)=>{let t;let l=Buffer.byteLength(e),u=null,d=s,m=0;if(i){let e=Date.now();(!n.ts||(m=e-n.ts)>=o)&&(n.ts=e,t=r-n.bytes,n.bytes=t<0?-t:0,m=0),t=r-n.bytes}if(i){if(t<=0)return setTimeout(()=>{a(null,e)},o-m);t<d&&(d=t)}d&&l>d&&l-d>c&&(u=e.subarray(d),e=e.subarray(0,d)),p(e,u?()=>{process.nextTick(a,null,u)}:a)};l(e,function e(a,n){if(a)return t(a);n?l(n,e):t(null)})}}var eY=t(17702);let{asyncIterator:eX}=Symbol,eQ=async function*(e){e.stream?yield*e.stream():e.arrayBuffer?yield await e.arrayBuffer():e[eX]?yield*e[eX]():yield e},e0=ej.ALPHABET.ALPHA_DIGIT+"-_",e1="function"==typeof TextEncoder?new TextEncoder:new eI.TextEncoder,e2=e1.encode("\r\n");class e3{constructor(e,a){let{escapeName:t}=this.constructor,n=J.isString(a),i=`Content-Disposition: form-data; name="${t(e)}"${!n&&a.name?`; filename="${t(a.name)}"`:""}\r
`;n?a=e1.encode(String(a).replace(/\r?\n|\r\n?/g,"\r\n")):i+=`Content-Type: ${a.type||"application/octet-stream"}\r
`,this.headers=e1.encode(i+"\r\n"),this.contentLength=n?a.byteLength:a.size,this.size=this.headers.byteLength+this.contentLength+2,this.name=e,this.value=a}async *encode(){yield this.headers;let{value:e}=this;J.isTypedArray(e)?yield e:yield*eQ(e),yield e2}static escapeName(e){return String(e).replace(/[\r\n"]/g,e=>({"\r":"%0D","\n":"%0A",'"':"%22"})[e])}}let e6=(e,a,t)=>{let{tag:n="form-data-boundary",size:i=25,boundary:s=n+"-"+ej.generateString(i,e0)}=t||{};if(!J.isFormData(e))throw TypeError("FormData instance required");if(s.length<1||s.length>70)throw Error("boundary must be 10-70 characters long");let o=e1.encode("--"+s+"\r\n"),r=e1.encode("--"+s+"--\r\n\r\n"),c=r.byteLength,p=Array.from(e.entries()).map(([e,a])=>{let t=new e3(e,a);return c+=t.size,t});c+=o.byteLength*p.length;let l={"Content-Type":`multipart/form-data; boundary=${s}`};return Number.isFinite(c=J.toFiniteNumber(c))&&(l["Content-Length"]=c),a&&a(l),eK.Readable.from(async function*(){for(let e of p)yield o,yield*e.encode();yield r}())};class e7 extends eK.Transform{__transform(e,a,t){this.push(e),t()}_transform(e,a,t){if(0!==e.length&&(this._transform=this.__transform,120!==e[0])){let e=Buffer.alloc(2);e[0]=120,e[1]=156,this.push(e,a)}this.__transform(e,a,t)}}let e4=(e,a)=>J.isAsyncFn(e)?function(...t){let n=t.pop();e.apply(this,t).then(e=>{try{a?n(null,...a(e)):n(null,e)}catch(e){n(e)}},n)}:e,e8=function(e,a){let t;let n=Array(e=e||10),i=Array(e),s=0,o=0;return a=void 0!==a?a:1e3,function(r){let c=Date.now(),p=i[o];t||(t=c),n[s]=r,i[s]=c;let l=o,u=0;for(;l!==s;)u+=n[l++],l%=e;if((s=(s+1)%e)===o&&(o=(o+1)%e),c-t<a)return;let d=p&&c-p;return d?Math.round(1e3*u/d):void 0}},e9=function(e,a){let t,n,i=0,s=1e3/a,o=(a,s=Date.now())=>{i=s,t=null,n&&(clearTimeout(n),n=null),e.apply(null,a)};return[(...e)=>{let a=Date.now(),r=a-i;r>=s?o(e,a):(t=e,n||(n=setTimeout(()=>{n=null,o(t)},s-r)))},()=>t&&o(t)]},e5=(e,a,t=3)=>{let n=0,i=e8(50,250);return e9(t=>{let s=t.loaded,o=t.lengthComputable?t.total:void 0,r=s-n,c=i(r);n=s,e({loaded:s,total:o,progress:o?s/o:void 0,bytes:r,rate:c||void 0,estimated:c&&o&&s<=o?(o-s)/c:void 0,event:t,lengthComputable:null!=o,[a?"download":"upload"]:!0})},t)},ae=(e,a)=>{let t=null!=e;return[n=>a[0]({lengthComputable:t,total:e,loaded:n}),a[1]]},aa=e=>(...a)=>J.asap(()=>e(...a)),at={flush:eV.constants.Z_SYNC_FLUSH,finishFlush:eV.constants.Z_SYNC_FLUSH},an={flush:eV.constants.BROTLI_OPERATION_FLUSH,finishFlush:eV.constants.BROTLI_OPERATION_FLUSH},ai=J.isFunction(eV.createBrotliDecompress),{http:as,https:ao}=eM,ar=/https:?/,ac=ej.protocols.map(e=>e+":"),ap=(e,[a,t])=>(e.on("end",t).on("error",t),a);function al(e,a){e.beforeRedirects.proxy&&e.beforeRedirects.proxy(e),e.beforeRedirects.config&&e.beforeRedirects.config(e,a)}let au="undefined"!=typeof process&&"process"===J.kindOf(process),ad=e=>new Promise((a,t)=>{let n,i;let s=(e,a)=>{!i&&(i=!0,n&&n(e,a))},o=e=>{s(e,!0),t(e)};e(e=>{s(e),a(e)},o,e=>n=e).catch(o)}),am=({address:e,family:a})=>{if(!J.isString(e))throw TypeError("address must be a string");return{address:e,family:a||(0>e.indexOf(".")?6:4)}},af=(e,a)=>am(J.isObject(e)?e:{address:e,family:a}),ah=au&&function(e){return ad(async function(a,t,n){let i,s,o,r,c,p,l,{data:u,lookup:d,family:m}=e,{responseType:f,responseEncoding:h}=e,x=e.method.toUpperCase(),v=!1;if(d){let e=e4(d,e=>J.isArray(e)?e:[e]);d=(a,t,n)=>{e(a,t,(e,a,i)=>{if(e)return n(e);let s=J.isArray(a)?a.map(e=>af(e)):[af(a,i)];t.all?n(e,s):n(e,s[0].address,s[0].family)})}}let b=new eY.EventEmitter,g=()=>{e.cancelToken&&e.cancelToken.unsubscribe(y),e.signal&&e.signal.removeEventListener("abort",y),b.removeAllListeners()};function y(a){b.emit("abort",!a||a.type?new eU(null,e,c):a)}n((e,a)=>{r=!0,a&&(v=!0,g())}),b.once("abort",t),(e.cancelToken||e.signal)&&(e.cancelToken&&e.cancelToken.subscribe(y),e.signal&&(e.signal.aborted?y():e.signal.addEventListener("abort",y)));let w=new URL(eP(e.baseURL,e.url,e.allowAbsoluteUrls),ej.hasBrowserEnv?ej.origin:void 0),_=w.protocol||ac[0];if("data:"===_){let n;if("GET"!==x)return eN(a,t,{status:405,statusText:"method not allowed",headers:{},config:e});try{n=function(e,a,t){let n=t&&t.Blob||ej.classes.Blob,i=eW(e);if(void 0===a&&n&&(a=!0),"data"===i){e=i.length?e.slice(i.length+1):e;let t=eG.exec(e);if(!t)throw new Z("Invalid URL",Z.ERR_INVALID_URL);let s=t[1],o=t[2],r=t[3],c=Buffer.from(decodeURIComponent(r),o?"base64":"utf8");if(a){if(!n)throw new Z("Blob is not supported",Z.ERR_NOT_SUPPORT);return new n([c],{type:s})}return c}throw new Z("Unsupported protocol "+i,Z.ERR_NOT_SUPPORT)}(e.url,"blob"===f,{Blob:e.env&&e.env.Blob})}catch(a){throw Z.from(a,Z.ERR_BAD_REQUEST,e)}return"text"===f?(n=n.toString(h),h&&"utf8"!==h||(n=J.stripBOM(n))):"stream"===f&&(n=eK.Readable.from(n)),eN(a,t,{data:n,status:200,statusText:"OK",headers:new ez,config:e})}if(-1===ac.indexOf(_))return t(new Z("Unsupported protocol "+_,Z.ERR_BAD_REQUEST,e));let j=ez.from(e.headers).normalize();j.set("User-Agent","axios/"+eH,!1);let{onUploadProgress:k,onDownloadProgress:E}=e,S=e.maxRate;if(J.isSpecCompliantForm(u)){let e=j.getContentType(/boundary=([-_\w\d]{10,70})/i);u=e6(u,e=>{j.set(e)},{tag:`axios-${eH}-boundary`,boundary:e&&e[1]||void 0})}else if(J.isFormData(u)&&J.isFunction(u.getHeaders)){if(j.set(u.getHeaders()),!j.hasContentLength())try{let e=await eI.promisify(u.getLength).call(u);Number.isFinite(e)&&e>=0&&j.setContentLength(e)}catch(e){}}else if(J.isBlob(u)||J.isFile(u))u.size&&j.setContentType(u.type||"application/octet-stream"),j.setContentLength(u.size||0),u=eK.Readable.from(eQ(u));else if(u&&!J.isStream(u)){if(Buffer.isBuffer(u));else if(J.isArrayBuffer(u))u=Buffer.from(new Uint8Array(u));else{if(!J.isString(u))return t(new Z("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",Z.ERR_BAD_REQUEST,e));u=Buffer.from(u,"utf-8")}if(j.setContentLength(u.length,!1),e.maxBodyLength>-1&&u.length>e.maxBodyLength)return t(new Z("Request body larger than maxBodyLength limit",Z.ERR_BAD_REQUEST,e))}let O=J.toFiniteNumber(j.getContentLength());J.isArray(S)?(i=S[0],s=S[1]):i=s=S,u&&(k||i)&&(J.isStream(u)||(u=eK.Readable.from(u,{objectMode:!1})),u=eK.pipeline([u,new eZ({maxRate:J.toFiniteNumber(i)})],J.noop),k&&u.on("progress",ap(u,ae(O,e5(aa(k),!1,3))))),e.auth&&(o=(e.auth.username||"")+":"+(e.auth.password||"")),!o&&w.username&&(o=w.username+":"+w.password),o&&j.delete("authorization");try{p=ep(w.pathname+w.search,e.params,e.paramsSerializer).replace(/^\?/,"")}catch(n){let a=Error(n.message);return a.config=e,a.url=e.url,a.exists=!0,t(a)}j.set("Accept-Encoding","gzip, compress, deflate"+(ai?", br":""),!1);let T={path:p,method:x,headers:j.toJSON(),agents:{http:e.httpAgent,https:e.httpsAgent},auth:o,protocol:_,family:m,beforeRedirect:al,beforeRedirects:{}};J.isUndefined(d)||(T.lookup=d),e.socketPath?T.socketPath=e.socketPath:(T.hostname=w.hostname.startsWith("[")?w.hostname.slice(1,-1):w.hostname,T.port=w.port,function e(a,t,n){let i=t;if(!i&&!1!==i){let e=eB.getProxyForUrl(n);e&&(i=new URL(e))}if(i){if(i.username&&(i.auth=(i.username||"")+":"+(i.password||"")),i.auth){(i.auth.username||i.auth.password)&&(i.auth=(i.auth.username||"")+":"+(i.auth.password||""));let e=Buffer.from(i.auth,"utf8").toString("base64");a.headers["Proxy-Authorization"]="Basic "+e}a.headers.host=a.hostname+(a.port?":"+a.port:"");let e=i.hostname||i.host;a.hostname=e,a.host=e,a.port=i.port,a.path=n,i.protocol&&(a.protocol=i.protocol.includes(":")?i.protocol:`${i.protocol}:`)}a.beforeRedirects.proxy=function(a){e(a,t,a.href)}}(T,e.proxy,_+"//"+w.hostname+(w.port?":"+w.port:"")+T.path));let F=ar.test(T.protocol);if(T.agent=F?e.httpsAgent:e.httpAgent,e.transport?l=e.transport:0===e.maxRedirects?l=F?e$:eq:(e.maxRedirects&&(T.maxRedirects=e.maxRedirects),e.beforeRedirect&&(T.beforeRedirects.config=e.beforeRedirect),l=F?ao:as),e.maxBodyLength>-1?T.maxBodyLength=e.maxBodyLength:T.maxBodyLength=1/0,e.insecureHTTPParser&&(T.insecureHTTPParser=e.insecureHTTPParser),c=l.request(T,function(n){if(c.destroyed)return;let i=[n],o=+n.headers["content-length"];if(E||s){let e=new eZ({maxRate:J.toFiniteNumber(s)});E&&e.on("progress",ap(e,ae(o,e5(aa(E),!0,3)))),i.push(e)}let r=n,p=n.req||c;if(!1!==e.decompress&&n.headers["content-encoding"])switch(("HEAD"===x||204===n.statusCode)&&delete n.headers["content-encoding"],(n.headers["content-encoding"]||"").toLowerCase()){case"gzip":case"x-gzip":case"compress":case"x-compress":i.push(eV.createUnzip(at)),delete n.headers["content-encoding"];break;case"deflate":i.push(new e7),i.push(eV.createUnzip(at)),delete n.headers["content-encoding"];break;case"br":ai&&(i.push(eV.createBrotliDecompress(an)),delete n.headers["content-encoding"])}r=i.length>1?eK.pipeline(i,J.noop):i[0];let l=eK.finished(r,()=>{l(),g()}),u={status:n.statusCode,statusText:n.statusMessage,headers:new ez(n.headers),config:e,request:p};if("stream"===f)u.data=r,eN(a,t,u);else{let n=[],i=0;r.on("data",function(a){n.push(a),i+=a.length,e.maxContentLength>-1&&i>e.maxContentLength&&(v=!0,r.destroy(),t(new Z("maxContentLength size of "+e.maxContentLength+" exceeded",Z.ERR_BAD_RESPONSE,e,p)))}),r.on("aborted",function(){if(v)return;let a=new Z("stream has been aborted",Z.ERR_BAD_RESPONSE,e,p);r.destroy(a),t(a)}),r.on("error",function(a){c.destroyed||t(Z.from(a,null,e,p))}),r.on("end",function(){try{let e=1===n.length?n[0]:Buffer.concat(n);"arraybuffer"===f||(e=e.toString(h),h&&"utf8"!==h||(e=J.stripBOM(e))),u.data=e}catch(a){return t(Z.from(a,null,e,u.request,u))}eN(a,t,u)})}b.once("abort",e=>{r.destroyed||(r.emit("error",e),r.destroy())})}),b.once("abort",e=>{t(e),c.destroy(e)}),c.on("error",function(a){t(Z.from(a,null,e,c))}),c.on("socket",function(e){e.setKeepAlive(!0,6e4)}),e.timeout){let a=parseInt(e.timeout,10);if(Number.isNaN(a)){t(new Z("error trying to parse `config.timeout` to int",Z.ERR_BAD_OPTION_VALUE,e,c));return}c.setTimeout(a,function(){if(r)return;let a=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded",n=e.transitional||eu;e.timeoutErrorMessage&&(a=e.timeoutErrorMessage),t(new Z(a,n.clarifyTimeoutError?Z.ETIMEDOUT:Z.ECONNABORTED,e,c)),y()})}if(J.isStream(u)){let a=!1,t=!1;u.on("end",()=>{a=!0}),u.once("error",e=>{t=!0,c.destroy(e)}),u.on("close",()=>{a||t||y(new eU("Request stream has been aborted",e,c))}),u.pipe(c)}else c.end(u)})},ax=ej.hasStandardBrowserEnv?(s=new URL(ej.origin),o=ej.navigator&&/(msie|trident)/i.test(ej.navigator.userAgent),e=>(e=new URL(e,ej.origin),s.protocol===e.protocol&&s.host===e.host&&(o||s.port===e.port))):()=>!0,av=ej.hasStandardBrowserEnv?{write(e,a,t,n,i,s){let o=[e+"="+encodeURIComponent(a)];J.isNumber(t)&&o.push("expires="+new Date(t).toGMTString()),J.isString(n)&&o.push("path="+n),J.isString(i)&&o.push("domain="+i),!0===s&&o.push("secure"),document.cookie=o.join("; ")},read(e){let a=document.cookie.match(RegExp("(^|;\\s*)("+e+")=([^;]*)"));return a?decodeURIComponent(a[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}},ab=e=>e instanceof ez?{...e}:e;function ag(e,a){a=a||{};let t={};function n(e,a,t,n){return J.isPlainObject(e)&&J.isPlainObject(a)?J.merge.call({caseless:n},e,a):J.isPlainObject(a)?J.merge({},a):J.isArray(a)?a.slice():a}function i(e,a,t,i){return J.isUndefined(a)?J.isUndefined(e)?void 0:n(void 0,e,t,i):n(e,a,t,i)}function s(e,a){if(!J.isUndefined(a))return n(void 0,a)}function o(e,a){return J.isUndefined(a)?J.isUndefined(e)?void 0:n(void 0,e):n(void 0,a)}function r(t,i,s){return s in a?n(t,i):s in e?n(void 0,t):void 0}let c={url:s,method:s,data:s,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:r,headers:(e,a,t)=>i(ab(e),ab(a),t,!0)};return J.forEach(Object.keys(Object.assign({},e,a)),function(n){let s=c[n]||i,o=s(e[n],a[n],n);J.isUndefined(o)&&s!==r||(t[n]=o)}),t}let ay=e=>{let a;let t=ag({},e),{data:n,withXSRFToken:i,xsrfHeaderName:s,xsrfCookieName:o,headers:r,auth:c}=t;if(t.headers=r=ez.from(r),t.url=ep(eP(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&r.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),J.isFormData(n)){if(ej.hasStandardBrowserEnv||ej.hasStandardBrowserWebWorkerEnv)r.setContentType(void 0);else if(!1!==(a=r.getContentType())){let[e,...t]=a?a.split(";").map(e=>e.trim()).filter(Boolean):[];r.setContentType([e||"multipart/form-data",...t].join("; "))}}if(ej.hasStandardBrowserEnv&&(i&&J.isFunction(i)&&(i=i(t)),i||!1!==i&&ax(t.url))){let e=s&&o&&av.read(o);e&&r.set(s,e)}return t},aw="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise(function(a,t){let n,i,s,o,r;let c=ay(e),p=c.data,l=ez.from(c.headers).normalize(),{responseType:u,onUploadProgress:d,onDownloadProgress:m}=c;function f(){o&&o(),r&&r(),c.cancelToken&&c.cancelToken.unsubscribe(n),c.signal&&c.signal.removeEventListener("abort",n)}let h=new XMLHttpRequest;function x(){if(!h)return;let n=ez.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders());eN(function(e){a(e),f()},function(e){t(e),f()},{data:u&&"text"!==u&&"json"!==u?h.response:h.responseText,status:h.status,statusText:h.statusText,headers:n,config:e,request:h}),h=null}h.open(c.method.toUpperCase(),c.url,!0),h.timeout=c.timeout,"onloadend"in h?h.onloadend=x:h.onreadystatechange=function(){h&&4===h.readyState&&(0!==h.status||h.responseURL&&0===h.responseURL.indexOf("file:"))&&setTimeout(x)},h.onabort=function(){h&&(t(new Z("Request aborted",Z.ECONNABORTED,e,h)),h=null)},h.onerror=function(){t(new Z("Network Error",Z.ERR_NETWORK,e,h)),h=null},h.ontimeout=function(){let a=c.timeout?"timeout of "+c.timeout+"ms exceeded":"timeout exceeded",n=c.transitional||eu;c.timeoutErrorMessage&&(a=c.timeoutErrorMessage),t(new Z(a,n.clarifyTimeoutError?Z.ETIMEDOUT:Z.ECONNABORTED,e,h)),h=null},void 0===p&&l.setContentType(null),"setRequestHeader"in h&&J.forEach(l.toJSON(),function(e,a){h.setRequestHeader(a,e)}),J.isUndefined(c.withCredentials)||(h.withCredentials=!!c.withCredentials),u&&"json"!==u&&(h.responseType=c.responseType),m&&([s,r]=e5(m,!0),h.addEventListener("progress",s)),d&&h.upload&&([i,o]=e5(d),h.upload.addEventListener("progress",i),h.upload.addEventListener("loadend",o)),(c.cancelToken||c.signal)&&(n=a=>{h&&(t(!a||a.type?new eU(null,e,h):a),h.abort(),h=null)},c.cancelToken&&c.cancelToken.subscribe(n),c.signal&&(c.signal.aborted?n():c.signal.addEventListener("abort",n)));let v=eW(c.url);if(v&&-1===ej.protocols.indexOf(v)){t(new Z("Unsupported protocol "+v+":",Z.ERR_BAD_REQUEST,e));return}h.send(p||null)})},a_=(e,a)=>{let{length:t}=e=e?e.filter(Boolean):[];if(a||t){let t,n=new AbortController,i=function(e){if(!t){t=!0,o();let a=e instanceof Error?e:this.reason;n.abort(a instanceof Z?a:new eU(a instanceof Error?a.message:a))}},s=a&&setTimeout(()=>{s=null,i(new Z(`timeout ${a} of ms exceeded`,Z.ETIMEDOUT))},a),o=()=>{e&&(s&&clearTimeout(s),s=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(i):e.removeEventListener("abort",i)}),e=null)};e.forEach(e=>e.addEventListener("abort",i));let{signal:r}=n;return r.unsubscribe=()=>J.asap(o),r}},aj=function*(e,a){let t,n=e.byteLength;if(!a||n<a){yield e;return}let i=0;for(;i<n;)t=i+a,yield e.slice(i,t),i=t},ak=async function*(e,a){for await(let t of aE(e))yield*aj(t,a)},aE=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}let a=e.getReader();try{for(;;){let{done:e,value:t}=await a.read();if(e)break;yield t}}finally{await a.cancel()}},aS=(e,a,t,n)=>{let i;let s=ak(e,a),o=0,r=e=>{!i&&(i=!0,n&&n(e))};return new ReadableStream({async pull(e){try{let{done:a,value:n}=await s.next();if(a){r(),e.close();return}let i=n.byteLength;if(t){let e=o+=i;t(e)}e.enqueue(new Uint8Array(n))}catch(e){throw r(e),e}},cancel:e=>(r(e),s.return())},{highWaterMark:2})},aO="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,aT=aO&&"function"==typeof ReadableStream,aF=aO&&("function"==typeof TextEncoder?(r=new TextEncoder,e=>r.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer())),aC=(e,...a)=>{try{return!!e(...a)}catch(e){return!1}},aR=aT&&aC(()=>{let e=!1,a=new Request(ej.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!a}),aA=aT&&aC(()=>J.isReadableStream(new Response("").body)),az={stream:aA&&(e=>e.body)};aO&&(d=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{az[e]||(az[e]=J.isFunction(d[e])?a=>a[e]():(a,t)=>{throw new Z(`Response type '${e}' is not supported`,Z.ERR_NOT_SUPPORT,t)})}));let aL=async e=>{if(null==e)return 0;if(J.isBlob(e))return e.size;if(J.isSpecCompliantForm(e)){let a=new Request(ej.origin,{method:"POST",body:e});return(await a.arrayBuffer()).byteLength}return J.isArrayBufferView(e)||J.isArrayBuffer(e)?e.byteLength:(J.isURLSearchParams(e)&&(e+=""),J.isString(e))?(await aF(e)).byteLength:void 0},aD=async(e,a)=>{let t=J.toFiniteNumber(e.getContentLength());return null==t?aL(a):t},aU={http:ah,xhr:aw,fetch:aO&&(async e=>{let a,t,{url:n,method:i,data:s,signal:o,cancelToken:r,timeout:c,onDownloadProgress:p,onUploadProgress:l,responseType:u,headers:d,withCredentials:m="same-origin",fetchOptions:f}=ay(e);u=u?(u+"").toLowerCase():"text";let h=a_([o,r&&r.toAbortSignal()],c),x=h&&h.unsubscribe&&(()=>{h.unsubscribe()});try{if(l&&aR&&"get"!==i&&"head"!==i&&0!==(t=await aD(d,s))){let e,a=new Request(n,{method:"POST",body:s,duplex:"half"});if(J.isFormData(s)&&(e=a.headers.get("content-type"))&&d.setContentType(e),a.body){let[e,n]=ae(t,e5(aa(l)));s=aS(a.body,65536,e,n)}}J.isString(m)||(m=m?"include":"omit");let o="credentials"in Request.prototype;a=new Request(n,{...f,signal:h,method:i.toUpperCase(),headers:d.normalize().toJSON(),body:s,duplex:"half",credentials:o?m:void 0});let r=await fetch(a),c=aA&&("stream"===u||"response"===u);if(aA&&(p||c&&x)){let e={};["status","statusText","headers"].forEach(a=>{e[a]=r[a]});let a=J.toFiniteNumber(r.headers.get("content-length")),[t,n]=p&&ae(a,e5(aa(p),!0))||[];r=new Response(aS(r.body,65536,t,()=>{n&&n(),x&&x()}),e)}u=u||"text";let v=await az[J.findKey(az,u)||"text"](r,e);return!c&&x&&x(),await new Promise((t,n)=>{eN(t,n,{data:v,headers:ez.from(r.headers),status:r.status,statusText:r.statusText,config:e,request:a})})}catch(t){if(x&&x(),t&&"TypeError"===t.name&&/fetch/i.test(t.message))throw Object.assign(new Z("Network Error",Z.ERR_NETWORK,e,a),{cause:t.cause||t});throw Z.from(t,t&&t.code,e,a)}})};J.forEach(aU,(e,a)=>{if(e){try{Object.defineProperty(e,"name",{value:a})}catch(e){}Object.defineProperty(e,"adapterName",{value:a})}});let aN=e=>`- ${e}`,aP=e=>J.isFunction(e)||null===e||!1===e,aB={getAdapter:e=>{let a,t;let{length:n}=e=J.isArray(e)?e:[e],i={};for(let s=0;s<n;s++){let n;if(t=a=e[s],!aP(a)&&void 0===(t=aU[(n=String(a)).toLowerCase()]))throw new Z(`Unknown adapter '${n}'`);if(t)break;i[n||"#"+s]=t}if(!t){let e=Object.entries(i).map(([e,a])=>`adapter ${e} `+(!1===a?"is not supported by the environment":"is not available in the build"));throw new Z("There is no suitable adapter to dispatch the request "+(n?e.length>1?"since :\n"+e.map(aN).join("\n"):" "+aN(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return t}};function aq(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new eU(null,e)}function a$(e){return aq(e),e.headers=ez.from(e.headers),e.data=eL.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),aB.getAdapter(e.adapter||eE.adapter)(e).then(function(a){return aq(e),a.data=eL.call(e,e.transformResponse,a),a.headers=ez.from(a.headers),a},function(a){return!eD(a)&&(aq(e),a&&a.response&&(a.response.data=eL.call(e,e.transformResponse,a.response),a.response.headers=ez.from(a.response.headers))),Promise.reject(a)})}let aI={};["object","boolean","number","function","string","symbol"].forEach((e,a)=>{aI[e]=function(t){return typeof t===e||"a"+(a<1?"n ":" ")+e}});let aM={};aI.transitional=function(e,a,t){function n(e,a){return"[Axios v"+eH+"] Transitional option '"+e+"'"+a+(t?". "+t:"")}return(t,i,s)=>{if(!1===e)throw new Z(n(i," has been removed"+(a?" in "+a:"")),Z.ERR_DEPRECATED);return a&&!aM[i]&&(aM[i]=!0,console.warn(n(i," has been deprecated since v"+a+" and will be removed in the near future"))),!e||e(t,i,s)}},aI.spelling=function(e){return(a,t)=>(console.warn(`${t} is likely a misspelling of ${e}`),!0)};let aV={assertOptions:function(e,a,t){if("object"!=typeof e)throw new Z("options must be an object",Z.ERR_BAD_OPTION_VALUE);let n=Object.keys(e),i=n.length;for(;i-- >0;){let s=n[i],o=a[s];if(o){let a=e[s],t=void 0===a||o(a,s,e);if(!0!==t)throw new Z("option "+s+" must be "+t,Z.ERR_BAD_OPTION_VALUE);continue}if(!0!==t)throw new Z("Unknown option "+s,Z.ERR_BAD_OPTION)}},validators:aI},aH=aV.validators;class aW{constructor(e){this.defaults=e,this.interceptors={request:new el,response:new el}}async request(e,a){try{return await this._request(e,a)}catch(e){if(e instanceof Error){let a={};Error.captureStackTrace?Error.captureStackTrace(a):a=Error();let t=a.stack?a.stack.replace(/^.+\n/,""):"";try{e.stack?t&&!String(e.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+t):e.stack=t}catch(e){}}throw e}}_request(e,a){let t,n;"string"==typeof e?(a=a||{}).url=e:a=e||{};let{transitional:i,paramsSerializer:s,headers:o}=a=ag(this.defaults,a);void 0!==i&&aV.assertOptions(i,{silentJSONParsing:aH.transitional(aH.boolean),forcedJSONParsing:aH.transitional(aH.boolean),clarifyTimeoutError:aH.transitional(aH.boolean)},!1),null!=s&&(J.isFunction(s)?a.paramsSerializer={serialize:s}:aV.assertOptions(s,{encode:aH.function,serialize:aH.function},!0)),void 0!==a.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?a.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:a.allowAbsoluteUrls=!0),aV.assertOptions(a,{baseUrl:aH.spelling("baseURL"),withXsrfToken:aH.spelling("withXSRFToken")},!0),a.method=(a.method||this.defaults.method||"get").toLowerCase();let r=o&&J.merge(o.common,o[a.method]);o&&J.forEach(["delete","get","head","post","put","patch","common"],e=>{delete o[e]}),a.headers=ez.concat(r,o);let c=[],p=!0;this.interceptors.request.forEach(function(e){("function"!=typeof e.runWhen||!1!==e.runWhen(a))&&(p=p&&e.synchronous,c.unshift(e.fulfilled,e.rejected))});let l=[];this.interceptors.response.forEach(function(e){l.push(e.fulfilled,e.rejected)});let u=0;if(!p){let e=[a$.bind(this),void 0];for(e.unshift.apply(e,c),e.push.apply(e,l),n=e.length,t=Promise.resolve(a);u<n;)t=t.then(e[u++],e[u++]);return t}n=c.length;let d=a;for(u=0;u<n;){let e=c[u++],a=c[u++];try{d=e(d)}catch(e){a.call(this,e);break}}try{t=a$.call(this,d)}catch(e){return Promise.reject(e)}for(u=0,n=l.length;u<n;)t=t.then(l[u++],l[u++]);return t}getUri(e){return ep(eP((e=ag(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}J.forEach(["delete","get","head","options"],function(e){aW.prototype[e]=function(a,t){return this.request(ag(t||{},{method:e,url:a,data:(t||{}).data}))}}),J.forEach(["post","put","patch"],function(e){function a(a){return function(t,n,i){return this.request(ag(i||{},{method:e,headers:a?{"Content-Type":"multipart/form-data"}:{},url:t,data:n}))}}aW.prototype[e]=a(),aW.prototype[e+"Form"]=a(!0)});class aG{constructor(e){let a;if("function"!=typeof e)throw TypeError("executor must be a function.");this.promise=new Promise(function(e){a=e});let t=this;this.promise.then(e=>{if(!t._listeners)return;let a=t._listeners.length;for(;a-- >0;)t._listeners[a](e);t._listeners=null}),this.promise.then=e=>{let a;let n=new Promise(e=>{t.subscribe(e),a=e}).then(e);return n.cancel=function(){t.unsubscribe(a)},n},e(function(e,n,i){t.reason||(t.reason=new eU(e,n,i),a(t.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let a=this._listeners.indexOf(e);-1!==a&&this._listeners.splice(a,1)}toAbortSignal(){let e=new AbortController,a=a=>{e.abort(a)};return this.subscribe(a),e.signal.unsubscribe=()=>this.unsubscribe(a),e.signal}static source(){let e;return{token:new aG(function(a){e=a}),cancel:e}}}let aK={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(aK).forEach(([e,a])=>{aK[a]=e});let aJ=function e(a){let t=new aW(a),n=f(aW.prototype.request,t);return J.extend(n,aW.prototype,t,{allOwnKeys:!0}),J.extend(n,t,null,{allOwnKeys:!0}),n.create=function(t){return e(ag(a,t))},n}(eE);aJ.Axios=aW,aJ.CanceledError=eU,aJ.CancelToken=aG,aJ.isCancel=eD,aJ.VERSION=eH,aJ.toFormData=ei,aJ.AxiosError=Z,aJ.Cancel=aJ.CanceledError,aJ.all=function(e){return Promise.all(e)},aJ.spread=function(e){return function(a){return e.apply(null,a)}},aJ.isAxiosError=function(e){return J.isObject(e)&&!0===e.isAxiosError},aJ.mergeConfig=ag,aJ.AxiosHeaders=ez,aJ.formToJSON=e=>ek(J.isHTMLForm(e)?new FormData(e):e),aJ.getAdapter=aB.getAdapter,aJ.HttpStatusCode=aK,aJ.default=aJ;let aZ=aJ},40572:e=>{"use strict";e.exports=JSON.parse('{"application/1d-interleaved-parityfec":{"source":"iana"},"application/3gpdash-qoe-report+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/3gpp-ims+xml":{"source":"iana","compressible":true},"application/3gpphal+json":{"source":"iana","compressible":true},"application/3gpphalforms+json":{"source":"iana","compressible":true},"application/a2l":{"source":"iana"},"application/ace+cbor":{"source":"iana"},"application/activemessage":{"source":"iana"},"application/activity+json":{"source":"iana","compressible":true},"application/alto-costmap+json":{"source":"iana","compressible":true},"application/alto-costmapfilter+json":{"source":"iana","compressible":true},"application/alto-directory+json":{"source":"iana","compressible":true},"application/alto-endpointcost+json":{"source":"iana","compressible":true},"application/alto-endpointcostparams+json":{"source":"iana","compressible":true},"application/alto-endpointprop+json":{"source":"iana","compressible":true},"application/alto-endpointpropparams+json":{"source":"iana","compressible":true},"application/alto-error+json":{"source":"iana","compressible":true},"application/alto-networkmap+json":{"source":"iana","compressible":true},"application/alto-networkmapfilter+json":{"source":"iana","compressible":true},"application/alto-updatestreamcontrol+json":{"source":"iana","compressible":true},"application/alto-updatestreamparams+json":{"source":"iana","compressible":true},"application/aml":{"source":"iana"},"application/andrew-inset":{"source":"iana","extensions":["ez"]},"application/applefile":{"source":"iana"},"application/applixware":{"source":"apache","extensions":["aw"]},"application/at+jwt":{"source":"iana"},"application/atf":{"source":"iana"},"application/atfx":{"source":"iana"},"application/atom+xml":{"source":"iana","compressible":true,"extensions":["atom"]},"application/atomcat+xml":{"source":"iana","compressible":true,"extensions":["atomcat"]},"application/atomdeleted+xml":{"source":"iana","compressible":true,"extensions":["atomdeleted"]},"application/atomicmail":{"source":"iana"},"application/atomsvc+xml":{"source":"iana","compressible":true,"extensions":["atomsvc"]},"application/atsc-dwd+xml":{"source":"iana","compressible":true,"extensions":["dwd"]},"application/atsc-dynamic-event-message":{"source":"iana"},"application/atsc-held+xml":{"source":"iana","compressible":true,"extensions":["held"]},"application/atsc-rdt+json":{"source":"iana","compressible":true},"application/atsc-rsat+xml":{"source":"iana","compressible":true,"extensions":["rsat"]},"application/atxml":{"source":"iana"},"application/auth-policy+xml":{"source":"iana","compressible":true},"application/bacnet-xdd+zip":{"source":"iana","compressible":false},"application/batch-smtp":{"source":"iana"},"application/bdoc":{"compressible":false,"extensions":["bdoc"]},"application/beep+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/calendar+json":{"source":"iana","compressible":true},"application/calendar+xml":{"source":"iana","compressible":true,"extensions":["xcs"]},"application/call-completion":{"source":"iana"},"application/cals-1840":{"source":"iana"},"application/captive+json":{"source":"iana","compressible":true},"application/cbor":{"source":"iana"},"application/cbor-seq":{"source":"iana"},"application/cccex":{"source":"iana"},"application/ccmp+xml":{"source":"iana","compressible":true},"application/ccxml+xml":{"source":"iana","compressible":true,"extensions":["ccxml"]},"application/cdfx+xml":{"source":"iana","compressible":true,"extensions":["cdfx"]},"application/cdmi-capability":{"source":"iana","extensions":["cdmia"]},"application/cdmi-container":{"source":"iana","extensions":["cdmic"]},"application/cdmi-domain":{"source":"iana","extensions":["cdmid"]},"application/cdmi-object":{"source":"iana","extensions":["cdmio"]},"application/cdmi-queue":{"source":"iana","extensions":["cdmiq"]},"application/cdni":{"source":"iana"},"application/cea":{"source":"iana"},"application/cea-2018+xml":{"source":"iana","compressible":true},"application/cellml+xml":{"source":"iana","compressible":true},"application/cfw":{"source":"iana"},"application/city+json":{"source":"iana","compressible":true},"application/clr":{"source":"iana"},"application/clue+xml":{"source":"iana","compressible":true},"application/clue_info+xml":{"source":"iana","compressible":true},"application/cms":{"source":"iana"},"application/cnrp+xml":{"source":"iana","compressible":true},"application/coap-group+json":{"source":"iana","compressible":true},"application/coap-payload":{"source":"iana"},"application/commonground":{"source":"iana"},"application/conference-info+xml":{"source":"iana","compressible":true},"application/cose":{"source":"iana"},"application/cose-key":{"source":"iana"},"application/cose-key-set":{"source":"iana"},"application/cpl+xml":{"source":"iana","compressible":true,"extensions":["cpl"]},"application/csrattrs":{"source":"iana"},"application/csta+xml":{"source":"iana","compressible":true},"application/cstadata+xml":{"source":"iana","compressible":true},"application/csvm+json":{"source":"iana","compressible":true},"application/cu-seeme":{"source":"apache","extensions":["cu"]},"application/cwt":{"source":"iana"},"application/cybercash":{"source":"iana"},"application/dart":{"compressible":true},"application/dash+xml":{"source":"iana","compressible":true,"extensions":["mpd"]},"application/dash-patch+xml":{"source":"iana","compressible":true,"extensions":["mpp"]},"application/dashdelta":{"source":"iana"},"application/davmount+xml":{"source":"iana","compressible":true,"extensions":["davmount"]},"application/dca-rft":{"source":"iana"},"application/dcd":{"source":"iana"},"application/dec-dx":{"source":"iana"},"application/dialog-info+xml":{"source":"iana","compressible":true},"application/dicom":{"source":"iana"},"application/dicom+json":{"source":"iana","compressible":true},"application/dicom+xml":{"source":"iana","compressible":true},"application/dii":{"source":"iana"},"application/dit":{"source":"iana"},"application/dns":{"source":"iana"},"application/dns+json":{"source":"iana","compressible":true},"application/dns-message":{"source":"iana"},"application/docbook+xml":{"source":"apache","compressible":true,"extensions":["dbk"]},"application/dots+cbor":{"source":"iana"},"application/dskpp+xml":{"source":"iana","compressible":true},"application/dssc+der":{"source":"iana","extensions":["dssc"]},"application/dssc+xml":{"source":"iana","compressible":true,"extensions":["xdssc"]},"application/dvcs":{"source":"iana"},"application/ecmascript":{"source":"iana","compressible":true,"extensions":["es","ecma"]},"application/edi-consent":{"source":"iana"},"application/edi-x12":{"source":"iana","compressible":false},"application/edifact":{"source":"iana","compressible":false},"application/efi":{"source":"iana"},"application/elm+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/elm+xml":{"source":"iana","compressible":true},"application/emergencycalldata.cap+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/emergencycalldata.comment+xml":{"source":"iana","compressible":true},"application/emergencycalldata.control+xml":{"source":"iana","compressible":true},"application/emergencycalldata.deviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.ecall.msd":{"source":"iana"},"application/emergencycalldata.providerinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.serviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.subscriberinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.veds+xml":{"source":"iana","compressible":true},"application/emma+xml":{"source":"iana","compressible":true,"extensions":["emma"]},"application/emotionml+xml":{"source":"iana","compressible":true,"extensions":["emotionml"]},"application/encaprtp":{"source":"iana"},"application/epp+xml":{"source":"iana","compressible":true},"application/epub+zip":{"source":"iana","compressible":false,"extensions":["epub"]},"application/eshop":{"source":"iana"},"application/exi":{"source":"iana","extensions":["exi"]},"application/expect-ct-report+json":{"source":"iana","compressible":true},"application/express":{"source":"iana","extensions":["exp"]},"application/fastinfoset":{"source":"iana"},"application/fastsoap":{"source":"iana"},"application/fdt+xml":{"source":"iana","compressible":true,"extensions":["fdt"]},"application/fhir+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/fhir+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/fido.trusted-apps+json":{"compressible":true},"application/fits":{"source":"iana"},"application/flexfec":{"source":"iana"},"application/font-sfnt":{"source":"iana"},"application/font-tdpfr":{"source":"iana","extensions":["pfr"]},"application/font-woff":{"source":"iana","compressible":false},"application/framework-attributes+xml":{"source":"iana","compressible":true},"application/geo+json":{"source":"iana","compressible":true,"extensions":["geojson"]},"application/geo+json-seq":{"source":"iana"},"application/geopackage+sqlite3":{"source":"iana"},"application/geoxacml+xml":{"source":"iana","compressible":true},"application/gltf-buffer":{"source":"iana"},"application/gml+xml":{"source":"iana","compressible":true,"extensions":["gml"]},"application/gpx+xml":{"source":"apache","compressible":true,"extensions":["gpx"]},"application/gxf":{"source":"apache","extensions":["gxf"]},"application/gzip":{"source":"iana","compressible":false,"extensions":["gz"]},"application/h224":{"source":"iana"},"application/held+xml":{"source":"iana","compressible":true},"application/hjson":{"extensions":["hjson"]},"application/http":{"source":"iana"},"application/hyperstudio":{"source":"iana","extensions":["stk"]},"application/ibe-key-request+xml":{"source":"iana","compressible":true},"application/ibe-pkg-reply+xml":{"source":"iana","compressible":true},"application/ibe-pp-data":{"source":"iana"},"application/iges":{"source":"iana"},"application/im-iscomposing+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/index":{"source":"iana"},"application/index.cmd":{"source":"iana"},"application/index.obj":{"source":"iana"},"application/index.response":{"source":"iana"},"application/index.vnd":{"source":"iana"},"application/inkml+xml":{"source":"iana","compressible":true,"extensions":["ink","inkml"]},"application/iotp":{"source":"iana"},"application/ipfix":{"source":"iana","extensions":["ipfix"]},"application/ipp":{"source":"iana"},"application/isup":{"source":"iana"},"application/its+xml":{"source":"iana","compressible":true,"extensions":["its"]},"application/java-archive":{"source":"apache","compressible":false,"extensions":["jar","war","ear"]},"application/java-serialized-object":{"source":"apache","compressible":false,"extensions":["ser"]},"application/java-vm":{"source":"apache","compressible":false,"extensions":["class"]},"application/javascript":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["js","mjs"]},"application/jf2feed+json":{"source":"iana","compressible":true},"application/jose":{"source":"iana"},"application/jose+json":{"source":"iana","compressible":true},"application/jrd+json":{"source":"iana","compressible":true},"application/jscalendar+json":{"source":"iana","compressible":true},"application/json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["json","map"]},"application/json-patch+json":{"source":"iana","compressible":true},"application/json-seq":{"source":"iana"},"application/json5":{"extensions":["json5"]},"application/jsonml+json":{"source":"apache","compressible":true,"extensions":["jsonml"]},"application/jwk+json":{"source":"iana","compressible":true},"application/jwk-set+json":{"source":"iana","compressible":true},"application/jwt":{"source":"iana"},"application/kpml-request+xml":{"source":"iana","compressible":true},"application/kpml-response+xml":{"source":"iana","compressible":true},"application/ld+json":{"source":"iana","compressible":true,"extensions":["jsonld"]},"application/lgr+xml":{"source":"iana","compressible":true,"extensions":["lgr"]},"application/link-format":{"source":"iana"},"application/load-control+xml":{"source":"iana","compressible":true},"application/lost+xml":{"source":"iana","compressible":true,"extensions":["lostxml"]},"application/lostsync+xml":{"source":"iana","compressible":true},"application/lpf+zip":{"source":"iana","compressible":false},"application/lxf":{"source":"iana"},"application/mac-binhex40":{"source":"iana","extensions":["hqx"]},"application/mac-compactpro":{"source":"apache","extensions":["cpt"]},"application/macwriteii":{"source":"iana"},"application/mads+xml":{"source":"iana","compressible":true,"extensions":["mads"]},"application/manifest+json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["webmanifest"]},"application/marc":{"source":"iana","extensions":["mrc"]},"application/marcxml+xml":{"source":"iana","compressible":true,"extensions":["mrcx"]},"application/mathematica":{"source":"iana","extensions":["ma","nb","mb"]},"application/mathml+xml":{"source":"iana","compressible":true,"extensions":["mathml"]},"application/mathml-content+xml":{"source":"iana","compressible":true},"application/mathml-presentation+xml":{"source":"iana","compressible":true},"application/mbms-associated-procedure-description+xml":{"source":"iana","compressible":true},"application/mbms-deregister+xml":{"source":"iana","compressible":true},"application/mbms-envelope+xml":{"source":"iana","compressible":true},"application/mbms-msk+xml":{"source":"iana","compressible":true},"application/mbms-msk-response+xml":{"source":"iana","compressible":true},"application/mbms-protection-description+xml":{"source":"iana","compressible":true},"application/mbms-reception-report+xml":{"source":"iana","compressible":true},"application/mbms-register+xml":{"source":"iana","compressible":true},"application/mbms-register-response+xml":{"source":"iana","compressible":true},"application/mbms-schedule+xml":{"source":"iana","compressible":true},"application/mbms-user-service-description+xml":{"source":"iana","compressible":true},"application/mbox":{"source":"iana","extensions":["mbox"]},"application/media-policy-dataset+xml":{"source":"iana","compressible":true,"extensions":["mpf"]},"application/media_control+xml":{"source":"iana","compressible":true},"application/mediaservercontrol+xml":{"source":"iana","compressible":true,"extensions":["mscml"]},"application/merge-patch+json":{"source":"iana","compressible":true},"application/metalink+xml":{"source":"apache","compressible":true,"extensions":["metalink"]},"application/metalink4+xml":{"source":"iana","compressible":true,"extensions":["meta4"]},"application/mets+xml":{"source":"iana","compressible":true,"extensions":["mets"]},"application/mf4":{"source":"iana"},"application/mikey":{"source":"iana"},"application/mipc":{"source":"iana"},"application/missing-blocks+cbor-seq":{"source":"iana"},"application/mmt-aei+xml":{"source":"iana","compressible":true,"extensions":["maei"]},"application/mmt-usd+xml":{"source":"iana","compressible":true,"extensions":["musd"]},"application/mods+xml":{"source":"iana","compressible":true,"extensions":["mods"]},"application/moss-keys":{"source":"iana"},"application/moss-signature":{"source":"iana"},"application/mosskey-data":{"source":"iana"},"application/mosskey-request":{"source":"iana"},"application/mp21":{"source":"iana","extensions":["m21","mp21"]},"application/mp4":{"source":"iana","extensions":["mp4s","m4p"]},"application/mpeg4-generic":{"source":"iana"},"application/mpeg4-iod":{"source":"iana"},"application/mpeg4-iod-xmt":{"source":"iana"},"application/mrb-consumer+xml":{"source":"iana","compressible":true},"application/mrb-publish+xml":{"source":"iana","compressible":true},"application/msc-ivr+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msc-mixer+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msword":{"source":"iana","compressible":false,"extensions":["doc","dot"]},"application/mud+json":{"source":"iana","compressible":true},"application/multipart-core":{"source":"iana"},"application/mxf":{"source":"iana","extensions":["mxf"]},"application/n-quads":{"source":"iana","extensions":["nq"]},"application/n-triples":{"source":"iana","extensions":["nt"]},"application/nasdata":{"source":"iana"},"application/news-checkgroups":{"source":"iana","charset":"US-ASCII"},"application/news-groupinfo":{"source":"iana","charset":"US-ASCII"},"application/news-transmission":{"source":"iana"},"application/nlsml+xml":{"source":"iana","compressible":true},"application/node":{"source":"iana","extensions":["cjs"]},"application/nss":{"source":"iana"},"application/oauth-authz-req+jwt":{"source":"iana"},"application/oblivious-dns-message":{"source":"iana"},"application/ocsp-request":{"source":"iana"},"application/ocsp-response":{"source":"iana"},"application/octet-stream":{"source":"iana","compressible":false,"extensions":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/oda":{"source":"iana","extensions":["oda"]},"application/odm+xml":{"source":"iana","compressible":true},"application/odx":{"source":"iana"},"application/oebps-package+xml":{"source":"iana","compressible":true,"extensions":["opf"]},"application/ogg":{"source":"iana","compressible":false,"extensions":["ogx"]},"application/omdoc+xml":{"source":"apache","compressible":true,"extensions":["omdoc"]},"application/onenote":{"source":"apache","extensions":["onetoc","onetoc2","onetmp","onepkg"]},"application/opc-nodeset+xml":{"source":"iana","compressible":true},"application/oscore":{"source":"iana"},"application/oxps":{"source":"iana","extensions":["oxps"]},"application/p21":{"source":"iana"},"application/p21+zip":{"source":"iana","compressible":false},"application/p2p-overlay+xml":{"source":"iana","compressible":true,"extensions":["relo"]},"application/parityfec":{"source":"iana"},"application/passport":{"source":"iana"},"application/patch-ops-error+xml":{"source":"iana","compressible":true,"extensions":["xer"]},"application/pdf":{"source":"iana","compressible":false,"extensions":["pdf"]},"application/pdx":{"source":"iana"},"application/pem-certificate-chain":{"source":"iana"},"application/pgp-encrypted":{"source":"iana","compressible":false,"extensions":["pgp"]},"application/pgp-keys":{"source":"iana","extensions":["asc"]},"application/pgp-signature":{"source":"iana","extensions":["asc","sig"]},"application/pics-rules":{"source":"apache","extensions":["prf"]},"application/pidf+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pidf-diff+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pkcs10":{"source":"iana","extensions":["p10"]},"application/pkcs12":{"source":"iana"},"application/pkcs7-mime":{"source":"iana","extensions":["p7m","p7c"]},"application/pkcs7-signature":{"source":"iana","extensions":["p7s"]},"application/pkcs8":{"source":"iana","extensions":["p8"]},"application/pkcs8-encrypted":{"source":"iana"},"application/pkix-attr-cert":{"source":"iana","extensions":["ac"]},"application/pkix-cert":{"source":"iana","extensions":["cer"]},"application/pkix-crl":{"source":"iana","extensions":["crl"]},"application/pkix-pkipath":{"source":"iana","extensions":["pkipath"]},"application/pkixcmp":{"source":"iana","extensions":["pki"]},"application/pls+xml":{"source":"iana","compressible":true,"extensions":["pls"]},"application/poc-settings+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/postscript":{"source":"iana","compressible":true,"extensions":["ai","eps","ps"]},"application/ppsp-tracker+json":{"source":"iana","compressible":true},"application/problem+json":{"source":"iana","compressible":true},"application/problem+xml":{"source":"iana","compressible":true},"application/provenance+xml":{"source":"iana","compressible":true,"extensions":["provx"]},"application/prs.alvestrand.titrax-sheet":{"source":"iana"},"application/prs.cww":{"source":"iana","extensions":["cww"]},"application/prs.cyn":{"source":"iana","charset":"7-BIT"},"application/prs.hpub+zip":{"source":"iana","compressible":false},"application/prs.nprend":{"source":"iana"},"application/prs.plucker":{"source":"iana"},"application/prs.rdf-xml-crypt":{"source":"iana"},"application/prs.xsf+xml":{"source":"iana","compressible":true},"application/pskc+xml":{"source":"iana","compressible":true,"extensions":["pskcxml"]},"application/pvd+json":{"source":"iana","compressible":true},"application/qsig":{"source":"iana"},"application/raml+yaml":{"compressible":true,"extensions":["raml"]},"application/raptorfec":{"source":"iana"},"application/rdap+json":{"source":"iana","compressible":true},"application/rdf+xml":{"source":"iana","compressible":true,"extensions":["rdf","owl"]},"application/reginfo+xml":{"source":"iana","compressible":true,"extensions":["rif"]},"application/relax-ng-compact-syntax":{"source":"iana","extensions":["rnc"]},"application/remote-printing":{"source":"iana"},"application/reputon+json":{"source":"iana","compressible":true},"application/resource-lists+xml":{"source":"iana","compressible":true,"extensions":["rl"]},"application/resource-lists-diff+xml":{"source":"iana","compressible":true,"extensions":["rld"]},"application/rfc+xml":{"source":"iana","compressible":true},"application/riscos":{"source":"iana"},"application/rlmi+xml":{"source":"iana","compressible":true},"application/rls-services+xml":{"source":"iana","compressible":true,"extensions":["rs"]},"application/route-apd+xml":{"source":"iana","compressible":true,"extensions":["rapd"]},"application/route-s-tsid+xml":{"source":"iana","compressible":true,"extensions":["sls"]},"application/route-usd+xml":{"source":"iana","compressible":true,"extensions":["rusd"]},"application/rpki-ghostbusters":{"source":"iana","extensions":["gbr"]},"application/rpki-manifest":{"source":"iana","extensions":["mft"]},"application/rpki-publication":{"source":"iana"},"application/rpki-roa":{"source":"iana","extensions":["roa"]},"application/rpki-updown":{"source":"iana"},"application/rsd+xml":{"source":"apache","compressible":true,"extensions":["rsd"]},"application/rss+xml":{"source":"apache","compressible":true,"extensions":["rss"]},"application/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"application/rtploopback":{"source":"iana"},"application/rtx":{"source":"iana"},"application/samlassertion+xml":{"source":"iana","compressible":true},"application/samlmetadata+xml":{"source":"iana","compressible":true},"application/sarif+json":{"source":"iana","compressible":true},"application/sarif-external-properties+json":{"source":"iana","compressible":true},"application/sbe":{"source":"iana"},"application/sbml+xml":{"source":"iana","compressible":true,"extensions":["sbml"]},"application/scaip+xml":{"source":"iana","compressible":true},"application/scim+json":{"source":"iana","compressible":true},"application/scvp-cv-request":{"source":"iana","extensions":["scq"]},"application/scvp-cv-response":{"source":"iana","extensions":["scs"]},"application/scvp-vp-request":{"source":"iana","extensions":["spq"]},"application/scvp-vp-response":{"source":"iana","extensions":["spp"]},"application/sdp":{"source":"iana","extensions":["sdp"]},"application/secevent+jwt":{"source":"iana"},"application/senml+cbor":{"source":"iana"},"application/senml+json":{"source":"iana","compressible":true},"application/senml+xml":{"source":"iana","compressible":true,"extensions":["senmlx"]},"application/senml-etch+cbor":{"source":"iana"},"application/senml-etch+json":{"source":"iana","compressible":true},"application/senml-exi":{"source":"iana"},"application/sensml+cbor":{"source":"iana"},"application/sensml+json":{"source":"iana","compressible":true},"application/sensml+xml":{"source":"iana","compressible":true,"extensions":["sensmlx"]},"application/sensml-exi":{"source":"iana"},"application/sep+xml":{"source":"iana","compressible":true},"application/sep-exi":{"source":"iana"},"application/session-info":{"source":"iana"},"application/set-payment":{"source":"iana"},"application/set-payment-initiation":{"source":"iana","extensions":["setpay"]},"application/set-registration":{"source":"iana"},"application/set-registration-initiation":{"source":"iana","extensions":["setreg"]},"application/sgml":{"source":"iana"},"application/sgml-open-catalog":{"source":"iana"},"application/shf+xml":{"source":"iana","compressible":true,"extensions":["shf"]},"application/sieve":{"source":"iana","extensions":["siv","sieve"]},"application/simple-filter+xml":{"source":"iana","compressible":true},"application/simple-message-summary":{"source":"iana"},"application/simplesymbolcontainer":{"source":"iana"},"application/sipc":{"source":"iana"},"application/slate":{"source":"iana"},"application/smil":{"source":"iana"},"application/smil+xml":{"source":"iana","compressible":true,"extensions":["smi","smil"]},"application/smpte336m":{"source":"iana"},"application/soap+fastinfoset":{"source":"iana"},"application/soap+xml":{"source":"iana","compressible":true},"application/sparql-query":{"source":"iana","extensions":["rq"]},"application/sparql-results+xml":{"source":"iana","compressible":true,"extensions":["srx"]},"application/spdx+json":{"source":"iana","compressible":true},"application/spirits-event+xml":{"source":"iana","compressible":true},"application/sql":{"source":"iana"},"application/srgs":{"source":"iana","extensions":["gram"]},"application/srgs+xml":{"source":"iana","compressible":true,"extensions":["grxml"]},"application/sru+xml":{"source":"iana","compressible":true,"extensions":["sru"]},"application/ssdl+xml":{"source":"apache","compressible":true,"extensions":["ssdl"]},"application/ssml+xml":{"source":"iana","compressible":true,"extensions":["ssml"]},"application/stix+json":{"source":"iana","compressible":true},"application/swid+xml":{"source":"iana","compressible":true,"extensions":["swidtag"]},"application/tamp-apex-update":{"source":"iana"},"application/tamp-apex-update-confirm":{"source":"iana"},"application/tamp-community-update":{"source":"iana"},"application/tamp-community-update-confirm":{"source":"iana"},"application/tamp-error":{"source":"iana"},"application/tamp-sequence-adjust":{"source":"iana"},"application/tamp-sequence-adjust-confirm":{"source":"iana"},"application/tamp-status-query":{"source":"iana"},"application/tamp-status-response":{"source":"iana"},"application/tamp-update":{"source":"iana"},"application/tamp-update-confirm":{"source":"iana"},"application/tar":{"compressible":true},"application/taxii+json":{"source":"iana","compressible":true},"application/td+json":{"source":"iana","compressible":true},"application/tei+xml":{"source":"iana","compressible":true,"extensions":["tei","teicorpus"]},"application/tetra_isi":{"source":"iana"},"application/thraud+xml":{"source":"iana","compressible":true,"extensions":["tfi"]},"application/timestamp-query":{"source":"iana"},"application/timestamp-reply":{"source":"iana"},"application/timestamped-data":{"source":"iana","extensions":["tsd"]},"application/tlsrpt+gzip":{"source":"iana"},"application/tlsrpt+json":{"source":"iana","compressible":true},"application/tnauthlist":{"source":"iana"},"application/token-introspection+jwt":{"source":"iana"},"application/toml":{"compressible":true,"extensions":["toml"]},"application/trickle-ice-sdpfrag":{"source":"iana"},"application/trig":{"source":"iana","extensions":["trig"]},"application/ttml+xml":{"source":"iana","compressible":true,"extensions":["ttml"]},"application/tve-trigger":{"source":"iana"},"application/tzif":{"source":"iana"},"application/tzif-leap":{"source":"iana"},"application/ubjson":{"compressible":false,"extensions":["ubj"]},"application/ulpfec":{"source":"iana"},"application/urc-grpsheet+xml":{"source":"iana","compressible":true},"application/urc-ressheet+xml":{"source":"iana","compressible":true,"extensions":["rsheet"]},"application/urc-targetdesc+xml":{"source":"iana","compressible":true,"extensions":["td"]},"application/urc-uisocketdesc+xml":{"source":"iana","compressible":true},"application/vcard+json":{"source":"iana","compressible":true},"application/vcard+xml":{"source":"iana","compressible":true},"application/vemmi":{"source":"iana"},"application/vividence.scriptfile":{"source":"apache"},"application/vnd.1000minds.decision-model+xml":{"source":"iana","compressible":true,"extensions":["1km"]},"application/vnd.3gpp-prose+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-prose-pc3ch+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-v2x-local-service-information":{"source":"iana"},"application/vnd.3gpp.5gnas":{"source":"iana"},"application/vnd.3gpp.access-transfer-events+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.bsf+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gmop+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gtpc":{"source":"iana"},"application/vnd.3gpp.interworking-data":{"source":"iana"},"application/vnd.3gpp.lpp":{"source":"iana"},"application/vnd.3gpp.mc-signalling-ear":{"source":"iana"},"application/vnd.3gpp.mcdata-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-payload":{"source":"iana"},"application/vnd.3gpp.mcdata-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-signalling":{"source":"iana"},"application/vnd.3gpp.mcdata-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-floor-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-signed+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-init-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-transmission-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mid-call+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ngap":{"source":"iana"},"application/vnd.3gpp.pfcp":{"source":"iana"},"application/vnd.3gpp.pic-bw-large":{"source":"iana","extensions":["plb"]},"application/vnd.3gpp.pic-bw-small":{"source":"iana","extensions":["psb"]},"application/vnd.3gpp.pic-bw-var":{"source":"iana","extensions":["pvb"]},"application/vnd.3gpp.s1ap":{"source":"iana"},"application/vnd.3gpp.sms":{"source":"iana"},"application/vnd.3gpp.sms+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-ext+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.state-and-event-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ussd+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.bcmcsinfo+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.sms":{"source":"iana"},"application/vnd.3gpp2.tcap":{"source":"iana","extensions":["tcap"]},"application/vnd.3lightssoftware.imagescal":{"source":"iana"},"application/vnd.3m.post-it-notes":{"source":"iana","extensions":["pwn"]},"application/vnd.accpac.simply.aso":{"source":"iana","extensions":["aso"]},"application/vnd.accpac.simply.imp":{"source":"iana","extensions":["imp"]},"application/vnd.acucobol":{"source":"iana","extensions":["acu"]},"application/vnd.acucorp":{"source":"iana","extensions":["atc","acutc"]},"application/vnd.adobe.air-application-installer-package+zip":{"source":"apache","compressible":false,"extensions":["air"]},"application/vnd.adobe.flash.movie":{"source":"iana"},"application/vnd.adobe.formscentral.fcdt":{"source":"iana","extensions":["fcdt"]},"application/vnd.adobe.fxp":{"source":"iana","extensions":["fxp","fxpl"]},"application/vnd.adobe.partial-upload":{"source":"iana"},"application/vnd.adobe.xdp+xml":{"source":"iana","compressible":true,"extensions":["xdp"]},"application/vnd.adobe.xfdf":{"source":"iana","extensions":["xfdf"]},"application/vnd.aether.imp":{"source":"iana"},"application/vnd.afpc.afplinedata":{"source":"iana"},"application/vnd.afpc.afplinedata-pagedef":{"source":"iana"},"application/vnd.afpc.cmoca-cmresource":{"source":"iana"},"application/vnd.afpc.foca-charset":{"source":"iana"},"application/vnd.afpc.foca-codedfont":{"source":"iana"},"application/vnd.afpc.foca-codepage":{"source":"iana"},"application/vnd.afpc.modca":{"source":"iana"},"application/vnd.afpc.modca-cmtable":{"source":"iana"},"application/vnd.afpc.modca-formdef":{"source":"iana"},"application/vnd.afpc.modca-mediummap":{"source":"iana"},"application/vnd.afpc.modca-objectcontainer":{"source":"iana"},"application/vnd.afpc.modca-overlay":{"source":"iana"},"application/vnd.afpc.modca-pagesegment":{"source":"iana"},"application/vnd.age":{"source":"iana","extensions":["age"]},"application/vnd.ah-barcode":{"source":"iana"},"application/vnd.ahead.space":{"source":"iana","extensions":["ahead"]},"application/vnd.airzip.filesecure.azf":{"source":"iana","extensions":["azf"]},"application/vnd.airzip.filesecure.azs":{"source":"iana","extensions":["azs"]},"application/vnd.amadeus+json":{"source":"iana","compressible":true},"application/vnd.amazon.ebook":{"source":"apache","extensions":["azw"]},"application/vnd.amazon.mobi8-ebook":{"source":"iana"},"application/vnd.americandynamics.acc":{"source":"iana","extensions":["acc"]},"application/vnd.amiga.ami":{"source":"iana","extensions":["ami"]},"application/vnd.amundsen.maze+xml":{"source":"iana","compressible":true},"application/vnd.android.ota":{"source":"iana"},"application/vnd.android.package-archive":{"source":"apache","compressible":false,"extensions":["apk"]},"application/vnd.anki":{"source":"iana"},"application/vnd.anser-web-certificate-issue-initiation":{"source":"iana","extensions":["cii"]},"application/vnd.anser-web-funds-transfer-initiation":{"source":"apache","extensions":["fti"]},"application/vnd.antix.game-component":{"source":"iana","extensions":["atx"]},"application/vnd.apache.arrow.file":{"source":"iana"},"application/vnd.apache.arrow.stream":{"source":"iana"},"application/vnd.apache.thrift.binary":{"source":"iana"},"application/vnd.apache.thrift.compact":{"source":"iana"},"application/vnd.apache.thrift.json":{"source":"iana"},"application/vnd.api+json":{"source":"iana","compressible":true},"application/vnd.aplextor.warrp+json":{"source":"iana","compressible":true},"application/vnd.apothekende.reservation+json":{"source":"iana","compressible":true},"application/vnd.apple.installer+xml":{"source":"iana","compressible":true,"extensions":["mpkg"]},"application/vnd.apple.keynote":{"source":"iana","extensions":["key"]},"application/vnd.apple.mpegurl":{"source":"iana","extensions":["m3u8"]},"application/vnd.apple.numbers":{"source":"iana","extensions":["numbers"]},"application/vnd.apple.pages":{"source":"iana","extensions":["pages"]},"application/vnd.apple.pkpass":{"compressible":false,"extensions":["pkpass"]},"application/vnd.arastra.swi":{"source":"iana"},"application/vnd.aristanetworks.swi":{"source":"iana","extensions":["swi"]},"application/vnd.artisan+json":{"source":"iana","compressible":true},"application/vnd.artsquare":{"source":"iana"},"application/vnd.astraea-software.iota":{"source":"iana","extensions":["iota"]},"application/vnd.audiograph":{"source":"iana","extensions":["aep"]},"application/vnd.autopackage":{"source":"iana"},"application/vnd.avalon+json":{"source":"iana","compressible":true},"application/vnd.avistar+xml":{"source":"iana","compressible":true},"application/vnd.balsamiq.bmml+xml":{"source":"iana","compressible":true,"extensions":["bmml"]},"application/vnd.balsamiq.bmpr":{"source":"iana"},"application/vnd.banana-accounting":{"source":"iana"},"application/vnd.bbf.usp.error":{"source":"iana"},"application/vnd.bbf.usp.msg":{"source":"iana"},"application/vnd.bbf.usp.msg+json":{"source":"iana","compressible":true},"application/vnd.bekitzur-stech+json":{"source":"iana","compressible":true},"application/vnd.bint.med-content":{"source":"iana"},"application/vnd.biopax.rdf+xml":{"source":"iana","compressible":true},"application/vnd.blink-idb-value-wrapper":{"source":"iana"},"application/vnd.blueice.multipass":{"source":"iana","extensions":["mpm"]},"application/vnd.bluetooth.ep.oob":{"source":"iana"},"application/vnd.bluetooth.le.oob":{"source":"iana"},"application/vnd.bmi":{"source":"iana","extensions":["bmi"]},"application/vnd.bpf":{"source":"iana"},"application/vnd.bpf3":{"source":"iana"},"application/vnd.businessobjects":{"source":"iana","extensions":["rep"]},"application/vnd.byu.uapi+json":{"source":"iana","compressible":true},"application/vnd.cab-jscript":{"source":"iana"},"application/vnd.canon-cpdl":{"source":"iana"},"application/vnd.canon-lips":{"source":"iana"},"application/vnd.capasystems-pg+json":{"source":"iana","compressible":true},"application/vnd.cendio.thinlinc.clientconf":{"source":"iana"},"application/vnd.century-systems.tcp_stream":{"source":"iana"},"application/vnd.chemdraw+xml":{"source":"iana","compressible":true,"extensions":["cdxml"]},"application/vnd.chess-pgn":{"source":"iana"},"application/vnd.chipnuts.karaoke-mmd":{"source":"iana","extensions":["mmd"]},"application/vnd.ciedi":{"source":"iana"},"application/vnd.cinderella":{"source":"iana","extensions":["cdy"]},"application/vnd.cirpack.isdn-ext":{"source":"iana"},"application/vnd.citationstyles.style+xml":{"source":"iana","compressible":true,"extensions":["csl"]},"application/vnd.claymore":{"source":"iana","extensions":["cla"]},"application/vnd.cloanto.rp9":{"source":"iana","extensions":["rp9"]},"application/vnd.clonk.c4group":{"source":"iana","extensions":["c4g","c4d","c4f","c4p","c4u"]},"application/vnd.cluetrust.cartomobile-config":{"source":"iana","extensions":["c11amc"]},"application/vnd.cluetrust.cartomobile-config-pkg":{"source":"iana","extensions":["c11amz"]},"application/vnd.coffeescript":{"source":"iana"},"application/vnd.collabio.xodocuments.document":{"source":"iana"},"application/vnd.collabio.xodocuments.document-template":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation-template":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet-template":{"source":"iana"},"application/vnd.collection+json":{"source":"iana","compressible":true},"application/vnd.collection.doc+json":{"source":"iana","compressible":true},"application/vnd.collection.next+json":{"source":"iana","compressible":true},"application/vnd.comicbook+zip":{"source":"iana","compressible":false},"application/vnd.comicbook-rar":{"source":"iana"},"application/vnd.commerce-battelle":{"source":"iana"},"application/vnd.commonspace":{"source":"iana","extensions":["csp"]},"application/vnd.contact.cmsg":{"source":"iana","extensions":["cdbcmsg"]},"application/vnd.coreos.ignition+json":{"source":"iana","compressible":true},"application/vnd.cosmocaller":{"source":"iana","extensions":["cmc"]},"application/vnd.crick.clicker":{"source":"iana","extensions":["clkx"]},"application/vnd.crick.clicker.keyboard":{"source":"iana","extensions":["clkk"]},"application/vnd.crick.clicker.palette":{"source":"iana","extensions":["clkp"]},"application/vnd.crick.clicker.template":{"source":"iana","extensions":["clkt"]},"application/vnd.crick.clicker.wordbank":{"source":"iana","extensions":["clkw"]},"application/vnd.criticaltools.wbs+xml":{"source":"iana","compressible":true,"extensions":["wbs"]},"application/vnd.cryptii.pipe+json":{"source":"iana","compressible":true},"application/vnd.crypto-shade-file":{"source":"iana"},"application/vnd.cryptomator.encrypted":{"source":"iana"},"application/vnd.cryptomator.vault":{"source":"iana"},"application/vnd.ctc-posml":{"source":"iana","extensions":["pml"]},"application/vnd.ctct.ws+xml":{"source":"iana","compressible":true},"application/vnd.cups-pdf":{"source":"iana"},"application/vnd.cups-postscript":{"source":"iana"},"application/vnd.cups-ppd":{"source":"iana","extensions":["ppd"]},"application/vnd.cups-raster":{"source":"iana"},"application/vnd.cups-raw":{"source":"iana"},"application/vnd.curl":{"source":"iana"},"application/vnd.curl.car":{"source":"apache","extensions":["car"]},"application/vnd.curl.pcurl":{"source":"apache","extensions":["pcurl"]},"application/vnd.cyan.dean.root+xml":{"source":"iana","compressible":true},"application/vnd.cybank":{"source":"iana"},"application/vnd.cyclonedx+json":{"source":"iana","compressible":true},"application/vnd.cyclonedx+xml":{"source":"iana","compressible":true},"application/vnd.d2l.coursepackage1p0+zip":{"source":"iana","compressible":false},"application/vnd.d3m-dataset":{"source":"iana"},"application/vnd.d3m-problem":{"source":"iana"},"application/vnd.dart":{"source":"iana","compressible":true,"extensions":["dart"]},"application/vnd.data-vision.rdz":{"source":"iana","extensions":["rdz"]},"application/vnd.datapackage+json":{"source":"iana","compressible":true},"application/vnd.dataresource+json":{"source":"iana","compressible":true},"application/vnd.dbf":{"source":"iana","extensions":["dbf"]},"application/vnd.debian.binary-package":{"source":"iana"},"application/vnd.dece.data":{"source":"iana","extensions":["uvf","uvvf","uvd","uvvd"]},"application/vnd.dece.ttml+xml":{"source":"iana","compressible":true,"extensions":["uvt","uvvt"]},"application/vnd.dece.unspecified":{"source":"iana","extensions":["uvx","uvvx"]},"application/vnd.dece.zip":{"source":"iana","extensions":["uvz","uvvz"]},"application/vnd.denovo.fcselayout-link":{"source":"iana","extensions":["fe_launch"]},"application/vnd.desmume.movie":{"source":"iana"},"application/vnd.dir-bi.plate-dl-nosuffix":{"source":"iana"},"application/vnd.dm.delegation+xml":{"source":"iana","compressible":true},"application/vnd.dna":{"source":"iana","extensions":["dna"]},"application/vnd.document+json":{"source":"iana","compressible":true},"application/vnd.dolby.mlp":{"source":"apache","extensions":["mlp"]},"application/vnd.dolby.mobile.1":{"source":"iana"},"application/vnd.dolby.mobile.2":{"source":"iana"},"application/vnd.doremir.scorecloud-binary-document":{"source":"iana"},"application/vnd.dpgraph":{"source":"iana","extensions":["dpg"]},"application/vnd.dreamfactory":{"source":"iana","extensions":["dfac"]},"application/vnd.drive+json":{"source":"iana","compressible":true},"application/vnd.ds-keypoint":{"source":"apache","extensions":["kpxx"]},"application/vnd.dtg.local":{"source":"iana"},"application/vnd.dtg.local.flash":{"source":"iana"},"application/vnd.dtg.local.html":{"source":"iana"},"application/vnd.dvb.ait":{"source":"iana","extensions":["ait"]},"application/vnd.dvb.dvbisl+xml":{"source":"iana","compressible":true},"application/vnd.dvb.dvbj":{"source":"iana"},"application/vnd.dvb.esgcontainer":{"source":"iana"},"application/vnd.dvb.ipdcdftnotifaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess2":{"source":"iana"},"application/vnd.dvb.ipdcesgpdd":{"source":"iana"},"application/vnd.dvb.ipdcroaming":{"source":"iana"},"application/vnd.dvb.iptv.alfec-base":{"source":"iana"},"application/vnd.dvb.iptv.alfec-enhancement":{"source":"iana"},"application/vnd.dvb.notif-aggregate-root+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-container+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-generic+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-msglist+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-request+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-response+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-init+xml":{"source":"iana","compressible":true},"application/vnd.dvb.pfr":{"source":"iana"},"application/vnd.dvb.service":{"source":"iana","extensions":["svc"]},"application/vnd.dxr":{"source":"iana"},"application/vnd.dynageo":{"source":"iana","extensions":["geo"]},"application/vnd.dzr":{"source":"iana"},"application/vnd.easykaraoke.cdgdownload":{"source":"iana"},"application/vnd.ecdis-update":{"source":"iana"},"application/vnd.ecip.rlp":{"source":"iana"},"application/vnd.eclipse.ditto+json":{"source":"iana","compressible":true},"application/vnd.ecowin.chart":{"source":"iana","extensions":["mag"]},"application/vnd.ecowin.filerequest":{"source":"iana"},"application/vnd.ecowin.fileupdate":{"source":"iana"},"application/vnd.ecowin.series":{"source":"iana"},"application/vnd.ecowin.seriesrequest":{"source":"iana"},"application/vnd.ecowin.seriesupdate":{"source":"iana"},"application/vnd.efi.img":{"source":"iana"},"application/vnd.efi.iso":{"source":"iana"},"application/vnd.emclient.accessrequest+xml":{"source":"iana","compressible":true},"application/vnd.enliven":{"source":"iana","extensions":["nml"]},"application/vnd.enphase.envoy":{"source":"iana"},"application/vnd.eprints.data+xml":{"source":"iana","compressible":true},"application/vnd.epson.esf":{"source":"iana","extensions":["esf"]},"application/vnd.epson.msf":{"source":"iana","extensions":["msf"]},"application/vnd.epson.quickanime":{"source":"iana","extensions":["qam"]},"application/vnd.epson.salt":{"source":"iana","extensions":["slt"]},"application/vnd.epson.ssf":{"source":"iana","extensions":["ssf"]},"application/vnd.ericsson.quickcall":{"source":"iana"},"application/vnd.espass-espass+zip":{"source":"iana","compressible":false},"application/vnd.eszigno3+xml":{"source":"iana","compressible":true,"extensions":["es3","et3"]},"application/vnd.etsi.aoc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.asic-e+zip":{"source":"iana","compressible":false},"application/vnd.etsi.asic-s+zip":{"source":"iana","compressible":false},"application/vnd.etsi.cug+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvcommand+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-bc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-cod+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-npvr+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvservice+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsync+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvueprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mcid+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mheg5":{"source":"iana"},"application/vnd.etsi.overload-control-policy-dataset+xml":{"source":"iana","compressible":true},"application/vnd.etsi.pstn+xml":{"source":"iana","compressible":true},"application/vnd.etsi.sci+xml":{"source":"iana","compressible":true},"application/vnd.etsi.simservs+xml":{"source":"iana","compressible":true},"application/vnd.etsi.timestamp-token":{"source":"iana"},"application/vnd.etsi.tsl+xml":{"source":"iana","compressible":true},"application/vnd.etsi.tsl.der":{"source":"iana"},"application/vnd.eu.kasparian.car+json":{"source":"iana","compressible":true},"application/vnd.eudora.data":{"source":"iana"},"application/vnd.evolv.ecig.profile":{"source":"iana"},"application/vnd.evolv.ecig.settings":{"source":"iana"},"application/vnd.evolv.ecig.theme":{"source":"iana"},"application/vnd.exstream-empower+zip":{"source":"iana","compressible":false},"application/vnd.exstream-package":{"source":"iana"},"application/vnd.ezpix-album":{"source":"iana","extensions":["ez2"]},"application/vnd.ezpix-package":{"source":"iana","extensions":["ez3"]},"application/vnd.f-secure.mobile":{"source":"iana"},"application/vnd.familysearch.gedcom+zip":{"source":"iana","compressible":false},"application/vnd.fastcopy-disk-image":{"source":"iana"},"application/vnd.fdf":{"source":"iana","extensions":["fdf"]},"application/vnd.fdsn.mseed":{"source":"iana","extensions":["mseed"]},"application/vnd.fdsn.seed":{"source":"iana","extensions":["seed","dataless"]},"application/vnd.ffsns":{"source":"iana"},"application/vnd.ficlab.flb+zip":{"source":"iana","compressible":false},"application/vnd.filmit.zfc":{"source":"iana"},"application/vnd.fints":{"source":"iana"},"application/vnd.firemonkeys.cloudcell":{"source":"iana"},"application/vnd.flographit":{"source":"iana","extensions":["gph"]},"application/vnd.fluxtime.clip":{"source":"iana","extensions":["ftc"]},"application/vnd.font-fontforge-sfd":{"source":"iana"},"application/vnd.framemaker":{"source":"iana","extensions":["fm","frame","maker","book"]},"application/vnd.frogans.fnc":{"source":"iana","extensions":["fnc"]},"application/vnd.frogans.ltf":{"source":"iana","extensions":["ltf"]},"application/vnd.fsc.weblaunch":{"source":"iana","extensions":["fsc"]},"application/vnd.fujifilm.fb.docuworks":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.binder":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.container":{"source":"iana"},"application/vnd.fujifilm.fb.jfi+xml":{"source":"iana","compressible":true},"application/vnd.fujitsu.oasys":{"source":"iana","extensions":["oas"]},"application/vnd.fujitsu.oasys2":{"source":"iana","extensions":["oa2"]},"application/vnd.fujitsu.oasys3":{"source":"iana","extensions":["oa3"]},"application/vnd.fujitsu.oasysgp":{"source":"iana","extensions":["fg5"]},"application/vnd.fujitsu.oasysprs":{"source":"iana","extensions":["bh2"]},"application/vnd.fujixerox.art-ex":{"source":"iana"},"application/vnd.fujixerox.art4":{"source":"iana"},"application/vnd.fujixerox.ddd":{"source":"iana","extensions":["ddd"]},"application/vnd.fujixerox.docuworks":{"source":"iana","extensions":["xdw"]},"application/vnd.fujixerox.docuworks.binder":{"source":"iana","extensions":["xbd"]},"application/vnd.fujixerox.docuworks.container":{"source":"iana"},"application/vnd.fujixerox.hbpl":{"source":"iana"},"application/vnd.fut-misnet":{"source":"iana"},"application/vnd.futoin+cbor":{"source":"iana"},"application/vnd.futoin+json":{"source":"iana","compressible":true},"application/vnd.fuzzysheet":{"source":"iana","extensions":["fzs"]},"application/vnd.genomatix.tuxedo":{"source":"iana","extensions":["txd"]},"application/vnd.gentics.grd+json":{"source":"iana","compressible":true},"application/vnd.geo+json":{"source":"iana","compressible":true},"application/vnd.geocube+xml":{"source":"iana","compressible":true},"application/vnd.geogebra.file":{"source":"iana","extensions":["ggb"]},"application/vnd.geogebra.slides":{"source":"iana"},"application/vnd.geogebra.tool":{"source":"iana","extensions":["ggt"]},"application/vnd.geometry-explorer":{"source":"iana","extensions":["gex","gre"]},"application/vnd.geonext":{"source":"iana","extensions":["gxt"]},"application/vnd.geoplan":{"source":"iana","extensions":["g2w"]},"application/vnd.geospace":{"source":"iana","extensions":["g3w"]},"application/vnd.gerber":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt-response":{"source":"iana"},"application/vnd.gmx":{"source":"iana","extensions":["gmx"]},"application/vnd.google-apps.document":{"compressible":false,"extensions":["gdoc"]},"application/vnd.google-apps.presentation":{"compressible":false,"extensions":["gslides"]},"application/vnd.google-apps.spreadsheet":{"compressible":false,"extensions":["gsheet"]},"application/vnd.google-earth.kml+xml":{"source":"iana","compressible":true,"extensions":["kml"]},"application/vnd.google-earth.kmz":{"source":"iana","compressible":false,"extensions":["kmz"]},"application/vnd.gov.sk.e-form+xml":{"source":"iana","compressible":true},"application/vnd.gov.sk.e-form+zip":{"source":"iana","compressible":false},"application/vnd.gov.sk.xmldatacontainer+xml":{"source":"iana","compressible":true},"application/vnd.grafeq":{"source":"iana","extensions":["gqf","gqs"]},"application/vnd.gridmp":{"source":"iana"},"application/vnd.groove-account":{"source":"iana","extensions":["gac"]},"application/vnd.groove-help":{"source":"iana","extensions":["ghf"]},"application/vnd.groove-identity-message":{"source":"iana","extensions":["gim"]},"application/vnd.groove-injector":{"source":"iana","extensions":["grv"]},"application/vnd.groove-tool-message":{"source":"iana","extensions":["gtm"]},"application/vnd.groove-tool-template":{"source":"iana","extensions":["tpl"]},"application/vnd.groove-vcard":{"source":"iana","extensions":["vcg"]},"application/vnd.hal+json":{"source":"iana","compressible":true},"application/vnd.hal+xml":{"source":"iana","compressible":true,"extensions":["hal"]},"application/vnd.handheld-entertainment+xml":{"source":"iana","compressible":true,"extensions":["zmm"]},"application/vnd.hbci":{"source":"iana","extensions":["hbci"]},"application/vnd.hc+json":{"source":"iana","compressible":true},"application/vnd.hcl-bireports":{"source":"iana"},"application/vnd.hdt":{"source":"iana"},"application/vnd.heroku+json":{"source":"iana","compressible":true},"application/vnd.hhe.lesson-player":{"source":"iana","extensions":["les"]},"application/vnd.hl7cda+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hl7v2+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hp-hpgl":{"source":"iana","extensions":["hpgl"]},"application/vnd.hp-hpid":{"source":"iana","extensions":["hpid"]},"application/vnd.hp-hps":{"source":"iana","extensions":["hps"]},"application/vnd.hp-jlyt":{"source":"iana","extensions":["jlt"]},"application/vnd.hp-pcl":{"source":"iana","extensions":["pcl"]},"application/vnd.hp-pclxl":{"source":"iana","extensions":["pclxl"]},"application/vnd.httphone":{"source":"iana"},"application/vnd.hydrostatix.sof-data":{"source":"iana","extensions":["sfd-hdstx"]},"application/vnd.hyper+json":{"source":"iana","compressible":true},"application/vnd.hyper-item+json":{"source":"iana","compressible":true},"application/vnd.hyperdrive+json":{"source":"iana","compressible":true},"application/vnd.hzn-3d-crossword":{"source":"iana"},"application/vnd.ibm.afplinedata":{"source":"iana"},"application/vnd.ibm.electronic-media":{"source":"iana"},"application/vnd.ibm.minipay":{"source":"iana","extensions":["mpy"]},"application/vnd.ibm.modcap":{"source":"iana","extensions":["afp","listafp","list3820"]},"application/vnd.ibm.rights-management":{"source":"iana","extensions":["irm"]},"application/vnd.ibm.secure-container":{"source":"iana","extensions":["sc"]},"application/vnd.iccprofile":{"source":"iana","extensions":["icc","icm"]},"application/vnd.ieee.1905":{"source":"iana"},"application/vnd.igloader":{"source":"iana","extensions":["igl"]},"application/vnd.imagemeter.folder+zip":{"source":"iana","compressible":false},"application/vnd.imagemeter.image+zip":{"source":"iana","compressible":false},"application/vnd.immervision-ivp":{"source":"iana","extensions":["ivp"]},"application/vnd.immervision-ivu":{"source":"iana","extensions":["ivu"]},"application/vnd.ims.imsccv1p1":{"source":"iana"},"application/vnd.ims.imsccv1p2":{"source":"iana"},"application/vnd.ims.imsccv1p3":{"source":"iana"},"application/vnd.ims.lis.v2.result+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolconsumerprofile+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy.id+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings.simple+json":{"source":"iana","compressible":true},"application/vnd.informedcontrol.rms+xml":{"source":"iana","compressible":true},"application/vnd.informix-visionary":{"source":"iana"},"application/vnd.infotech.project":{"source":"iana"},"application/vnd.infotech.project+xml":{"source":"iana","compressible":true},"application/vnd.innopath.wamp.notification":{"source":"iana"},"application/vnd.insors.igm":{"source":"iana","extensions":["igm"]},"application/vnd.intercon.formnet":{"source":"iana","extensions":["xpw","xpx"]},"application/vnd.intergeo":{"source":"iana","extensions":["i2g"]},"application/vnd.intertrust.digibox":{"source":"iana"},"application/vnd.intertrust.nncp":{"source":"iana"},"application/vnd.intu.qbo":{"source":"iana","extensions":["qbo"]},"application/vnd.intu.qfx":{"source":"iana","extensions":["qfx"]},"application/vnd.iptc.g2.catalogitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.conceptitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.knowledgeitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsmessage+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.packageitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.planningitem+xml":{"source":"iana","compressible":true},"application/vnd.ipunplugged.rcprofile":{"source":"iana","extensions":["rcprofile"]},"application/vnd.irepository.package+xml":{"source":"iana","compressible":true,"extensions":["irp"]},"application/vnd.is-xpr":{"source":"iana","extensions":["xpr"]},"application/vnd.isac.fcs":{"source":"iana","extensions":["fcs"]},"application/vnd.iso11783-10+zip":{"source":"iana","compressible":false},"application/vnd.jam":{"source":"iana","extensions":["jam"]},"application/vnd.japannet-directory-service":{"source":"iana"},"application/vnd.japannet-jpnstore-wakeup":{"source":"iana"},"application/vnd.japannet-payment-wakeup":{"source":"iana"},"application/vnd.japannet-registration":{"source":"iana"},"application/vnd.japannet-registration-wakeup":{"source":"iana"},"application/vnd.japannet-setstore-wakeup":{"source":"iana"},"application/vnd.japannet-verification":{"source":"iana"},"application/vnd.japannet-verification-wakeup":{"source":"iana"},"application/vnd.jcp.javame.midlet-rms":{"source":"iana","extensions":["rms"]},"application/vnd.jisp":{"source":"iana","extensions":["jisp"]},"application/vnd.joost.joda-archive":{"source":"iana","extensions":["joda"]},"application/vnd.jsk.isdn-ngn":{"source":"iana"},"application/vnd.kahootz":{"source":"iana","extensions":["ktz","ktr"]},"application/vnd.kde.karbon":{"source":"iana","extensions":["karbon"]},"application/vnd.kde.kchart":{"source":"iana","extensions":["chrt"]},"application/vnd.kde.kformula":{"source":"iana","extensions":["kfo"]},"application/vnd.kde.kivio":{"source":"iana","extensions":["flw"]},"application/vnd.kde.kontour":{"source":"iana","extensions":["kon"]},"application/vnd.kde.kpresenter":{"source":"iana","extensions":["kpr","kpt"]},"application/vnd.kde.kspread":{"source":"iana","extensions":["ksp"]},"application/vnd.kde.kword":{"source":"iana","extensions":["kwd","kwt"]},"application/vnd.kenameaapp":{"source":"iana","extensions":["htke"]},"application/vnd.kidspiration":{"source":"iana","extensions":["kia"]},"application/vnd.kinar":{"source":"iana","extensions":["kne","knp"]},"application/vnd.koan":{"source":"iana","extensions":["skp","skd","skt","skm"]},"application/vnd.kodak-descriptor":{"source":"iana","extensions":["sse"]},"application/vnd.las":{"source":"iana"},"application/vnd.las.las+json":{"source":"iana","compressible":true},"application/vnd.las.las+xml":{"source":"iana","compressible":true,"extensions":["lasxml"]},"application/vnd.laszip":{"source":"iana"},"application/vnd.leap+json":{"source":"iana","compressible":true},"application/vnd.liberty-request+xml":{"source":"iana","compressible":true},"application/vnd.llamagraphics.life-balance.desktop":{"source":"iana","extensions":["lbd"]},"application/vnd.llamagraphics.life-balance.exchange+xml":{"source":"iana","compressible":true,"extensions":["lbe"]},"application/vnd.logipipe.circuit+zip":{"source":"iana","compressible":false},"application/vnd.loom":{"source":"iana"},"application/vnd.lotus-1-2-3":{"source":"iana","extensions":["123"]},"application/vnd.lotus-approach":{"source":"iana","extensions":["apr"]},"application/vnd.lotus-freelance":{"source":"iana","extensions":["pre"]},"application/vnd.lotus-notes":{"source":"iana","extensions":["nsf"]},"application/vnd.lotus-organizer":{"source":"iana","extensions":["org"]},"application/vnd.lotus-screencam":{"source":"iana","extensions":["scm"]},"application/vnd.lotus-wordpro":{"source":"iana","extensions":["lwp"]},"application/vnd.macports.portpkg":{"source":"iana","extensions":["portpkg"]},"application/vnd.mapbox-vector-tile":{"source":"iana","extensions":["mvt"]},"application/vnd.marlin.drm.actiontoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.conftoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.license+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.mdcf":{"source":"iana"},"application/vnd.mason+json":{"source":"iana","compressible":true},"application/vnd.maxar.archive.3tz+zip":{"source":"iana","compressible":false},"application/vnd.maxmind.maxmind-db":{"source":"iana"},"application/vnd.mcd":{"source":"iana","extensions":["mcd"]},"application/vnd.medcalcdata":{"source":"iana","extensions":["mc1"]},"application/vnd.mediastation.cdkey":{"source":"iana","extensions":["cdkey"]},"application/vnd.meridian-slingshot":{"source":"iana"},"application/vnd.mfer":{"source":"iana","extensions":["mwf"]},"application/vnd.mfmp":{"source":"iana","extensions":["mfm"]},"application/vnd.micro+json":{"source":"iana","compressible":true},"application/vnd.micrografx.flo":{"source":"iana","extensions":["flo"]},"application/vnd.micrografx.igx":{"source":"iana","extensions":["igx"]},"application/vnd.microsoft.portable-executable":{"source":"iana"},"application/vnd.microsoft.windows.thumbnail-cache":{"source":"iana"},"application/vnd.miele+json":{"source":"iana","compressible":true},"application/vnd.mif":{"source":"iana","extensions":["mif"]},"application/vnd.minisoft-hp3000-save":{"source":"iana"},"application/vnd.mitsubishi.misty-guard.trustweb":{"source":"iana"},"application/vnd.mobius.daf":{"source":"iana","extensions":["daf"]},"application/vnd.mobius.dis":{"source":"iana","extensions":["dis"]},"application/vnd.mobius.mbk":{"source":"iana","extensions":["mbk"]},"application/vnd.mobius.mqy":{"source":"iana","extensions":["mqy"]},"application/vnd.mobius.msl":{"source":"iana","extensions":["msl"]},"application/vnd.mobius.plc":{"source":"iana","extensions":["plc"]},"application/vnd.mobius.txf":{"source":"iana","extensions":["txf"]},"application/vnd.mophun.application":{"source":"iana","extensions":["mpn"]},"application/vnd.mophun.certificate":{"source":"iana","extensions":["mpc"]},"application/vnd.motorola.flexsuite":{"source":"iana"},"application/vnd.motorola.flexsuite.adsi":{"source":"iana"},"application/vnd.motorola.flexsuite.fis":{"source":"iana"},"application/vnd.motorola.flexsuite.gotap":{"source":"iana"},"application/vnd.motorola.flexsuite.kmr":{"source":"iana"},"application/vnd.motorola.flexsuite.ttc":{"source":"iana"},"application/vnd.motorola.flexsuite.wem":{"source":"iana"},"application/vnd.motorola.iprm":{"source":"iana"},"application/vnd.mozilla.xul+xml":{"source":"iana","compressible":true,"extensions":["xul"]},"application/vnd.ms-3mfdocument":{"source":"iana"},"application/vnd.ms-artgalry":{"source":"iana","extensions":["cil"]},"application/vnd.ms-asf":{"source":"iana"},"application/vnd.ms-cab-compressed":{"source":"iana","extensions":["cab"]},"application/vnd.ms-color.iccprofile":{"source":"apache"},"application/vnd.ms-excel":{"source":"iana","compressible":false,"extensions":["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-excel.addin.macroenabled.12":{"source":"iana","extensions":["xlam"]},"application/vnd.ms-excel.sheet.binary.macroenabled.12":{"source":"iana","extensions":["xlsb"]},"application/vnd.ms-excel.sheet.macroenabled.12":{"source":"iana","extensions":["xlsm"]},"application/vnd.ms-excel.template.macroenabled.12":{"source":"iana","extensions":["xltm"]},"application/vnd.ms-fontobject":{"source":"iana","compressible":true,"extensions":["eot"]},"application/vnd.ms-htmlhelp":{"source":"iana","extensions":["chm"]},"application/vnd.ms-ims":{"source":"iana","extensions":["ims"]},"application/vnd.ms-lrm":{"source":"iana","extensions":["lrm"]},"application/vnd.ms-office.activex+xml":{"source":"iana","compressible":true},"application/vnd.ms-officetheme":{"source":"iana","extensions":["thmx"]},"application/vnd.ms-opentype":{"source":"apache","compressible":true},"application/vnd.ms-outlook":{"compressible":false,"extensions":["msg"]},"application/vnd.ms-package.obfuscated-opentype":{"source":"apache"},"application/vnd.ms-pki.seccat":{"source":"apache","extensions":["cat"]},"application/vnd.ms-pki.stl":{"source":"apache","extensions":["stl"]},"application/vnd.ms-playready.initiator+xml":{"source":"iana","compressible":true},"application/vnd.ms-powerpoint":{"source":"iana","compressible":false,"extensions":["ppt","pps","pot"]},"application/vnd.ms-powerpoint.addin.macroenabled.12":{"source":"iana","extensions":["ppam"]},"application/vnd.ms-powerpoint.presentation.macroenabled.12":{"source":"iana","extensions":["pptm"]},"application/vnd.ms-powerpoint.slide.macroenabled.12":{"source":"iana","extensions":["sldm"]},"application/vnd.ms-powerpoint.slideshow.macroenabled.12":{"source":"iana","extensions":["ppsm"]},"application/vnd.ms-powerpoint.template.macroenabled.12":{"source":"iana","extensions":["potm"]},"application/vnd.ms-printdevicecapabilities+xml":{"source":"iana","compressible":true},"application/vnd.ms-printing.printticket+xml":{"source":"apache","compressible":true},"application/vnd.ms-printschematicket+xml":{"source":"iana","compressible":true},"application/vnd.ms-project":{"source":"iana","extensions":["mpp","mpt"]},"application/vnd.ms-tnef":{"source":"iana"},"application/vnd.ms-windows.devicepairing":{"source":"iana"},"application/vnd.ms-windows.nwprinting.oob":{"source":"iana"},"application/vnd.ms-windows.printerpairing":{"source":"iana"},"application/vnd.ms-windows.wsd.oob":{"source":"iana"},"application/vnd.ms-wmdrm.lic-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.lic-resp":{"source":"iana"},"application/vnd.ms-wmdrm.meter-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.meter-resp":{"source":"iana"},"application/vnd.ms-word.document.macroenabled.12":{"source":"iana","extensions":["docm"]},"application/vnd.ms-word.template.macroenabled.12":{"source":"iana","extensions":["dotm"]},"application/vnd.ms-works":{"source":"iana","extensions":["wps","wks","wcm","wdb"]},"application/vnd.ms-wpl":{"source":"iana","extensions":["wpl"]},"application/vnd.ms-xpsdocument":{"source":"iana","compressible":false,"extensions":["xps"]},"application/vnd.msa-disk-image":{"source":"iana"},"application/vnd.mseq":{"source":"iana","extensions":["mseq"]},"application/vnd.msign":{"source":"iana"},"application/vnd.multiad.creator":{"source":"iana"},"application/vnd.multiad.creator.cif":{"source":"iana"},"application/vnd.music-niff":{"source":"iana"},"application/vnd.musician":{"source":"iana","extensions":["mus"]},"application/vnd.muvee.style":{"source":"iana","extensions":["msty"]},"application/vnd.mynfc":{"source":"iana","extensions":["taglet"]},"application/vnd.nacamar.ybrid+json":{"source":"iana","compressible":true},"application/vnd.ncd.control":{"source":"iana"},"application/vnd.ncd.reference":{"source":"iana"},"application/vnd.nearst.inv+json":{"source":"iana","compressible":true},"application/vnd.nebumind.line":{"source":"iana"},"application/vnd.nervana":{"source":"iana"},"application/vnd.netfpx":{"source":"iana"},"application/vnd.neurolanguage.nlu":{"source":"iana","extensions":["nlu"]},"application/vnd.nimn":{"source":"iana"},"application/vnd.nintendo.nitro.rom":{"source":"iana"},"application/vnd.nintendo.snes.rom":{"source":"iana"},"application/vnd.nitf":{"source":"iana","extensions":["ntf","nitf"]},"application/vnd.noblenet-directory":{"source":"iana","extensions":["nnd"]},"application/vnd.noblenet-sealer":{"source":"iana","extensions":["nns"]},"application/vnd.noblenet-web":{"source":"iana","extensions":["nnw"]},"application/vnd.nokia.catalogs":{"source":"iana"},"application/vnd.nokia.conml+wbxml":{"source":"iana"},"application/vnd.nokia.conml+xml":{"source":"iana","compressible":true},"application/vnd.nokia.iptv.config+xml":{"source":"iana","compressible":true},"application/vnd.nokia.isds-radio-presets":{"source":"iana"},"application/vnd.nokia.landmark+wbxml":{"source":"iana"},"application/vnd.nokia.landmark+xml":{"source":"iana","compressible":true},"application/vnd.nokia.landmarkcollection+xml":{"source":"iana","compressible":true},"application/vnd.nokia.n-gage.ac+xml":{"source":"iana","compressible":true,"extensions":["ac"]},"application/vnd.nokia.n-gage.data":{"source":"iana","extensions":["ngdat"]},"application/vnd.nokia.n-gage.symbian.install":{"source":"iana","extensions":["n-gage"]},"application/vnd.nokia.ncd":{"source":"iana"},"application/vnd.nokia.pcd+wbxml":{"source":"iana"},"application/vnd.nokia.pcd+xml":{"source":"iana","compressible":true},"application/vnd.nokia.radio-preset":{"source":"iana","extensions":["rpst"]},"application/vnd.nokia.radio-presets":{"source":"iana","extensions":["rpss"]},"application/vnd.novadigm.edm":{"source":"iana","extensions":["edm"]},"application/vnd.novadigm.edx":{"source":"iana","extensions":["edx"]},"application/vnd.novadigm.ext":{"source":"iana","extensions":["ext"]},"application/vnd.ntt-local.content-share":{"source":"iana"},"application/vnd.ntt-local.file-transfer":{"source":"iana"},"application/vnd.ntt-local.ogw_remote-access":{"source":"iana"},"application/vnd.ntt-local.sip-ta_remote":{"source":"iana"},"application/vnd.ntt-local.sip-ta_tcp_stream":{"source":"iana"},"application/vnd.oasis.opendocument.chart":{"source":"iana","extensions":["odc"]},"application/vnd.oasis.opendocument.chart-template":{"source":"iana","extensions":["otc"]},"application/vnd.oasis.opendocument.database":{"source":"iana","extensions":["odb"]},"application/vnd.oasis.opendocument.formula":{"source":"iana","extensions":["odf"]},"application/vnd.oasis.opendocument.formula-template":{"source":"iana","extensions":["odft"]},"application/vnd.oasis.opendocument.graphics":{"source":"iana","compressible":false,"extensions":["odg"]},"application/vnd.oasis.opendocument.graphics-template":{"source":"iana","extensions":["otg"]},"application/vnd.oasis.opendocument.image":{"source":"iana","extensions":["odi"]},"application/vnd.oasis.opendocument.image-template":{"source":"iana","extensions":["oti"]},"application/vnd.oasis.opendocument.presentation":{"source":"iana","compressible":false,"extensions":["odp"]},"application/vnd.oasis.opendocument.presentation-template":{"source":"iana","extensions":["otp"]},"application/vnd.oasis.opendocument.spreadsheet":{"source":"iana","compressible":false,"extensions":["ods"]},"application/vnd.oasis.opendocument.spreadsheet-template":{"source":"iana","extensions":["ots"]},"application/vnd.oasis.opendocument.text":{"source":"iana","compressible":false,"extensions":["odt"]},"application/vnd.oasis.opendocument.text-master":{"source":"iana","extensions":["odm"]},"application/vnd.oasis.opendocument.text-template":{"source":"iana","extensions":["ott"]},"application/vnd.oasis.opendocument.text-web":{"source":"iana","extensions":["oth"]},"application/vnd.obn":{"source":"iana"},"application/vnd.ocf+cbor":{"source":"iana"},"application/vnd.oci.image.manifest.v1+json":{"source":"iana","compressible":true},"application/vnd.oftn.l10n+json":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessdownload+xml":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessstreaming+xml":{"source":"iana","compressible":true},"application/vnd.oipf.cspg-hexbinary":{"source":"iana"},"application/vnd.oipf.dae.svg+xml":{"source":"iana","compressible":true},"application/vnd.oipf.dae.xhtml+xml":{"source":"iana","compressible":true},"application/vnd.oipf.mippvcontrolmessage+xml":{"source":"iana","compressible":true},"application/vnd.oipf.pae.gem":{"source":"iana"},"application/vnd.oipf.spdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.oipf.spdlist+xml":{"source":"iana","compressible":true},"application/vnd.oipf.ueprofile+xml":{"source":"iana","compressible":true},"application/vnd.oipf.userprofile+xml":{"source":"iana","compressible":true},"application/vnd.olpc-sugar":{"source":"iana","extensions":["xo"]},"application/vnd.oma-scws-config":{"source":"iana"},"application/vnd.oma-scws-http-request":{"source":"iana"},"application/vnd.oma-scws-http-response":{"source":"iana"},"application/vnd.oma.bcast.associated-procedure-parameter+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.drm-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.imd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.ltkm":{"source":"iana"},"application/vnd.oma.bcast.notification+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.provisioningtrigger":{"source":"iana"},"application/vnd.oma.bcast.sgboot":{"source":"iana"},"application/vnd.oma.bcast.sgdd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sgdu":{"source":"iana"},"application/vnd.oma.bcast.simple-symbol-container":{"source":"iana"},"application/vnd.oma.bcast.smartcard-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sprov+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.stkm":{"source":"iana"},"application/vnd.oma.cab-address-book+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-feature-handler+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-pcc+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-subs-invite+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-user-prefs+xml":{"source":"iana","compressible":true},"application/vnd.oma.dcd":{"source":"iana"},"application/vnd.oma.dcdc":{"source":"iana"},"application/vnd.oma.dd2+xml":{"source":"iana","compressible":true,"extensions":["dd2"]},"application/vnd.oma.drm.risd+xml":{"source":"iana","compressible":true},"application/vnd.oma.group-usage-list+xml":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+cbor":{"source":"iana"},"application/vnd.oma.lwm2m+json":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+tlv":{"source":"iana"},"application/vnd.oma.pal+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.detailed-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.final-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.groups+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.invocation-descriptor+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.optimized-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.push":{"source":"iana"},"application/vnd.oma.scidm.messages+xml":{"source":"iana","compressible":true},"application/vnd.oma.xcap-directory+xml":{"source":"iana","compressible":true},"application/vnd.omads-email+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-file+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-folder+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omaloc-supl-init":{"source":"iana"},"application/vnd.onepager":{"source":"iana"},"application/vnd.onepagertamp":{"source":"iana"},"application/vnd.onepagertamx":{"source":"iana"},"application/vnd.onepagertat":{"source":"iana"},"application/vnd.onepagertatp":{"source":"iana"},"application/vnd.onepagertatx":{"source":"iana"},"application/vnd.openblox.game+xml":{"source":"iana","compressible":true,"extensions":["obgx"]},"application/vnd.openblox.game-binary":{"source":"iana"},"application/vnd.openeye.oeb":{"source":"iana"},"application/vnd.openofficeorg.extension":{"source":"apache","extensions":["oxt"]},"application/vnd.openstreetmap.data+xml":{"source":"iana","compressible":true,"extensions":["osm"]},"application/vnd.opentimestamps.ots":{"source":"iana"},"application/vnd.openxmlformats-officedocument.custom-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.customxmlproperties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawing+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chart+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramcolors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramdata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramlayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramstyle+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.extended-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.commentauthors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.handoutmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesslide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{"source":"iana","compressible":false,"extensions":["pptx"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slide":{"source":"iana","extensions":["sldx"]},"application/vnd.openxmlformats-officedocument.presentationml.slide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidelayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidemaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideshow":{"source":"iana","extensions":["ppsx"]},"application/vnd.openxmlformats-officedocument.presentationml.slideshow.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideupdateinfo+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tablestyles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tags+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.template":{"source":"iana","extensions":["potx"]},"application/vnd.openxmlformats-officedocument.presentationml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.viewprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.calcchain+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.externallink+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcachedefinition+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcacherecords+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivottable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.querytable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionheaders+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionlog+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedstrings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{"source":"iana","compressible":false,"extensions":["xlsx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetmetadata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.tablesinglecells+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.template":{"source":"iana","extensions":["xltx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.usernames+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.volatiledependencies+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.theme+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.themeoverride+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.vmldrawing":{"source":"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{"source":"iana","compressible":false,"extensions":["docx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.glossary+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.fonttable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.template":{"source":"iana","extensions":["dotx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.websettings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.core-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.digital-signature-xmlsignature+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.relationships+xml":{"source":"iana","compressible":true},"application/vnd.oracle.resource+json":{"source":"iana","compressible":true},"application/vnd.orange.indata":{"source":"iana"},"application/vnd.osa.netdeploy":{"source":"iana"},"application/vnd.osgeo.mapguide.package":{"source":"iana","extensions":["mgp"]},"application/vnd.osgi.bundle":{"source":"iana"},"application/vnd.osgi.dp":{"source":"iana","extensions":["dp"]},"application/vnd.osgi.subsystem":{"source":"iana","extensions":["esa"]},"application/vnd.otps.ct-kip+xml":{"source":"iana","compressible":true},"application/vnd.oxli.countgraph":{"source":"iana"},"application/vnd.pagerduty+json":{"source":"iana","compressible":true},"application/vnd.palm":{"source":"iana","extensions":["pdb","pqa","oprc"]},"application/vnd.panoply":{"source":"iana"},"application/vnd.paos.xml":{"source":"iana"},"application/vnd.patentdive":{"source":"iana"},"application/vnd.patientecommsdoc":{"source":"iana"},"application/vnd.pawaafile":{"source":"iana","extensions":["paw"]},"application/vnd.pcos":{"source":"iana"},"application/vnd.pg.format":{"source":"iana","extensions":["str"]},"application/vnd.pg.osasli":{"source":"iana","extensions":["ei6"]},"application/vnd.piaccess.application-licence":{"source":"iana"},"application/vnd.picsel":{"source":"iana","extensions":["efif"]},"application/vnd.pmi.widget":{"source":"iana","extensions":["wg"]},"application/vnd.poc.group-advertisement+xml":{"source":"iana","compressible":true},"application/vnd.pocketlearn":{"source":"iana","extensions":["plf"]},"application/vnd.powerbuilder6":{"source":"iana","extensions":["pbd"]},"application/vnd.powerbuilder6-s":{"source":"iana"},"application/vnd.powerbuilder7":{"source":"iana"},"application/vnd.powerbuilder7-s":{"source":"iana"},"application/vnd.powerbuilder75":{"source":"iana"},"application/vnd.powerbuilder75-s":{"source":"iana"},"application/vnd.preminet":{"source":"iana"},"application/vnd.previewsystems.box":{"source":"iana","extensions":["box"]},"application/vnd.proteus.magazine":{"source":"iana","extensions":["mgz"]},"application/vnd.psfs":{"source":"iana"},"application/vnd.publishare-delta-tree":{"source":"iana","extensions":["qps"]},"application/vnd.pvi.ptid1":{"source":"iana","extensions":["ptid"]},"application/vnd.pwg-multiplexed":{"source":"iana"},"application/vnd.pwg-xhtml-print+xml":{"source":"iana","compressible":true},"application/vnd.qualcomm.brew-app-res":{"source":"iana"},"application/vnd.quarantainenet":{"source":"iana"},"application/vnd.quark.quarkxpress":{"source":"iana","extensions":["qxd","qxt","qwd","qwt","qxl","qxb"]},"application/vnd.quobject-quoxdocument":{"source":"iana"},"application/vnd.radisys.moml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conn+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-stream+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-base+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-detect+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-sendrecv+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-group+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-speech+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-transform+xml":{"source":"iana","compressible":true},"application/vnd.rainstor.data":{"source":"iana"},"application/vnd.rapid":{"source":"iana"},"application/vnd.rar":{"source":"iana","extensions":["rar"]},"application/vnd.realvnc.bed":{"source":"iana","extensions":["bed"]},"application/vnd.recordare.musicxml":{"source":"iana","extensions":["mxl"]},"application/vnd.recordare.musicxml+xml":{"source":"iana","compressible":true,"extensions":["musicxml"]},"application/vnd.renlearn.rlprint":{"source":"iana"},"application/vnd.resilient.logic":{"source":"iana"},"application/vnd.restful+json":{"source":"iana","compressible":true},"application/vnd.rig.cryptonote":{"source":"iana","extensions":["cryptonote"]},"application/vnd.rim.cod":{"source":"apache","extensions":["cod"]},"application/vnd.rn-realmedia":{"source":"apache","extensions":["rm"]},"application/vnd.rn-realmedia-vbr":{"source":"apache","extensions":["rmvb"]},"application/vnd.route66.link66+xml":{"source":"iana","compressible":true,"extensions":["link66"]},"application/vnd.rs-274x":{"source":"iana"},"application/vnd.ruckus.download":{"source":"iana"},"application/vnd.s3sms":{"source":"iana"},"application/vnd.sailingtracker.track":{"source":"iana","extensions":["st"]},"application/vnd.sar":{"source":"iana"},"application/vnd.sbm.cid":{"source":"iana"},"application/vnd.sbm.mid2":{"source":"iana"},"application/vnd.scribus":{"source":"iana"},"application/vnd.sealed.3df":{"source":"iana"},"application/vnd.sealed.csf":{"source":"iana"},"application/vnd.sealed.doc":{"source":"iana"},"application/vnd.sealed.eml":{"source":"iana"},"application/vnd.sealed.mht":{"source":"iana"},"application/vnd.sealed.net":{"source":"iana"},"application/vnd.sealed.ppt":{"source":"iana"},"application/vnd.sealed.tiff":{"source":"iana"},"application/vnd.sealed.xls":{"source":"iana"},"application/vnd.sealedmedia.softseal.html":{"source":"iana"},"application/vnd.sealedmedia.softseal.pdf":{"source":"iana"},"application/vnd.seemail":{"source":"iana","extensions":["see"]},"application/vnd.seis+json":{"source":"iana","compressible":true},"application/vnd.sema":{"source":"iana","extensions":["sema"]},"application/vnd.semd":{"source":"iana","extensions":["semd"]},"application/vnd.semf":{"source":"iana","extensions":["semf"]},"application/vnd.shade-save-file":{"source":"iana"},"application/vnd.shana.informed.formdata":{"source":"iana","extensions":["ifm"]},"application/vnd.shana.informed.formtemplate":{"source":"iana","extensions":["itp"]},"application/vnd.shana.informed.interchange":{"source":"iana","extensions":["iif"]},"application/vnd.shana.informed.package":{"source":"iana","extensions":["ipk"]},"application/vnd.shootproof+json":{"source":"iana","compressible":true},"application/vnd.shopkick+json":{"source":"iana","compressible":true},"application/vnd.shp":{"source":"iana"},"application/vnd.shx":{"source":"iana"},"application/vnd.sigrok.session":{"source":"iana"},"application/vnd.simtech-mindmapper":{"source":"iana","extensions":["twd","twds"]},"application/vnd.siren+json":{"source":"iana","compressible":true},"application/vnd.smaf":{"source":"iana","extensions":["mmf"]},"application/vnd.smart.notebook":{"source":"iana"},"application/vnd.smart.teacher":{"source":"iana","extensions":["teacher"]},"application/vnd.snesdev-page-table":{"source":"iana"},"application/vnd.software602.filler.form+xml":{"source":"iana","compressible":true,"extensions":["fo"]},"application/vnd.software602.filler.form-xml-zip":{"source":"iana"},"application/vnd.solent.sdkm+xml":{"source":"iana","compressible":true,"extensions":["sdkm","sdkd"]},"application/vnd.spotfire.dxp":{"source":"iana","extensions":["dxp"]},"application/vnd.spotfire.sfs":{"source":"iana","extensions":["sfs"]},"application/vnd.sqlite3":{"source":"iana"},"application/vnd.sss-cod":{"source":"iana"},"application/vnd.sss-dtf":{"source":"iana"},"application/vnd.sss-ntf":{"source":"iana"},"application/vnd.stardivision.calc":{"source":"apache","extensions":["sdc"]},"application/vnd.stardivision.draw":{"source":"apache","extensions":["sda"]},"application/vnd.stardivision.impress":{"source":"apache","extensions":["sdd"]},"application/vnd.stardivision.math":{"source":"apache","extensions":["smf"]},"application/vnd.stardivision.writer":{"source":"apache","extensions":["sdw","vor"]},"application/vnd.stardivision.writer-global":{"source":"apache","extensions":["sgl"]},"application/vnd.stepmania.package":{"source":"iana","extensions":["smzip"]},"application/vnd.stepmania.stepchart":{"source":"iana","extensions":["sm"]},"application/vnd.street-stream":{"source":"iana"},"application/vnd.sun.wadl+xml":{"source":"iana","compressible":true,"extensions":["wadl"]},"application/vnd.sun.xml.calc":{"source":"apache","extensions":["sxc"]},"application/vnd.sun.xml.calc.template":{"source":"apache","extensions":["stc"]},"application/vnd.sun.xml.draw":{"source":"apache","extensions":["sxd"]},"application/vnd.sun.xml.draw.template":{"source":"apache","extensions":["std"]},"application/vnd.sun.xml.impress":{"source":"apache","extensions":["sxi"]},"application/vnd.sun.xml.impress.template":{"source":"apache","extensions":["sti"]},"application/vnd.sun.xml.math":{"source":"apache","extensions":["sxm"]},"application/vnd.sun.xml.writer":{"source":"apache","extensions":["sxw"]},"application/vnd.sun.xml.writer.global":{"source":"apache","extensions":["sxg"]},"application/vnd.sun.xml.writer.template":{"source":"apache","extensions":["stw"]},"application/vnd.sus-calendar":{"source":"iana","extensions":["sus","susp"]},"application/vnd.svd":{"source":"iana","extensions":["svd"]},"application/vnd.swiftview-ics":{"source":"iana"},"application/vnd.sycle+xml":{"source":"iana","compressible":true},"application/vnd.syft+json":{"source":"iana","compressible":true},"application/vnd.symbian.install":{"source":"apache","extensions":["sis","sisx"]},"application/vnd.syncml+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xsm"]},"application/vnd.syncml.dm+wbxml":{"source":"iana","charset":"UTF-8","extensions":["bdm"]},"application/vnd.syncml.dm+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xdm"]},"application/vnd.syncml.dm.notification":{"source":"iana"},"application/vnd.syncml.dmddf+wbxml":{"source":"iana"},"application/vnd.syncml.dmddf+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["ddf"]},"application/vnd.syncml.dmtnds+wbxml":{"source":"iana"},"application/vnd.syncml.dmtnds+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.syncml.ds.notification":{"source":"iana"},"application/vnd.tableschema+json":{"source":"iana","compressible":true},"application/vnd.tao.intent-module-archive":{"source":"iana","extensions":["tao"]},"application/vnd.tcpdump.pcap":{"source":"iana","extensions":["pcap","cap","dmp"]},"application/vnd.think-cell.ppttc+json":{"source":"iana","compressible":true},"application/vnd.tmd.mediaflex.api+xml":{"source":"iana","compressible":true},"application/vnd.tml":{"source":"iana"},"application/vnd.tmobile-livetv":{"source":"iana","extensions":["tmo"]},"application/vnd.tri.onesource":{"source":"iana"},"application/vnd.trid.tpt":{"source":"iana","extensions":["tpt"]},"application/vnd.triscape.mxs":{"source":"iana","extensions":["mxs"]},"application/vnd.trueapp":{"source":"iana","extensions":["tra"]},"application/vnd.truedoc":{"source":"iana"},"application/vnd.ubisoft.webplayer":{"source":"iana"},"application/vnd.ufdl":{"source":"iana","extensions":["ufd","ufdl"]},"application/vnd.uiq.theme":{"source":"iana","extensions":["utz"]},"application/vnd.umajin":{"source":"iana","extensions":["umj"]},"application/vnd.unity":{"source":"iana","extensions":["unityweb"]},"application/vnd.uoml+xml":{"source":"iana","compressible":true,"extensions":["uoml"]},"application/vnd.uplanet.alert":{"source":"iana"},"application/vnd.uplanet.alert-wbxml":{"source":"iana"},"application/vnd.uplanet.bearer-choice":{"source":"iana"},"application/vnd.uplanet.bearer-choice-wbxml":{"source":"iana"},"application/vnd.uplanet.cacheop":{"source":"iana"},"application/vnd.uplanet.cacheop-wbxml":{"source":"iana"},"application/vnd.uplanet.channel":{"source":"iana"},"application/vnd.uplanet.channel-wbxml":{"source":"iana"},"application/vnd.uplanet.list":{"source":"iana"},"application/vnd.uplanet.list-wbxml":{"source":"iana"},"application/vnd.uplanet.listcmd":{"source":"iana"},"application/vnd.uplanet.listcmd-wbxml":{"source":"iana"},"application/vnd.uplanet.signal":{"source":"iana"},"application/vnd.uri-map":{"source":"iana"},"application/vnd.valve.source.material":{"source":"iana"},"application/vnd.vcx":{"source":"iana","extensions":["vcx"]},"application/vnd.vd-study":{"source":"iana"},"application/vnd.vectorworks":{"source":"iana"},"application/vnd.vel+json":{"source":"iana","compressible":true},"application/vnd.verimatrix.vcas":{"source":"iana"},"application/vnd.veritone.aion+json":{"source":"iana","compressible":true},"application/vnd.veryant.thin":{"source":"iana"},"application/vnd.ves.encrypted":{"source":"iana"},"application/vnd.vidsoft.vidconference":{"source":"iana"},"application/vnd.visio":{"source":"iana","extensions":["vsd","vst","vss","vsw"]},"application/vnd.visionary":{"source":"iana","extensions":["vis"]},"application/vnd.vividence.scriptfile":{"source":"iana"},"application/vnd.vsf":{"source":"iana","extensions":["vsf"]},"application/vnd.wap.sic":{"source":"iana"},"application/vnd.wap.slc":{"source":"iana"},"application/vnd.wap.wbxml":{"source":"iana","charset":"UTF-8","extensions":["wbxml"]},"application/vnd.wap.wmlc":{"source":"iana","extensions":["wmlc"]},"application/vnd.wap.wmlscriptc":{"source":"iana","extensions":["wmlsc"]},"application/vnd.webturbo":{"source":"iana","extensions":["wtb"]},"application/vnd.wfa.dpp":{"source":"iana"},"application/vnd.wfa.p2p":{"source":"iana"},"application/vnd.wfa.wsc":{"source":"iana"},"application/vnd.windows.devicepairing":{"source":"iana"},"application/vnd.wmc":{"source":"iana"},"application/vnd.wmf.bootstrap":{"source":"iana"},"application/vnd.wolfram.mathematica":{"source":"iana"},"application/vnd.wolfram.mathematica.package":{"source":"iana"},"application/vnd.wolfram.player":{"source":"iana","extensions":["nbp"]},"application/vnd.wordperfect":{"source":"iana","extensions":["wpd"]},"application/vnd.wqd":{"source":"iana","extensions":["wqd"]},"application/vnd.wrq-hp3000-labelled":{"source":"iana"},"application/vnd.wt.stf":{"source":"iana","extensions":["stf"]},"application/vnd.wv.csp+wbxml":{"source":"iana"},"application/vnd.wv.csp+xml":{"source":"iana","compressible":true},"application/vnd.wv.ssp+xml":{"source":"iana","compressible":true},"application/vnd.xacml+json":{"source":"iana","compressible":true},"application/vnd.xara":{"source":"iana","extensions":["xar"]},"application/vnd.xfdl":{"source":"iana","extensions":["xfdl"]},"application/vnd.xfdl.webform":{"source":"iana"},"application/vnd.xmi+xml":{"source":"iana","compressible":true},"application/vnd.xmpie.cpkg":{"source":"iana"},"application/vnd.xmpie.dpkg":{"source":"iana"},"application/vnd.xmpie.plan":{"source":"iana"},"application/vnd.xmpie.ppkg":{"source":"iana"},"application/vnd.xmpie.xlim":{"source":"iana"},"application/vnd.yamaha.hv-dic":{"source":"iana","extensions":["hvd"]},"application/vnd.yamaha.hv-script":{"source":"iana","extensions":["hvs"]},"application/vnd.yamaha.hv-voice":{"source":"iana","extensions":["hvp"]},"application/vnd.yamaha.openscoreformat":{"source":"iana","extensions":["osf"]},"application/vnd.yamaha.openscoreformat.osfpvg+xml":{"source":"iana","compressible":true,"extensions":["osfpvg"]},"application/vnd.yamaha.remote-setup":{"source":"iana"},"application/vnd.yamaha.smaf-audio":{"source":"iana","extensions":["saf"]},"application/vnd.yamaha.smaf-phrase":{"source":"iana","extensions":["spf"]},"application/vnd.yamaha.through-ngn":{"source":"iana"},"application/vnd.yamaha.tunnel-udpencap":{"source":"iana"},"application/vnd.yaoweme":{"source":"iana"},"application/vnd.yellowriver-custom-menu":{"source":"iana","extensions":["cmp"]},"application/vnd.youtube.yt":{"source":"iana"},"application/vnd.zul":{"source":"iana","extensions":["zir","zirz"]},"application/vnd.zzazz.deck+xml":{"source":"iana","compressible":true,"extensions":["zaz"]},"application/voicexml+xml":{"source":"iana","compressible":true,"extensions":["vxml"]},"application/voucher-cms+json":{"source":"iana","compressible":true},"application/vq-rtcpxr":{"source":"iana"},"application/wasm":{"source":"iana","compressible":true,"extensions":["wasm"]},"application/watcherinfo+xml":{"source":"iana","compressible":true,"extensions":["wif"]},"application/webpush-options+json":{"source":"iana","compressible":true},"application/whoispp-query":{"source":"iana"},"application/whoispp-response":{"source":"iana"},"application/widget":{"source":"iana","extensions":["wgt"]},"application/winhlp":{"source":"apache","extensions":["hlp"]},"application/wita":{"source":"iana"},"application/wordperfect5.1":{"source":"iana"},"application/wsdl+xml":{"source":"iana","compressible":true,"extensions":["wsdl"]},"application/wspolicy+xml":{"source":"iana","compressible":true,"extensions":["wspolicy"]},"application/x-7z-compressed":{"source":"apache","compressible":false,"extensions":["7z"]},"application/x-abiword":{"source":"apache","extensions":["abw"]},"application/x-ace-compressed":{"source":"apache","extensions":["ace"]},"application/x-amf":{"source":"apache"},"application/x-apple-diskimage":{"source":"apache","extensions":["dmg"]},"application/x-arj":{"compressible":false,"extensions":["arj"]},"application/x-authorware-bin":{"source":"apache","extensions":["aab","x32","u32","vox"]},"application/x-authorware-map":{"source":"apache","extensions":["aam"]},"application/x-authorware-seg":{"source":"apache","extensions":["aas"]},"application/x-bcpio":{"source":"apache","extensions":["bcpio"]},"application/x-bdoc":{"compressible":false,"extensions":["bdoc"]},"application/x-bittorrent":{"source":"apache","extensions":["torrent"]},"application/x-blorb":{"source":"apache","extensions":["blb","blorb"]},"application/x-bzip":{"source":"apache","compressible":false,"extensions":["bz"]},"application/x-bzip2":{"source":"apache","compressible":false,"extensions":["bz2","boz"]},"application/x-cbr":{"source":"apache","extensions":["cbr","cba","cbt","cbz","cb7"]},"application/x-cdlink":{"source":"apache","extensions":["vcd"]},"application/x-cfs-compressed":{"source":"apache","extensions":["cfs"]},"application/x-chat":{"source":"apache","extensions":["chat"]},"application/x-chess-pgn":{"source":"apache","extensions":["pgn"]},"application/x-chrome-extension":{"extensions":["crx"]},"application/x-cocoa":{"source":"nginx","extensions":["cco"]},"application/x-compress":{"source":"apache"},"application/x-conference":{"source":"apache","extensions":["nsc"]},"application/x-cpio":{"source":"apache","extensions":["cpio"]},"application/x-csh":{"source":"apache","extensions":["csh"]},"application/x-deb":{"compressible":false},"application/x-debian-package":{"source":"apache","extensions":["deb","udeb"]},"application/x-dgc-compressed":{"source":"apache","extensions":["dgc"]},"application/x-director":{"source":"apache","extensions":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"]},"application/x-doom":{"source":"apache","extensions":["wad"]},"application/x-dtbncx+xml":{"source":"apache","compressible":true,"extensions":["ncx"]},"application/x-dtbook+xml":{"source":"apache","compressible":true,"extensions":["dtb"]},"application/x-dtbresource+xml":{"source":"apache","compressible":true,"extensions":["res"]},"application/x-dvi":{"source":"apache","compressible":false,"extensions":["dvi"]},"application/x-envoy":{"source":"apache","extensions":["evy"]},"application/x-eva":{"source":"apache","extensions":["eva"]},"application/x-font-bdf":{"source":"apache","extensions":["bdf"]},"application/x-font-dos":{"source":"apache"},"application/x-font-framemaker":{"source":"apache"},"application/x-font-ghostscript":{"source":"apache","extensions":["gsf"]},"application/x-font-libgrx":{"source":"apache"},"application/x-font-linux-psf":{"source":"apache","extensions":["psf"]},"application/x-font-pcf":{"source":"apache","extensions":["pcf"]},"application/x-font-snf":{"source":"apache","extensions":["snf"]},"application/x-font-speedo":{"source":"apache"},"application/x-font-sunos-news":{"source":"apache"},"application/x-font-type1":{"source":"apache","extensions":["pfa","pfb","pfm","afm"]},"application/x-font-vfont":{"source":"apache"},"application/x-freearc":{"source":"apache","extensions":["arc"]},"application/x-futuresplash":{"source":"apache","extensions":["spl"]},"application/x-gca-compressed":{"source":"apache","extensions":["gca"]},"application/x-glulx":{"source":"apache","extensions":["ulx"]},"application/x-gnumeric":{"source":"apache","extensions":["gnumeric"]},"application/x-gramps-xml":{"source":"apache","extensions":["gramps"]},"application/x-gtar":{"source":"apache","extensions":["gtar"]},"application/x-gzip":{"source":"apache"},"application/x-hdf":{"source":"apache","extensions":["hdf"]},"application/x-httpd-php":{"compressible":true,"extensions":["php"]},"application/x-install-instructions":{"source":"apache","extensions":["install"]},"application/x-iso9660-image":{"source":"apache","extensions":["iso"]},"application/x-iwork-keynote-sffkey":{"extensions":["key"]},"application/x-iwork-numbers-sffnumbers":{"extensions":["numbers"]},"application/x-iwork-pages-sffpages":{"extensions":["pages"]},"application/x-java-archive-diff":{"source":"nginx","extensions":["jardiff"]},"application/x-java-jnlp-file":{"source":"apache","compressible":false,"extensions":["jnlp"]},"application/x-javascript":{"compressible":true},"application/x-keepass2":{"extensions":["kdbx"]},"application/x-latex":{"source":"apache","compressible":false,"extensions":["latex"]},"application/x-lua-bytecode":{"extensions":["luac"]},"application/x-lzh-compressed":{"source":"apache","extensions":["lzh","lha"]},"application/x-makeself":{"source":"nginx","extensions":["run"]},"application/x-mie":{"source":"apache","extensions":["mie"]},"application/x-mobipocket-ebook":{"source":"apache","extensions":["prc","mobi"]},"application/x-mpegurl":{"compressible":false},"application/x-ms-application":{"source":"apache","extensions":["application"]},"application/x-ms-shortcut":{"source":"apache","extensions":["lnk"]},"application/x-ms-wmd":{"source":"apache","extensions":["wmd"]},"application/x-ms-wmz":{"source":"apache","extensions":["wmz"]},"application/x-ms-xbap":{"source":"apache","extensions":["xbap"]},"application/x-msaccess":{"source":"apache","extensions":["mdb"]},"application/x-msbinder":{"source":"apache","extensions":["obd"]},"application/x-mscardfile":{"source":"apache","extensions":["crd"]},"application/x-msclip":{"source":"apache","extensions":["clp"]},"application/x-msdos-program":{"extensions":["exe"]},"application/x-msdownload":{"source":"apache","extensions":["exe","dll","com","bat","msi"]},"application/x-msmediaview":{"source":"apache","extensions":["mvb","m13","m14"]},"application/x-msmetafile":{"source":"apache","extensions":["wmf","wmz","emf","emz"]},"application/x-msmoney":{"source":"apache","extensions":["mny"]},"application/x-mspublisher":{"source":"apache","extensions":["pub"]},"application/x-msschedule":{"source":"apache","extensions":["scd"]},"application/x-msterminal":{"source":"apache","extensions":["trm"]},"application/x-mswrite":{"source":"apache","extensions":["wri"]},"application/x-netcdf":{"source":"apache","extensions":["nc","cdf"]},"application/x-ns-proxy-autoconfig":{"compressible":true,"extensions":["pac"]},"application/x-nzb":{"source":"apache","extensions":["nzb"]},"application/x-perl":{"source":"nginx","extensions":["pl","pm"]},"application/x-pilot":{"source":"nginx","extensions":["prc","pdb"]},"application/x-pkcs12":{"source":"apache","compressible":false,"extensions":["p12","pfx"]},"application/x-pkcs7-certificates":{"source":"apache","extensions":["p7b","spc"]},"application/x-pkcs7-certreqresp":{"source":"apache","extensions":["p7r"]},"application/x-pki-message":{"source":"iana"},"application/x-rar-compressed":{"source":"apache","compressible":false,"extensions":["rar"]},"application/x-redhat-package-manager":{"source":"nginx","extensions":["rpm"]},"application/x-research-info-systems":{"source":"apache","extensions":["ris"]},"application/x-sea":{"source":"nginx","extensions":["sea"]},"application/x-sh":{"source":"apache","compressible":true,"extensions":["sh"]},"application/x-shar":{"source":"apache","extensions":["shar"]},"application/x-shockwave-flash":{"source":"apache","compressible":false,"extensions":["swf"]},"application/x-silverlight-app":{"source":"apache","extensions":["xap"]},"application/x-sql":{"source":"apache","extensions":["sql"]},"application/x-stuffit":{"source":"apache","compressible":false,"extensions":["sit"]},"application/x-stuffitx":{"source":"apache","extensions":["sitx"]},"application/x-subrip":{"source":"apache","extensions":["srt"]},"application/x-sv4cpio":{"source":"apache","extensions":["sv4cpio"]},"application/x-sv4crc":{"source":"apache","extensions":["sv4crc"]},"application/x-t3vm-image":{"source":"apache","extensions":["t3"]},"application/x-tads":{"source":"apache","extensions":["gam"]},"application/x-tar":{"source":"apache","compressible":true,"extensions":["tar"]},"application/x-tcl":{"source":"apache","extensions":["tcl","tk"]},"application/x-tex":{"source":"apache","extensions":["tex"]},"application/x-tex-tfm":{"source":"apache","extensions":["tfm"]},"application/x-texinfo":{"source":"apache","extensions":["texinfo","texi"]},"application/x-tgif":{"source":"apache","extensions":["obj"]},"application/x-ustar":{"source":"apache","extensions":["ustar"]},"application/x-virtualbox-hdd":{"compressible":true,"extensions":["hdd"]},"application/x-virtualbox-ova":{"compressible":true,"extensions":["ova"]},"application/x-virtualbox-ovf":{"compressible":true,"extensions":["ovf"]},"application/x-virtualbox-vbox":{"compressible":true,"extensions":["vbox"]},"application/x-virtualbox-vbox-extpack":{"compressible":false,"extensions":["vbox-extpack"]},"application/x-virtualbox-vdi":{"compressible":true,"extensions":["vdi"]},"application/x-virtualbox-vhd":{"compressible":true,"extensions":["vhd"]},"application/x-virtualbox-vmdk":{"compressible":true,"extensions":["vmdk"]},"application/x-wais-source":{"source":"apache","extensions":["src"]},"application/x-web-app-manifest+json":{"compressible":true,"extensions":["webapp"]},"application/x-www-form-urlencoded":{"source":"iana","compressible":true},"application/x-x509-ca-cert":{"source":"iana","extensions":["der","crt","pem"]},"application/x-x509-ca-ra-cert":{"source":"iana"},"application/x-x509-next-ca-cert":{"source":"iana"},"application/x-xfig":{"source":"apache","extensions":["fig"]},"application/x-xliff+xml":{"source":"apache","compressible":true,"extensions":["xlf"]},"application/x-xpinstall":{"source":"apache","compressible":false,"extensions":["xpi"]},"application/x-xz":{"source":"apache","extensions":["xz"]},"application/x-zmachine":{"source":"apache","extensions":["z1","z2","z3","z4","z5","z6","z7","z8"]},"application/x400-bp":{"source":"iana"},"application/xacml+xml":{"source":"iana","compressible":true},"application/xaml+xml":{"source":"apache","compressible":true,"extensions":["xaml"]},"application/xcap-att+xml":{"source":"iana","compressible":true,"extensions":["xav"]},"application/xcap-caps+xml":{"source":"iana","compressible":true,"extensions":["xca"]},"application/xcap-diff+xml":{"source":"iana","compressible":true,"extensions":["xdf"]},"application/xcap-el+xml":{"source":"iana","compressible":true,"extensions":["xel"]},"application/xcap-error+xml":{"source":"iana","compressible":true},"application/xcap-ns+xml":{"source":"iana","compressible":true,"extensions":["xns"]},"application/xcon-conference-info+xml":{"source":"iana","compressible":true},"application/xcon-conference-info-diff+xml":{"source":"iana","compressible":true},"application/xenc+xml":{"source":"iana","compressible":true,"extensions":["xenc"]},"application/xhtml+xml":{"source":"iana","compressible":true,"extensions":["xhtml","xht"]},"application/xhtml-voice+xml":{"source":"apache","compressible":true},"application/xliff+xml":{"source":"iana","compressible":true,"extensions":["xlf"]},"application/xml":{"source":"iana","compressible":true,"extensions":["xml","xsl","xsd","rng"]},"application/xml-dtd":{"source":"iana","compressible":true,"extensions":["dtd"]},"application/xml-external-parsed-entity":{"source":"iana"},"application/xml-patch+xml":{"source":"iana","compressible":true},"application/xmpp+xml":{"source":"iana","compressible":true},"application/xop+xml":{"source":"iana","compressible":true,"extensions":["xop"]},"application/xproc+xml":{"source":"apache","compressible":true,"extensions":["xpl"]},"application/xslt+xml":{"source":"iana","compressible":true,"extensions":["xsl","xslt"]},"application/xspf+xml":{"source":"apache","compressible":true,"extensions":["xspf"]},"application/xv+xml":{"source":"iana","compressible":true,"extensions":["mxml","xhvml","xvml","xvm"]},"application/yang":{"source":"iana","extensions":["yang"]},"application/yang-data+json":{"source":"iana","compressible":true},"application/yang-data+xml":{"source":"iana","compressible":true},"application/yang-patch+json":{"source":"iana","compressible":true},"application/yang-patch+xml":{"source":"iana","compressible":true},"application/yin+xml":{"source":"iana","compressible":true,"extensions":["yin"]},"application/zip":{"source":"iana","compressible":false,"extensions":["zip"]},"application/zlib":{"source":"iana"},"application/zstd":{"source":"iana"},"audio/1d-interleaved-parityfec":{"source":"iana"},"audio/32kadpcm":{"source":"iana"},"audio/3gpp":{"source":"iana","compressible":false,"extensions":["3gpp"]},"audio/3gpp2":{"source":"iana"},"audio/aac":{"source":"iana"},"audio/ac3":{"source":"iana"},"audio/adpcm":{"source":"apache","extensions":["adp"]},"audio/amr":{"source":"iana","extensions":["amr"]},"audio/amr-wb":{"source":"iana"},"audio/amr-wb+":{"source":"iana"},"audio/aptx":{"source":"iana"},"audio/asc":{"source":"iana"},"audio/atrac-advanced-lossless":{"source":"iana"},"audio/atrac-x":{"source":"iana"},"audio/atrac3":{"source":"iana"},"audio/basic":{"source":"iana","compressible":false,"extensions":["au","snd"]},"audio/bv16":{"source":"iana"},"audio/bv32":{"source":"iana"},"audio/clearmode":{"source":"iana"},"audio/cn":{"source":"iana"},"audio/dat12":{"source":"iana"},"audio/dls":{"source":"iana"},"audio/dsr-es201108":{"source":"iana"},"audio/dsr-es202050":{"source":"iana"},"audio/dsr-es202211":{"source":"iana"},"audio/dsr-es202212":{"source":"iana"},"audio/dv":{"source":"iana"},"audio/dvi4":{"source":"iana"},"audio/eac3":{"source":"iana"},"audio/encaprtp":{"source":"iana"},"audio/evrc":{"source":"iana"},"audio/evrc-qcp":{"source":"iana"},"audio/evrc0":{"source":"iana"},"audio/evrc1":{"source":"iana"},"audio/evrcb":{"source":"iana"},"audio/evrcb0":{"source":"iana"},"audio/evrcb1":{"source":"iana"},"audio/evrcnw":{"source":"iana"},"audio/evrcnw0":{"source":"iana"},"audio/evrcnw1":{"source":"iana"},"audio/evrcwb":{"source":"iana"},"audio/evrcwb0":{"source":"iana"},"audio/evrcwb1":{"source":"iana"},"audio/evs":{"source":"iana"},"audio/flexfec":{"source":"iana"},"audio/fwdred":{"source":"iana"},"audio/g711-0":{"source":"iana"},"audio/g719":{"source":"iana"},"audio/g722":{"source":"iana"},"audio/g7221":{"source":"iana"},"audio/g723":{"source":"iana"},"audio/g726-16":{"source":"iana"},"audio/g726-24":{"source":"iana"},"audio/g726-32":{"source":"iana"},"audio/g726-40":{"source":"iana"},"audio/g728":{"source":"iana"},"audio/g729":{"source":"iana"},"audio/g7291":{"source":"iana"},"audio/g729d":{"source":"iana"},"audio/g729e":{"source":"iana"},"audio/gsm":{"source":"iana"},"audio/gsm-efr":{"source":"iana"},"audio/gsm-hr-08":{"source":"iana"},"audio/ilbc":{"source":"iana"},"audio/ip-mr_v2.5":{"source":"iana"},"audio/isac":{"source":"apache"},"audio/l16":{"source":"iana"},"audio/l20":{"source":"iana"},"audio/l24":{"source":"iana","compressible":false},"audio/l8":{"source":"iana"},"audio/lpc":{"source":"iana"},"audio/melp":{"source":"iana"},"audio/melp1200":{"source":"iana"},"audio/melp2400":{"source":"iana"},"audio/melp600":{"source":"iana"},"audio/mhas":{"source":"iana"},"audio/midi":{"source":"apache","extensions":["mid","midi","kar","rmi"]},"audio/mobile-xmf":{"source":"iana","extensions":["mxmf"]},"audio/mp3":{"compressible":false,"extensions":["mp3"]},"audio/mp4":{"source":"iana","compressible":false,"extensions":["m4a","mp4a"]},"audio/mp4a-latm":{"source":"iana"},"audio/mpa":{"source":"iana"},"audio/mpa-robust":{"source":"iana"},"audio/mpeg":{"source":"iana","compressible":false,"extensions":["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/mpeg4-generic":{"source":"iana"},"audio/musepack":{"source":"apache"},"audio/ogg":{"source":"iana","compressible":false,"extensions":["oga","ogg","spx","opus"]},"audio/opus":{"source":"iana"},"audio/parityfec":{"source":"iana"},"audio/pcma":{"source":"iana"},"audio/pcma-wb":{"source":"iana"},"audio/pcmu":{"source":"iana"},"audio/pcmu-wb":{"source":"iana"},"audio/prs.sid":{"source":"iana"},"audio/qcelp":{"source":"iana"},"audio/raptorfec":{"source":"iana"},"audio/red":{"source":"iana"},"audio/rtp-enc-aescm128":{"source":"iana"},"audio/rtp-midi":{"source":"iana"},"audio/rtploopback":{"source":"iana"},"audio/rtx":{"source":"iana"},"audio/s3m":{"source":"apache","extensions":["s3m"]},"audio/scip":{"source":"iana"},"audio/silk":{"source":"apache","extensions":["sil"]},"audio/smv":{"source":"iana"},"audio/smv-qcp":{"source":"iana"},"audio/smv0":{"source":"iana"},"audio/sofa":{"source":"iana"},"audio/sp-midi":{"source":"iana"},"audio/speex":{"source":"iana"},"audio/t140c":{"source":"iana"},"audio/t38":{"source":"iana"},"audio/telephone-event":{"source":"iana"},"audio/tetra_acelp":{"source":"iana"},"audio/tetra_acelp_bb":{"source":"iana"},"audio/tone":{"source":"iana"},"audio/tsvcis":{"source":"iana"},"audio/uemclip":{"source":"iana"},"audio/ulpfec":{"source":"iana"},"audio/usac":{"source":"iana"},"audio/vdvi":{"source":"iana"},"audio/vmr-wb":{"source":"iana"},"audio/vnd.3gpp.iufp":{"source":"iana"},"audio/vnd.4sb":{"source":"iana"},"audio/vnd.audiokoz":{"source":"iana"},"audio/vnd.celp":{"source":"iana"},"audio/vnd.cisco.nse":{"source":"iana"},"audio/vnd.cmles.radio-events":{"source":"iana"},"audio/vnd.cns.anp1":{"source":"iana"},"audio/vnd.cns.inf1":{"source":"iana"},"audio/vnd.dece.audio":{"source":"iana","extensions":["uva","uvva"]},"audio/vnd.digital-winds":{"source":"iana","extensions":["eol"]},"audio/vnd.dlna.adts":{"source":"iana"},"audio/vnd.dolby.heaac.1":{"source":"iana"},"audio/vnd.dolby.heaac.2":{"source":"iana"},"audio/vnd.dolby.mlp":{"source":"iana"},"audio/vnd.dolby.mps":{"source":"iana"},"audio/vnd.dolby.pl2":{"source":"iana"},"audio/vnd.dolby.pl2x":{"source":"iana"},"audio/vnd.dolby.pl2z":{"source":"iana"},"audio/vnd.dolby.pulse.1":{"source":"iana"},"audio/vnd.dra":{"source":"iana","extensions":["dra"]},"audio/vnd.dts":{"source":"iana","extensions":["dts"]},"audio/vnd.dts.hd":{"source":"iana","extensions":["dtshd"]},"audio/vnd.dts.uhd":{"source":"iana"},"audio/vnd.dvb.file":{"source":"iana"},"audio/vnd.everad.plj":{"source":"iana"},"audio/vnd.hns.audio":{"source":"iana"},"audio/vnd.lucent.voice":{"source":"iana","extensions":["lvp"]},"audio/vnd.ms-playready.media.pya":{"source":"iana","extensions":["pya"]},"audio/vnd.nokia.mobile-xmf":{"source":"iana"},"audio/vnd.nortel.vbk":{"source":"iana"},"audio/vnd.nuera.ecelp4800":{"source":"iana","extensions":["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{"source":"iana","extensions":["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{"source":"iana","extensions":["ecelp9600"]},"audio/vnd.octel.sbc":{"source":"iana"},"audio/vnd.presonus.multitrack":{"source":"iana"},"audio/vnd.qcelp":{"source":"iana"},"audio/vnd.rhetorex.32kadpcm":{"source":"iana"},"audio/vnd.rip":{"source":"iana","extensions":["rip"]},"audio/vnd.rn-realaudio":{"compressible":false},"audio/vnd.sealedmedia.softseal.mpeg":{"source":"iana"},"audio/vnd.vmx.cvsd":{"source":"iana"},"audio/vnd.wave":{"compressible":false},"audio/vorbis":{"source":"iana","compressible":false},"audio/vorbis-config":{"source":"iana"},"audio/wav":{"compressible":false,"extensions":["wav"]},"audio/wave":{"compressible":false,"extensions":["wav"]},"audio/webm":{"source":"apache","compressible":false,"extensions":["weba"]},"audio/x-aac":{"source":"apache","compressible":false,"extensions":["aac"]},"audio/x-aiff":{"source":"apache","extensions":["aif","aiff","aifc"]},"audio/x-caf":{"source":"apache","compressible":false,"extensions":["caf"]},"audio/x-flac":{"source":"apache","extensions":["flac"]},"audio/x-m4a":{"source":"nginx","extensions":["m4a"]},"audio/x-matroska":{"source":"apache","extensions":["mka"]},"audio/x-mpegurl":{"source":"apache","extensions":["m3u"]},"audio/x-ms-wax":{"source":"apache","extensions":["wax"]},"audio/x-ms-wma":{"source":"apache","extensions":["wma"]},"audio/x-pn-realaudio":{"source":"apache","extensions":["ram","ra"]},"audio/x-pn-realaudio-plugin":{"source":"apache","extensions":["rmp"]},"audio/x-realaudio":{"source":"nginx","extensions":["ra"]},"audio/x-tta":{"source":"apache"},"audio/x-wav":{"source":"apache","extensions":["wav"]},"audio/xm":{"source":"apache","extensions":["xm"]},"chemical/x-cdx":{"source":"apache","extensions":["cdx"]},"chemical/x-cif":{"source":"apache","extensions":["cif"]},"chemical/x-cmdf":{"source":"apache","extensions":["cmdf"]},"chemical/x-cml":{"source":"apache","extensions":["cml"]},"chemical/x-csml":{"source":"apache","extensions":["csml"]},"chemical/x-pdb":{"source":"apache"},"chemical/x-xyz":{"source":"apache","extensions":["xyz"]},"font/collection":{"source":"iana","extensions":["ttc"]},"font/otf":{"source":"iana","compressible":true,"extensions":["otf"]},"font/sfnt":{"source":"iana"},"font/ttf":{"source":"iana","compressible":true,"extensions":["ttf"]},"font/woff":{"source":"iana","extensions":["woff"]},"font/woff2":{"source":"iana","extensions":["woff2"]},"image/aces":{"source":"iana","extensions":["exr"]},"image/apng":{"compressible":false,"extensions":["apng"]},"image/avci":{"source":"iana","extensions":["avci"]},"image/avcs":{"source":"iana","extensions":["avcs"]},"image/avif":{"source":"iana","compressible":false,"extensions":["avif"]},"image/bmp":{"source":"iana","compressible":true,"extensions":["bmp"]},"image/cgm":{"source":"iana","extensions":["cgm"]},"image/dicom-rle":{"source":"iana","extensions":["drle"]},"image/emf":{"source":"iana","extensions":["emf"]},"image/fits":{"source":"iana","extensions":["fits"]},"image/g3fax":{"source":"iana","extensions":["g3"]},"image/gif":{"source":"iana","compressible":false,"extensions":["gif"]},"image/heic":{"source":"iana","extensions":["heic"]},"image/heic-sequence":{"source":"iana","extensions":["heics"]},"image/heif":{"source":"iana","extensions":["heif"]},"image/heif-sequence":{"source":"iana","extensions":["heifs"]},"image/hej2k":{"source":"iana","extensions":["hej2"]},"image/hsj2":{"source":"iana","extensions":["hsj2"]},"image/ief":{"source":"iana","extensions":["ief"]},"image/jls":{"source":"iana","extensions":["jls"]},"image/jp2":{"source":"iana","compressible":false,"extensions":["jp2","jpg2"]},"image/jpeg":{"source":"iana","compressible":false,"extensions":["jpeg","jpg","jpe"]},"image/jph":{"source":"iana","extensions":["jph"]},"image/jphc":{"source":"iana","extensions":["jhc"]},"image/jpm":{"source":"iana","compressible":false,"extensions":["jpm"]},"image/jpx":{"source":"iana","compressible":false,"extensions":["jpx","jpf"]},"image/jxr":{"source":"iana","extensions":["jxr"]},"image/jxra":{"source":"iana","extensions":["jxra"]},"image/jxrs":{"source":"iana","extensions":["jxrs"]},"image/jxs":{"source":"iana","extensions":["jxs"]},"image/jxsc":{"source":"iana","extensions":["jxsc"]},"image/jxsi":{"source":"iana","extensions":["jxsi"]},"image/jxss":{"source":"iana","extensions":["jxss"]},"image/ktx":{"source":"iana","extensions":["ktx"]},"image/ktx2":{"source":"iana","extensions":["ktx2"]},"image/naplps":{"source":"iana"},"image/pjpeg":{"compressible":false},"image/png":{"source":"iana","compressible":false,"extensions":["png"]},"image/prs.btif":{"source":"iana","extensions":["btif"]},"image/prs.pti":{"source":"iana","extensions":["pti"]},"image/pwg-raster":{"source":"iana"},"image/sgi":{"source":"apache","extensions":["sgi"]},"image/svg+xml":{"source":"iana","compressible":true,"extensions":["svg","svgz"]},"image/t38":{"source":"iana","extensions":["t38"]},"image/tiff":{"source":"iana","compressible":false,"extensions":["tif","tiff"]},"image/tiff-fx":{"source":"iana","extensions":["tfx"]},"image/vnd.adobe.photoshop":{"source":"iana","compressible":true,"extensions":["psd"]},"image/vnd.airzip.accelerator.azv":{"source":"iana","extensions":["azv"]},"image/vnd.cns.inf2":{"source":"iana"},"image/vnd.dece.graphic":{"source":"iana","extensions":["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{"source":"iana","extensions":["djvu","djv"]},"image/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"image/vnd.dwg":{"source":"iana","extensions":["dwg"]},"image/vnd.dxf":{"source":"iana","extensions":["dxf"]},"image/vnd.fastbidsheet":{"source":"iana","extensions":["fbs"]},"image/vnd.fpx":{"source":"iana","extensions":["fpx"]},"image/vnd.fst":{"source":"iana","extensions":["fst"]},"image/vnd.fujixerox.edmics-mmr":{"source":"iana","extensions":["mmr"]},"image/vnd.fujixerox.edmics-rlc":{"source":"iana","extensions":["rlc"]},"image/vnd.globalgraphics.pgb":{"source":"iana"},"image/vnd.microsoft.icon":{"source":"iana","compressible":true,"extensions":["ico"]},"image/vnd.mix":{"source":"iana"},"image/vnd.mozilla.apng":{"source":"iana"},"image/vnd.ms-dds":{"compressible":true,"extensions":["dds"]},"image/vnd.ms-modi":{"source":"iana","extensions":["mdi"]},"image/vnd.ms-photo":{"source":"apache","extensions":["wdp"]},"image/vnd.net-fpx":{"source":"iana","extensions":["npx"]},"image/vnd.pco.b16":{"source":"iana","extensions":["b16"]},"image/vnd.radiance":{"source":"iana"},"image/vnd.sealed.png":{"source":"iana"},"image/vnd.sealedmedia.softseal.gif":{"source":"iana"},"image/vnd.sealedmedia.softseal.jpg":{"source":"iana"},"image/vnd.svf":{"source":"iana"},"image/vnd.tencent.tap":{"source":"iana","extensions":["tap"]},"image/vnd.valve.source.texture":{"source":"iana","extensions":["vtf"]},"image/vnd.wap.wbmp":{"source":"iana","extensions":["wbmp"]},"image/vnd.xiff":{"source":"iana","extensions":["xif"]},"image/vnd.zbrush.pcx":{"source":"iana","extensions":["pcx"]},"image/webp":{"source":"apache","extensions":["webp"]},"image/wmf":{"source":"iana","extensions":["wmf"]},"image/x-3ds":{"source":"apache","extensions":["3ds"]},"image/x-cmu-raster":{"source":"apache","extensions":["ras"]},"image/x-cmx":{"source":"apache","extensions":["cmx"]},"image/x-freehand":{"source":"apache","extensions":["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{"source":"apache","compressible":true,"extensions":["ico"]},"image/x-jng":{"source":"nginx","extensions":["jng"]},"image/x-mrsid-image":{"source":"apache","extensions":["sid"]},"image/x-ms-bmp":{"source":"nginx","compressible":true,"extensions":["bmp"]},"image/x-pcx":{"source":"apache","extensions":["pcx"]},"image/x-pict":{"source":"apache","extensions":["pic","pct"]},"image/x-portable-anymap":{"source":"apache","extensions":["pnm"]},"image/x-portable-bitmap":{"source":"apache","extensions":["pbm"]},"image/x-portable-graymap":{"source":"apache","extensions":["pgm"]},"image/x-portable-pixmap":{"source":"apache","extensions":["ppm"]},"image/x-rgb":{"source":"apache","extensions":["rgb"]},"image/x-tga":{"source":"apache","extensions":["tga"]},"image/x-xbitmap":{"source":"apache","extensions":["xbm"]},"image/x-xcf":{"compressible":false},"image/x-xpixmap":{"source":"apache","extensions":["xpm"]},"image/x-xwindowdump":{"source":"apache","extensions":["xwd"]},"message/cpim":{"source":"iana"},"message/delivery-status":{"source":"iana"},"message/disposition-notification":{"source":"iana","extensions":["disposition-notification"]},"message/external-body":{"source":"iana"},"message/feedback-report":{"source":"iana"},"message/global":{"source":"iana","extensions":["u8msg"]},"message/global-delivery-status":{"source":"iana","extensions":["u8dsn"]},"message/global-disposition-notification":{"source":"iana","extensions":["u8mdn"]},"message/global-headers":{"source":"iana","extensions":["u8hdr"]},"message/http":{"source":"iana","compressible":false},"message/imdn+xml":{"source":"iana","compressible":true},"message/news":{"source":"iana"},"message/partial":{"source":"iana","compressible":false},"message/rfc822":{"source":"iana","compressible":true,"extensions":["eml","mime"]},"message/s-http":{"source":"iana"},"message/sip":{"source":"iana"},"message/sipfrag":{"source":"iana"},"message/tracking-status":{"source":"iana"},"message/vnd.si.simp":{"source":"iana"},"message/vnd.wfa.wsc":{"source":"iana","extensions":["wsc"]},"model/3mf":{"source":"iana","extensions":["3mf"]},"model/e57":{"source":"iana"},"model/gltf+json":{"source":"iana","compressible":true,"extensions":["gltf"]},"model/gltf-binary":{"source":"iana","compressible":true,"extensions":["glb"]},"model/iges":{"source":"iana","compressible":false,"extensions":["igs","iges"]},"model/mesh":{"source":"iana","compressible":false,"extensions":["msh","mesh","silo"]},"model/mtl":{"source":"iana","extensions":["mtl"]},"model/obj":{"source":"iana","extensions":["obj"]},"model/step":{"source":"iana"},"model/step+xml":{"source":"iana","compressible":true,"extensions":["stpx"]},"model/step+zip":{"source":"iana","compressible":false,"extensions":["stpz"]},"model/step-xml+zip":{"source":"iana","compressible":false,"extensions":["stpxz"]},"model/stl":{"source":"iana","extensions":["stl"]},"model/vnd.collada+xml":{"source":"iana","compressible":true,"extensions":["dae"]},"model/vnd.dwf":{"source":"iana","extensions":["dwf"]},"model/vnd.flatland.3dml":{"source":"iana"},"model/vnd.gdl":{"source":"iana","extensions":["gdl"]},"model/vnd.gs-gdl":{"source":"apache"},"model/vnd.gs.gdl":{"source":"iana"},"model/vnd.gtw":{"source":"iana","extensions":["gtw"]},"model/vnd.moml+xml":{"source":"iana","compressible":true},"model/vnd.mts":{"source":"iana","extensions":["mts"]},"model/vnd.opengex":{"source":"iana","extensions":["ogex"]},"model/vnd.parasolid.transmit.binary":{"source":"iana","extensions":["x_b"]},"model/vnd.parasolid.transmit.text":{"source":"iana","extensions":["x_t"]},"model/vnd.pytha.pyox":{"source":"iana"},"model/vnd.rosette.annotated-data-model":{"source":"iana"},"model/vnd.sap.vds":{"source":"iana","extensions":["vds"]},"model/vnd.usdz+zip":{"source":"iana","compressible":false,"extensions":["usdz"]},"model/vnd.valve.source.compiled-map":{"source":"iana","extensions":["bsp"]},"model/vnd.vtu":{"source":"iana","extensions":["vtu"]},"model/vrml":{"source":"iana","compressible":false,"extensions":["wrl","vrml"]},"model/x3d+binary":{"source":"apache","compressible":false,"extensions":["x3db","x3dbz"]},"model/x3d+fastinfoset":{"source":"iana","extensions":["x3db"]},"model/x3d+vrml":{"source":"apache","compressible":false,"extensions":["x3dv","x3dvz"]},"model/x3d+xml":{"source":"iana","compressible":true,"extensions":["x3d","x3dz"]},"model/x3d-vrml":{"source":"iana","extensions":["x3dv"]},"multipart/alternative":{"source":"iana","compressible":false},"multipart/appledouble":{"source":"iana"},"multipart/byteranges":{"source":"iana"},"multipart/digest":{"source":"iana"},"multipart/encrypted":{"source":"iana","compressible":false},"multipart/form-data":{"source":"iana","compressible":false},"multipart/header-set":{"source":"iana"},"multipart/mixed":{"source":"iana"},"multipart/multilingual":{"source":"iana"},"multipart/parallel":{"source":"iana"},"multipart/related":{"source":"iana","compressible":false},"multipart/report":{"source":"iana"},"multipart/signed":{"source":"iana","compressible":false},"multipart/vnd.bint.med-plus":{"source":"iana"},"multipart/voice-message":{"source":"iana"},"multipart/x-mixed-replace":{"source":"iana"},"text/1d-interleaved-parityfec":{"source":"iana"},"text/cache-manifest":{"source":"iana","compressible":true,"extensions":["appcache","manifest"]},"text/calendar":{"source":"iana","extensions":["ics","ifb"]},"text/calender":{"compressible":true},"text/cmd":{"compressible":true},"text/coffeescript":{"extensions":["coffee","litcoffee"]},"text/cql":{"source":"iana"},"text/cql-expression":{"source":"iana"},"text/cql-identifier":{"source":"iana"},"text/css":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["css"]},"text/csv":{"source":"iana","compressible":true,"extensions":["csv"]},"text/csv-schema":{"source":"iana"},"text/directory":{"source":"iana"},"text/dns":{"source":"iana"},"text/ecmascript":{"source":"iana"},"text/encaprtp":{"source":"iana"},"text/enriched":{"source":"iana"},"text/fhirpath":{"source":"iana"},"text/flexfec":{"source":"iana"},"text/fwdred":{"source":"iana"},"text/gff3":{"source":"iana"},"text/grammar-ref-list":{"source":"iana"},"text/html":{"source":"iana","compressible":true,"extensions":["html","htm","shtml"]},"text/jade":{"extensions":["jade"]},"text/javascript":{"source":"iana","compressible":true},"text/jcr-cnd":{"source":"iana"},"text/jsx":{"compressible":true,"extensions":["jsx"]},"text/less":{"compressible":true,"extensions":["less"]},"text/markdown":{"source":"iana","compressible":true,"extensions":["markdown","md"]},"text/mathml":{"source":"nginx","extensions":["mml"]},"text/mdx":{"compressible":true,"extensions":["mdx"]},"text/mizar":{"source":"iana"},"text/n3":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["n3"]},"text/parameters":{"source":"iana","charset":"UTF-8"},"text/parityfec":{"source":"iana"},"text/plain":{"source":"iana","compressible":true,"extensions":["txt","text","conf","def","list","log","in","ini"]},"text/provenance-notation":{"source":"iana","charset":"UTF-8"},"text/prs.fallenstein.rst":{"source":"iana"},"text/prs.lines.tag":{"source":"iana","extensions":["dsc"]},"text/prs.prop.logic":{"source":"iana"},"text/raptorfec":{"source":"iana"},"text/red":{"source":"iana"},"text/rfc822-headers":{"source":"iana"},"text/richtext":{"source":"iana","compressible":true,"extensions":["rtx"]},"text/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"text/rtp-enc-aescm128":{"source":"iana"},"text/rtploopback":{"source":"iana"},"text/rtx":{"source":"iana"},"text/sgml":{"source":"iana","extensions":["sgml","sgm"]},"text/shaclc":{"source":"iana"},"text/shex":{"source":"iana","extensions":["shex"]},"text/slim":{"extensions":["slim","slm"]},"text/spdx":{"source":"iana","extensions":["spdx"]},"text/strings":{"source":"iana"},"text/stylus":{"extensions":["stylus","styl"]},"text/t140":{"source":"iana"},"text/tab-separated-values":{"source":"iana","compressible":true,"extensions":["tsv"]},"text/troff":{"source":"iana","extensions":["t","tr","roff","man","me","ms"]},"text/turtle":{"source":"iana","charset":"UTF-8","extensions":["ttl"]},"text/ulpfec":{"source":"iana"},"text/uri-list":{"source":"iana","compressible":true,"extensions":["uri","uris","urls"]},"text/vcard":{"source":"iana","compressible":true,"extensions":["vcard"]},"text/vnd.a":{"source":"iana"},"text/vnd.abc":{"source":"iana"},"text/vnd.ascii-art":{"source":"iana"},"text/vnd.curl":{"source":"iana","extensions":["curl"]},"text/vnd.curl.dcurl":{"source":"apache","extensions":["dcurl"]},"text/vnd.curl.mcurl":{"source":"apache","extensions":["mcurl"]},"text/vnd.curl.scurl":{"source":"apache","extensions":["scurl"]},"text/vnd.debian.copyright":{"source":"iana","charset":"UTF-8"},"text/vnd.dmclientscript":{"source":"iana"},"text/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"text/vnd.esmertec.theme-descriptor":{"source":"iana","charset":"UTF-8"},"text/vnd.familysearch.gedcom":{"source":"iana","extensions":["ged"]},"text/vnd.ficlab.flt":{"source":"iana"},"text/vnd.fly":{"source":"iana","extensions":["fly"]},"text/vnd.fmi.flexstor":{"source":"iana","extensions":["flx"]},"text/vnd.gml":{"source":"iana"},"text/vnd.graphviz":{"source":"iana","extensions":["gv"]},"text/vnd.hans":{"source":"iana"},"text/vnd.hgl":{"source":"iana"},"text/vnd.in3d.3dml":{"source":"iana","extensions":["3dml"]},"text/vnd.in3d.spot":{"source":"iana","extensions":["spot"]},"text/vnd.iptc.newsml":{"source":"iana"},"text/vnd.iptc.nitf":{"source":"iana"},"text/vnd.latex-z":{"source":"iana"},"text/vnd.motorola.reflex":{"source":"iana"},"text/vnd.ms-mediapackage":{"source":"iana"},"text/vnd.net2phone.commcenter.command":{"source":"iana"},"text/vnd.radisys.msml-basic-layout":{"source":"iana"},"text/vnd.senx.warpscript":{"source":"iana"},"text/vnd.si.uricatalogue":{"source":"iana"},"text/vnd.sosi":{"source":"iana"},"text/vnd.sun.j2me.app-descriptor":{"source":"iana","charset":"UTF-8","extensions":["jad"]},"text/vnd.trolltech.linguist":{"source":"iana","charset":"UTF-8"},"text/vnd.wap.si":{"source":"iana"},"text/vnd.wap.sl":{"source":"iana"},"text/vnd.wap.wml":{"source":"iana","extensions":["wml"]},"text/vnd.wap.wmlscript":{"source":"iana","extensions":["wmls"]},"text/vtt":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["vtt"]},"text/x-asm":{"source":"apache","extensions":["s","asm"]},"text/x-c":{"source":"apache","extensions":["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{"source":"nginx","extensions":["htc"]},"text/x-fortran":{"source":"apache","extensions":["f","for","f77","f90"]},"text/x-gwt-rpc":{"compressible":true},"text/x-handlebars-template":{"extensions":["hbs"]},"text/x-java-source":{"source":"apache","extensions":["java"]},"text/x-jquery-tmpl":{"compressible":true},"text/x-lua":{"extensions":["lua"]},"text/x-markdown":{"compressible":true,"extensions":["mkd"]},"text/x-nfo":{"source":"apache","extensions":["nfo"]},"text/x-opml":{"source":"apache","extensions":["opml"]},"text/x-org":{"compressible":true,"extensions":["org"]},"text/x-pascal":{"source":"apache","extensions":["p","pas"]},"text/x-processing":{"compressible":true,"extensions":["pde"]},"text/x-sass":{"extensions":["sass"]},"text/x-scss":{"extensions":["scss"]},"text/x-setext":{"source":"apache","extensions":["etx"]},"text/x-sfv":{"source":"apache","extensions":["sfv"]},"text/x-suse-ymp":{"compressible":true,"extensions":["ymp"]},"text/x-uuencode":{"source":"apache","extensions":["uu"]},"text/x-vcalendar":{"source":"apache","extensions":["vcs"]},"text/x-vcard":{"source":"apache","extensions":["vcf"]},"text/xml":{"source":"iana","compressible":true,"extensions":["xml"]},"text/xml-external-parsed-entity":{"source":"iana"},"text/yaml":{"compressible":true,"extensions":["yaml","yml"]},"video/1d-interleaved-parityfec":{"source":"iana"},"video/3gpp":{"source":"iana","extensions":["3gp","3gpp"]},"video/3gpp-tt":{"source":"iana"},"video/3gpp2":{"source":"iana","extensions":["3g2"]},"video/av1":{"source":"iana"},"video/bmpeg":{"source":"iana"},"video/bt656":{"source":"iana"},"video/celb":{"source":"iana"},"video/dv":{"source":"iana"},"video/encaprtp":{"source":"iana"},"video/ffv1":{"source":"iana"},"video/flexfec":{"source":"iana"},"video/h261":{"source":"iana","extensions":["h261"]},"video/h263":{"source":"iana","extensions":["h263"]},"video/h263-1998":{"source":"iana"},"video/h263-2000":{"source":"iana"},"video/h264":{"source":"iana","extensions":["h264"]},"video/h264-rcdo":{"source":"iana"},"video/h264-svc":{"source":"iana"},"video/h265":{"source":"iana"},"video/iso.segment":{"source":"iana","extensions":["m4s"]},"video/jpeg":{"source":"iana","extensions":["jpgv"]},"video/jpeg2000":{"source":"iana"},"video/jpm":{"source":"apache","extensions":["jpm","jpgm"]},"video/jxsv":{"source":"iana"},"video/mj2":{"source":"iana","extensions":["mj2","mjp2"]},"video/mp1s":{"source":"iana"},"video/mp2p":{"source":"iana"},"video/mp2t":{"source":"iana","extensions":["ts"]},"video/mp4":{"source":"iana","compressible":false,"extensions":["mp4","mp4v","mpg4"]},"video/mp4v-es":{"source":"iana"},"video/mpeg":{"source":"iana","compressible":false,"extensions":["mpeg","mpg","mpe","m1v","m2v"]},"video/mpeg4-generic":{"source":"iana"},"video/mpv":{"source":"iana"},"video/nv":{"source":"iana"},"video/ogg":{"source":"iana","compressible":false,"extensions":["ogv"]},"video/parityfec":{"source":"iana"},"video/pointer":{"source":"iana"},"video/quicktime":{"source":"iana","compressible":false,"extensions":["qt","mov"]},"video/raptorfec":{"source":"iana"},"video/raw":{"source":"iana"},"video/rtp-enc-aescm128":{"source":"iana"},"video/rtploopback":{"source":"iana"},"video/rtx":{"source":"iana"},"video/scip":{"source":"iana"},"video/smpte291":{"source":"iana"},"video/smpte292m":{"source":"iana"},"video/ulpfec":{"source":"iana"},"video/vc1":{"source":"iana"},"video/vc2":{"source":"iana"},"video/vnd.cctv":{"source":"iana"},"video/vnd.dece.hd":{"source":"iana","extensions":["uvh","uvvh"]},"video/vnd.dece.mobile":{"source":"iana","extensions":["uvm","uvvm"]},"video/vnd.dece.mp4":{"source":"iana"},"video/vnd.dece.pd":{"source":"iana","extensions":["uvp","uvvp"]},"video/vnd.dece.sd":{"source":"iana","extensions":["uvs","uvvs"]},"video/vnd.dece.video":{"source":"iana","extensions":["uvv","uvvv"]},"video/vnd.directv.mpeg":{"source":"iana"},"video/vnd.directv.mpeg-tts":{"source":"iana"},"video/vnd.dlna.mpeg-tts":{"source":"iana"},"video/vnd.dvb.file":{"source":"iana","extensions":["dvb"]},"video/vnd.fvt":{"source":"iana","extensions":["fvt"]},"video/vnd.hns.video":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.ttsavc":{"source":"iana"},"video/vnd.iptvforum.ttsmpeg2":{"source":"iana"},"video/vnd.motorola.video":{"source":"iana"},"video/vnd.motorola.videop":{"source":"iana"},"video/vnd.mpegurl":{"source":"iana","extensions":["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{"source":"iana","extensions":["pyv"]},"video/vnd.nokia.interleaved-multimedia":{"source":"iana"},"video/vnd.nokia.mp4vr":{"source":"iana"},"video/vnd.nokia.videovoip":{"source":"iana"},"video/vnd.objectvideo":{"source":"iana"},"video/vnd.radgamettools.bink":{"source":"iana"},"video/vnd.radgamettools.smacker":{"source":"iana"},"video/vnd.sealed.mpeg1":{"source":"iana"},"video/vnd.sealed.mpeg4":{"source":"iana"},"video/vnd.sealed.swf":{"source":"iana"},"video/vnd.sealedmedia.softseal.mov":{"source":"iana"},"video/vnd.uvvu.mp4":{"source":"iana","extensions":["uvu","uvvu"]},"video/vnd.vivo":{"source":"iana","extensions":["viv"]},"video/vnd.youtube.yt":{"source":"iana"},"video/vp8":{"source":"iana"},"video/vp9":{"source":"iana"},"video/webm":{"source":"apache","compressible":false,"extensions":["webm"]},"video/x-f4v":{"source":"apache","extensions":["f4v"]},"video/x-fli":{"source":"apache","extensions":["fli"]},"video/x-flv":{"source":"apache","compressible":false,"extensions":["flv"]},"video/x-m4v":{"source":"apache","extensions":["m4v"]},"video/x-matroska":{"source":"apache","compressible":false,"extensions":["mkv","mk3d","mks"]},"video/x-mng":{"source":"apache","extensions":["mng"]},"video/x-ms-asf":{"source":"apache","extensions":["asf","asx"]},"video/x-ms-vob":{"source":"apache","extensions":["vob"]},"video/x-ms-wm":{"source":"apache","extensions":["wm"]},"video/x-ms-wmv":{"source":"apache","compressible":false,"extensions":["wmv"]},"video/x-ms-wmx":{"source":"apache","extensions":["wmx"]},"video/x-ms-wvx":{"source":"apache","extensions":["wvx"]},"video/x-msvideo":{"source":"apache","extensions":["avi"]},"video/x-sgi-movie":{"source":"apache","extensions":["movie"]},"video/x-smv":{"source":"apache","extensions":["smv"]},"x-conference/x-cooltalk":{"source":"apache","extensions":["ice"]},"x-shader/x-fragment":{"compressible":true},"x-shader/x-vertex":{"compressible":true}}')}};