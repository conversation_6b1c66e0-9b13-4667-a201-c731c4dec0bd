﻿using FakeItEasy;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using MicroSaasWebApi.Controllers.PXW;
using MicroSaasWebApi.Models.PXW.AppSettings;
using MicroSaasWebApi.Properties.PXW;
using MicroSaasWebApi.Services.PXW.Interface;
using System.Net;

namespace MicroSaasWebApi.Tests.Unit.Controller
{
    public class DataApiControllerTests
    {
        private DataApiController _dataApiController;
        private readonly IServiceProvider _serviceProvider;
        private readonly IBaseService _baseService;
        private readonly ILoggerService _loggerService;
        private readonly IExternalApiService _externalApiService;

        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly IServiceScope _serviceScope;


        const string endpointName = "ProfilesAccountsSummaryRetrieve";
        const string fakeAuthHeader = "Bearer someValidToken";
        const string fakeUrl = "http://fakedataApiUrl.com";
        const string jwtToken = "eyJhbGciOiJSUzI1NiIsImtpZCI6Im1fZmN2QU9XNHZoWXdJTnRYYW0zbFNtamRJdkRQT004bER2aHNTNWdkY3MiLCJ0eXAiOiJKV1QifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YSBt7aLlhCdSbDkb8d5XzPlIgu1owNU--UKxI0mAKKr1RoWsQ52J2Na4knfDiAWzXegHMMl-CvyUIbg7o5Tyq2qPzBcVRA4hfpTuQVwxEofc2-3mJX0sjsu47dwwYKAMGGj8mkHFwZZ_gUr35wtWMbXI-6fii_V52HTbu7ukiQSRXVXl0zkeC9jUt7MFkhVvm6NRei-vNeVK1eX7i05kaT5PVq8IjovC2zlcX69QrzcxJEkPgtMO1qfbDJBVJPl1-YPcwolD6wStdS0rzYWJam1rM0jeR-vOTBXDXf46P7I_SvkKYKE8LBfiCY6Ns-MKlHa2WTRI6w0Z2p26Nkc81w";
        const string responseBody = "{ \"success\": true }";

        public DataApiControllerTests()
        {            
            _serviceProvider = A.Fake<IServiceProvider>();
            _baseService = A.Fake<IBaseService>();
            _loggerService = A.Fake<ILoggerService>();
            _externalApiService = A.Fake<IExternalApiService>();
            _serviceScope = A.Fake<IServiceScope>();
            _serviceScopeFactory = A.Fake<IServiceScopeFactory>(); // Make sure you have this defined

            A.CallTo(() => _serviceProvider.GetService(typeof(IServiceScopeFactory))).Returns(_serviceScopeFactory);
            A.CallTo(() => _serviceScopeFactory.CreateScope()).Returns(_serviceScope);

            // Use GetRequiredService to match the actual calls in your constructor
            A.CallTo(() => _serviceScope.ServiceProvider.GetService(typeof(ILoggerService))).Returns(_loggerService);
            A.CallTo(() => _serviceScope.ServiceProvider.GetService(typeof(IExternalApiService))).Returns(_externalApiService);

            _dataApiController = new DataApiController(_baseService, _loggerService);
        }

        #region ShouldReturn200OK_WhenCalledSuccessfully
        [Fact, Trait("Category", "DataAPI")]
        public async Task CurrenciesList_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDataApi = new PortfolioDataApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, (object)"SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _dataApiController.CurrenciesList();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        [Fact, Trait("Category", "DataAPI")]
        public async Task ProfilesAccountsSummaryRetrieve_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDataApi = new PortfolioDataApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, (object)"SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _dataApiController.ProfilesAccountsSummaryRetrieve();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        [Fact, Trait("Category", "DataAPI")]
        public async Task ProfilesAsOfDatesRetrieve_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDataApi = new PortfolioDataApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, (object)"SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _dataApiController.ProfilesAsOfDatesRetrieve();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        [Fact, Trait("Category", "DataAPI")]
        public async Task ProfilesCustodianAccountCodesRetrieve_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDataApi = new PortfolioDataApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, (object)"SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _dataApiController.ProfilesCustodianAccountCodesRetrieve();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        [Fact, Trait("Category", "DataAPI")]
        public async Task ProfilesDashboardLineGraphRetrieve_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDataApi = new PortfolioDataApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, (object)"SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _dataApiController.ProfilesDashboardLineGraphRetrieve();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        [Fact, Trait("Category", "DataAPI")]
        public async Task ProfilesDashboardSummaryRetrieve_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDataApi = new PortfolioDataApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, (object)"SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _dataApiController.ProfilesDashboardSummaryRetrieve();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        [Fact, Trait("Category", "DataAPI")]
        public async Task ProfilesFundingsRetrieve_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDataApi = new PortfolioDataApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, (object)"SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _dataApiController.ProfilesFundingsRetrieve();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        [Fact, Trait("Category", "DataAPI")]
        public async Task ProfilesHoldingsRetrieve_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDataApi = new PortfolioDataApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, (object)"SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _dataApiController.ProfilesHoldingsRetrieve();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        [Fact, Trait("Category", "DataAPI")]
        public async Task ProfilesPieChartByAccountRetrieve_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDataApi = new PortfolioDataApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, (object)"SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _dataApiController.ProfilesPieChartByAccountRetrieve();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        [Fact, Trait("Category", "DataAPI")]
        public async Task ProfilesPieChartByAssetClassRetrieve_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDataApi = new PortfolioDataApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, (object)"SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _dataApiController.ProfilesPieChartByAssetClassRetrieve();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        [Fact, Trait("Category", "DataAPI")]
        public async Task ProfilesPieChartByCurrencyRetrieve_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDataApi = new PortfolioDataApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, (object)"SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _dataApiController.ProfilesPieChartByCurrencyRetrieve();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "DataAPI")]
        public async Task ProfilesPieChartsRetrieve_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDataApi = new PortfolioDataApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, (object)"SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _dataApiController.ProfilesPieChartsRetrieve();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        [Fact, Trait("Category", "DataAPI")]
        public async Task ProfilesTopHoldingsRetrieve_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDataApi = new PortfolioDataApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, (object)"SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _dataApiController.ProfilesTopHoldingsRetrieve();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        [Fact, Trait("Category", "DataAPI")]
        public async Task ProfilesTransactionsRetrieve_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDataApi = new PortfolioDataApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, (object)"SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _dataApiController.ProfilesAccountsSummaryRetrieve();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        [Fact, Trait("Category", "DataAPI")]
        public async Task ProfilesAccountsList_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDataApi = new PortfolioDataApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, (object)"SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _dataApiController.ProfilesAccountsList();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        [Fact, Trait("Category", "DataAPI")]
        public async Task ProfilesAccountsHoldingsRetrieve_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDataApi = new PortfolioDataApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, (object)"SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _dataApiController.ProfilesAccountsHoldingsRetrieve();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        [Fact, Trait("Category", "DataAPI")]
        public async Task ProfilesAccountsTransactionsRetrieve_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDataApi = new PortfolioDataApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, (object)"SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _dataApiController.ProfilesAccountsTransactionsRetrieve();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        [Fact, Trait("Category", "DataAPI")]
        public async Task LogUserLogin_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDataApi = new PortfolioDataApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, (object)"SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _dataApiController.LogUserLogin();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        [Fact, Trait("Category", "DataAPI")]
        public async Task LogUserLogout_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDataApi = new PortfolioDataApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, (object)"SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _dataApiController.LogUserLogout();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        [Fact, Trait("Category", "DataAPI")]
        public async Task AccountLevelPerformance_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDataApi = new PortfolioDataApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, (object)"SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _dataApiController.AccountLevelPerformance();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        [Fact, Trait("Category", "DataAPI")]
        public async Task PresetDateRanges_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDataApi = new PortfolioDataApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, (object)"SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _dataApiController.PresetDateRanges();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        [Fact, Trait("Category", "DataAPI")]
        public async Task RorTable_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDataApi = new PortfolioDataApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns((true, (object)"SomeResult", string.Empty, HttpStatusCode.OK));

            // Act
            var result = await _dataApiController.RorTable();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact, Trait("Category", "DataAPI")]
        public async Task CheckSecurityCodeAvailability_ShouldReturn204NoContent_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDataApi = new PortfolioDataApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._))
                .Returns((true, (object)"SomeResult", string.Empty, HttpStatusCode.NoContent));

            // Act
            var result = await _dataApiController.CheckSecurityCodeAvailability();

            // Assert
            result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        #endregion
        //#region ShouldReturn500_WhenEndpointUrlNotFound
        //[Fact, Trait("Category", "DataAPI")]
        //public async Task AccountLevelPerformance_ShouldReturn500_WhenEndpointUrlNotFound()
        //{
        //    // Arrange
        //    //var mockExternalApiService = new Mock<IExternalApiService>();
        //    //var mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
        //    //var mockResponseLoggerService = new Mock<ILoggerService>();
        //    //var controller = new YourController(mockExternalApiService.Object, mockHttpContextAccessor.Object, mockResponseLoggerService.Object);

        //    //mockHttpContextAccessor.Setup(x => x.HttpContext.Request.Headers[It.IsAny<string>()])
        //    //    .Returns("Bearer SampleToken");
        //    //mockHttpContextAccessor.Setup(x => x.HttpContext.Items["Endpoints"])
        //    //    .Returns(new Endpoints());

        //    // Act
        //    var result = await _dataApiController.AccountLevelPerformance();

        //    // Assert
        //    var internalServerErrorResult = Assert.IsType<ObjectResult>(result);
        //    Assert.Equal(500, internalServerErrorResult.StatusCode);
        //}
        //#endregion
        //#region ShouldReturn400BadRequest_WhenMsalExceptionOccurs
        //[Fact, Trait("Category", "DataAPI")]
        //public async Task ProfilesAccountsSummaryRetrieve_ShouldReturn400BadRequest_WhenBearerTokenNotFound()
        //{
        //    // Arrange
        //    // Set up fake HttpContextAccessor with null or invalid Authorization header.

        //    // Act
        //    var result = await _dataApiController.ProfilesAccountsSummaryRetrieve();

        //    // Assert
        //    result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
        //}

        //#endregion
        //#region ShouldReturn400BadRequest_WhenMsalExceptionOccurs
        //[Fact, Trait("Category", "DataAPI")]
        //public async Task ProfilesAccountsSummaryRetrieve_ShouldReturn400BadRequest_WhenMsalExceptionOccurs()
        //{
        //    // Arrange
        //    // Fake the necessary service to throw MsalException.

        //    // Act
        //    var result = await _dataApiController.ProfilesAccountsSummaryRetrieve();

        //    // Assert
        //    result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
        //}
        //#endregion
        //#region ShouldReturn500InternalServerError_WhenGenericExceptionOccurs
        //[Fact, Trait("Category", "DataAPI")]
        //public async Task ProfilesAccountsSummaryRetrieve_ShouldReturn500InternalServerError_WhenGenericExceptionOccurs()
        //{
        //    // Arrange
        //    // Fake the necessary service to throw a generic exception.

        //    // Act
        //    var result = await _dataApiController.ProfilesAccountsSummaryRetrieve();

        //    // Assert
        //    result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status500InternalServerError);
        //}
        //#endregion
        //#region WhenApiCallFails
        //[Fact, Trait("Category", "DataAPI")]
        //public async Task ProfilesAccountsSummaryRetrieve_ShouldReturnStatusCode_WhenApiCallFails()
        //{
        //    // Arrange
        //    // Fake the ExternalApiService to return failure.

        //    // Act
        //    var result = await _dataApiController.ProfilesAccountsSummaryRetrieve();

        //    // Assert
        //    result.Should().BeOfType<ObjectResult>();
        //    // Additional assertions based on failure status code.
        //}
        //#endregion


    }

}
