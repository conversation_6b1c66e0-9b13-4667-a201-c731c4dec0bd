'use client'
// Blog details component
import '../assets/styles/blog-page.scss'
import BlogMoreArticles from './BlogMoreArticles'
import BlogSpotlight from './BlogSpotlight'
import EnhancedBlogContent from '@/app/blog/_assets/components/EnhancedBlogContent'
import AdManager from './AdManager'
import { useEffect } from 'react'

const BlogDetails = ({ postDetails, allPosts }: any) => {
	return (
		<div>
			<div className='w-full max-w-[90rem] mx-auto px-4 sm:px-6 lg:px-8 mt-16'>
				<BlogSpotlight post={postDetails} />
				<div className='mt-4 flex justify-between'>
					{/* Main content */}
					<div className='flex-1 mx-auto max-w-5xl pr-8'>
						<EnhancedBlogContent content={postDetails?.content?.rendered || ''} />

						{/* In-article ad after blog content */}
						<div className='mt-6 mb-6'>
							<AdManager type="in-article" />
						</div>
					</div>

					{/* Right sidebar for ads */}
					<div className='hidden lg:block w-[320px] sticky top-32 h-screen'>
						<AdManager type="multiplex-vertical" />
					</div>
				</div>
				{/* <div id='blog-detail-faq'>
					<Faq data={faqArray} isHomePage={false} />
				</div> */}
			</div>
			<BlogMoreArticles
				currentBlog={postDetails?.title?.rendered}
				AllPosts={allPosts}
			/>

			{/* Horizontal AdSense Ad */}
			<div className='mt-12'>
				<AdManager type="display-horizontal" />
			</div>
		</div>
	)
}

export default BlogDetails
