(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[635],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},6195:e=>{"use strict";e.exports=require("node:buffer")},2480:()=>{},9263:(e,r,t)=>{"use strict";t.r(r),t.d(r,{ComponentMod:()=>E,default:()=>_});var n={};t.r(n),t.d(n,{GET:()=>f,dynamic:()=>d,runtime:()=>p});var o={};t.r(o),t.d(o,{originalPathname:()=>x,patchFetch:()=>j,requestAsyncStorage:()=>m,routeModule:()=>h,serverHooks:()=>P,staticGenerationAsyncStorage:()=>w});var a=t(932),i=t(2561),s=t(4828),u=t(6631),c=t(6333),l=t(462),k=t(9985);let d="force-dynamic",p="edge";async function f(e){try{let r=await (0,l.a)(),t=e.nextUrl.searchParams.get("projectID");if(!r)return k.xk.json({error:"Unauthorized"},{status:401});if(!t)return k.xk.json({error:"Project ID is required"},{status:400});let n=await c.Z.project.findUnique({where:{id:t}});if(!n?.webhookLink)return k.xk.json({error:"Project not found or webhook link not available"},{status:404});return k.xk.json({success:!0,webhookLink:n.webhookLink})}catch(e){return console.error("Connection error:",e),k.xk.json({error:"An unexpected error occurred"},{status:500})}}let h=new i.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/(make)/link/route",pathname:"/api/link",filename:"route",bundlePath:"app/api/(make)/link/route"},resolvedPagePath:"C:\\Projects\\PersonalPortal\\src\\app\\api\\(make)\\link\\route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:m,staticGenerationAsyncStorage:w,serverHooks:P}=h,x="/api/(make)/link/route";function j(){return(0,u.XH)({serverHooks:P,staticGenerationAsyncStorage:w})}let E=o,_=a.a.wrap(h)},462:(e,r,t)=>{"use strict";t.d(r,{a:()=>a});var n=t(5657),o=t(2436);async function a(){t(7185);let{userId:e}=(0,o.I)();return e?(0,n.N)().users.getUser(e):null}},6333:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});let n=new(t(7225)).PrismaClient}},e=>{var r=r=>e(e.s=r);e.O(0,[520,288,225,436,657],()=>r(9263));var t=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/(make)/link/route"]=t}]);
//# sourceMappingURL=route.js.map