import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'

const isPublicRoute = createRouteMatcher([
  '/',
  '/sign-in(.*)',
  '/sign-up(.*)',
  '/api/create-checkout-session',
  '/api/webhook/:path*',
  '/api/test-email', // Allow public access to test email API
  '/test-email', // Allow public access to test email page
  '/dashboard',
  '/api/waiting-list',
  '/waiting-list',
  '/blog',
  '/blog(.*)',
  '/sitemap.xml',
  '/processing-page(.*)',
  '/images/:path*', // Allow access to all files in the images directory
  '/ads.txt', // Allow public access to ads.txt
  '/doc/:path*', // Allow access to documents
  '/cv', // Allow access to CV redirect
  '/ad-demo' // Allow access to ad demo page
])

export default clerkMiddleware((auth, request) => {
  if (!isPublicRoute(request)) {
    auth().protect()
  }
})

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|ads.txt|doc).*)', // Exclude static files and documents from authentication
    '/(api|trpc)(.*)',
  ],
};