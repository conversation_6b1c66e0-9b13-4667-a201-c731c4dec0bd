'use client'

// Function to create a simple copy button with SVG
function createCopy<PERSON><PERSON>on(codeBlock: HTMLElement) {
  // Create button element
  const button = document.createElement('button')
  button.className = 'copy-code-button'
  button.setAttribute('aria-label', 'Copy code')
  button.setAttribute('type', 'button')

  // Set button styles
  button.style.position = 'absolute'
  button.style.top = '0.5rem'
  button.style.right = '0.5rem'
  button.style.padding = '0.25rem'
  button.style.backgroundColor = 'transparent'
  button.style.border = 'none'
  button.style.cursor = 'pointer'
  button.style.color = 'inherit'
  button.style.zIndex = '10'

  // Set the SVG directly as innerHTML
  button.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect><path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path></svg>`

  // Add click handler
  button.onclick = async (e) => {
    e.preventDefault()
    e.stopPropagation()
    try {
      await navigator.clipboard.writeText(codeBlock.textContent?.trim() || '')

      // Change to checkmark SVG for success
      const originalHTML = button.innerHTML
      button.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>`

      // Reset after 2 seconds
      setTimeout(() => {
        button.innerHTML = originalHTML
      }, 2000)
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }

  return button
}

// Add a global event listener to ensure the copy functionality is applied when the page loads
if (typeof window !== 'undefined') {
  window.addEventListener('load', () => {
    const theme = document.documentElement.classList.contains('dark') ? 'dark' : 'light'
    enhanceCodeBlocks(theme)
  })

  // Also add a mutation observer to detect when new code blocks are added to the DOM
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        const theme = document.documentElement.classList.contains('dark') ? 'dark' : 'light'
        enhanceCodeBlocks(theme)
      }
    })
  })

  // Start observing the document body for changes
  observer.observe(document.body, { childList: true, subtree: true })
}

export function enhanceCodeBlocks(theme?: string) {
  // Find all code blocks in the blog content
  const codeBlocks = document.querySelectorAll<HTMLElement>(
    'pre, code, .blog-content pre, .blog-content code, .wp-block-code, .code-block, .code-snippet, .wp-block-preformatted'
  )

  // Create a Set to track elements that already have copy buttons
  const processedElements = new Set()

  // Add copy button to each code block
  codeBlocks.forEach(codeBlock => {
    // Determine the parent element to attach the button to
    let targetElement = codeBlock

    // If the code block is inside a pre tag, use the pre tag as the target
    if (codeBlock.parentElement && codeBlock.parentElement.tagName === 'PRE') {
      targetElement = codeBlock.parentElement
    }

    // Generate a unique identifier for this element
    const elementId = targetElement.id ||
                     `code-block-${Math.random().toString(36).substring(2, 11)}`

    // If we've already processed this element, skip it
    if (processedElements.has(elementId)) return

    // Mark this element as processed
    processedElements.add(elementId)

    // Apply styling to code blocks
    if (targetElement instanceof HTMLElement) {
      targetElement.style.position = 'relative'
    }

    // Check if this is an AdSense code block (usually has comments indicating it)
    const isAdSenseBlock = codeBlock.textContent?.includes('Adsense') ||
                          codeBlock.textContent?.includes('AdSense') ||
                          codeBlock.textContent?.includes('ca-pub-');

    if (isAdSenseBlock) {
      // Don't modify AdSense code blocks
      return;
    }

    // Only add copy button if it doesn't already exist and the code block has multiple lines
    const existingButtons = targetElement.querySelectorAll('.copy-code-button');

    // Check if the code block has multiple lines
    const codeText = codeBlock.textContent || '';
    const lineCount = codeText.split('\n').filter(line => line.trim().length > 0).length;
    const isMultiLine = lineCount > 1;

    // Only add copy button for multi-line code blocks and if no button exists
    if (existingButtons.length === 0 && isMultiLine) {
      const button = createCopyButton(codeBlock);
      targetElement.appendChild(button);

      // Add position relative to the target element if not already set
      if (targetElement instanceof HTMLElement && targetElement.style.position !== 'relative') {
        targetElement.style.position = 'relative';
      }
    }

    // Remove any syntax highlighting classes
    if (targetElement.classList) {
      Array.from(targetElement.classList).forEach(className => {
        if (className.includes('language-') ||
            className.includes('hljs') ||
            className.includes('prism')) {
          targetElement.classList.remove(className)
        }
      })
    }

    // Apply consistent styling to code blocks
    if (targetElement instanceof HTMLElement) {
      if (theme === 'dark') {
        targetElement.style.backgroundColor = '#0F1121' // Dark background
        targetElement.style.color = '#e2e8f0' // Light text
        targetElement.style.border = 'none' // No border
      } else {
        targetElement.style.backgroundColor = '#f7fafc' // Light background
        targetElement.style.color = '#2d3748' // Dark text
        targetElement.style.border = 'none' // No border
      }

      // Ensure the code block has proper styling
      targetElement.style.borderRadius = '0.375rem'
      targetElement.style.padding = '1rem'
      targetElement.style.margin = '1rem 0'
      targetElement.style.overflowX = 'auto'
    }

    // Remove syntax highlighting from child elements
    const childElements = targetElement.querySelectorAll('*')
    childElements.forEach(el => {
      if (el instanceof HTMLElement) {
        el.style.color = 'inherit'
        el.style.backgroundColor = 'transparent'
        el.style.fontWeight = 'normal'
        el.style.fontStyle = 'normal'
      }
    })
  })
}

// Function to enhance blog content with additional features
export function enhanceBlogContent(theme?: string) {
  // Style code blocks (without copy buttons)
  enhanceCodeBlocks(theme)

  // Apply theme-aware styles to elements
  applyThemeStyles(theme)

  // Force re-render of theme-specific elements
  if (theme === 'dark') {
    document.documentElement.classList.add('dark')
    document.documentElement.classList.remove('light')
  } else {
    document.documentElement.classList.add('light')
    document.documentElement.classList.remove('dark')
  }

  // Add multiple delays to catch any dynamically loaded content
  setTimeout(() => enhanceCodeBlocks(theme), 500)
  setTimeout(() => enhanceCodeBlocks(theme), 1000)
  setTimeout(() => enhanceCodeBlocks(theme), 2000)
}

// Function to apply theme-aware styles
function applyThemeStyles(theme?: string) {
  // Apply styles based on the current theme

  // Style the main blog content container
  const blogContent = document.querySelector<HTMLElement>('.blog-content')
  if (blogContent) {
    // Remove any existing theme classes first
    blogContent.classList.remove('bg-white', 'bg-[#1E232C]', 'text-gray-800', 'text-white', 'border-gray-200', 'border-[#373C53]', 'light-theme', 'dark-theme')

    // Apply appropriate classes based on theme
    if (theme === 'dark') {
      blogContent.classList.add('bg-[#1E232C]', 'text-white', 'border-[#373C53]', 'dark-theme')
      blogContent.classList.remove('light-theme')

      // Force dark theme styles with inline styles
      blogContent.style.backgroundColor = '#1E232C'
      blogContent.style.color = 'white'
      blogContent.style.borderColor = '#373C53'
    } else {
      blogContent.classList.add('bg-white', 'text-gray-800', 'border-gray-200', 'light-theme')
      blogContent.classList.remove('dark-theme')

      // Force light theme styles with inline styles
      blogContent.style.backgroundColor = 'white'
      blogContent.style.color = '#2d3748' // text-gray-800
      blogContent.style.borderColor = '#e2e8f0' // border-gray-200
    }
  }
  // Get all images and add responsive classes
  const images = document.querySelectorAll<HTMLImageElement>('.blog-content img')
  images.forEach(img => {
    img.classList.add('max-w-full', 'h-auto', 'rounded-md')

    // Add error handling for images without modifying URLs
    img.onerror = function() {
      console.error('Image failed to load:', img.src)
      // Keep the original image but add a visual indicator that it failed to load
      img.style.border = '2px dashed #ff0000'
      img.style.padding = '10px'
    }
  })

  // Style paragraphs
  const paragraphs = document.querySelectorAll('.blog-content p')
  paragraphs.forEach(p => {
    // Remove any existing theme classes first
    p.classList.remove('text-gray-800', 'text-gray-200')

    // Apply appropriate classes based on theme
    if (theme === 'dark') {
      p.classList.add('text-gray-200')
    } else {
      p.classList.add('text-gray-800')
    }
  })

  // Style tables
  const tables = document.querySelectorAll<HTMLTableElement>('.blog-content table')
  tables.forEach(table => {
    table.classList.add('w-full', 'border-collapse', 'my-4')
    table.style.borderSpacing = '0'

    // Style table headers
    const headers = table.querySelectorAll('th')
    headers.forEach(th => {
      th.classList.add('bg-gray-100', 'dark:bg-gray-800', 'p-2', 'text-left', 'border', 'border-gray-300', 'dark:border-gray-700')
    })

    // Style table cells
    const cells = table.querySelectorAll('td')
    cells.forEach(td => {
      td.classList.add('p-2', 'border', 'border-gray-300', 'dark:border-gray-700')
    })
  })

  // Style links
  const links = document.querySelectorAll<HTMLElement>('.blog-content a:not(.copy-code-button)')
  links.forEach(link => {
    link.classList.add('text-blue-500', 'hover:underline')
  })

  // Style headings
  const headings = document.querySelectorAll<HTMLElement>('.blog-content h1, .blog-content h2, .blog-content h3, .blog-content h4, .blog-content h5, .blog-content h6')
  headings.forEach(heading => {
    // Remove any existing theme classes first
    heading.classList.remove('text-gray-900', 'text-white')

    // Apply appropriate classes based on theme
    if (theme === 'dark') {
      heading.classList.add('text-white', 'font-semibold')
    } else {
      heading.classList.add('text-gray-900', 'font-semibold')
    }
  })

  // Style code blocks
  const codeBlocks = document.querySelectorAll<HTMLElement>('.blog-content pre')
  codeBlocks.forEach(block => {
    // Remove any existing theme classes first
    block.classList.remove('bg-gray-50', 'bg-[#0F1121]', 'border-gray-200', 'border-[#373C53]', 'text-gray-200', 'text-gray-800', 'border')

    // Apply appropriate classes based on theme
    if (theme === 'dark') {
      block.classList.add('bg-[#0F1121]', 'text-gray-200')
    } else {
      block.classList.add('bg-gray-50', 'text-gray-800')
    }

    // Add padding and rounded corners if not already present
    if (!block.classList.contains('p-4')) {
      block.classList.add('p-4', 'rounded-md', 'my-4', 'relative')
    }

    // Ensure no border
    block.style.border = 'none'
  })

  // Style inline code
  const inlineCodes = document.querySelectorAll<HTMLElement>('.blog-content code:not(pre code)')
  inlineCodes.forEach(code => {
    // Remove any existing theme classes first
    code.classList.remove('bg-gray-100', 'bg-gray-800', 'text-gray-800', 'text-gray-200')

    // Apply appropriate classes based on theme
    if (theme === 'dark') {
      code.classList.add('text-gray-200')
    } else {
      code.classList.add('text-gray-800')
    }

    // Ensure no border and transparent background
    code.style.border = 'none'
    code.style.backgroundColor = 'transparent'
    code.style.padding = '0'
    code.style.margin = '0 0.1rem'
  })

  // Style blockquotes
  const blockquotes = document.querySelectorAll<HTMLElement>('.blog-content blockquote')
  blockquotes.forEach(blockquote => {
    // Remove any existing theme classes first
    blockquote.classList.remove('text-gray-600', 'text-gray-400', 'border-l-gray-300', 'border-l-gray-700')

    // Apply appropriate classes based on theme
    if (theme === 'dark') {
      blockquote.classList.add('text-gray-400', 'border-l-gray-700')
    } else {
      blockquote.classList.add('text-gray-600', 'border-l-gray-300')
    }
  })
}



