(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[870],{89550:function(e){e.exports={style:{fontFamily:"'__Inter_d65c78', '__Inter_Fallback_d65c78'",fontStyle:"normal"},className:"__className_d65c78"}},36760:function(e,t){var n;!function(){"use strict";var o={}.hasOwnProperty;function r(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=l(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return r.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)o.call(e,n)&&e[n]&&(t=l(t,n));return t}(n)))}return e}function l(e,t){return t?e?e+" "+t:e+t:e}e.exports?(r.default=r,e.exports=r):void 0!==(n=(function(){return r}).apply(t,[]))&&(e.exports=n)}()},88291:function(e,t,n){"use strict";n.d(t,{Analytics:function(){return s}});var o=n(2265),r=n(40257),l=()=>{window.va||(window.va=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];(window.vaq=window.vaq||[]).push(t)})};function i(){return"undefined"!=typeof window}function a(){return"production"}function c(){return"development"===((i()?window.vam:a())||"production")}function s(e){return(0,o.useEffect)(()=>{var t;e.beforeSend&&(null==(t=window.va)||t.call(window,"beforeSend",e.beforeSend))},[e.beforeSend]),(0,o.useEffect)(()=>{var t;!function(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{debug:!0};if(!i())return;(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"auto";if("auto"===e){window.vam=a();return}window.vam=e})(t.mode),l(),t.beforeSend&&(null==(e=window.va)||e.call(window,"beforeSend",t.beforeSend));let n=t.scriptSrc?t.scriptSrc:c()?"https://va.vercel-scripts.com/v1/script.debug.js":t.basePath?"".concat(t.basePath,"/insights/script.js"):"/_vercel/insights/script.js";if(document.head.querySelector('script[src*="'.concat(n,'"]')))return;let o=document.createElement("script");o.src=n,o.defer=!0,o.dataset.sdkn="@vercel/analytics"+(t.framework?"/".concat(t.framework):""),o.dataset.sdkv="1.5.0",t.disableAutoTrack&&(o.dataset.disableAutoTrack="1"),t.endpoint?o.dataset.endpoint=t.endpoint:t.basePath&&(o.dataset.endpoint="".concat(t.basePath,"/insights")),t.dsn&&(o.dataset.dsn=t.dsn),o.onerror=()=>{let e=c()?"Please check if any ad blockers are enabled and try again.":"Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.";console.log("[Vercel Web Analytics] Failed to load script from ".concat(n,". ").concat(e))},c()&&!1===t.debug&&(o.dataset.debug="false"),document.head.appendChild(o)}({framework:e.framework||"react",basePath:null!==(t=e.basePath)&&void 0!==t?t:function(){if(void 0!==r&&void 0!==r.env)return r.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH}(),...void 0!==e.route&&{disableAutoTrack:!0},...e})},[]),(0,o.useEffect)(()=>{e.route&&e.path&&function(e){var t;let{route:n,path:o}=e;null==(t=window.va)||t.call(window,"pageview",{route:n,path:o})}({route:e.route,path:e.path})},[e.route,e.path]),null}},1952:function(e,t,n){"use strict";n.d(t,{SpeedInsights:function(){return f}});var o=n(2265),r=n(99376),l=n(40257),i=()=>{window.si||(window.si=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];(window.siq=window.siq||[]).push(t)})};function a(){return false}function c(e){return new RegExp("/".concat(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"(?=[/?#]|$)"))}function s(e){(0,o.useEffect)(()=>{var t;e.beforeSend&&(null==(t=window.si)||t.call(window,"beforeSend",e.beforeSend))},[e.beforeSend]);let t=(0,o.useRef)(null);return(0,o.useEffect)(()=>{if(t.current)e.route&&t.current(e.route);else{var n,o;let r=function(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!("undefined"!=typeof window)||null===t.route)return null;i();let n=t.scriptSrc?t.scriptSrc:t.dsn?"https://va.vercel-scripts.com/v1/speed-insights/script.js":t.basePath?"".concat(t.basePath,"/speed-insights/script.js"):"/_vercel/speed-insights/script.js";if(document.head.querySelector('script[src*="'.concat(n,'"]')))return null;t.beforeSend&&(null==(e=window.si)||e.call(window,"beforeSend",t.beforeSend));let o=document.createElement("script");return o.src=n,o.defer=!0,o.dataset.sdkn="@vercel/speed-insights"+(t.framework?"/".concat(t.framework):""),o.dataset.sdkv="1.2.0",t.sampleRate&&(o.dataset.sampleRate=t.sampleRate.toString()),t.route&&(o.dataset.route=t.route),t.endpoint?o.dataset.endpoint=t.endpoint:t.basePath&&(o.dataset.endpoint="".concat(t.basePath,"/speed-insights/vitals")),t.dsn&&(o.dataset.dsn=t.dsn),o.onerror=()=>{console.log("[Vercel Speed Insights] Failed to load script from ".concat(n,". Please check if any content blockers are enabled and try again."))},document.head.appendChild(o),{setRoute:e=>{o.dataset.route=null!=e?e:void 0}}}({framework:null!==(n=e.framework)&&void 0!==n?n:"react",basePath:null!==(o=e.basePath)&&void 0!==o?o:function(){if(void 0!==l&&void 0!==l.env)return l.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH}(),...e});r&&(t.current=r.setRoute)}},[e.route]),null}var u=()=>{let e=(0,r.useParams)(),t=(0,r.useSearchParams)()||new URLSearchParams,n=(0,r.usePathname)();return e?function(e,t){if(!e||!t)return e;let n=e;try{let e=Object.entries(t);for(let[t,o]of e)if(!Array.isArray(o)){let e=c(o);e.test(n)&&(n=n.replace(e,"/[".concat(t,"]")))}for(let[t,o]of e)if(Array.isArray(o)){let e=c(o.join("/"));e.test(n)&&(n=n.replace(e,"/[...".concat(t,"]")))}return n}catch(t){return e}}(n,Object.keys(e).length?e:Object.fromEntries(t.entries())):null};function d(e){let t=u();return o.createElement(s,{route:t,...e,framework:"next",basePath:function(){if(void 0!==l&&void 0!==l.env)return l.env.NEXT_PUBLIC_VERCEL_OBSERVABILITY_BASEPATH}()})}function f(e){return o.createElement(o.Suspense,{fallback:null},o.createElement(d,{...e}))}},88254:function(e,t,n){"use strict";n.d(t,{u:function(){return eT}});var o=n(2265);let r=Math.min,l=Math.max,i=Math.round,a=Math.floor,c=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},u={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function f(e){return e.split("-")[0]}function p(e){return e.split("-")[1]}function v(e){return"x"===e?"y":"x"}function m(e){return"y"===e?"height":"width"}function h(e){return["top","bottom"].includes(f(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>u[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function g(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:o,height:r}=e;return{width:o,height:r,top:n,left:t,right:t+o,bottom:n+r,x:t,y:n}}function _(e,t,n){let o,{reference:r,floating:l}=e,i=h(t),a=v(h(t)),c=m(a),s=f(t),u="y"===i,d=r.x+r.width/2-l.width/2,y=r.y+r.height/2-l.height/2,w=r[c]/2-l[c]/2;switch(s){case"top":o={x:d,y:r.y-l.height};break;case"bottom":o={x:d,y:r.y+r.height};break;case"right":o={x:r.x+r.width,y:y};break;case"left":o={x:r.x-l.width,y:y};break;default:o={x:r.x,y:r.y}}switch(p(t)){case"start":o[a]-=w*(n&&u?-1:1);break;case"end":o[a]+=w*(n&&u?-1:1)}return o}let E=async(e,t,n)=>{let{placement:o="bottom",strategy:r="absolute",middleware:l=[],platform:i}=n,a=l.filter(Boolean),c=await (null==i.isRTL?void 0:i.isRTL(t)),s=await i.getElementRects({reference:e,floating:t,strategy:r}),{x:u,y:d}=_(s,o,c),f=o,p={},v=0;for(let n=0;n<a.length;n++){let{name:l,fn:m}=a[n],{x:h,y:y,data:w,reset:g}=await m({x:u,y:d,initialPlacement:o,placement:f,strategy:r,middlewareData:p,rects:s,platform:i,elements:{reference:e,floating:t}});u=null!=h?h:u,d=null!=y?y:d,p={...p,[l]:{...p[l],...w}},g&&v<=50&&(v++,"object"==typeof g&&(g.placement&&(f=g.placement),g.rects&&(s=!0===g.rects?await i.getElementRects({reference:e,floating:t,strategy:r}):g.rects),{x:u,y:d}=_(s,f,c)),n=-1)}return{x:u,y:d,placement:f,strategy:r,middlewareData:p}};async function S(e,t){var n;void 0===t&&(t={});let{x:o,y:r,platform:l,rects:i,elements:a,strategy:c}=e,{boundary:s="clippingAncestors",rootBoundary:u="viewport",elementContext:f="floating",altBoundary:p=!1,padding:v=0}=d(t,e),m=g(v),h=a[p?"floating"===f?"reference":"floating":f],y=b(await l.getClippingRect({element:null==(n=await (null==l.isElement?void 0:l.isElement(h)))||n?h:h.contextElement||await (null==l.getDocumentElement?void 0:l.getDocumentElement(a.floating)),boundary:s,rootBoundary:u,strategy:c})),w="floating"===f?{x:o,y:r,width:i.floating.width,height:i.floating.height}:i.reference,_=await (null==l.getOffsetParent?void 0:l.getOffsetParent(a.floating)),E=await (null==l.isElement?void 0:l.isElement(_))&&await (null==l.getScale?void 0:l.getScale(_))||{x:1,y:1},S=b(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:_,strategy:c}):w);return{top:(y.top-S.top+m.top)/E.y,bottom:(S.bottom-y.bottom+m.bottom)/E.y,left:(y.left-S.left+m.left)/E.x,right:(S.right-y.right+m.right)/E.x}}async function x(e,t){let{placement:n,platform:o,elements:r}=e,l=await (null==o.isRTL?void 0:o.isRTL(r.floating)),i=f(n),a=p(n),c="y"===h(n),s=["left","top"].includes(i)?-1:1,u=l&&c?-1:1,v=d(t,e),{mainAxis:m,crossAxis:y,alignmentAxis:w}="number"==typeof v?{mainAxis:v,crossAxis:0,alignmentAxis:null}:{mainAxis:v.mainAxis||0,crossAxis:v.crossAxis||0,alignmentAxis:v.alignmentAxis};return a&&"number"==typeof w&&(y="end"===a?-1*w:w),c?{x:y*u,y:m*s}:{x:m*s,y:y*u}}function A(){return"undefined"!=typeof window}function k(e){return L(e)?(e.nodeName||"").toLowerCase():"#document"}function R(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function T(e){var t;return null==(t=(L(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function L(e){return!!A()&&(e instanceof Node||e instanceof R(e).Node)}function O(e){return!!A()&&(e instanceof Element||e instanceof R(e).Element)}function C(e){return!!A()&&(e instanceof HTMLElement||e instanceof R(e).HTMLElement)}function P(e){return!!A()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof R(e).ShadowRoot)}function N(e){let{overflow:t,overflowX:n,overflowY:o,display:r}=D(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!["inline","contents"].includes(r)}function j(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function I(e){let t=B(),n=O(e)?D(e):e;return"none"!==n.transform||"none"!==n.perspective||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function B(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function $(e){return["html","body","#document"].includes(k(e))}function D(e){return R(e).getComputedStyle(e)}function H(e){return O(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function W(e){if("html"===k(e))return e;let t=e.assignedSlot||e.parentNode||P(e)&&e.host||T(e);return P(t)?t.host:t}function F(e,t,n){var o;void 0===t&&(t=[]),void 0===n&&(n=!0);let r=function e(t){let n=W(t);return $(n)?t.ownerDocument?t.ownerDocument.body:t.body:C(n)&&N(n)?n:e(n)}(e),l=r===(null==(o=e.ownerDocument)?void 0:o.body),i=R(r);if(l){let e=V(i);return t.concat(i,i.visualViewport||[],N(r)?r:[],e&&n?F(e):[])}return t.concat(r,F(r,[],n))}function V(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function q(e){let t=D(e),n=parseFloat(t.width)||0,o=parseFloat(t.height)||0,r=C(e),l=r?e.offsetWidth:n,a=r?e.offsetHeight:o,c=i(n)!==l||i(o)!==a;return c&&(n=l,o=a),{width:n,height:o,$:c}}function M(e){return O(e)?e:e.contextElement}function z(e){let t=M(e);if(!C(t))return c(1);let n=t.getBoundingClientRect(),{width:o,height:r,$:l}=q(t),a=(l?i(n.width):n.width)/o,s=(l?i(n.height):n.height)/r;return a&&Number.isFinite(a)||(a=1),s&&Number.isFinite(s)||(s=1),{x:a,y:s}}let K=c(0);function X(e){let t=R(e);return B()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:K}function Y(e,t,n,o){var r;void 0===t&&(t=!1),void 0===n&&(n=!1);let l=e.getBoundingClientRect(),i=M(e),a=c(1);t&&(o?O(o)&&(a=z(o)):a=z(e));let s=(void 0===(r=n)&&(r=!1),o&&(!r||o===R(i))&&r)?X(i):c(0),u=(l.left+s.x)/a.x,d=(l.top+s.y)/a.y,f=l.width/a.x,p=l.height/a.y;if(i){let e=R(i),t=o&&O(o)?R(o):o,n=e,r=V(n);for(;r&&o&&t!==n;){let e=z(r),t=r.getBoundingClientRect(),o=D(r),l=t.left+(r.clientLeft+parseFloat(o.paddingLeft))*e.x,i=t.top+(r.clientTop+parseFloat(o.paddingTop))*e.y;u*=e.x,d*=e.y,f*=e.x,p*=e.y,u+=l,d+=i,r=V(n=R(r))}}return b({width:f,height:p,x:u,y:d})}function U(e,t){let n=H(e).scrollLeft;return t?t.left+n:Y(T(e)).left+n}function Z(e,t,n){void 0===n&&(n=!1);let o=e.getBoundingClientRect();return{x:o.left+t.scrollLeft-(n?0:U(e,o)),y:o.top+t.scrollTop}}function G(e,t,n){let o;if("viewport"===t)o=function(e,t){let n=R(e),o=T(e),r=n.visualViewport,l=o.clientWidth,i=o.clientHeight,a=0,c=0;if(r){l=r.width,i=r.height;let e=B();(!e||e&&"fixed"===t)&&(a=r.offsetLeft,c=r.offsetTop)}return{width:l,height:i,x:a,y:c}}(e,n);else if("document"===t)o=function(e){let t=T(e),n=H(e),o=e.ownerDocument.body,r=l(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),i=l(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight),a=-n.scrollLeft+U(e),c=-n.scrollTop;return"rtl"===D(o).direction&&(a+=l(t.clientWidth,o.clientWidth)-r),{width:r,height:i,x:a,y:c}}(T(e));else if(O(t))o=function(e,t){let n=Y(e,!0,"fixed"===t),o=n.top+e.clientTop,r=n.left+e.clientLeft,l=C(e)?z(e):c(1),i=e.clientWidth*l.x;return{width:i,height:e.clientHeight*l.y,x:r*l.x,y:o*l.y}}(t,n);else{let n=X(e);o={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(o)}function J(e){return"static"===D(e).position}function Q(e,t){if(!C(e)||"fixed"===D(e).position)return null;if(t)return t(e);let n=e.offsetParent;return T(e)===n&&(n=n.ownerDocument.body),n}function ee(e,t){let n=R(e);if(j(e))return n;if(!C(e)){let t=W(e);for(;t&&!$(t);){if(O(t)&&!J(t))return t;t=W(t)}return n}let o=Q(e,t);for(;o&&["table","td","th"].includes(k(o))&&J(o);)o=Q(o,t);return o&&$(o)&&J(o)&&!I(o)?n:o||function(e){let t=W(e);for(;C(t)&&!$(t);){if(I(t))return t;if(j(t))break;t=W(t)}return null}(e)||n}let et=async function(e){let t=this.getOffsetParent||ee,n=this.getDimensions,o=await n(e.floating);return{reference:function(e,t,n){let o=C(t),r=T(t),l="fixed"===n,i=Y(e,!0,l,t),a={scrollLeft:0,scrollTop:0},s=c(0);if(o||!o&&!l){if(("body"!==k(t)||N(r))&&(a=H(t)),o){let e=Y(t,!0,l,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else r&&(s.x=U(r))}let u=!r||o||l?c(0):Z(r,a);return{x:i.left+a.scrollLeft-s.x-u.x,y:i.top+a.scrollTop-s.y-u.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}},en={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:o,strategy:r}=e,l="fixed"===r,i=T(o),a=!!t&&j(t.floating);if(o===i||a&&l)return n;let s={scrollLeft:0,scrollTop:0},u=c(1),d=c(0),f=C(o);if((f||!f&&!l)&&(("body"!==k(o)||N(i))&&(s=H(o)),C(o))){let e=Y(o);u=z(o),d.x=e.x+o.clientLeft,d.y=e.y+o.clientTop}let p=!i||f||l?c(0):Z(i,s,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-s.scrollLeft*u.x+d.x+p.x,y:n.y*u.y-s.scrollTop*u.y+d.y+p.y}},getDocumentElement:T,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:o,strategy:i}=e,a=[..."clippingAncestors"===n?j(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let o=F(e,[],!1).filter(e=>O(e)&&"body"!==k(e)),r=null,l="fixed"===D(e).position,i=l?W(e):e;for(;O(i)&&!$(i);){let t=D(i),n=I(i);n||"fixed"!==t.position||(r=null),(l?!n&&!r:!n&&"static"===t.position&&!!r&&["absolute","fixed"].includes(r.position)||N(i)&&!n&&function e(t,n){let o=W(t);return!(o===n||!O(o)||$(o))&&("fixed"===D(o).position||e(o,n))}(e,i))?o=o.filter(e=>e!==i):r=t,i=W(i)}return t.set(e,o),o}(t,this._c):[].concat(n),o],c=a[0],s=a.reduce((e,n)=>{let o=G(t,n,i);return e.top=l(o.top,e.top),e.right=r(o.right,e.right),e.bottom=r(o.bottom,e.bottom),e.left=l(o.left,e.left),e},G(t,c,i));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:ee,getElementRects:et,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=q(e);return{width:t,height:n}},getScale:z,isElement:O,isRTL:function(e){return"rtl"===D(e).direction}},eo=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,o;let{x:r,y:l,placement:i,middlewareData:a}=t,c=await x(t,e);return i===(null==(n=a.offset)?void 0:n.placement)&&null!=(o=a.arrow)&&o.alignmentOffset?{}:{x:r+c.x,y:l+c.y,data:{...c,placement:i}}}}},er=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:o,placement:i}=t,{mainAxis:a=!0,crossAxis:c=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=d(e,t),p={x:n,y:o},m=await S(t,u),y=h(f(i)),w=v(y),g=p[w],b=p[y];if(a){let e="y"===w?"top":"left",t="y"===w?"bottom":"right",n=g+m[e],o=g-m[t];g=l(n,r(g,o))}if(c){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=b+m[e],o=b-m[t];b=l(n,r(b,o))}let _=s.fn({...t,[w]:g,[y]:b});return{..._,data:{x:_.x-n,y:_.y-o,enabled:{[w]:a,[y]:c}}}}}},el=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,o,r,l,i;let{placement:a,middlewareData:c,rects:s,initialPlacement:u,platform:g,elements:b}=t,{mainAxis:_=!0,crossAxis:E=!0,fallbackPlacements:x,fallbackStrategy:A="bestFit",fallbackAxisSideDirection:k="none",flipAlignment:R=!0,...T}=d(e,t);if(null!=(n=c.arrow)&&n.alignmentOffset)return{};let L=f(a),O=h(u),C=f(u)===u,P=await (null==g.isRTL?void 0:g.isRTL(b.floating)),N=x||(C||!R?[w(u)]:function(e){let t=w(e);return[y(e),t,y(t)]}(u)),j="none"!==k;!x&&j&&N.push(...function(e,t,n,o){let r=p(e),l=function(e,t,n){let o=["left","right"],r=["right","left"];switch(e){case"top":case"bottom":if(n)return t?r:o;return t?o:r;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(f(e),"start"===n,o);return r&&(l=l.map(e=>e+"-"+r),t&&(l=l.concat(l.map(y)))),l}(u,R,k,P));let I=[u,...N],B=await S(t,T),$=[],D=(null==(o=c.flip)?void 0:o.overflows)||[];if(_&&$.push(B[L]),E){let e=function(e,t,n){void 0===n&&(n=!1);let o=p(e),r=v(h(e)),l=m(r),i="x"===r?o===(n?"end":"start")?"right":"left":"start"===o?"bottom":"top";return t.reference[l]>t.floating[l]&&(i=w(i)),[i,w(i)]}(a,s,P);$.push(B[e[0]],B[e[1]])}if(D=[...D,{placement:a,overflows:$}],!$.every(e=>e<=0)){let e=((null==(r=c.flip)?void 0:r.index)||0)+1,t=I[e];if(t)return{data:{index:e,overflows:D},reset:{placement:t}};let n=null==(l=D.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:l.placement;if(!n)switch(A){case"bestFit":{let e=null==(i=D.filter(e=>{if(j){let t=h(e.placement);return t===O||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:i[0];e&&(n=e);break}case"initialPlacement":n=u}if(a!==n)return{reset:{placement:n}}}return{}}}},ei=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:o,placement:i,rects:a,platform:c,elements:s,middlewareData:u}=t,{element:f,padding:y=0}=d(e,t)||{};if(null==f)return{};let w=g(y),b={x:n,y:o},_=v(h(i)),E=m(_),S=await c.getDimensions(f),x="y"===_,A=x?"clientHeight":"clientWidth",k=a.reference[E]+a.reference[_]-b[_]-a.floating[E],R=b[_]-a.reference[_],T=await (null==c.getOffsetParent?void 0:c.getOffsetParent(f)),L=T?T[A]:0;L&&await (null==c.isElement?void 0:c.isElement(T))||(L=s.floating[A]||a.floating[E]);let O=L/2-S[E]/2-1,C=r(w[x?"top":"left"],O),P=r(w[x?"bottom":"right"],O),N=L-S[E]-P,j=L/2-S[E]/2+(k/2-R/2),I=l(C,r(j,N)),B=!u.arrow&&null!=p(i)&&j!==I&&a.reference[E]/2-(j<C?C:P)-S[E]/2<0,$=B?j<C?j-C:j-N:0;return{[_]:b[_]+$,data:{[_]:I,centerOffset:j-I-$,...B&&{alignmentOffset:$}},reset:B}}}),ea=(e,t,n)=>{let o=new Map,r={platform:en,...n},l={...r.platform,_c:o};return E(e,t,{...r,platform:l})};var ec=n(36760),es=n(40257);let eu={core:!1,base:!1};function ed({css:e,id:t="react-tooltip-base-styles",type:n="base",ref:o}){var r,l;if(!e||"undefined"==typeof document||eu[n]||"core"===n&&void 0!==es&&(null===(r=null==es?void 0:es.env)||void 0===r?void 0:r.REACT_TOOLTIP_DISABLE_CORE_STYLES)||"base"!==n&&void 0!==es&&(null===(l=null==es?void 0:es.env)||void 0===l?void 0:l.REACT_TOOLTIP_DISABLE_BASE_STYLES))return;"core"===n&&(t="react-tooltip-core-styles"),o||(o={});let{insertAt:i}=o;if(document.getElementById(t))return;let a=document.head||document.getElementsByTagName("head")[0],c=document.createElement("style");c.id=t,c.type="text/css","top"===i&&a.firstChild?a.insertBefore(c,a.firstChild):a.appendChild(c),c.styleSheet?c.styleSheet.cssText=e:c.appendChild(document.createTextNode(e)),eu[n]=!0}let ef=async({elementReference:e=null,tooltipReference:t=null,tooltipArrowReference:n=null,place:o="top",offset:r=10,strategy:l="absolute",middlewares:i=[eo(Number(r)),el({fallbackAxisSideDirection:"start"}),er({padding:5})],border:a})=>e&&null!==t?n?(i.push(ei({element:n,padding:5})),ea(e,t,{placement:o,strategy:l,middleware:i}).then(({x:e,y:t,placement:n,middlewareData:o})=>{var r,l;let i={left:`${e}px`,top:`${t}px`,border:a},{x:c,y:s}=null!==(r=o.arrow)&&void 0!==r?r:{x:0,y:0},u=null!==(l=({top:"bottom",right:"left",bottom:"top",left:"right"})[n.split("-")[0]])&&void 0!==l?l:"bottom",d=0;if(a){let e=`${a}`.match(/(\d+)px/);d=(null==e?void 0:e[1])?Number(e[1]):1}return{tooltipStyles:i,tooltipArrowStyles:{left:null!=c?`${c}px`:"",top:null!=s?`${s}px`:"",right:"",bottom:"",...a&&{borderBottom:a,borderRight:a},[u]:`-${4+d}px`},place:n}})):ea(e,t,{placement:"bottom",strategy:l,middleware:i}).then(({x:e,y:t,placement:n})=>({tooltipStyles:{left:`${e}px`,top:`${t}px`},tooltipArrowStyles:{},place:n})):{tooltipStyles:{},tooltipArrowStyles:{},place:o},ep=(e,t)=>!("CSS"in window&&"supports"in window.CSS)||window.CSS.supports(e,t),ev=(e,t,n)=>{let o=null,r=function(...r){let l=()=>{o=null,n||e.apply(this,r)};n&&!o&&(e.apply(this,r),o=setTimeout(l,t)),n||(o&&clearTimeout(o),o=setTimeout(l,t))};return r.cancel=()=>{o&&(clearTimeout(o),o=null)},r},em=e=>null!==e&&!Array.isArray(e)&&"object"==typeof e,eh=(e,t)=>{if(e===t)return!0;if(Array.isArray(e)&&Array.isArray(t))return e.length===t.length&&e.every((e,n)=>eh(e,t[n]));if(Array.isArray(e)!==Array.isArray(t))return!1;if(!em(e)||!em(t))return e===t;let n=Object.keys(e),o=Object.keys(t);return n.length===o.length&&n.every(n=>eh(e[n],t[n]))},ey=e=>{if(!(e instanceof HTMLElement||e instanceof SVGElement))return!1;let t=getComputedStyle(e);return["overflow","overflow-x","overflow-y"].some(e=>{let n=t.getPropertyValue(e);return"auto"===n||"scroll"===n})},ew=e=>{if(!e)return null;let t=e.parentElement;for(;t;){if(ey(t))return t;t=t.parentElement}return document.scrollingElement||document.documentElement},eg="undefined"!=typeof window?o.useLayoutEffect:o.useEffect,eb=e=>{e.current&&(clearTimeout(e.current),e.current=null)},e_={anchorRefs:new Set,activeAnchor:{current:null},attach:()=>{},detach:()=>{},setActiveAnchor:()=>{}},eE=(0,o.createContext)({getTooltipData:()=>e_});function eS(e="DEFAULT_TOOLTIP_ID"){return(0,o.useContext)(eE).getTooltipData(e)}var ex={tooltip:"core-styles-module_tooltip__3vRRp",fixed:"core-styles-module_fixed__pcSol",arrow:"core-styles-module_arrow__cvMwQ",noArrow:"core-styles-module_noArrow__xock6",clickable:"core-styles-module_clickable__ZuTTB",show:"core-styles-module_show__Nt9eE",closing:"core-styles-module_closing__sGnxF"},eA={tooltip:"styles-module_tooltip__mnnfp",arrow:"styles-module_arrow__K0L3T",dark:"styles-module_dark__xNqje",light:"styles-module_light__Z6W-X",success:"styles-module_success__A2AKt",warning:"styles-module_warning__SCK0X",error:"styles-module_error__JvumD",info:"styles-module_info__BWdHW"};let ek=({forwardRef:e,id:t,className:n,classNameArrow:i,variant:c="dark",anchorId:s,anchorSelect:u,place:d="top",offset:f=10,events:p=["hover"],openOnClick:v=!1,positionStrategy:m="absolute",middlewares:h,wrapper:y,delayShow:w=0,delayHide:g=0,float:b=!1,hidden:_=!1,noArrow:E=!1,clickable:S=!1,closeOnEsc:x=!1,closeOnScroll:A=!1,closeOnResize:k=!1,openEvents:R,closeEvents:L,globalCloseEvents:O,imperativeModeOnly:C,style:P,position:N,afterShow:j,afterHide:I,disableTooltip:B,content:$,contentWrapperRef:D,isOpen:H,defaultIsOpen:W=!1,setIsOpen:V,activeAnchor:q,setActiveAnchor:z,border:K,opacity:X,arrowColor:U,role:Z="tooltip"})=>{var G;let J=(0,o.useRef)(null),Q=(0,o.useRef)(null),ee=(0,o.useRef)(null),et=(0,o.useRef)(null),en=(0,o.useRef)(null),[eo,er]=(0,o.useState)({tooltipStyles:{},tooltipArrowStyles:{},place:d}),[el,ei]=(0,o.useState)(!1),[ea,es]=(0,o.useState)(!1),[eu,ed]=(0,o.useState)(null),ep=(0,o.useRef)(!1),em=(0,o.useRef)(null),{anchorRefs:ey,setActiveAnchor:e_}=eS(t),eE=(0,o.useRef)(!1),[ek,eR]=(0,o.useState)([]),eT=(0,o.useRef)(!1),eL=v||p.includes("click"),eO=eL||(null==R?void 0:R.click)||(null==R?void 0:R.dblclick)||(null==R?void 0:R.mousedown),eC=R?{...R}:{mouseover:!0,focus:!0,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1};!R&&eL&&Object.assign(eC,{mouseenter:!1,focus:!1,mouseover:!1,click:!0});let eP=L?{...L}:{mouseout:!0,blur:!0,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1};!L&&eL&&Object.assign(eP,{mouseleave:!1,blur:!1,mouseout:!1});let eN=O?{...O}:{escape:x||!1,scroll:A||!1,resize:k||!1,clickOutsideAnchor:eO||!1};C&&(Object.assign(eC,{mouseenter:!1,focus:!1,click:!1,dblclick:!1,mousedown:!1}),Object.assign(eP,{mouseleave:!1,blur:!1,click:!1,dblclick:!1,mouseup:!1}),Object.assign(eN,{escape:!1,scroll:!1,resize:!1,clickOutsideAnchor:!1})),eg(()=>(eT.current=!0,()=>{eT.current=!1}),[]);let ej=e=>{eT.current&&(e&&es(!0),setTimeout(()=>{eT.current&&(null==V||V(e),void 0===H&&ei(e))},10))};(0,o.useEffect)(()=>{if(void 0===H)return()=>null;H&&es(!0);let e=setTimeout(()=>{ei(H)},10);return()=>{clearTimeout(e)}},[H]),(0,o.useEffect)(()=>{if(el!==ep.current){if(eb(en),ep.current=el,el)null==j||j();else{let e=(e=>{let t=e.match(/^([\d.]+)(ms|s)$/);if(!t)return 0;let[,n,o]=t;return Number(n)*("ms"===o?1:1e3)})(getComputedStyle(document.body).getPropertyValue("--rt-transition-show-delay"));en.current=setTimeout(()=>{es(!1),ed(null),null==I||I()},e+25)}}},[el]);let eI=e=>{er(t=>eh(t,e)?t:e)},eB=(e=w)=>{eb(ee),ea?ej(!0):ee.current=setTimeout(()=>{ej(!0)},e)},e$=(e=g)=>{eb(et),et.current=setTimeout(()=>{eE.current||ej(!1)},e)},eD=e=>{var t;if(!e)return;let n=null!==(t=e.currentTarget)&&void 0!==t?t:e.target;if(!(null==n?void 0:n.isConnected))return z(null),void e_({current:null});w?eB():ej(!0),z(n),e_({current:n}),eb(et)},eH=()=>{S?e$(g||100):g?e$():ej(!1),eb(ee)},eW=({x:e,y:t})=>{var n;ef({place:null!==(n=null==eu?void 0:eu.place)&&void 0!==n?n:d,offset:f,elementReference:{getBoundingClientRect:()=>({x:e,y:t,width:0,height:0,top:t,left:e,right:e,bottom:t})},tooltipReference:J.current,tooltipArrowReference:Q.current,strategy:m,middlewares:h,border:K}).then(e=>{eI(e)})},eF=e=>{if(!e)return;let t={x:e.clientX,y:e.clientY};eW(t),em.current=t},eV=e=>{var t;if(!el)return;let n=e.target;n.isConnected&&(null===(t=J.current)||void 0===t||!t.contains(n))&&([document.querySelector(`[id='${s}']`),...ek].some(e=>null==e?void 0:e.contains(n))||(ej(!1),eb(ee)))},eq=ev(eD,50,!0),eM=ev(eH,50,!0),ez=e=>{eM.cancel(),eq(e)},eK=()=>{eq.cancel(),eM()},eX=(0,o.useCallback)(()=>{var e,t;let n=null!==(e=null==eu?void 0:eu.position)&&void 0!==e?e:N;n?eW(n):b?em.current&&eW(em.current):(null==q?void 0:q.isConnected)&&ef({place:null!==(t=null==eu?void 0:eu.place)&&void 0!==t?t:d,offset:f,elementReference:q,tooltipReference:J.current,tooltipArrowReference:Q.current,strategy:m,middlewares:h,border:K}).then(e=>{eT.current&&eI(e)})},[el,q,$,P,d,null==eu?void 0:eu.place,f,m,N,null==eu?void 0:eu.position,b]);(0,o.useEffect)(()=>{var e,t;let n=new Set(ey);ek.forEach(e=>{(null==B?void 0:B(e))||n.add({current:e})});let o=document.querySelector(`[id='${s}']`);!o||(null==B?void 0:B(o))||n.add({current:o});let i=()=>{ej(!1)},c=ew(q),u=ew(J.current);eN.scroll&&(window.addEventListener("scroll",i),null==c||c.addEventListener("scroll",i),null==u||u.addEventListener("scroll",i));let d=null;eN.resize?window.addEventListener("resize",i):q&&J.current&&(d=function(e,t,n,o){let i;void 0===o&&(o={});let{ancestorScroll:c=!0,ancestorResize:s=!0,elementResize:u="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=o,p=M(e),v=c||s?[...p?F(p):[],...F(t)]:[];v.forEach(e=>{c&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)});let m=p&&d?function(e,t){let n,o=null,i=T(e);function c(){var e;clearTimeout(n),null==(e=o)||e.disconnect(),o=null}return function s(u,d){void 0===u&&(u=!1),void 0===d&&(d=1),c();let{left:f,top:p,width:v,height:m}=e.getBoundingClientRect();if(u||t(),!v||!m)return;let h=a(p),y=a(i.clientWidth-(f+v)),w={rootMargin:-h+"px "+-y+"px "+-a(i.clientHeight-(p+m))+"px "+-a(f)+"px",threshold:l(0,r(1,d))||1},g=!0;function b(e){let t=e[0].intersectionRatio;if(t!==d){if(!g)return s();t?s(!1,t):n=setTimeout(()=>{s(!1,1e-7)},1e3)}g=!1}try{o=new IntersectionObserver(b,{...w,root:i.ownerDocument})}catch(e){o=new IntersectionObserver(b,w)}o.observe(e)}(!0),c}(p,n):null,h=-1,y=null;u&&(y=new ResizeObserver(e=>{let[o]=e;o&&o.target===p&&y&&(y.unobserve(t),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),n()}),p&&!f&&y.observe(p),y.observe(t));let w=f?Y(e):null;return f&&function t(){let o=Y(e);w&&(o.x!==w.x||o.y!==w.y||o.width!==w.width||o.height!==w.height)&&n(),w=o,i=requestAnimationFrame(t)}(),n(),()=>{var e;v.forEach(e=>{c&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=y)||e.disconnect(),y=null,f&&cancelAnimationFrame(i)}}(q,J.current,eX,{ancestorResize:!0,elementResize:!0,layoutShift:!0}));let f=e=>{"Escape"===e.key&&ej(!1)};eN.escape&&window.addEventListener("keydown",f),eN.clickOutsideAnchor&&window.addEventListener("click",eV);let p=[],v=e=>{el&&(null==e?void 0:e.target)===q||eD(e)},m=e=>{el&&(null==e?void 0:e.target)===q&&eH()},h=["mouseover","mouseout","mouseenter","mouseleave","focus","blur"],y=["click","dblclick","mousedown","mouseup"];Object.entries(eC).forEach(([e,t])=>{t&&(h.includes(e)?p.push({event:e,listener:ez}):y.includes(e)&&p.push({event:e,listener:v}))}),Object.entries(eP).forEach(([e,t])=>{t&&(h.includes(e)?p.push({event:e,listener:eK}):y.includes(e)&&p.push({event:e,listener:m}))}),b&&p.push({event:"pointermove",listener:eF});let w=()=>{eE.current=!0},g=()=>{eE.current=!1,eH()};return S&&!eO&&(null===(e=J.current)||void 0===e||e.addEventListener("mouseenter",w),null===(t=J.current)||void 0===t||t.addEventListener("mouseleave",g)),p.forEach(({event:e,listener:t})=>{n.forEach(n=>{var o;null===(o=n.current)||void 0===o||o.addEventListener(e,t)})}),()=>{var e,t;eN.scroll&&(window.removeEventListener("scroll",i),null==c||c.removeEventListener("scroll",i),null==u||u.removeEventListener("scroll",i)),eN.resize?window.removeEventListener("resize",i):null==d||d(),eN.clickOutsideAnchor&&window.removeEventListener("click",eV),eN.escape&&window.removeEventListener("keydown",f),S&&!eO&&(null===(e=J.current)||void 0===e||e.removeEventListener("mouseenter",w),null===(t=J.current)||void 0===t||t.removeEventListener("mouseleave",g)),p.forEach(({event:e,listener:t})=>{n.forEach(n=>{var o;null===(o=n.current)||void 0===o||o.removeEventListener(e,t)})})}},[q,eX,ea,ey,ek,R,L,O,eL,w,g]),(0,o.useEffect)(()=>{var e,n;let o=null!==(n=null!==(e=null==eu?void 0:eu.anchorSelect)&&void 0!==e?e:u)&&void 0!==n?n:"";!o&&t&&(o=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`);let r=new MutationObserver(e=>{let n=[],r=[];e.forEach(e=>{if("attributes"===e.type&&"data-tooltip-id"===e.attributeName&&(e.target.getAttribute("data-tooltip-id")===t?n.push(e.target):e.oldValue===t&&r.push(e.target)),"childList"===e.type){if(q){let t=[...e.removedNodes].filter(e=>1===e.nodeType);if(o)try{r.push(...t.filter(e=>e.matches(o))),r.push(...t.flatMap(e=>[...e.querySelectorAll(o)]))}catch(e){}t.some(e=>{var t;return!!(null===(t=null==e?void 0:e.contains)||void 0===t?void 0:t.call(e,q))&&(es(!1),ej(!1),z(null),eb(ee),eb(et),!0)})}if(o)try{let t=[...e.addedNodes].filter(e=>1===e.nodeType);n.push(...t.filter(e=>e.matches(o))),n.push(...t.flatMap(e=>[...e.querySelectorAll(o)]))}catch(e){}}}),(n.length||r.length)&&eR(e=>[...e.filter(e=>!r.includes(e)),...n])});return r.observe(document.body,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["data-tooltip-id"],attributeOldValue:!0}),()=>{r.disconnect()}},[t,u,null==eu?void 0:eu.anchorSelect,q]),(0,o.useEffect)(()=>{eX()},[eX]),(0,o.useEffect)(()=>{if(!(null==D?void 0:D.current))return()=>null;let e=new ResizeObserver(()=>{setTimeout(()=>eX())});return e.observe(D.current),()=>{e.disconnect()}},[$,null==D?void 0:D.current]),(0,o.useEffect)(()=>{var e;let t=document.querySelector(`[id='${s}']`),n=[...ek,t];q&&n.includes(q)||z(null!==(e=ek[0])&&void 0!==e?e:t)},[s,ek,q]),(0,o.useEffect)(()=>(W&&ej(!0),()=>{eb(ee),eb(et)}),[]),(0,o.useEffect)(()=>{var e;let n=null!==(e=null==eu?void 0:eu.anchorSelect)&&void 0!==e?e:u;if(!n&&t&&(n=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`),n)try{let e=Array.from(document.querySelectorAll(n));eR(e)}catch(e){eR([])}},[t,u,null==eu?void 0:eu.anchorSelect]),(0,o.useEffect)(()=>{ee.current&&(eb(ee),eB(w))},[w]);let eY=null!==(G=null==eu?void 0:eu.content)&&void 0!==G?G:$,eU=el&&Object.keys(eo.tooltipStyles).length>0;return(0,o.useImperativeHandle)(e,()=>({open:e=>{if(null==e?void 0:e.anchorSelect)try{document.querySelector(e.anchorSelect)}catch(t){return void console.warn(`[react-tooltip] "${e.anchorSelect}" is not a valid CSS selector`)}ed(null!=e?e:null),(null==e?void 0:e.delay)?eB(e.delay):ej(!0)},close:e=>{(null==e?void 0:e.delay)?e$(e.delay):ej(!1)},activeAnchor:q,place:eo.place,isOpen:!!(ea&&!_&&eY&&eU)})),ea&&!_&&eY?o.createElement(y,{id:t,role:Z,className:ec("react-tooltip",ex.tooltip,eA.tooltip,eA[c],n,`react-tooltip__place-${eo.place}`,ex[eU?"show":"closing"],eU?"react-tooltip__show":"react-tooltip__closing","fixed"===m&&ex.fixed,S&&ex.clickable),onTransitionEnd:e=>{eb(en),el||"opacity"!==e.propertyName||(es(!1),ed(null),null==I||I())},style:{...P,...eo.tooltipStyles,opacity:void 0!==X&&eU?X:void 0},ref:J},eY,o.createElement(y,{className:ec("react-tooltip-arrow",ex.arrow,eA.arrow,i,E&&ex.noArrow),style:{...eo.tooltipArrowStyles,background:U?`linear-gradient(to right bottom, transparent 50%, ${U} 50%)`:void 0},ref:Q})):null},eR=({content:e})=>o.createElement("span",{dangerouslySetInnerHTML:{__html:e}}),eT=o.forwardRef(({id:e,anchorId:t,anchorSelect:n,content:r,html:l,render:i,className:a,classNameArrow:c,variant:s="dark",place:u="top",offset:d=10,wrapper:f="div",children:p=null,events:v=["hover"],openOnClick:m=!1,positionStrategy:h="absolute",middlewares:y,delayShow:w=0,delayHide:g=0,float:b=!1,hidden:_=!1,noArrow:E=!1,clickable:S=!1,closeOnEsc:x=!1,closeOnScroll:A=!1,closeOnResize:k=!1,openEvents:R,closeEvents:T,globalCloseEvents:L,imperativeModeOnly:O=!1,style:C,position:P,isOpen:N,defaultIsOpen:j=!1,disableStyleInjection:I=!1,border:B,opacity:$,arrowColor:D,setIsOpen:H,afterShow:W,afterHide:F,disableTooltip:V,role:q="tooltip"},M)=>{let[z,K]=(0,o.useState)(r),[X,Y]=(0,o.useState)(l),[U,Z]=(0,o.useState)(u),[G,J]=(0,o.useState)(s),[Q,ee]=(0,o.useState)(d),[et,en]=(0,o.useState)(w),[eo,er]=(0,o.useState)(g),[el,ei]=(0,o.useState)(b),[ea,es]=(0,o.useState)(_),[eu,ed]=(0,o.useState)(f),[ef,ev]=(0,o.useState)(v),[em,eh]=(0,o.useState)(h),[ey,ew]=(0,o.useState)(null),[eg,eb]=(0,o.useState)(null),e_=(0,o.useRef)(I),{anchorRefs:eE,activeAnchor:ex}=eS(e),eA=e=>null==e?void 0:e.getAttributeNames().reduce((t,n)=>{var o;return n.startsWith("data-tooltip-")&&(t[n.replace(/^data-tooltip-/,"")]=null!==(o=null==e?void 0:e.getAttribute(n))&&void 0!==o?o:null),t},{}),eT=e=>{let t={place:e=>{Z(null!=e?e:u)},content:e=>{K(null!=e?e:r)},html:e=>{Y(null!=e?e:l)},variant:e=>{J(null!=e?e:s)},offset:e=>{ee(null===e?d:Number(e))},wrapper:e=>{ed(null!=e?e:f)},events:e=>{let t=null==e?void 0:e.split(" ");ev(null!=t?t:v)},"position-strategy":e=>{eh(null!=e?e:h)},"delay-show":e=>{en(null===e?w:Number(e))},"delay-hide":e=>{er(null===e?g:Number(e))},float:e=>{ei(null===e?b:"true"===e)},hidden:e=>{es(null===e?_:"true"===e)},"class-name":e=>{ew(e)}};Object.values(t).forEach(e=>e(null)),Object.entries(e).forEach(([e,n])=>{var o;null===(o=t[e])||void 0===o||o.call(t,n)})};(0,o.useEffect)(()=>{K(r)},[r]),(0,o.useEffect)(()=>{Y(l)},[l]),(0,o.useEffect)(()=>{Z(u)},[u]),(0,o.useEffect)(()=>{J(s)},[s]),(0,o.useEffect)(()=>{ee(d)},[d]),(0,o.useEffect)(()=>{en(w)},[w]),(0,o.useEffect)(()=>{er(g)},[g]),(0,o.useEffect)(()=>{ei(b)},[b]),(0,o.useEffect)(()=>{es(_)},[_]),(0,o.useEffect)(()=>{eh(h)},[h]),(0,o.useEffect)(()=>{e_.current!==I&&console.warn("[react-tooltip] Do not change `disableStyleInjection` dynamically.")},[I]),(0,o.useEffect)(()=>{"undefined"!=typeof window&&window.dispatchEvent(new CustomEvent("react-tooltip-inject-styles",{detail:{disableCore:"core"===I,disableBase:I}}))},[]),(0,o.useEffect)(()=>{var o;let r=new Set(eE),l=n;if(!l&&e&&(l=`[data-tooltip-id='${e.replace(/'/g,"\\'")}']`),l)try{document.querySelectorAll(l).forEach(e=>{r.add({current:e})})}catch(e){console.warn(`[react-tooltip] "${l}" is not a valid CSS selector`)}let i=document.querySelector(`[id='${t}']`);if(i&&r.add({current:i}),!r.size)return()=>null;let a=null!==(o=null!=eg?eg:i)&&void 0!==o?o:ex.current,c=new MutationObserver(e=>{e.forEach(e=>{var t;a&&"attributes"===e.type&&(null===(t=e.attributeName)||void 0===t?void 0:t.startsWith("data-tooltip-"))&&eT(eA(a))})});return a&&(eT(eA(a)),c.observe(a,{attributes:!0,childList:!1,subtree:!1})),()=>{c.disconnect()}},[eE,ex,eg,t,n]),(0,o.useEffect)(()=>{(null==C?void 0:C.border)&&console.warn("[react-tooltip] Do not set `style.border`. Use `border` prop instead."),B&&!ep("border",`${B}`)&&console.warn(`[react-tooltip] "${B}" is not a valid \`border\`.`),(null==C?void 0:C.opacity)&&console.warn("[react-tooltip] Do not set `style.opacity`. Use `opacity` prop instead."),$&&!ep("opacity",`${$}`)&&console.warn(`[react-tooltip] "${$}" is not a valid \`opacity\`.`)},[]);let eL=p,eO=(0,o.useRef)(null);if(i){let e=i({content:(null==eg?void 0:eg.getAttribute("data-tooltip-content"))||z||null,activeAnchor:eg});eL=e?o.createElement("div",{ref:eO,className:"react-tooltip-content-wrapper"},e):null}else z&&(eL=z);X&&(eL=o.createElement(eR,{content:X}));let eC={forwardRef:M,id:e,anchorId:t,anchorSelect:n,className:ec(a,ey),classNameArrow:c,content:eL,contentWrapperRef:eO,place:U,variant:G,offset:Q,wrapper:eu,events:ef,openOnClick:m,positionStrategy:em,middlewares:y,delayShow:et,delayHide:eo,float:el,hidden:ea,noArrow:E,clickable:S,closeOnEsc:x,closeOnScroll:A,closeOnResize:k,openEvents:R,closeEvents:T,globalCloseEvents:L,imperativeModeOnly:O,style:C,position:P,isOpen:N,defaultIsOpen:j,border:B,opacity:$,arrowColor:D,setIsOpen:H,afterShow:W,afterHide:F,disableTooltip:V,activeAnchor:eg,setActiveAnchor:e=>eb(e),role:q};return o.createElement(ek,{...eC})});"undefined"!=typeof window&&window.addEventListener("react-tooltip-inject-styles",e=>{e.detail.disableCore||ed({css:":root{--rt-color-white:#fff;--rt-color-dark:#222;--rt-color-success:#8dc572;--rt-color-error:#be6464;--rt-color-warning:#f0ad4e;--rt-color-info:#337ab7;--rt-opacity:0.9;--rt-transition-show-delay:0.15s;--rt-transition-closing-delay:0.15s}.core-styles-module_tooltip__3vRRp{position:absolute;top:0;left:0;pointer-events:none;opacity:0;will-change:opacity}.core-styles-module_fixed__pcSol{position:fixed}.core-styles-module_arrow__cvMwQ{position:absolute;background:inherit}.core-styles-module_noArrow__xock6{display:none}.core-styles-module_clickable__ZuTTB{pointer-events:auto}.core-styles-module_show__Nt9eE{opacity:var(--rt-opacity);transition:opacity var(--rt-transition-show-delay)ease-out}.core-styles-module_closing__sGnxF{opacity:0;transition:opacity var(--rt-transition-closing-delay)ease-in}",type:"core"}),e.detail.disableBase||ed({css:`
.styles-module_tooltip__mnnfp{padding:8px 16px;border-radius:3px;font-size:90%;width:max-content}.styles-module_arrow__K0L3T{width:8px;height:8px}[class*='react-tooltip__place-top']>.styles-module_arrow__K0L3T{transform:rotate(45deg)}[class*='react-tooltip__place-right']>.styles-module_arrow__K0L3T{transform:rotate(135deg)}[class*='react-tooltip__place-bottom']>.styles-module_arrow__K0L3T{transform:rotate(225deg)}[class*='react-tooltip__place-left']>.styles-module_arrow__K0L3T{transform:rotate(315deg)}.styles-module_dark__xNqje{background:var(--rt-color-dark);color:var(--rt-color-white)}.styles-module_light__Z6W-X{background-color:var(--rt-color-white);color:var(--rt-color-dark)}.styles-module_success__A2AKt{background-color:var(--rt-color-success);color:var(--rt-color-white)}.styles-module_warning__SCK0X{background-color:var(--rt-color-warning);color:var(--rt-color-white)}.styles-module_error__JvumD{background-color:var(--rt-color-error);color:var(--rt-color-white)}.styles-module_info__BWdHW{background-color:var(--rt-color-info);color:var(--rt-color-white)}`,type:"base"})})}}]);