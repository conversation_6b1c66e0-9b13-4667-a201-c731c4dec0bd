﻿using MicroSaasWebApi.Models.AlphaCCO;
using MicroSaasWebApi.Models.MICROSAAS.AppSettings;
using System.IdentityModel.Tokens.Jwt;

namespace MicroSaasWebApi.Services.MICROSAAS.Interface
{
    public interface IApplicationSettingsService
    {
        public Task SetAppConfigurationWithKeyVault();
        Tenant GetFilteredTenant();
        public Task SetAppSettingsFilteredTenantInHttpContext(string? audience);
        public Task SetAppSettingsEndPointsInHttpContext();

        public Task SetAlphaCCOWorkflowsInHttpContext();
        public AlphaCCOWorkflows GetAlphaCCOWorkflows();
        public void SetTokenValuesInHttpContext(JwtSecurityToken decodedToken);

        public void SetLanguageInHttpContext(string? language);
    }
}
