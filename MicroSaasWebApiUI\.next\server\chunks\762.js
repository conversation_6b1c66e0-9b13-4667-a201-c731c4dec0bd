exports.id=762,exports.ids=[762],exports.modules={16811:(e,a,i)=>{e.exports={parallel:i(65903),serial:i(11610),serialOrdered:i(37854)}},45961:e=>{e.exports=function(e){Object.keys(e.jobs).forEach(a.bind(e)),e.jobs={}};function a(e){"function"==typeof this.jobs[e]&&this.jobs[e]()}},96446:(e,a,i)=>{var t=i(10516);e.exports=function(e){var a=!1;return t(function(){a=!0}),function(i,n){a?e(i,n):t(function(){e(i,n)})}}},10516:e=>{e.exports=function(e){var a="function"==typeof setImmediate?setImmediate:"object"==typeof process&&"function"==typeof process.nextTick?process.nextTick:null;a?a(e):setTimeout(e,0)}},39262:(e,a,i)=>{var t=i(96446),n=i(45961);e.exports=function(e,a,i,o){var s,r,p=i.keyedList?i.keyedList[i.index]:i.index;i.jobs[p]=(s=e[p],r=function(e,a){p in i.jobs&&(delete i.jobs[p],e?n(i):i.results[p]=a,o(e,i.results))},2==a.length?a(s,t(r)):a(s,p,t(r)))}},66924:e=>{e.exports=function(e,a){var i=!Array.isArray(e),t={index:0,keyedList:i||a?Object.keys(e):null,jobs:{},results:i?{}:[],size:i?Object.keys(e).length:e.length};return a&&t.keyedList.sort(i?a:function(i,t){return a(e[i],e[t])}),t}},3528:(e,a,i)=>{var t=i(45961),n=i(96446);e.exports=function(e){Object.keys(this.jobs).length&&(this.index=this.size,t(this),n(e)(null,this.results))}},65903:(e,a,i)=>{var t=i(39262),n=i(66924),o=i(3528);e.exports=function(e,a,i){for(var s=n(e);s.index<(s.keyedList||e).length;)t(e,a,s,function(e,a){if(e){i(e,a);return}if(0===Object.keys(s.jobs).length){i(null,s.results);return}}),s.index++;return o.bind(s,i)}},11610:(e,a,i)=>{var t=i(37854);e.exports=function(e,a,i){return t(e,a,null,i)}},37854:(e,a,i)=>{var t=i(39262),n=i(66924),o=i(3528);function s(e,a){return e<a?-1:e>a?1:0}e.exports=function(e,a,i,s){var r=n(e,i);return t(e,a,r,function i(n,o){if(n){s(n,o);return}if(r.index++,r.index<(r.keyedList||e).length){t(e,a,r,i);return}s(null,r.results)}),o.bind(r,s)},e.exports.ascending=s,e.exports.descending=function(e,a){return -1*s(e,a)}},59130:(e,a,i)=>{var t=i(21764),n=i(76162).Stream,o=i(90932);function s(){this.writable=!1,this.readable=!0,this.dataSize=0,this.maxDataSize=2097152,this.pauseStreams=!0,this._released=!1,this._streams=[],this._currentStream=null,this._insideLoop=!1,this._pendingNext=!1}e.exports=s,t.inherits(s,n),s.create=function(e){var a=new this;for(var i in e=e||{})a[i]=e[i];return a},s.isStreamLike=function(e){return"function"!=typeof e&&"string"!=typeof e&&"boolean"!=typeof e&&"number"!=typeof e&&!Buffer.isBuffer(e)},s.prototype.append=function(e){if(s.isStreamLike(e)){if(!(e instanceof o)){var a=o.create(e,{maxDataSize:1/0,pauseStream:this.pauseStreams});e.on("data",this._checkDataSize.bind(this)),e=a}this._handleErrors(e),this.pauseStreams&&e.pause()}return this._streams.push(e),this},s.prototype.pipe=function(e,a){return n.prototype.pipe.call(this,e,a),this.resume(),e},s.prototype._getNext=function(){if(this._currentStream=null,this._insideLoop){this._pendingNext=!0;return}this._insideLoop=!0;try{do this._pendingNext=!1,this._realGetNext();while(this._pendingNext)}finally{this._insideLoop=!1}},s.prototype._realGetNext=function(){var e=this._streams.shift();if(void 0===e){this.end();return}if("function"!=typeof e){this._pipeNext(e);return}e((function(e){s.isStreamLike(e)&&(e.on("data",this._checkDataSize.bind(this)),this._handleErrors(e)),this._pipeNext(e)}).bind(this))},s.prototype._pipeNext=function(e){if(this._currentStream=e,s.isStreamLike(e)){e.on("end",this._getNext.bind(this)),e.pipe(this,{end:!1});return}this.write(e),this._getNext()},s.prototype._handleErrors=function(e){var a=this;e.on("error",function(e){a._emitError(e)})},s.prototype.write=function(e){this.emit("data",e)},s.prototype.pause=function(){this.pauseStreams&&(this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.pause&&this._currentStream.pause(),this.emit("pause"))},s.prototype.resume=function(){this._released||(this._released=!0,this.writable=!0,this._getNext()),this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.resume&&this._currentStream.resume(),this.emit("resume")},s.prototype.end=function(){this._reset(),this.emit("end")},s.prototype.destroy=function(){this._reset(),this.emit("close")},s.prototype._reset=function(){this.writable=!1,this._streams=[],this._currentStream=null},s.prototype._checkDataSize=function(){if(this._updateDataSize(),!(this.dataSize<=this.maxDataSize)){var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this._emitError(Error(e))}},s.prototype._updateDataSize=function(){this.dataSize=0;var e=this;this._streams.forEach(function(a){a.dataSize&&(e.dataSize+=a.dataSize)}),this._currentStream&&this._currentStream.dataSize&&(this.dataSize+=this._currentStream.dataSize)},s.prototype._emitError=function(e){this._reset(),this.emit("error",e)}},11314:(e,a)=>{!function(){"use strict";function e(a,i,t,n){return this instanceof e?(this.domain=a||void 0,this.path=i||"/",this.secure=!!t,this.script=!!n,this):new e(a,i,t,n)}function i(e,a,t){return e instanceof i?e:this instanceof i?(this.name=null,this.value=null,this.expiration_date=1/0,this.path=String(t||"/"),this.explicit_path=!1,this.domain=a||null,this.explicit_domain=!1,this.secure=!1,this.noscript=!1,e&&this.parse(e,a,t),this):new i(e,a,t)}e.All=Object.freeze(Object.create(null)),a.CookieAccessInfo=e,a.Cookie=i,i.prototype.toString=function(){var e=[this.name+"="+this.value];return this.expiration_date!==1/0&&e.push("expires="+new Date(this.expiration_date).toGMTString()),this.domain&&e.push("domain="+this.domain),this.path&&e.push("path="+this.path),this.secure&&e.push("secure"),this.noscript&&e.push("httponly"),e.join("; ")},i.prototype.toValueString=function(){return this.name+"="+this.value};var t=/[:](?=\s*[a-zA-Z0-9_\-]+\s*[=])/g;function n(){var e,a;return this instanceof n?(e=Object.create(null),this.setCookie=function(t,n,o){var s,r;if(s=(t=new i(t,n,o)).expiration_date<=Date.now(),void 0!==e[t.name]){for(r=0,a=e[t.name];r<a.length;r+=1)if(a[r].collidesWith(t)){if(s)return a.splice(r,1),0===a.length&&delete e[t.name],!1;return a[r]=t,t}return!s&&(a.push(t),t)}return!s&&(e[t.name]=[t],e[t.name])},this.getCookie=function(i,t){var n,o;if(a=e[i])for(o=0;o<a.length;o+=1){if((n=a[o]).expiration_date<=Date.now()){0===a.length&&delete e[n.name];continue}if(n.matches(t))return n}},this.getCookies=function(a){var i,t,n=[];for(i in e)(t=this.getCookie(i,a))&&n.push(t);return n.toString=function(){return n.join(":")},n.toValueString=function(){return n.map(function(e){return e.toValueString()}).join("; ")},n},this):new n}i.prototype.parse=function(e,a,t){if(this instanceof i){if(e.length>32768){console.warn("Cookie too long for parsing (>32768 characters)");return}var n,o=e.split(";").filter(function(e){return!!e}),s=o[0].match(/([^=]+)=([\s\S]*)/);if(!s){console.warn("Invalid cookie header encountered. Header: '"+e+"'");return}var r=s[1],p=s[2];if("string"!=typeof r||0===r.length||"string"!=typeof p){console.warn("Unable to extract values from cookie header. Cookie: '"+e+"'");return}for(n=1,this.name=r,this.value=p;n<o.length;n+=1)switch(r=(s=o[n].match(/([^=]+)(?:=([\s\S]*))?/))[1].trim().toLowerCase(),p=s[2],r){case"httponly":this.noscript=!0;break;case"expires":this.expiration_date=p?Number(Date.parse(p)):1/0;break;case"path":this.path=p?p.trim():"",this.explicit_path=!0;break;case"domain":this.domain=p?p.trim():"",this.explicit_domain=!!this.domain;break;case"secure":this.secure=!0}return this.explicit_path||(this.path=t||"/"),this.explicit_domain||(this.domain=a),this}return new i().parse(e,a,t)},i.prototype.matches=function(a){return a===e.All||(!this.noscript||!a.script)&&(!this.secure||!!a.secure)&&!!this.collidesWith(a)},i.prototype.collidesWith=function(e){if(this.path&&!e.path||this.domain&&!e.domain||this.path&&0!==e.path.indexOf(this.path)||this.explicit_path&&0!==e.path.indexOf(this.path))return!1;var a=e.domain&&e.domain.replace(/^[\.]/,""),i=this.domain&&this.domain.replace(/^[\.]/,"");if(i===a)return!0;if(i){if(!this.explicit_domain)return!1;var t=a.indexOf(i);if(-1===t||t!==a.length-i.length)return!1}return!0},a.CookieJar=n,n.prototype.setCookies=function(e,a,n){e=Array.isArray(e)?e:e.split(t);var o,s,r=[];for(o=0,e=e.map(function(e){return new i(e,a,n)});o<e.length;o+=1)s=e[o],this.setCookie(s,a,n)&&r.push(s);return r}}()},93050:(e,a,i)=>{a.formatArgs=function(a){if(a[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+a[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let i="color: "+this.color;a.splice(1,0,i,"color: inherit");let t=0,n=0;a[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(t++,"%c"===e&&(n=t))}),a.splice(n,0,i)},a.save=function(e){try{e?a.storage.setItem("debug",e):a.storage.removeItem("debug")}catch(e){}},a.load=function(){let e;try{e=a.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},a.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},a.storage=function(){try{return localStorage}catch(e){}}(),a.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),a.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],a.log=console.debug||console.log||(()=>{}),e.exports=i(76465)(a);let{formatters:t}=e.exports;t.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},76465:(e,a,i)=>{e.exports=function(e){function a(e){let i,n,o;let s=null;function r(...e){if(!r.enabled)return;let t=Number(new Date),n=t-(i||t);r.diff=n,r.prev=i,r.curr=t,i=t,e[0]=a.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let o=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(i,t)=>{if("%%"===i)return"%";o++;let n=a.formatters[t];if("function"==typeof n){let a=e[o];i=n.call(r,a),e.splice(o,1),o--}return i}),a.formatArgs.call(r,e),(r.log||a.log).apply(r,e)}return r.namespace=e,r.useColors=a.useColors(),r.color=a.selectColor(e),r.extend=t,r.destroy=a.destroy,Object.defineProperty(r,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(n!==a.namespaces&&(n=a.namespaces,o=a.enabled(e)),o),set:e=>{s=e}}),"function"==typeof a.init&&a.init(r),r}function t(e,i){let t=a(this.namespace+(void 0===i?":":i)+e);return t.log=this.log,t}function n(e,a){let i=0,t=0,n=-1,o=0;for(;i<e.length;)if(t<a.length&&(a[t]===e[i]||"*"===a[t]))"*"===a[t]?(n=t,o=i):i++,t++;else{if(-1===n)return!1;t=n+1,i=++o}for(;t<a.length&&"*"===a[t];)t++;return t===a.length}return a.debug=a,a.default=a,a.coerce=function(e){return e instanceof Error?e.stack||e.message:e},a.disable=function(){let e=[...a.names,...a.skips.map(e=>"-"+e)].join(",");return a.enable(""),e},a.enable=function(e){for(let i of(a.save(e),a.namespaces=e,a.names=[],a.skips=[],("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean)))"-"===i[0]?a.skips.push(i.slice(1)):a.names.push(i)},a.enabled=function(e){for(let i of a.skips)if(n(e,i))return!1;for(let i of a.names)if(n(e,i))return!0;return!1},a.humanize=i(13974),a.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(i=>{a[i]=e[i]}),a.names=[],a.skips=[],a.formatters={},a.selectColor=function(e){let i=0;for(let a=0;a<e.length;a++)i=(i<<5)-i+e.charCodeAt(a)|0;return a.colors[Math.abs(i)%a.colors.length]},a.enable(a.load()),a}},19092:(e,a,i)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=i(93050):e.exports=i(12226)},12226:(e,a,i)=>{let t=i(74175),n=i(21764);a.init=function(e){e.inspectOpts={};let i=Object.keys(a.inspectOpts);for(let t=0;t<i.length;t++)e.inspectOpts[i[t]]=a.inspectOpts[i[t]]},a.log=function(...e){return process.stderr.write(n.formatWithOptions(a.inspectOpts,...e)+"\n")},a.formatArgs=function(i){let{namespace:t,useColors:n}=this;if(n){let a=this.color,n="\x1b[3"+(a<8?a:"8;5;"+a),o=`  ${n};1m${t} \u001B[0m`;i[0]=o+i[0].split("\n").join("\n"+o),i.push(n+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else i[0]=(a.inspectOpts.hideDate?"":new Date().toISOString()+" ")+t+" "+i[0]},a.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},a.load=function(){return process.env.DEBUG},a.useColors=function(){return"colors"in a.inspectOpts?!!a.inspectOpts.colors:t.isatty(process.stderr.fd)},a.destroy=n.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),a.colors=[6,2,3,4,5,1];try{let e=i(67057);e&&(e.stderr||e).level>=2&&(a.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}a.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,a)=>{let i=a.substring(6).toLowerCase().replace(/_([a-z])/g,(e,a)=>a.toUpperCase()),t=process.env[a];return t=!!/^(yes|on|true|enabled)$/i.test(t)||!/^(no|off|false|disabled)$/i.test(t)&&("null"===t?null:Number(t)),e[i]=t,e},{}),e.exports=i(76465)(a);let{formatters:o}=e.exports;o.o=function(e){return this.inspectOpts.colors=this.useColors,n.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},o.O=function(e){return this.inspectOpts.colors=this.useColors,n.inspect(e,this.inspectOpts)}},90932:(e,a,i)=>{var t=i(76162).Stream,n=i(21764);function o(){this.source=null,this.dataSize=0,this.maxDataSize=1048576,this.pauseStream=!0,this._maxDataSizeExceeded=!1,this._released=!1,this._bufferedEvents=[]}e.exports=o,n.inherits(o,t),o.create=function(e,a){var i=new this;for(var t in a=a||{})i[t]=a[t];i.source=e;var n=e.emit;return e.emit=function(){return i._handleEmit(arguments),n.apply(e,arguments)},e.on("error",function(){}),i.pauseStream&&e.pause(),i},Object.defineProperty(o.prototype,"readable",{configurable:!0,enumerable:!0,get:function(){return this.source.readable}}),o.prototype.setEncoding=function(){return this.source.setEncoding.apply(this.source,arguments)},o.prototype.resume=function(){this._released||this.release(),this.source.resume()},o.prototype.pause=function(){this.source.pause()},o.prototype.release=function(){this._released=!0,this._bufferedEvents.forEach((function(e){this.emit.apply(this,e)}).bind(this)),this._bufferedEvents=[]},o.prototype.pipe=function(){var e=t.prototype.pipe.apply(this,arguments);return this.resume(),e},o.prototype._handleEmit=function(e){if(this._released){this.emit.apply(this,e);return}"data"===e[0]&&(this.dataSize+=e[1].length,this._checkIfMaxDataSizeExceeded()),this._bufferedEvents.push(e)},o.prototype._checkIfMaxDataSizeExceeded=function(){if(!this._maxDataSizeExceeded&&!(this.dataSize<=this.maxDataSize)){this._maxDataSizeExceeded=!0;var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this.emit("error",Error(e))}}},62502:(e,a,i)=>{var t;e.exports=function(){if(!t){try{t=i(19092)("follow-redirects")}catch(e){}"function"!=typeof t&&(t=function(){})}t.apply(null,arguments)}},86875:(e,a,i)=>{var t=i(17360),n=t.URL,o=i(32615),s=i(35240),r=i(76162).Writable,p=i(27790),c=i(62502);!function(){var e="undefined"!=typeof process,a="undefined"!=typeof window&&"undefined"!=typeof document,i=O(Error.captureStackTrace);e||!a&&i||console.warn("The follow-redirects package should be excluded from browser builds.")}();var l=!1;try{p(new n(""))}catch(e){l="ERR_INVALID_URL"===e.code}var u=["auth","host","hostname","href","path","pathname","port","protocol","query","search","hash"],d=["abort","aborted","connect","error","socket","timeout"],m=Object.create(null);d.forEach(function(e){m[e]=function(a,i,t){this._redirectable.emit(e,a,i,t)}});var h=R("ERR_INVALID_URL","Invalid URL",TypeError),f=R("ERR_FR_REDIRECTION_FAILURE","Redirected request failed"),x=R("ERR_FR_TOO_MANY_REDIRECTS","Maximum number of redirects exceeded",f),v=R("ERR_FR_MAX_BODY_LENGTH_EXCEEDED","Request body larger than maxBodyLength limit"),b=R("ERR_STREAM_WRITE_AFTER_END","write after end"),g=r.prototype.destroy||_;function y(e,a){r.call(this),this._sanitizeOptions(e),this._options=e,this._ended=!1,this._ending=!1,this._redirectCount=0,this._redirects=[],this._requestBodyLength=0,this._requestBodyBuffers=[],a&&this.on("response",a);var i=this;this._onNativeResponse=function(e){try{i._processResponse(e)}catch(e){i.emit("error",e instanceof f?e:new f({cause:e}))}},this._performRequest()}function w(e){var a={maxRedirects:21,maxBodyLength:10485760},i={};return Object.keys(e).forEach(function(t){var o=t+":",s=i[o]=e[t],r=a[t]=Object.create(s);Object.defineProperties(r,{request:{value:function(e,t,s){var r;return(r=e,n&&r instanceof n)?e=T(e):C(e)?e=T(k(e)):(s=t,t=E(e),e={protocol:o}),O(t)&&(s=t,t=null),(t=Object.assign({maxRedirects:a.maxRedirects,maxBodyLength:a.maxBodyLength},e,t)).nativeProtocols=i,C(t.host)||C(t.hostname)||(t.hostname="::1"),p.equal(t.protocol,o,"protocol mismatch"),c("options",t),new y(t,s)},configurable:!0,enumerable:!0,writable:!0},get:{value:function(e,a,i){var t=r.request(e,a,i);return t.end(),t},configurable:!0,enumerable:!0,writable:!0}})}),a}function _(){}function k(e){var a;if(l)a=new n(e);else if(!C((a=E(t.parse(e))).protocol))throw new h({input:e});return a}function E(e){if(/^\[/.test(e.hostname)&&!/^\[[:0-9a-f]+\]$/i.test(e.hostname)||/^\[/.test(e.host)&&!/^\[[:0-9a-f]+\](:\d+)?$/i.test(e.host))throw new h({input:e.href||e});return e}function T(e,a){var i=a||{};for(var t of u)i[t]=e[t];return i.hostname.startsWith("[")&&(i.hostname=i.hostname.slice(1,-1)),""!==i.port&&(i.port=Number(i.port)),i.path=i.search?i.pathname+i.search:i.pathname,i}function j(e,a){var i;for(var t in a)e.test(t)&&(i=a[t],delete a[t]);return null==i?void 0:String(i).trim()}function R(e,a,i){function t(i){O(Error.captureStackTrace)&&Error.captureStackTrace(this,this.constructor),Object.assign(this,i||{}),this.code=e,this.message=this.cause?a+": "+this.cause.message:a}return t.prototype=new(i||Error),Object.defineProperties(t.prototype,{constructor:{value:t,enumerable:!1},name:{value:"Error ["+e+"]",enumerable:!1}}),t}function S(e,a){for(var i of d)e.removeListener(i,m[i]);e.on("error",_),e.destroy(a)}function C(e){return"string"==typeof e||e instanceof String}function O(e){return"function"==typeof e}y.prototype=Object.create(r.prototype),y.prototype.abort=function(){S(this._currentRequest),this._currentRequest.abort(),this.emit("abort")},y.prototype.destroy=function(e){return S(this._currentRequest,e),g.call(this,e),this},y.prototype.write=function(e,a,i){if(this._ending)throw new b;if(!C(e)&&!("object"==typeof e&&"length"in e))throw TypeError("data should be a string, Buffer or Uint8Array");if(O(a)&&(i=a,a=null),0===e.length){i&&i();return}this._requestBodyLength+e.length<=this._options.maxBodyLength?(this._requestBodyLength+=e.length,this._requestBodyBuffers.push({data:e,encoding:a}),this._currentRequest.write(e,a,i)):(this.emit("error",new v),this.abort())},y.prototype.end=function(e,a,i){if(O(e)?(i=e,e=a=null):O(a)&&(i=a,a=null),e){var t=this,n=this._currentRequest;this.write(e,a,function(){t._ended=!0,n.end(null,null,i)}),this._ending=!0}else this._ended=this._ending=!0,this._currentRequest.end(null,null,i)},y.prototype.setHeader=function(e,a){this._options.headers[e]=a,this._currentRequest.setHeader(e,a)},y.prototype.removeHeader=function(e){delete this._options.headers[e],this._currentRequest.removeHeader(e)},y.prototype.setTimeout=function(e,a){var i=this;function t(a){a.setTimeout(e),a.removeListener("timeout",a.destroy),a.addListener("timeout",a.destroy)}function n(a){i._timeout&&clearTimeout(i._timeout),i._timeout=setTimeout(function(){i.emit("timeout"),o()},e),t(a)}function o(){i._timeout&&(clearTimeout(i._timeout),i._timeout=null),i.removeListener("abort",o),i.removeListener("error",o),i.removeListener("response",o),i.removeListener("close",o),a&&i.removeListener("timeout",a),i.socket||i._currentRequest.removeListener("socket",n)}return a&&this.on("timeout",a),this.socket?n(this.socket):this._currentRequest.once("socket",n),this.on("socket",t),this.on("abort",o),this.on("error",o),this.on("response",o),this.on("close",o),this},["flushHeaders","getHeader","setNoDelay","setSocketKeepAlive"].forEach(function(e){y.prototype[e]=function(a,i){return this._currentRequest[e](a,i)}}),["aborted","connection","socket"].forEach(function(e){Object.defineProperty(y.prototype,e,{get:function(){return this._currentRequest[e]}})}),y.prototype._sanitizeOptions=function(e){if(e.headers||(e.headers={}),e.host&&(e.hostname||(e.hostname=e.host),delete e.host),!e.pathname&&e.path){var a=e.path.indexOf("?");a<0?e.pathname=e.path:(e.pathname=e.path.substring(0,a),e.search=e.path.substring(a))}},y.prototype._performRequest=function(){var e=this._options.protocol,a=this._options.nativeProtocols[e];if(!a)throw TypeError("Unsupported protocol "+e);if(this._options.agents){var i=e.slice(0,-1);this._options.agent=this._options.agents[i]}var n=this._currentRequest=a.request(this._options,this._onNativeResponse);for(var o of(n._redirectable=this,d))n.on(o,m[o]);if(this._currentUrl=/^\//.test(this._options.path)?t.format(this._options):this._options.path,this._isRedirect){var s=0,r=this,p=this._requestBodyBuffers;!function e(a){if(n===r._currentRequest){if(a)r.emit("error",a);else if(s<p.length){var i=p[s++];n.finished||n.write(i.data,i.encoding,e)}else r._ended&&n.end()}}()}},y.prototype._processResponse=function(e){var a,i,o,s=e.statusCode;this._options.trackRedirects&&this._redirects.push({url:this._currentUrl,headers:e.headers,statusCode:s});var r=e.headers.location;if(!r||!1===this._options.followRedirects||s<300||s>=400){e.responseUrl=this._currentUrl,e.redirects=this._redirects,this.emit("response",e),this._requestBodyBuffers=[];return}if(S(this._currentRequest),e.destroy(),++this._redirectCount>this._options.maxRedirects)throw new x;var u=this._options.beforeRedirect;u&&(o=Object.assign({Host:e.req.getHeader("host")},this._options.headers));var d=this._options.method;(301!==s&&302!==s||"POST"!==this._options.method)&&(303!==s||/^(?:GET|HEAD)$/.test(this._options.method))||(this._options.method="GET",this._requestBodyBuffers=[],j(/^content-/i,this._options.headers));var m=j(/^host$/i,this._options.headers),h=k(this._currentUrl),f=m||h.host,v=/^\w+:/.test(r)?this._currentUrl:t.format(Object.assign(h,{host:f})),b=l?new n(r,v):k(t.resolve(v,r));if(c("redirecting to",b.href),this._isRedirect=!0,T(b,this._options),(b.protocol===h.protocol||"https:"===b.protocol)&&(b.host===f||(p(C(a=b.host)&&C(f)),(i=a.length-f.length-1)>0&&"."===a[i]&&a.endsWith(f)))||j(/^(?:(?:proxy-)?authorization|cookie)$/i,this._options.headers),O(u)){var g={headers:e.headers,statusCode:s},y={url:v,method:d,headers:o};u(this._options,g,y),this._sanitizeOptions(this._options)}this._performRequest()},e.exports=w({http:o,https:s}),e.exports.wrap=w},32291:(e,a,i)=>{var t=i(59130),n=i(21764),o=i(55315),s=i(32615),r=i(35240),p=i(17360).parse,c=i(92048),l=i(76162).Stream,u=i(89427),d=i(16811),m=i(9060);function h(e){if(!(this instanceof h))return new h(e);for(var a in this._overheadLength=0,this._valueLength=0,this._valuesToMeasure=[],t.call(this),e=e||{})this[a]=e[a]}e.exports=h,n.inherits(h,t),h.LINE_BREAK="\r\n",h.DEFAULT_CONTENT_TYPE="application/octet-stream",h.prototype.append=function(e,a,i){"string"==typeof(i=i||{})&&(i={filename:i});var n=t.prototype.append.bind(this);if("number"==typeof a&&(a=""+a),Array.isArray(a)){this._error(Error("Arrays are not supported."));return}var o=this._multiPartHeader(e,a,i),s=this._multiPartFooter();n(o),n(a),n(s),this._trackLength(o,a,i)},h.prototype._trackLength=function(e,a,i){var t=0;null!=i.knownLength?t+=+i.knownLength:Buffer.isBuffer(a)?t=a.length:"string"==typeof a&&(t=Buffer.byteLength(a)),this._valueLength+=t,this._overheadLength+=Buffer.byteLength(e)+h.LINE_BREAK.length,a&&(a.path||a.readable&&a.hasOwnProperty("httpVersion")||a instanceof l)&&(i.knownLength||this._valuesToMeasure.push(a))},h.prototype._lengthRetriever=function(e,a){e.hasOwnProperty("fd")?void 0!=e.end&&e.end!=1/0&&void 0!=e.start?a(null,e.end+1-(e.start?e.start:0)):c.stat(e.path,function(i,t){if(i){a(i);return}a(null,t.size-(e.start?e.start:0))}):e.hasOwnProperty("httpVersion")?a(null,+e.headers["content-length"]):e.hasOwnProperty("httpModule")?(e.on("response",function(i){e.pause(),a(null,+i.headers["content-length"])}),e.resume()):a("Unknown stream")},h.prototype._multiPartHeader=function(e,a,i){if("string"==typeof i.header)return i.header;var t,n=this._getContentDisposition(a,i),o=this._getContentType(a,i),s="",r={"Content-Disposition":["form-data",'name="'+e+'"'].concat(n||[]),"Content-Type":[].concat(o||[])};for(var p in"object"==typeof i.header&&m(r,i.header),r)if(r.hasOwnProperty(p)){if(null==(t=r[p]))continue;Array.isArray(t)||(t=[t]),t.length&&(s+=p+": "+t.join("; ")+h.LINE_BREAK)}return"--"+this.getBoundary()+h.LINE_BREAK+s+h.LINE_BREAK},h.prototype._getContentDisposition=function(e,a){var i,t;return"string"==typeof a.filepath?i=o.normalize(a.filepath).replace(/\\/g,"/"):a.filename||e.name||e.path?i=o.basename(a.filename||e.name||e.path):e.readable&&e.hasOwnProperty("httpVersion")&&(i=o.basename(e.client._httpMessage.path||"")),i&&(t='filename="'+i+'"'),t},h.prototype._getContentType=function(e,a){var i=a.contentType;return!i&&e.name&&(i=u.lookup(e.name)),!i&&e.path&&(i=u.lookup(e.path)),!i&&e.readable&&e.hasOwnProperty("httpVersion")&&(i=e.headers["content-type"]),!i&&(a.filepath||a.filename)&&(i=u.lookup(a.filepath||a.filename)),i||"object"!=typeof e||(i=h.DEFAULT_CONTENT_TYPE),i},h.prototype._multiPartFooter=function(){return(function(e){var a=h.LINE_BREAK;0===this._streams.length&&(a+=this._lastBoundary()),e(a)}).bind(this)},h.prototype._lastBoundary=function(){return"--"+this.getBoundary()+"--"+h.LINE_BREAK},h.prototype.getHeaders=function(e){var a,i={"content-type":"multipart/form-data; boundary="+this.getBoundary()};for(a in e)e.hasOwnProperty(a)&&(i[a.toLowerCase()]=e[a]);return i},h.prototype.setBoundary=function(e){this._boundary=e},h.prototype.getBoundary=function(){return this._boundary||this._generateBoundary(),this._boundary},h.prototype.getBuffer=function(){for(var e=new Buffer.alloc(0),a=this.getBoundary(),i=0,t=this._streams.length;i<t;i++)"function"!=typeof this._streams[i]&&(Buffer.isBuffer(this._streams[i])?e=Buffer.concat([e,this._streams[i]]):e=Buffer.concat([e,Buffer.from(this._streams[i])]),("string"!=typeof this._streams[i]||this._streams[i].substring(2,a.length+2)!==a)&&(e=Buffer.concat([e,Buffer.from(h.LINE_BREAK)])));return Buffer.concat([e,Buffer.from(this._lastBoundary())])},h.prototype._generateBoundary=function(){for(var e="--------------------------",a=0;a<24;a++)e+=Math.floor(10*Math.random()).toString(16);this._boundary=e},h.prototype.getLengthSync=function(){var e=this._overheadLength+this._valueLength;return this._streams.length&&(e+=this._lastBoundary().length),this.hasKnownLength()||this._error(Error("Cannot calculate proper length in synchronous way.")),e},h.prototype.hasKnownLength=function(){var e=!0;return this._valuesToMeasure.length&&(e=!1),e},h.prototype.getLength=function(e){var a=this._overheadLength+this._valueLength;if(this._streams.length&&(a+=this._lastBoundary().length),!this._valuesToMeasure.length){process.nextTick(e.bind(this,null,a));return}d.parallel(this._valuesToMeasure,this._lengthRetriever,function(i,t){if(i){e(i);return}t.forEach(function(e){a+=e}),e(null,a)})},h.prototype.submit=function(e,a){var i,t,n={method:"post"};return"string"==typeof e?t=m({port:(e=p(e)).port,path:e.pathname,host:e.hostname,protocol:e.protocol},n):(t=m(e,n)).port||(t.port="https:"==t.protocol?443:80),t.headers=this.getHeaders(e.headers),i="https:"==t.protocol?r.request(t):s.request(t),this.getLength((function(e,t){if(e&&"Unknown stream"!==e){this._error(e);return}if(t&&i.setHeader("Content-Length",t),this.pipe(i),a){var n,o=function(e,t){return i.removeListener("error",o),i.removeListener("response",n),a.call(this,e,t)};n=o.bind(this,null),i.on("error",o),i.on("response",n)}}).bind(this)),i},h.prototype._error=function(e){this.error||(this.error=e,this.pause(),this.emit("error",e))},h.prototype.toString=function(){return"[object FormData]"}},9060:e=>{e.exports=function(e,a){return Object.keys(a).forEach(function(i){e[i]=e[i]||a[i]}),e}},12835:(e,a,i)=>{var t=i(21764),n=i(92048),o=i(17702).EventEmitter,s=i(84770);function r(e){for(var a in o.call(this),this.size=0,this.path=null,this.name=null,this.type=null,this.hash=null,this.lastModifiedDate=null,this._writeStream=null,e)this[a]=e[a];"string"==typeof this.hash?this.hash=s.createHash(e.hash):this.hash=null}e.exports=r,t.inherits(r,o),r.prototype.open=function(){this._writeStream=new n.WriteStream(this.path)},r.prototype.toJSON=function(){var e={size:this.size,path:this.path,name:this.name,type:this.type,mtime:this.lastModifiedDate,length:this.length,filename:this.filename,mime:this.mime};return this.hash&&""!=this.hash&&(e.hash=this.hash),e},r.prototype.write=function(e,a){var i=this;if(i.hash&&i.hash.update(e),this._writeStream.closed)return a();this._writeStream.write(e,function(){i.lastModifiedDate=new Date,i.size+=e.length,i.emit("progress",i.size),a()})},r.prototype.end=function(e){var a=this;a.hash&&(a.hash=a.hash.digest("hex")),this._writeStream.end(function(){a.emit("end"),e()})}},27550:(e,a,i)=>{var t=i(84770),n=i(92048),o=i(21764),s=i(55315),r=i(12835),p=i(55138).MultipartParser,c=i(88316).l,l=i(27762).h,u=i(58135).c,d=i(74026).StringDecoder,m=i(17702).EventEmitter,h=i(76162).Stream,f=i(19801);function x(e){return this instanceof x?(m.call(this),e=e||{},this.error=null,this.ended=!1,this.maxFields=e.maxFields||1e3,this.maxFieldsSize=e.maxFieldsSize||20971520,this.maxFileSize=e.maxFileSize||209715200,this.keepExtensions=e.keepExtensions||!1,this.uploadDir=e.uploadDir||f.tmpdir&&f.tmpdir()||f.tmpDir(),this.encoding=e.encoding||"utf-8",this.headers=null,this.type=null,this.hash=e.hash||!1,this.multiples=e.multiples||!1,this.bytesReceived=null,this.bytesExpected=null,this._parser=null,this._flushing=0,this._fieldsSize=0,this._fileSize=0,this.openedFiles=[],this):new x(e)}o.inherits(x,m),a.c=x,x.prototype.parse=function(e,a){if(this.pause=function(){try{e.pause()}catch(e){return this.ended||this._error(e),!1}return!0},this.resume=function(){try{e.resume()}catch(e){return this.ended||this._error(e),!1}return!0},a){var i={},t={};this.on("field",function(e,a){i[e]=a}).on("file",function(e,a){this.multiples&&t[e]?(Array.isArray(t[e])||(t[e]=[t[e]]),t[e].push(a)):t[e]=a}).on("error",function(e){a(e,i,t)}).on("end",function(){a(null,i,t)})}this.writeHeaders(e.headers);var n=this;return e.on("error",function(e){n._error(e)}).on("aborted",function(){n.emit("aborted"),n._error(Error("Request aborted"))}).on("data",function(e){n.write(e)}).on("end",function(){if(!n.error){var e=n._parser.end();e&&n._error(e)}}),this},x.prototype.writeHeaders=function(e){this.headers=e,this._parseContentLength(),this._parseContentType()},x.prototype.write=function(e){if(!this.error){if(!this._parser){this._error(Error("uninitialized parser"));return}if("function"!=typeof this._parser.write){this._error(Error("did not expect data"));return}this.bytesReceived+=e.length,this.emit("progress",this.bytesReceived,this.bytesExpected);var a=this._parser.write(e);return a!==e.length&&this._error(Error("parser error, "+a+" of "+e.length+" bytes parsed")),a}},x.prototype.pause=function(){return!1},x.prototype.resume=function(){return!1},x.prototype.onPart=function(e){this.handlePart(e)},x.prototype.handlePart=function(e){var a=this;if(void 0===e.filename){var i="",t=new d(this.encoding);e.on("data",function(e){if(a._fieldsSize+=e.length,a._fieldsSize>a.maxFieldsSize){a._error(Error("maxFieldsSize exceeded, received "+a._fieldsSize+" bytes of field data"));return}i+=t.write(e)}),e.on("end",function(){a.emit("field",e.name,i)});return}this._flushing++;var n=new r({path:this._uploadPath(e.filename),name:e.filename,type:e.mime,hash:a.hash});this.emit("fileBegin",e.name,n),n.open(),this.openedFiles.push(n),e.on("data",function(e){if(a._fileSize+=e.length,a._fileSize>a.maxFileSize){a._error(Error("maxFileSize exceeded, received "+a._fileSize+" bytes of file data"));return}0!=e.length&&(a.pause(),n.write(e,function(){a.resume()}))}),e.on("end",function(){n.end(function(){a._flushing--,a.emit("file",e.name,n),a._maybeEnd()})})},x.prototype._parseContentType=function(){if(0===this.bytesExpected){var e;this._parser=(e=this,{end:function(){return e.ended=!0,e._maybeEnd(),null}});return}if(!this.headers["content-type"]){this._error(Error("bad content-type header, no content-type"));return}if(this.headers["content-type"].match(/octet-stream/i)){this._initOctetStream();return}if(this.headers["content-type"].match(/urlencoded/i)){this._initUrlencoded();return}if(this.headers["content-type"].match(/multipart/i)){var a=this.headers["content-type"].match(/boundary=(?:"([^"]+)"|([^;]+))/i);a?this._initMultipart(a[1]||a[2]):this._error(Error("bad content-type header, no multipart boundary"));return}if(this.headers["content-type"].match(/json/i)){this._initJSONencoded();return}this._error(Error("bad content-type header, unknown content-type: "+this.headers["content-type"]))},x.prototype._error=function(e){this.error||this.ended||(this.error=e,this.emit("error",e),Array.isArray(this.openedFiles)&&this.openedFiles.forEach(function(e){e._writeStream.on("error",function(){}).destroy(),setTimeout(n.unlink,0,e.path,function(e){})}))},x.prototype._parseContentLength=function(){this.bytesReceived=0,this.headers["content-length"]?this.bytesExpected=parseInt(this.headers["content-length"],10):void 0===this.headers["transfer-encoding"]&&(this.bytesExpected=0),null!==this.bytesExpected&&this.emit("progress",this.bytesReceived,this.bytesExpected)},x.prototype._newParser=function(){return new p},x.prototype._initMultipart=function(e){this.type="multipart";var a,i,t,n=new p,o=this;n.initWithBoundary(e),n.onPartBegin=function(){(t=new h).readable=!0,t.headers={},t.name=null,t.filename=null,t.mime=null,t.transferEncoding="binary",t.transferBuffer="",a="",i=""},n.onHeaderField=function(e,i,t){a+=e.toString(o.encoding,i,t)},n.onHeaderValue=function(e,a,t){i+=e.toString(o.encoding,a,t)},n.onHeaderEnd=function(){a=a.toLowerCase(),t.headers[a]=i;var e=i.match(/\bname=("([^"]*)"|([^\(\)<>@,;:\\"\/\[\]\?=\{\}\s\t/]+))/i);"content-disposition"==a?(e&&(t.name=e[2]||e[3]||""),t.filename=o._fileName(i)):"content-type"==a?t.mime=i:"content-transfer-encoding"==a&&(t.transferEncoding=i.toLowerCase()),a="",i=""},n.onHeadersEnd=function(){switch(t.transferEncoding){case"binary":case"7bit":case"8bit":n.onPartData=function(e,a,i){t.emit("data",e.slice(a,i))},n.onPartEnd=function(){t.emit("end")};break;case"base64":n.onPartData=function(e,a,i){t.transferBuffer+=e.slice(a,i).toString("ascii");var n=4*parseInt(t.transferBuffer.length/4,10);t.emit("data",new Buffer(t.transferBuffer.substring(0,n),"base64")),t.transferBuffer=t.transferBuffer.substring(n)},n.onPartEnd=function(){t.emit("data",new Buffer(t.transferBuffer,"base64")),t.emit("end")};break;default:return o._error(Error("unknown transfer-encoding"))}o.onPart(t)},n.onEnd=function(){o.ended=!0,o._maybeEnd()},this._parser=n},x.prototype._fileName=function(e){var a=e.match(/\bfilename=("(.*?)"|([^\(\)<>@,;:\\"\/\[\]\?=\{\}\s\t/]+))($|;\s)/i);if(a){var i=a[2]||a[3]||"",t=i.substr(i.lastIndexOf("\\")+1);return(t=t.replace(/%22/g,'"')).replace(/&#([\d]{4});/g,function(e,a){return String.fromCharCode(a)})}},x.prototype._initUrlencoded=function(){this.type="urlencoded";var e=new c(this.maxFields),a=this;e.onField=function(e,i){a.emit("field",e,i)},e.onEnd=function(){a.ended=!0,a._maybeEnd()},this._parser=e},x.prototype._initOctetStream=function(){this.type="octet-stream";var e=this.headers["x-file-name"],a=this.headers["content-type"],i=new r({path:this._uploadPath(e),name:e,type:a});this.emit("fileBegin",e,i),i.open(),this.openedFiles.push(i),this._flushing++;var t=this;t._parser=new l;var n=0;t._parser.on("data",function(e){t.pause(),n++,i.write(e,function(){n--,t.resume(),t.ended&&t._parser.emit("doneWritingFile")})}),t._parser.on("end",function(){t._flushing--,t.ended=!0;var e=function(){i.end(function(){t.emit("file","file",i),t._maybeEnd()})};0===n?e():t._parser.once("doneWritingFile",e)})},x.prototype._initJSONencoded=function(){this.type="json";var e=new u(this),a=this;e.onField=function(e,i){a.emit("field",e,i)},e.onEnd=function(){a.ended=!0,a._maybeEnd()},this._parser=e},x.prototype._uploadPath=function(e){var a="upload_"+t.randomBytes(16).toString("hex");if(this.keepExtensions){var i=s.extname(e);a+=i=i.replace(/(\.[a-z0-9]+).*/i,"$1")}return s.join(this.uploadDir,a)},x.prototype._maybeEnd=function(){!this.ended||this._flushing||this.error||this.emit("end")}},39351:(e,a,i)=>{var t=i(27550).c;t.IncomingForm=t,e.exports=t},58135:(e,a,i)=>{var t=i(78893).Buffer;function n(e){this.parent=e,this.chunks=[],this.bytesWritten=0}a.c=n,n.prototype.write=function(e){return this.bytesWritten+=e.length,this.chunks.push(e),e.length},n.prototype.end=function(){try{var e=JSON.parse(t.concat(this.chunks));for(var a in e)this.onField(a,e[a])}catch(e){this.parent.emit("error",e)}this.data=null,this.onEnd()}},55138:(e,a,i)=>{var t=i(78893).Buffer,n=0,o={PARSER_UNINITIALIZED:n++,START:n++,START_BOUNDARY:n++,HEADER_FIELD_START:n++,HEADER_FIELD:n++,HEADER_VALUE_START:n++,HEADER_VALUE:n++,HEADER_VALUE_ALMOST_DONE:n++,HEADERS_ALMOST_DONE:n++,PART_DATA_START:n++,PART_DATA:n++,PART_END:n++,END:n++},s={PART_BOUNDARY:1,LAST_BOUNDARY:2};for(n in o)a[n]=o[n];function r(){this.boundary=null,this.boundaryChars=null,this.lookbehind=null,this.state=o.PARSER_UNINITIALIZED,this.index=null,this.flags=0}a.MultipartParser=r,r.stateToString=function(e){for(var a in o)if(o[a]===e)return a},r.prototype.initWithBoundary=function(e){this.boundary=new t(e.length+4),this.boundary.write("\r\n--",0),this.boundary.write(e,4),this.lookbehind=new t(this.boundary.length+8),this.state=o.START,this.boundaryChars={};for(var a=0;a<this.boundary.length;a++)this.boundaryChars[this.boundary[a]]=!0},r.prototype.write=function(e){var a,i,t=this,n=0,r=e.length,p=this.index,c=this.index,l=this.state,u=this.flags,d=this.lookbehind,m=this.boundary,h=this.boundaryChars,f=this.boundary.length,x=f-1,v=e.length,b=function(e){t[e+"Mark"]=n},g=function(e,a,i,n){if(void 0===i||i!==n){var o="on"+e.substr(0,1).toUpperCase()+e.substr(1);o in t&&t[o](a,i,n)}},y=function(a,i){var o=a+"Mark";o in t&&(i?(g(a,e,t[o],n),delete t[o]):(g(a,e,t[o],e.length),t[o]=0))};for(n=0;n<r;n++)switch(a=e[n],l){case o.PARSER_UNINITIALIZED:return n;case o.START:c=0,l=o.START_BOUNDARY;case o.START_BOUNDARY:if(c==m.length-2){if(45==a)u|=s.LAST_BOUNDARY;else if(13!=a)return n;c++;break}if(c-1==m.length-2){if(u&s.LAST_BOUNDARY&&45==a)g("end"),l=o.END,u=0;else{if(u&s.LAST_BOUNDARY||10!=a)return n;c=0,g("partBegin"),l=o.HEADER_FIELD_START}break}a!=m[c+2]&&(c=-2),a==m[c+2]&&c++;break;case o.HEADER_FIELD_START:l=o.HEADER_FIELD,b("headerField"),c=0;case o.HEADER_FIELD:if(13==a){delete t["headerField"+"Mark"],l=o.HEADERS_ALMOST_DONE;break}if(c++,45==a)break;if(58==a){if(1==c)return n;y("headerField",!0),l=o.HEADER_VALUE_START;break}if((i=32|a)<97||i>122)return n;break;case o.HEADER_VALUE_START:if(32==a)break;b("headerValue"),l=o.HEADER_VALUE;case o.HEADER_VALUE:13==a&&(y("headerValue",!0),g("headerEnd"),l=o.HEADER_VALUE_ALMOST_DONE);break;case o.HEADER_VALUE_ALMOST_DONE:if(10!=a)return n;l=o.HEADER_FIELD_START;break;case o.HEADERS_ALMOST_DONE:if(10!=a)return n;g("headersEnd"),l=o.PART_DATA_START;break;case o.PART_DATA_START:l=o.PART_DATA,b("partData");case o.PART_DATA:if(p=c,0===c){for(n+=x;n<v&&!(e[n]in h);)n+=f;n-=x,a=e[n]}if(c<m.length)m[c]==a?(0===c&&y("partData",!0),c++):c=0;else if(c==m.length)c++,13==a?u|=s.PART_BOUNDARY:45==a?u|=s.LAST_BOUNDARY:c=0;else if(c-1==m.length){if(u&s.PART_BOUNDARY){if(c=0,10==a){u&=~s.PART_BOUNDARY,g("partEnd"),g("partBegin"),l=o.HEADER_FIELD_START;break}}else u&s.LAST_BOUNDARY&&45==a?(g("partEnd"),g("end"),l=o.END,u=0):c=0}c>0?d[c-1]=a:p>0&&(g("partData",d,0,p),p=0,b("partData"),n--);break;case o.END:break;default:return n}return y("headerField"),y("headerValue"),y("partData"),this.index=c,this.state=l,this.flags=u,r},r.prototype.end=function(){var e=function(e,a){var i="on"+a.substr(0,1).toUpperCase()+a.substr(1);i in e&&e[i]()};if(this.state==o.HEADER_FIELD_START&&0===this.index||this.state==o.PART_DATA&&this.index==this.boundary.length)e(this,"partEnd"),e(this,"end");else if(this.state!=o.END)return Error("MultipartParser.end(): stream ended unexpectedly: "+this.explain())},r.prototype.explain=function(){return"state = "+r.stateToString(this.state)}},27762:(e,a,i)=>{var t=i(17702).EventEmitter;function n(e){if(!(this instanceof n))return new n(e);t.call(this)}i(21764).inherits(n,t),a.h=n,n.prototype.write=function(e){return this.emit("data",e),e.length},n.prototype.end=function(){this.emit("end")}},88316:(e,a,i)=>{var t=i(86624);function n(e){this.maxKeys=e,this.buffer=""}a.l=n,n.prototype.write=function(e){return this.buffer+=e.toString("ascii"),e.length},n.prototype.end=function(){var e=t.parse(this.buffer,"&","=",{maxKeys:this.maxKeys});for(var a in e)this.onField(a,e[a]);this.buffer="",this.onEnd()}},72616:e=>{"use strict";e.exports=(e,a=process.argv)=>{let i=e.startsWith("-")?"":1===e.length?"-":"--",t=a.indexOf(i+e),n=a.indexOf("--");return -1!==t&&(-1===n||t<n)}},10862:function(e,a,i){var t,n;n=function(){var e=/^;\s*([^"=]+)=(?:"([^"]+)"|([^";,]+)(?:[;,]|$))/,a=/^<([^>]*)>/,i=/^\s*,\s*/;return{parse:function(t,n){for(var o,s,r,p=n&&n.extended||!1,c=[];t&&(t=t.trim(),s=a.exec(t));){for(var l={link:s[1]},u=(t=t.slice(s[0].length)).match(i);t&&(!u||u.index>0)&&(o=e.exec(t));)u=(t=t.slice(o[0].length)).match(i),"rel"===o[1]||"rev"===o[1]?(r=(o[2]||o[3]).split(/\s+/),l[o[1]]=r):l[o[1]]=o[2]||o[3];c.push(l),t=t.replace(i,"")}return p?c:c.reduce(function(e,a){return a.rel&&a.rel.forEach(function(i){e[i]=a.link}),e},{})},stringify:function(e){var a=Object.keys(e).reduce(function(a,i){return a[e[i]]=a[e[i]]||[],a[e[i]].push(i),a},{});return Object.keys(a).reduce(function(e,i){return e.concat("<"+i+'>; rel="'+a[i].join(" ")+'"')},[]).join(", ")}}},e.exports?e.exports=n():"function"==typeof this.define&&this.define.amd?void 0!==(t=n.call(a,i,a,e))&&(e.exports=t):this.li=n()},98683:(e,a,i)=>{"use strict";var t=i(32615);e.exports=t.METHODS&&t.METHODS.map(function(e){return e.toLowerCase()})||["get","post","put","head","delete","options","trace","copy","lock","mkcol","move","purge","propfind","proppatch","unlock","report","mkactivity","checkout","merge","m-search","notify","subscribe","unsubscribe","patch","search","connect"]},79587:(e,a,i)=>{e.exports=i(2753)},89427:(e,a,i)=>{"use strict";var t=i(79587),n=i(55315).extname,o=/^\s*([^;\s]*)(?:;|\s|$)/,s=/^text\//i;function r(e){if(!e||"string"!=typeof e)return!1;var a=o.exec(e),i=a&&t[a[1].toLowerCase()];return i&&i.charset?i.charset:!!(a&&s.test(a[1]))&&"UTF-8"}a.charset=r,a.charsets={lookup:r},a.contentType=function(e){if(!e||"string"!=typeof e)return!1;var i=-1===e.indexOf("/")?a.lookup(e):e;if(!i)return!1;if(-1===i.indexOf("charset")){var t=a.charset(i);t&&(i+="; charset="+t.toLowerCase())}return i},a.extension=function(e){if(!e||"string"!=typeof e)return!1;var i=o.exec(e),t=i&&a.extensions[i[1].toLowerCase()];return!!t&&!!t.length&&t[0]},a.extensions=Object.create(null),a.lookup=function(e){if(!e||"string"!=typeof e)return!1;var i=n("x."+e).toLowerCase().substr(1);return!!i&&(a.types[i]||!1)},a.types=Object.create(null),function(e,a){var i=["nginx","apache",void 0,"iana"];Object.keys(t).forEach(function(n){var o=t[n],s=o.extensions;if(s&&s.length){e[n]=s;for(var r=0;r<s.length;r++){var p=s[r];if(a[p]){var c=i.indexOf(t[a[p]].source),l=i.indexOf(o.source);if("application/octet-stream"!==a[p]&&(c>l||c===l&&"application/"===a[p].substr(0,12)))continue}a[p]=n}}})}(a.extensions,a.types)},31636:e=>{"use strict";function a(){this._types=Object.create(null),this._extensions=Object.create(null);for(let e=0;e<arguments.length;e++)this.define(arguments[e]);this.define=this.define.bind(this),this.getType=this.getType.bind(this),this.getExtension=this.getExtension.bind(this)}a.prototype.define=function(e,a){for(let i in e){let t=e[i].map(function(e){return e.toLowerCase()});i=i.toLowerCase();for(let e=0;e<t.length;e++){let n=t[e];if("*"!==n[0]){if(!a&&n in this._types)throw Error('Attempt to change mapping for "'+n+'" extension from "'+this._types[n]+'" to "'+i+'". Pass `force=true` to allow this, otherwise remove "'+n+'" from the list of extensions for "'+i+'".');this._types[n]=i}}if(a||!this._extensions[i]){let e=t[0];this._extensions[i]="*"!==e[0]?e:e.substr(1)}}},a.prototype.getType=function(e){let a=(e=String(e)).replace(/^.*[/\\]/,"").toLowerCase(),i=a.replace(/^.*\./,"").toLowerCase(),t=a.length<e.length;return(i.length<a.length-1||!t)&&this._types[i]||null},a.prototype.getExtension=function(e){return(e=/^\s*([^;\s]*)/.test(e)&&RegExp.$1)&&this._extensions[e.toLowerCase()]||null},e.exports=a},40316:(e,a,i)=>{"use strict";let t=i(31636);e.exports=new t(i(14661),i(48374))},48374:e=>{e.exports={"application/prs.cww":["cww"],"application/vnd.1000minds.decision-model+xml":["1km"],"application/vnd.3gpp.pic-bw-large":["plb"],"application/vnd.3gpp.pic-bw-small":["psb"],"application/vnd.3gpp.pic-bw-var":["pvb"],"application/vnd.3gpp2.tcap":["tcap"],"application/vnd.3m.post-it-notes":["pwn"],"application/vnd.accpac.simply.aso":["aso"],"application/vnd.accpac.simply.imp":["imp"],"application/vnd.acucobol":["acu"],"application/vnd.acucorp":["atc","acutc"],"application/vnd.adobe.air-application-installer-package+zip":["air"],"application/vnd.adobe.formscentral.fcdt":["fcdt"],"application/vnd.adobe.fxp":["fxp","fxpl"],"application/vnd.adobe.xdp+xml":["xdp"],"application/vnd.adobe.xfdf":["xfdf"],"application/vnd.ahead.space":["ahead"],"application/vnd.airzip.filesecure.azf":["azf"],"application/vnd.airzip.filesecure.azs":["azs"],"application/vnd.amazon.ebook":["azw"],"application/vnd.americandynamics.acc":["acc"],"application/vnd.amiga.ami":["ami"],"application/vnd.android.package-archive":["apk"],"application/vnd.anser-web-certificate-issue-initiation":["cii"],"application/vnd.anser-web-funds-transfer-initiation":["fti"],"application/vnd.antix.game-component":["atx"],"application/vnd.apple.installer+xml":["mpkg"],"application/vnd.apple.keynote":["key"],"application/vnd.apple.mpegurl":["m3u8"],"application/vnd.apple.numbers":["numbers"],"application/vnd.apple.pages":["pages"],"application/vnd.apple.pkpass":["pkpass"],"application/vnd.aristanetworks.swi":["swi"],"application/vnd.astraea-software.iota":["iota"],"application/vnd.audiograph":["aep"],"application/vnd.balsamiq.bmml+xml":["bmml"],"application/vnd.blueice.multipass":["mpm"],"application/vnd.bmi":["bmi"],"application/vnd.businessobjects":["rep"],"application/vnd.chemdraw+xml":["cdxml"],"application/vnd.chipnuts.karaoke-mmd":["mmd"],"application/vnd.cinderella":["cdy"],"application/vnd.citationstyles.style+xml":["csl"],"application/vnd.claymore":["cla"],"application/vnd.cloanto.rp9":["rp9"],"application/vnd.clonk.c4group":["c4g","c4d","c4f","c4p","c4u"],"application/vnd.cluetrust.cartomobile-config":["c11amc"],"application/vnd.cluetrust.cartomobile-config-pkg":["c11amz"],"application/vnd.commonspace":["csp"],"application/vnd.contact.cmsg":["cdbcmsg"],"application/vnd.cosmocaller":["cmc"],"application/vnd.crick.clicker":["clkx"],"application/vnd.crick.clicker.keyboard":["clkk"],"application/vnd.crick.clicker.palette":["clkp"],"application/vnd.crick.clicker.template":["clkt"],"application/vnd.crick.clicker.wordbank":["clkw"],"application/vnd.criticaltools.wbs+xml":["wbs"],"application/vnd.ctc-posml":["pml"],"application/vnd.cups-ppd":["ppd"],"application/vnd.curl.car":["car"],"application/vnd.curl.pcurl":["pcurl"],"application/vnd.dart":["dart"],"application/vnd.data-vision.rdz":["rdz"],"application/vnd.dbf":["dbf"],"application/vnd.dece.data":["uvf","uvvf","uvd","uvvd"],"application/vnd.dece.ttml+xml":["uvt","uvvt"],"application/vnd.dece.unspecified":["uvx","uvvx"],"application/vnd.dece.zip":["uvz","uvvz"],"application/vnd.denovo.fcselayout-link":["fe_launch"],"application/vnd.dna":["dna"],"application/vnd.dolby.mlp":["mlp"],"application/vnd.dpgraph":["dpg"],"application/vnd.dreamfactory":["dfac"],"application/vnd.ds-keypoint":["kpxx"],"application/vnd.dvb.ait":["ait"],"application/vnd.dvb.service":["svc"],"application/vnd.dynageo":["geo"],"application/vnd.ecowin.chart":["mag"],"application/vnd.enliven":["nml"],"application/vnd.epson.esf":["esf"],"application/vnd.epson.msf":["msf"],"application/vnd.epson.quickanime":["qam"],"application/vnd.epson.salt":["slt"],"application/vnd.epson.ssf":["ssf"],"application/vnd.eszigno3+xml":["es3","et3"],"application/vnd.ezpix-album":["ez2"],"application/vnd.ezpix-package":["ez3"],"application/vnd.fdf":["fdf"],"application/vnd.fdsn.mseed":["mseed"],"application/vnd.fdsn.seed":["seed","dataless"],"application/vnd.flographit":["gph"],"application/vnd.fluxtime.clip":["ftc"],"application/vnd.framemaker":["fm","frame","maker","book"],"application/vnd.frogans.fnc":["fnc"],"application/vnd.frogans.ltf":["ltf"],"application/vnd.fsc.weblaunch":["fsc"],"application/vnd.fujitsu.oasys":["oas"],"application/vnd.fujitsu.oasys2":["oa2"],"application/vnd.fujitsu.oasys3":["oa3"],"application/vnd.fujitsu.oasysgp":["fg5"],"application/vnd.fujitsu.oasysprs":["bh2"],"application/vnd.fujixerox.ddd":["ddd"],"application/vnd.fujixerox.docuworks":["xdw"],"application/vnd.fujixerox.docuworks.binder":["xbd"],"application/vnd.fuzzysheet":["fzs"],"application/vnd.genomatix.tuxedo":["txd"],"application/vnd.geogebra.file":["ggb"],"application/vnd.geogebra.tool":["ggt"],"application/vnd.geometry-explorer":["gex","gre"],"application/vnd.geonext":["gxt"],"application/vnd.geoplan":["g2w"],"application/vnd.geospace":["g3w"],"application/vnd.gmx":["gmx"],"application/vnd.google-apps.document":["gdoc"],"application/vnd.google-apps.presentation":["gslides"],"application/vnd.google-apps.spreadsheet":["gsheet"],"application/vnd.google-earth.kml+xml":["kml"],"application/vnd.google-earth.kmz":["kmz"],"application/vnd.grafeq":["gqf","gqs"],"application/vnd.groove-account":["gac"],"application/vnd.groove-help":["ghf"],"application/vnd.groove-identity-message":["gim"],"application/vnd.groove-injector":["grv"],"application/vnd.groove-tool-message":["gtm"],"application/vnd.groove-tool-template":["tpl"],"application/vnd.groove-vcard":["vcg"],"application/vnd.hal+xml":["hal"],"application/vnd.handheld-entertainment+xml":["zmm"],"application/vnd.hbci":["hbci"],"application/vnd.hhe.lesson-player":["les"],"application/vnd.hp-hpgl":["hpgl"],"application/vnd.hp-hpid":["hpid"],"application/vnd.hp-hps":["hps"],"application/vnd.hp-jlyt":["jlt"],"application/vnd.hp-pcl":["pcl"],"application/vnd.hp-pclxl":["pclxl"],"application/vnd.hydrostatix.sof-data":["sfd-hdstx"],"application/vnd.ibm.minipay":["mpy"],"application/vnd.ibm.modcap":["afp","listafp","list3820"],"application/vnd.ibm.rights-management":["irm"],"application/vnd.ibm.secure-container":["sc"],"application/vnd.iccprofile":["icc","icm"],"application/vnd.igloader":["igl"],"application/vnd.immervision-ivp":["ivp"],"application/vnd.immervision-ivu":["ivu"],"application/vnd.insors.igm":["igm"],"application/vnd.intercon.formnet":["xpw","xpx"],"application/vnd.intergeo":["i2g"],"application/vnd.intu.qbo":["qbo"],"application/vnd.intu.qfx":["qfx"],"application/vnd.ipunplugged.rcprofile":["rcprofile"],"application/vnd.irepository.package+xml":["irp"],"application/vnd.is-xpr":["xpr"],"application/vnd.isac.fcs":["fcs"],"application/vnd.jam":["jam"],"application/vnd.jcp.javame.midlet-rms":["rms"],"application/vnd.jisp":["jisp"],"application/vnd.joost.joda-archive":["joda"],"application/vnd.kahootz":["ktz","ktr"],"application/vnd.kde.karbon":["karbon"],"application/vnd.kde.kchart":["chrt"],"application/vnd.kde.kformula":["kfo"],"application/vnd.kde.kivio":["flw"],"application/vnd.kde.kontour":["kon"],"application/vnd.kde.kpresenter":["kpr","kpt"],"application/vnd.kde.kspread":["ksp"],"application/vnd.kde.kword":["kwd","kwt"],"application/vnd.kenameaapp":["htke"],"application/vnd.kidspiration":["kia"],"application/vnd.kinar":["kne","knp"],"application/vnd.koan":["skp","skd","skt","skm"],"application/vnd.kodak-descriptor":["sse"],"application/vnd.las.las+xml":["lasxml"],"application/vnd.llamagraphics.life-balance.desktop":["lbd"],"application/vnd.llamagraphics.life-balance.exchange+xml":["lbe"],"application/vnd.lotus-1-2-3":["123"],"application/vnd.lotus-approach":["apr"],"application/vnd.lotus-freelance":["pre"],"application/vnd.lotus-notes":["nsf"],"application/vnd.lotus-organizer":["org"],"application/vnd.lotus-screencam":["scm"],"application/vnd.lotus-wordpro":["lwp"],"application/vnd.macports.portpkg":["portpkg"],"application/vnd.mapbox-vector-tile":["mvt"],"application/vnd.mcd":["mcd"],"application/vnd.medcalcdata":["mc1"],"application/vnd.mediastation.cdkey":["cdkey"],"application/vnd.mfer":["mwf"],"application/vnd.mfmp":["mfm"],"application/vnd.micrografx.flo":["flo"],"application/vnd.micrografx.igx":["igx"],"application/vnd.mif":["mif"],"application/vnd.mobius.daf":["daf"],"application/vnd.mobius.dis":["dis"],"application/vnd.mobius.mbk":["mbk"],"application/vnd.mobius.mqy":["mqy"],"application/vnd.mobius.msl":["msl"],"application/vnd.mobius.plc":["plc"],"application/vnd.mobius.txf":["txf"],"application/vnd.mophun.application":["mpn"],"application/vnd.mophun.certificate":["mpc"],"application/vnd.mozilla.xul+xml":["xul"],"application/vnd.ms-artgalry":["cil"],"application/vnd.ms-cab-compressed":["cab"],"application/vnd.ms-excel":["xls","xlm","xla","xlc","xlt","xlw"],"application/vnd.ms-excel.addin.macroenabled.12":["xlam"],"application/vnd.ms-excel.sheet.binary.macroenabled.12":["xlsb"],"application/vnd.ms-excel.sheet.macroenabled.12":["xlsm"],"application/vnd.ms-excel.template.macroenabled.12":["xltm"],"application/vnd.ms-fontobject":["eot"],"application/vnd.ms-htmlhelp":["chm"],"application/vnd.ms-ims":["ims"],"application/vnd.ms-lrm":["lrm"],"application/vnd.ms-officetheme":["thmx"],"application/vnd.ms-outlook":["msg"],"application/vnd.ms-pki.seccat":["cat"],"application/vnd.ms-pki.stl":["*stl"],"application/vnd.ms-powerpoint":["ppt","pps","pot"],"application/vnd.ms-powerpoint.addin.macroenabled.12":["ppam"],"application/vnd.ms-powerpoint.presentation.macroenabled.12":["pptm"],"application/vnd.ms-powerpoint.slide.macroenabled.12":["sldm"],"application/vnd.ms-powerpoint.slideshow.macroenabled.12":["ppsm"],"application/vnd.ms-powerpoint.template.macroenabled.12":["potm"],"application/vnd.ms-project":["mpp","mpt"],"application/vnd.ms-word.document.macroenabled.12":["docm"],"application/vnd.ms-word.template.macroenabled.12":["dotm"],"application/vnd.ms-works":["wps","wks","wcm","wdb"],"application/vnd.ms-wpl":["wpl"],"application/vnd.ms-xpsdocument":["xps"],"application/vnd.mseq":["mseq"],"application/vnd.musician":["mus"],"application/vnd.muvee.style":["msty"],"application/vnd.mynfc":["taglet"],"application/vnd.neurolanguage.nlu":["nlu"],"application/vnd.nitf":["ntf","nitf"],"application/vnd.noblenet-directory":["nnd"],"application/vnd.noblenet-sealer":["nns"],"application/vnd.noblenet-web":["nnw"],"application/vnd.nokia.n-gage.ac+xml":["*ac"],"application/vnd.nokia.n-gage.data":["ngdat"],"application/vnd.nokia.n-gage.symbian.install":["n-gage"],"application/vnd.nokia.radio-preset":["rpst"],"application/vnd.nokia.radio-presets":["rpss"],"application/vnd.novadigm.edm":["edm"],"application/vnd.novadigm.edx":["edx"],"application/vnd.novadigm.ext":["ext"],"application/vnd.oasis.opendocument.chart":["odc"],"application/vnd.oasis.opendocument.chart-template":["otc"],"application/vnd.oasis.opendocument.database":["odb"],"application/vnd.oasis.opendocument.formula":["odf"],"application/vnd.oasis.opendocument.formula-template":["odft"],"application/vnd.oasis.opendocument.graphics":["odg"],"application/vnd.oasis.opendocument.graphics-template":["otg"],"application/vnd.oasis.opendocument.image":["odi"],"application/vnd.oasis.opendocument.image-template":["oti"],"application/vnd.oasis.opendocument.presentation":["odp"],"application/vnd.oasis.opendocument.presentation-template":["otp"],"application/vnd.oasis.opendocument.spreadsheet":["ods"],"application/vnd.oasis.opendocument.spreadsheet-template":["ots"],"application/vnd.oasis.opendocument.text":["odt"],"application/vnd.oasis.opendocument.text-master":["odm"],"application/vnd.oasis.opendocument.text-template":["ott"],"application/vnd.oasis.opendocument.text-web":["oth"],"application/vnd.olpc-sugar":["xo"],"application/vnd.oma.dd2+xml":["dd2"],"application/vnd.openblox.game+xml":["obgx"],"application/vnd.openofficeorg.extension":["oxt"],"application/vnd.openstreetmap.data+xml":["osm"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":["pptx"],"application/vnd.openxmlformats-officedocument.presentationml.slide":["sldx"],"application/vnd.openxmlformats-officedocument.presentationml.slideshow":["ppsx"],"application/vnd.openxmlformats-officedocument.presentationml.template":["potx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":["xlsx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.template":["xltx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":["docx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.template":["dotx"],"application/vnd.osgeo.mapguide.package":["mgp"],"application/vnd.osgi.dp":["dp"],"application/vnd.osgi.subsystem":["esa"],"application/vnd.palm":["pdb","pqa","oprc"],"application/vnd.pawaafile":["paw"],"application/vnd.pg.format":["str"],"application/vnd.pg.osasli":["ei6"],"application/vnd.picsel":["efif"],"application/vnd.pmi.widget":["wg"],"application/vnd.pocketlearn":["plf"],"application/vnd.powerbuilder6":["pbd"],"application/vnd.previewsystems.box":["box"],"application/vnd.proteus.magazine":["mgz"],"application/vnd.publishare-delta-tree":["qps"],"application/vnd.pvi.ptid1":["ptid"],"application/vnd.quark.quarkxpress":["qxd","qxt","qwd","qwt","qxl","qxb"],"application/vnd.rar":["rar"],"application/vnd.realvnc.bed":["bed"],"application/vnd.recordare.musicxml":["mxl"],"application/vnd.recordare.musicxml+xml":["musicxml"],"application/vnd.rig.cryptonote":["cryptonote"],"application/vnd.rim.cod":["cod"],"application/vnd.rn-realmedia":["rm"],"application/vnd.rn-realmedia-vbr":["rmvb"],"application/vnd.route66.link66+xml":["link66"],"application/vnd.sailingtracker.track":["st"],"application/vnd.seemail":["see"],"application/vnd.sema":["sema"],"application/vnd.semd":["semd"],"application/vnd.semf":["semf"],"application/vnd.shana.informed.formdata":["ifm"],"application/vnd.shana.informed.formtemplate":["itp"],"application/vnd.shana.informed.interchange":["iif"],"application/vnd.shana.informed.package":["ipk"],"application/vnd.simtech-mindmapper":["twd","twds"],"application/vnd.smaf":["mmf"],"application/vnd.smart.teacher":["teacher"],"application/vnd.software602.filler.form+xml":["fo"],"application/vnd.solent.sdkm+xml":["sdkm","sdkd"],"application/vnd.spotfire.dxp":["dxp"],"application/vnd.spotfire.sfs":["sfs"],"application/vnd.stardivision.calc":["sdc"],"application/vnd.stardivision.draw":["sda"],"application/vnd.stardivision.impress":["sdd"],"application/vnd.stardivision.math":["smf"],"application/vnd.stardivision.writer":["sdw","vor"],"application/vnd.stardivision.writer-global":["sgl"],"application/vnd.stepmania.package":["smzip"],"application/vnd.stepmania.stepchart":["sm"],"application/vnd.sun.wadl+xml":["wadl"],"application/vnd.sun.xml.calc":["sxc"],"application/vnd.sun.xml.calc.template":["stc"],"application/vnd.sun.xml.draw":["sxd"],"application/vnd.sun.xml.draw.template":["std"],"application/vnd.sun.xml.impress":["sxi"],"application/vnd.sun.xml.impress.template":["sti"],"application/vnd.sun.xml.math":["sxm"],"application/vnd.sun.xml.writer":["sxw"],"application/vnd.sun.xml.writer.global":["sxg"],"application/vnd.sun.xml.writer.template":["stw"],"application/vnd.sus-calendar":["sus","susp"],"application/vnd.svd":["svd"],"application/vnd.symbian.install":["sis","sisx"],"application/vnd.syncml+xml":["xsm"],"application/vnd.syncml.dm+wbxml":["bdm"],"application/vnd.syncml.dm+xml":["xdm"],"application/vnd.syncml.dmddf+xml":["ddf"],"application/vnd.tao.intent-module-archive":["tao"],"application/vnd.tcpdump.pcap":["pcap","cap","dmp"],"application/vnd.tmobile-livetv":["tmo"],"application/vnd.trid.tpt":["tpt"],"application/vnd.triscape.mxs":["mxs"],"application/vnd.trueapp":["tra"],"application/vnd.ufdl":["ufd","ufdl"],"application/vnd.uiq.theme":["utz"],"application/vnd.umajin":["umj"],"application/vnd.unity":["unityweb"],"application/vnd.uoml+xml":["uoml"],"application/vnd.vcx":["vcx"],"application/vnd.visio":["vsd","vst","vss","vsw"],"application/vnd.visionary":["vis"],"application/vnd.vsf":["vsf"],"application/vnd.wap.wbxml":["wbxml"],"application/vnd.wap.wmlc":["wmlc"],"application/vnd.wap.wmlscriptc":["wmlsc"],"application/vnd.webturbo":["wtb"],"application/vnd.wolfram.player":["nbp"],"application/vnd.wordperfect":["wpd"],"application/vnd.wqd":["wqd"],"application/vnd.wt.stf":["stf"],"application/vnd.xara":["xar"],"application/vnd.xfdl":["xfdl"],"application/vnd.yamaha.hv-dic":["hvd"],"application/vnd.yamaha.hv-script":["hvs"],"application/vnd.yamaha.hv-voice":["hvp"],"application/vnd.yamaha.openscoreformat":["osf"],"application/vnd.yamaha.openscoreformat.osfpvg+xml":["osfpvg"],"application/vnd.yamaha.smaf-audio":["saf"],"application/vnd.yamaha.smaf-phrase":["spf"],"application/vnd.yellowriver-custom-menu":["cmp"],"application/vnd.zul":["zir","zirz"],"application/vnd.zzazz.deck+xml":["zaz"],"application/x-7z-compressed":["7z"],"application/x-abiword":["abw"],"application/x-ace-compressed":["ace"],"application/x-apple-diskimage":["*dmg"],"application/x-arj":["arj"],"application/x-authorware-bin":["aab","x32","u32","vox"],"application/x-authorware-map":["aam"],"application/x-authorware-seg":["aas"],"application/x-bcpio":["bcpio"],"application/x-bdoc":["*bdoc"],"application/x-bittorrent":["torrent"],"application/x-blorb":["blb","blorb"],"application/x-bzip":["bz"],"application/x-bzip2":["bz2","boz"],"application/x-cbr":["cbr","cba","cbt","cbz","cb7"],"application/x-cdlink":["vcd"],"application/x-cfs-compressed":["cfs"],"application/x-chat":["chat"],"application/x-chess-pgn":["pgn"],"application/x-chrome-extension":["crx"],"application/x-cocoa":["cco"],"application/x-conference":["nsc"],"application/x-cpio":["cpio"],"application/x-csh":["csh"],"application/x-debian-package":["*deb","udeb"],"application/x-dgc-compressed":["dgc"],"application/x-director":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"],"application/x-doom":["wad"],"application/x-dtbncx+xml":["ncx"],"application/x-dtbook+xml":["dtb"],"application/x-dtbresource+xml":["res"],"application/x-dvi":["dvi"],"application/x-envoy":["evy"],"application/x-eva":["eva"],"application/x-font-bdf":["bdf"],"application/x-font-ghostscript":["gsf"],"application/x-font-linux-psf":["psf"],"application/x-font-pcf":["pcf"],"application/x-font-snf":["snf"],"application/x-font-type1":["pfa","pfb","pfm","afm"],"application/x-freearc":["arc"],"application/x-futuresplash":["spl"],"application/x-gca-compressed":["gca"],"application/x-glulx":["ulx"],"application/x-gnumeric":["gnumeric"],"application/x-gramps-xml":["gramps"],"application/x-gtar":["gtar"],"application/x-hdf":["hdf"],"application/x-httpd-php":["php"],"application/x-install-instructions":["install"],"application/x-iso9660-image":["*iso"],"application/x-iwork-keynote-sffkey":["*key"],"application/x-iwork-numbers-sffnumbers":["*numbers"],"application/x-iwork-pages-sffpages":["*pages"],"application/x-java-archive-diff":["jardiff"],"application/x-java-jnlp-file":["jnlp"],"application/x-keepass2":["kdbx"],"application/x-latex":["latex"],"application/x-lua-bytecode":["luac"],"application/x-lzh-compressed":["lzh","lha"],"application/x-makeself":["run"],"application/x-mie":["mie"],"application/x-mobipocket-ebook":["prc","mobi"],"application/x-ms-application":["application"],"application/x-ms-shortcut":["lnk"],"application/x-ms-wmd":["wmd"],"application/x-ms-wmz":["wmz"],"application/x-ms-xbap":["xbap"],"application/x-msaccess":["mdb"],"application/x-msbinder":["obd"],"application/x-mscardfile":["crd"],"application/x-msclip":["clp"],"application/x-msdos-program":["*exe"],"application/x-msdownload":["*exe","*dll","com","bat","*msi"],"application/x-msmediaview":["mvb","m13","m14"],"application/x-msmetafile":["*wmf","*wmz","*emf","emz"],"application/x-msmoney":["mny"],"application/x-mspublisher":["pub"],"application/x-msschedule":["scd"],"application/x-msterminal":["trm"],"application/x-mswrite":["wri"],"application/x-netcdf":["nc","cdf"],"application/x-ns-proxy-autoconfig":["pac"],"application/x-nzb":["nzb"],"application/x-perl":["pl","pm"],"application/x-pilot":["*prc","*pdb"],"application/x-pkcs12":["p12","pfx"],"application/x-pkcs7-certificates":["p7b","spc"],"application/x-pkcs7-certreqresp":["p7r"],"application/x-rar-compressed":["*rar"],"application/x-redhat-package-manager":["rpm"],"application/x-research-info-systems":["ris"],"application/x-sea":["sea"],"application/x-sh":["sh"],"application/x-shar":["shar"],"application/x-shockwave-flash":["swf"],"application/x-silverlight-app":["xap"],"application/x-sql":["sql"],"application/x-stuffit":["sit"],"application/x-stuffitx":["sitx"],"application/x-subrip":["srt"],"application/x-sv4cpio":["sv4cpio"],"application/x-sv4crc":["sv4crc"],"application/x-t3vm-image":["t3"],"application/x-tads":["gam"],"application/x-tar":["tar"],"application/x-tcl":["tcl","tk"],"application/x-tex":["tex"],"application/x-tex-tfm":["tfm"],"application/x-texinfo":["texinfo","texi"],"application/x-tgif":["*obj"],"application/x-ustar":["ustar"],"application/x-virtualbox-hdd":["hdd"],"application/x-virtualbox-ova":["ova"],"application/x-virtualbox-ovf":["ovf"],"application/x-virtualbox-vbox":["vbox"],"application/x-virtualbox-vbox-extpack":["vbox-extpack"],"application/x-virtualbox-vdi":["vdi"],"application/x-virtualbox-vhd":["vhd"],"application/x-virtualbox-vmdk":["vmdk"],"application/x-wais-source":["src"],"application/x-web-app-manifest+json":["webapp"],"application/x-x509-ca-cert":["der","crt","pem"],"application/x-xfig":["fig"],"application/x-xliff+xml":["*xlf"],"application/x-xpinstall":["xpi"],"application/x-xz":["xz"],"application/x-zmachine":["z1","z2","z3","z4","z5","z6","z7","z8"],"audio/vnd.dece.audio":["uva","uvva"],"audio/vnd.digital-winds":["eol"],"audio/vnd.dra":["dra"],"audio/vnd.dts":["dts"],"audio/vnd.dts.hd":["dtshd"],"audio/vnd.lucent.voice":["lvp"],"audio/vnd.ms-playready.media.pya":["pya"],"audio/vnd.nuera.ecelp4800":["ecelp4800"],"audio/vnd.nuera.ecelp7470":["ecelp7470"],"audio/vnd.nuera.ecelp9600":["ecelp9600"],"audio/vnd.rip":["rip"],"audio/x-aac":["aac"],"audio/x-aiff":["aif","aiff","aifc"],"audio/x-caf":["caf"],"audio/x-flac":["flac"],"audio/x-m4a":["*m4a"],"audio/x-matroska":["mka"],"audio/x-mpegurl":["m3u"],"audio/x-ms-wax":["wax"],"audio/x-ms-wma":["wma"],"audio/x-pn-realaudio":["ram","ra"],"audio/x-pn-realaudio-plugin":["rmp"],"audio/x-realaudio":["*ra"],"audio/x-wav":["*wav"],"chemical/x-cdx":["cdx"],"chemical/x-cif":["cif"],"chemical/x-cmdf":["cmdf"],"chemical/x-cml":["cml"],"chemical/x-csml":["csml"],"chemical/x-xyz":["xyz"],"image/prs.btif":["btif"],"image/prs.pti":["pti"],"image/vnd.adobe.photoshop":["psd"],"image/vnd.airzip.accelerator.azv":["azv"],"image/vnd.dece.graphic":["uvi","uvvi","uvg","uvvg"],"image/vnd.djvu":["djvu","djv"],"image/vnd.dvb.subtitle":["*sub"],"image/vnd.dwg":["dwg"],"image/vnd.dxf":["dxf"],"image/vnd.fastbidsheet":["fbs"],"image/vnd.fpx":["fpx"],"image/vnd.fst":["fst"],"image/vnd.fujixerox.edmics-mmr":["mmr"],"image/vnd.fujixerox.edmics-rlc":["rlc"],"image/vnd.microsoft.icon":["ico"],"image/vnd.ms-dds":["dds"],"image/vnd.ms-modi":["mdi"],"image/vnd.ms-photo":["wdp"],"image/vnd.net-fpx":["npx"],"image/vnd.pco.b16":["b16"],"image/vnd.tencent.tap":["tap"],"image/vnd.valve.source.texture":["vtf"],"image/vnd.wap.wbmp":["wbmp"],"image/vnd.xiff":["xif"],"image/vnd.zbrush.pcx":["pcx"],"image/x-3ds":["3ds"],"image/x-cmu-raster":["ras"],"image/x-cmx":["cmx"],"image/x-freehand":["fh","fhc","fh4","fh5","fh7"],"image/x-icon":["*ico"],"image/x-jng":["jng"],"image/x-mrsid-image":["sid"],"image/x-ms-bmp":["*bmp"],"image/x-pcx":["*pcx"],"image/x-pict":["pic","pct"],"image/x-portable-anymap":["pnm"],"image/x-portable-bitmap":["pbm"],"image/x-portable-graymap":["pgm"],"image/x-portable-pixmap":["ppm"],"image/x-rgb":["rgb"],"image/x-tga":["tga"],"image/x-xbitmap":["xbm"],"image/x-xpixmap":["xpm"],"image/x-xwindowdump":["xwd"],"message/vnd.wfa.wsc":["wsc"],"model/vnd.collada+xml":["dae"],"model/vnd.dwf":["dwf"],"model/vnd.gdl":["gdl"],"model/vnd.gtw":["gtw"],"model/vnd.mts":["mts"],"model/vnd.opengex":["ogex"],"model/vnd.parasolid.transmit.binary":["x_b"],"model/vnd.parasolid.transmit.text":["x_t"],"model/vnd.sap.vds":["vds"],"model/vnd.usdz+zip":["usdz"],"model/vnd.valve.source.compiled-map":["bsp"],"model/vnd.vtu":["vtu"],"text/prs.lines.tag":["dsc"],"text/vnd.curl":["curl"],"text/vnd.curl.dcurl":["dcurl"],"text/vnd.curl.mcurl":["mcurl"],"text/vnd.curl.scurl":["scurl"],"text/vnd.dvb.subtitle":["sub"],"text/vnd.fly":["fly"],"text/vnd.fmi.flexstor":["flx"],"text/vnd.graphviz":["gv"],"text/vnd.in3d.3dml":["3dml"],"text/vnd.in3d.spot":["spot"],"text/vnd.sun.j2me.app-descriptor":["jad"],"text/vnd.wap.wml":["wml"],"text/vnd.wap.wmlscript":["wmls"],"text/x-asm":["s","asm"],"text/x-c":["c","cc","cxx","cpp","h","hh","dic"],"text/x-component":["htc"],"text/x-fortran":["f","for","f77","f90"],"text/x-handlebars-template":["hbs"],"text/x-java-source":["java"],"text/x-lua":["lua"],"text/x-markdown":["mkd"],"text/x-nfo":["nfo"],"text/x-opml":["opml"],"text/x-org":["*org"],"text/x-pascal":["p","pas"],"text/x-processing":["pde"],"text/x-sass":["sass"],"text/x-scss":["scss"],"text/x-setext":["etx"],"text/x-sfv":["sfv"],"text/x-suse-ymp":["ymp"],"text/x-uuencode":["uu"],"text/x-vcalendar":["vcs"],"text/x-vcard":["vcf"],"video/vnd.dece.hd":["uvh","uvvh"],"video/vnd.dece.mobile":["uvm","uvvm"],"video/vnd.dece.pd":["uvp","uvvp"],"video/vnd.dece.sd":["uvs","uvvs"],"video/vnd.dece.video":["uvv","uvvv"],"video/vnd.dvb.file":["dvb"],"video/vnd.fvt":["fvt"],"video/vnd.mpegurl":["mxu","m4u"],"video/vnd.ms-playready.media.pyv":["pyv"],"video/vnd.uvvu.mp4":["uvu","uvvu"],"video/vnd.vivo":["viv"],"video/x-f4v":["f4v"],"video/x-fli":["fli"],"video/x-flv":["flv"],"video/x-m4v":["m4v"],"video/x-matroska":["mkv","mk3d","mks"],"video/x-mng":["mng"],"video/x-ms-asf":["asf","asx"],"video/x-ms-vob":["vob"],"video/x-ms-wm":["wm"],"video/x-ms-wmv":["wmv"],"video/x-ms-wmx":["wmx"],"video/x-ms-wvx":["wvx"],"video/x-msvideo":["avi"],"video/x-sgi-movie":["movie"],"video/x-smv":["smv"],"x-conference/x-cooltalk":["ice"]}},14661:e=>{e.exports={"application/andrew-inset":["ez"],"application/applixware":["aw"],"application/atom+xml":["atom"],"application/atomcat+xml":["atomcat"],"application/atomdeleted+xml":["atomdeleted"],"application/atomsvc+xml":["atomsvc"],"application/atsc-dwd+xml":["dwd"],"application/atsc-held+xml":["held"],"application/atsc-rsat+xml":["rsat"],"application/bdoc":["bdoc"],"application/calendar+xml":["xcs"],"application/ccxml+xml":["ccxml"],"application/cdfx+xml":["cdfx"],"application/cdmi-capability":["cdmia"],"application/cdmi-container":["cdmic"],"application/cdmi-domain":["cdmid"],"application/cdmi-object":["cdmio"],"application/cdmi-queue":["cdmiq"],"application/cu-seeme":["cu"],"application/dash+xml":["mpd"],"application/davmount+xml":["davmount"],"application/docbook+xml":["dbk"],"application/dssc+der":["dssc"],"application/dssc+xml":["xdssc"],"application/ecmascript":["es","ecma"],"application/emma+xml":["emma"],"application/emotionml+xml":["emotionml"],"application/epub+zip":["epub"],"application/exi":["exi"],"application/express":["exp"],"application/fdt+xml":["fdt"],"application/font-tdpfr":["pfr"],"application/geo+json":["geojson"],"application/gml+xml":["gml"],"application/gpx+xml":["gpx"],"application/gxf":["gxf"],"application/gzip":["gz"],"application/hjson":["hjson"],"application/hyperstudio":["stk"],"application/inkml+xml":["ink","inkml"],"application/ipfix":["ipfix"],"application/its+xml":["its"],"application/java-archive":["jar","war","ear"],"application/java-serialized-object":["ser"],"application/java-vm":["class"],"application/javascript":["js","mjs"],"application/json":["json","map"],"application/json5":["json5"],"application/jsonml+json":["jsonml"],"application/ld+json":["jsonld"],"application/lgr+xml":["lgr"],"application/lost+xml":["lostxml"],"application/mac-binhex40":["hqx"],"application/mac-compactpro":["cpt"],"application/mads+xml":["mads"],"application/manifest+json":["webmanifest"],"application/marc":["mrc"],"application/marcxml+xml":["mrcx"],"application/mathematica":["ma","nb","mb"],"application/mathml+xml":["mathml"],"application/mbox":["mbox"],"application/mediaservercontrol+xml":["mscml"],"application/metalink+xml":["metalink"],"application/metalink4+xml":["meta4"],"application/mets+xml":["mets"],"application/mmt-aei+xml":["maei"],"application/mmt-usd+xml":["musd"],"application/mods+xml":["mods"],"application/mp21":["m21","mp21"],"application/mp4":["mp4s","m4p"],"application/msword":["doc","dot"],"application/mxf":["mxf"],"application/n-quads":["nq"],"application/n-triples":["nt"],"application/node":["cjs"],"application/octet-stream":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"],"application/oda":["oda"],"application/oebps-package+xml":["opf"],"application/ogg":["ogx"],"application/omdoc+xml":["omdoc"],"application/onenote":["onetoc","onetoc2","onetmp","onepkg"],"application/oxps":["oxps"],"application/p2p-overlay+xml":["relo"],"application/patch-ops-error+xml":["xer"],"application/pdf":["pdf"],"application/pgp-encrypted":["pgp"],"application/pgp-signature":["asc","sig"],"application/pics-rules":["prf"],"application/pkcs10":["p10"],"application/pkcs7-mime":["p7m","p7c"],"application/pkcs7-signature":["p7s"],"application/pkcs8":["p8"],"application/pkix-attr-cert":["ac"],"application/pkix-cert":["cer"],"application/pkix-crl":["crl"],"application/pkix-pkipath":["pkipath"],"application/pkixcmp":["pki"],"application/pls+xml":["pls"],"application/postscript":["ai","eps","ps"],"application/provenance+xml":["provx"],"application/pskc+xml":["pskcxml"],"application/raml+yaml":["raml"],"application/rdf+xml":["rdf","owl"],"application/reginfo+xml":["rif"],"application/relax-ng-compact-syntax":["rnc"],"application/resource-lists+xml":["rl"],"application/resource-lists-diff+xml":["rld"],"application/rls-services+xml":["rs"],"application/route-apd+xml":["rapd"],"application/route-s-tsid+xml":["sls"],"application/route-usd+xml":["rusd"],"application/rpki-ghostbusters":["gbr"],"application/rpki-manifest":["mft"],"application/rpki-roa":["roa"],"application/rsd+xml":["rsd"],"application/rss+xml":["rss"],"application/rtf":["rtf"],"application/sbml+xml":["sbml"],"application/scvp-cv-request":["scq"],"application/scvp-cv-response":["scs"],"application/scvp-vp-request":["spq"],"application/scvp-vp-response":["spp"],"application/sdp":["sdp"],"application/senml+xml":["senmlx"],"application/sensml+xml":["sensmlx"],"application/set-payment-initiation":["setpay"],"application/set-registration-initiation":["setreg"],"application/shf+xml":["shf"],"application/sieve":["siv","sieve"],"application/smil+xml":["smi","smil"],"application/sparql-query":["rq"],"application/sparql-results+xml":["srx"],"application/srgs":["gram"],"application/srgs+xml":["grxml"],"application/sru+xml":["sru"],"application/ssdl+xml":["ssdl"],"application/ssml+xml":["ssml"],"application/swid+xml":["swidtag"],"application/tei+xml":["tei","teicorpus"],"application/thraud+xml":["tfi"],"application/timestamped-data":["tsd"],"application/toml":["toml"],"application/trig":["trig"],"application/ttml+xml":["ttml"],"application/ubjson":["ubj"],"application/urc-ressheet+xml":["rsheet"],"application/urc-targetdesc+xml":["td"],"application/voicexml+xml":["vxml"],"application/wasm":["wasm"],"application/widget":["wgt"],"application/winhlp":["hlp"],"application/wsdl+xml":["wsdl"],"application/wspolicy+xml":["wspolicy"],"application/xaml+xml":["xaml"],"application/xcap-att+xml":["xav"],"application/xcap-caps+xml":["xca"],"application/xcap-diff+xml":["xdf"],"application/xcap-el+xml":["xel"],"application/xcap-ns+xml":["xns"],"application/xenc+xml":["xenc"],"application/xhtml+xml":["xhtml","xht"],"application/xliff+xml":["xlf"],"application/xml":["xml","xsl","xsd","rng"],"application/xml-dtd":["dtd"],"application/xop+xml":["xop"],"application/xproc+xml":["xpl"],"application/xslt+xml":["*xsl","xslt"],"application/xspf+xml":["xspf"],"application/xv+xml":["mxml","xhvml","xvml","xvm"],"application/yang":["yang"],"application/yin+xml":["yin"],"application/zip":["zip"],"audio/3gpp":["*3gpp"],"audio/adpcm":["adp"],"audio/amr":["amr"],"audio/basic":["au","snd"],"audio/midi":["mid","midi","kar","rmi"],"audio/mobile-xmf":["mxmf"],"audio/mp3":["*mp3"],"audio/mp4":["m4a","mp4a"],"audio/mpeg":["mpga","mp2","mp2a","mp3","m2a","m3a"],"audio/ogg":["oga","ogg","spx","opus"],"audio/s3m":["s3m"],"audio/silk":["sil"],"audio/wav":["wav"],"audio/wave":["*wav"],"audio/webm":["weba"],"audio/xm":["xm"],"font/collection":["ttc"],"font/otf":["otf"],"font/ttf":["ttf"],"font/woff":["woff"],"font/woff2":["woff2"],"image/aces":["exr"],"image/apng":["apng"],"image/avif":["avif"],"image/bmp":["bmp"],"image/cgm":["cgm"],"image/dicom-rle":["drle"],"image/emf":["emf"],"image/fits":["fits"],"image/g3fax":["g3"],"image/gif":["gif"],"image/heic":["heic"],"image/heic-sequence":["heics"],"image/heif":["heif"],"image/heif-sequence":["heifs"],"image/hej2k":["hej2"],"image/hsj2":["hsj2"],"image/ief":["ief"],"image/jls":["jls"],"image/jp2":["jp2","jpg2"],"image/jpeg":["jpeg","jpg","jpe"],"image/jph":["jph"],"image/jphc":["jhc"],"image/jpm":["jpm"],"image/jpx":["jpx","jpf"],"image/jxr":["jxr"],"image/jxra":["jxra"],"image/jxrs":["jxrs"],"image/jxs":["jxs"],"image/jxsc":["jxsc"],"image/jxsi":["jxsi"],"image/jxss":["jxss"],"image/ktx":["ktx"],"image/ktx2":["ktx2"],"image/png":["png"],"image/sgi":["sgi"],"image/svg+xml":["svg","svgz"],"image/t38":["t38"],"image/tiff":["tif","tiff"],"image/tiff-fx":["tfx"],"image/webp":["webp"],"image/wmf":["wmf"],"message/disposition-notification":["disposition-notification"],"message/global":["u8msg"],"message/global-delivery-status":["u8dsn"],"message/global-disposition-notification":["u8mdn"],"message/global-headers":["u8hdr"],"message/rfc822":["eml","mime"],"model/3mf":["3mf"],"model/gltf+json":["gltf"],"model/gltf-binary":["glb"],"model/iges":["igs","iges"],"model/mesh":["msh","mesh","silo"],"model/mtl":["mtl"],"model/obj":["obj"],"model/step+xml":["stpx"],"model/step+zip":["stpz"],"model/step-xml+zip":["stpxz"],"model/stl":["stl"],"model/vrml":["wrl","vrml"],"model/x3d+binary":["*x3db","x3dbz"],"model/x3d+fastinfoset":["x3db"],"model/x3d+vrml":["*x3dv","x3dvz"],"model/x3d+xml":["x3d","x3dz"],"model/x3d-vrml":["x3dv"],"text/cache-manifest":["appcache","manifest"],"text/calendar":["ics","ifb"],"text/coffeescript":["coffee","litcoffee"],"text/css":["css"],"text/csv":["csv"],"text/html":["html","htm","shtml"],"text/jade":["jade"],"text/jsx":["jsx"],"text/less":["less"],"text/markdown":["markdown","md"],"text/mathml":["mml"],"text/mdx":["mdx"],"text/n3":["n3"],"text/plain":["txt","text","conf","def","list","log","in","ini"],"text/richtext":["rtx"],"text/rtf":["*rtf"],"text/sgml":["sgml","sgm"],"text/shex":["shex"],"text/slim":["slim","slm"],"text/spdx":["spdx"],"text/stylus":["stylus","styl"],"text/tab-separated-values":["tsv"],"text/troff":["t","tr","roff","man","me","ms"],"text/turtle":["ttl"],"text/uri-list":["uri","uris","urls"],"text/vcard":["vcard"],"text/vtt":["vtt"],"text/xml":["*xml"],"text/yaml":["yaml","yml"],"video/3gpp":["3gp","3gpp"],"video/3gpp2":["3g2"],"video/h261":["h261"],"video/h263":["h263"],"video/h264":["h264"],"video/iso.segment":["m4s"],"video/jpeg":["jpgv"],"video/jpm":["*jpm","jpgm"],"video/mj2":["mj2","mjp2"],"video/mp2t":["ts"],"video/mp4":["mp4","mp4v","mpg4"],"video/mpeg":["mpeg","mpg","mpe","m1v","m2v"],"video/ogg":["ogv"],"video/quicktime":["qt","mov"],"video/webm":["webm"]}},13974:e=>{function a(e,a,i,t){return Math.round(e/i)+" "+t+(a>=1.5*i?"s":"")}e.exports=function(e,i){i=i||{};var t,n,o=typeof e;if("string"===o&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var a=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(a){var i=parseFloat(a[1]);switch((a[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*i;case"weeks":case"week":case"w":return 6048e5*i;case"days":case"day":case"d":return 864e5*i;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*i;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*i;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*i;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return i;default:return}}}}(e);if("number"===o&&isFinite(e))return i.long?(t=Math.abs(e))>=864e5?a(e,t,864e5,"day"):t>=36e5?a(e,t,36e5,"hour"):t>=6e4?a(e,t,6e4,"minute"):t>=1e3?a(e,t,1e3,"second"):e+" ms":(n=Math.abs(e))>=864e5?Math.round(e/864e5)+"d":n>=36e5?Math.round(e/36e5)+"h":n>=6e4?Math.round(e/6e4)+"m":n>=1e3?Math.round(e/1e3)+"s":e+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},21180:(e,a,i)=>{"use strict";var t=i(17360).parse,n={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},o=String.prototype.endsWith||function(e){return e.length<=this.length&&-1!==this.indexOf(e,this.length-e.length)};function s(e){return process.env[e.toLowerCase()]||process.env[e.toUpperCase()]||""}a.getProxyForUrl=function(e){var a,i,r,p="string"==typeof e?t(e):e||{},c=p.protocol,l=p.host,u=p.port;if("string"!=typeof l||!l||"string"!=typeof c||(c=c.split(":",1)[0],a=l=l.replace(/:\d*$/,""),i=u=parseInt(u)||n[c]||0,!(!(r=(s("npm_config_no_proxy")||s("no_proxy")).toLowerCase())||"*"!==r&&r.split(/[,\s]/).every(function(e){if(!e)return!0;var t=e.match(/^(.+):(\d+)$/),n=t?t[1]:e,s=t?parseInt(t[2]):0;return!!s&&s!==i||(/^[.*]/.test(n)?("*"===n.charAt(0)&&(n=n.slice(1)),!o.call(a,n)):a!==n)}))))return"";var d=s("npm_config_"+c+"_proxy")||s(c+"_proxy")||s("npm_config_proxy")||s("all_proxy");return d&&-1===d.indexOf("://")&&(d=c+"://"+d),d}},18243:(e,a,i)=>{var t=i(78893),n=t.Buffer;function o(e,a){for(var i in e)a[i]=e[i]}function s(e,a,i){return n(e,a,i)}n.from&&n.alloc&&n.allocUnsafe&&n.allocUnsafeSlow?e.exports=t:(o(t,a),a.Buffer=s),s.prototype=Object.create(n.prototype),o(n,s),s.from=function(e,a,i){if("number"==typeof e)throw TypeError("Argument must not be a number");return n(e,a,i)},s.alloc=function(e,a,i){if("number"!=typeof e)throw TypeError("Argument must be a number");var t=n(e);return void 0!==a?"string"==typeof i?t.fill(a,i):t.fill(a):t.fill(0),t},s.allocUnsafe=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return n(e)},s.allocUnsafeSlow=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return t.SlowBuffer(e)}},39229:e=>{function a(){this._defaults=[]}["use","on","once","set","query","type","accept","auth","withCredentials","sortQuery","retry","ok","redirects","timeout","buffer","serialize","parse","ca","key","pfx","cert"].forEach(e=>{a.prototype[e]=function(...a){return this._defaults.push({fn:e,args:a}),this}}),a.prototype._setDefaults=function(e){this._defaults.forEach(a=>{e[a.fn].apply(e,a.args)})},e.exports=a},84546:e=>{"use strict";e.exports=function(e){return null!==e&&"object"==typeof e}},94708:(e,a,i)=>{"use strict";let t=i(11314).CookieJar,n=i(11314).CookieAccessInfo,o=i(17360).parse,s=i(32436),r=i(39229),p=i(98683);function c(e){if(!(this instanceof c))return new c(e);r.call(this),this.jar=new t,e&&(e.ca&&this.ca(e.ca),e.key&&this.key(e.key),e.pfx&&this.pfx(e.pfx),e.cert&&this.cert(e.cert))}e.exports=c,c.prototype=Object.create(r.prototype),c.prototype._saveCookies=function(e){let a=e.headers["set-cookie"];a&&this.jar.setCookies(a)},c.prototype._attachCookies=function(e){let a=o(e.url),i=n(a.hostname,a.pathname,"https:"==a.protocol),t=this.jar.getCookies(i).toValueString();e.cookies=t},p.forEach(e=>{let a=e.toUpperCase();c.prototype[e]=function(e,i){let t=new s.Request(a,e);return t.on("response",this._saveCookies.bind(this)),t.on("redirect",this._saveCookies.bind(this)),t.on("redirect",this._attachCookies.bind(this,t)),this._attachCookies(t),this._setDefaults(t),i&&t.end(i),t}}),c.prototype.del=c.prototype.delete},32278:(e,a,i)=>{"use strict";let t=i(32694),n=i(76162),o=i(21764),s=i(98216),r=i(82452),p=i(17360).parse,{HTTP2_HEADER_PATH:c,HTTP2_HEADER_STATUS:l,HTTP2_HEADER_METHOD:u,HTTP2_HEADER_AUTHORITY:d,HTTP2_HEADER_HOST:m,HTTP2_HEADER_SET_COOKIE:h,NGHTTP2_CANCEL:f}=t.constants;function x(e,a){n.call(this);let i=a.port||("https:"===e?443:80),o=a.host||"localhost";delete a.port,delete a.host,this.method=a.method,this.path=a.path,this.protocol=e,this.host=o,delete a.method,delete a.path;let s=Object.assign({},a);a.socketPath&&(s.socketPath=a.socketPath,s.createConnection=this.createUnixConnection.bind(this)),this._headers={};let r=t.connect(`${e}//${o}:${i}`,s);this.setHeader("host",`${o}:${i}`),r.on("error",e=>this.emit("error",e)),this.session=r}o.inherits(x,n),x.prototype.createUnixConnection=function(e,a){switch(this.protocol){case"http:":return s.connect(a.socketPath);case"https:":return a.ALPNProtocols=["h2"],a.servername=this.host,a.allowHalfOpen=!0,r.connect(a.socketPath,a);default:throw Error("Unsupported protocol",this.protocol)}},x.prototype.setNoDelay=function(e){},x.prototype.getFrame=function(){if(this.frame)return this.frame;let e={[c]:this.path,[u]:this.method},a=this.mapToHttp2Header(this._headers);a=Object.assign(a,e);let i=this.session.request(a);return i.once("response",(e,a)=>{e=this.mapToHttpHeader(e),i.headers=e,i.status=i.statusCode=e[l],this.emit("response",i)}),this._headerSent=!0,i.once("drain",()=>this.emit("drain")),i.on("error",e=>this.emit("error",e)),i.on("close",()=>this.session.close()),this.frame=i,i},x.prototype.mapToHttpHeader=function(e){let a=Object.keys(e),i={};for(var t=0;t<a.length;t++){let n=a[t],o=e[n];(n=n.toLowerCase())===h&&(o=Array.isArray(o)?o:[o]),i[n]=o}return i},x.prototype.mapToHttp2Header=function(e){let a=Object.keys(e),i={};for(var t=0;t<a.length;t++){let n=a[t],o=e[n];(n=n.toLowerCase())===m&&(n=d,o=/^http\:\/\/|^https\:\/\//.test(o)?p(o).host:o),i[n]=o}return i},x.prototype.setHeader=function(e,a){this._headers[e.toLowerCase()]=a},x.prototype.getHeader=function(e){return this._headers[e.toLowerCase()]},x.prototype.write=function(e,a){return this.getFrame().write(e,a)},x.prototype.pipe=function(e,a){return this.getFrame().pipe(e,a)},x.prototype.end=function(e){this.getFrame().end(e)},x.prototype.abort=function(e){this.getFrame().close(f),this.session.destroy()},a.setProtocol=function(e){return{request:function(a){return new x(e,a)}}}},32436:(e,a,i)=>{"use strict";let t;let n=i(19092)("superagent"),o=i(39351),s=i(25626),r=i(47475),p=i(17360).parse,c=i(17360).format,l=i(17360).resolve,u=i(98683),d=i(76162),m=i(55279),h=i(54310).R,f=i(40316),x=i(35240),v=i(32615),b=i(92048),g=i(6684),y=i(71568),w=i(21764),_=i(37236),k=i(71726),E=i(11314);try{t=i(32278)}catch(e){}function T(e,i){return"function"==typeof i?new a.Request("GET",e).end(i):1==arguments.length?new a.Request("GET",e):new a.Request(e,i)}function j(){}function R(e){let a=`node-superagent/${_.version}`;e._header={"user-agent":a},e.header={"User-Agent":a}}function S(e,a){d.call(this),"string"!=typeof a&&(a=c(a)),this._enableHttp2=!!process.env.HTTP2_TEST,this._agent=!1,this._formData=null,this.method=e,this.url=a,R(this),this.writable=!0,this._redirects=0,this.redirects("HEAD"===e?0:5),this.cookies="",this.qs={},this._query=[],this.qsRaw=this._query,this._redirectList=[],this._streamRequest=!1,this.once("end",this.clearTimeout.bind(this))}function C(e){return/[\/+]json($|[^-\w])/.test(e)}function O(e){return~[301,302,303,305,307,308].indexOf(e)}(a=e.exports=T).Request=S,a.agent=i(94708),a.Response=r,f.define({"application/x-www-form-urlencoded":["form","urlencoded","form-data"]},!0),a.protocols={"http:":v,"https:":x,"http2:":t},a.serialize={"application/x-www-form-urlencoded":g.stringify,"application/json":JSON.stringify},a.parse=i(72426),a.buffer={},w.inherits(S,d),k(S.prototype),S.prototype.http2=function(e){if(void 0===a.protocols["http2:"])throw Error("superagent: this version of Node.js does not support http2");return this._enableHttp2=void 0===e||e,this},S.prototype.attach=function(e,a,i){if(a){if(this._data)throw Error("superagent can't mix .send() and .attach()");let t=i||{};"string"==typeof i&&(t={filename:i}),"string"==typeof a?(t.filename||(t.filename=a),n("creating `fs.ReadStream` instance for file: %s",a),a=b.createReadStream(a)):!t.filename&&a.path&&(t.filename=a.path),this._getFormData().append(e,a,t)}return this},S.prototype._getFormData=function(){return this._formData||(this._formData=new s,this._formData.on("error",e=>{this.emit("error",e),this.abort()})),this._formData},S.prototype.agent=function(e){return arguments.length?(this._agent=e,this):this._agent},S.prototype.type=function(e){return this.set("Content-Type",~e.indexOf("/")?e:f.getType(e))},S.prototype.accept=function(e){return this.set("Accept",~e.indexOf("/")?e:f.getType(e))},S.prototype.query=function(e){return"string"==typeof e?this._query.push(e):Object.assign(this.qs,e),this},S.prototype.write=function(e,a){let i=this.request();return this._streamRequest||(this._streamRequest=!0),i.write(e,a)},S.prototype.pipe=function(e,a){return this.piped=!0,this.buffer(!1),this.end(),this._pipeContinue(e,a)},S.prototype._pipeContinue=function(e,a){return this.req.once("response",i=>{if(O(i.statusCode)&&this._redirects++!=this._maxRedirects)return this._redirect(i)._pipeContinue(e,a);if(this.res=i,this._emitResponse(),!this._aborted){if(this._shouldUnzip(i)){let t=y.createUnzip();t.on("error",a=>{if(a&&"Z_BUF_ERROR"===a.code){e.emit("end");return}e.emit("error",a)}),i.pipe(t).pipe(e,a)}else i.pipe(e,a);i.once("end",()=>{this.emit("end")})}}),e},S.prototype.buffer=function(e){return this._buffer=!1!==e,this},S.prototype._redirect=function(e){let a=e.headers.location;if(!a)return this.callback(Error("No location header for redirect"),e);n("redirect %s -> %s",this.url,a),a=l(this.url,a),e.resume();let i=this.req._headers,t=p(a).host!==p(this.url).host;return(301==e.statusCode||302==e.statusCode)&&(i=m.cleanHeader(this.req._headers,t),this.method="HEAD"==this.method?"HEAD":"GET",this._data=null),303==e.statusCode&&(i=m.cleanHeader(this.req._headers,t),this.method="GET",this._data=null),delete i.host,delete this.req,delete this._formData,R(this),this._endCalled=!1,this.url=a,this.qs={},this._query.length=0,this.set(i),this.emit("redirect",e),this._redirectList.push(this.url),this.end(this._callback),this},S.prototype.auth=function(e,a,i){return 1==arguments.length&&(a=""),"object"==typeof a&&null!==a&&(i=a,a=""),i||(i={type:"basic"}),this._auth(e,a,i,e=>new Buffer.from(e).toString("base64"))},S.prototype.ca=function(e){return this._ca=e,this},S.prototype.key=function(e){return this._key=e,this},S.prototype.pfx=function(e){return"object"!=typeof e||Buffer.isBuffer(e)?this._pfx=e:(this._pfx=e.pfx,this._passphrase=e.passphrase),this},S.prototype.cert=function(e){return this._cert=e,this},S.prototype.request=function(){let e;if(this.req)return this.req;let i={};try{let e=g.stringify(this.qs,{indices:!1,strictNullHandling:!0});e&&(this.qs={},this._query.push(e)),this._finalizeQueryString()}catch(e){return this.emit("error",e)}let t=this.url,n=this._retries;if(t.indexOf("`")>-1){let a=t.indexOf("?");-1!==a&&(e=t.substr(a+1).match(/`|\%60/g))}if(0!=t.indexOf("http")&&(t=`http://${t}`),t=p(t),e){let a=0;t.query=t.query.replace(/\%60/g,()=>e[a++]),t.search=`?${t.query}`,t.path=t.pathname+t.search}if(!0===/^https?\+unix:/.test(t.protocol)){t.protocol=`${t.protocol.split("+")[0]}:`;let e=t.path.match(/^([^/]+)(.+)$/);i.socketPath=e[1].replace(/%2F/g,"/"),t.path=e[2]}if(this._connectOverride){let e=t.hostname,a=e in this._connectOverride?this._connectOverride[e]:this._connectOverride["*"];a&&(this._header.host||this.set("host",t.host),t.host=/:/.test(a)?`[${a}]`:a,t.port&&(t.host+=`:${t.port}`),t.hostname=a)}i.method=this.method,i.port=t.port,i.path=t.path,i.host=t.hostname,i.ca=this._ca,i.key=this._key,i.pfx=this._pfx,i.cert=this._cert,i.passphrase=this._passphrase,i.agent=this._agent,this._header.host&&(i.servername=this._header.host.replace(/:[0-9]+$/,"")),this._trustLocalhost&&/^(?:localhost|127\.0\.0\.\d+|(0*:)+:0*1)$/.test(t.hostname)&&(i.rejectUnauthorized=!1);let o=this._enableHttp2?a.protocols["http2:"].setProtocol(t.protocol):a.protocols[t.protocol],s=this.req=o.request(i);if(s.setNoDelay(!0),"HEAD"!=i.method&&s.setHeader("Accept-Encoding","gzip, deflate"),this.protocol=t.protocol,this.host=t.host,s.once("drain",()=>{this.emit("drain")}),s.on("error",e=>{!this._aborted&&this._retries===n&&(this.response||this.callback(e))}),t.auth){let e=t.auth.split(":");this.auth(e[0],e[1])}for(let e in this.username&&this.password&&this.auth(this.username,this.password),this.header)this.header.hasOwnProperty(e)&&s.setHeader(e,this.header[e]);if(this.cookies){if(this._header.hasOwnProperty("cookie")){let e=new E.CookieJar;e.setCookies(this._header.cookie.split(";")),e.setCookies(this.cookies.split(";")),s.setHeader("Cookie",e.getCookies(E.CookieAccessInfo.All).toValueString())}else s.setHeader("Cookie",this.cookies)}return s},S.prototype.callback=function(e,a){if(this._shouldRetry(e,a))return this._retry();let i=this._callback||j;if(this.clearTimeout(),this.called)return console.warn("superagent: double callback bug");if(this.called=!0,!e)try{if(!this._isResponseOK(a)){let i="Unsuccessful HTTP response";a&&(i=v.STATUS_CODES[a.status]||i),(e=Error(i)).status=a?a.status:void 0}}catch(a){e=a}if(!e)return i(null,a);e.response=a,this._maxRetries&&(e.retries=this._retries-1),e&&this.listeners("error").length>0&&this.emit("error",e),i(e,a)},S.prototype._isHost=function(e){return Buffer.isBuffer(e)||e instanceof d||e instanceof s},S.prototype._emitResponse=function(e,a){let i=new r(this);return this.response=i,i.redirects=this._redirectList,void 0!==e&&(i.body=e),i.files=a,this._endCalled&&(i.pipe=function(){throw Error("end() has already been called, so it's too late to start piping")}),this.emit("response",i),i},S.prototype.end=function(e){if(this.request(),n("%s %s",this.method,this.url),this._endCalled)throw Error(".end() was called twice. This is not supported in superagent");this._endCalled=!0,this._callback=e||j,this._end()},S.prototype._end=function(){if(this._aborted)return this.callback(Error("The request has been aborted even before .end() was called"));let e=this._data,i=this.req,t=this.method;if(this._setTimeouts(),"HEAD"!=t&&!i._headerSent){if("string"!=typeof e){let t=i.getHeader("Content-Type");t&&(t=t.split(";")[0]);let n=this._serializer||a.serialize[t];!n&&C(t)&&(n=a.serialize["application/json"]),n&&(e=n(e))}e&&!i.getHeader("Content-Length")&&i.setHeader("Content-Length",Buffer.isBuffer(e)?e.length:Buffer.byteLength(e))}i.once("response",e=>{if(n("%s %s -> %s",this.method,this.url,e.statusCode),this._responseTimeoutTimer&&clearTimeout(this._responseTimeoutTimer),this.piped)return;let t=this._maxRedirects,s=m.type(e.headers["content-type"]||"")||"text/plain",r=s.split("/")[0],p="multipart"==r,c=O(e.statusCode),l=this._responseType;if(this.res=e,c&&this._redirects++!=t)return this._redirect(e);if("HEAD"==this.method){this.emit("end"),this.callback(null,this._emitResponse());return}this._shouldUnzip(e)&&h(i,e);let u=this._buffer;void 0===u&&s in a.buffer&&(u=!!a.buffer[s]);let d=this._parser;if(void 0===u&&d&&(console.warn("A custom superagent parser has been set, but buffering strategy for the parser hasn't been configured. Call `req.buffer(true or false)` or set `superagent.buffer[mime] = true or false`"),u=!0),!d){if(l)d=a.parse.image,u=!0;else if(p){let e=new o.IncomingForm;d=e.parse.bind(e),u=!0}else(function(e){let a=e.split("/")[0];return"image"==a||"video"==a})(s)?(d=a.parse.image,u=!0):a.parse[s]?d=a.parse[s]:"text"==r?(d=a.parse.text,u=!1!==u):C(s)?(d=a.parse["application/json"],u=!1!==u):u?d=a.parse.text:void 0===u&&(d=a.parse.image,u=!0)}(void 0===u&&function(e){let a=e.split("/"),i=a[0],t=a[1];return"text"==i||"x-www-form-urlencoded"==t}(s)||C(s))&&(u=!0),this._resBuffered=u;let f=!1;if(u){let a=this._maxResponseSize||2e8;e.on("data",i=>{if((a-=i.byteLength||i.length)<0){let a=Error("Maximum response size reached");a.code="ETOOLARGE",f=!1,e.destroy(a)}})}if(d)try{f=u,d(e,(e,a,i)=>{if(!this.timedout){if(e&&!this._aborted)return this.callback(e);f&&(this.emit("end"),this.callback(null,this._emitResponse(a,i)))}})}catch(e){this.callback(e);return}if(this.res=e,!u){if(n("unbuffered %s %s",this.method,this.url),this.callback(null,this._emitResponse()),p)return;e.once("end",()=>{n("end %s %s",this.method,this.url),this.emit("end")});return}e.once("error",e=>{f=!1,this.callback(e,null)}),f||e.once("end",()=>{n("end %s %s",this.method,this.url),this.emit("end"),this.callback(null,this._emitResponse())})}),this.emit("request",this);let s=()=>{let e=i.getHeader("Content-Length"),a=0,t=new d.Transform;return t._transform=(i,t,n)=>{a+=i.length,this.emit("progress",{direction:"upload",lengthComputable:!0,loaded:a,total:e}),n(null,i)},t},r=this._formData;if(r){let e=r.getHeaders();for(let a in e)n('setting FormData header: "%s: %s"',a,e[a]),i.setHeader(a,e[a]);r.getLength((e,a)=>{n("got FormData Content-Length: %s",a),"number"==typeof a&&i.setHeader("Content-Length",a),r.pipe(s()).pipe(i)})}else Buffer.isBuffer(e)?(e=>{let a=new d.Readable,i=e.length,t=i%16384,n=i-t;for(let i=0;i<n;i+=16384){let t=e.slice(i,i+16384);a.push(t)}if(t>0){let i=e.slice(-t);a.push(i)}return a.push(null),a})(e).pipe(s()).pipe(i):i.end(e)},S.prototype._shouldUnzip=e=>204!==e.statusCode&&304!==e.statusCode&&"0"!==e.headers["content-length"]&&/^\s*(?:deflate|gzip)\s*$/.test(e.headers["content-encoding"]),S.prototype.connect=function(e){return"string"==typeof e?this._connectOverride={"*":e}:"object"==typeof e?this._connectOverride=e:this._connectOverride=void 0,this},S.prototype.trustLocalhost=function(e){return this._trustLocalhost=void 0===e||e,this},-1==u.indexOf("del")&&(u=u.slice(0)).push("del"),u.forEach(e=>{let a=e;e=(e="del"==e?"delete":e).toUpperCase(),T[a]=(a,i,t)=>{let n=T(e,a);return"function"==typeof i&&(t=i,i=null),i&&("GET"===e||"HEAD"===e?n.query(i):n.send(i)),t&&n.end(t),n}})},75326:e=>{"use strict";e.exports=(e,a)=>{let i=[];e.on("data",e=>{i.push(e)}),e.on("end",()=>{a(null,Buffer.concat(i))})}},72426:(e,a,i)=>{"use strict";a["application/x-www-form-urlencoded"]=i(52647),a["application/json"]=i(88871),a.text=i(24413);let t=i(75326);a["application/octet-stream"]=t,a["application/pdf"]=t,a.image=t},88871:e=>{"use strict";e.exports=function(e,a){e.text="",e.setEncoding("utf8"),e.on("data",a=>{e.text+=a}),e.on("end",()=>{try{var i=e.text&&JSON.parse(e.text)}catch(a){var t=a;t.rawResponse=e.text||null,t.statusCode=e.statusCode}finally{a(t,i)}})}},24413:e=>{"use strict";e.exports=(e,a)=>{e.text="",e.setEncoding("utf8"),e.on("data",a=>{e.text+=a}),e.on("end",a)}},52647:(e,a,i)=>{"use strict";let t=i(6684);e.exports=(e,a)=>{e.text="",e.setEncoding("ascii"),e.on("data",a=>{e.text+=a}),e.on("end",()=>{try{a(null,t.parse(e.text))}catch(e){a(e)}})}},47475:(e,a,i)=>{"use strict";let t=i(21764),n=i(76162),o=i(57755);function s(e){n.call(this);let a=this.res=e.res;this.request=e,this.req=e.req,this.text=a.text,this.body=void 0!==a.body?a.body:{},this.files=a.files||{},this.buffered=e._resBuffered,this.header=this.headers=a.headers,this._setStatusProperties(a.statusCode),this._setHeaderProperties(this.header),this.setEncoding=a.setEncoding.bind(a),a.on("data",this.emit.bind(this,"data")),a.on("end",this.emit.bind(this,"end")),a.on("close",this.emit.bind(this,"close")),a.on("error",this.emit.bind(this,"error"))}e.exports=s,t.inherits(s,n),o(s.prototype),s.prototype.destroy=function(e){this.res.destroy(e)},s.prototype.pause=function(){this.res.pause()},s.prototype.resume=function(){this.res.resume()},s.prototype.toError=function(){let e=this.req,a=e.method,i=e.path,t=Error(`cannot ${a} ${i} (${this.status})`);return t.status=this.status,t.text=this.text,t.method=a,t.path=i,t},s.prototype.setStatusProperties=function(e){return console.warn("In superagent 2.x setStatusProperties is a private method"),this._setStatusProperties(e)},s.prototype.toJSON=function(){return{req:this.request.toJSON(),header:this.header,status:this.status,text:this.text}}},54310:(e,a,i)=>{"use strict";let t=i(74026).StringDecoder,n=i(76162),o=i(71568);a.R=(e,a)=>{let i;let s=o.createUnzip(),r=new n;r.req=e,s.on("error",e=>{if(e&&"Z_BUF_ERROR"===e.code){r.emit("end");return}r.emit("error",e)}),a.pipe(s),a.setEncoding=e=>{i=new t(e)},s.on("data",e=>{if(i){let a=i.write(e);a.length&&r.emit("data",a)}else r.emit("data",e)}),s.on("end",()=>{r.emit("end")});let p=a.on;a.on=function(e,i){return"data"==e||"end"==e?r.on(e,i.bind(a)):("error"==e&&r.on(e,i.bind(a)),p.call(a,e,i)),this}}},71726:(e,a,i)=>{"use strict";let t=i(84546);function n(e){if(e)return function(e){for(let a in n.prototype)e[a]=n.prototype[a];return e}(e)}e.exports=n,n.prototype.clearTimeout=function(){return clearTimeout(this._timer),clearTimeout(this._responseTimeoutTimer),delete this._timer,delete this._responseTimeoutTimer,this},n.prototype.parse=function(e){return this._parser=e,this},n.prototype.responseType=function(e){return this._responseType=e,this},n.prototype.serialize=function(e){return this._serializer=e,this},n.prototype.timeout=function(e){if(!e||"object"!=typeof e)return this._timeout=e,this._responseTimeout=0,this;for(let a in e)switch(a){case"deadline":this._timeout=e.deadline;break;case"response":this._responseTimeout=e.response;break;default:console.warn("Unknown timeout option",a)}return this},n.prototype.retry=function(e,a){return(0==arguments.length||!0===e)&&(e=1),e<=0&&(e=0),this._maxRetries=e,this._retries=0,this._retryCallback=a,this};let o=["ECONNRESET","ETIMEDOUT","EADDRINFO","ESOCKETTIMEDOUT"];n.prototype._shouldRetry=function(e,a){if(!this._maxRetries||this._retries++>=this._maxRetries)return!1;if(this._retryCallback)try{let i=this._retryCallback(e,a);if(!0===i)return!0;if(!1===i)return!1}catch(e){console.error(e)}return!!(a&&a.status&&a.status>=500&&501!=a.status||e&&(e.code&&~o.indexOf(e.code)||e.timeout&&"ECONNABORTED"==e.code||e.crossDomain))},n.prototype._retry=function(){return this.clearTimeout(),this.req&&(this.req=null,this.req=this.request()),this._aborted=!1,this.timedout=!1,this._end()},n.prototype.then=function(e,a){if(!this._fullfilledPromise){let e=this;this._endCalled&&console.warn("Warning: superagent request was sent twice, because both .end() and .then() were called. Never call .end() if you use promises"),this._fullfilledPromise=new Promise((a,i)=>{e.on("error",i),e.on("abort",()=>{let e=Error("Aborted");e.code="ABORTED",e.status=this.status,e.method=this.method,e.url=this.url,i(e)}),e.end((e,t)=>{e?i(e):a(t)})})}return this._fullfilledPromise.then(e,a)},n.prototype.catch=function(e){return this.then(void 0,e)},n.prototype.use=function(e){return e(this),this},n.prototype.ok=function(e){if("function"!=typeof e)throw Error("Callback required");return this._okCallback=e,this},n.prototype._isResponseOK=function(e){return!!e&&(this._okCallback?this._okCallback(e):e.status>=200&&e.status<300)},n.prototype.get=function(e){return this._header[e.toLowerCase()]},n.prototype.getHeader=n.prototype.get,n.prototype.set=function(e,a){if(t(e)){for(let a in e)this.set(a,e[a]);return this}return this._header[e.toLowerCase()]=a,this.header[e]=a,this},n.prototype.unset=function(e){return delete this._header[e.toLowerCase()],delete this.header[e],this},n.prototype.field=function(e,a){if(null==e)throw Error(".field(name, val) name can not be empty");if(this._data)throw Error(".field() can't be used if .send() is used. Please use only .send() or only .field() & .attach()");if(t(e)){for(let a in e)this.field(a,e[a]);return this}if(Array.isArray(a)){for(let i in a)this.field(e,a[i]);return this}if(null==a)throw Error(".field(name, val) val can not be empty");return"boolean"==typeof a&&(a=""+a),this._getFormData().append(e,a),this},n.prototype.abort=function(){return this._aborted||(this._aborted=!0,this.xhr&&this.xhr.abort(),this.req&&this.req.abort(),this.clearTimeout(),this.emit("abort")),this},n.prototype._auth=function(e,a,i,t){switch(i.type){case"basic":this.set("Authorization",`Basic ${t(`${e}:${a}`)}`);break;case"auto":this.username=e,this.password=a;break;case"bearer":this.set("Authorization",`Bearer ${e}`)}return this},n.prototype.withCredentials=function(e){return void 0==e&&(e=!0),this._withCredentials=e,this},n.prototype.redirects=function(e){return this._maxRedirects=e,this},n.prototype.maxResponseSize=function(e){if("number"!=typeof e)throw TypeError("Invalid argument");return this._maxResponseSize=e,this},n.prototype.toJSON=function(){return{method:this.method,url:this.url,data:this._data,headers:this._header}},n.prototype.send=function(e){let a=t(e),i=this._header["content-type"];if(this._formData)throw Error(".send() can't be used if .attach() or .field() is used. Please use only .send() or only .field() & .attach()");if(a&&!this._data)Array.isArray(e)?this._data=[]:this._isHost(e)||(this._data={});else if(e&&this._data&&this._isHost(this._data))throw Error("Can't merge these send calls");if(a&&t(this._data))for(let a in e)this._data[a]=e[a];else"string"==typeof e?(i||this.type("form"),"application/x-www-form-urlencoded"==(i=this._header["content-type"])?this._data=this._data?`${this._data}&${e}`:e:this._data=(this._data||"")+e):this._data=e;return!a||this._isHost(e)||i||this.type("json"),this},n.prototype.sortQuery=function(e){return this._sort=void 0===e||e,this},n.prototype._finalizeQueryString=function(){let e=this._query.join("&");if(e&&(this.url+=(this.url.indexOf("?")>=0?"&":"?")+e),this._query.length=0,this._sort){let e=this.url.indexOf("?");if(e>=0){let a=this.url.substring(e+1).split("&");"function"==typeof this._sort?a.sort(this._sort):a.sort(),this.url=this.url.substring(0,e)+"?"+a.join("&")}}},n.prototype._appendQueryString=()=>{console.trace("Unsupported")},n.prototype._timeoutError=function(e,a,i){if(this._aborted)return;let t=Error(`${e+a}ms exceeded`);t.timeout=a,t.code="ECONNABORTED",t.errno=i,this.timedout=!0,this.abort(),this.callback(t)},n.prototype._setTimeouts=function(){let e=this;this._timeout&&!this._timer&&(this._timer=setTimeout(()=>{e._timeoutError("Timeout of ",e._timeout,"ETIME")},this._timeout)),this._responseTimeout&&!this._responseTimeoutTimer&&(this._responseTimeoutTimer=setTimeout(()=>{e._timeoutError("Response timeout of ",e._responseTimeout,"ETIMEDOUT")},this._responseTimeout))}},57755:(e,a,i)=>{"use strict";let t=i(55279);function n(e){if(e)return function(e){for(let a in n.prototype)e[a]=n.prototype[a];return e}(e)}e.exports=n,n.prototype.get=function(e){return this.header[e.toLowerCase()]},n.prototype._setHeaderProperties=function(e){let a=e["content-type"]||"";this.type=t.type(a);let i=t.params(a);for(let e in i)this[e]=i[e];this.links={};try{e.link&&(this.links=t.parseLinks(e.link))}catch(e){}},n.prototype._setStatusProperties=function(e){let a=e/100|0;this.status=this.statusCode=e,this.statusType=a,this.info=1==a,this.ok=2==a,this.redirect=3==a,this.clientError=4==a,this.serverError=5==a,this.error=(4==a||5==a)&&this.toError(),this.created=201==e,this.accepted=202==e,this.noContent=204==e,this.badRequest=400==e,this.unauthorized=401==e,this.notAcceptable=406==e,this.forbidden=403==e,this.notFound=404==e,this.unprocessableEntity=422==e}},55279:(e,a)=>{"use strict";a.type=e=>e.split(/ *; */).shift(),a.params=e=>e.split(/ *; */).reduce((e,a)=>{let i=a.split(/ *= */),t=i.shift(),n=i.shift();return t&&n&&(e[t]=n),e},{}),a.parseLinks=e=>e.split(/ *, */).reduce((e,a)=>{let i=a.split(/ *; */),t=i[0].slice(1,-1);return e[i[1].split(/ *= */)[1].slice(1,-1)]=t,e},{}),a.cleanHeader=(e,a)=>(delete e["content-type"],delete e["content-length"],delete e["transfer-encoding"],delete e.host,a&&(delete e.authorization,delete e.cookie),e)},25626:(e,a,i)=>{var t=i(59130),n=i(21764),o=i(55315),s=i(32615),r=i(35240),p=i(17360).parse,c=i(92048),l=i(89427),u=i(16811),d=i(82605),m=i(18243).Buffer;function h(e){if(!(this instanceof h))return new h;for(var a in this._overheadLength=0,this._valueLength=0,this._valuesToMeasure=[],t.call(this),e=e||{})this[a]=e[a]}e.exports=h,n.inherits(h,t),h.LINE_BREAK="\r\n",h.DEFAULT_CONTENT_TYPE="application/octet-stream",h.prototype.append=function(e,a,i){"string"==typeof(i=i||{})&&(i={filename:i});var n=t.prototype.append.bind(this);if("number"==typeof a&&(a=""+a),Array.isArray(a)){this._error(Error("Arrays are not supported."));return}var o=this._multiPartHeader(e,a,i),s=this._multiPartFooter();n(o),n(a),n(s),this._trackLength(o,a,i)},h.prototype._trackLength=function(e,a,i){var t=0;null!=i.knownLength?t+=+i.knownLength:m.isBuffer(a)?t=a.length:"string"==typeof a&&(t=m.byteLength(a)),this._valueLength+=t,this._overheadLength+=m.byteLength(e)+h.LINE_BREAK.length,a&&(a.path||a.readable&&a.hasOwnProperty("httpVersion"))&&(i.knownLength||this._valuesToMeasure.push(a))},h.prototype._lengthRetriever=function(e,a){e.hasOwnProperty("fd")?void 0!=e.end&&e.end!=1/0&&void 0!=e.start?a(null,e.end+1-(e.start?e.start:0)):c.stat(e.path,function(i,t){if(i){a(i);return}a(null,t.size-(e.start?e.start:0))}):e.hasOwnProperty("httpVersion")?a(null,+e.headers["content-length"]):e.hasOwnProperty("httpModule")?(e.on("response",function(i){e.pause(),a(null,+i.headers["content-length"])}),e.resume()):a("Unknown stream")},h.prototype._multiPartHeader=function(e,a,i){if("string"==typeof i.header)return i.header;var t,n=this._getContentDisposition(a,i),o=this._getContentType(a,i),s="",r={"Content-Disposition":["form-data",'name="'+e+'"'].concat(n||[]),"Content-Type":[].concat(o||[])};for(var p in"object"==typeof i.header&&d(r,i.header),r)if(r.hasOwnProperty(p)){if(null==(t=r[p]))continue;Array.isArray(t)||(t=[t]),t.length&&(s+=p+": "+t.join("; ")+h.LINE_BREAK)}return"--"+this.getBoundary()+h.LINE_BREAK+s+h.LINE_BREAK},h.prototype._getContentDisposition=function(e,a){var i,t;return"string"==typeof a.filepath?i=o.normalize(a.filepath).replace(/\\/g,"/"):a.filename||e.name||e.path?i=o.basename(a.filename||e.name||e.path):e.readable&&e.hasOwnProperty("httpVersion")&&(i=o.basename(e.client._httpMessage.path||"")),i&&(t='filename="'+i+'"'),t},h.prototype._getContentType=function(e,a){var i=a.contentType;return!i&&e.name&&(i=l.lookup(e.name)),!i&&e.path&&(i=l.lookup(e.path)),!i&&e.readable&&e.hasOwnProperty("httpVersion")&&(i=e.headers["content-type"]),!i&&(a.filepath||a.filename)&&(i=l.lookup(a.filepath||a.filename)),i||"object"!=typeof e||(i=h.DEFAULT_CONTENT_TYPE),i},h.prototype._multiPartFooter=function(){return(function(e){var a=h.LINE_BREAK;0===this._streams.length&&(a+=this._lastBoundary()),e(a)}).bind(this)},h.prototype._lastBoundary=function(){return"--"+this.getBoundary()+"--"+h.LINE_BREAK},h.prototype.getHeaders=function(e){var a,i={"content-type":"multipart/form-data; boundary="+this.getBoundary()};for(a in e)e.hasOwnProperty(a)&&(i[a.toLowerCase()]=e[a]);return i},h.prototype.getBoundary=function(){return this._boundary||this._generateBoundary(),this._boundary},h.prototype.getBuffer=function(){for(var e=new m.alloc(0),a=this.getBoundary(),i=0,t=this._streams.length;i<t;i++)"function"!=typeof this._streams[i]&&(e=m.isBuffer(this._streams[i])?m.concat([e,this._streams[i]]):m.concat([e,m.from(this._streams[i])]),("string"!=typeof this._streams[i]||this._streams[i].substring(2,a.length+2)!==a)&&(e=m.concat([e,m.from(h.LINE_BREAK)])));return m.concat([e,m.from(this._lastBoundary())])},h.prototype._generateBoundary=function(){for(var e="--------------------------",a=0;a<24;a++)e+=Math.floor(10*Math.random()).toString(16);this._boundary=e},h.prototype.getLengthSync=function(){var e=this._overheadLength+this._valueLength;return this._streams.length&&(e+=this._lastBoundary().length),this.hasKnownLength()||this._error(Error("Cannot calculate proper length in synchronous way.")),e},h.prototype.hasKnownLength=function(){var e=!0;return this._valuesToMeasure.length&&(e=!1),e},h.prototype.getLength=function(e){var a=this._overheadLength+this._valueLength;if(this._streams.length&&(a+=this._lastBoundary().length),!this._valuesToMeasure.length){process.nextTick(e.bind(this,null,a));return}u.parallel(this._valuesToMeasure,this._lengthRetriever,function(i,t){if(i){e(i);return}t.forEach(function(e){a+=e}),e(null,a)})},h.prototype.submit=function(e,a){var i,t,n={method:"post"};return"string"==typeof e?t=d({port:(e=p(e)).port,path:e.pathname,host:e.hostname,protocol:e.protocol},n):(t=d(e,n)).port||(t.port="https:"==t.protocol?443:80),t.headers=this.getHeaders(e.headers),i="https:"==t.protocol?r.request(t):s.request(t),this.getLength((function(e,t){if(e){this._error(e);return}i.setHeader("Content-Length",t),this.pipe(i),a&&(i.on("error",a),i.on("response",a.bind(this,null)))}).bind(this)),i},h.prototype._error=function(e){this.error||(this.error=e,this.pause(),this.emit("error",e))},h.prototype.toString=function(){return"[object FormData]"}},82605:e=>{e.exports=function(e,a){return Object.keys(a).forEach(function(i){e[i]=e[i]||a[i]}),e}},67057:(e,a,i)=>{"use strict";let t;let n=i(19801),o=i(74175),s=i(72616),{env:r}=process;function p(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function c(e,a){if(0===t)return 0;if(s("color=16m")||s("color=full")||s("color=truecolor"))return 3;if(s("color=256"))return 2;if(e&&!a&&void 0===t)return 0;let i=t||0;if("dumb"===r.TERM)return i;if("win32"===process.platform){let e=n.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in r)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(e=>e in r)||"codeship"===r.CI_NAME?1:i;if("TEAMCITY_VERSION"in r)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(r.TEAMCITY_VERSION)?1:0;if("truecolor"===r.COLORTERM)return 3;if("TERM_PROGRAM"in r){let e=parseInt((r.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(r.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(r.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(r.TERM)||"COLORTERM"in r?1:i}s("no-color")||s("no-colors")||s("color=false")||s("color=never")?t=0:(s("color")||s("colors")||s("color=true")||s("color=always"))&&(t=1),"FORCE_COLOR"in r&&(t="true"===r.FORCE_COLOR?1:"false"===r.FORCE_COLOR?0:0===r.FORCE_COLOR.length?1:Math.min(parseInt(r.FORCE_COLOR,10),3)),e.exports={supportsColor:function(e){return p(c(e,e&&e.isTTY))},stdout:p(c(!0,o.isatty(1))),stderr:p(c(!0,o.isatty(2)))}},69987:(e,a,i)=>{"use strict";let t=i(10862).parse;e.exports={locateAPIRootHeader:function(e){let a="https://api.w.org/",i=t(e.link||e.headers&&e.headers.link),n=i&&i[a];if(n)return n;throw Error(`No header link found with rel="${a}"`)}}},50917:(e,a,i)=>{"use strict";let t=i(6684),n=i(1755),o=i(80916),s=i(77103),r=i(50307),p=i(88411);function c(e){this._options=["auth","endpoint","headers","username","password","nonce"].reduce((a,i)=>(e&&e[i]&&(a[i]=e[i]),a),{}),this.transport=e&&e.transport,this._params={},this._supportedMethods=["head","get","put","post","delete"],this._path={}}let l=e=>e,u=e=>e?r(e,(e,a,i)=>(null!=a&&""!==a&&(e[i]=a),e),{}):e,d=(e,a)=>{if(!e.reduce((e,i)=>!i.validate||e||i.validate(a),!1))throw Error(["Invalid path component:",a,"does not match"+(e.length>1?" any of":""),e.reduce((e,a)=>e.concat(a.component),[]).join(", ")].join(" "))};c.prototype._renderQuery=function(){var e;let a={...u(this._params)},i=(e=this._taxonomyFilters)?r(e,(e,a,i)=>(e[i]=a.map(e=>(e+"").trim().toLowerCase()).join("+"),e),{}):{};a.filter={...u(this._filters),...i};let n=t.stringify(a,{arrayFormat:"brackets"}).split("&").sort().join("&"),o=/\?/.test(this._options.endpoint)?"&":"?";return""===n?"":o+n},c.prototype._renderPath=function(){this.validatePath();let e=this._path,a=Object.keys(e).sort((e,a)=>parseInt(e,10)-parseInt(a,10)).map(a=>e[a]);return[this._namespace].concat(a).filter(l).join("/")},c.prototype.toString=function(){let e=this._renderPath(),a=this._renderQuery();return this._options.endpoint+e+a},c.prototype.setPathPart=function(e,a){if(this._path[e])throw Error("Cannot overwrite value "+this._path[e]);return this._path[e]=a,this},c.prototype.validatePath=function(){let e=Object.keys(this._path).map(e=>parseInt(e,10)).filter(e=>!isNaN(e)),a=Math.max.apply(null,e),i=[],t=!0;for(let e=0;e<=a;e++)this._levels&&this._levels[e]&&(this._path[e]?(d(this._levels[e],this._path[e]),i.push(this._path[e])):(i.push(" ??? "),t=!1));if(!t)throw Error("Incomplete URL! Missing component: /"+i.join("/"));return this},c.prototype.param=function(e,a){return e&&("string"!=typeof e||void 0!==a)&&("string"==typeof e&&(e=o(e,a)),Object.keys(e).forEach(a=>{let i=e[a];Array.isArray(i)&&(i=p(i).sort(n)),this._params[a]=i})),this},c.prototype.context=s("context"),c.prototype.edit=function(){return this.context("edit")},c.prototype.embed=function(){return this.param("_embed",!0)},c.prototype.page=s("page"),c.prototype.perPage=s("per_page"),c.prototype.offset=s("offset"),c.prototype.order=s("order"),c.prototype.orderby=s("orderby"),c.prototype.search=s("search"),c.prototype.include=s("include"),c.prototype.exclude=s("exclude"),c.prototype.slug=s("slug"),c.prototype.namespace=function(e){return this._namespace=e,this},c.prototype.auth=function(e){return"object"==typeof e&&("string"==typeof e.username&&(this._options.username=e.username),"string"==typeof e.password&&(this._options.password=e.password),e.nonce&&(this._options.nonce=e.nonce)),this._options.auth=!0,this},c.prototype.file=function(e,a){if(global.Buffer&&e instanceof global.Buffer&&!a)throw Error(".file(): File name is a required argument when uploading a Buffer");return this._attachment=e,this._attachmentName=a||void 0,this},c.prototype.setHeaders=function(e,a){return"string"==typeof e&&(e=o(e,a)),this._options.headers={...this._options.headers||{},...e},this},c.prototype.get=function(e){return this.transport.get(this,e)},c.prototype.headers=function(e){return this.transport.head(this,e)},c.prototype.create=function(e,a){return this.transport.post(this,e,a)},c.prototype.update=function(e,a){return this.transport.put(this,e,a)},c.prototype.delete=function(e,a){return this.transport.delete(this,e,a)},c.prototype.then=function(e,a){return this.transport.get(this).then(e,a)},e.exports=c},20134:(e,a,i)=>{"use strict";let t=i(59939).create,n=i(36667).create,o=i(50307);e.exports={generate:function(e){return o(e,(e,a,i)=>(e[i]=o(a,(e,a,o)=>{let s=n(t(a,o),o,i);return e[o]=function(e){return new s({...this._options,...e})},e[o].Ctor=s,e},{}),e),{})}}},36667:(e,a,i)=>{"use strict";let t=i(50917),n=i(67543),o=i(4330);e.exports={create:function(e,a,i){class s extends t{constructor(t){super(t),this._levels=e._levels,this.setPathPart(0,a).namespace(i)}}return"object"==typeof e._getArgs&&Object.keys(e._getArgs).forEach(e=>{let a=n[e];"object"==typeof a&&Object.keys(a).forEach(e=>{o(s.prototype,e,a[e])})}),Object.keys(e._setters).forEach(a=>{s.prototype[a]||(s.prototype[a]=e._setters[a])}),s}}},71711:(e,a,i)=>{"use strict";let t=i(32436),n=i(10862).parse,o=i(50917),s=i(21675),r=i(50307),p=i(99175);function c(e,a){return a.headers?r(a.headers,(e,a,i)=>e.set(i,a),e):e}function l(e,a,i){if(!i&&!a.auth&&!a.nonce)return e;if(a.nonce)return e.set("X-WP-Nonce",a.nonce),e;let t=a.username,n=a.password;return t&&n?e.auth(t,n):e}function u(e,a,i){return new Promise((a,i)=>{e.end((e,t)=>{e||t.error?i(e||t.error):a(t)})}).then(i).then(e=>(a&&"function"==typeof a&&a(null,e),e),e=>{if(e.response&&e.response.body&&e.response.body.code&&(e=e.response.body),a&&"function"==typeof a)a(e);else throw e})}function d(e,a){let i=function(e){let a=e.body;if(p(a)&&"text/html"===e.type)try{a=JSON.parse(e.text)}catch(e){}return a}(a),t=function(e,a,i){let t=null;if(!e.headers||(Object.keys(e.headers).forEach(a=>{e.headers[a.toLowerCase()]=e.headers[a]}),!e.headers["x-wp-totalpages"]))return t;let s=+e.headers["x-wp-totalpages"];if(!s||0===s)return t;let r=e.headers.link?n(e.headers.link):{};return t={total:+e.headers["x-wp-total"],totalPages:s,links:r},r.next&&(t.next=new o({...a,transport:i,endpoint:r.next})),r.prev&&(t.prev=new o({...a,transport:i,endpoint:r.prev})),t}(a,e._options,e.transport);return t&&(i._paging=t),i}function m(e){return e.headers}e.exports={delete:function(e,a,i){i||"function"!=typeof a||(i=a,a=null),s("delete",e);let n=e.toString(),o=l(t.del(n),e._options,!0).send(a);return u(o=c(o,e._options),i,d.bind(null,e))},get:function(e,a){s("get",e);let i=e.toString(),n=l(t.get(i),e._options);return u(n=c(n,e._options),a,d.bind(null,e))},head:function(e,a){s("head",e);let i=e.toString(),n=l(t.head(i),e._options);return u(n=c(n,e._options),a,m)},post:function(e,a,i){s("post",e);let n=e.toString();a=a||{};let o=l(t.post(n),e._options,!0);return o=c(o,e._options),u(o=e._attachment?r(a,(e,a,i)=>e.field(i,a),o.attach("file",e._attachment,e._attachmentName)):o.send(a),i,d.bind(null,e))},put:function(e,a,i){s("put",e);let n=e.toString();a=a||{};let o=l(t.put(n),e._options,!0).send(a);return u(o=c(o,e._options),i,d.bind(null,e))}}},72091:(e,a,i)=>{"use strict";let t=i(1755),n=i(80916),o=i(88411),s={};s.filter=function(e,a){return e&&("string"!=typeof e||void 0!==a)&&("string"==typeof e&&(e=n(e,a)),this._filters={...this._filters,...e}),this},s.taxonomy=function(e,a){let i=Array.isArray(a),n=i?a.reduce((e,a)=>e&&"number"==typeof a,!0):"number"==typeof a,s=i?a.reduce((e,a)=>e&&"string"==typeof a,!0):"string"==typeof a;if(!s&&!n)throw Error("term must be a number, string, or array of numbers or strings");"category"===e?e=s?"category_name":"cat":"post_tag"===e&&(e="tag"),this._taxonomyFilters=this._taxonomyFilters||{};let r=(this._taxonomyFilters[e]||[]).concat(a).sort(t);return this._taxonomyFilters[e]=o(r,!0),this},s.year=function(e){return s.filter.call(this,"year",e)},s.month=function(e){let a;if("string"==typeof e){if(isNaN(a=new Date(Date.parse(e+" 1, 2012"))))return this;e=a.getMonth()+1}return"number"==typeof e?s.filter.call(this,"monthnum",e):this},s.day=function(e){return s.filter.call(this,"day",e)},s.path=function(e){return s.filter.call(this,"pagename",e)},e.exports=s},67543:(e,a,i)=>{"use strict";let t=i(72091),n=i(23353),o={categories:{categories:n.categories,category:n.category},categories_exclude:{excludeCategories:n.excludeCategories},tags:{tags:n.tags,tag:n.tag},tags_exclude:{excludeTags:n.excludeTags},filter:t,post:{post:n.post,forPost:n.post}};["after","author","before","parent","password","status","sticky"].forEach(e=>{o[e]={},o[e][e]=n[e]}),e.exports=o},23353:(e,a,i)=>{"use strict";let t=i(77103),n=i(2876),o={},s=i(72091),r=s.filter,p=s.taxonomy;o.author=function(e){if(void 0===e)return this;if("string"==typeof e)return this.param("author",null),r.call(this,"author_name",e);if("number"==typeof e)return r.call(this,"author_name",null),this.param("author",e);if(null===e)return r.call(this,"author_name",null),this.param("author",null);throw Error("author must be either a nicename string or numeric ID")},o.parent=t("parent"),o.post=t("post"),o.password=t("password"),o.status=t("status"),o.sticky=t("sticky"),o.categories=t("categories"),o.category=function(e){return n(e)?o.categories.call(this,e):p.call(this,"category",e)},o.excludeCategories=t("categories_exclude"),o.tags=t("tags"),o.tag=function(e){return n(e)?o.tags.call(this,e):p.call(this,"tag",e)},o.excludeTags=t("tags_exclude"),o.before=function(e){return this.param("before",new Date(e).toISOString())},o.after=function(e){return this.param("after",new Date(e).toISOString())},e.exports=o},47265:e=>{"use strict";e.exports={create:function(e){let a=e.level,i=e.names[0],t=e.methods||[],n=e.children?Object.keys(e.children).map(a=>e.children[a]).filter(e=>!0===e.namedGroup):[],o=1===n.length&&n[0],s=o&&o.level;return e.namedGroup?function(e){return this.setPathPart(a,e),t.length&&(this._supportedMethods=t),this}:function(e){return this.setPathPart(a,i),void 0!==e&&s&&this.setPathPart(s,e),this}}}},59939:(e,a,i)=>{"use strict";let t=i(47265).create;e.exports={create:function(e,a){let i={_path:{0:a},_levels:{},_setters:{},_getArgs:e._getArgs};return Object.keys(e).forEach(a=>{"_getArgs"!==a&&function e(a,i){var n,o,s;let r;n=a._levels,o=i.level,s={component:i.component,validate:i.validate,methods:i.methods},n[o]=n[o]||[],n[o].push(s),i.level>0&&(r=t(i),i.names.forEach(e=>{let i=e.replace(/[_-]+\w/g,e=>e.replace(/[_-]+/,"").toUpperCase());a._setters[i]||(a._setters[i]=r)})),i.children&&Object.keys(i.children).forEach(t=>{e(a,i.children[t])})}(i,e[a])}),i}}},26307:(e,a,i)=>{"use strict";let t=i(27511).namedGroupRE,n=i(85556),o=i(91221),s=i(50307);function r(e,a,i,n,o,s){let r=n.match(t),p=r&&r[1],c=r&&r[2],l=r?c||p:n,u=r?p:n,d=i[l]||{component:n,namedGroup:!!r,level:o,names:[]};0>d.names.indexOf(u)&&d.names.push(u);let m=""===c?/.*/:RegExp(c?"^"+c+"$":n,"i");return d.validate=e=>m.test(e),s[o+1]?d.children=d.children||{}:(d.methods=(e.methods||[]).map(e=>e.toLowerCase()),d.methods.indexOf("get")>-1&&-1===d.methods.indexOf("head")&&d.methods.push("head"),e.endpoints&&(a._getArgs=a._getArgs||{},e.endpoints.forEach(e=>{e.methods.forEach(i=>{"get"===i.toLowerCase()&&Object.keys(e.args).forEach(i=>{a._getArgs[i]=e.args[i]})})}))),i[l]=d,d.children}function p(e,a,i){let t=a.namespace,s=i.replace("/"+t+"/","").replace(/\/\?$/,""),p=n(s);if(!t||"/"+t===i||!s)return e;o(e,t,{});let c=e[t],l=p[0];o(c,l,{});let u=c[l];return p.reduce(r.bind(null,a,u),u),e}e.exports={build:function(e){return s(e,p,{})}}},1755:e=>{"use strict";e.exports=(e,a)=>e>a?1:e<a?-1:0},4330:e=>{"use strict";e.exports=(e,a,i)=>{"function"!=typeof i||e[a]||(e[a]=i)}},2876:e=>{"use strict";let a=e=>{if("number"==typeof e)return!0;if("string"==typeof e)return/^\d+$/.test(e);if(Array.isArray(e)){for(let i=0;i<e.length;i++)if(!a(e[i]))return!1;return!0}return!1};e.exports=a},21675:e=>{"use strict";e.exports=(e,a)=>{if(-1===a._supportedMethods.indexOf(e.toLowerCase()))throw Error("Unsupported method; supported methods are: "+a._supportedMethods.join(", "));return!0}},91221:e=>{"use strict";e.exports=(e,a,i)=>{e&&void 0===e[a]&&(e[a]=i)}},99175:e=>{"use strict";e.exports=e=>{if("object"!=typeof e||Array.isArray(e))return!1;for(let a in e)if(e.hasOwnProperty(a))return!1;return!0}},80916:e=>{"use strict";e.exports=(e,a)=>{let i={};return i[e]=a,i}},27511:e=>{"use strict";let a="\\(\\?(?:P<|<|')([^>']+)[>']([^\\)]*(\\))?\\??)\\)";e.exports={pattern:a,namedGroupRE:new RegExp(a)}},50307:e=>{"use strict";e.exports=(e,a,i)=>Object.keys(e).reduce((i,t)=>a(i,e[t],t),i)},77103:e=>{"use strict";e.exports=e=>function(a){return this.param(e,a)}},85556:(e,a,i)=>{"use strict";let t=RegExp("([^/]*"+i(27511).pattern.replace(/([^\\])\(([^?])/g,"$1(?:$2")+"[^/]*)");e.exports=e=>e.split(t).reduce((e,a)=>a?t.test(a)?e.concat(a):e.concat(a.split("/").filter(Boolean)):e,[])},88411:e=>{e.exports=e=>Array.from(new Set(e))},58190:(e,a,i)=>{"use strict";let t=i(26307).build,n=i(20134).generate,o=i(77103),s=i(4330),r=i(67543);e.exports=function(e,a,i={}){let p=["head","get","patch","put","post","delete"];Array.isArray(i.methods)?p=i.methods.map(e=>e.trim().toLowerCase()):"string"==typeof i.methods&&(p=[i.methods.trim().toLowerCase()]),-1!==p.indexOf("get")&&-1===p.indexOf("head")?p.push("head"):-1!==p.indexOf("head")&&-1===p.indexOf("get")&&p.push("get");let c=e.replace(/^[\s/]*/,"/").replace(/[\s/]*$/,"/")+a.replace(/^[\s/]*/,""),l={};l[c]={namespace:e,methods:p};let u=n(t(l))[e],d=u[Object.keys(u)[0]].Ctor;function m(e={}){return new d({...e,...this?this._options:{}})}return i&&i.params&&i.params.forEach(e=>{if("string"==typeof e){if("object"==typeof r[e]){Object.keys(r[e]).forEach(a=>{s(d.prototype,a,r[e][a])});return}s(d.prototype,e,o(e))}}),i&&"object"==typeof i.mixins&&Object.keys(i.mixins).forEach(e=>{s(d.prototype,e,i.mixins[e])}),m.Ctor=d,m}},87378:(e,a,i)=>{"use strict";let t;let n=i(50307),o=i(44283),s=i(26307).build,r=i(20134).generate,p=i(69987),c=i(50917),l=i(71711);function u(e){if(this instanceof u==!1)return new u(e);if("string"!=typeof e.endpoint)throw Error("options hash must contain an API endpoint URL string");return this._ns={},this._options={endpoint:e.endpoint.replace(/\/?$/,"/")},e&&(e.username||e.password||e.nonce)&&this.auth(e),this.transport(e.transport).bootstrap(e&&e.routes)}u.prototype.transport=function(e){let a=this._options;return a.transport||(a.transport=Object.create(u.transport)),["get","head","post","put","delete"].forEach(i=>{e&&e[i]&&(a.transport[i]=e[i])}),this},u.transport=Object.create(l),Object.freeze(u.transport),u.site=function(e,a){return new u({endpoint:e,routes:a})},u.prototype.url=function(e){return new c({...this._options,endpoint:e})},u.prototype.root=function(e){e=e||"";let a=new c({...this._options});return a._path={0:e},a},u.prototype.setHeaders=c.prototype.setHeaders,u.prototype.auth=c.prototype.auth,u.prototype.registerRoute=i(58190),u.prototype.bootstrap=function(e){let a;return e?a=r(s(e)):(t||(t=r(s(o))),a=t),n(a,(e,a,i)=>(e._ns[i]=n(a,(e,a,i)=>(e[i]=a,e),e._ns[i]||{_options:e._options}),"wp/v2"===i&&Object.keys(e._ns[i]).forEach(a=>{e[a]=e._ns[i][a]}),e),this)},u.prototype.namespace=function(e){if(!this._ns[e])throw Error("Error: namespace "+e+" is not recognized");return this._ns[e]},u.discover=e=>{let a;let i=u.site(e).root();return i.headers().catch(()=>i.get()).then(p.locateAPIRootHeader).then(e=>(a=e,u.site(e).root().get())).then(e=>new u({endpoint:a,routes:e.routes})).catch(e=>{if(console.error(e),a)return console.warn("Endpoint detected, proceeding despite error..."),console.warn("Binding to "+a+" and assuming default routes"),new u.site(a);throw Error("Autodiscovery failed")})},e.exports=u},29712:(e,a,i)=>{"use strict";let t,n,o,s,r,p,c;i.d(a,{Z:()=>aY});var l,u,d,m={};function h(e,a){return function(){return e.apply(a,arguments)}}i.r(m),i.d(m,{hasBrowserEnv:()=>eb,hasStandardBrowserEnv:()=>ey,hasStandardBrowserWebWorkerEnv:()=>ew,navigator:()=>eg,origin:()=>e_});let{toString:f}=Object.prototype,{getPrototypeOf:x}=Object,v=(t=Object.create(null),e=>{let a=f.call(e);return t[a]||(t[a]=a.slice(8,-1).toLowerCase())}),b=e=>(e=e.toLowerCase(),a=>v(a)===e),g=e=>a=>typeof a===e,{isArray:y}=Array,w=g("undefined"),_=b("ArrayBuffer"),k=g("string"),E=g("function"),T=g("number"),j=e=>null!==e&&"object"==typeof e,R=e=>{if("object"!==v(e))return!1;let a=x(e);return(null===a||a===Object.prototype||null===Object.getPrototypeOf(a))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},S=b("Date"),C=b("File"),O=b("Blob"),A=b("FileList"),z=b("URLSearchParams"),[L,P,q,F]=["ReadableStream","Request","Response","Headers"].map(b);function D(e,a,{allOwnKeys:i=!1}={}){let t,n;if(null!=e){if("object"!=typeof e&&(e=[e]),y(e))for(t=0,n=e.length;t<n;t++)a.call(null,e[t],t,e);else{let n;let o=i?Object.getOwnPropertyNames(e):Object.keys(e),s=o.length;for(t=0;t<s;t++)n=o[t],a.call(null,e[n],n,e)}}}function B(e,a){let i;a=a.toLowerCase();let t=Object.keys(e),n=t.length;for(;n-- >0;)if(a===(i=t[n]).toLowerCase())return i;return null}let U="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,N=e=>!w(e)&&e!==U,I=(n="undefined"!=typeof Uint8Array&&x(Uint8Array),e=>n&&e instanceof n),H=b("HTMLFormElement"),G=(({hasOwnProperty:e})=>(a,i)=>e.call(a,i))(Object.prototype),M=b("RegExp"),$=(e,a)=>{let i=Object.getOwnPropertyDescriptors(e),t={};D(i,(i,n)=>{let o;!1!==(o=a(i,n,e))&&(t[n]=o||i)}),Object.defineProperties(e,t)},V=b("AsyncFunction"),W=(l="function"==typeof setImmediate,u=E(U.postMessage),l?setImmediate:u?(p=`axios@${Math.random()}`,c=[],U.addEventListener("message",({source:e,data:a})=>{e===U&&a===p&&c.length&&c.shift()()},!1),e=>{c.push(e),U.postMessage(p,"*")}):e=>setTimeout(e)),K="undefined"!=typeof queueMicrotask?queueMicrotask.bind(U):"undefined"!=typeof process&&process.nextTick||W,J={isArray:y,isArrayBuffer:_,isBuffer:function(e){return null!==e&&!w(e)&&null!==e.constructor&&!w(e.constructor)&&E(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let a;return e&&("function"==typeof FormData&&e instanceof FormData||E(e.append)&&("formdata"===(a=v(e))||"object"===a&&E(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&_(e.buffer)},isString:k,isNumber:T,isBoolean:e=>!0===e||!1===e,isObject:j,isPlainObject:R,isReadableStream:L,isRequest:P,isResponse:q,isHeaders:F,isUndefined:w,isDate:S,isFile:C,isBlob:O,isRegExp:M,isFunction:E,isStream:e=>j(e)&&E(e.pipe),isURLSearchParams:z,isTypedArray:I,isFileList:A,forEach:D,merge:function e(){let{caseless:a}=N(this)&&this||{},i={},t=(t,n)=>{let o=a&&B(i,n)||n;R(i[o])&&R(t)?i[o]=e(i[o],t):R(t)?i[o]=e({},t):y(t)?i[o]=t.slice():i[o]=t};for(let e=0,a=arguments.length;e<a;e++)arguments[e]&&D(arguments[e],t);return i},extend:(e,a,i,{allOwnKeys:t}={})=>(D(a,(a,t)=>{i&&E(a)?e[t]=h(a,i):e[t]=a},{allOwnKeys:t}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,a,i,t)=>{e.prototype=Object.create(a.prototype,t),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:a.prototype}),i&&Object.assign(e.prototype,i)},toFlatObject:(e,a,i,t)=>{let n,o,s;let r={};if(a=a||{},null==e)return a;do{for(o=(n=Object.getOwnPropertyNames(e)).length;o-- >0;)s=n[o],(!t||t(s,e,a))&&!r[s]&&(a[s]=e[s],r[s]=!0);e=!1!==i&&x(e)}while(e&&(!i||i(e,a))&&e!==Object.prototype);return a},kindOf:v,kindOfTest:b,endsWith:(e,a,i)=>{e=String(e),(void 0===i||i>e.length)&&(i=e.length),i-=a.length;let t=e.indexOf(a,i);return -1!==t&&t===i},toArray:e=>{if(!e)return null;if(y(e))return e;let a=e.length;if(!T(a))return null;let i=Array(a);for(;a-- >0;)i[a]=e[a];return i},forEachEntry:(e,a)=>{let i;let t=(e&&e[Symbol.iterator]).call(e);for(;(i=t.next())&&!i.done;){let t=i.value;a.call(e,t[0],t[1])}},matchAll:(e,a)=>{let i;let t=[];for(;null!==(i=e.exec(a));)t.push(i);return t},isHTMLForm:H,hasOwnProperty:G,hasOwnProp:G,reduceDescriptors:$,freezeMethods:e=>{$(e,(a,i)=>{if(E(e)&&-1!==["arguments","caller","callee"].indexOf(i))return!1;if(E(e[i])){if(a.enumerable=!1,"writable"in a){a.writable=!1;return}a.set||(a.set=()=>{throw Error("Can not rewrite read-only method '"+i+"'")})}})},toObjectSet:(e,a)=>{let i={};return(e=>{e.forEach(e=>{i[e]=!0})})(y(e)?e:String(e).split(a)),i},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,a,i){return a.toUpperCase()+i}),noop:()=>{},toFiniteNumber:(e,a)=>null!=e&&Number.isFinite(e=+e)?e:a,findKey:B,global:U,isContextDefined:N,isSpecCompliantForm:function(e){return!!(e&&E(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])},toJSONObject:e=>{let a=Array(10),i=(e,t)=>{if(j(e)){if(a.indexOf(e)>=0)return;if(!("toJSON"in e)){a[t]=e;let n=y(e)?[]:{};return D(e,(e,a)=>{let o=i(e,t+1);w(o)||(n[a]=o)}),a[t]=void 0,n}}return e};return i(e,0)},isAsyncFn:V,isThenable:e=>e&&(j(e)||E(e))&&E(e.then)&&E(e.catch),setImmediate:W,asap:K};function Y(e,a,i,t,n){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack,this.message=e,this.name="AxiosError",a&&(this.code=a),i&&(this.config=i),t&&(this.request=t),n&&(this.response=n,this.status=n.status?n.status:null)}J.inherits(Y,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:J.toJSONObject(this.config),code:this.code,status:this.status}}});let Q=Y.prototype,Z={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Z[e]={value:e}}),Object.defineProperties(Y,Z),Object.defineProperty(Q,"isAxiosError",{value:!0}),Y.from=(e,a,i,t,n,o)=>{let s=Object.create(Q);return J.toFlatObject(e,s,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),Y.call(s,e.message,a,i,t,n),s.cause=e,s.name=e.name,o&&Object.assign(s,o),s};var X=i(32291);function ee(e){return J.isPlainObject(e)||J.isArray(e)}function ea(e){return J.endsWith(e,"[]")?e.slice(0,-2):e}function ei(e,a,i){return e?e.concat(a).map(function(e,a){return e=ea(e),!i&&a?"["+e+"]":e}).join(i?".":""):a}let et=J.toFlatObject(J,{},null,function(e){return/^is[A-Z]/.test(e)}),en=function(e,a,i){if(!J.isObject(e))throw TypeError("target must be an object");a=a||new(X||FormData);let t=(i=J.toFlatObject(i,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,a){return!J.isUndefined(a[e])})).metaTokens,n=i.visitor||c,o=i.dots,s=i.indexes,r=(i.Blob||"undefined"!=typeof Blob&&Blob)&&J.isSpecCompliantForm(a);if(!J.isFunction(n))throw TypeError("visitor must be a function");function p(e){if(null===e)return"";if(J.isDate(e))return e.toISOString();if(!r&&J.isBlob(e))throw new Y("Blob is not supported. Use a Buffer instead.");return J.isArrayBuffer(e)||J.isTypedArray(e)?r&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function c(e,i,n){let r=e;if(e&&!n&&"object"==typeof e){if(J.endsWith(i,"{}"))i=t?i:i.slice(0,-2),e=JSON.stringify(e);else{var c;if(J.isArray(e)&&(c=e,J.isArray(c)&&!c.some(ee))||(J.isFileList(e)||J.endsWith(i,"[]"))&&(r=J.toArray(e)))return i=ea(i),r.forEach(function(e,t){J.isUndefined(e)||null===e||a.append(!0===s?ei([i],t,o):null===s?i:i+"[]",p(e))}),!1}}return!!ee(e)||(a.append(ei(n,i,o),p(e)),!1)}let l=[],u=Object.assign(et,{defaultVisitor:c,convertValue:p,isVisitable:ee});if(!J.isObject(e))throw TypeError("data must be an object");return function e(i,t){if(!J.isUndefined(i)){if(-1!==l.indexOf(i))throw Error("Circular reference detected in "+t.join("."));l.push(i),J.forEach(i,function(i,o){!0===(!(J.isUndefined(i)||null===i)&&n.call(a,i,J.isString(o)?o.trim():o,t,u))&&e(i,t?t.concat(o):[o])}),l.pop()}}(e),a};function eo(e){let a={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return a[e]})}function es(e,a){this._pairs=[],e&&en(e,this,a)}let er=es.prototype;function ep(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ec(e,a,i){let t;if(!a)return e;let n=i&&i.encode||ep;J.isFunction(i)&&(i={serialize:i});let o=i&&i.serialize;if(t=o?o(a,i):J.isURLSearchParams(a)?a.toString():new es(a,i).toString(n)){let a=e.indexOf("#");-1!==a&&(e=e.slice(0,a)),e+=(-1===e.indexOf("?")?"?":"&")+t}return e}er.append=function(e,a){this._pairs.push([e,a])},er.toString=function(e){let a=e?function(a){return e.call(this,a,eo)}:eo;return this._pairs.map(function(e){return a(e[0])+"="+a(e[1])},"").join("&")};class el{constructor(){this.handlers=[]}use(e,a,i){return this.handlers.push({fulfilled:e,rejected:a,synchronous:!!i&&i.synchronous,runWhen:i?i.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){J.forEach(this.handlers,function(a){null!==a&&e(a)})}}let eu={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1};var ed=i(84770);let em=i(17360).URLSearchParams,eh="abcdefghijklmnopqrstuvwxyz",ef="0123456789",ex={DIGIT:ef,ALPHA:eh,ALPHA_DIGIT:eh+eh.toUpperCase()+ef},ev={isNode:!0,classes:{URLSearchParams:em,FormData:X,Blob:"undefined"!=typeof Blob&&Blob||null},ALPHABET:ex,generateString:(e=16,a=ex.ALPHA_DIGIT)=>{let i="",{length:t}=a,n=new Uint32Array(e);ed.randomFillSync(n);for(let o=0;o<e;o++)i+=a[n[o]%t];return i},protocols:["http","https","file","data"]},eb="undefined"!=typeof window&&"undefined"!=typeof document,eg="object"==typeof navigator&&navigator||void 0,ey=eb&&(!eg||0>["ReactNative","NativeScript","NS"].indexOf(eg.product)),ew="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,e_=eb&&window.location.href||"http://localhost",ek={...m,...ev},eE=function(e){if(J.isFormData(e)&&J.isFunction(e.entries)){let a={};return J.forEachEntry(e,(e,i)=>{!function e(a,i,t,n){let o=a[n++];if("__proto__"===o)return!0;let s=Number.isFinite(+o),r=n>=a.length;return(o=!o&&J.isArray(t)?t.length:o,r)?J.hasOwnProp(t,o)?t[o]=[t[o],i]:t[o]=i:(t[o]&&J.isObject(t[o])||(t[o]=[]),e(a,i,t[o],n)&&J.isArray(t[o])&&(t[o]=function(e){let a,i;let t={},n=Object.keys(e),o=n.length;for(a=0;a<o;a++)t[i=n[a]]=e[i];return t}(t[o]))),!s}(J.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0]),i,a,0)}),a}return null},eT={transitional:eu,adapter:["xhr","http","fetch"],transformRequest:[function(e,a){let i;let t=a.getContentType()||"",n=t.indexOf("application/json")>-1,o=J.isObject(e);if(o&&J.isHTMLForm(e)&&(e=new FormData(e)),J.isFormData(e))return n?JSON.stringify(eE(e)):e;if(J.isArrayBuffer(e)||J.isBuffer(e)||J.isStream(e)||J.isFile(e)||J.isBlob(e)||J.isReadableStream(e))return e;if(J.isArrayBufferView(e))return e.buffer;if(J.isURLSearchParams(e))return a.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();if(o){if(t.indexOf("application/x-www-form-urlencoded")>-1){var s,r;return(s=e,r=this.formSerializer,en(s,new ek.classes.URLSearchParams,Object.assign({visitor:function(e,a,i,t){return ek.isNode&&J.isBuffer(e)?(this.append(a,e.toString("base64")),!1):t.defaultVisitor.apply(this,arguments)}},r))).toString()}if((i=J.isFileList(e))||t.indexOf("multipart/form-data")>-1){let a=this.env&&this.env.FormData;return en(i?{"files[]":e}:e,a&&new a,this.formSerializer)}}return o||n?(a.setContentType("application/json",!1),function(e,a,i){if(J.isString(e))try{return(0,JSON.parse)(e),J.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){let a=this.transitional||eT.transitional,i=a&&a.forcedJSONParsing,t="json"===this.responseType;if(J.isResponse(e)||J.isReadableStream(e))return e;if(e&&J.isString(e)&&(i&&!this.responseType||t)){let i=a&&a.silentJSONParsing;try{return JSON.parse(e)}catch(e){if(!i&&t){if("SyntaxError"===e.name)throw Y.from(e,Y.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ek.classes.FormData,Blob:ek.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};J.forEach(["delete","get","head","post","put","patch"],e=>{eT.headers[e]={}});let ej=J.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),eR=e=>{let a,i,t;let n={};return e&&e.split("\n").forEach(function(e){t=e.indexOf(":"),a=e.substring(0,t).trim().toLowerCase(),i=e.substring(t+1).trim(),!a||n[a]&&ej[a]||("set-cookie"===a?n[a]?n[a].push(i):n[a]=[i]:n[a]=n[a]?n[a]+", "+i:i)}),n},eS=Symbol("internals");function eC(e){return e&&String(e).trim().toLowerCase()}function eO(e){return!1===e||null==e?e:J.isArray(e)?e.map(eO):String(e)}let eA=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function ez(e,a,i,t,n){if(J.isFunction(t))return t.call(this,a,i);if(n&&(a=i),J.isString(a)){if(J.isString(t))return -1!==a.indexOf(t);if(J.isRegExp(t))return t.test(a)}}class eL{constructor(e){e&&this.set(e)}set(e,a,i){let t=this;function n(e,a,i){let n=eC(a);if(!n)throw Error("header name must be a non-empty string");let o=J.findKey(t,n);o&&void 0!==t[o]&&!0!==i&&(void 0!==i||!1===t[o])||(t[o||a]=eO(e))}let o=(e,a)=>J.forEach(e,(e,i)=>n(e,i,a));if(J.isPlainObject(e)||e instanceof this.constructor)o(e,a);else if(J.isString(e)&&(e=e.trim())&&!eA(e))o(eR(e),a);else if(J.isHeaders(e))for(let[a,t]of e.entries())n(t,a,i);else null!=e&&n(a,e,i);return this}get(e,a){if(e=eC(e)){let i=J.findKey(this,e);if(i){let e=this[i];if(!a)return e;if(!0===a)return function(e){let a;let i=Object.create(null),t=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;for(;a=t.exec(e);)i[a[1]]=a[2];return i}(e);if(J.isFunction(a))return a.call(this,e,i);if(J.isRegExp(a))return a.exec(e);throw TypeError("parser must be boolean|regexp|function")}}}has(e,a){if(e=eC(e)){let i=J.findKey(this,e);return!!(i&&void 0!==this[i]&&(!a||ez(this,this[i],i,a)))}return!1}delete(e,a){let i=this,t=!1;function n(e){if(e=eC(e)){let n=J.findKey(i,e);n&&(!a||ez(i,i[n],n,a))&&(delete i[n],t=!0)}}return J.isArray(e)?e.forEach(n):n(e),t}clear(e){let a=Object.keys(this),i=a.length,t=!1;for(;i--;){let n=a[i];(!e||ez(this,this[n],n,e,!0))&&(delete this[n],t=!0)}return t}normalize(e){let a=this,i={};return J.forEach(this,(t,n)=>{let o=J.findKey(i,n);if(o){a[o]=eO(t),delete a[n];return}let s=e?n.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,a,i)=>a.toUpperCase()+i):String(n).trim();s!==n&&delete a[n],a[s]=eO(t),i[s]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let a=Object.create(null);return J.forEach(this,(i,t)=>{null!=i&&!1!==i&&(a[t]=e&&J.isArray(i)?i.join(", "):i)}),a}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,a])=>e+": "+a).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...a){let i=new this(e);return a.forEach(e=>i.set(e)),i}static accessor(e){let a=(this[eS]=this[eS]={accessors:{}}).accessors,i=this.prototype;function t(e){let t=eC(e);a[t]||(function(e,a){let i=J.toCamelCase(" "+a);["get","set","has"].forEach(t=>{Object.defineProperty(e,t+i,{value:function(e,i,n){return this[t].call(this,a,e,i,n)},configurable:!0})})}(i,e),a[t]=!0)}return J.isArray(e)?e.forEach(t):t(e),this}}function eP(e,a){let i=this||eT,t=a||i,n=eL.from(t.headers),o=t.data;return J.forEach(e,function(e){o=e.call(i,o,n.normalize(),a?a.status:void 0)}),n.normalize(),o}function eq(e){return!!(e&&e.__CANCEL__)}function eF(e,a,i){Y.call(this,null==e?"canceled":e,Y.ERR_CANCELED,a,i),this.name="CanceledError"}function eD(e,a,i){let t=i.config.validateStatus;!i.status||!t||t(i.status)?e(i):a(new Y("Request failed with status code "+i.status,[Y.ERR_BAD_REQUEST,Y.ERR_BAD_RESPONSE][Math.floor(i.status/100)-4],i.config,i.request,i))}function eB(e,a,i){let t=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(a);return e&&t||!1==i?a?e.replace(/\/?\/$/,"")+"/"+a.replace(/^\/+/,""):e:a}eL.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),J.reduceDescriptors(eL.prototype,({value:e},a)=>{let i=a[0].toUpperCase()+a.slice(1);return{get:()=>e,set(e){this[i]=e}}}),J.freezeMethods(eL),J.inherits(eF,Y,{__CANCEL__:!0});var eU=i(21180),eN=i(32615),eI=i(35240),eH=i(21764),eG=i(86875),eM=i(71568);let e$="1.8.3";function eV(e){let a=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return a&&a[1]||""}let eW=/^(?:([^;]+);)?(?:[^;]+;)?(base64|),([\s\S]*)$/;var eK=i(76162);let eJ=Symbol("internals");class eY extends eK.Transform{constructor(e){super({readableHighWaterMark:(e=J.toFlatObject(e,{maxRate:0,chunkSize:65536,minChunkSize:100,timeWindow:500,ticksRate:2,samplesCount:15},null,(e,a)=>!J.isUndefined(a[e]))).chunkSize});let a=this[eJ]={timeWindow:e.timeWindow,chunkSize:e.chunkSize,maxRate:e.maxRate,minChunkSize:e.minChunkSize,bytesSeen:0,isCaptured:!1,notifiedBytesLoaded:0,ts:Date.now(),bytes:0,onReadCallback:null};this.on("newListener",e=>{"progress"!==e||a.isCaptured||(a.isCaptured=!0)})}_read(e){let a=this[eJ];return a.onReadCallback&&a.onReadCallback(),super._read(e)}_transform(e,a,i){let t=this[eJ],n=t.maxRate,o=this.readableHighWaterMark,s=t.timeWindow,r=n/(1e3/s),p=!1!==t.minChunkSize?Math.max(t.minChunkSize,.01*r):0,c=(e,a)=>{let i=Buffer.byteLength(e);t.bytesSeen+=i,t.bytes+=i,t.isCaptured&&this.emit("progress",t.bytesSeen),this.push(e)?process.nextTick(a):t.onReadCallback=()=>{t.onReadCallback=null,process.nextTick(a)}},l=(e,a)=>{let i;let l=Buffer.byteLength(e),u=null,d=o,m=0;if(n){let e=Date.now();(!t.ts||(m=e-t.ts)>=s)&&(t.ts=e,i=r-t.bytes,t.bytes=i<0?-i:0,m=0),i=r-t.bytes}if(n){if(i<=0)return setTimeout(()=>{a(null,e)},s-m);i<d&&(d=i)}d&&l>d&&l-d>p&&(u=e.subarray(d),e=e.subarray(0,d)),c(e,u?()=>{process.nextTick(a,null,u)}:a)};l(e,function e(a,t){if(a)return i(a);t?l(t,e):i(null)})}}var eQ=i(17702);let{asyncIterator:eZ}=Symbol,eX=async function*(e){e.stream?yield*e.stream():e.arrayBuffer?yield await e.arrayBuffer():e[eZ]?yield*e[eZ]():yield e},e0=ek.ALPHABET.ALPHA_DIGIT+"-_",e1="function"==typeof TextEncoder?new TextEncoder:new eH.TextEncoder,e2=e1.encode("\r\n");class e3{constructor(e,a){let{escapeName:i}=this.constructor,t=J.isString(a),n=`Content-Disposition: form-data; name="${i(e)}"${!t&&a.name?`; filename="${i(a.name)}"`:""}\r
`;t?a=e1.encode(String(a).replace(/\r?\n|\r\n?/g,"\r\n")):n+=`Content-Type: ${a.type||"application/octet-stream"}\r
`,this.headers=e1.encode(n+"\r\n"),this.contentLength=t?a.byteLength:a.size,this.size=this.headers.byteLength+this.contentLength+2,this.name=e,this.value=a}async *encode(){yield this.headers;let{value:e}=this;J.isTypedArray(e)?yield e:yield*eX(e),yield e2}static escapeName(e){return String(e).replace(/[\r\n"]/g,e=>({"\r":"%0D","\n":"%0A",'"':"%22"})[e])}}let e6=(e,a,i)=>{let{tag:t="form-data-boundary",size:n=25,boundary:o=t+"-"+ek.generateString(n,e0)}=i||{};if(!J.isFormData(e))throw TypeError("FormData instance required");if(o.length<1||o.length>70)throw Error("boundary must be 10-70 characters long");let s=e1.encode("--"+o+"\r\n"),r=e1.encode("--"+o+"--\r\n\r\n"),p=r.byteLength,c=Array.from(e.entries()).map(([e,a])=>{let i=new e3(e,a);return p+=i.size,i});p+=s.byteLength*c.length;let l={"Content-Type":`multipart/form-data; boundary=${o}`};return Number.isFinite(p=J.toFiniteNumber(p))&&(l["Content-Length"]=p),a&&a(l),eK.Readable.from(async function*(){for(let e of c)yield s,yield*e.encode();yield r}())};class e4 extends eK.Transform{__transform(e,a,i){this.push(e),i()}_transform(e,a,i){if(0!==e.length&&(this._transform=this.__transform,120!==e[0])){let e=Buffer.alloc(2);e[0]=120,e[1]=156,this.push(e,a)}this.__transform(e,a,i)}}let e7=(e,a)=>J.isAsyncFn(e)?function(...i){let t=i.pop();e.apply(this,i).then(e=>{try{a?t(null,...a(e)):t(null,e)}catch(e){t(e)}},t)}:e,e5=function(e,a){let i;let t=Array(e=e||10),n=Array(e),o=0,s=0;return a=void 0!==a?a:1e3,function(r){let p=Date.now(),c=n[s];i||(i=p),t[o]=r,n[o]=p;let l=s,u=0;for(;l!==o;)u+=t[l++],l%=e;if((o=(o+1)%e)===s&&(s=(s+1)%e),p-i<a)return;let d=c&&p-c;return d?Math.round(1e3*u/d):void 0}},e8=function(e,a){let i,t,n=0,o=1e3/a,s=(a,o=Date.now())=>{n=o,i=null,t&&(clearTimeout(t),t=null),e.apply(null,a)};return[(...e)=>{let a=Date.now(),r=a-n;r>=o?s(e,a):(i=e,t||(t=setTimeout(()=>{t=null,s(i)},o-r)))},()=>i&&s(i)]},e9=(e,a,i=3)=>{let t=0,n=e5(50,250);return e8(i=>{let o=i.loaded,s=i.lengthComputable?i.total:void 0,r=o-t,p=n(r);t=o,e({loaded:o,total:s,progress:s?o/s:void 0,bytes:r,rate:p||void 0,estimated:p&&s&&o<=s?(s-o)/p:void 0,event:i,lengthComputable:null!=s,[a?"download":"upload"]:!0})},i)},ae=(e,a)=>{let i=null!=e;return[t=>a[0]({lengthComputable:i,total:e,loaded:t}),a[1]]},aa=e=>(...a)=>J.asap(()=>e(...a)),ai={flush:eM.constants.Z_SYNC_FLUSH,finishFlush:eM.constants.Z_SYNC_FLUSH},at={flush:eM.constants.BROTLI_OPERATION_FLUSH,finishFlush:eM.constants.BROTLI_OPERATION_FLUSH},an=J.isFunction(eM.createBrotliDecompress),{http:ao,https:as}=eG,ar=/https:?/,ap=ek.protocols.map(e=>e+":"),ac=(e,[a,i])=>(e.on("end",i).on("error",i),a);function al(e,a){e.beforeRedirects.proxy&&e.beforeRedirects.proxy(e),e.beforeRedirects.config&&e.beforeRedirects.config(e,a)}let au="undefined"!=typeof process&&"process"===J.kindOf(process),ad=e=>new Promise((a,i)=>{let t,n;let o=(e,a)=>{!n&&(n=!0,t&&t(e,a))},s=e=>{o(e,!0),i(e)};e(e=>{o(e),a(e)},s,e=>t=e).catch(s)}),am=({address:e,family:a})=>{if(!J.isString(e))throw TypeError("address must be a string");return{address:e,family:a||(0>e.indexOf(".")?6:4)}},ah=(e,a)=>am(J.isObject(e)?e:{address:e,family:a}),af=au&&function(e){return ad(async function(a,i,t){let n,o,s,r,p,c,l,{data:u,lookup:d,family:m}=e,{responseType:h,responseEncoding:f}=e,x=e.method.toUpperCase(),v=!1;if(d){let e=e7(d,e=>J.isArray(e)?e:[e]);d=(a,i,t)=>{e(a,i,(e,a,n)=>{if(e)return t(e);let o=J.isArray(a)?a.map(e=>ah(e)):[ah(a,n)];i.all?t(e,o):t(e,o[0].address,o[0].family)})}}let b=new eQ.EventEmitter,g=()=>{e.cancelToken&&e.cancelToken.unsubscribe(y),e.signal&&e.signal.removeEventListener("abort",y),b.removeAllListeners()};function y(a){b.emit("abort",!a||a.type?new eF(null,e,p):a)}t((e,a)=>{r=!0,a&&(v=!0,g())}),b.once("abort",i),(e.cancelToken||e.signal)&&(e.cancelToken&&e.cancelToken.subscribe(y),e.signal&&(e.signal.aborted?y():e.signal.addEventListener("abort",y)));let w=new URL(eB(e.baseURL,e.url,e.allowAbsoluteUrls),ek.hasBrowserEnv?ek.origin:void 0),_=w.protocol||ap[0];if("data:"===_){let t;if("GET"!==x)return eD(a,i,{status:405,statusText:"method not allowed",headers:{},config:e});try{t=function(e,a,i){let t=i&&i.Blob||ek.classes.Blob,n=eV(e);if(void 0===a&&t&&(a=!0),"data"===n){e=n.length?e.slice(n.length+1):e;let i=eW.exec(e);if(!i)throw new Y("Invalid URL",Y.ERR_INVALID_URL);let o=i[1],s=i[2],r=i[3],p=Buffer.from(decodeURIComponent(r),s?"base64":"utf8");if(a){if(!t)throw new Y("Blob is not supported",Y.ERR_NOT_SUPPORT);return new t([p],{type:o})}return p}throw new Y("Unsupported protocol "+n,Y.ERR_NOT_SUPPORT)}(e.url,"blob"===h,{Blob:e.env&&e.env.Blob})}catch(a){throw Y.from(a,Y.ERR_BAD_REQUEST,e)}return"text"===h?(t=t.toString(f),f&&"utf8"!==f||(t=J.stripBOM(t))):"stream"===h&&(t=eK.Readable.from(t)),eD(a,i,{data:t,status:200,statusText:"OK",headers:new eL,config:e})}if(-1===ap.indexOf(_))return i(new Y("Unsupported protocol "+_,Y.ERR_BAD_REQUEST,e));let k=eL.from(e.headers).normalize();k.set("User-Agent","axios/"+e$,!1);let{onUploadProgress:E,onDownloadProgress:T}=e,j=e.maxRate;if(J.isSpecCompliantForm(u)){let e=k.getContentType(/boundary=([-_\w\d]{10,70})/i);u=e6(u,e=>{k.set(e)},{tag:`axios-${e$}-boundary`,boundary:e&&e[1]||void 0})}else if(J.isFormData(u)&&J.isFunction(u.getHeaders)){if(k.set(u.getHeaders()),!k.hasContentLength())try{let e=await eH.promisify(u.getLength).call(u);Number.isFinite(e)&&e>=0&&k.setContentLength(e)}catch(e){}}else if(J.isBlob(u)||J.isFile(u))u.size&&k.setContentType(u.type||"application/octet-stream"),k.setContentLength(u.size||0),u=eK.Readable.from(eX(u));else if(u&&!J.isStream(u)){if(Buffer.isBuffer(u));else if(J.isArrayBuffer(u))u=Buffer.from(new Uint8Array(u));else{if(!J.isString(u))return i(new Y("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",Y.ERR_BAD_REQUEST,e));u=Buffer.from(u,"utf-8")}if(k.setContentLength(u.length,!1),e.maxBodyLength>-1&&u.length>e.maxBodyLength)return i(new Y("Request body larger than maxBodyLength limit",Y.ERR_BAD_REQUEST,e))}let R=J.toFiniteNumber(k.getContentLength());J.isArray(j)?(n=j[0],o=j[1]):n=o=j,u&&(E||n)&&(J.isStream(u)||(u=eK.Readable.from(u,{objectMode:!1})),u=eK.pipeline([u,new eY({maxRate:J.toFiniteNumber(n)})],J.noop),E&&u.on("progress",ac(u,ae(R,e9(aa(E),!1,3))))),e.auth&&(s=(e.auth.username||"")+":"+(e.auth.password||"")),!s&&w.username&&(s=w.username+":"+w.password),s&&k.delete("authorization");try{c=ec(w.pathname+w.search,e.params,e.paramsSerializer).replace(/^\?/,"")}catch(t){let a=Error(t.message);return a.config=e,a.url=e.url,a.exists=!0,i(a)}k.set("Accept-Encoding","gzip, compress, deflate"+(an?", br":""),!1);let S={path:c,method:x,headers:k.toJSON(),agents:{http:e.httpAgent,https:e.httpsAgent},auth:s,protocol:_,family:m,beforeRedirect:al,beforeRedirects:{}};J.isUndefined(d)||(S.lookup=d),e.socketPath?S.socketPath=e.socketPath:(S.hostname=w.hostname.startsWith("[")?w.hostname.slice(1,-1):w.hostname,S.port=w.port,function e(a,i,t){let n=i;if(!n&&!1!==n){let e=eU.getProxyForUrl(t);e&&(n=new URL(e))}if(n){if(n.username&&(n.auth=(n.username||"")+":"+(n.password||"")),n.auth){(n.auth.username||n.auth.password)&&(n.auth=(n.auth.username||"")+":"+(n.auth.password||""));let e=Buffer.from(n.auth,"utf8").toString("base64");a.headers["Proxy-Authorization"]="Basic "+e}a.headers.host=a.hostname+(a.port?":"+a.port:"");let e=n.hostname||n.host;a.hostname=e,a.host=e,a.port=n.port,a.path=t,n.protocol&&(a.protocol=n.protocol.includes(":")?n.protocol:`${n.protocol}:`)}a.beforeRedirects.proxy=function(a){e(a,i,a.href)}}(S,e.proxy,_+"//"+w.hostname+(w.port?":"+w.port:"")+S.path));let C=ar.test(S.protocol);if(S.agent=C?e.httpsAgent:e.httpAgent,e.transport?l=e.transport:0===e.maxRedirects?l=C?eI:eN:(e.maxRedirects&&(S.maxRedirects=e.maxRedirects),e.beforeRedirect&&(S.beforeRedirects.config=e.beforeRedirect),l=C?as:ao),e.maxBodyLength>-1?S.maxBodyLength=e.maxBodyLength:S.maxBodyLength=1/0,e.insecureHTTPParser&&(S.insecureHTTPParser=e.insecureHTTPParser),p=l.request(S,function(t){if(p.destroyed)return;let n=[t],s=+t.headers["content-length"];if(T||o){let e=new eY({maxRate:J.toFiniteNumber(o)});T&&e.on("progress",ac(e,ae(s,e9(aa(T),!0,3)))),n.push(e)}let r=t,c=t.req||p;if(!1!==e.decompress&&t.headers["content-encoding"])switch(("HEAD"===x||204===t.statusCode)&&delete t.headers["content-encoding"],(t.headers["content-encoding"]||"").toLowerCase()){case"gzip":case"x-gzip":case"compress":case"x-compress":n.push(eM.createUnzip(ai)),delete t.headers["content-encoding"];break;case"deflate":n.push(new e4),n.push(eM.createUnzip(ai)),delete t.headers["content-encoding"];break;case"br":an&&(n.push(eM.createBrotliDecompress(at)),delete t.headers["content-encoding"])}r=n.length>1?eK.pipeline(n,J.noop):n[0];let l=eK.finished(r,()=>{l(),g()}),u={status:t.statusCode,statusText:t.statusMessage,headers:new eL(t.headers),config:e,request:c};if("stream"===h)u.data=r,eD(a,i,u);else{let t=[],n=0;r.on("data",function(a){t.push(a),n+=a.length,e.maxContentLength>-1&&n>e.maxContentLength&&(v=!0,r.destroy(),i(new Y("maxContentLength size of "+e.maxContentLength+" exceeded",Y.ERR_BAD_RESPONSE,e,c)))}),r.on("aborted",function(){if(v)return;let a=new Y("stream has been aborted",Y.ERR_BAD_RESPONSE,e,c);r.destroy(a),i(a)}),r.on("error",function(a){p.destroyed||i(Y.from(a,null,e,c))}),r.on("end",function(){try{let e=1===t.length?t[0]:Buffer.concat(t);"arraybuffer"===h||(e=e.toString(f),f&&"utf8"!==f||(e=J.stripBOM(e))),u.data=e}catch(a){return i(Y.from(a,null,e,u.request,u))}eD(a,i,u)})}b.once("abort",e=>{r.destroyed||(r.emit("error",e),r.destroy())})}),b.once("abort",e=>{i(e),p.destroy(e)}),p.on("error",function(a){i(Y.from(a,null,e,p))}),p.on("socket",function(e){e.setKeepAlive(!0,6e4)}),e.timeout){let a=parseInt(e.timeout,10);if(Number.isNaN(a)){i(new Y("error trying to parse `config.timeout` to int",Y.ERR_BAD_OPTION_VALUE,e,p));return}p.setTimeout(a,function(){if(r)return;let a=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded",t=e.transitional||eu;e.timeoutErrorMessage&&(a=e.timeoutErrorMessage),i(new Y(a,t.clarifyTimeoutError?Y.ETIMEDOUT:Y.ECONNABORTED,e,p)),y()})}if(J.isStream(u)){let a=!1,i=!1;u.on("end",()=>{a=!0}),u.once("error",e=>{i=!0,p.destroy(e)}),u.on("close",()=>{a||i||y(new eF("Request stream has been aborted",e,p))}),u.pipe(p)}else p.end(u)})},ax=ek.hasStandardBrowserEnv?(o=new URL(ek.origin),s=ek.navigator&&/(msie|trident)/i.test(ek.navigator.userAgent),e=>(e=new URL(e,ek.origin),o.protocol===e.protocol&&o.host===e.host&&(s||o.port===e.port))):()=>!0,av=ek.hasStandardBrowserEnv?{write(e,a,i,t,n,o){let s=[e+"="+encodeURIComponent(a)];J.isNumber(i)&&s.push("expires="+new Date(i).toGMTString()),J.isString(t)&&s.push("path="+t),J.isString(n)&&s.push("domain="+n),!0===o&&s.push("secure"),document.cookie=s.join("; ")},read(e){let a=document.cookie.match(RegExp("(^|;\\s*)("+e+")=([^;]*)"));return a?decodeURIComponent(a[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}},ab=e=>e instanceof eL?{...e}:e;function ag(e,a){a=a||{};let i={};function t(e,a,i,t){return J.isPlainObject(e)&&J.isPlainObject(a)?J.merge.call({caseless:t},e,a):J.isPlainObject(a)?J.merge({},a):J.isArray(a)?a.slice():a}function n(e,a,i,n){return J.isUndefined(a)?J.isUndefined(e)?void 0:t(void 0,e,i,n):t(e,a,i,n)}function o(e,a){if(!J.isUndefined(a))return t(void 0,a)}function s(e,a){return J.isUndefined(a)?J.isUndefined(e)?void 0:t(void 0,e):t(void 0,a)}function r(i,n,o){return o in a?t(i,n):o in e?t(void 0,i):void 0}let p={url:o,method:o,data:o,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:r,headers:(e,a,i)=>n(ab(e),ab(a),i,!0)};return J.forEach(Object.keys(Object.assign({},e,a)),function(t){let o=p[t]||n,s=o(e[t],a[t],t);J.isUndefined(s)&&o!==r||(i[t]=s)}),i}let ay=e=>{let a;let i=ag({},e),{data:t,withXSRFToken:n,xsrfHeaderName:o,xsrfCookieName:s,headers:r,auth:p}=i;if(i.headers=r=eL.from(r),i.url=ec(eB(i.baseURL,i.url,i.allowAbsoluteUrls),e.params,e.paramsSerializer),p&&r.set("Authorization","Basic "+btoa((p.username||"")+":"+(p.password?unescape(encodeURIComponent(p.password)):""))),J.isFormData(t)){if(ek.hasStandardBrowserEnv||ek.hasStandardBrowserWebWorkerEnv)r.setContentType(void 0);else if(!1!==(a=r.getContentType())){let[e,...i]=a?a.split(";").map(e=>e.trim()).filter(Boolean):[];r.setContentType([e||"multipart/form-data",...i].join("; "))}}if(ek.hasStandardBrowserEnv&&(n&&J.isFunction(n)&&(n=n(i)),n||!1!==n&&ax(i.url))){let e=o&&s&&av.read(s);e&&r.set(o,e)}return i},aw="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise(function(a,i){let t,n,o,s,r;let p=ay(e),c=p.data,l=eL.from(p.headers).normalize(),{responseType:u,onUploadProgress:d,onDownloadProgress:m}=p;function h(){s&&s(),r&&r(),p.cancelToken&&p.cancelToken.unsubscribe(t),p.signal&&p.signal.removeEventListener("abort",t)}let f=new XMLHttpRequest;function x(){if(!f)return;let t=eL.from("getAllResponseHeaders"in f&&f.getAllResponseHeaders());eD(function(e){a(e),h()},function(e){i(e),h()},{data:u&&"text"!==u&&"json"!==u?f.response:f.responseText,status:f.status,statusText:f.statusText,headers:t,config:e,request:f}),f=null}f.open(p.method.toUpperCase(),p.url,!0),f.timeout=p.timeout,"onloadend"in f?f.onloadend=x:f.onreadystatechange=function(){f&&4===f.readyState&&(0!==f.status||f.responseURL&&0===f.responseURL.indexOf("file:"))&&setTimeout(x)},f.onabort=function(){f&&(i(new Y("Request aborted",Y.ECONNABORTED,e,f)),f=null)},f.onerror=function(){i(new Y("Network Error",Y.ERR_NETWORK,e,f)),f=null},f.ontimeout=function(){let a=p.timeout?"timeout of "+p.timeout+"ms exceeded":"timeout exceeded",t=p.transitional||eu;p.timeoutErrorMessage&&(a=p.timeoutErrorMessage),i(new Y(a,t.clarifyTimeoutError?Y.ETIMEDOUT:Y.ECONNABORTED,e,f)),f=null},void 0===c&&l.setContentType(null),"setRequestHeader"in f&&J.forEach(l.toJSON(),function(e,a){f.setRequestHeader(a,e)}),J.isUndefined(p.withCredentials)||(f.withCredentials=!!p.withCredentials),u&&"json"!==u&&(f.responseType=p.responseType),m&&([o,r]=e9(m,!0),f.addEventListener("progress",o)),d&&f.upload&&([n,s]=e9(d),f.upload.addEventListener("progress",n),f.upload.addEventListener("loadend",s)),(p.cancelToken||p.signal)&&(t=a=>{f&&(i(!a||a.type?new eF(null,e,f):a),f.abort(),f=null)},p.cancelToken&&p.cancelToken.subscribe(t),p.signal&&(p.signal.aborted?t():p.signal.addEventListener("abort",t)));let v=eV(p.url);if(v&&-1===ek.protocols.indexOf(v)){i(new Y("Unsupported protocol "+v+":",Y.ERR_BAD_REQUEST,e));return}f.send(c||null)})},a_=(e,a)=>{let{length:i}=e=e?e.filter(Boolean):[];if(a||i){let i,t=new AbortController,n=function(e){if(!i){i=!0,s();let a=e instanceof Error?e:this.reason;t.abort(a instanceof Y?a:new eF(a instanceof Error?a.message:a))}},o=a&&setTimeout(()=>{o=null,n(new Y(`timeout ${a} of ms exceeded`,Y.ETIMEDOUT))},a),s=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(n):e.removeEventListener("abort",n)}),e=null)};e.forEach(e=>e.addEventListener("abort",n));let{signal:r}=t;return r.unsubscribe=()=>J.asap(s),r}},ak=function*(e,a){let i,t=e.byteLength;if(!a||t<a){yield e;return}let n=0;for(;n<t;)i=n+a,yield e.slice(n,i),n=i},aE=async function*(e,a){for await(let i of aT(e))yield*ak(i,a)},aT=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}let a=e.getReader();try{for(;;){let{done:e,value:i}=await a.read();if(e)break;yield i}}finally{await a.cancel()}},aj=(e,a,i,t)=>{let n;let o=aE(e,a),s=0,r=e=>{!n&&(n=!0,t&&t(e))};return new ReadableStream({async pull(e){try{let{done:a,value:t}=await o.next();if(a){r(),e.close();return}let n=t.byteLength;if(i){let e=s+=n;i(e)}e.enqueue(new Uint8Array(t))}catch(e){throw r(e),e}},cancel:e=>(r(e),o.return())},{highWaterMark:2})},aR="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,aS=aR&&"function"==typeof ReadableStream,aC=aR&&("function"==typeof TextEncoder?(r=new TextEncoder,e=>r.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer())),aO=(e,...a)=>{try{return!!e(...a)}catch(e){return!1}},aA=aS&&aO(()=>{let e=!1,a=new Request(ek.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!a}),az=aS&&aO(()=>J.isReadableStream(new Response("").body)),aL={stream:az&&(e=>e.body)};aR&&(d=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{aL[e]||(aL[e]=J.isFunction(d[e])?a=>a[e]():(a,i)=>{throw new Y(`Response type '${e}' is not supported`,Y.ERR_NOT_SUPPORT,i)})}));let aP=async e=>{if(null==e)return 0;if(J.isBlob(e))return e.size;if(J.isSpecCompliantForm(e)){let a=new Request(ek.origin,{method:"POST",body:e});return(await a.arrayBuffer()).byteLength}return J.isArrayBufferView(e)||J.isArrayBuffer(e)?e.byteLength:(J.isURLSearchParams(e)&&(e+=""),J.isString(e))?(await aC(e)).byteLength:void 0},aq=async(e,a)=>{let i=J.toFiniteNumber(e.getContentLength());return null==i?aP(a):i},aF={http:af,xhr:aw,fetch:aR&&(async e=>{let a,i,{url:t,method:n,data:o,signal:s,cancelToken:r,timeout:p,onDownloadProgress:c,onUploadProgress:l,responseType:u,headers:d,withCredentials:m="same-origin",fetchOptions:h}=ay(e);u=u?(u+"").toLowerCase():"text";let f=a_([s,r&&r.toAbortSignal()],p),x=f&&f.unsubscribe&&(()=>{f.unsubscribe()});try{if(l&&aA&&"get"!==n&&"head"!==n&&0!==(i=await aq(d,o))){let e,a=new Request(t,{method:"POST",body:o,duplex:"half"});if(J.isFormData(o)&&(e=a.headers.get("content-type"))&&d.setContentType(e),a.body){let[e,t]=ae(i,e9(aa(l)));o=aj(a.body,65536,e,t)}}J.isString(m)||(m=m?"include":"omit");let s="credentials"in Request.prototype;a=new Request(t,{...h,signal:f,method:n.toUpperCase(),headers:d.normalize().toJSON(),body:o,duplex:"half",credentials:s?m:void 0});let r=await fetch(a),p=az&&("stream"===u||"response"===u);if(az&&(c||p&&x)){let e={};["status","statusText","headers"].forEach(a=>{e[a]=r[a]});let a=J.toFiniteNumber(r.headers.get("content-length")),[i,t]=c&&ae(a,e9(aa(c),!0))||[];r=new Response(aj(r.body,65536,i,()=>{t&&t(),x&&x()}),e)}u=u||"text";let v=await aL[J.findKey(aL,u)||"text"](r,e);return!p&&x&&x(),await new Promise((i,t)=>{eD(i,t,{data:v,headers:eL.from(r.headers),status:r.status,statusText:r.statusText,config:e,request:a})})}catch(i){if(x&&x(),i&&"TypeError"===i.name&&/fetch/i.test(i.message))throw Object.assign(new Y("Network Error",Y.ERR_NETWORK,e,a),{cause:i.cause||i});throw Y.from(i,i&&i.code,e,a)}})};J.forEach(aF,(e,a)=>{if(e){try{Object.defineProperty(e,"name",{value:a})}catch(e){}Object.defineProperty(e,"adapterName",{value:a})}});let aD=e=>`- ${e}`,aB=e=>J.isFunction(e)||null===e||!1===e,aU={getAdapter:e=>{let a,i;let{length:t}=e=J.isArray(e)?e:[e],n={};for(let o=0;o<t;o++){let t;if(i=a=e[o],!aB(a)&&void 0===(i=aF[(t=String(a)).toLowerCase()]))throw new Y(`Unknown adapter '${t}'`);if(i)break;n[t||"#"+o]=i}if(!i){let e=Object.entries(n).map(([e,a])=>`adapter ${e} `+(!1===a?"is not supported by the environment":"is not available in the build"));throw new Y("There is no suitable adapter to dispatch the request "+(t?e.length>1?"since :\n"+e.map(aD).join("\n"):" "+aD(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return i}};function aN(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new eF(null,e)}function aI(e){return aN(e),e.headers=eL.from(e.headers),e.data=eP.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),aU.getAdapter(e.adapter||eT.adapter)(e).then(function(a){return aN(e),a.data=eP.call(e,e.transformResponse,a),a.headers=eL.from(a.headers),a},function(a){return!eq(a)&&(aN(e),a&&a.response&&(a.response.data=eP.call(e,e.transformResponse,a.response),a.response.headers=eL.from(a.response.headers))),Promise.reject(a)})}let aH={};["object","boolean","number","function","string","symbol"].forEach((e,a)=>{aH[e]=function(i){return typeof i===e||"a"+(a<1?"n ":" ")+e}});let aG={};aH.transitional=function(e,a,i){function t(e,a){return"[Axios v"+e$+"] Transitional option '"+e+"'"+a+(i?". "+i:"")}return(i,n,o)=>{if(!1===e)throw new Y(t(n," has been removed"+(a?" in "+a:"")),Y.ERR_DEPRECATED);return a&&!aG[n]&&(aG[n]=!0,console.warn(t(n," has been deprecated since v"+a+" and will be removed in the near future"))),!e||e(i,n,o)}},aH.spelling=function(e){return(a,i)=>(console.warn(`${i} is likely a misspelling of ${e}`),!0)};let aM={assertOptions:function(e,a,i){if("object"!=typeof e)throw new Y("options must be an object",Y.ERR_BAD_OPTION_VALUE);let t=Object.keys(e),n=t.length;for(;n-- >0;){let o=t[n],s=a[o];if(s){let a=e[o],i=void 0===a||s(a,o,e);if(!0!==i)throw new Y("option "+o+" must be "+i,Y.ERR_BAD_OPTION_VALUE);continue}if(!0!==i)throw new Y("Unknown option "+o,Y.ERR_BAD_OPTION)}},validators:aH},a$=aM.validators;class aV{constructor(e){this.defaults=e,this.interceptors={request:new el,response:new el}}async request(e,a){try{return await this._request(e,a)}catch(e){if(e instanceof Error){let a={};Error.captureStackTrace?Error.captureStackTrace(a):a=Error();let i=a.stack?a.stack.replace(/^.+\n/,""):"";try{e.stack?i&&!String(e.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+i):e.stack=i}catch(e){}}throw e}}_request(e,a){let i,t;"string"==typeof e?(a=a||{}).url=e:a=e||{};let{transitional:n,paramsSerializer:o,headers:s}=a=ag(this.defaults,a);void 0!==n&&aM.assertOptions(n,{silentJSONParsing:a$.transitional(a$.boolean),forcedJSONParsing:a$.transitional(a$.boolean),clarifyTimeoutError:a$.transitional(a$.boolean)},!1),null!=o&&(J.isFunction(o)?a.paramsSerializer={serialize:o}:aM.assertOptions(o,{encode:a$.function,serialize:a$.function},!0)),void 0!==a.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?a.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:a.allowAbsoluteUrls=!0),aM.assertOptions(a,{baseUrl:a$.spelling("baseURL"),withXsrfToken:a$.spelling("withXSRFToken")},!0),a.method=(a.method||this.defaults.method||"get").toLowerCase();let r=s&&J.merge(s.common,s[a.method]);s&&J.forEach(["delete","get","head","post","put","patch","common"],e=>{delete s[e]}),a.headers=eL.concat(r,s);let p=[],c=!0;this.interceptors.request.forEach(function(e){("function"!=typeof e.runWhen||!1!==e.runWhen(a))&&(c=c&&e.synchronous,p.unshift(e.fulfilled,e.rejected))});let l=[];this.interceptors.response.forEach(function(e){l.push(e.fulfilled,e.rejected)});let u=0;if(!c){let e=[aI.bind(this),void 0];for(e.unshift.apply(e,p),e.push.apply(e,l),t=e.length,i=Promise.resolve(a);u<t;)i=i.then(e[u++],e[u++]);return i}t=p.length;let d=a;for(u=0;u<t;){let e=p[u++],a=p[u++];try{d=e(d)}catch(e){a.call(this,e);break}}try{i=aI.call(this,d)}catch(e){return Promise.reject(e)}for(u=0,t=l.length;u<t;)i=i.then(l[u++],l[u++]);return i}getUri(e){return ec(eB((e=ag(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}J.forEach(["delete","get","head","options"],function(e){aV.prototype[e]=function(a,i){return this.request(ag(i||{},{method:e,url:a,data:(i||{}).data}))}}),J.forEach(["post","put","patch"],function(e){function a(a){return function(i,t,n){return this.request(ag(n||{},{method:e,headers:a?{"Content-Type":"multipart/form-data"}:{},url:i,data:t}))}}aV.prototype[e]=a(),aV.prototype[e+"Form"]=a(!0)});class aW{constructor(e){let a;if("function"!=typeof e)throw TypeError("executor must be a function.");this.promise=new Promise(function(e){a=e});let i=this;this.promise.then(e=>{if(!i._listeners)return;let a=i._listeners.length;for(;a-- >0;)i._listeners[a](e);i._listeners=null}),this.promise.then=e=>{let a;let t=new Promise(e=>{i.subscribe(e),a=e}).then(e);return t.cancel=function(){i.unsubscribe(a)},t},e(function(e,t,n){i.reason||(i.reason=new eF(e,t,n),a(i.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let a=this._listeners.indexOf(e);-1!==a&&this._listeners.splice(a,1)}toAbortSignal(){let e=new AbortController,a=a=>{e.abort(a)};return this.subscribe(a),e.signal.unsubscribe=()=>this.unsubscribe(a),e.signal}static source(){let e;return{token:new aW(function(a){e=a}),cancel:e}}}let aK={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(aK).forEach(([e,a])=>{aK[a]=e});let aJ=function e(a){let i=new aV(a),t=h(aV.prototype.request,i);return J.extend(t,aV.prototype,i,{allOwnKeys:!0}),J.extend(t,i,null,{allOwnKeys:!0}),t.create=function(i){return e(ag(a,i))},t}(eT);aJ.Axios=aV,aJ.CanceledError=eF,aJ.CancelToken=aW,aJ.isCancel=eq,aJ.VERSION=e$,aJ.toFormData=en,aJ.AxiosError=Y,aJ.Cancel=aJ.CanceledError,aJ.all=function(e){return Promise.all(e)},aJ.spread=function(e){return function(a){return e.apply(null,a)}},aJ.isAxiosError=function(e){return J.isObject(e)&&!0===e.isAxiosError},aJ.mergeConfig=ag,aJ.AxiosHeaders=eL,aJ.formToJSON=e=>eE(J.isHTMLForm(e)?new FormData(e):e),aJ.getAdapter=aU.getAdapter,aJ.HttpStatusCode=aK,aJ.default=aJ;let aY=aJ},2753:e=>{"use strict";e.exports=JSON.parse('{"application/1d-interleaved-parityfec":{"source":"iana"},"application/3gpdash-qoe-report+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/3gpp-ims+xml":{"source":"iana","compressible":true},"application/3gpphal+json":{"source":"iana","compressible":true},"application/3gpphalforms+json":{"source":"iana","compressible":true},"application/a2l":{"source":"iana"},"application/ace+cbor":{"source":"iana"},"application/activemessage":{"source":"iana"},"application/activity+json":{"source":"iana","compressible":true},"application/alto-costmap+json":{"source":"iana","compressible":true},"application/alto-costmapfilter+json":{"source":"iana","compressible":true},"application/alto-directory+json":{"source":"iana","compressible":true},"application/alto-endpointcost+json":{"source":"iana","compressible":true},"application/alto-endpointcostparams+json":{"source":"iana","compressible":true},"application/alto-endpointprop+json":{"source":"iana","compressible":true},"application/alto-endpointpropparams+json":{"source":"iana","compressible":true},"application/alto-error+json":{"source":"iana","compressible":true},"application/alto-networkmap+json":{"source":"iana","compressible":true},"application/alto-networkmapfilter+json":{"source":"iana","compressible":true},"application/alto-updatestreamcontrol+json":{"source":"iana","compressible":true},"application/alto-updatestreamparams+json":{"source":"iana","compressible":true},"application/aml":{"source":"iana"},"application/andrew-inset":{"source":"iana","extensions":["ez"]},"application/applefile":{"source":"iana"},"application/applixware":{"source":"apache","extensions":["aw"]},"application/at+jwt":{"source":"iana"},"application/atf":{"source":"iana"},"application/atfx":{"source":"iana"},"application/atom+xml":{"source":"iana","compressible":true,"extensions":["atom"]},"application/atomcat+xml":{"source":"iana","compressible":true,"extensions":["atomcat"]},"application/atomdeleted+xml":{"source":"iana","compressible":true,"extensions":["atomdeleted"]},"application/atomicmail":{"source":"iana"},"application/atomsvc+xml":{"source":"iana","compressible":true,"extensions":["atomsvc"]},"application/atsc-dwd+xml":{"source":"iana","compressible":true,"extensions":["dwd"]},"application/atsc-dynamic-event-message":{"source":"iana"},"application/atsc-held+xml":{"source":"iana","compressible":true,"extensions":["held"]},"application/atsc-rdt+json":{"source":"iana","compressible":true},"application/atsc-rsat+xml":{"source":"iana","compressible":true,"extensions":["rsat"]},"application/atxml":{"source":"iana"},"application/auth-policy+xml":{"source":"iana","compressible":true},"application/bacnet-xdd+zip":{"source":"iana","compressible":false},"application/batch-smtp":{"source":"iana"},"application/bdoc":{"compressible":false,"extensions":["bdoc"]},"application/beep+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/calendar+json":{"source":"iana","compressible":true},"application/calendar+xml":{"source":"iana","compressible":true,"extensions":["xcs"]},"application/call-completion":{"source":"iana"},"application/cals-1840":{"source":"iana"},"application/captive+json":{"source":"iana","compressible":true},"application/cbor":{"source":"iana"},"application/cbor-seq":{"source":"iana"},"application/cccex":{"source":"iana"},"application/ccmp+xml":{"source":"iana","compressible":true},"application/ccxml+xml":{"source":"iana","compressible":true,"extensions":["ccxml"]},"application/cdfx+xml":{"source":"iana","compressible":true,"extensions":["cdfx"]},"application/cdmi-capability":{"source":"iana","extensions":["cdmia"]},"application/cdmi-container":{"source":"iana","extensions":["cdmic"]},"application/cdmi-domain":{"source":"iana","extensions":["cdmid"]},"application/cdmi-object":{"source":"iana","extensions":["cdmio"]},"application/cdmi-queue":{"source":"iana","extensions":["cdmiq"]},"application/cdni":{"source":"iana"},"application/cea":{"source":"iana"},"application/cea-2018+xml":{"source":"iana","compressible":true},"application/cellml+xml":{"source":"iana","compressible":true},"application/cfw":{"source":"iana"},"application/city+json":{"source":"iana","compressible":true},"application/clr":{"source":"iana"},"application/clue+xml":{"source":"iana","compressible":true},"application/clue_info+xml":{"source":"iana","compressible":true},"application/cms":{"source":"iana"},"application/cnrp+xml":{"source":"iana","compressible":true},"application/coap-group+json":{"source":"iana","compressible":true},"application/coap-payload":{"source":"iana"},"application/commonground":{"source":"iana"},"application/conference-info+xml":{"source":"iana","compressible":true},"application/cose":{"source":"iana"},"application/cose-key":{"source":"iana"},"application/cose-key-set":{"source":"iana"},"application/cpl+xml":{"source":"iana","compressible":true,"extensions":["cpl"]},"application/csrattrs":{"source":"iana"},"application/csta+xml":{"source":"iana","compressible":true},"application/cstadata+xml":{"source":"iana","compressible":true},"application/csvm+json":{"source":"iana","compressible":true},"application/cu-seeme":{"source":"apache","extensions":["cu"]},"application/cwt":{"source":"iana"},"application/cybercash":{"source":"iana"},"application/dart":{"compressible":true},"application/dash+xml":{"source":"iana","compressible":true,"extensions":["mpd"]},"application/dash-patch+xml":{"source":"iana","compressible":true,"extensions":["mpp"]},"application/dashdelta":{"source":"iana"},"application/davmount+xml":{"source":"iana","compressible":true,"extensions":["davmount"]},"application/dca-rft":{"source":"iana"},"application/dcd":{"source":"iana"},"application/dec-dx":{"source":"iana"},"application/dialog-info+xml":{"source":"iana","compressible":true},"application/dicom":{"source":"iana"},"application/dicom+json":{"source":"iana","compressible":true},"application/dicom+xml":{"source":"iana","compressible":true},"application/dii":{"source":"iana"},"application/dit":{"source":"iana"},"application/dns":{"source":"iana"},"application/dns+json":{"source":"iana","compressible":true},"application/dns-message":{"source":"iana"},"application/docbook+xml":{"source":"apache","compressible":true,"extensions":["dbk"]},"application/dots+cbor":{"source":"iana"},"application/dskpp+xml":{"source":"iana","compressible":true},"application/dssc+der":{"source":"iana","extensions":["dssc"]},"application/dssc+xml":{"source":"iana","compressible":true,"extensions":["xdssc"]},"application/dvcs":{"source":"iana"},"application/ecmascript":{"source":"iana","compressible":true,"extensions":["es","ecma"]},"application/edi-consent":{"source":"iana"},"application/edi-x12":{"source":"iana","compressible":false},"application/edifact":{"source":"iana","compressible":false},"application/efi":{"source":"iana"},"application/elm+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/elm+xml":{"source":"iana","compressible":true},"application/emergencycalldata.cap+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/emergencycalldata.comment+xml":{"source":"iana","compressible":true},"application/emergencycalldata.control+xml":{"source":"iana","compressible":true},"application/emergencycalldata.deviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.ecall.msd":{"source":"iana"},"application/emergencycalldata.providerinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.serviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.subscriberinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.veds+xml":{"source":"iana","compressible":true},"application/emma+xml":{"source":"iana","compressible":true,"extensions":["emma"]},"application/emotionml+xml":{"source":"iana","compressible":true,"extensions":["emotionml"]},"application/encaprtp":{"source":"iana"},"application/epp+xml":{"source":"iana","compressible":true},"application/epub+zip":{"source":"iana","compressible":false,"extensions":["epub"]},"application/eshop":{"source":"iana"},"application/exi":{"source":"iana","extensions":["exi"]},"application/expect-ct-report+json":{"source":"iana","compressible":true},"application/express":{"source":"iana","extensions":["exp"]},"application/fastinfoset":{"source":"iana"},"application/fastsoap":{"source":"iana"},"application/fdt+xml":{"source":"iana","compressible":true,"extensions":["fdt"]},"application/fhir+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/fhir+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/fido.trusted-apps+json":{"compressible":true},"application/fits":{"source":"iana"},"application/flexfec":{"source":"iana"},"application/font-sfnt":{"source":"iana"},"application/font-tdpfr":{"source":"iana","extensions":["pfr"]},"application/font-woff":{"source":"iana","compressible":false},"application/framework-attributes+xml":{"source":"iana","compressible":true},"application/geo+json":{"source":"iana","compressible":true,"extensions":["geojson"]},"application/geo+json-seq":{"source":"iana"},"application/geopackage+sqlite3":{"source":"iana"},"application/geoxacml+xml":{"source":"iana","compressible":true},"application/gltf-buffer":{"source":"iana"},"application/gml+xml":{"source":"iana","compressible":true,"extensions":["gml"]},"application/gpx+xml":{"source":"apache","compressible":true,"extensions":["gpx"]},"application/gxf":{"source":"apache","extensions":["gxf"]},"application/gzip":{"source":"iana","compressible":false,"extensions":["gz"]},"application/h224":{"source":"iana"},"application/held+xml":{"source":"iana","compressible":true},"application/hjson":{"extensions":["hjson"]},"application/http":{"source":"iana"},"application/hyperstudio":{"source":"iana","extensions":["stk"]},"application/ibe-key-request+xml":{"source":"iana","compressible":true},"application/ibe-pkg-reply+xml":{"source":"iana","compressible":true},"application/ibe-pp-data":{"source":"iana"},"application/iges":{"source":"iana"},"application/im-iscomposing+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/index":{"source":"iana"},"application/index.cmd":{"source":"iana"},"application/index.obj":{"source":"iana"},"application/index.response":{"source":"iana"},"application/index.vnd":{"source":"iana"},"application/inkml+xml":{"source":"iana","compressible":true,"extensions":["ink","inkml"]},"application/iotp":{"source":"iana"},"application/ipfix":{"source":"iana","extensions":["ipfix"]},"application/ipp":{"source":"iana"},"application/isup":{"source":"iana"},"application/its+xml":{"source":"iana","compressible":true,"extensions":["its"]},"application/java-archive":{"source":"apache","compressible":false,"extensions":["jar","war","ear"]},"application/java-serialized-object":{"source":"apache","compressible":false,"extensions":["ser"]},"application/java-vm":{"source":"apache","compressible":false,"extensions":["class"]},"application/javascript":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["js","mjs"]},"application/jf2feed+json":{"source":"iana","compressible":true},"application/jose":{"source":"iana"},"application/jose+json":{"source":"iana","compressible":true},"application/jrd+json":{"source":"iana","compressible":true},"application/jscalendar+json":{"source":"iana","compressible":true},"application/json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["json","map"]},"application/json-patch+json":{"source":"iana","compressible":true},"application/json-seq":{"source":"iana"},"application/json5":{"extensions":["json5"]},"application/jsonml+json":{"source":"apache","compressible":true,"extensions":["jsonml"]},"application/jwk+json":{"source":"iana","compressible":true},"application/jwk-set+json":{"source":"iana","compressible":true},"application/jwt":{"source":"iana"},"application/kpml-request+xml":{"source":"iana","compressible":true},"application/kpml-response+xml":{"source":"iana","compressible":true},"application/ld+json":{"source":"iana","compressible":true,"extensions":["jsonld"]},"application/lgr+xml":{"source":"iana","compressible":true,"extensions":["lgr"]},"application/link-format":{"source":"iana"},"application/load-control+xml":{"source":"iana","compressible":true},"application/lost+xml":{"source":"iana","compressible":true,"extensions":["lostxml"]},"application/lostsync+xml":{"source":"iana","compressible":true},"application/lpf+zip":{"source":"iana","compressible":false},"application/lxf":{"source":"iana"},"application/mac-binhex40":{"source":"iana","extensions":["hqx"]},"application/mac-compactpro":{"source":"apache","extensions":["cpt"]},"application/macwriteii":{"source":"iana"},"application/mads+xml":{"source":"iana","compressible":true,"extensions":["mads"]},"application/manifest+json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["webmanifest"]},"application/marc":{"source":"iana","extensions":["mrc"]},"application/marcxml+xml":{"source":"iana","compressible":true,"extensions":["mrcx"]},"application/mathematica":{"source":"iana","extensions":["ma","nb","mb"]},"application/mathml+xml":{"source":"iana","compressible":true,"extensions":["mathml"]},"application/mathml-content+xml":{"source":"iana","compressible":true},"application/mathml-presentation+xml":{"source":"iana","compressible":true},"application/mbms-associated-procedure-description+xml":{"source":"iana","compressible":true},"application/mbms-deregister+xml":{"source":"iana","compressible":true},"application/mbms-envelope+xml":{"source":"iana","compressible":true},"application/mbms-msk+xml":{"source":"iana","compressible":true},"application/mbms-msk-response+xml":{"source":"iana","compressible":true},"application/mbms-protection-description+xml":{"source":"iana","compressible":true},"application/mbms-reception-report+xml":{"source":"iana","compressible":true},"application/mbms-register+xml":{"source":"iana","compressible":true},"application/mbms-register-response+xml":{"source":"iana","compressible":true},"application/mbms-schedule+xml":{"source":"iana","compressible":true},"application/mbms-user-service-description+xml":{"source":"iana","compressible":true},"application/mbox":{"source":"iana","extensions":["mbox"]},"application/media-policy-dataset+xml":{"source":"iana","compressible":true,"extensions":["mpf"]},"application/media_control+xml":{"source":"iana","compressible":true},"application/mediaservercontrol+xml":{"source":"iana","compressible":true,"extensions":["mscml"]},"application/merge-patch+json":{"source":"iana","compressible":true},"application/metalink+xml":{"source":"apache","compressible":true,"extensions":["metalink"]},"application/metalink4+xml":{"source":"iana","compressible":true,"extensions":["meta4"]},"application/mets+xml":{"source":"iana","compressible":true,"extensions":["mets"]},"application/mf4":{"source":"iana"},"application/mikey":{"source":"iana"},"application/mipc":{"source":"iana"},"application/missing-blocks+cbor-seq":{"source":"iana"},"application/mmt-aei+xml":{"source":"iana","compressible":true,"extensions":["maei"]},"application/mmt-usd+xml":{"source":"iana","compressible":true,"extensions":["musd"]},"application/mods+xml":{"source":"iana","compressible":true,"extensions":["mods"]},"application/moss-keys":{"source":"iana"},"application/moss-signature":{"source":"iana"},"application/mosskey-data":{"source":"iana"},"application/mosskey-request":{"source":"iana"},"application/mp21":{"source":"iana","extensions":["m21","mp21"]},"application/mp4":{"source":"iana","extensions":["mp4s","m4p"]},"application/mpeg4-generic":{"source":"iana"},"application/mpeg4-iod":{"source":"iana"},"application/mpeg4-iod-xmt":{"source":"iana"},"application/mrb-consumer+xml":{"source":"iana","compressible":true},"application/mrb-publish+xml":{"source":"iana","compressible":true},"application/msc-ivr+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msc-mixer+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msword":{"source":"iana","compressible":false,"extensions":["doc","dot"]},"application/mud+json":{"source":"iana","compressible":true},"application/multipart-core":{"source":"iana"},"application/mxf":{"source":"iana","extensions":["mxf"]},"application/n-quads":{"source":"iana","extensions":["nq"]},"application/n-triples":{"source":"iana","extensions":["nt"]},"application/nasdata":{"source":"iana"},"application/news-checkgroups":{"source":"iana","charset":"US-ASCII"},"application/news-groupinfo":{"source":"iana","charset":"US-ASCII"},"application/news-transmission":{"source":"iana"},"application/nlsml+xml":{"source":"iana","compressible":true},"application/node":{"source":"iana","extensions":["cjs"]},"application/nss":{"source":"iana"},"application/oauth-authz-req+jwt":{"source":"iana"},"application/oblivious-dns-message":{"source":"iana"},"application/ocsp-request":{"source":"iana"},"application/ocsp-response":{"source":"iana"},"application/octet-stream":{"source":"iana","compressible":false,"extensions":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/oda":{"source":"iana","extensions":["oda"]},"application/odm+xml":{"source":"iana","compressible":true},"application/odx":{"source":"iana"},"application/oebps-package+xml":{"source":"iana","compressible":true,"extensions":["opf"]},"application/ogg":{"source":"iana","compressible":false,"extensions":["ogx"]},"application/omdoc+xml":{"source":"apache","compressible":true,"extensions":["omdoc"]},"application/onenote":{"source":"apache","extensions":["onetoc","onetoc2","onetmp","onepkg"]},"application/opc-nodeset+xml":{"source":"iana","compressible":true},"application/oscore":{"source":"iana"},"application/oxps":{"source":"iana","extensions":["oxps"]},"application/p21":{"source":"iana"},"application/p21+zip":{"source":"iana","compressible":false},"application/p2p-overlay+xml":{"source":"iana","compressible":true,"extensions":["relo"]},"application/parityfec":{"source":"iana"},"application/passport":{"source":"iana"},"application/patch-ops-error+xml":{"source":"iana","compressible":true,"extensions":["xer"]},"application/pdf":{"source":"iana","compressible":false,"extensions":["pdf"]},"application/pdx":{"source":"iana"},"application/pem-certificate-chain":{"source":"iana"},"application/pgp-encrypted":{"source":"iana","compressible":false,"extensions":["pgp"]},"application/pgp-keys":{"source":"iana","extensions":["asc"]},"application/pgp-signature":{"source":"iana","extensions":["asc","sig"]},"application/pics-rules":{"source":"apache","extensions":["prf"]},"application/pidf+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pidf-diff+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pkcs10":{"source":"iana","extensions":["p10"]},"application/pkcs12":{"source":"iana"},"application/pkcs7-mime":{"source":"iana","extensions":["p7m","p7c"]},"application/pkcs7-signature":{"source":"iana","extensions":["p7s"]},"application/pkcs8":{"source":"iana","extensions":["p8"]},"application/pkcs8-encrypted":{"source":"iana"},"application/pkix-attr-cert":{"source":"iana","extensions":["ac"]},"application/pkix-cert":{"source":"iana","extensions":["cer"]},"application/pkix-crl":{"source":"iana","extensions":["crl"]},"application/pkix-pkipath":{"source":"iana","extensions":["pkipath"]},"application/pkixcmp":{"source":"iana","extensions":["pki"]},"application/pls+xml":{"source":"iana","compressible":true,"extensions":["pls"]},"application/poc-settings+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/postscript":{"source":"iana","compressible":true,"extensions":["ai","eps","ps"]},"application/ppsp-tracker+json":{"source":"iana","compressible":true},"application/problem+json":{"source":"iana","compressible":true},"application/problem+xml":{"source":"iana","compressible":true},"application/provenance+xml":{"source":"iana","compressible":true,"extensions":["provx"]},"application/prs.alvestrand.titrax-sheet":{"source":"iana"},"application/prs.cww":{"source":"iana","extensions":["cww"]},"application/prs.cyn":{"source":"iana","charset":"7-BIT"},"application/prs.hpub+zip":{"source":"iana","compressible":false},"application/prs.nprend":{"source":"iana"},"application/prs.plucker":{"source":"iana"},"application/prs.rdf-xml-crypt":{"source":"iana"},"application/prs.xsf+xml":{"source":"iana","compressible":true},"application/pskc+xml":{"source":"iana","compressible":true,"extensions":["pskcxml"]},"application/pvd+json":{"source":"iana","compressible":true},"application/qsig":{"source":"iana"},"application/raml+yaml":{"compressible":true,"extensions":["raml"]},"application/raptorfec":{"source":"iana"},"application/rdap+json":{"source":"iana","compressible":true},"application/rdf+xml":{"source":"iana","compressible":true,"extensions":["rdf","owl"]},"application/reginfo+xml":{"source":"iana","compressible":true,"extensions":["rif"]},"application/relax-ng-compact-syntax":{"source":"iana","extensions":["rnc"]},"application/remote-printing":{"source":"iana"},"application/reputon+json":{"source":"iana","compressible":true},"application/resource-lists+xml":{"source":"iana","compressible":true,"extensions":["rl"]},"application/resource-lists-diff+xml":{"source":"iana","compressible":true,"extensions":["rld"]},"application/rfc+xml":{"source":"iana","compressible":true},"application/riscos":{"source":"iana"},"application/rlmi+xml":{"source":"iana","compressible":true},"application/rls-services+xml":{"source":"iana","compressible":true,"extensions":["rs"]},"application/route-apd+xml":{"source":"iana","compressible":true,"extensions":["rapd"]},"application/route-s-tsid+xml":{"source":"iana","compressible":true,"extensions":["sls"]},"application/route-usd+xml":{"source":"iana","compressible":true,"extensions":["rusd"]},"application/rpki-ghostbusters":{"source":"iana","extensions":["gbr"]},"application/rpki-manifest":{"source":"iana","extensions":["mft"]},"application/rpki-publication":{"source":"iana"},"application/rpki-roa":{"source":"iana","extensions":["roa"]},"application/rpki-updown":{"source":"iana"},"application/rsd+xml":{"source":"apache","compressible":true,"extensions":["rsd"]},"application/rss+xml":{"source":"apache","compressible":true,"extensions":["rss"]},"application/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"application/rtploopback":{"source":"iana"},"application/rtx":{"source":"iana"},"application/samlassertion+xml":{"source":"iana","compressible":true},"application/samlmetadata+xml":{"source":"iana","compressible":true},"application/sarif+json":{"source":"iana","compressible":true},"application/sarif-external-properties+json":{"source":"iana","compressible":true},"application/sbe":{"source":"iana"},"application/sbml+xml":{"source":"iana","compressible":true,"extensions":["sbml"]},"application/scaip+xml":{"source":"iana","compressible":true},"application/scim+json":{"source":"iana","compressible":true},"application/scvp-cv-request":{"source":"iana","extensions":["scq"]},"application/scvp-cv-response":{"source":"iana","extensions":["scs"]},"application/scvp-vp-request":{"source":"iana","extensions":["spq"]},"application/scvp-vp-response":{"source":"iana","extensions":["spp"]},"application/sdp":{"source":"iana","extensions":["sdp"]},"application/secevent+jwt":{"source":"iana"},"application/senml+cbor":{"source":"iana"},"application/senml+json":{"source":"iana","compressible":true},"application/senml+xml":{"source":"iana","compressible":true,"extensions":["senmlx"]},"application/senml-etch+cbor":{"source":"iana"},"application/senml-etch+json":{"source":"iana","compressible":true},"application/senml-exi":{"source":"iana"},"application/sensml+cbor":{"source":"iana"},"application/sensml+json":{"source":"iana","compressible":true},"application/sensml+xml":{"source":"iana","compressible":true,"extensions":["sensmlx"]},"application/sensml-exi":{"source":"iana"},"application/sep+xml":{"source":"iana","compressible":true},"application/sep-exi":{"source":"iana"},"application/session-info":{"source":"iana"},"application/set-payment":{"source":"iana"},"application/set-payment-initiation":{"source":"iana","extensions":["setpay"]},"application/set-registration":{"source":"iana"},"application/set-registration-initiation":{"source":"iana","extensions":["setreg"]},"application/sgml":{"source":"iana"},"application/sgml-open-catalog":{"source":"iana"},"application/shf+xml":{"source":"iana","compressible":true,"extensions":["shf"]},"application/sieve":{"source":"iana","extensions":["siv","sieve"]},"application/simple-filter+xml":{"source":"iana","compressible":true},"application/simple-message-summary":{"source":"iana"},"application/simplesymbolcontainer":{"source":"iana"},"application/sipc":{"source":"iana"},"application/slate":{"source":"iana"},"application/smil":{"source":"iana"},"application/smil+xml":{"source":"iana","compressible":true,"extensions":["smi","smil"]},"application/smpte336m":{"source":"iana"},"application/soap+fastinfoset":{"source":"iana"},"application/soap+xml":{"source":"iana","compressible":true},"application/sparql-query":{"source":"iana","extensions":["rq"]},"application/sparql-results+xml":{"source":"iana","compressible":true,"extensions":["srx"]},"application/spdx+json":{"source":"iana","compressible":true},"application/spirits-event+xml":{"source":"iana","compressible":true},"application/sql":{"source":"iana"},"application/srgs":{"source":"iana","extensions":["gram"]},"application/srgs+xml":{"source":"iana","compressible":true,"extensions":["grxml"]},"application/sru+xml":{"source":"iana","compressible":true,"extensions":["sru"]},"application/ssdl+xml":{"source":"apache","compressible":true,"extensions":["ssdl"]},"application/ssml+xml":{"source":"iana","compressible":true,"extensions":["ssml"]},"application/stix+json":{"source":"iana","compressible":true},"application/swid+xml":{"source":"iana","compressible":true,"extensions":["swidtag"]},"application/tamp-apex-update":{"source":"iana"},"application/tamp-apex-update-confirm":{"source":"iana"},"application/tamp-community-update":{"source":"iana"},"application/tamp-community-update-confirm":{"source":"iana"},"application/tamp-error":{"source":"iana"},"application/tamp-sequence-adjust":{"source":"iana"},"application/tamp-sequence-adjust-confirm":{"source":"iana"},"application/tamp-status-query":{"source":"iana"},"application/tamp-status-response":{"source":"iana"},"application/tamp-update":{"source":"iana"},"application/tamp-update-confirm":{"source":"iana"},"application/tar":{"compressible":true},"application/taxii+json":{"source":"iana","compressible":true},"application/td+json":{"source":"iana","compressible":true},"application/tei+xml":{"source":"iana","compressible":true,"extensions":["tei","teicorpus"]},"application/tetra_isi":{"source":"iana"},"application/thraud+xml":{"source":"iana","compressible":true,"extensions":["tfi"]},"application/timestamp-query":{"source":"iana"},"application/timestamp-reply":{"source":"iana"},"application/timestamped-data":{"source":"iana","extensions":["tsd"]},"application/tlsrpt+gzip":{"source":"iana"},"application/tlsrpt+json":{"source":"iana","compressible":true},"application/tnauthlist":{"source":"iana"},"application/token-introspection+jwt":{"source":"iana"},"application/toml":{"compressible":true,"extensions":["toml"]},"application/trickle-ice-sdpfrag":{"source":"iana"},"application/trig":{"source":"iana","extensions":["trig"]},"application/ttml+xml":{"source":"iana","compressible":true,"extensions":["ttml"]},"application/tve-trigger":{"source":"iana"},"application/tzif":{"source":"iana"},"application/tzif-leap":{"source":"iana"},"application/ubjson":{"compressible":false,"extensions":["ubj"]},"application/ulpfec":{"source":"iana"},"application/urc-grpsheet+xml":{"source":"iana","compressible":true},"application/urc-ressheet+xml":{"source":"iana","compressible":true,"extensions":["rsheet"]},"application/urc-targetdesc+xml":{"source":"iana","compressible":true,"extensions":["td"]},"application/urc-uisocketdesc+xml":{"source":"iana","compressible":true},"application/vcard+json":{"source":"iana","compressible":true},"application/vcard+xml":{"source":"iana","compressible":true},"application/vemmi":{"source":"iana"},"application/vividence.scriptfile":{"source":"apache"},"application/vnd.1000minds.decision-model+xml":{"source":"iana","compressible":true,"extensions":["1km"]},"application/vnd.3gpp-prose+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-prose-pc3ch+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-v2x-local-service-information":{"source":"iana"},"application/vnd.3gpp.5gnas":{"source":"iana"},"application/vnd.3gpp.access-transfer-events+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.bsf+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gmop+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gtpc":{"source":"iana"},"application/vnd.3gpp.interworking-data":{"source":"iana"},"application/vnd.3gpp.lpp":{"source":"iana"},"application/vnd.3gpp.mc-signalling-ear":{"source":"iana"},"application/vnd.3gpp.mcdata-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-payload":{"source":"iana"},"application/vnd.3gpp.mcdata-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-signalling":{"source":"iana"},"application/vnd.3gpp.mcdata-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-floor-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-signed+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-init-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-transmission-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mid-call+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ngap":{"source":"iana"},"application/vnd.3gpp.pfcp":{"source":"iana"},"application/vnd.3gpp.pic-bw-large":{"source":"iana","extensions":["plb"]},"application/vnd.3gpp.pic-bw-small":{"source":"iana","extensions":["psb"]},"application/vnd.3gpp.pic-bw-var":{"source":"iana","extensions":["pvb"]},"application/vnd.3gpp.s1ap":{"source":"iana"},"application/vnd.3gpp.sms":{"source":"iana"},"application/vnd.3gpp.sms+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-ext+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.state-and-event-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ussd+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.bcmcsinfo+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.sms":{"source":"iana"},"application/vnd.3gpp2.tcap":{"source":"iana","extensions":["tcap"]},"application/vnd.3lightssoftware.imagescal":{"source":"iana"},"application/vnd.3m.post-it-notes":{"source":"iana","extensions":["pwn"]},"application/vnd.accpac.simply.aso":{"source":"iana","extensions":["aso"]},"application/vnd.accpac.simply.imp":{"source":"iana","extensions":["imp"]},"application/vnd.acucobol":{"source":"iana","extensions":["acu"]},"application/vnd.acucorp":{"source":"iana","extensions":["atc","acutc"]},"application/vnd.adobe.air-application-installer-package+zip":{"source":"apache","compressible":false,"extensions":["air"]},"application/vnd.adobe.flash.movie":{"source":"iana"},"application/vnd.adobe.formscentral.fcdt":{"source":"iana","extensions":["fcdt"]},"application/vnd.adobe.fxp":{"source":"iana","extensions":["fxp","fxpl"]},"application/vnd.adobe.partial-upload":{"source":"iana"},"application/vnd.adobe.xdp+xml":{"source":"iana","compressible":true,"extensions":["xdp"]},"application/vnd.adobe.xfdf":{"source":"iana","extensions":["xfdf"]},"application/vnd.aether.imp":{"source":"iana"},"application/vnd.afpc.afplinedata":{"source":"iana"},"application/vnd.afpc.afplinedata-pagedef":{"source":"iana"},"application/vnd.afpc.cmoca-cmresource":{"source":"iana"},"application/vnd.afpc.foca-charset":{"source":"iana"},"application/vnd.afpc.foca-codedfont":{"source":"iana"},"application/vnd.afpc.foca-codepage":{"source":"iana"},"application/vnd.afpc.modca":{"source":"iana"},"application/vnd.afpc.modca-cmtable":{"source":"iana"},"application/vnd.afpc.modca-formdef":{"source":"iana"},"application/vnd.afpc.modca-mediummap":{"source":"iana"},"application/vnd.afpc.modca-objectcontainer":{"source":"iana"},"application/vnd.afpc.modca-overlay":{"source":"iana"},"application/vnd.afpc.modca-pagesegment":{"source":"iana"},"application/vnd.age":{"source":"iana","extensions":["age"]},"application/vnd.ah-barcode":{"source":"iana"},"application/vnd.ahead.space":{"source":"iana","extensions":["ahead"]},"application/vnd.airzip.filesecure.azf":{"source":"iana","extensions":["azf"]},"application/vnd.airzip.filesecure.azs":{"source":"iana","extensions":["azs"]},"application/vnd.amadeus+json":{"source":"iana","compressible":true},"application/vnd.amazon.ebook":{"source":"apache","extensions":["azw"]},"application/vnd.amazon.mobi8-ebook":{"source":"iana"},"application/vnd.americandynamics.acc":{"source":"iana","extensions":["acc"]},"application/vnd.amiga.ami":{"source":"iana","extensions":["ami"]},"application/vnd.amundsen.maze+xml":{"source":"iana","compressible":true},"application/vnd.android.ota":{"source":"iana"},"application/vnd.android.package-archive":{"source":"apache","compressible":false,"extensions":["apk"]},"application/vnd.anki":{"source":"iana"},"application/vnd.anser-web-certificate-issue-initiation":{"source":"iana","extensions":["cii"]},"application/vnd.anser-web-funds-transfer-initiation":{"source":"apache","extensions":["fti"]},"application/vnd.antix.game-component":{"source":"iana","extensions":["atx"]},"application/vnd.apache.arrow.file":{"source":"iana"},"application/vnd.apache.arrow.stream":{"source":"iana"},"application/vnd.apache.thrift.binary":{"source":"iana"},"application/vnd.apache.thrift.compact":{"source":"iana"},"application/vnd.apache.thrift.json":{"source":"iana"},"application/vnd.api+json":{"source":"iana","compressible":true},"application/vnd.aplextor.warrp+json":{"source":"iana","compressible":true},"application/vnd.apothekende.reservation+json":{"source":"iana","compressible":true},"application/vnd.apple.installer+xml":{"source":"iana","compressible":true,"extensions":["mpkg"]},"application/vnd.apple.keynote":{"source":"iana","extensions":["key"]},"application/vnd.apple.mpegurl":{"source":"iana","extensions":["m3u8"]},"application/vnd.apple.numbers":{"source":"iana","extensions":["numbers"]},"application/vnd.apple.pages":{"source":"iana","extensions":["pages"]},"application/vnd.apple.pkpass":{"compressible":false,"extensions":["pkpass"]},"application/vnd.arastra.swi":{"source":"iana"},"application/vnd.aristanetworks.swi":{"source":"iana","extensions":["swi"]},"application/vnd.artisan+json":{"source":"iana","compressible":true},"application/vnd.artsquare":{"source":"iana"},"application/vnd.astraea-software.iota":{"source":"iana","extensions":["iota"]},"application/vnd.audiograph":{"source":"iana","extensions":["aep"]},"application/vnd.autopackage":{"source":"iana"},"application/vnd.avalon+json":{"source":"iana","compressible":true},"application/vnd.avistar+xml":{"source":"iana","compressible":true},"application/vnd.balsamiq.bmml+xml":{"source":"iana","compressible":true,"extensions":["bmml"]},"application/vnd.balsamiq.bmpr":{"source":"iana"},"application/vnd.banana-accounting":{"source":"iana"},"application/vnd.bbf.usp.error":{"source":"iana"},"application/vnd.bbf.usp.msg":{"source":"iana"},"application/vnd.bbf.usp.msg+json":{"source":"iana","compressible":true},"application/vnd.bekitzur-stech+json":{"source":"iana","compressible":true},"application/vnd.bint.med-content":{"source":"iana"},"application/vnd.biopax.rdf+xml":{"source":"iana","compressible":true},"application/vnd.blink-idb-value-wrapper":{"source":"iana"},"application/vnd.blueice.multipass":{"source":"iana","extensions":["mpm"]},"application/vnd.bluetooth.ep.oob":{"source":"iana"},"application/vnd.bluetooth.le.oob":{"source":"iana"},"application/vnd.bmi":{"source":"iana","extensions":["bmi"]},"application/vnd.bpf":{"source":"iana"},"application/vnd.bpf3":{"source":"iana"},"application/vnd.businessobjects":{"source":"iana","extensions":["rep"]},"application/vnd.byu.uapi+json":{"source":"iana","compressible":true},"application/vnd.cab-jscript":{"source":"iana"},"application/vnd.canon-cpdl":{"source":"iana"},"application/vnd.canon-lips":{"source":"iana"},"application/vnd.capasystems-pg+json":{"source":"iana","compressible":true},"application/vnd.cendio.thinlinc.clientconf":{"source":"iana"},"application/vnd.century-systems.tcp_stream":{"source":"iana"},"application/vnd.chemdraw+xml":{"source":"iana","compressible":true,"extensions":["cdxml"]},"application/vnd.chess-pgn":{"source":"iana"},"application/vnd.chipnuts.karaoke-mmd":{"source":"iana","extensions":["mmd"]},"application/vnd.ciedi":{"source":"iana"},"application/vnd.cinderella":{"source":"iana","extensions":["cdy"]},"application/vnd.cirpack.isdn-ext":{"source":"iana"},"application/vnd.citationstyles.style+xml":{"source":"iana","compressible":true,"extensions":["csl"]},"application/vnd.claymore":{"source":"iana","extensions":["cla"]},"application/vnd.cloanto.rp9":{"source":"iana","extensions":["rp9"]},"application/vnd.clonk.c4group":{"source":"iana","extensions":["c4g","c4d","c4f","c4p","c4u"]},"application/vnd.cluetrust.cartomobile-config":{"source":"iana","extensions":["c11amc"]},"application/vnd.cluetrust.cartomobile-config-pkg":{"source":"iana","extensions":["c11amz"]},"application/vnd.coffeescript":{"source":"iana"},"application/vnd.collabio.xodocuments.document":{"source":"iana"},"application/vnd.collabio.xodocuments.document-template":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation-template":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet-template":{"source":"iana"},"application/vnd.collection+json":{"source":"iana","compressible":true},"application/vnd.collection.doc+json":{"source":"iana","compressible":true},"application/vnd.collection.next+json":{"source":"iana","compressible":true},"application/vnd.comicbook+zip":{"source":"iana","compressible":false},"application/vnd.comicbook-rar":{"source":"iana"},"application/vnd.commerce-battelle":{"source":"iana"},"application/vnd.commonspace":{"source":"iana","extensions":["csp"]},"application/vnd.contact.cmsg":{"source":"iana","extensions":["cdbcmsg"]},"application/vnd.coreos.ignition+json":{"source":"iana","compressible":true},"application/vnd.cosmocaller":{"source":"iana","extensions":["cmc"]},"application/vnd.crick.clicker":{"source":"iana","extensions":["clkx"]},"application/vnd.crick.clicker.keyboard":{"source":"iana","extensions":["clkk"]},"application/vnd.crick.clicker.palette":{"source":"iana","extensions":["clkp"]},"application/vnd.crick.clicker.template":{"source":"iana","extensions":["clkt"]},"application/vnd.crick.clicker.wordbank":{"source":"iana","extensions":["clkw"]},"application/vnd.criticaltools.wbs+xml":{"source":"iana","compressible":true,"extensions":["wbs"]},"application/vnd.cryptii.pipe+json":{"source":"iana","compressible":true},"application/vnd.crypto-shade-file":{"source":"iana"},"application/vnd.cryptomator.encrypted":{"source":"iana"},"application/vnd.cryptomator.vault":{"source":"iana"},"application/vnd.ctc-posml":{"source":"iana","extensions":["pml"]},"application/vnd.ctct.ws+xml":{"source":"iana","compressible":true},"application/vnd.cups-pdf":{"source":"iana"},"application/vnd.cups-postscript":{"source":"iana"},"application/vnd.cups-ppd":{"source":"iana","extensions":["ppd"]},"application/vnd.cups-raster":{"source":"iana"},"application/vnd.cups-raw":{"source":"iana"},"application/vnd.curl":{"source":"iana"},"application/vnd.curl.car":{"source":"apache","extensions":["car"]},"application/vnd.curl.pcurl":{"source":"apache","extensions":["pcurl"]},"application/vnd.cyan.dean.root+xml":{"source":"iana","compressible":true},"application/vnd.cybank":{"source":"iana"},"application/vnd.cyclonedx+json":{"source":"iana","compressible":true},"application/vnd.cyclonedx+xml":{"source":"iana","compressible":true},"application/vnd.d2l.coursepackage1p0+zip":{"source":"iana","compressible":false},"application/vnd.d3m-dataset":{"source":"iana"},"application/vnd.d3m-problem":{"source":"iana"},"application/vnd.dart":{"source":"iana","compressible":true,"extensions":["dart"]},"application/vnd.data-vision.rdz":{"source":"iana","extensions":["rdz"]},"application/vnd.datapackage+json":{"source":"iana","compressible":true},"application/vnd.dataresource+json":{"source":"iana","compressible":true},"application/vnd.dbf":{"source":"iana","extensions":["dbf"]},"application/vnd.debian.binary-package":{"source":"iana"},"application/vnd.dece.data":{"source":"iana","extensions":["uvf","uvvf","uvd","uvvd"]},"application/vnd.dece.ttml+xml":{"source":"iana","compressible":true,"extensions":["uvt","uvvt"]},"application/vnd.dece.unspecified":{"source":"iana","extensions":["uvx","uvvx"]},"application/vnd.dece.zip":{"source":"iana","extensions":["uvz","uvvz"]},"application/vnd.denovo.fcselayout-link":{"source":"iana","extensions":["fe_launch"]},"application/vnd.desmume.movie":{"source":"iana"},"application/vnd.dir-bi.plate-dl-nosuffix":{"source":"iana"},"application/vnd.dm.delegation+xml":{"source":"iana","compressible":true},"application/vnd.dna":{"source":"iana","extensions":["dna"]},"application/vnd.document+json":{"source":"iana","compressible":true},"application/vnd.dolby.mlp":{"source":"apache","extensions":["mlp"]},"application/vnd.dolby.mobile.1":{"source":"iana"},"application/vnd.dolby.mobile.2":{"source":"iana"},"application/vnd.doremir.scorecloud-binary-document":{"source":"iana"},"application/vnd.dpgraph":{"source":"iana","extensions":["dpg"]},"application/vnd.dreamfactory":{"source":"iana","extensions":["dfac"]},"application/vnd.drive+json":{"source":"iana","compressible":true},"application/vnd.ds-keypoint":{"source":"apache","extensions":["kpxx"]},"application/vnd.dtg.local":{"source":"iana"},"application/vnd.dtg.local.flash":{"source":"iana"},"application/vnd.dtg.local.html":{"source":"iana"},"application/vnd.dvb.ait":{"source":"iana","extensions":["ait"]},"application/vnd.dvb.dvbisl+xml":{"source":"iana","compressible":true},"application/vnd.dvb.dvbj":{"source":"iana"},"application/vnd.dvb.esgcontainer":{"source":"iana"},"application/vnd.dvb.ipdcdftnotifaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess2":{"source":"iana"},"application/vnd.dvb.ipdcesgpdd":{"source":"iana"},"application/vnd.dvb.ipdcroaming":{"source":"iana"},"application/vnd.dvb.iptv.alfec-base":{"source":"iana"},"application/vnd.dvb.iptv.alfec-enhancement":{"source":"iana"},"application/vnd.dvb.notif-aggregate-root+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-container+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-generic+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-msglist+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-request+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-response+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-init+xml":{"source":"iana","compressible":true},"application/vnd.dvb.pfr":{"source":"iana"},"application/vnd.dvb.service":{"source":"iana","extensions":["svc"]},"application/vnd.dxr":{"source":"iana"},"application/vnd.dynageo":{"source":"iana","extensions":["geo"]},"application/vnd.dzr":{"source":"iana"},"application/vnd.easykaraoke.cdgdownload":{"source":"iana"},"application/vnd.ecdis-update":{"source":"iana"},"application/vnd.ecip.rlp":{"source":"iana"},"application/vnd.eclipse.ditto+json":{"source":"iana","compressible":true},"application/vnd.ecowin.chart":{"source":"iana","extensions":["mag"]},"application/vnd.ecowin.filerequest":{"source":"iana"},"application/vnd.ecowin.fileupdate":{"source":"iana"},"application/vnd.ecowin.series":{"source":"iana"},"application/vnd.ecowin.seriesrequest":{"source":"iana"},"application/vnd.ecowin.seriesupdate":{"source":"iana"},"application/vnd.efi.img":{"source":"iana"},"application/vnd.efi.iso":{"source":"iana"},"application/vnd.emclient.accessrequest+xml":{"source":"iana","compressible":true},"application/vnd.enliven":{"source":"iana","extensions":["nml"]},"application/vnd.enphase.envoy":{"source":"iana"},"application/vnd.eprints.data+xml":{"source":"iana","compressible":true},"application/vnd.epson.esf":{"source":"iana","extensions":["esf"]},"application/vnd.epson.msf":{"source":"iana","extensions":["msf"]},"application/vnd.epson.quickanime":{"source":"iana","extensions":["qam"]},"application/vnd.epson.salt":{"source":"iana","extensions":["slt"]},"application/vnd.epson.ssf":{"source":"iana","extensions":["ssf"]},"application/vnd.ericsson.quickcall":{"source":"iana"},"application/vnd.espass-espass+zip":{"source":"iana","compressible":false},"application/vnd.eszigno3+xml":{"source":"iana","compressible":true,"extensions":["es3","et3"]},"application/vnd.etsi.aoc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.asic-e+zip":{"source":"iana","compressible":false},"application/vnd.etsi.asic-s+zip":{"source":"iana","compressible":false},"application/vnd.etsi.cug+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvcommand+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-bc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-cod+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-npvr+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvservice+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsync+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvueprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mcid+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mheg5":{"source":"iana"},"application/vnd.etsi.overload-control-policy-dataset+xml":{"source":"iana","compressible":true},"application/vnd.etsi.pstn+xml":{"source":"iana","compressible":true},"application/vnd.etsi.sci+xml":{"source":"iana","compressible":true},"application/vnd.etsi.simservs+xml":{"source":"iana","compressible":true},"application/vnd.etsi.timestamp-token":{"source":"iana"},"application/vnd.etsi.tsl+xml":{"source":"iana","compressible":true},"application/vnd.etsi.tsl.der":{"source":"iana"},"application/vnd.eu.kasparian.car+json":{"source":"iana","compressible":true},"application/vnd.eudora.data":{"source":"iana"},"application/vnd.evolv.ecig.profile":{"source":"iana"},"application/vnd.evolv.ecig.settings":{"source":"iana"},"application/vnd.evolv.ecig.theme":{"source":"iana"},"application/vnd.exstream-empower+zip":{"source":"iana","compressible":false},"application/vnd.exstream-package":{"source":"iana"},"application/vnd.ezpix-album":{"source":"iana","extensions":["ez2"]},"application/vnd.ezpix-package":{"source":"iana","extensions":["ez3"]},"application/vnd.f-secure.mobile":{"source":"iana"},"application/vnd.familysearch.gedcom+zip":{"source":"iana","compressible":false},"application/vnd.fastcopy-disk-image":{"source":"iana"},"application/vnd.fdf":{"source":"iana","extensions":["fdf"]},"application/vnd.fdsn.mseed":{"source":"iana","extensions":["mseed"]},"application/vnd.fdsn.seed":{"source":"iana","extensions":["seed","dataless"]},"application/vnd.ffsns":{"source":"iana"},"application/vnd.ficlab.flb+zip":{"source":"iana","compressible":false},"application/vnd.filmit.zfc":{"source":"iana"},"application/vnd.fints":{"source":"iana"},"application/vnd.firemonkeys.cloudcell":{"source":"iana"},"application/vnd.flographit":{"source":"iana","extensions":["gph"]},"application/vnd.fluxtime.clip":{"source":"iana","extensions":["ftc"]},"application/vnd.font-fontforge-sfd":{"source":"iana"},"application/vnd.framemaker":{"source":"iana","extensions":["fm","frame","maker","book"]},"application/vnd.frogans.fnc":{"source":"iana","extensions":["fnc"]},"application/vnd.frogans.ltf":{"source":"iana","extensions":["ltf"]},"application/vnd.fsc.weblaunch":{"source":"iana","extensions":["fsc"]},"application/vnd.fujifilm.fb.docuworks":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.binder":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.container":{"source":"iana"},"application/vnd.fujifilm.fb.jfi+xml":{"source":"iana","compressible":true},"application/vnd.fujitsu.oasys":{"source":"iana","extensions":["oas"]},"application/vnd.fujitsu.oasys2":{"source":"iana","extensions":["oa2"]},"application/vnd.fujitsu.oasys3":{"source":"iana","extensions":["oa3"]},"application/vnd.fujitsu.oasysgp":{"source":"iana","extensions":["fg5"]},"application/vnd.fujitsu.oasysprs":{"source":"iana","extensions":["bh2"]},"application/vnd.fujixerox.art-ex":{"source":"iana"},"application/vnd.fujixerox.art4":{"source":"iana"},"application/vnd.fujixerox.ddd":{"source":"iana","extensions":["ddd"]},"application/vnd.fujixerox.docuworks":{"source":"iana","extensions":["xdw"]},"application/vnd.fujixerox.docuworks.binder":{"source":"iana","extensions":["xbd"]},"application/vnd.fujixerox.docuworks.container":{"source":"iana"},"application/vnd.fujixerox.hbpl":{"source":"iana"},"application/vnd.fut-misnet":{"source":"iana"},"application/vnd.futoin+cbor":{"source":"iana"},"application/vnd.futoin+json":{"source":"iana","compressible":true},"application/vnd.fuzzysheet":{"source":"iana","extensions":["fzs"]},"application/vnd.genomatix.tuxedo":{"source":"iana","extensions":["txd"]},"application/vnd.gentics.grd+json":{"source":"iana","compressible":true},"application/vnd.geo+json":{"source":"iana","compressible":true},"application/vnd.geocube+xml":{"source":"iana","compressible":true},"application/vnd.geogebra.file":{"source":"iana","extensions":["ggb"]},"application/vnd.geogebra.slides":{"source":"iana"},"application/vnd.geogebra.tool":{"source":"iana","extensions":["ggt"]},"application/vnd.geometry-explorer":{"source":"iana","extensions":["gex","gre"]},"application/vnd.geonext":{"source":"iana","extensions":["gxt"]},"application/vnd.geoplan":{"source":"iana","extensions":["g2w"]},"application/vnd.geospace":{"source":"iana","extensions":["g3w"]},"application/vnd.gerber":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt-response":{"source":"iana"},"application/vnd.gmx":{"source":"iana","extensions":["gmx"]},"application/vnd.google-apps.document":{"compressible":false,"extensions":["gdoc"]},"application/vnd.google-apps.presentation":{"compressible":false,"extensions":["gslides"]},"application/vnd.google-apps.spreadsheet":{"compressible":false,"extensions":["gsheet"]},"application/vnd.google-earth.kml+xml":{"source":"iana","compressible":true,"extensions":["kml"]},"application/vnd.google-earth.kmz":{"source":"iana","compressible":false,"extensions":["kmz"]},"application/vnd.gov.sk.e-form+xml":{"source":"iana","compressible":true},"application/vnd.gov.sk.e-form+zip":{"source":"iana","compressible":false},"application/vnd.gov.sk.xmldatacontainer+xml":{"source":"iana","compressible":true},"application/vnd.grafeq":{"source":"iana","extensions":["gqf","gqs"]},"application/vnd.gridmp":{"source":"iana"},"application/vnd.groove-account":{"source":"iana","extensions":["gac"]},"application/vnd.groove-help":{"source":"iana","extensions":["ghf"]},"application/vnd.groove-identity-message":{"source":"iana","extensions":["gim"]},"application/vnd.groove-injector":{"source":"iana","extensions":["grv"]},"application/vnd.groove-tool-message":{"source":"iana","extensions":["gtm"]},"application/vnd.groove-tool-template":{"source":"iana","extensions":["tpl"]},"application/vnd.groove-vcard":{"source":"iana","extensions":["vcg"]},"application/vnd.hal+json":{"source":"iana","compressible":true},"application/vnd.hal+xml":{"source":"iana","compressible":true,"extensions":["hal"]},"application/vnd.handheld-entertainment+xml":{"source":"iana","compressible":true,"extensions":["zmm"]},"application/vnd.hbci":{"source":"iana","extensions":["hbci"]},"application/vnd.hc+json":{"source":"iana","compressible":true},"application/vnd.hcl-bireports":{"source":"iana"},"application/vnd.hdt":{"source":"iana"},"application/vnd.heroku+json":{"source":"iana","compressible":true},"application/vnd.hhe.lesson-player":{"source":"iana","extensions":["les"]},"application/vnd.hl7cda+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hl7v2+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hp-hpgl":{"source":"iana","extensions":["hpgl"]},"application/vnd.hp-hpid":{"source":"iana","extensions":["hpid"]},"application/vnd.hp-hps":{"source":"iana","extensions":["hps"]},"application/vnd.hp-jlyt":{"source":"iana","extensions":["jlt"]},"application/vnd.hp-pcl":{"source":"iana","extensions":["pcl"]},"application/vnd.hp-pclxl":{"source":"iana","extensions":["pclxl"]},"application/vnd.httphone":{"source":"iana"},"application/vnd.hydrostatix.sof-data":{"source":"iana","extensions":["sfd-hdstx"]},"application/vnd.hyper+json":{"source":"iana","compressible":true},"application/vnd.hyper-item+json":{"source":"iana","compressible":true},"application/vnd.hyperdrive+json":{"source":"iana","compressible":true},"application/vnd.hzn-3d-crossword":{"source":"iana"},"application/vnd.ibm.afplinedata":{"source":"iana"},"application/vnd.ibm.electronic-media":{"source":"iana"},"application/vnd.ibm.minipay":{"source":"iana","extensions":["mpy"]},"application/vnd.ibm.modcap":{"source":"iana","extensions":["afp","listafp","list3820"]},"application/vnd.ibm.rights-management":{"source":"iana","extensions":["irm"]},"application/vnd.ibm.secure-container":{"source":"iana","extensions":["sc"]},"application/vnd.iccprofile":{"source":"iana","extensions":["icc","icm"]},"application/vnd.ieee.1905":{"source":"iana"},"application/vnd.igloader":{"source":"iana","extensions":["igl"]},"application/vnd.imagemeter.folder+zip":{"source":"iana","compressible":false},"application/vnd.imagemeter.image+zip":{"source":"iana","compressible":false},"application/vnd.immervision-ivp":{"source":"iana","extensions":["ivp"]},"application/vnd.immervision-ivu":{"source":"iana","extensions":["ivu"]},"application/vnd.ims.imsccv1p1":{"source":"iana"},"application/vnd.ims.imsccv1p2":{"source":"iana"},"application/vnd.ims.imsccv1p3":{"source":"iana"},"application/vnd.ims.lis.v2.result+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolconsumerprofile+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy.id+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings.simple+json":{"source":"iana","compressible":true},"application/vnd.informedcontrol.rms+xml":{"source":"iana","compressible":true},"application/vnd.informix-visionary":{"source":"iana"},"application/vnd.infotech.project":{"source":"iana"},"application/vnd.infotech.project+xml":{"source":"iana","compressible":true},"application/vnd.innopath.wamp.notification":{"source":"iana"},"application/vnd.insors.igm":{"source":"iana","extensions":["igm"]},"application/vnd.intercon.formnet":{"source":"iana","extensions":["xpw","xpx"]},"application/vnd.intergeo":{"source":"iana","extensions":["i2g"]},"application/vnd.intertrust.digibox":{"source":"iana"},"application/vnd.intertrust.nncp":{"source":"iana"},"application/vnd.intu.qbo":{"source":"iana","extensions":["qbo"]},"application/vnd.intu.qfx":{"source":"iana","extensions":["qfx"]},"application/vnd.iptc.g2.catalogitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.conceptitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.knowledgeitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsmessage+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.packageitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.planningitem+xml":{"source":"iana","compressible":true},"application/vnd.ipunplugged.rcprofile":{"source":"iana","extensions":["rcprofile"]},"application/vnd.irepository.package+xml":{"source":"iana","compressible":true,"extensions":["irp"]},"application/vnd.is-xpr":{"source":"iana","extensions":["xpr"]},"application/vnd.isac.fcs":{"source":"iana","extensions":["fcs"]},"application/vnd.iso11783-10+zip":{"source":"iana","compressible":false},"application/vnd.jam":{"source":"iana","extensions":["jam"]},"application/vnd.japannet-directory-service":{"source":"iana"},"application/vnd.japannet-jpnstore-wakeup":{"source":"iana"},"application/vnd.japannet-payment-wakeup":{"source":"iana"},"application/vnd.japannet-registration":{"source":"iana"},"application/vnd.japannet-registration-wakeup":{"source":"iana"},"application/vnd.japannet-setstore-wakeup":{"source":"iana"},"application/vnd.japannet-verification":{"source":"iana"},"application/vnd.japannet-verification-wakeup":{"source":"iana"},"application/vnd.jcp.javame.midlet-rms":{"source":"iana","extensions":["rms"]},"application/vnd.jisp":{"source":"iana","extensions":["jisp"]},"application/vnd.joost.joda-archive":{"source":"iana","extensions":["joda"]},"application/vnd.jsk.isdn-ngn":{"source":"iana"},"application/vnd.kahootz":{"source":"iana","extensions":["ktz","ktr"]},"application/vnd.kde.karbon":{"source":"iana","extensions":["karbon"]},"application/vnd.kde.kchart":{"source":"iana","extensions":["chrt"]},"application/vnd.kde.kformula":{"source":"iana","extensions":["kfo"]},"application/vnd.kde.kivio":{"source":"iana","extensions":["flw"]},"application/vnd.kde.kontour":{"source":"iana","extensions":["kon"]},"application/vnd.kde.kpresenter":{"source":"iana","extensions":["kpr","kpt"]},"application/vnd.kde.kspread":{"source":"iana","extensions":["ksp"]},"application/vnd.kde.kword":{"source":"iana","extensions":["kwd","kwt"]},"application/vnd.kenameaapp":{"source":"iana","extensions":["htke"]},"application/vnd.kidspiration":{"source":"iana","extensions":["kia"]},"application/vnd.kinar":{"source":"iana","extensions":["kne","knp"]},"application/vnd.koan":{"source":"iana","extensions":["skp","skd","skt","skm"]},"application/vnd.kodak-descriptor":{"source":"iana","extensions":["sse"]},"application/vnd.las":{"source":"iana"},"application/vnd.las.las+json":{"source":"iana","compressible":true},"application/vnd.las.las+xml":{"source":"iana","compressible":true,"extensions":["lasxml"]},"application/vnd.laszip":{"source":"iana"},"application/vnd.leap+json":{"source":"iana","compressible":true},"application/vnd.liberty-request+xml":{"source":"iana","compressible":true},"application/vnd.llamagraphics.life-balance.desktop":{"source":"iana","extensions":["lbd"]},"application/vnd.llamagraphics.life-balance.exchange+xml":{"source":"iana","compressible":true,"extensions":["lbe"]},"application/vnd.logipipe.circuit+zip":{"source":"iana","compressible":false},"application/vnd.loom":{"source":"iana"},"application/vnd.lotus-1-2-3":{"source":"iana","extensions":["123"]},"application/vnd.lotus-approach":{"source":"iana","extensions":["apr"]},"application/vnd.lotus-freelance":{"source":"iana","extensions":["pre"]},"application/vnd.lotus-notes":{"source":"iana","extensions":["nsf"]},"application/vnd.lotus-organizer":{"source":"iana","extensions":["org"]},"application/vnd.lotus-screencam":{"source":"iana","extensions":["scm"]},"application/vnd.lotus-wordpro":{"source":"iana","extensions":["lwp"]},"application/vnd.macports.portpkg":{"source":"iana","extensions":["portpkg"]},"application/vnd.mapbox-vector-tile":{"source":"iana","extensions":["mvt"]},"application/vnd.marlin.drm.actiontoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.conftoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.license+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.mdcf":{"source":"iana"},"application/vnd.mason+json":{"source":"iana","compressible":true},"application/vnd.maxar.archive.3tz+zip":{"source":"iana","compressible":false},"application/vnd.maxmind.maxmind-db":{"source":"iana"},"application/vnd.mcd":{"source":"iana","extensions":["mcd"]},"application/vnd.medcalcdata":{"source":"iana","extensions":["mc1"]},"application/vnd.mediastation.cdkey":{"source":"iana","extensions":["cdkey"]},"application/vnd.meridian-slingshot":{"source":"iana"},"application/vnd.mfer":{"source":"iana","extensions":["mwf"]},"application/vnd.mfmp":{"source":"iana","extensions":["mfm"]},"application/vnd.micro+json":{"source":"iana","compressible":true},"application/vnd.micrografx.flo":{"source":"iana","extensions":["flo"]},"application/vnd.micrografx.igx":{"source":"iana","extensions":["igx"]},"application/vnd.microsoft.portable-executable":{"source":"iana"},"application/vnd.microsoft.windows.thumbnail-cache":{"source":"iana"},"application/vnd.miele+json":{"source":"iana","compressible":true},"application/vnd.mif":{"source":"iana","extensions":["mif"]},"application/vnd.minisoft-hp3000-save":{"source":"iana"},"application/vnd.mitsubishi.misty-guard.trustweb":{"source":"iana"},"application/vnd.mobius.daf":{"source":"iana","extensions":["daf"]},"application/vnd.mobius.dis":{"source":"iana","extensions":["dis"]},"application/vnd.mobius.mbk":{"source":"iana","extensions":["mbk"]},"application/vnd.mobius.mqy":{"source":"iana","extensions":["mqy"]},"application/vnd.mobius.msl":{"source":"iana","extensions":["msl"]},"application/vnd.mobius.plc":{"source":"iana","extensions":["plc"]},"application/vnd.mobius.txf":{"source":"iana","extensions":["txf"]},"application/vnd.mophun.application":{"source":"iana","extensions":["mpn"]},"application/vnd.mophun.certificate":{"source":"iana","extensions":["mpc"]},"application/vnd.motorola.flexsuite":{"source":"iana"},"application/vnd.motorola.flexsuite.adsi":{"source":"iana"},"application/vnd.motorola.flexsuite.fis":{"source":"iana"},"application/vnd.motorola.flexsuite.gotap":{"source":"iana"},"application/vnd.motorola.flexsuite.kmr":{"source":"iana"},"application/vnd.motorola.flexsuite.ttc":{"source":"iana"},"application/vnd.motorola.flexsuite.wem":{"source":"iana"},"application/vnd.motorola.iprm":{"source":"iana"},"application/vnd.mozilla.xul+xml":{"source":"iana","compressible":true,"extensions":["xul"]},"application/vnd.ms-3mfdocument":{"source":"iana"},"application/vnd.ms-artgalry":{"source":"iana","extensions":["cil"]},"application/vnd.ms-asf":{"source":"iana"},"application/vnd.ms-cab-compressed":{"source":"iana","extensions":["cab"]},"application/vnd.ms-color.iccprofile":{"source":"apache"},"application/vnd.ms-excel":{"source":"iana","compressible":false,"extensions":["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-excel.addin.macroenabled.12":{"source":"iana","extensions":["xlam"]},"application/vnd.ms-excel.sheet.binary.macroenabled.12":{"source":"iana","extensions":["xlsb"]},"application/vnd.ms-excel.sheet.macroenabled.12":{"source":"iana","extensions":["xlsm"]},"application/vnd.ms-excel.template.macroenabled.12":{"source":"iana","extensions":["xltm"]},"application/vnd.ms-fontobject":{"source":"iana","compressible":true,"extensions":["eot"]},"application/vnd.ms-htmlhelp":{"source":"iana","extensions":["chm"]},"application/vnd.ms-ims":{"source":"iana","extensions":["ims"]},"application/vnd.ms-lrm":{"source":"iana","extensions":["lrm"]},"application/vnd.ms-office.activex+xml":{"source":"iana","compressible":true},"application/vnd.ms-officetheme":{"source":"iana","extensions":["thmx"]},"application/vnd.ms-opentype":{"source":"apache","compressible":true},"application/vnd.ms-outlook":{"compressible":false,"extensions":["msg"]},"application/vnd.ms-package.obfuscated-opentype":{"source":"apache"},"application/vnd.ms-pki.seccat":{"source":"apache","extensions":["cat"]},"application/vnd.ms-pki.stl":{"source":"apache","extensions":["stl"]},"application/vnd.ms-playready.initiator+xml":{"source":"iana","compressible":true},"application/vnd.ms-powerpoint":{"source":"iana","compressible":false,"extensions":["ppt","pps","pot"]},"application/vnd.ms-powerpoint.addin.macroenabled.12":{"source":"iana","extensions":["ppam"]},"application/vnd.ms-powerpoint.presentation.macroenabled.12":{"source":"iana","extensions":["pptm"]},"application/vnd.ms-powerpoint.slide.macroenabled.12":{"source":"iana","extensions":["sldm"]},"application/vnd.ms-powerpoint.slideshow.macroenabled.12":{"source":"iana","extensions":["ppsm"]},"application/vnd.ms-powerpoint.template.macroenabled.12":{"source":"iana","extensions":["potm"]},"application/vnd.ms-printdevicecapabilities+xml":{"source":"iana","compressible":true},"application/vnd.ms-printing.printticket+xml":{"source":"apache","compressible":true},"application/vnd.ms-printschematicket+xml":{"source":"iana","compressible":true},"application/vnd.ms-project":{"source":"iana","extensions":["mpp","mpt"]},"application/vnd.ms-tnef":{"source":"iana"},"application/vnd.ms-windows.devicepairing":{"source":"iana"},"application/vnd.ms-windows.nwprinting.oob":{"source":"iana"},"application/vnd.ms-windows.printerpairing":{"source":"iana"},"application/vnd.ms-windows.wsd.oob":{"source":"iana"},"application/vnd.ms-wmdrm.lic-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.lic-resp":{"source":"iana"},"application/vnd.ms-wmdrm.meter-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.meter-resp":{"source":"iana"},"application/vnd.ms-word.document.macroenabled.12":{"source":"iana","extensions":["docm"]},"application/vnd.ms-word.template.macroenabled.12":{"source":"iana","extensions":["dotm"]},"application/vnd.ms-works":{"source":"iana","extensions":["wps","wks","wcm","wdb"]},"application/vnd.ms-wpl":{"source":"iana","extensions":["wpl"]},"application/vnd.ms-xpsdocument":{"source":"iana","compressible":false,"extensions":["xps"]},"application/vnd.msa-disk-image":{"source":"iana"},"application/vnd.mseq":{"source":"iana","extensions":["mseq"]},"application/vnd.msign":{"source":"iana"},"application/vnd.multiad.creator":{"source":"iana"},"application/vnd.multiad.creator.cif":{"source":"iana"},"application/vnd.music-niff":{"source":"iana"},"application/vnd.musician":{"source":"iana","extensions":["mus"]},"application/vnd.muvee.style":{"source":"iana","extensions":["msty"]},"application/vnd.mynfc":{"source":"iana","extensions":["taglet"]},"application/vnd.nacamar.ybrid+json":{"source":"iana","compressible":true},"application/vnd.ncd.control":{"source":"iana"},"application/vnd.ncd.reference":{"source":"iana"},"application/vnd.nearst.inv+json":{"source":"iana","compressible":true},"application/vnd.nebumind.line":{"source":"iana"},"application/vnd.nervana":{"source":"iana"},"application/vnd.netfpx":{"source":"iana"},"application/vnd.neurolanguage.nlu":{"source":"iana","extensions":["nlu"]},"application/vnd.nimn":{"source":"iana"},"application/vnd.nintendo.nitro.rom":{"source":"iana"},"application/vnd.nintendo.snes.rom":{"source":"iana"},"application/vnd.nitf":{"source":"iana","extensions":["ntf","nitf"]},"application/vnd.noblenet-directory":{"source":"iana","extensions":["nnd"]},"application/vnd.noblenet-sealer":{"source":"iana","extensions":["nns"]},"application/vnd.noblenet-web":{"source":"iana","extensions":["nnw"]},"application/vnd.nokia.catalogs":{"source":"iana"},"application/vnd.nokia.conml+wbxml":{"source":"iana"},"application/vnd.nokia.conml+xml":{"source":"iana","compressible":true},"application/vnd.nokia.iptv.config+xml":{"source":"iana","compressible":true},"application/vnd.nokia.isds-radio-presets":{"source":"iana"},"application/vnd.nokia.landmark+wbxml":{"source":"iana"},"application/vnd.nokia.landmark+xml":{"source":"iana","compressible":true},"application/vnd.nokia.landmarkcollection+xml":{"source":"iana","compressible":true},"application/vnd.nokia.n-gage.ac+xml":{"source":"iana","compressible":true,"extensions":["ac"]},"application/vnd.nokia.n-gage.data":{"source":"iana","extensions":["ngdat"]},"application/vnd.nokia.n-gage.symbian.install":{"source":"iana","extensions":["n-gage"]},"application/vnd.nokia.ncd":{"source":"iana"},"application/vnd.nokia.pcd+wbxml":{"source":"iana"},"application/vnd.nokia.pcd+xml":{"source":"iana","compressible":true},"application/vnd.nokia.radio-preset":{"source":"iana","extensions":["rpst"]},"application/vnd.nokia.radio-presets":{"source":"iana","extensions":["rpss"]},"application/vnd.novadigm.edm":{"source":"iana","extensions":["edm"]},"application/vnd.novadigm.edx":{"source":"iana","extensions":["edx"]},"application/vnd.novadigm.ext":{"source":"iana","extensions":["ext"]},"application/vnd.ntt-local.content-share":{"source":"iana"},"application/vnd.ntt-local.file-transfer":{"source":"iana"},"application/vnd.ntt-local.ogw_remote-access":{"source":"iana"},"application/vnd.ntt-local.sip-ta_remote":{"source":"iana"},"application/vnd.ntt-local.sip-ta_tcp_stream":{"source":"iana"},"application/vnd.oasis.opendocument.chart":{"source":"iana","extensions":["odc"]},"application/vnd.oasis.opendocument.chart-template":{"source":"iana","extensions":["otc"]},"application/vnd.oasis.opendocument.database":{"source":"iana","extensions":["odb"]},"application/vnd.oasis.opendocument.formula":{"source":"iana","extensions":["odf"]},"application/vnd.oasis.opendocument.formula-template":{"source":"iana","extensions":["odft"]},"application/vnd.oasis.opendocument.graphics":{"source":"iana","compressible":false,"extensions":["odg"]},"application/vnd.oasis.opendocument.graphics-template":{"source":"iana","extensions":["otg"]},"application/vnd.oasis.opendocument.image":{"source":"iana","extensions":["odi"]},"application/vnd.oasis.opendocument.image-template":{"source":"iana","extensions":["oti"]},"application/vnd.oasis.opendocument.presentation":{"source":"iana","compressible":false,"extensions":["odp"]},"application/vnd.oasis.opendocument.presentation-template":{"source":"iana","extensions":["otp"]},"application/vnd.oasis.opendocument.spreadsheet":{"source":"iana","compressible":false,"extensions":["ods"]},"application/vnd.oasis.opendocument.spreadsheet-template":{"source":"iana","extensions":["ots"]},"application/vnd.oasis.opendocument.text":{"source":"iana","compressible":false,"extensions":["odt"]},"application/vnd.oasis.opendocument.text-master":{"source":"iana","extensions":["odm"]},"application/vnd.oasis.opendocument.text-template":{"source":"iana","extensions":["ott"]},"application/vnd.oasis.opendocument.text-web":{"source":"iana","extensions":["oth"]},"application/vnd.obn":{"source":"iana"},"application/vnd.ocf+cbor":{"source":"iana"},"application/vnd.oci.image.manifest.v1+json":{"source":"iana","compressible":true},"application/vnd.oftn.l10n+json":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessdownload+xml":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessstreaming+xml":{"source":"iana","compressible":true},"application/vnd.oipf.cspg-hexbinary":{"source":"iana"},"application/vnd.oipf.dae.svg+xml":{"source":"iana","compressible":true},"application/vnd.oipf.dae.xhtml+xml":{"source":"iana","compressible":true},"application/vnd.oipf.mippvcontrolmessage+xml":{"source":"iana","compressible":true},"application/vnd.oipf.pae.gem":{"source":"iana"},"application/vnd.oipf.spdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.oipf.spdlist+xml":{"source":"iana","compressible":true},"application/vnd.oipf.ueprofile+xml":{"source":"iana","compressible":true},"application/vnd.oipf.userprofile+xml":{"source":"iana","compressible":true},"application/vnd.olpc-sugar":{"source":"iana","extensions":["xo"]},"application/vnd.oma-scws-config":{"source":"iana"},"application/vnd.oma-scws-http-request":{"source":"iana"},"application/vnd.oma-scws-http-response":{"source":"iana"},"application/vnd.oma.bcast.associated-procedure-parameter+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.drm-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.imd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.ltkm":{"source":"iana"},"application/vnd.oma.bcast.notification+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.provisioningtrigger":{"source":"iana"},"application/vnd.oma.bcast.sgboot":{"source":"iana"},"application/vnd.oma.bcast.sgdd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sgdu":{"source":"iana"},"application/vnd.oma.bcast.simple-symbol-container":{"source":"iana"},"application/vnd.oma.bcast.smartcard-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sprov+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.stkm":{"source":"iana"},"application/vnd.oma.cab-address-book+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-feature-handler+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-pcc+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-subs-invite+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-user-prefs+xml":{"source":"iana","compressible":true},"application/vnd.oma.dcd":{"source":"iana"},"application/vnd.oma.dcdc":{"source":"iana"},"application/vnd.oma.dd2+xml":{"source":"iana","compressible":true,"extensions":["dd2"]},"application/vnd.oma.drm.risd+xml":{"source":"iana","compressible":true},"application/vnd.oma.group-usage-list+xml":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+cbor":{"source":"iana"},"application/vnd.oma.lwm2m+json":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+tlv":{"source":"iana"},"application/vnd.oma.pal+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.detailed-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.final-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.groups+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.invocation-descriptor+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.optimized-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.push":{"source":"iana"},"application/vnd.oma.scidm.messages+xml":{"source":"iana","compressible":true},"application/vnd.oma.xcap-directory+xml":{"source":"iana","compressible":true},"application/vnd.omads-email+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-file+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-folder+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omaloc-supl-init":{"source":"iana"},"application/vnd.onepager":{"source":"iana"},"application/vnd.onepagertamp":{"source":"iana"},"application/vnd.onepagertamx":{"source":"iana"},"application/vnd.onepagertat":{"source":"iana"},"application/vnd.onepagertatp":{"source":"iana"},"application/vnd.onepagertatx":{"source":"iana"},"application/vnd.openblox.game+xml":{"source":"iana","compressible":true,"extensions":["obgx"]},"application/vnd.openblox.game-binary":{"source":"iana"},"application/vnd.openeye.oeb":{"source":"iana"},"application/vnd.openofficeorg.extension":{"source":"apache","extensions":["oxt"]},"application/vnd.openstreetmap.data+xml":{"source":"iana","compressible":true,"extensions":["osm"]},"application/vnd.opentimestamps.ots":{"source":"iana"},"application/vnd.openxmlformats-officedocument.custom-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.customxmlproperties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawing+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chart+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramcolors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramdata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramlayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramstyle+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.extended-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.commentauthors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.handoutmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesslide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{"source":"iana","compressible":false,"extensions":["pptx"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slide":{"source":"iana","extensions":["sldx"]},"application/vnd.openxmlformats-officedocument.presentationml.slide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidelayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidemaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideshow":{"source":"iana","extensions":["ppsx"]},"application/vnd.openxmlformats-officedocument.presentationml.slideshow.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideupdateinfo+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tablestyles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tags+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.template":{"source":"iana","extensions":["potx"]},"application/vnd.openxmlformats-officedocument.presentationml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.viewprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.calcchain+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.externallink+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcachedefinition+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcacherecords+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivottable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.querytable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionheaders+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionlog+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedstrings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{"source":"iana","compressible":false,"extensions":["xlsx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetmetadata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.tablesinglecells+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.template":{"source":"iana","extensions":["xltx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.usernames+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.volatiledependencies+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.theme+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.themeoverride+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.vmldrawing":{"source":"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{"source":"iana","compressible":false,"extensions":["docx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.glossary+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.fonttable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.template":{"source":"iana","extensions":["dotx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.websettings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.core-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.digital-signature-xmlsignature+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.relationships+xml":{"source":"iana","compressible":true},"application/vnd.oracle.resource+json":{"source":"iana","compressible":true},"application/vnd.orange.indata":{"source":"iana"},"application/vnd.osa.netdeploy":{"source":"iana"},"application/vnd.osgeo.mapguide.package":{"source":"iana","extensions":["mgp"]},"application/vnd.osgi.bundle":{"source":"iana"},"application/vnd.osgi.dp":{"source":"iana","extensions":["dp"]},"application/vnd.osgi.subsystem":{"source":"iana","extensions":["esa"]},"application/vnd.otps.ct-kip+xml":{"source":"iana","compressible":true},"application/vnd.oxli.countgraph":{"source":"iana"},"application/vnd.pagerduty+json":{"source":"iana","compressible":true},"application/vnd.palm":{"source":"iana","extensions":["pdb","pqa","oprc"]},"application/vnd.panoply":{"source":"iana"},"application/vnd.paos.xml":{"source":"iana"},"application/vnd.patentdive":{"source":"iana"},"application/vnd.patientecommsdoc":{"source":"iana"},"application/vnd.pawaafile":{"source":"iana","extensions":["paw"]},"application/vnd.pcos":{"source":"iana"},"application/vnd.pg.format":{"source":"iana","extensions":["str"]},"application/vnd.pg.osasli":{"source":"iana","extensions":["ei6"]},"application/vnd.piaccess.application-licence":{"source":"iana"},"application/vnd.picsel":{"source":"iana","extensions":["efif"]},"application/vnd.pmi.widget":{"source":"iana","extensions":["wg"]},"application/vnd.poc.group-advertisement+xml":{"source":"iana","compressible":true},"application/vnd.pocketlearn":{"source":"iana","extensions":["plf"]},"application/vnd.powerbuilder6":{"source":"iana","extensions":["pbd"]},"application/vnd.powerbuilder6-s":{"source":"iana"},"application/vnd.powerbuilder7":{"source":"iana"},"application/vnd.powerbuilder7-s":{"source":"iana"},"application/vnd.powerbuilder75":{"source":"iana"},"application/vnd.powerbuilder75-s":{"source":"iana"},"application/vnd.preminet":{"source":"iana"},"application/vnd.previewsystems.box":{"source":"iana","extensions":["box"]},"application/vnd.proteus.magazine":{"source":"iana","extensions":["mgz"]},"application/vnd.psfs":{"source":"iana"},"application/vnd.publishare-delta-tree":{"source":"iana","extensions":["qps"]},"application/vnd.pvi.ptid1":{"source":"iana","extensions":["ptid"]},"application/vnd.pwg-multiplexed":{"source":"iana"},"application/vnd.pwg-xhtml-print+xml":{"source":"iana","compressible":true},"application/vnd.qualcomm.brew-app-res":{"source":"iana"},"application/vnd.quarantainenet":{"source":"iana"},"application/vnd.quark.quarkxpress":{"source":"iana","extensions":["qxd","qxt","qwd","qwt","qxl","qxb"]},"application/vnd.quobject-quoxdocument":{"source":"iana"},"application/vnd.radisys.moml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conn+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-stream+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-base+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-detect+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-sendrecv+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-group+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-speech+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-transform+xml":{"source":"iana","compressible":true},"application/vnd.rainstor.data":{"source":"iana"},"application/vnd.rapid":{"source":"iana"},"application/vnd.rar":{"source":"iana","extensions":["rar"]},"application/vnd.realvnc.bed":{"source":"iana","extensions":["bed"]},"application/vnd.recordare.musicxml":{"source":"iana","extensions":["mxl"]},"application/vnd.recordare.musicxml+xml":{"source":"iana","compressible":true,"extensions":["musicxml"]},"application/vnd.renlearn.rlprint":{"source":"iana"},"application/vnd.resilient.logic":{"source":"iana"},"application/vnd.restful+json":{"source":"iana","compressible":true},"application/vnd.rig.cryptonote":{"source":"iana","extensions":["cryptonote"]},"application/vnd.rim.cod":{"source":"apache","extensions":["cod"]},"application/vnd.rn-realmedia":{"source":"apache","extensions":["rm"]},"application/vnd.rn-realmedia-vbr":{"source":"apache","extensions":["rmvb"]},"application/vnd.route66.link66+xml":{"source":"iana","compressible":true,"extensions":["link66"]},"application/vnd.rs-274x":{"source":"iana"},"application/vnd.ruckus.download":{"source":"iana"},"application/vnd.s3sms":{"source":"iana"},"application/vnd.sailingtracker.track":{"source":"iana","extensions":["st"]},"application/vnd.sar":{"source":"iana"},"application/vnd.sbm.cid":{"source":"iana"},"application/vnd.sbm.mid2":{"source":"iana"},"application/vnd.scribus":{"source":"iana"},"application/vnd.sealed.3df":{"source":"iana"},"application/vnd.sealed.csf":{"source":"iana"},"application/vnd.sealed.doc":{"source":"iana"},"application/vnd.sealed.eml":{"source":"iana"},"application/vnd.sealed.mht":{"source":"iana"},"application/vnd.sealed.net":{"source":"iana"},"application/vnd.sealed.ppt":{"source":"iana"},"application/vnd.sealed.tiff":{"source":"iana"},"application/vnd.sealed.xls":{"source":"iana"},"application/vnd.sealedmedia.softseal.html":{"source":"iana"},"application/vnd.sealedmedia.softseal.pdf":{"source":"iana"},"application/vnd.seemail":{"source":"iana","extensions":["see"]},"application/vnd.seis+json":{"source":"iana","compressible":true},"application/vnd.sema":{"source":"iana","extensions":["sema"]},"application/vnd.semd":{"source":"iana","extensions":["semd"]},"application/vnd.semf":{"source":"iana","extensions":["semf"]},"application/vnd.shade-save-file":{"source":"iana"},"application/vnd.shana.informed.formdata":{"source":"iana","extensions":["ifm"]},"application/vnd.shana.informed.formtemplate":{"source":"iana","extensions":["itp"]},"application/vnd.shana.informed.interchange":{"source":"iana","extensions":["iif"]},"application/vnd.shana.informed.package":{"source":"iana","extensions":["ipk"]},"application/vnd.shootproof+json":{"source":"iana","compressible":true},"application/vnd.shopkick+json":{"source":"iana","compressible":true},"application/vnd.shp":{"source":"iana"},"application/vnd.shx":{"source":"iana"},"application/vnd.sigrok.session":{"source":"iana"},"application/vnd.simtech-mindmapper":{"source":"iana","extensions":["twd","twds"]},"application/vnd.siren+json":{"source":"iana","compressible":true},"application/vnd.smaf":{"source":"iana","extensions":["mmf"]},"application/vnd.smart.notebook":{"source":"iana"},"application/vnd.smart.teacher":{"source":"iana","extensions":["teacher"]},"application/vnd.snesdev-page-table":{"source":"iana"},"application/vnd.software602.filler.form+xml":{"source":"iana","compressible":true,"extensions":["fo"]},"application/vnd.software602.filler.form-xml-zip":{"source":"iana"},"application/vnd.solent.sdkm+xml":{"source":"iana","compressible":true,"extensions":["sdkm","sdkd"]},"application/vnd.spotfire.dxp":{"source":"iana","extensions":["dxp"]},"application/vnd.spotfire.sfs":{"source":"iana","extensions":["sfs"]},"application/vnd.sqlite3":{"source":"iana"},"application/vnd.sss-cod":{"source":"iana"},"application/vnd.sss-dtf":{"source":"iana"},"application/vnd.sss-ntf":{"source":"iana"},"application/vnd.stardivision.calc":{"source":"apache","extensions":["sdc"]},"application/vnd.stardivision.draw":{"source":"apache","extensions":["sda"]},"application/vnd.stardivision.impress":{"source":"apache","extensions":["sdd"]},"application/vnd.stardivision.math":{"source":"apache","extensions":["smf"]},"application/vnd.stardivision.writer":{"source":"apache","extensions":["sdw","vor"]},"application/vnd.stardivision.writer-global":{"source":"apache","extensions":["sgl"]},"application/vnd.stepmania.package":{"source":"iana","extensions":["smzip"]},"application/vnd.stepmania.stepchart":{"source":"iana","extensions":["sm"]},"application/vnd.street-stream":{"source":"iana"},"application/vnd.sun.wadl+xml":{"source":"iana","compressible":true,"extensions":["wadl"]},"application/vnd.sun.xml.calc":{"source":"apache","extensions":["sxc"]},"application/vnd.sun.xml.calc.template":{"source":"apache","extensions":["stc"]},"application/vnd.sun.xml.draw":{"source":"apache","extensions":["sxd"]},"application/vnd.sun.xml.draw.template":{"source":"apache","extensions":["std"]},"application/vnd.sun.xml.impress":{"source":"apache","extensions":["sxi"]},"application/vnd.sun.xml.impress.template":{"source":"apache","extensions":["sti"]},"application/vnd.sun.xml.math":{"source":"apache","extensions":["sxm"]},"application/vnd.sun.xml.writer":{"source":"apache","extensions":["sxw"]},"application/vnd.sun.xml.writer.global":{"source":"apache","extensions":["sxg"]},"application/vnd.sun.xml.writer.template":{"source":"apache","extensions":["stw"]},"application/vnd.sus-calendar":{"source":"iana","extensions":["sus","susp"]},"application/vnd.svd":{"source":"iana","extensions":["svd"]},"application/vnd.swiftview-ics":{"source":"iana"},"application/vnd.sycle+xml":{"source":"iana","compressible":true},"application/vnd.syft+json":{"source":"iana","compressible":true},"application/vnd.symbian.install":{"source":"apache","extensions":["sis","sisx"]},"application/vnd.syncml+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xsm"]},"application/vnd.syncml.dm+wbxml":{"source":"iana","charset":"UTF-8","extensions":["bdm"]},"application/vnd.syncml.dm+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xdm"]},"application/vnd.syncml.dm.notification":{"source":"iana"},"application/vnd.syncml.dmddf+wbxml":{"source":"iana"},"application/vnd.syncml.dmddf+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["ddf"]},"application/vnd.syncml.dmtnds+wbxml":{"source":"iana"},"application/vnd.syncml.dmtnds+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.syncml.ds.notification":{"source":"iana"},"application/vnd.tableschema+json":{"source":"iana","compressible":true},"application/vnd.tao.intent-module-archive":{"source":"iana","extensions":["tao"]},"application/vnd.tcpdump.pcap":{"source":"iana","extensions":["pcap","cap","dmp"]},"application/vnd.think-cell.ppttc+json":{"source":"iana","compressible":true},"application/vnd.tmd.mediaflex.api+xml":{"source":"iana","compressible":true},"application/vnd.tml":{"source":"iana"},"application/vnd.tmobile-livetv":{"source":"iana","extensions":["tmo"]},"application/vnd.tri.onesource":{"source":"iana"},"application/vnd.trid.tpt":{"source":"iana","extensions":["tpt"]},"application/vnd.triscape.mxs":{"source":"iana","extensions":["mxs"]},"application/vnd.trueapp":{"source":"iana","extensions":["tra"]},"application/vnd.truedoc":{"source":"iana"},"application/vnd.ubisoft.webplayer":{"source":"iana"},"application/vnd.ufdl":{"source":"iana","extensions":["ufd","ufdl"]},"application/vnd.uiq.theme":{"source":"iana","extensions":["utz"]},"application/vnd.umajin":{"source":"iana","extensions":["umj"]},"application/vnd.unity":{"source":"iana","extensions":["unityweb"]},"application/vnd.uoml+xml":{"source":"iana","compressible":true,"extensions":["uoml"]},"application/vnd.uplanet.alert":{"source":"iana"},"application/vnd.uplanet.alert-wbxml":{"source":"iana"},"application/vnd.uplanet.bearer-choice":{"source":"iana"},"application/vnd.uplanet.bearer-choice-wbxml":{"source":"iana"},"application/vnd.uplanet.cacheop":{"source":"iana"},"application/vnd.uplanet.cacheop-wbxml":{"source":"iana"},"application/vnd.uplanet.channel":{"source":"iana"},"application/vnd.uplanet.channel-wbxml":{"source":"iana"},"application/vnd.uplanet.list":{"source":"iana"},"application/vnd.uplanet.list-wbxml":{"source":"iana"},"application/vnd.uplanet.listcmd":{"source":"iana"},"application/vnd.uplanet.listcmd-wbxml":{"source":"iana"},"application/vnd.uplanet.signal":{"source":"iana"},"application/vnd.uri-map":{"source":"iana"},"application/vnd.valve.source.material":{"source":"iana"},"application/vnd.vcx":{"source":"iana","extensions":["vcx"]},"application/vnd.vd-study":{"source":"iana"},"application/vnd.vectorworks":{"source":"iana"},"application/vnd.vel+json":{"source":"iana","compressible":true},"application/vnd.verimatrix.vcas":{"source":"iana"},"application/vnd.veritone.aion+json":{"source":"iana","compressible":true},"application/vnd.veryant.thin":{"source":"iana"},"application/vnd.ves.encrypted":{"source":"iana"},"application/vnd.vidsoft.vidconference":{"source":"iana"},"application/vnd.visio":{"source":"iana","extensions":["vsd","vst","vss","vsw"]},"application/vnd.visionary":{"source":"iana","extensions":["vis"]},"application/vnd.vividence.scriptfile":{"source":"iana"},"application/vnd.vsf":{"source":"iana","extensions":["vsf"]},"application/vnd.wap.sic":{"source":"iana"},"application/vnd.wap.slc":{"source":"iana"},"application/vnd.wap.wbxml":{"source":"iana","charset":"UTF-8","extensions":["wbxml"]},"application/vnd.wap.wmlc":{"source":"iana","extensions":["wmlc"]},"application/vnd.wap.wmlscriptc":{"source":"iana","extensions":["wmlsc"]},"application/vnd.webturbo":{"source":"iana","extensions":["wtb"]},"application/vnd.wfa.dpp":{"source":"iana"},"application/vnd.wfa.p2p":{"source":"iana"},"application/vnd.wfa.wsc":{"source":"iana"},"application/vnd.windows.devicepairing":{"source":"iana"},"application/vnd.wmc":{"source":"iana"},"application/vnd.wmf.bootstrap":{"source":"iana"},"application/vnd.wolfram.mathematica":{"source":"iana"},"application/vnd.wolfram.mathematica.package":{"source":"iana"},"application/vnd.wolfram.player":{"source":"iana","extensions":["nbp"]},"application/vnd.wordperfect":{"source":"iana","extensions":["wpd"]},"application/vnd.wqd":{"source":"iana","extensions":["wqd"]},"application/vnd.wrq-hp3000-labelled":{"source":"iana"},"application/vnd.wt.stf":{"source":"iana","extensions":["stf"]},"application/vnd.wv.csp+wbxml":{"source":"iana"},"application/vnd.wv.csp+xml":{"source":"iana","compressible":true},"application/vnd.wv.ssp+xml":{"source":"iana","compressible":true},"application/vnd.xacml+json":{"source":"iana","compressible":true},"application/vnd.xara":{"source":"iana","extensions":["xar"]},"application/vnd.xfdl":{"source":"iana","extensions":["xfdl"]},"application/vnd.xfdl.webform":{"source":"iana"},"application/vnd.xmi+xml":{"source":"iana","compressible":true},"application/vnd.xmpie.cpkg":{"source":"iana"},"application/vnd.xmpie.dpkg":{"source":"iana"},"application/vnd.xmpie.plan":{"source":"iana"},"application/vnd.xmpie.ppkg":{"source":"iana"},"application/vnd.xmpie.xlim":{"source":"iana"},"application/vnd.yamaha.hv-dic":{"source":"iana","extensions":["hvd"]},"application/vnd.yamaha.hv-script":{"source":"iana","extensions":["hvs"]},"application/vnd.yamaha.hv-voice":{"source":"iana","extensions":["hvp"]},"application/vnd.yamaha.openscoreformat":{"source":"iana","extensions":["osf"]},"application/vnd.yamaha.openscoreformat.osfpvg+xml":{"source":"iana","compressible":true,"extensions":["osfpvg"]},"application/vnd.yamaha.remote-setup":{"source":"iana"},"application/vnd.yamaha.smaf-audio":{"source":"iana","extensions":["saf"]},"application/vnd.yamaha.smaf-phrase":{"source":"iana","extensions":["spf"]},"application/vnd.yamaha.through-ngn":{"source":"iana"},"application/vnd.yamaha.tunnel-udpencap":{"source":"iana"},"application/vnd.yaoweme":{"source":"iana"},"application/vnd.yellowriver-custom-menu":{"source":"iana","extensions":["cmp"]},"application/vnd.youtube.yt":{"source":"iana"},"application/vnd.zul":{"source":"iana","extensions":["zir","zirz"]},"application/vnd.zzazz.deck+xml":{"source":"iana","compressible":true,"extensions":["zaz"]},"application/voicexml+xml":{"source":"iana","compressible":true,"extensions":["vxml"]},"application/voucher-cms+json":{"source":"iana","compressible":true},"application/vq-rtcpxr":{"source":"iana"},"application/wasm":{"source":"iana","compressible":true,"extensions":["wasm"]},"application/watcherinfo+xml":{"source":"iana","compressible":true,"extensions":["wif"]},"application/webpush-options+json":{"source":"iana","compressible":true},"application/whoispp-query":{"source":"iana"},"application/whoispp-response":{"source":"iana"},"application/widget":{"source":"iana","extensions":["wgt"]},"application/winhlp":{"source":"apache","extensions":["hlp"]},"application/wita":{"source":"iana"},"application/wordperfect5.1":{"source":"iana"},"application/wsdl+xml":{"source":"iana","compressible":true,"extensions":["wsdl"]},"application/wspolicy+xml":{"source":"iana","compressible":true,"extensions":["wspolicy"]},"application/x-7z-compressed":{"source":"apache","compressible":false,"extensions":["7z"]},"application/x-abiword":{"source":"apache","extensions":["abw"]},"application/x-ace-compressed":{"source":"apache","extensions":["ace"]},"application/x-amf":{"source":"apache"},"application/x-apple-diskimage":{"source":"apache","extensions":["dmg"]},"application/x-arj":{"compressible":false,"extensions":["arj"]},"application/x-authorware-bin":{"source":"apache","extensions":["aab","x32","u32","vox"]},"application/x-authorware-map":{"source":"apache","extensions":["aam"]},"application/x-authorware-seg":{"source":"apache","extensions":["aas"]},"application/x-bcpio":{"source":"apache","extensions":["bcpio"]},"application/x-bdoc":{"compressible":false,"extensions":["bdoc"]},"application/x-bittorrent":{"source":"apache","extensions":["torrent"]},"application/x-blorb":{"source":"apache","extensions":["blb","blorb"]},"application/x-bzip":{"source":"apache","compressible":false,"extensions":["bz"]},"application/x-bzip2":{"source":"apache","compressible":false,"extensions":["bz2","boz"]},"application/x-cbr":{"source":"apache","extensions":["cbr","cba","cbt","cbz","cb7"]},"application/x-cdlink":{"source":"apache","extensions":["vcd"]},"application/x-cfs-compressed":{"source":"apache","extensions":["cfs"]},"application/x-chat":{"source":"apache","extensions":["chat"]},"application/x-chess-pgn":{"source":"apache","extensions":["pgn"]},"application/x-chrome-extension":{"extensions":["crx"]},"application/x-cocoa":{"source":"nginx","extensions":["cco"]},"application/x-compress":{"source":"apache"},"application/x-conference":{"source":"apache","extensions":["nsc"]},"application/x-cpio":{"source":"apache","extensions":["cpio"]},"application/x-csh":{"source":"apache","extensions":["csh"]},"application/x-deb":{"compressible":false},"application/x-debian-package":{"source":"apache","extensions":["deb","udeb"]},"application/x-dgc-compressed":{"source":"apache","extensions":["dgc"]},"application/x-director":{"source":"apache","extensions":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"]},"application/x-doom":{"source":"apache","extensions":["wad"]},"application/x-dtbncx+xml":{"source":"apache","compressible":true,"extensions":["ncx"]},"application/x-dtbook+xml":{"source":"apache","compressible":true,"extensions":["dtb"]},"application/x-dtbresource+xml":{"source":"apache","compressible":true,"extensions":["res"]},"application/x-dvi":{"source":"apache","compressible":false,"extensions":["dvi"]},"application/x-envoy":{"source":"apache","extensions":["evy"]},"application/x-eva":{"source":"apache","extensions":["eva"]},"application/x-font-bdf":{"source":"apache","extensions":["bdf"]},"application/x-font-dos":{"source":"apache"},"application/x-font-framemaker":{"source":"apache"},"application/x-font-ghostscript":{"source":"apache","extensions":["gsf"]},"application/x-font-libgrx":{"source":"apache"},"application/x-font-linux-psf":{"source":"apache","extensions":["psf"]},"application/x-font-pcf":{"source":"apache","extensions":["pcf"]},"application/x-font-snf":{"source":"apache","extensions":["snf"]},"application/x-font-speedo":{"source":"apache"},"application/x-font-sunos-news":{"source":"apache"},"application/x-font-type1":{"source":"apache","extensions":["pfa","pfb","pfm","afm"]},"application/x-font-vfont":{"source":"apache"},"application/x-freearc":{"source":"apache","extensions":["arc"]},"application/x-futuresplash":{"source":"apache","extensions":["spl"]},"application/x-gca-compressed":{"source":"apache","extensions":["gca"]},"application/x-glulx":{"source":"apache","extensions":["ulx"]},"application/x-gnumeric":{"source":"apache","extensions":["gnumeric"]},"application/x-gramps-xml":{"source":"apache","extensions":["gramps"]},"application/x-gtar":{"source":"apache","extensions":["gtar"]},"application/x-gzip":{"source":"apache"},"application/x-hdf":{"source":"apache","extensions":["hdf"]},"application/x-httpd-php":{"compressible":true,"extensions":["php"]},"application/x-install-instructions":{"source":"apache","extensions":["install"]},"application/x-iso9660-image":{"source":"apache","extensions":["iso"]},"application/x-iwork-keynote-sffkey":{"extensions":["key"]},"application/x-iwork-numbers-sffnumbers":{"extensions":["numbers"]},"application/x-iwork-pages-sffpages":{"extensions":["pages"]},"application/x-java-archive-diff":{"source":"nginx","extensions":["jardiff"]},"application/x-java-jnlp-file":{"source":"apache","compressible":false,"extensions":["jnlp"]},"application/x-javascript":{"compressible":true},"application/x-keepass2":{"extensions":["kdbx"]},"application/x-latex":{"source":"apache","compressible":false,"extensions":["latex"]},"application/x-lua-bytecode":{"extensions":["luac"]},"application/x-lzh-compressed":{"source":"apache","extensions":["lzh","lha"]},"application/x-makeself":{"source":"nginx","extensions":["run"]},"application/x-mie":{"source":"apache","extensions":["mie"]},"application/x-mobipocket-ebook":{"source":"apache","extensions":["prc","mobi"]},"application/x-mpegurl":{"compressible":false},"application/x-ms-application":{"source":"apache","extensions":["application"]},"application/x-ms-shortcut":{"source":"apache","extensions":["lnk"]},"application/x-ms-wmd":{"source":"apache","extensions":["wmd"]},"application/x-ms-wmz":{"source":"apache","extensions":["wmz"]},"application/x-ms-xbap":{"source":"apache","extensions":["xbap"]},"application/x-msaccess":{"source":"apache","extensions":["mdb"]},"application/x-msbinder":{"source":"apache","extensions":["obd"]},"application/x-mscardfile":{"source":"apache","extensions":["crd"]},"application/x-msclip":{"source":"apache","extensions":["clp"]},"application/x-msdos-program":{"extensions":["exe"]},"application/x-msdownload":{"source":"apache","extensions":["exe","dll","com","bat","msi"]},"application/x-msmediaview":{"source":"apache","extensions":["mvb","m13","m14"]},"application/x-msmetafile":{"source":"apache","extensions":["wmf","wmz","emf","emz"]},"application/x-msmoney":{"source":"apache","extensions":["mny"]},"application/x-mspublisher":{"source":"apache","extensions":["pub"]},"application/x-msschedule":{"source":"apache","extensions":["scd"]},"application/x-msterminal":{"source":"apache","extensions":["trm"]},"application/x-mswrite":{"source":"apache","extensions":["wri"]},"application/x-netcdf":{"source":"apache","extensions":["nc","cdf"]},"application/x-ns-proxy-autoconfig":{"compressible":true,"extensions":["pac"]},"application/x-nzb":{"source":"apache","extensions":["nzb"]},"application/x-perl":{"source":"nginx","extensions":["pl","pm"]},"application/x-pilot":{"source":"nginx","extensions":["prc","pdb"]},"application/x-pkcs12":{"source":"apache","compressible":false,"extensions":["p12","pfx"]},"application/x-pkcs7-certificates":{"source":"apache","extensions":["p7b","spc"]},"application/x-pkcs7-certreqresp":{"source":"apache","extensions":["p7r"]},"application/x-pki-message":{"source":"iana"},"application/x-rar-compressed":{"source":"apache","compressible":false,"extensions":["rar"]},"application/x-redhat-package-manager":{"source":"nginx","extensions":["rpm"]},"application/x-research-info-systems":{"source":"apache","extensions":["ris"]},"application/x-sea":{"source":"nginx","extensions":["sea"]},"application/x-sh":{"source":"apache","compressible":true,"extensions":["sh"]},"application/x-shar":{"source":"apache","extensions":["shar"]},"application/x-shockwave-flash":{"source":"apache","compressible":false,"extensions":["swf"]},"application/x-silverlight-app":{"source":"apache","extensions":["xap"]},"application/x-sql":{"source":"apache","extensions":["sql"]},"application/x-stuffit":{"source":"apache","compressible":false,"extensions":["sit"]},"application/x-stuffitx":{"source":"apache","extensions":["sitx"]},"application/x-subrip":{"source":"apache","extensions":["srt"]},"application/x-sv4cpio":{"source":"apache","extensions":["sv4cpio"]},"application/x-sv4crc":{"source":"apache","extensions":["sv4crc"]},"application/x-t3vm-image":{"source":"apache","extensions":["t3"]},"application/x-tads":{"source":"apache","extensions":["gam"]},"application/x-tar":{"source":"apache","compressible":true,"extensions":["tar"]},"application/x-tcl":{"source":"apache","extensions":["tcl","tk"]},"application/x-tex":{"source":"apache","extensions":["tex"]},"application/x-tex-tfm":{"source":"apache","extensions":["tfm"]},"application/x-texinfo":{"source":"apache","extensions":["texinfo","texi"]},"application/x-tgif":{"source":"apache","extensions":["obj"]},"application/x-ustar":{"source":"apache","extensions":["ustar"]},"application/x-virtualbox-hdd":{"compressible":true,"extensions":["hdd"]},"application/x-virtualbox-ova":{"compressible":true,"extensions":["ova"]},"application/x-virtualbox-ovf":{"compressible":true,"extensions":["ovf"]},"application/x-virtualbox-vbox":{"compressible":true,"extensions":["vbox"]},"application/x-virtualbox-vbox-extpack":{"compressible":false,"extensions":["vbox-extpack"]},"application/x-virtualbox-vdi":{"compressible":true,"extensions":["vdi"]},"application/x-virtualbox-vhd":{"compressible":true,"extensions":["vhd"]},"application/x-virtualbox-vmdk":{"compressible":true,"extensions":["vmdk"]},"application/x-wais-source":{"source":"apache","extensions":["src"]},"application/x-web-app-manifest+json":{"compressible":true,"extensions":["webapp"]},"application/x-www-form-urlencoded":{"source":"iana","compressible":true},"application/x-x509-ca-cert":{"source":"iana","extensions":["der","crt","pem"]},"application/x-x509-ca-ra-cert":{"source":"iana"},"application/x-x509-next-ca-cert":{"source":"iana"},"application/x-xfig":{"source":"apache","extensions":["fig"]},"application/x-xliff+xml":{"source":"apache","compressible":true,"extensions":["xlf"]},"application/x-xpinstall":{"source":"apache","compressible":false,"extensions":["xpi"]},"application/x-xz":{"source":"apache","extensions":["xz"]},"application/x-zmachine":{"source":"apache","extensions":["z1","z2","z3","z4","z5","z6","z7","z8"]},"application/x400-bp":{"source":"iana"},"application/xacml+xml":{"source":"iana","compressible":true},"application/xaml+xml":{"source":"apache","compressible":true,"extensions":["xaml"]},"application/xcap-att+xml":{"source":"iana","compressible":true,"extensions":["xav"]},"application/xcap-caps+xml":{"source":"iana","compressible":true,"extensions":["xca"]},"application/xcap-diff+xml":{"source":"iana","compressible":true,"extensions":["xdf"]},"application/xcap-el+xml":{"source":"iana","compressible":true,"extensions":["xel"]},"application/xcap-error+xml":{"source":"iana","compressible":true},"application/xcap-ns+xml":{"source":"iana","compressible":true,"extensions":["xns"]},"application/xcon-conference-info+xml":{"source":"iana","compressible":true},"application/xcon-conference-info-diff+xml":{"source":"iana","compressible":true},"application/xenc+xml":{"source":"iana","compressible":true,"extensions":["xenc"]},"application/xhtml+xml":{"source":"iana","compressible":true,"extensions":["xhtml","xht"]},"application/xhtml-voice+xml":{"source":"apache","compressible":true},"application/xliff+xml":{"source":"iana","compressible":true,"extensions":["xlf"]},"application/xml":{"source":"iana","compressible":true,"extensions":["xml","xsl","xsd","rng"]},"application/xml-dtd":{"source":"iana","compressible":true,"extensions":["dtd"]},"application/xml-external-parsed-entity":{"source":"iana"},"application/xml-patch+xml":{"source":"iana","compressible":true},"application/xmpp+xml":{"source":"iana","compressible":true},"application/xop+xml":{"source":"iana","compressible":true,"extensions":["xop"]},"application/xproc+xml":{"source":"apache","compressible":true,"extensions":["xpl"]},"application/xslt+xml":{"source":"iana","compressible":true,"extensions":["xsl","xslt"]},"application/xspf+xml":{"source":"apache","compressible":true,"extensions":["xspf"]},"application/xv+xml":{"source":"iana","compressible":true,"extensions":["mxml","xhvml","xvml","xvm"]},"application/yang":{"source":"iana","extensions":["yang"]},"application/yang-data+json":{"source":"iana","compressible":true},"application/yang-data+xml":{"source":"iana","compressible":true},"application/yang-patch+json":{"source":"iana","compressible":true},"application/yang-patch+xml":{"source":"iana","compressible":true},"application/yin+xml":{"source":"iana","compressible":true,"extensions":["yin"]},"application/zip":{"source":"iana","compressible":false,"extensions":["zip"]},"application/zlib":{"source":"iana"},"application/zstd":{"source":"iana"},"audio/1d-interleaved-parityfec":{"source":"iana"},"audio/32kadpcm":{"source":"iana"},"audio/3gpp":{"source":"iana","compressible":false,"extensions":["3gpp"]},"audio/3gpp2":{"source":"iana"},"audio/aac":{"source":"iana"},"audio/ac3":{"source":"iana"},"audio/adpcm":{"source":"apache","extensions":["adp"]},"audio/amr":{"source":"iana","extensions":["amr"]},"audio/amr-wb":{"source":"iana"},"audio/amr-wb+":{"source":"iana"},"audio/aptx":{"source":"iana"},"audio/asc":{"source":"iana"},"audio/atrac-advanced-lossless":{"source":"iana"},"audio/atrac-x":{"source":"iana"},"audio/atrac3":{"source":"iana"},"audio/basic":{"source":"iana","compressible":false,"extensions":["au","snd"]},"audio/bv16":{"source":"iana"},"audio/bv32":{"source":"iana"},"audio/clearmode":{"source":"iana"},"audio/cn":{"source":"iana"},"audio/dat12":{"source":"iana"},"audio/dls":{"source":"iana"},"audio/dsr-es201108":{"source":"iana"},"audio/dsr-es202050":{"source":"iana"},"audio/dsr-es202211":{"source":"iana"},"audio/dsr-es202212":{"source":"iana"},"audio/dv":{"source":"iana"},"audio/dvi4":{"source":"iana"},"audio/eac3":{"source":"iana"},"audio/encaprtp":{"source":"iana"},"audio/evrc":{"source":"iana"},"audio/evrc-qcp":{"source":"iana"},"audio/evrc0":{"source":"iana"},"audio/evrc1":{"source":"iana"},"audio/evrcb":{"source":"iana"},"audio/evrcb0":{"source":"iana"},"audio/evrcb1":{"source":"iana"},"audio/evrcnw":{"source":"iana"},"audio/evrcnw0":{"source":"iana"},"audio/evrcnw1":{"source":"iana"},"audio/evrcwb":{"source":"iana"},"audio/evrcwb0":{"source":"iana"},"audio/evrcwb1":{"source":"iana"},"audio/evs":{"source":"iana"},"audio/flexfec":{"source":"iana"},"audio/fwdred":{"source":"iana"},"audio/g711-0":{"source":"iana"},"audio/g719":{"source":"iana"},"audio/g722":{"source":"iana"},"audio/g7221":{"source":"iana"},"audio/g723":{"source":"iana"},"audio/g726-16":{"source":"iana"},"audio/g726-24":{"source":"iana"},"audio/g726-32":{"source":"iana"},"audio/g726-40":{"source":"iana"},"audio/g728":{"source":"iana"},"audio/g729":{"source":"iana"},"audio/g7291":{"source":"iana"},"audio/g729d":{"source":"iana"},"audio/g729e":{"source":"iana"},"audio/gsm":{"source":"iana"},"audio/gsm-efr":{"source":"iana"},"audio/gsm-hr-08":{"source":"iana"},"audio/ilbc":{"source":"iana"},"audio/ip-mr_v2.5":{"source":"iana"},"audio/isac":{"source":"apache"},"audio/l16":{"source":"iana"},"audio/l20":{"source":"iana"},"audio/l24":{"source":"iana","compressible":false},"audio/l8":{"source":"iana"},"audio/lpc":{"source":"iana"},"audio/melp":{"source":"iana"},"audio/melp1200":{"source":"iana"},"audio/melp2400":{"source":"iana"},"audio/melp600":{"source":"iana"},"audio/mhas":{"source":"iana"},"audio/midi":{"source":"apache","extensions":["mid","midi","kar","rmi"]},"audio/mobile-xmf":{"source":"iana","extensions":["mxmf"]},"audio/mp3":{"compressible":false,"extensions":["mp3"]},"audio/mp4":{"source":"iana","compressible":false,"extensions":["m4a","mp4a"]},"audio/mp4a-latm":{"source":"iana"},"audio/mpa":{"source":"iana"},"audio/mpa-robust":{"source":"iana"},"audio/mpeg":{"source":"iana","compressible":false,"extensions":["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/mpeg4-generic":{"source":"iana"},"audio/musepack":{"source":"apache"},"audio/ogg":{"source":"iana","compressible":false,"extensions":["oga","ogg","spx","opus"]},"audio/opus":{"source":"iana"},"audio/parityfec":{"source":"iana"},"audio/pcma":{"source":"iana"},"audio/pcma-wb":{"source":"iana"},"audio/pcmu":{"source":"iana"},"audio/pcmu-wb":{"source":"iana"},"audio/prs.sid":{"source":"iana"},"audio/qcelp":{"source":"iana"},"audio/raptorfec":{"source":"iana"},"audio/red":{"source":"iana"},"audio/rtp-enc-aescm128":{"source":"iana"},"audio/rtp-midi":{"source":"iana"},"audio/rtploopback":{"source":"iana"},"audio/rtx":{"source":"iana"},"audio/s3m":{"source":"apache","extensions":["s3m"]},"audio/scip":{"source":"iana"},"audio/silk":{"source":"apache","extensions":["sil"]},"audio/smv":{"source":"iana"},"audio/smv-qcp":{"source":"iana"},"audio/smv0":{"source":"iana"},"audio/sofa":{"source":"iana"},"audio/sp-midi":{"source":"iana"},"audio/speex":{"source":"iana"},"audio/t140c":{"source":"iana"},"audio/t38":{"source":"iana"},"audio/telephone-event":{"source":"iana"},"audio/tetra_acelp":{"source":"iana"},"audio/tetra_acelp_bb":{"source":"iana"},"audio/tone":{"source":"iana"},"audio/tsvcis":{"source":"iana"},"audio/uemclip":{"source":"iana"},"audio/ulpfec":{"source":"iana"},"audio/usac":{"source":"iana"},"audio/vdvi":{"source":"iana"},"audio/vmr-wb":{"source":"iana"},"audio/vnd.3gpp.iufp":{"source":"iana"},"audio/vnd.4sb":{"source":"iana"},"audio/vnd.audiokoz":{"source":"iana"},"audio/vnd.celp":{"source":"iana"},"audio/vnd.cisco.nse":{"source":"iana"},"audio/vnd.cmles.radio-events":{"source":"iana"},"audio/vnd.cns.anp1":{"source":"iana"},"audio/vnd.cns.inf1":{"source":"iana"},"audio/vnd.dece.audio":{"source":"iana","extensions":["uva","uvva"]},"audio/vnd.digital-winds":{"source":"iana","extensions":["eol"]},"audio/vnd.dlna.adts":{"source":"iana"},"audio/vnd.dolby.heaac.1":{"source":"iana"},"audio/vnd.dolby.heaac.2":{"source":"iana"},"audio/vnd.dolby.mlp":{"source":"iana"},"audio/vnd.dolby.mps":{"source":"iana"},"audio/vnd.dolby.pl2":{"source":"iana"},"audio/vnd.dolby.pl2x":{"source":"iana"},"audio/vnd.dolby.pl2z":{"source":"iana"},"audio/vnd.dolby.pulse.1":{"source":"iana"},"audio/vnd.dra":{"source":"iana","extensions":["dra"]},"audio/vnd.dts":{"source":"iana","extensions":["dts"]},"audio/vnd.dts.hd":{"source":"iana","extensions":["dtshd"]},"audio/vnd.dts.uhd":{"source":"iana"},"audio/vnd.dvb.file":{"source":"iana"},"audio/vnd.everad.plj":{"source":"iana"},"audio/vnd.hns.audio":{"source":"iana"},"audio/vnd.lucent.voice":{"source":"iana","extensions":["lvp"]},"audio/vnd.ms-playready.media.pya":{"source":"iana","extensions":["pya"]},"audio/vnd.nokia.mobile-xmf":{"source":"iana"},"audio/vnd.nortel.vbk":{"source":"iana"},"audio/vnd.nuera.ecelp4800":{"source":"iana","extensions":["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{"source":"iana","extensions":["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{"source":"iana","extensions":["ecelp9600"]},"audio/vnd.octel.sbc":{"source":"iana"},"audio/vnd.presonus.multitrack":{"source":"iana"},"audio/vnd.qcelp":{"source":"iana"},"audio/vnd.rhetorex.32kadpcm":{"source":"iana"},"audio/vnd.rip":{"source":"iana","extensions":["rip"]},"audio/vnd.rn-realaudio":{"compressible":false},"audio/vnd.sealedmedia.softseal.mpeg":{"source":"iana"},"audio/vnd.vmx.cvsd":{"source":"iana"},"audio/vnd.wave":{"compressible":false},"audio/vorbis":{"source":"iana","compressible":false},"audio/vorbis-config":{"source":"iana"},"audio/wav":{"compressible":false,"extensions":["wav"]},"audio/wave":{"compressible":false,"extensions":["wav"]},"audio/webm":{"source":"apache","compressible":false,"extensions":["weba"]},"audio/x-aac":{"source":"apache","compressible":false,"extensions":["aac"]},"audio/x-aiff":{"source":"apache","extensions":["aif","aiff","aifc"]},"audio/x-caf":{"source":"apache","compressible":false,"extensions":["caf"]},"audio/x-flac":{"source":"apache","extensions":["flac"]},"audio/x-m4a":{"source":"nginx","extensions":["m4a"]},"audio/x-matroska":{"source":"apache","extensions":["mka"]},"audio/x-mpegurl":{"source":"apache","extensions":["m3u"]},"audio/x-ms-wax":{"source":"apache","extensions":["wax"]},"audio/x-ms-wma":{"source":"apache","extensions":["wma"]},"audio/x-pn-realaudio":{"source":"apache","extensions":["ram","ra"]},"audio/x-pn-realaudio-plugin":{"source":"apache","extensions":["rmp"]},"audio/x-realaudio":{"source":"nginx","extensions":["ra"]},"audio/x-tta":{"source":"apache"},"audio/x-wav":{"source":"apache","extensions":["wav"]},"audio/xm":{"source":"apache","extensions":["xm"]},"chemical/x-cdx":{"source":"apache","extensions":["cdx"]},"chemical/x-cif":{"source":"apache","extensions":["cif"]},"chemical/x-cmdf":{"source":"apache","extensions":["cmdf"]},"chemical/x-cml":{"source":"apache","extensions":["cml"]},"chemical/x-csml":{"source":"apache","extensions":["csml"]},"chemical/x-pdb":{"source":"apache"},"chemical/x-xyz":{"source":"apache","extensions":["xyz"]},"font/collection":{"source":"iana","extensions":["ttc"]},"font/otf":{"source":"iana","compressible":true,"extensions":["otf"]},"font/sfnt":{"source":"iana"},"font/ttf":{"source":"iana","compressible":true,"extensions":["ttf"]},"font/woff":{"source":"iana","extensions":["woff"]},"font/woff2":{"source":"iana","extensions":["woff2"]},"image/aces":{"source":"iana","extensions":["exr"]},"image/apng":{"compressible":false,"extensions":["apng"]},"image/avci":{"source":"iana","extensions":["avci"]},"image/avcs":{"source":"iana","extensions":["avcs"]},"image/avif":{"source":"iana","compressible":false,"extensions":["avif"]},"image/bmp":{"source":"iana","compressible":true,"extensions":["bmp"]},"image/cgm":{"source":"iana","extensions":["cgm"]},"image/dicom-rle":{"source":"iana","extensions":["drle"]},"image/emf":{"source":"iana","extensions":["emf"]},"image/fits":{"source":"iana","extensions":["fits"]},"image/g3fax":{"source":"iana","extensions":["g3"]},"image/gif":{"source":"iana","compressible":false,"extensions":["gif"]},"image/heic":{"source":"iana","extensions":["heic"]},"image/heic-sequence":{"source":"iana","extensions":["heics"]},"image/heif":{"source":"iana","extensions":["heif"]},"image/heif-sequence":{"source":"iana","extensions":["heifs"]},"image/hej2k":{"source":"iana","extensions":["hej2"]},"image/hsj2":{"source":"iana","extensions":["hsj2"]},"image/ief":{"source":"iana","extensions":["ief"]},"image/jls":{"source":"iana","extensions":["jls"]},"image/jp2":{"source":"iana","compressible":false,"extensions":["jp2","jpg2"]},"image/jpeg":{"source":"iana","compressible":false,"extensions":["jpeg","jpg","jpe"]},"image/jph":{"source":"iana","extensions":["jph"]},"image/jphc":{"source":"iana","extensions":["jhc"]},"image/jpm":{"source":"iana","compressible":false,"extensions":["jpm"]},"image/jpx":{"source":"iana","compressible":false,"extensions":["jpx","jpf"]},"image/jxr":{"source":"iana","extensions":["jxr"]},"image/jxra":{"source":"iana","extensions":["jxra"]},"image/jxrs":{"source":"iana","extensions":["jxrs"]},"image/jxs":{"source":"iana","extensions":["jxs"]},"image/jxsc":{"source":"iana","extensions":["jxsc"]},"image/jxsi":{"source":"iana","extensions":["jxsi"]},"image/jxss":{"source":"iana","extensions":["jxss"]},"image/ktx":{"source":"iana","extensions":["ktx"]},"image/ktx2":{"source":"iana","extensions":["ktx2"]},"image/naplps":{"source":"iana"},"image/pjpeg":{"compressible":false},"image/png":{"source":"iana","compressible":false,"extensions":["png"]},"image/prs.btif":{"source":"iana","extensions":["btif"]},"image/prs.pti":{"source":"iana","extensions":["pti"]},"image/pwg-raster":{"source":"iana"},"image/sgi":{"source":"apache","extensions":["sgi"]},"image/svg+xml":{"source":"iana","compressible":true,"extensions":["svg","svgz"]},"image/t38":{"source":"iana","extensions":["t38"]},"image/tiff":{"source":"iana","compressible":false,"extensions":["tif","tiff"]},"image/tiff-fx":{"source":"iana","extensions":["tfx"]},"image/vnd.adobe.photoshop":{"source":"iana","compressible":true,"extensions":["psd"]},"image/vnd.airzip.accelerator.azv":{"source":"iana","extensions":["azv"]},"image/vnd.cns.inf2":{"source":"iana"},"image/vnd.dece.graphic":{"source":"iana","extensions":["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{"source":"iana","extensions":["djvu","djv"]},"image/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"image/vnd.dwg":{"source":"iana","extensions":["dwg"]},"image/vnd.dxf":{"source":"iana","extensions":["dxf"]},"image/vnd.fastbidsheet":{"source":"iana","extensions":["fbs"]},"image/vnd.fpx":{"source":"iana","extensions":["fpx"]},"image/vnd.fst":{"source":"iana","extensions":["fst"]},"image/vnd.fujixerox.edmics-mmr":{"source":"iana","extensions":["mmr"]},"image/vnd.fujixerox.edmics-rlc":{"source":"iana","extensions":["rlc"]},"image/vnd.globalgraphics.pgb":{"source":"iana"},"image/vnd.microsoft.icon":{"source":"iana","compressible":true,"extensions":["ico"]},"image/vnd.mix":{"source":"iana"},"image/vnd.mozilla.apng":{"source":"iana"},"image/vnd.ms-dds":{"compressible":true,"extensions":["dds"]},"image/vnd.ms-modi":{"source":"iana","extensions":["mdi"]},"image/vnd.ms-photo":{"source":"apache","extensions":["wdp"]},"image/vnd.net-fpx":{"source":"iana","extensions":["npx"]},"image/vnd.pco.b16":{"source":"iana","extensions":["b16"]},"image/vnd.radiance":{"source":"iana"},"image/vnd.sealed.png":{"source":"iana"},"image/vnd.sealedmedia.softseal.gif":{"source":"iana"},"image/vnd.sealedmedia.softseal.jpg":{"source":"iana"},"image/vnd.svf":{"source":"iana"},"image/vnd.tencent.tap":{"source":"iana","extensions":["tap"]},"image/vnd.valve.source.texture":{"source":"iana","extensions":["vtf"]},"image/vnd.wap.wbmp":{"source":"iana","extensions":["wbmp"]},"image/vnd.xiff":{"source":"iana","extensions":["xif"]},"image/vnd.zbrush.pcx":{"source":"iana","extensions":["pcx"]},"image/webp":{"source":"apache","extensions":["webp"]},"image/wmf":{"source":"iana","extensions":["wmf"]},"image/x-3ds":{"source":"apache","extensions":["3ds"]},"image/x-cmu-raster":{"source":"apache","extensions":["ras"]},"image/x-cmx":{"source":"apache","extensions":["cmx"]},"image/x-freehand":{"source":"apache","extensions":["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{"source":"apache","compressible":true,"extensions":["ico"]},"image/x-jng":{"source":"nginx","extensions":["jng"]},"image/x-mrsid-image":{"source":"apache","extensions":["sid"]},"image/x-ms-bmp":{"source":"nginx","compressible":true,"extensions":["bmp"]},"image/x-pcx":{"source":"apache","extensions":["pcx"]},"image/x-pict":{"source":"apache","extensions":["pic","pct"]},"image/x-portable-anymap":{"source":"apache","extensions":["pnm"]},"image/x-portable-bitmap":{"source":"apache","extensions":["pbm"]},"image/x-portable-graymap":{"source":"apache","extensions":["pgm"]},"image/x-portable-pixmap":{"source":"apache","extensions":["ppm"]},"image/x-rgb":{"source":"apache","extensions":["rgb"]},"image/x-tga":{"source":"apache","extensions":["tga"]},"image/x-xbitmap":{"source":"apache","extensions":["xbm"]},"image/x-xcf":{"compressible":false},"image/x-xpixmap":{"source":"apache","extensions":["xpm"]},"image/x-xwindowdump":{"source":"apache","extensions":["xwd"]},"message/cpim":{"source":"iana"},"message/delivery-status":{"source":"iana"},"message/disposition-notification":{"source":"iana","extensions":["disposition-notification"]},"message/external-body":{"source":"iana"},"message/feedback-report":{"source":"iana"},"message/global":{"source":"iana","extensions":["u8msg"]},"message/global-delivery-status":{"source":"iana","extensions":["u8dsn"]},"message/global-disposition-notification":{"source":"iana","extensions":["u8mdn"]},"message/global-headers":{"source":"iana","extensions":["u8hdr"]},"message/http":{"source":"iana","compressible":false},"message/imdn+xml":{"source":"iana","compressible":true},"message/news":{"source":"iana"},"message/partial":{"source":"iana","compressible":false},"message/rfc822":{"source":"iana","compressible":true,"extensions":["eml","mime"]},"message/s-http":{"source":"iana"},"message/sip":{"source":"iana"},"message/sipfrag":{"source":"iana"},"message/tracking-status":{"source":"iana"},"message/vnd.si.simp":{"source":"iana"},"message/vnd.wfa.wsc":{"source":"iana","extensions":["wsc"]},"model/3mf":{"source":"iana","extensions":["3mf"]},"model/e57":{"source":"iana"},"model/gltf+json":{"source":"iana","compressible":true,"extensions":["gltf"]},"model/gltf-binary":{"source":"iana","compressible":true,"extensions":["glb"]},"model/iges":{"source":"iana","compressible":false,"extensions":["igs","iges"]},"model/mesh":{"source":"iana","compressible":false,"extensions":["msh","mesh","silo"]},"model/mtl":{"source":"iana","extensions":["mtl"]},"model/obj":{"source":"iana","extensions":["obj"]},"model/step":{"source":"iana"},"model/step+xml":{"source":"iana","compressible":true,"extensions":["stpx"]},"model/step+zip":{"source":"iana","compressible":false,"extensions":["stpz"]},"model/step-xml+zip":{"source":"iana","compressible":false,"extensions":["stpxz"]},"model/stl":{"source":"iana","extensions":["stl"]},"model/vnd.collada+xml":{"source":"iana","compressible":true,"extensions":["dae"]},"model/vnd.dwf":{"source":"iana","extensions":["dwf"]},"model/vnd.flatland.3dml":{"source":"iana"},"model/vnd.gdl":{"source":"iana","extensions":["gdl"]},"model/vnd.gs-gdl":{"source":"apache"},"model/vnd.gs.gdl":{"source":"iana"},"model/vnd.gtw":{"source":"iana","extensions":["gtw"]},"model/vnd.moml+xml":{"source":"iana","compressible":true},"model/vnd.mts":{"source":"iana","extensions":["mts"]},"model/vnd.opengex":{"source":"iana","extensions":["ogex"]},"model/vnd.parasolid.transmit.binary":{"source":"iana","extensions":["x_b"]},"model/vnd.parasolid.transmit.text":{"source":"iana","extensions":["x_t"]},"model/vnd.pytha.pyox":{"source":"iana"},"model/vnd.rosette.annotated-data-model":{"source":"iana"},"model/vnd.sap.vds":{"source":"iana","extensions":["vds"]},"model/vnd.usdz+zip":{"source":"iana","compressible":false,"extensions":["usdz"]},"model/vnd.valve.source.compiled-map":{"source":"iana","extensions":["bsp"]},"model/vnd.vtu":{"source":"iana","extensions":["vtu"]},"model/vrml":{"source":"iana","compressible":false,"extensions":["wrl","vrml"]},"model/x3d+binary":{"source":"apache","compressible":false,"extensions":["x3db","x3dbz"]},"model/x3d+fastinfoset":{"source":"iana","extensions":["x3db"]},"model/x3d+vrml":{"source":"apache","compressible":false,"extensions":["x3dv","x3dvz"]},"model/x3d+xml":{"source":"iana","compressible":true,"extensions":["x3d","x3dz"]},"model/x3d-vrml":{"source":"iana","extensions":["x3dv"]},"multipart/alternative":{"source":"iana","compressible":false},"multipart/appledouble":{"source":"iana"},"multipart/byteranges":{"source":"iana"},"multipart/digest":{"source":"iana"},"multipart/encrypted":{"source":"iana","compressible":false},"multipart/form-data":{"source":"iana","compressible":false},"multipart/header-set":{"source":"iana"},"multipart/mixed":{"source":"iana"},"multipart/multilingual":{"source":"iana"},"multipart/parallel":{"source":"iana"},"multipart/related":{"source":"iana","compressible":false},"multipart/report":{"source":"iana"},"multipart/signed":{"source":"iana","compressible":false},"multipart/vnd.bint.med-plus":{"source":"iana"},"multipart/voice-message":{"source":"iana"},"multipart/x-mixed-replace":{"source":"iana"},"text/1d-interleaved-parityfec":{"source":"iana"},"text/cache-manifest":{"source":"iana","compressible":true,"extensions":["appcache","manifest"]},"text/calendar":{"source":"iana","extensions":["ics","ifb"]},"text/calender":{"compressible":true},"text/cmd":{"compressible":true},"text/coffeescript":{"extensions":["coffee","litcoffee"]},"text/cql":{"source":"iana"},"text/cql-expression":{"source":"iana"},"text/cql-identifier":{"source":"iana"},"text/css":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["css"]},"text/csv":{"source":"iana","compressible":true,"extensions":["csv"]},"text/csv-schema":{"source":"iana"},"text/directory":{"source":"iana"},"text/dns":{"source":"iana"},"text/ecmascript":{"source":"iana"},"text/encaprtp":{"source":"iana"},"text/enriched":{"source":"iana"},"text/fhirpath":{"source":"iana"},"text/flexfec":{"source":"iana"},"text/fwdred":{"source":"iana"},"text/gff3":{"source":"iana"},"text/grammar-ref-list":{"source":"iana"},"text/html":{"source":"iana","compressible":true,"extensions":["html","htm","shtml"]},"text/jade":{"extensions":["jade"]},"text/javascript":{"source":"iana","compressible":true},"text/jcr-cnd":{"source":"iana"},"text/jsx":{"compressible":true,"extensions":["jsx"]},"text/less":{"compressible":true,"extensions":["less"]},"text/markdown":{"source":"iana","compressible":true,"extensions":["markdown","md"]},"text/mathml":{"source":"nginx","extensions":["mml"]},"text/mdx":{"compressible":true,"extensions":["mdx"]},"text/mizar":{"source":"iana"},"text/n3":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["n3"]},"text/parameters":{"source":"iana","charset":"UTF-8"},"text/parityfec":{"source":"iana"},"text/plain":{"source":"iana","compressible":true,"extensions":["txt","text","conf","def","list","log","in","ini"]},"text/provenance-notation":{"source":"iana","charset":"UTF-8"},"text/prs.fallenstein.rst":{"source":"iana"},"text/prs.lines.tag":{"source":"iana","extensions":["dsc"]},"text/prs.prop.logic":{"source":"iana"},"text/raptorfec":{"source":"iana"},"text/red":{"source":"iana"},"text/rfc822-headers":{"source":"iana"},"text/richtext":{"source":"iana","compressible":true,"extensions":["rtx"]},"text/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"text/rtp-enc-aescm128":{"source":"iana"},"text/rtploopback":{"source":"iana"},"text/rtx":{"source":"iana"},"text/sgml":{"source":"iana","extensions":["sgml","sgm"]},"text/shaclc":{"source":"iana"},"text/shex":{"source":"iana","extensions":["shex"]},"text/slim":{"extensions":["slim","slm"]},"text/spdx":{"source":"iana","extensions":["spdx"]},"text/strings":{"source":"iana"},"text/stylus":{"extensions":["stylus","styl"]},"text/t140":{"source":"iana"},"text/tab-separated-values":{"source":"iana","compressible":true,"extensions":["tsv"]},"text/troff":{"source":"iana","extensions":["t","tr","roff","man","me","ms"]},"text/turtle":{"source":"iana","charset":"UTF-8","extensions":["ttl"]},"text/ulpfec":{"source":"iana"},"text/uri-list":{"source":"iana","compressible":true,"extensions":["uri","uris","urls"]},"text/vcard":{"source":"iana","compressible":true,"extensions":["vcard"]},"text/vnd.a":{"source":"iana"},"text/vnd.abc":{"source":"iana"},"text/vnd.ascii-art":{"source":"iana"},"text/vnd.curl":{"source":"iana","extensions":["curl"]},"text/vnd.curl.dcurl":{"source":"apache","extensions":["dcurl"]},"text/vnd.curl.mcurl":{"source":"apache","extensions":["mcurl"]},"text/vnd.curl.scurl":{"source":"apache","extensions":["scurl"]},"text/vnd.debian.copyright":{"source":"iana","charset":"UTF-8"},"text/vnd.dmclientscript":{"source":"iana"},"text/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"text/vnd.esmertec.theme-descriptor":{"source":"iana","charset":"UTF-8"},"text/vnd.familysearch.gedcom":{"source":"iana","extensions":["ged"]},"text/vnd.ficlab.flt":{"source":"iana"},"text/vnd.fly":{"source":"iana","extensions":["fly"]},"text/vnd.fmi.flexstor":{"source":"iana","extensions":["flx"]},"text/vnd.gml":{"source":"iana"},"text/vnd.graphviz":{"source":"iana","extensions":["gv"]},"text/vnd.hans":{"source":"iana"},"text/vnd.hgl":{"source":"iana"},"text/vnd.in3d.3dml":{"source":"iana","extensions":["3dml"]},"text/vnd.in3d.spot":{"source":"iana","extensions":["spot"]},"text/vnd.iptc.newsml":{"source":"iana"},"text/vnd.iptc.nitf":{"source":"iana"},"text/vnd.latex-z":{"source":"iana"},"text/vnd.motorola.reflex":{"source":"iana"},"text/vnd.ms-mediapackage":{"source":"iana"},"text/vnd.net2phone.commcenter.command":{"source":"iana"},"text/vnd.radisys.msml-basic-layout":{"source":"iana"},"text/vnd.senx.warpscript":{"source":"iana"},"text/vnd.si.uricatalogue":{"source":"iana"},"text/vnd.sosi":{"source":"iana"},"text/vnd.sun.j2me.app-descriptor":{"source":"iana","charset":"UTF-8","extensions":["jad"]},"text/vnd.trolltech.linguist":{"source":"iana","charset":"UTF-8"},"text/vnd.wap.si":{"source":"iana"},"text/vnd.wap.sl":{"source":"iana"},"text/vnd.wap.wml":{"source":"iana","extensions":["wml"]},"text/vnd.wap.wmlscript":{"source":"iana","extensions":["wmls"]},"text/vtt":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["vtt"]},"text/x-asm":{"source":"apache","extensions":["s","asm"]},"text/x-c":{"source":"apache","extensions":["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{"source":"nginx","extensions":["htc"]},"text/x-fortran":{"source":"apache","extensions":["f","for","f77","f90"]},"text/x-gwt-rpc":{"compressible":true},"text/x-handlebars-template":{"extensions":["hbs"]},"text/x-java-source":{"source":"apache","extensions":["java"]},"text/x-jquery-tmpl":{"compressible":true},"text/x-lua":{"extensions":["lua"]},"text/x-markdown":{"compressible":true,"extensions":["mkd"]},"text/x-nfo":{"source":"apache","extensions":["nfo"]},"text/x-opml":{"source":"apache","extensions":["opml"]},"text/x-org":{"compressible":true,"extensions":["org"]},"text/x-pascal":{"source":"apache","extensions":["p","pas"]},"text/x-processing":{"compressible":true,"extensions":["pde"]},"text/x-sass":{"extensions":["sass"]},"text/x-scss":{"extensions":["scss"]},"text/x-setext":{"source":"apache","extensions":["etx"]},"text/x-sfv":{"source":"apache","extensions":["sfv"]},"text/x-suse-ymp":{"compressible":true,"extensions":["ymp"]},"text/x-uuencode":{"source":"apache","extensions":["uu"]},"text/x-vcalendar":{"source":"apache","extensions":["vcs"]},"text/x-vcard":{"source":"apache","extensions":["vcf"]},"text/xml":{"source":"iana","compressible":true,"extensions":["xml"]},"text/xml-external-parsed-entity":{"source":"iana"},"text/yaml":{"compressible":true,"extensions":["yaml","yml"]},"video/1d-interleaved-parityfec":{"source":"iana"},"video/3gpp":{"source":"iana","extensions":["3gp","3gpp"]},"video/3gpp-tt":{"source":"iana"},"video/3gpp2":{"source":"iana","extensions":["3g2"]},"video/av1":{"source":"iana"},"video/bmpeg":{"source":"iana"},"video/bt656":{"source":"iana"},"video/celb":{"source":"iana"},"video/dv":{"source":"iana"},"video/encaprtp":{"source":"iana"},"video/ffv1":{"source":"iana"},"video/flexfec":{"source":"iana"},"video/h261":{"source":"iana","extensions":["h261"]},"video/h263":{"source":"iana","extensions":["h263"]},"video/h263-1998":{"source":"iana"},"video/h263-2000":{"source":"iana"},"video/h264":{"source":"iana","extensions":["h264"]},"video/h264-rcdo":{"source":"iana"},"video/h264-svc":{"source":"iana"},"video/h265":{"source":"iana"},"video/iso.segment":{"source":"iana","extensions":["m4s"]},"video/jpeg":{"source":"iana","extensions":["jpgv"]},"video/jpeg2000":{"source":"iana"},"video/jpm":{"source":"apache","extensions":["jpm","jpgm"]},"video/jxsv":{"source":"iana"},"video/mj2":{"source":"iana","extensions":["mj2","mjp2"]},"video/mp1s":{"source":"iana"},"video/mp2p":{"source":"iana"},"video/mp2t":{"source":"iana","extensions":["ts"]},"video/mp4":{"source":"iana","compressible":false,"extensions":["mp4","mp4v","mpg4"]},"video/mp4v-es":{"source":"iana"},"video/mpeg":{"source":"iana","compressible":false,"extensions":["mpeg","mpg","mpe","m1v","m2v"]},"video/mpeg4-generic":{"source":"iana"},"video/mpv":{"source":"iana"},"video/nv":{"source":"iana"},"video/ogg":{"source":"iana","compressible":false,"extensions":["ogv"]},"video/parityfec":{"source":"iana"},"video/pointer":{"source":"iana"},"video/quicktime":{"source":"iana","compressible":false,"extensions":["qt","mov"]},"video/raptorfec":{"source":"iana"},"video/raw":{"source":"iana"},"video/rtp-enc-aescm128":{"source":"iana"},"video/rtploopback":{"source":"iana"},"video/rtx":{"source":"iana"},"video/scip":{"source":"iana"},"video/smpte291":{"source":"iana"},"video/smpte292m":{"source":"iana"},"video/ulpfec":{"source":"iana"},"video/vc1":{"source":"iana"},"video/vc2":{"source":"iana"},"video/vnd.cctv":{"source":"iana"},"video/vnd.dece.hd":{"source":"iana","extensions":["uvh","uvvh"]},"video/vnd.dece.mobile":{"source":"iana","extensions":["uvm","uvvm"]},"video/vnd.dece.mp4":{"source":"iana"},"video/vnd.dece.pd":{"source":"iana","extensions":["uvp","uvvp"]},"video/vnd.dece.sd":{"source":"iana","extensions":["uvs","uvvs"]},"video/vnd.dece.video":{"source":"iana","extensions":["uvv","uvvv"]},"video/vnd.directv.mpeg":{"source":"iana"},"video/vnd.directv.mpeg-tts":{"source":"iana"},"video/vnd.dlna.mpeg-tts":{"source":"iana"},"video/vnd.dvb.file":{"source":"iana","extensions":["dvb"]},"video/vnd.fvt":{"source":"iana","extensions":["fvt"]},"video/vnd.hns.video":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.ttsavc":{"source":"iana"},"video/vnd.iptvforum.ttsmpeg2":{"source":"iana"},"video/vnd.motorola.video":{"source":"iana"},"video/vnd.motorola.videop":{"source":"iana"},"video/vnd.mpegurl":{"source":"iana","extensions":["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{"source":"iana","extensions":["pyv"]},"video/vnd.nokia.interleaved-multimedia":{"source":"iana"},"video/vnd.nokia.mp4vr":{"source":"iana"},"video/vnd.nokia.videovoip":{"source":"iana"},"video/vnd.objectvideo":{"source":"iana"},"video/vnd.radgamettools.bink":{"source":"iana"},"video/vnd.radgamettools.smacker":{"source":"iana"},"video/vnd.sealed.mpeg1":{"source":"iana"},"video/vnd.sealed.mpeg4":{"source":"iana"},"video/vnd.sealed.swf":{"source":"iana"},"video/vnd.sealedmedia.softseal.mov":{"source":"iana"},"video/vnd.uvvu.mp4":{"source":"iana","extensions":["uvu","uvvu"]},"video/vnd.vivo":{"source":"iana","extensions":["viv"]},"video/vnd.youtube.yt":{"source":"iana"},"video/vp8":{"source":"iana"},"video/vp9":{"source":"iana"},"video/webm":{"source":"apache","compressible":false,"extensions":["webm"]},"video/x-f4v":{"source":"apache","extensions":["f4v"]},"video/x-fli":{"source":"apache","extensions":["fli"]},"video/x-flv":{"source":"apache","compressible":false,"extensions":["flv"]},"video/x-m4v":{"source":"apache","extensions":["m4v"]},"video/x-matroska":{"source":"apache","compressible":false,"extensions":["mkv","mk3d","mks"]},"video/x-mng":{"source":"apache","extensions":["mng"]},"video/x-ms-asf":{"source":"apache","extensions":["asf","asx"]},"video/x-ms-vob":{"source":"apache","extensions":["vob"]},"video/x-ms-wm":{"source":"apache","extensions":["wm"]},"video/x-ms-wmv":{"source":"apache","compressible":false,"extensions":["wmv"]},"video/x-ms-wmx":{"source":"apache","extensions":["wmx"]},"video/x-ms-wvx":{"source":"apache","extensions":["wvx"]},"video/x-msvideo":{"source":"apache","extensions":["avi"]},"video/x-sgi-movie":{"source":"apache","extensions":["movie"]},"video/x-smv":{"source":"apache","extensions":["smv"]},"x-conference/x-cooltalk":{"source":"apache","extensions":["ice"]},"x-shader/x-fragment":{"compressible":true},"x-shader/x-vertex":{"compressible":true}}')},37236:e=>{"use strict";e.exports=JSON.parse('{"name":"superagent","version":"4.1.0","description":"elegant & feature rich browser / node HTTP with a fluent API","scripts":{"prepare":"make all","test":"make test","test-http2":"make test-node-http2"},"keywords":["http","ajax","request","agent"],"license":"MIT","author":"TJ Holowaychuk <<EMAIL>>","contributors":["Kornel Lesiński <<EMAIL>>","Peter Lyons <<EMAIL>>","Hunter Loftis <<EMAIL>>"],"repository":{"type":"git","url":"git://github.com/visionmedia/superagent.git"},"dependencies":{"component-emitter":"^1.2.0","cookiejar":"^2.1.2","debug":"^4.1.0","form-data":"^2.3.3","formidable":"^1.2.0","methods":"^1.1.1","mime":"^2.4.0","qs":"^6.6.0","readable-stream":"^3.0.6"},"devDependencies":{"Base64":"^1.0.1","babel-core":"^6.26.3","babel-preset-es2015":"^6.24.1","babelify":"^8.0.0","basic-auth-connect":"^1.0.0","body-parser":"^1.18.2","browserify":"^16.2.3","cookie-parser":"^1.4.3","express":"^4.16.3","express-session":"^1.15.6","marked":"^0.5.2","mocha":"^3.5.3","multer":"^1.4.1","should":"^13.2.0","should-http":"^0.1.1","zuul":"^3.12.0"},"browser":{"./lib/node/index.js":"./lib/client.js","./test/support/server.js":"./test/support/blank.js"},"component":{"scripts":{"superagent":"lib/client.js"}},"main":"./lib/node/index.js","engines":{"node":">= 6.0"}}')},44283:e=>{"use strict";e.exports=JSON.parse('{"/":{"namespace":"","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"context":{}}}]},"/oembed/1.0":{"namespace":"oembed/1.0","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"namespace":{},"context":{}}}]},"/oembed/1.0/embed":{"namespace":"oembed/1.0","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"url":{},"format":{},"maxwidth":{}}}]},"/oembed/1.0/proxy":{"namespace":"oembed/1.0","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"url":{},"format":{},"maxwidth":{},"maxheight":{},"discover":{}}}]},"/wp/v2":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"namespace":{},"context":{}}}]},"/wp/v2/posts":{"namespace":"wp/v2","methods":["GET","POST"],"endpoints":[{"methods":["GET"],"args":{"context":{},"page":{},"per_page":{},"search":{},"after":{},"author":{},"author_exclude":{},"before":{},"exclude":{},"include":{},"offset":{},"order":{},"orderby":{},"slug":{},"status":{},"categories":{},"categories_exclude":{},"tags":{},"tags_exclude":{},"sticky":{}}},{"methods":["POST"],"args":{}}]},"/wp/v2/posts/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET","POST","PUT","PATCH","DELETE"],"endpoints":[{"methods":["GET"],"args":{"id":{},"context":{},"password":{}}},{"methods":["POST","PUT","PATCH"],"args":{}},{"methods":["DELETE"],"args":{}}]},"/wp/v2/posts/(?P<parent>[\\\\d]+)/revisions":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"parent":{},"context":{},"page":{},"per_page":{},"search":{},"exclude":{},"include":{},"offset":{},"order":{},"orderby":{}}}]},"/wp/v2/posts/(?P<parent>[\\\\d]+)/revisions/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET","DELETE"],"endpoints":[{"methods":["GET"],"args":{"parent":{},"id":{},"context":{}}},{"methods":["DELETE"],"args":{}}]},"/wp/v2/posts/(?P<id>[\\\\d]+)/autosaves":{"namespace":"wp/v2","methods":["GET","POST"],"endpoints":[{"methods":["GET"],"args":{"parent":{},"context":{}}},{"methods":["POST"],"args":{}}]},"/wp/v2/posts/(?P<parent>[\\\\d]+)/autosaves/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"parent":{},"id":{},"context":{}}}]},"/wp/v2/pages":{"namespace":"wp/v2","methods":["GET","POST"],"endpoints":[{"methods":["GET"],"args":{"context":{},"page":{},"per_page":{},"search":{},"after":{},"author":{},"author_exclude":{},"before":{},"exclude":{},"include":{},"menu_order":{},"offset":{},"order":{},"orderby":{},"parent":{},"parent_exclude":{},"slug":{},"status":{}}},{"methods":["POST"],"args":{}}]},"/wp/v2/pages/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET","POST","PUT","PATCH","DELETE"],"endpoints":[{"methods":["GET"],"args":{"id":{},"context":{},"password":{}}},{"methods":["POST","PUT","PATCH"],"args":{}},{"methods":["DELETE"],"args":{}}]},"/wp/v2/pages/(?P<parent>[\\\\d]+)/revisions":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"parent":{},"context":{},"page":{},"per_page":{},"search":{},"exclude":{},"include":{},"offset":{},"order":{},"orderby":{}}}]},"/wp/v2/pages/(?P<parent>[\\\\d]+)/revisions/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET","DELETE"],"endpoints":[{"methods":["GET"],"args":{"parent":{},"id":{},"context":{}}},{"methods":["DELETE"],"args":{}}]},"/wp/v2/pages/(?P<id>[\\\\d]+)/autosaves":{"namespace":"wp/v2","methods":["GET","POST"],"endpoints":[{"methods":["GET"],"args":{"parent":{},"context":{}}},{"methods":["POST"],"args":{}}]},"/wp/v2/pages/(?P<parent>[\\\\d]+)/autosaves/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"parent":{},"id":{},"context":{}}}]},"/wp/v2/media":{"namespace":"wp/v2","methods":["GET","POST"],"endpoints":[{"methods":["GET"],"args":{"context":{},"page":{},"per_page":{},"search":{},"after":{},"author":{},"author_exclude":{},"before":{},"exclude":{},"include":{},"offset":{},"order":{},"orderby":{},"parent":{},"parent_exclude":{},"slug":{},"status":{},"media_type":{},"mime_type":{}}},{"methods":["POST"],"args":{}}]},"/wp/v2/media/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET","POST","PUT","PATCH","DELETE"],"endpoints":[{"methods":["GET"],"args":{"id":{},"context":{}}},{"methods":["POST","PUT","PATCH"],"args":{}},{"methods":["DELETE"],"args":{}}]},"/wp/v2/blocks":{"namespace":"wp/v2","methods":["GET","POST"],"endpoints":[{"methods":["GET"],"args":{"context":{},"page":{},"per_page":{},"search":{},"after":{},"before":{},"exclude":{},"include":{},"offset":{},"order":{},"orderby":{},"slug":{},"status":{}}},{"methods":["POST"],"args":{}}]},"/wp/v2/blocks/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET","POST","PUT","PATCH","DELETE"],"endpoints":[{"methods":["GET"],"args":{"id":{},"context":{},"password":{}}},{"methods":["POST","PUT","PATCH"],"args":{}},{"methods":["DELETE"],"args":{}}]},"/wp/v2/blocks/(?P<id>[\\\\d]+)/autosaves":{"namespace":"wp/v2","methods":["GET","POST"],"endpoints":[{"methods":["GET"],"args":{"parent":{},"context":{}}},{"methods":["POST"],"args":{}}]},"/wp/v2/blocks/(?P<parent>[\\\\d]+)/autosaves/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"parent":{},"id":{},"context":{}}}]},"/wp/v2/types":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"context":{}}}]},"/wp/v2/types/(?P<type>[\\\\w-]+)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"type":{},"context":{}}}]},"/wp/v2/statuses":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"context":{}}}]},"/wp/v2/statuses/(?P<status>[\\\\w-]+)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"status":{},"context":{}}}]},"/wp/v2/taxonomies":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"context":{},"type":{}}}]},"/wp/v2/taxonomies/(?P<taxonomy>[\\\\w-]+)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"taxonomy":{},"context":{}}}]},"/wp/v2/categories":{"namespace":"wp/v2","methods":["GET","POST"],"endpoints":[{"methods":["GET"],"args":{"context":{},"page":{},"per_page":{},"search":{},"exclude":{},"include":{},"order":{},"orderby":{},"hide_empty":{},"parent":{},"post":{},"slug":{}}},{"methods":["POST"],"args":{}}]},"/wp/v2/categories/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET","POST","PUT","PATCH","DELETE"],"endpoints":[{"methods":["GET"],"args":{"id":{},"context":{}}},{"methods":["POST","PUT","PATCH"],"args":{}},{"methods":["DELETE"],"args":{}}]},"/wp/v2/tags":{"namespace":"wp/v2","methods":["GET","POST"],"endpoints":[{"methods":["GET"],"args":{"context":{},"page":{},"per_page":{},"search":{},"exclude":{},"include":{},"offset":{},"order":{},"orderby":{},"hide_empty":{},"post":{},"slug":{}}},{"methods":["POST"],"args":{}}]},"/wp/v2/tags/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET","POST","PUT","PATCH","DELETE"],"endpoints":[{"methods":["GET"],"args":{"id":{},"context":{}}},{"methods":["POST","PUT","PATCH"],"args":{}},{"methods":["DELETE"],"args":{}}]},"/wp/v2/users":{"namespace":"wp/v2","methods":["GET","POST"],"endpoints":[{"methods":["GET"],"args":{"context":{},"page":{},"per_page":{},"search":{},"exclude":{},"include":{},"offset":{},"order":{},"orderby":{},"slug":{},"roles":{},"who":{}}},{"methods":["POST"],"args":{}}]},"/wp/v2/users/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET","POST","PUT","PATCH","DELETE"],"endpoints":[{"methods":["GET"],"args":{"id":{},"context":{}}},{"methods":["POST","PUT","PATCH"],"args":{}},{"methods":["DELETE"],"args":{}}]},"/wp/v2/users/me":{"namespace":"wp/v2","methods":["GET","POST","PUT","PATCH","DELETE"],"endpoints":[{"methods":["GET"],"args":{"context":{}}},{"methods":["POST","PUT","PATCH"],"args":{}},{"methods":["DELETE"],"args":{}}]},"/wp/v2/comments":{"namespace":"wp/v2","methods":["GET","POST"],"endpoints":[{"methods":["GET"],"args":{"context":{},"page":{},"per_page":{},"search":{},"after":{},"author":{},"author_exclude":{},"author_email":{},"before":{},"exclude":{},"include":{},"offset":{},"order":{},"orderby":{},"parent":{},"parent_exclude":{},"post":{},"status":{},"type":{},"password":{}}},{"methods":["POST"],"args":{}}]},"/wp/v2/comments/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET","POST","PUT","PATCH","DELETE"],"endpoints":[{"methods":["GET"],"args":{"id":{},"context":{},"password":{}}},{"methods":["POST","PUT","PATCH"],"args":{}},{"methods":["DELETE"],"args":{}}]},"/wp/v2/search":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"context":{},"page":{},"per_page":{},"search":{},"type":{},"subtype":{}}}]},"/wp/v2/block-renderer/(?P<name>core/block)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"name":{},"context":{},"attributes":{},"post_id":{}}}]},"/wp/v2/block-renderer/(?P<name>core/latest-comments)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"name":{},"context":{},"attributes":{},"post_id":{}}}]},"/wp/v2/block-renderer/(?P<name>core/archives)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"name":{},"context":{},"attributes":{},"post_id":{}}}]},"/wp/v2/block-renderer/(?P<name>core/categories)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"name":{},"context":{},"attributes":{},"post_id":{}}}]},"/wp/v2/block-renderer/(?P<name>core/latest-posts)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"name":{},"context":{},"attributes":{},"post_id":{}}}]},"/wp/v2/block-renderer/(?P<name>core/shortcode)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"name":{},"context":{},"attributes":{},"post_id":{}}}]},"/wp/v2/settings":{"namespace":"wp/v2","methods":["GET","POST","PUT","PATCH"],"endpoints":[{"methods":["GET"],"args":{}},{"methods":["POST","PUT","PATCH"],"args":{}}]},"/wp/v2/themes":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"context":{},"page":{},"per_page":{},"search":{},"status":{}}}]}}')}};