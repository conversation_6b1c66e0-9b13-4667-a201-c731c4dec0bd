Folder PATH listing
Volume serial number is 885A-8B89
C:.
�   .gitignore
�   azure-pipeline.yml
�   azure-pipeline_PAL_PROD.yml
�   azure-pipeline_PHW.yml
�   azure_pipeline_main.yml
�   azure_pipeline_template.yml
�   Dockerfile
�   output.txt
� MicroSaasWebApipi.sln
�   README.md
�   
+---azure_pipelines
�   +---PAL
�   �       azure_pipeline_PAL_PROD.yml
�   �       azure_pipeline_PAL_QA.yml
�   �       azure_pipeline_PAL_UAT.yml
�   �       
�   +---PHW
�   �       azure_pipeline_PHW_PROD.yml
�   �       azure_pipeline_PHW_QA.yml
�   �       azure_pipeline_PHW_UAT.yml
�   �       
�   +---PXW
�           azure_pipeline_PXW_PROD.yml
�           azure_pipeline_PXW_QA.yml
�           azure_pipeline_PXW_UAT.yml
�           
+---DocumentBatchProcess
�   �   appsettings.json
�   �   ConfigLoader.cs
�   �   DocumentBatchProcess.csproj
�   �   Program.cs
�   �   Settings.job
�   �   
�   +---bin
�   �   +---Debug
�   �       +---net7.0
�   �               DocumentBatchProcess.deps.json
�   �               DocumentBatchProcess.dll
�   �               DocumentBatchProcess.pdb
�   �               
�   +---obj
�       �   DocumentBatchProcess.csproj.nuget.dgspec.json
�       �   DocumentBatchProcess.csproj.nuget.g.props
�       �   DocumentBatchProcess.csproj.nuget.g.targets
�       �   project.assets.json
�       �   project.nuget.cache
�       �   
�       +---Debug
�           +---net7.0
�               �   .NETCoreApp,Version=v7.0.AssemblyAttributes.cs
�               �   DocumentBatchProcess.AssemblyInfo.cs
�               �   DocumentBatchProcess.AssemblyInfoInputs.cache
�               �   DocumentBatchProcess.assets.cache
�               �   DocumentBatchProcess.csproj.AssemblyReference.cache
�               �   DocumentBatchProcess.csproj.BuildWithSkipAnalyzers
�               �   DocumentBatchProcess.csproj.CoreCompileInputs.cache
�               �   DocumentBatchProcess.csproj.FileListAbsolute.txt
�               �   DocumentBatchProcess.dll
�               �   DocumentBatchProcess.GeneratedMSBuildEditorConfig.editorconfig
�               �   DocumentBatchProcess.GlobalUsings.g.cs
�               �   DocumentBatchProcess.pdb
�               �   
�               +---ref
�               �       DocumentBatchProcess.dll
�               �       
�               +---refint
�                       DocumentBatchProcess.dll
�                       
+---MicroSaasWebApi
�   �   .dockerignore
�   �   appsettings.Development.json
�   �   appsettings.json
�   �   appsettings.phw-react.Development.json
�   �   appsettings.phw-react.json
�   �MicroSaasWebApiApi.csproj
�   �   Program-diff-swagger.cs
�   �   Program.cs
�   �   Readme.md
�   �   
�   +---.config
�   �       dotnet-tools.json
�   �       
�   +---Assets
�   �   +---PAL
�   �   �   +---AppSettings
�   �   �   �       appsettings.pal.prod.Development.json
�   �   �   �       appsettings.pal.prod.json
�   �   �   �       appsettings.pal.uat.Development.json
�   �   �   �       appsettings.pal.uat.json
�   �   �   �       
�   �   �   +---Postman
�   �   �           PXW-WEB-API-ENV-PXW.postman_environment.json
�   �   �           PXW-WEB-API.postman_collection.json
�   �   �           
�   �   +---PHW
�   �   �   +---AppSettings
�   �   �   �       appsettings.phw.prod.Development.json
�   �   �   �       appsettings.phw.prod.json
�   �   �   �       appsettings.phw.qa.Development.json
�   �   �   �       appsettings.phw.qa.json
�   �   �   �       appsettings.phw.uat.Development.json
�   �   �   �       appsettings.phw.uat.json
�   �   �   �       
�   �   �   +---Postman
�   �   �           PXW-WEB-API-ENV-PHW.postman_environment.json
�   �   �           PXW-WEB-API.postman_collection.json
�   �   �           
�   �   +---PXW
�   �       +---AppSettings
�   �       �       appsettings.pxw.uat.Development.json
�   �       �       appsettings.pxw.uat.json
�   �       �       
�   �       +---Postman
�   �               PXW-WEB-API-ENV-PXW.postman_environment.json
�   �               PXW-WEB-API.postman_collection.json
�   �               
�   +---bin
�   �   +---Debug
�   �       +---net7.0
�   �           �   AngleSharp.Css.dll
�   �           �   AngleSharp.dll
�   �           �   appsettings.Development.json
�   �           �   appsettings.json
�   �           �   appsettings.phw-react.Development.json
�   �           �   appsettings.phw-react.json
�   �           �   Azure.Core.dll
�   �           �   Azure.Identity.dll
�   �           �   Azure.Security.KeyVault.Certificates.dll
�   �           �   Azure.Security.KeyVault.Secrets.dll
�   �           �   HealthChecks.UI.Client.dll
�   �           �   HealthChecks.UI.Core.dll
�   �           �   Markdig.dll
�   �           �   Microsoft.ApplicationInsights.dll
�   �           �   Microsoft.AspNetCore.Authentication.AzureADB2C.UI.dll
�   �           �   Microsoft.AspNetCore.Authentication.JwtBearer.dll
�   �           �   Microsoft.AspNetCore.Authentication.OpenIdConnect.dll
�   �           �   Microsoft.AspNetCore.Cryptography.Internal.dll
�   �           �   Microsoft.AspNetCore.DataProtection.Abstractions.dll
�   �           �   Microsoft.AspNetCore.DataProtection.dll
�   �           �   Microsoft.AspNetCore.JsonPatch.dll
�   �           �   Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll
�   �           �   Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer.dll
�   �           �   Microsoft.AspNetCore.Mvc.Versioning.dll
�   �           �   Microsoft.AspNetCore.OpenApi.dll
�   �           �   Microsoft.Bcl.AsyncInterfaces.dll
�   �           �   Microsoft.Extensions.DependencyModel.dll
�   �           �   Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll
�   �           �   Microsoft.Extensions.Diagnostics.HealthChecks.dll
�   �           �   Microsoft.Extensions.Logging.Abstractions.dll
�   �           �   Microsoft.Extensions.Options.dll
�   �           �   Microsoft.Graph.Core.dll
�   �           �   Microsoft.Graph.dll
�   �           �   Microsoft.Identity.Abstractions.dll
�   �           �   Microsoft.Identity.Client.dll
�   �           �   Microsoft.Identity.Client.Extensions.Msal.dll
�   �           �   Microsoft.Identity.Web.Certificate.dll
�   �           �   Microsoft.Identity.Web.Certificateless.dll
�   �           �   Microsoft.Identity.Web.Diagnostics.dll
�   �           �   Microsoft.Identity.Web.dll
�   �           �   Microsoft.Identity.Web.TokenAcquisition.dll
�   �           �   Microsoft.Identity.Web.TokenCache.dll
�   �           �   Microsoft.Identity.Web.UI.dll
�   �           �   Microsoft.Identity.Web.UI.Views.dll
�   �           �   Microsoft.IdentityModel.Abstractions.dll
�   �           �   Microsoft.IdentityModel.JsonWebTokens.dll
�   �           �   Microsoft.IdentityModel.Logging.dll
�   �           �   Microsoft.IdentityModel.LoggingExtensions.dll
�   �           �   Microsoft.IdentityModel.Protocols.dll
�   �           �   Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
�   �           �   Microsoft.IdentityModel.Tokens.dll
�   �           �   Microsoft.IdentityModel.Validators.dll
�   �           �   Microsoft.Office.Client.Policy.dll
�   �           �   Microsoft.Office.Client.TranslationServices.dll
�   �           �   Microsoft.Office.SharePoint.Tools.dll
�   �           �   Microsoft.Online.SharePoint.Client.Tenant.dll
�   �           �   Microsoft.OpenApi.dll
�   �           �   Microsoft.ProjectServer.Client.dll
�   �           �   Microsoft.SharePoint.Client.dll
�   �           �   Microsoft.SharePoint.Client.DocumentManagement.dll
�   �           �   Microsoft.SharePoint.Client.Publishing.dll
�   �           �   Microsoft.SharePoint.Client.Runtime.dll
�   �           �   Microsoft.SharePoint.Client.Search.Applications.dll
�   �           �   Microsoft.SharePoint.Client.Search.dll
�   �           �   Microsoft.SharePoint.Client.Taxonomy.dll
�   �           �   Microsoft.SharePoint.Client.UserProfiles.dll
�   �           �   Microsoft.SharePoint.Client.WorkflowServices.dll
�   �           �   Microsoft.Win32.SystemEvents.dll
�   �           �   Newtonsoft.Json.Bson.dll
�   �           �   Newtonsoft.Json.dll
�   �           �   PnP.Core.dll
�   �           �   PnP.Framework.dll
�   �           �   Portable.Xaml.dll
�   �           MicroSaasWebApibApi.deps.json
�   �           MicroSaasWebApibApi.dll
�   �           MicroSaasWebApibApi.exe
�   �           MicroSaasWebApibApi.pdb
�   �           MicroSaasWebApibApi.runtimeconfig.json
�   �           MicroSaasWebApibApi.xml
�   �           �   RestSharp.dll
�   �           �   Scrutor.dll
�   �           �   SharePointAPI.dll
�   �           �   SharePointAPI.pdb
�   �           �   Swashbuckle.AspNetCore.Annotations.dll
�   �           �   Swashbuckle.AspNetCore.Filters.Abstractions.dll
�   �           �   Swashbuckle.AspNetCore.Filters.dll
�   �           �   Swashbuckle.AspNetCore.Newtonsoft.dll
�   �           �   Swashbuckle.AspNetCore.ReDoc.dll
�   �           �   Swashbuckle.AspNetCore.Swagger.dll
�   �           �   Swashbuckle.AspNetCore.SwaggerGen.dll
�   �           �   Swashbuckle.AspNetCore.SwaggerUI.dll
�   �           �   System.Configuration.ConfigurationManager.dll
�   �           �   System.DirectoryServices.dll
�   �           �   System.Drawing.Common.dll
�   �           �   System.IdentityModel.Tokens.Jwt.dll
�   �           �   System.IO.Packaging.dll
�   �           �   System.Security.Cryptography.Pkcs.dll
�   �           �   System.Security.Cryptography.ProtectedData.dll
�   �           �   System.Security.Cryptography.Xml.dll
�   �           �   System.Security.Permissions.dll
�   �           �   System.Windows.Extensions.dll
�   �           �   TimeZoneConverter.dll
�   �           �   
�   �           +---Assets
�   �           �   +---AppSettings
�   �           �   �       appsettings.phw.uat.Development.json
�   �           �   �       appsettings.phw.uat.json
�   �           �   �       appsettings.pxw.uat.Development.json
�   �           �   �       appsettings.pxw.uat.json
�   �           �   �       
�   �           �   +---Postman
�   �           �           PXW-WEB-API-ENV-PHW.postman_environment.json
�   �           �           PXW-WEB-API-ENV-PXW.postman_environment.json
�   �           �           PXW-WEB-API.postman_collection.json
�   �           �           
�   �           +---runtimes
�   �               +---win
�   �                   +---lib
�   �                       +---net7.0
�   �                               Microsoft.Win32.SystemEvents.dll
�   �                               System.DirectoryServices.dll
�   �                               System.Drawing.Common.dll
�   �                               System.Security.Cryptography.Pkcs.dll
�   �                               System.Security.Cryptography.ProtectedData.dll
�   �                               System.Windows.Extensions.dll
�   �                               
�   +---Controllers
�   �   +---PXW
�   �           AccountProfileController.cs
�   �           AlphaCCOController.cs
�   �           BaseController.cs
�   �           DataApiController.cs
�   �           DocumentApiController.cs
�   �           SharepointController.cs
�   �           TokenController.cs
�   �           
�   +---Documentation
�   �       getting-started.md
�   �       index.md
�   �       initial-workflow-completed.md
�   �       
�   +---Extensions
�   �       MiddlewareExtensions.cs
�   �       
�   +---Middlewares
�   �       AzureB2CTokenAuthenticationMiddleware.cs
�   �       CustomRedirectMiddleware.cs
�   �       DocumentationMiddleware.cs
�   �       
�   +---Models
�   �   +---AlphaCCO
�   �   �       AlphaCCOModel.cs
�   �   �       AlphaCCOQuestions.cs
�   �   �       AlphaCCOWorkflow.cs
�   �   �       APIResult.cs
�   �   �       BaseAPIRequest.cs
�   �   �       BaseObject.cs
�   �   �       SteppedTile.cs
�   �   �       SteppedTileCard.cs
�   �   �       WorkflowRequest.cs
�   �   �       
�   �   +---Documents
�   �   �       Documents.cs
�   �   �       PDFHelper.cs
�   �   �       
�   �   +---Profile
�   �   �       Account.cs
�   �   �       AccountInformation.cs
�   �   �       AccountMember.cs
�   �   �       AccountProfile.cs
�   �   �       FileDetail.cs
�   �   �       
�   �   +---PXW
�   �       �   RequestBody.cs
�   �       �   RequestBodyExample.cs
�   �       �   
�   �       +---AppSettings
�   �               AppSettings.cs
�   �               
�   +---obj
�   �   MicroSaasWebApibApi.csproj.nuget.dgspec.json
�   �   MicroSaasWebApibApi.csproj.nuget.g.props
�   �   MicroSaasWebApibApi.csproj.nuget.g.targets
�   �   �   project.assets.json
�   �   �   project.nuget.cache
�   �   �   
�   �   +---Debug
�   �       +---net7.0
�   �           �   .NETCoreApp,Version=v7.0.AssemblyAttributes.cs
�   �           �   apphost.exe
�   �           MicroSaasWebApibApi.AssemblyInfo.cs
�   �           MicroSaasWebApibApi.AssemblyInfoInputs.cache
�   �           MicroSaasWebApibApi.assets.cache
�   �           MicroSaasWebApibApi.csproj.AssemblyReference.cache
�   �           MicroSaasWebApibApi.csproj.BuildWithSkipAnalyzers
�   �           MicroSaasWebApibApi.csproj.CopyComplete
�   �           MicroSaasWebApibApi.csproj.CoreCompileInputs.cache
�   �           MicroSaasWebApibApi.csproj.FileListAbsolute.txt
�   �           MicroSaasWebApibApi.dll
�   �           MicroSaasWebApibApi.GeneratedMSBuildEditorConfig.editorconfig
�   �           MicroSaasWebApibApi.genruntimeconfig.cache
�   �           MicroSaasWebApibApi.GlobalUsings.g.cs
�   �           MicroSaasWebApibApi.MvcApplicationPartsAssemblyInfo.cache
�   �           MicroSaasWebApibApi.MvcApplicationPartsAssemblyInfo.cs
�   �           MicroSaasWebApibApi.pdb
�   �           MicroSaasWebApibApi.xml
�   �           �   staticwebassets.build.json
�   �           �   
�   �           +---ref
�   �           � MicroSaasWebApiWebApi.dll
�   �           �       
�   �           +---refint
�   �           � MicroSaasWebApiWebApi.dll
�   �           �       
�   �           +---staticwebassets
�   �                   msbuild.buMicroSaasWebApibApi.props
�   �                   msbuild.buildMultiTargetMicroSaasWebApibApi.props
�   �                   msbuild.buildTransitMicroSaasWebApibApi.props
�   �                   
�   +---Properties
�   �   �   launchSettings.json
�   �   �   serviceDependencies.json
�   �   �   serviceDependencies.local.json
�   �   �   
�   �   +---Enums
�   �   �   +---Profile
�   �   �   �       Profile.cs
�   �   �   �       
�   �   �   +---PXW
�   �   �           HttpStatusCodes.cs
�   �   �           Language.cs
�   �   �           
�   �   +---PXW
�   �           ApiHelper.cs
�   �           Constants.cs
�   �           
�   +---Services
�       +---AlphaCCO
�       �   +---PXW
�       �           WorkflowEngine.cs
�       �           
�       +---Auth
�       �   +---PXW
�       �       �   JwtValidationService.cs
�       �       �   
�       �       +---Interface
�       �               IJwtValidationService.cs
�       �               
�       +---Profile
�       �   +---PXW
�       �           ProfileEngine.cs
�       �           
�       +---PXW
�       �   �   ApplicationSettingsService.cs
�       �   �   BusinessEngine.cs
�       �   �   ControllerActionInvoker.cs
�       �   �   EngineBase.cs
�       �   �   ExternalApiService.cs
�       �   �   ResponseLoggerService.cs
�       �   �   TenantProviderService.cs
�       �   �   
�       �   +---Interface
�       �   �       IApplicationSettingsService.cs
�       �   �       IControllerActionInvoker.cs
�       �   �       IExternalApiService.cs
�       �   �       IResponseLoggerService.cs
�       �   �       ITenantProviderService.cs
�       �   �       
�       �   +---Utilities
�       �           MultipartRequestHelper.cs
�       �           
�       +---Sharepoint
�           +---PHW
�                   GetFiles.cs
�                   
+---MicroSaasWebApi.Tests.Unit
�   �   coverage.cobertura.xml
�   �   GlobalUsings.cs
�   �MicroSaasWebApiApi.Tests.Unit.csproj
�   �   Readme.md
�   �   
�   +---bin
�   �   +---Debug
�   �       +---net7.0
�   �           �   AngleSharp.Css.dll
�   �           �   AngleSharp.dll
�   �           �   appsettings.Development.json
�   �           �   appsettings.json
�   �           �   appsettings.phw-react.Development.json
�   �           �   appsettings.phw-react.json
�   �           �   Azure.Core.dll
�   �           �   Azure.Identity.dll
�   �           �   Azure.Security.KeyVault.Certificates.dll
�   �           �   Azure.Security.KeyVault.Secrets.dll
�   �           �   Castle.Core.dll
�   �           �   CoverletSourceRootsMaMicroSaasWebApiWebApi.Tests.Unit
�   �           �   FakeItEasy.dll
�   �           �   FluentAssertions.dll
�   �           �   HealthChecks.UI.Client.dll
�   �           �   HealthChecks.UI.Core.dll
�   �           �   Markdig.dll
�   �           �   Microsoft.ApplicationInsights.dll
�   �           �   Microsoft.AspNetCore.Authentication.AzureADB2C.UI.dll
�   �           �   Microsoft.AspNetCore.Authentication.JwtBearer.dll
�   �           �   Microsoft.AspNetCore.Authentication.OpenIdConnect.dll
�   �           �   Microsoft.AspNetCore.Cryptography.Internal.dll
�   �           �   Microsoft.AspNetCore.DataProtection.Abstractions.dll
�   �           �   Microsoft.AspNetCore.DataProtection.dll
�   �           �   Microsoft.AspNetCore.JsonPatch.dll
�   �           �   Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll
�   �           �   Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer.dll
�   �           �   Microsoft.AspNetCore.Mvc.Versioning.dll
�   �           �   Microsoft.AspNetCore.OpenApi.dll
�   �           �   Microsoft.Bcl.AsyncInterfaces.dll
�   �           �   Microsoft.Extensions.DependencyModel.dll
�   �           �   Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll
�   �           �   Microsoft.Extensions.Diagnostics.HealthChecks.dll
�   �           �   Microsoft.Extensions.Logging.Abstractions.dll
�   �           �   Microsoft.Extensions.Options.dll
�   �           �   Microsoft.Graph.Core.dll
�   �           �   Microsoft.Graph.dll
�   �           �   Microsoft.Identity.Abstractions.dll
�   �           �   Microsoft.Identity.Client.dll
�   �           �   Microsoft.Identity.Client.Extensions.Msal.dll
�   �           �   Microsoft.Identity.Web.Certificate.dll
�   �           �   Microsoft.Identity.Web.Certificateless.dll
�   �           �   Microsoft.Identity.Web.Diagnostics.dll
�   �           �   Microsoft.Identity.Web.dll
�   �           �   Microsoft.Identity.Web.TokenAcquisition.dll
�   �           �   Microsoft.Identity.Web.TokenCache.dll
�   �           �   Microsoft.Identity.Web.UI.dll
�   �           �   Microsoft.Identity.Web.UI.Views.dll
�   �           �   Microsoft.IdentityModel.Abstractions.dll
�   �           �   Microsoft.IdentityModel.JsonWebTokens.dll
�   �           �   Microsoft.IdentityModel.Logging.dll
�   �           �   Microsoft.IdentityModel.LoggingExtensions.dll
�   �           �   Microsoft.IdentityModel.Protocols.dll
�   �           �   Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
�   �           �   Microsoft.IdentityModel.Tokens.dll
�   �           �   Microsoft.IdentityModel.Validators.dll
�   �           �   Microsoft.Office.Client.Policy.dll
�   �           �   Microsoft.Office.Client.TranslationServices.dll
�   �           �   Microsoft.Office.SharePoint.Tools.dll
�   �           �   Microsoft.Online.SharePoint.Client.Tenant.dll
�   �           �   Microsoft.OpenApi.dll
�   �           �   Microsoft.ProjectServer.Client.dll
�   �           �   Microsoft.SharePoint.Client.dll
�   �           �   Microsoft.SharePoint.Client.DocumentManagement.dll
�   �           �   Microsoft.SharePoint.Client.Publishing.dll
�   �           �   Microsoft.SharePoint.Client.Runtime.dll
�   �           �   Microsoft.SharePoint.Client.Search.Applications.dll
�   �           �   Microsoft.SharePoint.Client.Search.dll
�   �           �   Microsoft.SharePoint.Client.Taxonomy.dll
�   �           �   Microsoft.SharePoint.Client.UserProfiles.dll
�   �           �   Microsoft.SharePoint.Client.WorkflowServices.dll
�   �           �   Microsoft.TestPlatform.CommunicationUtilities.dll
�   �           �   Microsoft.TestPlatform.CoreUtilities.dll
�   �           �   Microsoft.TestPlatform.CrossPlatEngine.dll
�   �           �   Microsoft.TestPlatform.PlatformAbstractions.dll
�   �           �   Microsoft.TestPlatform.Utilities.dll
�   �           �   Microsoft.VisualStudio.CodeCoverage.Shim.dll
�   �           �   Microsoft.VisualStudio.TestPlatform.Common.dll
�   �           �   Microsoft.VisualStudio.TestPlatform.ObjectModel.dll
�   �           �   Microsoft.Win32.SystemEvents.dll
�   �           �   Newtonsoft.Json.Bson.dll
�   �           �   Newtonsoft.Json.dll
�   �           �   NuGet.Frameworks.dll
�   �           �   PnP.Core.dll
�   �           �   PnP.Framework.dll
�   �           �   Portable.Xaml.dll
�   �           MicroSaasWebApibApi.deps.json
�   �           MicroSaasWebApibApi.dll
�   �           MicroSaasWebApibApi.exe
�   �           MicroSaasWebApibApi.pdb
�   �           MicroSaasWebApibApi.runtimeconfig.json
�   �           MicroSaasWebApibApi.Tests.Unit.deps.json
�   �           MicroSaasWebApibApi.Tests.Unit.dll
�   �           MicroSaasWebApibApi.Tests.Unit.pdb
�   �           MicroSaasWebApibApi.Tests.Unit.runtimeconfig.json
�   �           MicroSaasWebApibApi.xml
�   �           �   RestSharp.dll
�   �           �   Scrutor.dll
�   �           �   SharePointAPI.dll
�   �           �   SharePointAPI.pdb
�   �           �   Swashbuckle.AspNetCore.Annotations.dll
�   �           �   Swashbuckle.AspNetCore.Filters.Abstractions.dll
�   �           �   Swashbuckle.AspNetCore.Filters.dll
�   �           �   Swashbuckle.AspNetCore.Newtonsoft.dll
�   �           �   Swashbuckle.AspNetCore.ReDoc.dll
�   �           �   Swashbuckle.AspNetCore.Swagger.dll
�   �           �   Swashbuckle.AspNetCore.SwaggerGen.dll
�   �           �   Swashbuckle.AspNetCore.SwaggerUI.dll
�   �           �   System.Configuration.ConfigurationManager.dll
�   �           �   System.DirectoryServices.dll
�   �           �   System.Drawing.Common.dll
�   �           �   System.IdentityModel.Tokens.Jwt.dll
�   �           �   System.IO.Packaging.dll
�   �           �   System.Security.Cryptography.Pkcs.dll
�   �           �   System.Security.Cryptography.ProtectedData.dll
�   �           �   System.Security.Cryptography.Xml.dll
�   �           �   System.Security.Permissions.dll
�   �           �   System.Windows.Extensions.dll
�   �           �   testhost.dll
�   �           �   testhost.exe
�   �           �   TimeZoneConverter.dll
�   �           �   xunit.abstractions.dll
�   �           �   xunit.assert.dll
�   �           �   xunit.core.dll
�   �           �   xunit.execution.dotnet.dll
�   �           �   xunit.runner.visualstudio.dotnetcore.testadapter.dll
�   �           �   
�   �           +---Assets
�   �           �   +---AppSettings
�   �           �   �       appsettings.phw.uat.Development.json
�   �           �   �       appsettings.phw.uat.json
�   �           �   �       appsettings.pxw.uat.Development.json
�   �           �   �       appsettings.pxw.uat.json
�   �           �   �       
�   �           �   +---Postman
�   �           �           PXW-WEB-API-ENV-PHW.postman_environment.json
�   �           �           PXW-WEB-API-ENV-PXW.postman_environment.json
�   �           �           PXW-WEB-API.postman_collection.json
�   �           �           
�   �           +---cs
�   �           �       Microsoft.TestPlatform.CommunicationUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CoreUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CrossPlatEngine.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.Common.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
�   �           �       
�   �           +---de
�   �           �       Microsoft.TestPlatform.CommunicationUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CoreUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CrossPlatEngine.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.Common.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
�   �           �       
�   �           +---es
�   �           �       Microsoft.TestPlatform.CommunicationUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CoreUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CrossPlatEngine.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.Common.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
�   �           �       
�   �           +---fr
�   �           �       Microsoft.TestPlatform.CommunicationUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CoreUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CrossPlatEngine.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.Common.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
�   �           �       
�   �           +---it
�   �           �       Microsoft.TestPlatform.CommunicationUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CoreUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CrossPlatEngine.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.Common.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
�   �           �       
�   �           +---ja
�   �           �       Microsoft.TestPlatform.CommunicationUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CoreUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CrossPlatEngine.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.Common.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
�   �           �       
�   �           +---ko
�   �           �       Microsoft.TestPlatform.CommunicationUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CoreUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CrossPlatEngine.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.Common.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
�   �           �       
�   �           +---pl
�   �           �       Microsoft.TestPlatform.CommunicationUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CoreUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CrossPlatEngine.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.Common.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
�   �           �       
�   �           +---pt-BR
�   �           �       Microsoft.TestPlatform.CommunicationUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CoreUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CrossPlatEngine.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.Common.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
�   �           �       
�   �           +---ru
�   �           �       Microsoft.TestPlatform.CommunicationUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CoreUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CrossPlatEngine.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.Common.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
�   �           �       
�   �           +---runtimes
�   �           �   +---win
�   �           �       +---lib
�   �           �           +---net7.0
�   �           �                   Microsoft.Win32.SystemEvents.dll
�   �           �                   System.DirectoryServices.dll
�   �           �                   System.Drawing.Common.dll
�   �           �                   System.Security.Cryptography.Pkcs.dll
�   �           �                   System.Security.Cryptography.ProtectedData.dll
�   �           �                   System.Windows.Extensions.dll
�   �           �                   
�   �           +---tr
�   �           �       Microsoft.TestPlatform.CommunicationUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CoreUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CrossPlatEngine.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.Common.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
�   �           �       
�   �           +---zh-Hans
�   �           �       Microsoft.TestPlatform.CommunicationUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CoreUtilities.resources.dll
�   �           �       Microsoft.TestPlatform.CrossPlatEngine.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.Common.resources.dll
�   �           �       Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
�   �           �       
�   �           +---zh-Hant
�   �                   Microsoft.TestPlatform.CommunicationUtilities.resources.dll
�   �                   Microsoft.TestPlatform.CoreUtilities.resources.dll
�   �                   Microsoft.TestPlatform.CrossPlatEngine.resources.dll
�   �                   Microsoft.VisualStudio.TestPlatform.Common.resources.dll
�   �                   Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
�   �                   
�   +---Controller
�   �       DataApiControllerTests.cs
�   �       DocumentApiControllerTests.cs
�   �       SharepointControllerTests.cs
�   �       
�   +---coverlet
�   �   +---reports
�   �           Cobertura.xml
�   �           index.htm
�   �           index.html
�   �       MicroSaasWebApibApi_Account.html
�   �       MicroSaasWebApibApi_AccountBrief.html
�   �       MicroSaasWebApibApi_AccountInformation.html
�   �       MicroSaasWebApibApi_AccountMember.html
�   �       MicroSaasWebApibApi_AccountProfile.html
�   �       MicroSaasWebApibApi_AccountProfileController.html
�   �       MicroSaasWebApibApi_AccountResponse.html
�   �       MicroSaasWebApibApi_AccountsEmail.html
�   �       MicroSaasWebApibApi_AlphaCC0.html
�   �       MicroSaasWebApibApi_AlphaCCOAuthenticationResponse.html
�   �       MicroSaasWebApibApi_AlphaCCOCardRequest.html
�   �       MicroSaasWebApibApi_AlphaCCOController.html
�   �       MicroSaasWebApibApi_AlphaCCOExternalParty.html
�   �       MicroSaasWebApibApi_AlphaCCOExternalPartyResponse.html
�   �       MicroSaasWebApibApi_AlphaCCOLogin.html
�   �       MicroSaasWebApibApi_AlphaCCOModel.html
�   �       MicroSaasWebApibApi_AlphaCCOQuestionsRequest.html
�   �       MicroSaasWebApibApi_AlphaCCOQuestionsRequestBody.html
�   �       MicroSaasWebApibApi_AlphaCCOResponseBase.html
�   �       MicroSaasWebApibApi_AlphaCCOSearch.html
�   �       MicroSaasWebApibApi_AlphaCCOSearchResultResponse.html
�   �       MicroSaasWebApibApi_AlphaCCOWorkflow.html
�   �       MicroSaasWebApibApi_AlphaCCOWorkflowResponse.html
�   �       MicroSaasWebApibApi_AlphaCCoWorkflowStart.html
�   �       MicroSaasWebApibApi_AlphaCCoWorkflowStartResponse.html
�   �       MicroSaasWebApibApi_APIAccount.html
�   �       MicroSaasWebApibApi_ApiHelper.html
�   �       MicroSaasWebApibApi_APIProfile.html
�   �       MicroSaasWebApibApi_APIResult.html
�   �       MicroSaasWebApibApi_ApplicationSettingsService.html
�   �       MicroSaasWebApibApi_AppSettings.html
�   �       MicroSaasWebApibApi_AuthorizeCheckOperationFilter.html
�   �       MicroSaasWebApibApi_AzureAdB2C.html
�   �       MicroSaasWebApibApi_AzureB2CTokenAuthenticationMiddleware.html
�   �       MicroSaasWebApibApi_BaseAPIRequest.html
�   �       MicroSaasWebApibApi_BaseController.html
�   �       MicroSaasWebApibApi_BaseObject.html
�   �       MicroSaasWebApibApi_BearerTokenResponse.html
�   �       MicroSaasWebApibApi_BusinessEngine.html
�   �       MicroSaasWebApibApi_Configurations.html
�   �       MicroSaasWebApibApi_Constants.html
�   �       MicroSaasWebApibApi_CurrentStepStatus.html
�   �       MicroSaasWebApibApi_CustomRedirectMiddleware.html
�   �       MicroSaasWebApibApi_DataApiController.html
�   �       MicroSaasWebApibApi_DataBrief.html
�   �       MicroSaasWebApibApi_DocumentationMiddleware.html
�   �       MicroSaasWebApibApi_Documents.html
�   �       MicroSaasWebApibApi_DocumentType.html
�   �       MicroSaasWebApibApi_DownloadDocumentRequest.html
�   �       MicroSaasWebApibApi_DownloadDocumentsRequest.html
�   �       MicroSaasWebApibApi_Emails.html
�   �       MicroSaasWebApibApi_Endpoint.html
�   �       MicroSaasWebApibApi_Endpoints.html
�   �       MicroSaasWebApibApi_EngineBase.html
�   �       MicroSaasWebApibApi_ExternalApiService.html
�   �       MicroSaasWebApibApi_FileDetail.html
�   �       MicroSaasWebApibApi_GetDocumentsRequest.html
�   �       MicroSaasWebApibApi_JwtValidationService.html
�   �       MicroSaasWebApibApi_Logging.html
�   �       MicroSaasWebApibApi_LoginInfoRequest.html
�   �       MicroSaasWebApibApi_LogLevel.html
�   �       MicroSaasWebApibApi_MarkdownMiddlewareExtensions.html
�   �       MicroSaasWebApibApi_Member.html
�   �       MicroSaasWebApibApi_MemberBriefData.html
�   �       MicroSaasWebApibApi_MemberDetailResponse.html
�   �       MicroSaasWebApibApi_MemberResponse.html
�   �       MicroSaasWebApibApi_MembersResponse.html
�   �       MicroSaasWebApibApi_MicrosoftIdentityModelLogging.html
�   �       MicroSaasWebApibApi_Pages.html
�   �       MicroSaasWebApibApi_PortfolioDataApi.html
�   �       MicroSaasWebApibApi_PortfolioDataApiEndpoints.html
�   �       MicroSaasWebApibApi_ProfileAccountId.html
�   �       MicroSaasWebApibApi_ProfileBrief.html
�   �       MicroSaasWebApibApi_ProfileEngine.html
�   �       MicroSaasWebApibApi_ProfileResponse.html
�   �       MicroSaasWebApibApi_Program.html
�   �       MicroSaasWebApibApi_Properties.html
�   �       MicroSaasWebApibApi_RequestBody.html
�   �       MicroSaasWebApibApi_RequestBodyExample.html
�   �       MicroSaasWebApibApi_ResponseLoggerService.html
�   �       MicroSaasWebApibApi_SearchWorkflowRequest.html
�   �       MicroSaasWebApibApi_Settings.html
�   �       MicroSaasWebApibApi_SharepointController.html
�   �       MicroSaasWebApibApi_StartWorkflowRequest.html
�   �       MicroSaasWebApibApi_Step.html
�   �       MicroSaasWebApibApi_SteppedTile.html
�   �       MicroSaasWebApibApi_SteppedTileCard.html
�   �       MicroSaasWebApibApi_Tenant.html
�   �       MicroSaasWebApibApi_TenantProviderService.html
�   �       MicroSaasWebApibApi_TokenObject.html
�   �       MicroSaasWebApibApi_Value.html
�   �       MicroSaasWebApibApi_WorflowTask.html
�   �       MicroSaasWebApibApi_WorkflowEngine.html
�   �           SharePointAPI_CustomField.html
�   �           SharePointAPI_General.html
�   �           SharePointAPI_GetFiles.html
�   �           SharePointAPI_UploadFile.html
�   �           
�   +---Middleware
�   �       CustomRedirectMiddlewareTests.cs
�   �       
�   +---obj
�   �   MicroSaasWebApibApi.Tests.Unit.csproj.nuget.dgspec.json
�   �   MicroSaasWebApibApi.Tests.Unit.csproj.nuget.g.props
�   �   MicroSaasWebApibApi.Tests.Unit.csproj.nuget.g.targets
�   �   �   project.assets.json
�   �   �   project.nuget.cache
�   �   �   
�   �   +---Debug
�   �       +---net7.0
�   �           �   .NETCoreApp,Version=v7.0.AssemblyAttributes.cs
�   �           MicroSaasWebApibApi.Tests.Unit.AssemblyInfo.cs
�   �           MicroSaasWebApibApi.Tests.Unit.AssemblyInfoInputs.cache
�   �           MicroSaasWebApibApi.Tests.Unit.assets.cache
�   �           MicroSaasWebApibApi.Tests.Unit.csproj.AssemblyReference.cache
�   �           MicroSaasWebApibApi.Tests.Unit.csproj.BuildWithSkipAnalyzers
�   �           MicroSaasWebApibApi.Tests.Unit.csproj.CopyComplete
�   �           MicroSaasWebApibApi.Tests.Unit.csproj.CoreCompileInputs.cache
�   �           MicroSaasWebApibApi.Tests.Unit.csproj.FileListAbsolute.txt
�   �           MicroSaasWebApibApi.Tests.Unit.dll
�   �           MicroSaasWebApibApi.Tests.Unit.GeneratedMSBuildEditorConfig.editorconfig
�   �           MicroSaasWebApibApi.Tests.Unit.genruntimeconfig.cache
�   �           MicroSaasWebApibApi.Tests.Unit.GlobalUsings.g.cs
�   �           MicroSaasWebApibApi.Tests.Unit.pdb
�   �           �   
�   �           +---ref
�   �           � MicroSaasWebApiWebApi.Tests.Unit.dll
�   �           �       
�   �           +---refint
�   �               MicroSaasWebApibApi.Tests.Unit.dll
�   �                   
�   +---Services
�           FakeServiceScopePXW.cs
�           
+---SharePointAPI
    �   General.cs
    �   GetFiles.cs
    �   SharePointAPI.csproj
    �   UploadFile.cs
    �   
    +---bin
    �   +---Debug
    �       +---net7.0
    �               SharePointAPI.deps.json
    �               SharePointAPI.dll
    �               SharePointAPI.pdb
    �               
    +---obj
        �   project.assets.json
        �   project.nuget.cache
        �   SharePointAPI.csproj.nuget.dgspec.json
        �   SharePointAPI.csproj.nuget.g.props
        �   SharePointAPI.csproj.nuget.g.targets
        �   
        +---Debug
            +---net7.0
                �   .NETCoreApp,Version=v7.0.AssemblyAttributes.cs
                �   SharePointAPI.AssemblyInfo.cs
                �   SharePointAPI.AssemblyInfoInputs.cache
                �   SharePointAPI.assets.cache
                �   SharePointAPI.csproj.AssemblyReference.cache
                �   SharePointAPI.csproj.BuildWithSkipAnalyzers
                �   SharePointAPI.csproj.CoreCompileInputs.cache
                �   SharePointAPI.csproj.FileListAbsolute.txt
                �   SharePointAPI.dll
                �   SharePointAPI.GeneratedMSBuildEditorConfig.editorconfig
                �   SharePointAPI.GlobalUsings.g.cs
                �   SharePointAPI.pdb
                �   
                +---ref
                �       SharePointAPI.dll
                �       
                +---refint
                        SharePointAPI.dll
                        
