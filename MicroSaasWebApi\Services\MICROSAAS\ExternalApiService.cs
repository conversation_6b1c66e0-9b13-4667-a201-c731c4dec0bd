﻿using Newtonsoft.Json;
using MicroSaasWebApi.Properties.PXW;
using MicroSaasWebApi.Services.PXW.Interface;
using System.Net;
using System.Net.Http.Headers;
using System.Security.Authentication;
using System.Text;
using System.Web;

namespace MicroSaasWebApi.Services.PXW
{
    public class ExternalApiService : IExternalApiService
    {
        private readonly HttpClient _httpClient;
        public string apiUrl = string.Empty;

        private readonly ILoggerService _loggerService;

        public ExternalApiService(IHttpClientFactory httpClientFactory) => _httpClient = httpClientFactory.CreateClient("MyClient");

        public ExternalApiService(IHttpClientFactory httpClientFactory, ILoggerService loggerService)
        {
            _httpClient = httpClientFactory.CreateClient("MyClient");
            _loggerService = loggerService;
        }

        public async Task<(bool success, object? result, string? errorMessage, HttpStatusCode statusCode)>
            GetThirdPartyDataAsync(string apiUrl, string httpMethod, string? jwtToken = "",
            Dictionary<string, string>? queryParametersDictionary = null,
            string? payloadString = null, string? userLanguage = "en")
        {
            try
            {
                var builder = new UriBuilder(apiUrl);
                HttpResponseMessage response;

                if (queryParametersDictionary != null && queryParametersDictionary.Any())
                {
                    var query = HttpUtility.ParseQueryString(builder.Query);
                    foreach (var parameter in queryParametersDictionary)
                    {
                        query[parameter.Key] = parameter.Value;
                    }
                    builder.Query = query.ToString();
                }
                using (var request = new HttpRequestMessage(new HttpMethod(httpMethod.ToUpper()), builder.ToString()))
                {
                    if (!string.IsNullOrEmpty(jwtToken))
                    {
                        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", jwtToken);
                    }
                    request.Headers.AcceptLanguage.Add(new System.Net.Http.Headers.StringWithQualityHeaderValue(userLanguage ?? "en"));
                    // Set the request content based on the type of requestContent
                    if (payloadString != null && payloadString != string.Empty)
                    {
                        request.Content = new StringContent(payloadString, Encoding.UTF8, "application/json");
                    }

                    response = await _httpClient.SendAsync(request);

                    var statusCode = response.StatusCode;
                    //var content = await response.Content.ReadAsStringAsync();

                    if (response.IsSuccessStatusCode)
                    {
                        var contentType = response.Content.Headers.ContentType?.MediaType;

                        if (contentType == "application/json")
                        {
                            var jsonContent = await response.Content.ReadAsStringAsync();
                            return (true, JsonConvert.DeserializeObject(jsonContent), null, statusCode);
                        }
                        else if (contentType == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                        {
                            var excelContent = await response.Content.ReadAsByteArrayAsync();
                            return (true, excelContent, null, statusCode);
                        }
                        else if (contentType == "application/octet-stream" || response.Content.Headers.ContentDisposition?.FileName != null)
                        {
                            var fileContent = await response.Content.ReadAsByteArrayAsync();
                            return (true, fileContent, null, statusCode);
                        }
                        else if (contentType == "text/plain")
                        {
                            var textContent = await response.Content.ReadAsStringAsync();
                            return (true, textContent, null, statusCode);
                        }
                        else
                        {
                            var unknownContent = await response.Content.ReadAsStringAsync();
                            return (true, unknownContent, null, statusCode);
                        }
                    }
                    else
                    {
                        var errorContent = await response.Content.ReadAsStringAsync();
                        return (false, errorContent, errorContent, statusCode);
                    }
                }
            }
            catch (HttpRequestException ex)
            {
                // Handle exceptions related to the request, such as network issues
                return (false, ex.ToDetailedString(), ex.Message, HttpStatusCode.BadRequest);
            }
            catch (JsonReaderException ex)
            {
                // Handle exceptions related to JSON parsing
                return (false, ex.ToDetailedString(), ex.Message, HttpStatusCode.InternalServerError);
            }
            catch (Exception ex)
            {
                // Handle all other exceptions
                return (false, ex.ToDetailedString(), ex.Message, HttpStatusCode.InternalServerError);
            }
        }

        public async Task<HttpResponseMessage> GetFullResponseThirdPartyDataAsync(
              string apiUrl,
              string httpMethod,
              string? jwtToken = "",
              Dictionary<string, string>? queryParametersDictionary = null,
              string? payloadString = null,
              string? pydioUserToken = null
          )
        {
            try
            {
                var builder = new UriBuilder(apiUrl);
                HttpResponseMessage response;

                if (queryParametersDictionary != null && queryParametersDictionary.Any())
                {
                    var query = HttpUtility.ParseQueryString(builder.Query);
                    foreach (var parameter in queryParametersDictionary)
                    {
                        query[parameter.Key] = parameter.Value;
                    }
                    builder.Query = query.ToString();
                }
                using (var request = new HttpRequestMessage(new HttpMethod(httpMethod.ToUpper()), builder.ToString()))
                {
                    if (!string.IsNullOrEmpty(jwtToken))
                    {
                        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", jwtToken);
                    }
                    if (!string.IsNullOrEmpty(pydioUserToken))
                    {
                        request.Headers.Add("Pydio-User-Token", pydioUserToken);
                    }
                    // Set the request content based on the type of requestContent
                    if (payloadString != null && payloadString != string.Empty)
                    {
                        request.Content = new StringContent(payloadString, Encoding.UTF8, "application/json");
                    }
                    request.Content?.Headers.TryAddWithoutValidation("Accept", "application/json");
                    request.Content?.Headers.TryAddWithoutValidation("Content-Type", "application/json");
                    //var requestDetails = await ApiHelper.GetRequestDetailsAsync(request);
                    //await _loggerService.LogInformationAsync(requestDetails);
                    try{
                        response = await _httpClient.SendAsync(request);
                        return response;
                    }catch (Exception ex)
                    {
                        return HandleRequestException(ex);
                    }
                    
                }
            }
            catch (HttpRequestException ex)
            {
                // Handle exceptions related to the request, such as network issues
                await _loggerService.LogErrorAsync(ex, ex.Message);
                throw;
            }
            catch (JsonReaderException ex)
            {
                // Handle exceptions related to JSON parsing
                await _loggerService.LogErrorAsync(ex, ex.Message);
                throw;
            }
            catch (Exception ex)
            {
                // Handle all other exceptions
                await _loggerService.LogErrorAsync(ex, ex.Message);
                throw;
            }
        }

        public async Task<MultipartFormDataContent> PrepareFilteredFormDataAsync(HttpRequest request, string apiUrlString)
        {
            var filteredFormData = new MultipartFormDataContent();

            // List of keys to exclude
            var excludedKeys = new HashSet<string>
            {
                "TenantGuid",
                "Controller",
                "Action",
                "Component"
            };

            var formCollection = request.Form;
            apiUrl = apiUrlString;

            // Log the number of files received
            await _loggerService.LogInformationAsync($"Number of files received: {formCollection.Files.Count}");

            foreach (var field in formCollection)
            {
                string placeholderKey = $"{{{field.Key}}}";

                // Replace placeholders in the apiUrl
                if (apiUrl.Contains(placeholderKey))
                {
                    apiUrl = apiUrl.Replace(placeholderKey, field.Value.ToString());
                    await _loggerService.LogInformationAsync($"Replaced placeholder in URL: {placeholderKey} with value: {field.Value}");
                }
                else if (!excludedKeys.Contains(field.Key))
                {
                    // Add non-excluded fields to form data
                    filteredFormData.Add(new StringContent(field.Value), field.Key);
                    await _loggerService.LogInformationAsync($"Added field to form data: {field.Key} with value: {field.Value}");
                }
            }

            // Add files to the form data if not excluded
            foreach (var file in formCollection.Files)
            {
                if (!excludedKeys.Contains(file.Name))
                {
                    try
                    {
                        var fileStream = file.OpenReadStream();
                        var fileContent = new StreamContent(fileStream);
                        fileContent.Headers.ContentDisposition = new ContentDispositionHeaderValue("form-data")
                        {
                            Name = file.Name,
                            FileName = file.FileName
                        };
                        fileContent.Headers.ContentType = new MediaTypeHeaderValue(file.ContentType);

                        filteredFormData.Add(fileContent, file.Name, file.FileName);
                        await _loggerService.LogInformationAsync($"Added file to form data: {file.Name} with filename: {file.FileName} and content type: {file.ContentType}");
                    }
                    catch (Exception ex)
                    {
                        await _loggerService.LogErrorAsync(ex, $"Failed to add file {file.Name} with filename: {file.FileName} to form data.");
                    }
                }
            }

            // Log final URL
            await _loggerService.LogInformationAsync($"ApiUrl: set with value: {apiUrl}");

            return filteredFormData;
        }

        public async Task<HttpResponseMessage> GetThirdPartyAsyncFilteredFormData(
            string apiUrlString,
            string httpMethod,
            string jwtToken,
            HttpRequest request,
            string? pydioUserToken = null)
        {
            try
            {
                // Prepare the filtered form data using the helper method
                var multipartContent = await PrepareFilteredFormDataAsync(request, apiUrlString);

                var builder = new UriBuilder(apiUrl);

                using (var newRequest = new HttpRequestMessage(new HttpMethod(httpMethod.ToUpper()), builder.ToString()))
                {
                    await _loggerService.LogInformationAsync("IN EXTERNAL API SERVICE FILTERED FORM DATA");
                    // Set the prepared multipart content as the request content
                    newRequest.Content = multipartContent;                    
                    // Set the Content-Type header from multipartContent
                    if (multipartContent.Headers.ContentType != null)
                    {
                        newRequest.Content.Headers.ContentType = multipartContent.Headers.ContentType;
                    }                    
                    // Add headers from the original request
                    foreach (var header in request.Headers)
                    {
                        if (!newRequest.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray()) && newRequest.Content != null)
                        {
                            if (header.Key != "Authorization" && header.Key != "Content-Length")
                            {
                                newRequest.Content?.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray());
                            }
                        }
                    }
                    if (!string.IsNullOrEmpty(jwtToken))
                    {
                        newRequest.Headers.Authorization = new AuthenticationHeaderValue("Bearer", jwtToken);
                    }
                    if (!string.IsNullOrEmpty(pydioUserToken))
                    {
                        newRequest.Headers.Add("Pydio-User-Token", pydioUserToken);
                    }
                    // Print the keys and filenames from the newRequest content
                    if (newRequest.Content is MultipartFormDataContent multipart)
                    {
                        foreach (var content in multipart)
                        {
                            if (content.Headers.ContentDisposition != null)
                            {
                                if (content is StreamContent streamContent)
                                {
                                    // Calculate file size by reading the stream content length
                                    var fileSize = streamContent.Headers.ContentLength ?? 0;

                                    await _loggerService.LogInformationAsync(
                                        $"Preparing to send file: {content.Headers.ContentDisposition.Name} " +
                                        $"with filename: {ApiHelper.UnescapeFilename(content.Headers.ContentDisposition.FileName)} " +
                                        $"and content type: {content.Headers.ContentType} " +
                                        $"and size: {fileSize} bytes");
                                }
                                else
                                {
                                    await _loggerService.LogInformationAsync(
                                        $"Preparing to send field: {content.Headers.ContentDisposition.Name} " +
                                        $"with value: {await content.ReadAsStringAsync()}");
                                }
                            }
                        }
                    }
                    // Calculate Content-Length by reading the multipartContent into a MemoryStream
                    using (var memoryStream = new MemoryStream())
                    {
                        await multipartContent.CopyToAsync(memoryStream);
                        memoryStream.Position = 0;
                        long contentLength = memoryStream.Length;
                        
                        // Set Content-Length header manually
                        newRequest.Content.Headers.ContentLength = contentLength;
                        await _loggerService.LogInformationAsync($"Calculated Content-Length: {contentLength}");
                    }
                    await _loggerService.LogInformationAsync("IN EXTERNAL API SERVICE FILTERED FORM DATA, WILL SEND REQUEST");

                    HttpResponseMessage response;
                    try
                    {
                        newRequest.Headers.Host = builder.Host;
                        //var requestDetails = await ApiHelper.GetRequestDetailsAsync(newRequest);
                        //await _loggerService.LogInformationAsync(requestDetails);

                        // Send the request and log the response
                        response = await _httpClient.SendAsync(newRequest, HttpCompletionOption.ResponseContentRead);

                        await _loggerService.LogInformationAsync("RESPONSE STATUS CODE: " + response.StatusCode);

                        if (!response.IsSuccessStatusCode)
                        {
                            var errorMessage = await response.Content.ReadAsStringAsync();
                            await _loggerService.LogErrorAsync(new Exception($"External API error: {errorMessage}"),
                                $"External API responded with status code {response.StatusCode} and message: {errorMessage}");
                        }

                        return response;
                    }
                    catch (Exception ex)
                    {
                        return HandleRequestException(ex);
                    }
                }
            }
            catch (Exception ex)
            {
                var message = "An error occurred in GetThirdPartyAsyncFilteredFormData method.";
                await _loggerService.LogErrorAsync(ex, message);
                throw;
            }
        }

        private HttpResponseMessage HandleRequestException(Exception ex)
        {
            if (ex is TaskCanceledException)
            {
                var message = "Request timeout. The external API did not respond in time. ";
                _loggerService.LogErrorAsync(ex, message);
                return new HttpResponseMessage(HttpStatusCode.GatewayTimeout)
                {
                    Content = new StringContent(message + ex.Message)
                };
            }
            else if (ex is HttpRequestException)
            {
                var message = "HTTP request error occurred while contacting the external API. ";
                _loggerService.LogErrorAsync(ex, message);
                return new HttpResponseMessage(HttpStatusCode.ServiceUnavailable)
                {
                    Content = new StringContent(message + ex.Message)
                };
            }
            else if (ex is InvalidOperationException)
            {
                var message = "Invalid operation error occurred while processing the request. ";
                _loggerService.LogErrorAsync(ex, message);
                return new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent(message + ex.Message)
                };
            }
            else if (ex is AuthenticationException)
            {
                var message = "Authentication error occurred while accessing the external API. ";
                _loggerService.LogErrorAsync(ex, message);
                return new HttpResponseMessage(HttpStatusCode.Unauthorized)
                {
                    Content = new StringContent(message + ex.Message)
                };
            }
            else
            {
                var message = "An unexpected error occurred while processing the request. ";
                _loggerService.LogErrorAsync(ex, message);
                return new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent(message)
                };
            }
        }

        public async Task<HttpResponseMessage> GetThirdPartyAsyncFormData(string apiUrl, string httpMethod, string jwtToken, HttpRequest request, string? pydioUserToken = null)
        {
            try
            {
                var builder = new UriBuilder(apiUrl);

                using (var newRequest = new HttpRequestMessage(new HttpMethod(httpMethod.ToUpper()), builder.ToString()))
                {
                    // Log information
                    _loggerService.LogInformationAsync("IN EXTERNAL API SERVICE FORM DATA");

                    // Add content from the original request
                    var streamContent = new StreamContent(request.Body);
                    newRequest.Content = streamContent;

                    // Add headers from the original request
                    foreach (var header in request.Headers)
                    {
                        if (!newRequest.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray()) && newRequest.Content != null)
                        {
                            if (header.Key != "Authorization")
                            {
                                newRequest.Content?.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray());
                            }
                        }
                    }

                    // Add JWT token if available
                    if (!string.IsNullOrEmpty(jwtToken))
                    {
                        newRequest.Headers.Authorization = new AuthenticationHeaderValue("Bearer", jwtToken);
                    }
                    if (!string.IsNullOrEmpty(pydioUserToken))
                    {
                        newRequest.Headers.Add("Pydio-User-Token", pydioUserToken);
                    }
                    // Log before sending request
                    _loggerService.LogInformationAsync("IN EXTERNAL API SERVICE FORM DATA, WILL SEND REQUEST");

                    // Send request with timeout
                    HttpResponseMessage response;
                    try
                    {
                        newRequest.Headers.Host = builder.Host;
                        response = await _httpClient.SendAsync(newRequest, HttpCompletionOption.ResponseContentRead);
                        // Log response status
                        var response_status = response.StatusCode;
                        _loggerService.LogInformationAsync("RESPONSE STATUS CODE: " + response_status);
                        return response;
                    }
                    catch (Exception ex)
                    {
                        // Handle specific exceptions first
                        if (ex is TaskCanceledException)
                        {
                            return new HttpResponseMessage(HttpStatusCode.GatewayTimeout);
                        }
                        else if (ex is HttpRequestException)
                        {
                            return new HttpResponseMessage(HttpStatusCode.ServiceUnavailable);
                        }
                        else if (ex is InvalidOperationException)
                        {
                            return new HttpResponseMessage(HttpStatusCode.BadRequest);
                        }
                        else if (ex is AuthenticationException)
                        {
                            return new HttpResponseMessage(HttpStatusCode.Unauthorized);
                        }

                        // General catch-all for other exceptions
                        return new HttpResponseMessage(HttpStatusCode.InternalServerError);
                    }
                }
            }
            catch (HttpRequestException ex)
            {
                // Handle exceptions related to the request, such as network issues
                await _loggerService.LogErrorAsync(ex, ex.Message);
                throw;
            }
            catch (JsonReaderException ex)
            {
                // Handle exceptions related to JSON parsing
                await _loggerService.LogErrorAsync(ex, ex.Message);
                throw;
            }
            catch (Exception ex)
            {
                // Handle all other exceptions
                await _loggerService.LogErrorAsync(ex, ex.Message);
                throw;
            }
        }

        public async Task<HttpResponseMessage> GetThirdPartyDataAsyncNoTransform(
          string apiUrl,
          string httpMethod,
          string? jwtToken = "",
          Dictionary<string, string>? queryParametersDictionary = null,
          string? payloadString = null,
          string? pydioUserToken = null
          )
        {
            try
            {
                var builder = new UriBuilder(apiUrl);
                HttpResponseMessage response;

                if (queryParametersDictionary != null && queryParametersDictionary.Any())
                {
                    var query = HttpUtility.ParseQueryString(builder.Query);
                    foreach (var parameter in queryParametersDictionary)
                    {
                        query[parameter.Key] = parameter.Value;
                    }
                    builder.Query = query.ToString();
                }
                using (var request = new HttpRequestMessage(new HttpMethod(httpMethod.ToUpper()), builder.ToString()))
                {
                    if (!string.IsNullOrEmpty(jwtToken))
                    {
                        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", jwtToken);
                    }
                    if (!string.IsNullOrEmpty(pydioUserToken))
                    {
                        request.Headers.Add("Pydio-User-Token", pydioUserToken);
                    }
                    // Set the request content based on the type of requestContent
                    if (payloadString != null && payloadString != string.Empty)
                    {
                        request.Content = new StringContent(payloadString, Encoding.UTF8, "application/json");
                    }

                    response = await _httpClient.SendAsync(request);
                    return response;
                }
            }
            catch (HttpRequestException ex)
            {
                // Handle exceptions related to the request, such as network issues
                throw new Exception(ex.Message);
            }
            catch (JsonReaderException ex)
            {
                // Handle exceptions related to JSON parsing
                throw new Exception(ex.Message);
            }
            catch (Exception ex)
            {
                // Handle all other exceptions
                throw new Exception(ex.Message);
            }
        }

        // Helper method to check if the content is in JSON format
        static bool IsJsonContent(string content)
        {
            try
            {
                _ = Newtonsoft.Json.Linq.JToken.Parse(content);
                return true;
            }
            catch
            {
                return false;
            }
        }

    }
}
