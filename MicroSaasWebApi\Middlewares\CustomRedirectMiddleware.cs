﻿using Microsoft.IdentityModel.Tokens;
using MicroSaasWebApi.Models.PXW;
using MicroSaasWebApi.Models.PXW.AppSettings;
using MicroSaasWebApi.Properties.Enums.PXW;
using MicroSaasWebApi.Properties.PXW;
using MicroSaasWebApi.Services.PXW.Interface;
using MicroSaasWebApi.Services.PXW.Utilities;
using System.Text.Json;
using System.Text.RegularExpressions;
namespace MicroSaasWebApi.Middlewares
{
    public class CustomRedirectMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILoggerService _loggerService;
        private readonly IApplicationSettingsService _applicationSettingsService;
        private readonly ITenantProviderService _tenantProviderService;
        private readonly IConfiguration _configuration;

        public string?[] TenantGuids { get; set; }

        public CustomRedirectMiddleware(RequestDelegate next,
            IServiceProvider serviceProvider, IConfiguration configuration)
        {
            _next = next;
            using (var scope = serviceProvider.CreateScope())
            {
                _loggerService = scope.ServiceProvider.GetRequiredService<ILoggerService>();
                _applicationSettingsService = scope.ServiceProvider.GetRequiredService<IApplicationSettingsService>();
                _tenantProviderService = scope.ServiceProvider.GetRequiredService<ITenantProviderService>();
            }
            _configuration = configuration;
            TenantGuids = Array.Empty<string>();

        }

        public async Task InvokeAsync(HttpContext context)
        {
            if (context == null)
            {
                await LogAndHandleNullContext();
                return;
            }
            context.Request.EnableBuffering();
            await _loggerService.LogInformationAsync("CustomRedirectMiddleware is invoked.");
            await _loggerService.LogRequestInformationAsync();

            await _applicationSettingsService.SetAppConfigurationWithKeyVault();

            // later in your code:
            var tenants = new List<Tenant>();
            _configuration.Bind($"{Constants.Tenants}", tenants);
            if (tenants.Any())
            {
                TenantGuids = tenants.Select(t => t.TenantGuid).ToArray();
            }
            else
            {
                await _loggerService.SetResponseAndLogAsync(StatusCodes.Status403Forbidden, "No Client Configuration Found.");
            }
            if (context.Request.ContentType != null &&
            MultipartRequestHelper.IsMultipartContentType(context.Request.ContentType))
            {
                await ValidateMultipartRequest(context);
            }
            else
            {
                await ValidateNonMultipartRequest(context);
            }
        }
        private async Task LogAndHandleNullContext()
        {
            await _loggerService.SetResponseAndLogAsync(StatusCodes.Status403Forbidden, "No request Information is found.");
        }

        private async Task ValidateMultipartRequest(HttpContext context)
        {
            bool processed = false;
            string missingFieldsMessage = string.Empty;
            if (context != null)
            {
                List<string> missingFields = new List<string>();
                if ((context?.Request.Path == Constants.api)
                      && (context.Request.Method == HttpMethods.Post || context.Request.Method == HttpMethods.Get))
                {
                    ValidateField(context.Request.Form["Component"], "Component", missingFields);
                    ValidateField(context.Request.Form["Controller"], "Controller", missingFields);
                    ValidateField(context.Request.Form["Action"], "Action", missingFields);
                    ValidateGuidField(context.Request.Form["TenantGuid"], "TenantGuid", missingFields);
                }
                var tenantGuid = context?.Request.Form["TenantGuid"];
                var controller = context?.Request.Form["Controller"];
                var action = context?.Request.Form["Action"];

                if (!string.IsNullOrWhiteSpace(tenantGuid) && TenantGuids.Contains(tenantGuid.ToString()))
                {
                    await _applicationSettingsService.SetAppSettingsFilteredTenantInHttpContext(tenantGuid);
                    await _applicationSettingsService.SetAppSettingsEndPointsInHttpContext();
                    _tenantProviderService.SetTenantId(tenantGuid);
                }
                else
                {
                    string logMessage = string.IsNullOrWhiteSpace(tenantGuid) ? "TenantGuid is null or empty." : $"Incorrect TenantGuid: {tenantGuid}";
                    await _loggerService.LogInformationAsync(logMessage);
                    missingFields.Add(logMessage);
                    processed = true;
                }
                if (missingFields.Count > 0)
                {
                    missingFieldsMessage = $"The following fields are missing or empty: {string.Join(", ", missingFields)}";
                    await _loggerService.LogInformationAsync(missingFieldsMessage);
                    await _loggerService.SetResponseAndLogAsync(StatusCodes.Status400BadRequest, missingFieldsMessage);
                    processed = true;  // The response is already handled, no need to invoke next middleware
                    return;
                }
                context.Request.Path = $"{context.Request.Path}/{controller}/{action}";
            }

            if (!processed)
            {
                await _next(context);
            }
        }

        private async Task ValidateNonMultipartRequest(HttpContext context)
        {
            bool processed = false;
            string missingFieldsMessage = string.Empty;
            List<string> missingFields = new List<string>();
            RequestBody? input = null;
            string requestBodyText = string.Empty;
            var requestBodyStream = new MemoryStream();
            try
            {
                await context.Request.Body.CopyToAsync(requestBodyStream);
                requestBodyStream?.Seek(0, SeekOrigin.Begin);
                if (requestBodyStream != null && requestBodyStream.Length > 0)
                {
                    using (var reader = new StreamReader(requestBodyStream))
                    {
                        requestBodyText = reader.ReadToEnd();
                        context.Items[Constants.RequestBodyText] = requestBodyText;
                    }
                    if (!requestBodyText.IsNullOrEmpty())
                    {
                        input = JsonSerializer.Deserialize<RequestBody>(requestBodyText);
                        context.Items[Constants.RequestBody] = input;
                    }
                }
            }
            catch
            {
                requestBodyStream.Dispose();
                throw;
            }

            if ((context?.Request.Path == Constants.api)
                && (context.Request.Method == HttpMethods.Post || context.Request.Method == HttpMethods.Get))
            {
                try
                {
                    if (input != null)
                    {
                        ValidateField(input.Component, "Component", missingFields);
                        ValidateField(input.Controller, "Controller", missingFields);
                        ValidateField(input.Action, "Action", missingFields);
                        ValidateGuidField(input.TenantGuid, "TenantGuid", missingFields);

                        if (TenantGuids.Contains(input.TenantGuid))
                        {
                            await _applicationSettingsService.SetAppSettingsFilteredTenantInHttpContext(input.TenantGuid);
                            await _applicationSettingsService.SetAppSettingsEndPointsInHttpContext();

                            _tenantProviderService.SetTenantId(input.TenantGuid);
                        }
                        else { missingFields.Add("Incorrect TenantGuid Passed in RequestBody"); }

                        if (TenantGuids.Contains(input.Language))
                        {
                            _applicationSettingsService.SetLanguageInHttpContext(input.Language);
                        }
                        else { _applicationSettingsService.SetLanguageInHttpContext(Constants.English); }

                        string controller = input.Controller;
                        string[] controllerArray = input.Controller.Split('/');
                        if (controllerArray.Length > 0)
                        {
                            controller = controllerArray[controllerArray.Length - 1];
                        }
                        if (missingFields.Count > 0)
                        {
                            missingFieldsMessage = $"The following fields are missing or empty: {string.Join(", ", missingFields)}";
                            await _loggerService.LogInformationAsync(missingFieldsMessage);
                            await _loggerService.SetResponseAndLogAsync(StatusCodes.Status400BadRequest, missingFieldsMessage);
                            processed = true;  // The response is already handled, no need to invoke next middleware
                            return;
                        }


                        var contextPath = $"{context.Request.Path}/{input.Controller}/{input.Action}";
                        context.Request.Path = (contextPath);
                    }
                    else
                    {
                        await _loggerService.SetResponseAndLogAsync(StatusCodes.Status400BadRequest, "Input Request is Missing.");
                        return;
                    }
                }
                finally
                {
                    if (input is IDisposable disposable)
                    {
                        disposable.Dispose();
                    }
                }
            }

            if (!processed)
            {
                await _next(context);
            }
        }

        private static void ValidateField(string? fieldValue, string fieldName, List<string> missingFields)
        {
            if (string.IsNullOrWhiteSpace(fieldValue) || !IsValidField(fieldValue))
            {
                missingFields.Add(fieldName);
            }
        }

        private static void ValidateGuidField(string? fieldValue, string fieldName, List<string> missingFields)
        {
            if (string.IsNullOrWhiteSpace(fieldValue) || !Guid.TryParse(fieldValue, out _))
            {
                missingFields.Add(fieldName);
            }
        }

        private static bool IsValidField(string? fieldValue)
        {
            if (fieldValue == null) return false;
            return Regex.IsMatch(fieldValue, Constants.regexPattern);
        }
    }
}
