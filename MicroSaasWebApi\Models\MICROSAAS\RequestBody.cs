﻿using System.Text.Json;

namespace MicroSaasWebApi.Models.PXW
{
    /// <summary>
    /// Represents the input parameters for an API call.
    /// </summary>
    public class RequestBody : IDisposable
    {

        public RequestBody(string component, string tenantGuid, string language,
            string controller, string action,
            Dictionary<string, JsonElement>? pathParameters,
            Dictionary<string, JsonElement>? queryParameters,
            Dictionary<string, JsonElement>? payloadParameters)
        {
            Component = component;
            TenantGuid = tenantGuid;
            Language = language;
            Controller = controller;
            Action = action;
            PathParameters = pathParameters;
            QueryParameters = queryParameters;
            PayloadParameters = payloadParameters;
        }

        /// <summary>
        /// Gets or sets the name of the component.
        /// </summary>
        /// <remarks>
        /// This property is used for logging purposes.
        /// </remarks>
        public string Component { get; set; }

        /// <summary>
        /// Gets or sets the name of the tenantguid.
        /// </summary>
        public string TenantGuid { get; set; }
        /// <summary>
        /// Gets or sets the Language to be used in the portal.
        /// </summary>
        public string Language { get; set; }

        /// <summary>
        /// Gets or sets the name of the controller.
        /// </summary>
        public string Controller { get; set; }

        /// <summary>
        /// Gets or sets the name of the action.
        /// </summary>
        public string Action { get; set; }

        /// <summary>
        /// Gets or sets the optional path parameters for the API call.
        /// </summary>
        public Dictionary<string, JsonElement>? PathParameters { get; set; }

        /// <summary>
        /// Gets or sets the optional query parameters for the API call.
        /// </summary>
        public Dictionary<string, JsonElement>? QueryParameters { get; set; }
        /// <summary>
        /// Gets or sets the optional payload parameters for the API call.
        /// </summary>
        public Dictionary<string, JsonElement>? PayloadParameters { get; set; }

        public void Dispose()
        {
            // Dispose of any resources if needed
        }
    }

}
