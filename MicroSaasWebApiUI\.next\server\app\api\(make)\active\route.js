(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[654],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},6195:e=>{"use strict";e.exports=require("node:buffer")},2480:()=>{},358:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>P,default:()=>g});var a={};r.r(a),r.d(a,{GET:()=>k,POST:()=>f,dynamic:()=>l,runtime:()=>w});var o={};r.r(o),r.d(o,{originalPathname:()=>j,patchFetch:()=>A,requestAsyncStorage:()=>m,routeModule:()=>v,serverHooks:()=>_,staticGenerationAsyncStorage:()=>E});var i=r(932),s=r(2561),n=r(4828),c=r(6631),u=r(6333),d=r(462),p=r(1175),h=r(9985);let l="force-dynamic",w="edge";async function f(e){try{let t=await (0,d.a)(),{projectId:r}=await e.json();if(!t||!r)return h.xk.json({error:"Unauthorized or missing project ID"},{status:401});let a=await u.Z.project.findUnique({where:{id:r}});if(!a||!a.scenario_id)return h.xk.json({error:"Project or scenario not found"},{status:404});if("active"==a.status){if(!(await p.Z.post(`https://eu2.make.com/api/v2/scenarios/${a.scenario_id}/stop`,{},{headers:{Authorization:`Token ${process.env.MAKE_API_KEY}`}})).data.scenario)throw Error("Failed to deactivate scenario");if(!await u.Z.project.update({where:{id:r},data:{status:"inactive"}}))throw Error("Failed to update project")}else{if(!(await p.Z.post(`https://eu2.make.com/api/v2/scenarios/${a.scenario_id}/start`,{},{headers:{Authorization:`Token ${process.env.MAKE_API_KEY}`}})).data.scenario)throw Error("Failed to deactivate scenario");if(!await u.Z.project.update({where:{id:r},data:{status:"active"}}))throw Error("Failed to update project")}return h.xk.json({success:!0})}catch(e){return console.error("Activation error:",e),h.xk.json({error:"Failed to activate scenario"},{status:500})}}async function k(e){try{let t=await (0,d.a)(),{searchParams:r}=new URL(e.url),a=(await u.Z.project.findUnique({where:{id:r.get("id")}})).scenario_id;if(!t||!a)throw Error("Something went wrong");let o=await p.Z.get(`https://eu2.make.com/api/v2/scenarios/${a}`,{headers:{Authorization:`Token ${process.env.MAKE_API_KEY}`}});if(!o.data.scenario.id)throw Error("check status error");let{isActive:i}=o.data.scenario;return h.xk.json({success:!0,isActive:i})}catch(e){if(console.error("Connection error:",e),e)return h.xk.json({error:e.message});return h.xk.json({error:"An unexpected error occurred"},{status:500})}}let v=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/(make)/active/route",pathname:"/api/active",filename:"route",bundlePath:"app/api/(make)/active/route"},resolvedPagePath:"C:\\Projects\\PersonalPortal\\src\\app\\api\\(make)\\active\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:m,staticGenerationAsyncStorage:E,serverHooks:_}=v,j="/api/(make)/active/route";function A(){return(0,c.XH)({serverHooks:_,staticGenerationAsyncStorage:E})}let P=o,g=i.a.wrap(v)},6333:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=new(r(7225)).PrismaClient}},e=>{var t=t=>e(e.s=t);e.O(0,[520,288,225,436,657,551],()=>t(358));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/(make)/active/route"]=r}]);
//# sourceMappingURL=route.js.map