# See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:7.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:7.0 AS build
WORKDIR /src
COPY ["MicroSaasWebApi/MicroSaasWebApi.csproj", "MicroSaasWebApi/"]
RUN dotnet restore "MicroSaasWebApi/MicroSaasWebApi.csproj"
COPY . .
WORKDIR "/src/MicroSaasWebApi"
RUN dotnet build "MicroSaasWebApi.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "MicroSaasWebApi.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
EXPOSE 80
EXPOSE 443

COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "MicroSaasWebApi.dll"]

ENV PORT 8080
ENV PORT 7163
EXPOSE 8080
EXPOSE 7163
