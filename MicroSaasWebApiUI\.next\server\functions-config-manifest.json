{"version": 1, "functions": {"/api/scenarios": {}, "/api/link": {}, "/api/active": {}, "/api/scenarios/openAIAssistant": {}, "/api/projects": {}, "/api/stripe/create-checkout": {}, "/api/test-email": {}, "/api/webhook/resend": {}, "/api/waiting-list": {}, "/cv": {}, "/api/workflows/openAIAssistant": {}, "/api/webhook/stripe": {}, "/api/stripe/create-portal": {}, "/ad-demo": {}, "/blog": {}, "/privacy-policy": {}, "/security-policy": {}, "/waiting-list": {}, "/tos": {}}}