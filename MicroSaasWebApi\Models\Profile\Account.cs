﻿namespace MicroSaasWebApi.Models.Profile
{


    /// <summary>
    /// Summary description for Account
    /// </summary>
    [Serializable()]
    public class Account
    {
        public string? AccountCode { get; set; }
        public string? CustodianAccountCode { get; set; }
        public string? AccountVisualizationID { get; set; }
        public string? AccountName { get; set; }
        public string? FriendlyName { get; set; }
        public string? AccountType { get; set; }
        public string? Currency { get; set; }

        public string SelectionName
        {
            get
            {
                try
                {
                    if (!string.IsNullOrEmpty(FriendlyName))
                        return FriendlyName + " - " + CustodianAccountCode;
                    else if (!string.IsNullOrEmpty(CustodianAccountCode))
                        return $"{AccountType?.ToString()} - {CustodianAccountCode}";
                    else
                        return "Account Not Setup Yet";
                }
                catch
                {
                    return "Account Not Setup Yet";
                }
            }
        }
        public int ID { get; set; }
    }
}