const Auth = () => {
  return (
    <svg
      width="34"
      height="35"
      viewBox="0 0 34 35"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_521_32677)">
        <path
          d="M17 7.13699C13.9291 7.13699 11.4304 9.63562 11.4304 12.7065C11.4304 15.7775 13.9291 18.2761 17 18.2761C20.0709 18.2761 22.5696 15.7775 22.5696 12.7065C22.5696 9.63562 20.0709 7.13699 17 7.13699ZM17 16.5814C14.8635 16.5814 13.1251 14.843 13.1251 12.7065C13.1251 10.57 14.8635 8.83166 17 8.83166C19.1365 8.83166 20.8749 10.57 20.8749 12.7065C20.8749 14.843 19.1365 16.5814 17 16.5814Z"
          fill="currentColor"
          stroke="currentColor"
          strokeWidth="0.514113"
        />
        <path
          d="M17 1.81592C8.06524 1.81592 0.796875 9.08429 0.796875 18.019C0.796875 26.9538 8.06524 34.2222 17 34.2222C25.9348 34.2222 33.2031 26.9538 33.2031 18.019C33.2031 9.08429 25.9348 1.81592 17 1.81592ZM7.84602 29.2413C8.14929 24.4733 12.1824 20.6458 17 20.6458C21.8861 20.6458 25.8439 24.3955 26.1539 29.2414C23.6564 31.2834 20.4693 32.5104 17 32.5104C13.5307 32.5104 10.3446 31.2834 7.84602 29.2413ZM31.4913 18.019C31.4913 21.7756 30.051 25.2004 27.6956 27.7774C26.738 22.7009 22.3327 18.934 17 18.934C11.7433 18.934 7.27742 22.7678 6.30843 27.7828C3.95084 25.2044 2.50868 21.7777 2.50868 18.019C2.50868 10.0282 9.00913 3.52772 17 3.52772C24.9909 3.52772 31.4913 10.0282 31.4913 18.019Z"
          fill="currentColor"
          stroke="currentColor"
          strokeWidth="0.53125"
        />
      </g>
      <defs>
        <clipPath id="clip0_521_32677">
          <rect
            width="34"
            height="35"
            fill="currentColor"
            transform="translate(0 0.688965)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default Auth;
