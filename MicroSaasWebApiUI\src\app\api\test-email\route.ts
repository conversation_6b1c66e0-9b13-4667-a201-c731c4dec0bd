import { resendService } from '@/libs/resend';
import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';
// Make this route public and not require authentication
export const runtime = 'edge';

export async function POST(req: NextRequest) {
  try {
    const { email } = await req.json();

    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }

    console.log('Sending test email to:', email);

    // Send a test email
    const result = await resendService.sendThanksYouEmail(email);

    console.log('Email sent successfully:', result);

    return NextResponse.json({
      message: 'Test email sent successfully',
      id: result.id
    });
  } catch (error) {
    console.error('Error sending test email:', error);
    return NextResponse.json({
      error: 'Failed to send test email',
      details: error.message
    }, { status: 500 });
  }
}
