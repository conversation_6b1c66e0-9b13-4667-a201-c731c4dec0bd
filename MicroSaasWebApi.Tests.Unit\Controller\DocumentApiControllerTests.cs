using FakeItEasy;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using MicroSaasWebApi.Controllers.PXW;
using MicroSaasWebApi.Models.PXW.AppSettings;
using MicroSaasWebApi.Properties.PXW;
using MicroSaasWebApi.Services.PXW.Interface;
using System.Net;

namespace MicroSaasWebApi.Tests.Unit.Controller
{
    public class DocumentApiControllerTests
    {
        private DocumentApiController _documentApiController;
        private readonly IServiceProvider _serviceProvider = A.Fake<IServiceProvider>();
        private readonly IBaseService _baseService = A.Fake<IBaseService>();
        private readonly ILoggerService _loggerService = A.Fake<ILoggerService>();
        private readonly IHttpContextAccessor _httpContextAccessor = A.Fake<IHttpContextAccessor>();

        private readonly IExternalApiService _externalApiService = A.Fake<IExternalApiService>();

        private readonly IServiceScopeFactory _serviceScopeFactory = A.Fake<IServiceScopeFactory>();
        private readonly IServiceScope _serviceScope = A.Fake<IServiceScope>();

        const string endpointName = "ListDocuments";
        const string fakeAuthHeader = "Bearer someValidToken";
        const string fakeUrl = "http://fakedataApiUrl.com";
        const string jwtToken = "eyJhbGciOiJSUzI1NiIsImtpZCI6Im1fZmN2QU9XNHZoWXdJTnRYYW0zbFNtamRJdkRQT004bER2aHNTNWdkY3MiLCJ0eXAiOiJKV1QifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YSBt7aLlhCdSbDkb8d5XzPlIgu1owNU--UKxI0mAKKr1RoWsQ52J2Na4knfDiAWzXegHMMl-CvyUIbg7o5Tyq2qPzBcVRA4hfpTuQVwxEofc2-3mJX0sjsu47dwwYKAMGGj8mkHFwZZ_gUr35wtWMbXI-6fii_V52HTbu7ukiQSRXVXl0zkeC9jUt7MFkhVvm6NRei-vNeVK1eX7i05kaT5PVq8IjovC2zlcX69QrzcxJEkPgtMO1qfbDJBVJPl1-YPcwolD6wStdS0rzYWJam1rM0jeR-vOTBXDXf46P7I_SvkKYKE8LBfiCY6Ns-MKlHa2WTRI6w0Z2p26Nkc81w";
        const string responseBody = "{ \"success\": true }";

        public DocumentApiControllerTests()
        {
            A.CallTo(() => _baseService.HttpContextAccessor).Returns(_httpContextAccessor);

            A.CallTo(() => _serviceProvider.GetService(typeof(IServiceScopeFactory))).Returns(_serviceScopeFactory);
            A.CallTo(() => _serviceScopeFactory.CreateScope()).Returns(_serviceScope);

            // Use GetRequiredService to match the actual calls in your constructor
            A.CallTo(() => _serviceScope.ServiceProvider.GetService(typeof(ILoggerService))).Returns(_loggerService);
            A.CallTo(() => _serviceScope.ServiceProvider.GetService(typeof(IExternalApiService))).Returns(_externalApiService);

            _documentApiController = new DocumentApiController(_baseService, _loggerService);
            _documentApiController.ControllerContext = new ControllerContext()
            {
                HttpContext = new DefaultHttpContext(),
            };
        }
        #region ShouldReturn200OK_WhenCalledSuccessfully
        [Fact, Trait("Category", "DocumentAPI")]
        public async Task DocumentList_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };

            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);
            
            // Mock External API call
            HttpResponseMessage mockResponse = new(HttpStatusCode.OK)
            {
                Content = new StringContent(responseBody, System.Text.Encoding.UTF8, "application/json")
            };
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _baseService.GetDocumentApiEndpointsWithB2CToken(endpointName, "GET")).Returns(Task.FromResult((IActionResult)new ObjectResult(responseBody) { StatusCode = 200 }));

            // Act
            var result = await _documentApiController.ListDocuments();
            var objectResult = result as ObjectResult;

            // Assert
            Assert.NotNull(objectResult);
            Assert.IsAssignableFrom<ObjectResult>(result);
            Assert.Equal(200, objectResult.StatusCode);
            Assert.NotNull(objectResult.Value);
            // Verify logging
            //A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();

        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task DocumentSearch_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.SearchDocuments();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }


        [Fact, Trait("Category", "DocumentAPI")]
        public async Task DocumentDownload_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetThirdPartyDataAsyncNoTransform(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.DownloadDocuments();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task DocumentUpload_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetThirdPartyAsyncFormData(A<string>._, A<string>._, A<string>._, A<HttpRequest>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.UploadDocuments();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task DocumentMove_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.MoveDocuments();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task DocumentDelete_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.DeleteDocuments();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task CreateRole_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.CreateRole();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task ListRoles_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.ListRoles();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task GetRole_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.GetRole();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task DeleteRole_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.DeleteRole();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task CreateFolder_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.CreateFolder();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task ActivateExternalUser_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.ActivateExternalUser();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task CreateExternalUser_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.CreateExternalUser();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task DeactivateExternalUser_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.DeactivateExternalUser();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task InviteExternalUser_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.InviteExternalUser();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task ListOrganizations_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.ListOrganizations();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task CreateUser_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.CreateUser();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task UpdateUser_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.UpdateUser();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task GetUser_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.GetUser();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task DeleteUser_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.DeleteUser();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task CreateGroup_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.CreateGroup();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task UpdateGroup_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.UpdateGroup();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task GetGroup_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.GetGroup();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task DeleteGroup_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.DeleteGroup();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task GetDocumentCategories_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.GetDocumentCategories();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task GetUserPermissions_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.GetUserPermissions();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task SearchUsers_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.SearchUsers();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task GetShare_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.GetShare();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task UpdateShare_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.UpdateShare();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task CreateShare_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.CreateShare();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task GetDocumentActivityFeed_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.GetDocumentActivityFeed();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task AddDocumentCategory_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.AddDocumentCategory();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task DeleteDocumentCategory_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.DeleteDocumentCategory();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task ListNotifications_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.ListNotifications();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task ListUnreadNotifications_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.ListUnreadNotifications();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task UserSubscriptionNotifications_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.UserSubscriptionNotifications();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task AddComment_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.AddComment();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task GetComment_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.GetComment();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task ListComments_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.ListComments();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task UpdateComment_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.UpdateComment();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task DeleteComment_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.DeleteComment();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task ListUsers_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.ListUsers();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task GetCommentUnreadCount_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.GetCommentUnreadCount();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        [Fact, Trait("Category", "DocumentAPI")]
        public async Task GetAllShares_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.GetAllShares();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }


        [Fact, Trait("Category", "DocumentAPI")]
        public async Task SetUserEmailNotification_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            var fakeEndpoints = A.Fake<Endpoints>();
            var fakeEndpointValue = new MicroSaasWebApi.Models.PXW.AppSettings.Endpoint { URL = "/someEndpoint" };
            var tenant = new Tenant
            {
                Settings = new Settings
                {
                    PortfolioDocumentApi = new PortfolioDocumentApi
                    {
                        Domain = fakeUrl
                    }
                }
            };
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["FilteredTenant"]).Returns(tenant);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Request.Headers[Constants.Authorization]).Returns(fakeAuthHeader);
            A.CallTo(() => _baseService.HttpContextAccessor.HttpContext.Items["Endpoints"]).Returns(fakeEndpoints);

            // Mock External API call
            HttpResponseMessage mockResponse = new(System.Net.HttpStatusCode.OK);
            mockResponse.Headers.Add("Set-Cookie", "FakeCookie");
            A.CallTo(() => _externalApiService.GetFullResponseThirdPartyDataAsync(A<string>._, A<string>._, A<string>._, A<Dictionary<string, string>>._, A<string>._, A<string>._))
                .Returns(mockResponse);

            // Act
            var result = await _documentApiController.SetUserEmailNotification();
            var okResult = Assert.IsType<ObjectResult>(result);
            // Assert
            Assert.NotNull(okResult);
            A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            Assert.Equal(200, okResult.StatusCode);
        }

        #endregion

    }

}
