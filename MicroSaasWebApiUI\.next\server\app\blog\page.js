(()=>{var e={};e.id=404,e.ids=[404],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},32694:e=>{"use strict";e.exports=require("http2")},35240:e=>{"use strict";e.exports=require("https")},85807:e=>{"use strict";e.exports=require("module")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},6005:e=>{"use strict";e.exports=require("node:crypto")},86870:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),r(84274),r(58840),r(8130),r(7629),r(12523);var s=r(23191),a=r(88716),l=r(37922),i=r.n(l),o=r(95231),n={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);r.d(t,n);let d=["",{children:["blog",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,84274)),"C:\\Projects\\PersonalPortal\\src\\app\\blog\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,58840)),"C:\\Projects\\PersonalPortal\\src\\app\\blog\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e),async e=>(await Promise.resolve().then(r.bind(r,71150))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,63601))).default(e)],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,51922))).default(e)],twitter:[async e=>(await Promise.resolve().then(r.bind(r,40680))).default(e)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,8130)),"C:\\Projects\\PersonalPortal\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,7629)),"C:\\Projects\\PersonalPortal\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Projects\\PersonalPortal\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e),async e=>(await Promise.resolve().then(r.bind(r,71150))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,63601))).default(e)],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,51922))).default(e)],twitter:[async e=>(await Promise.resolve().then(r.bind(r,40680))).default(e)],manifest:void 0}}],c=["C:\\Projects\\PersonalPortal\\src\\app\\blog\\page.tsx"],u="/blog/page",x={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/blog/page",pathname:"/blog",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},57305:(e,t,r)=>{Promise.resolve().then(r.bind(r,59771))},59771:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var s=r(10326),a=r(17577),l=r(23609),i=r(90434),o=r(26228);r(56594),r(54534);let n=({articles:e,selectedCategoryId:t})=>{let[r,n]=(0,a.useState)(8),[d,c]=(0,a.useState)([]);(0,a.useEffect)(()=>{null==t?c(e):c(e.filter(e=>e.categories?.includes(t))),n(8)},[e,t]),(0,a.useEffect)(()=>{let e=document.documentElement.classList.contains("dark")?"dark":"light";(0,o.J)(e)},[d]);let u=e=>{let t=document.createElement("DIV");return t.innerHTML=e,t.textContent||t.innerText||""};return(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:d?.slice(0,r).map((e,t)=>s.jsxs(i.default,{href:`/blog/${e.slug}`,className:"bg-white dark:bg-[#1E232C] rounded-xl overflow-hidden shadow-lg dark:border border-solid border-[#373C53] hover:border-[#5b6285] transition-all duration-500 transform hover:scale-105 ease-in-out h-full flex flex-col",children:[e.image_url&&s.jsx("div",{className:"w-full h-40 overflow-hidden",children:s.jsx("img",{src:e.image_url,alt:e.title.rendered,className:"w-full h-full object-cover",onError:e=>{let t=e.target;console.error("Image failed to load:",t.src),t.src.startsWith("https://")&&(t.src=t.src.replace("https://","http://"))}})}),s.jsxs("div",{className:"p-5 flex flex-col flex-grow",children:[s.jsx("h2",{className:"font-semibold text-lg dark:text-white mb-3",children:e.title.rendered}),e.excerpt&&s.jsxs("p",{className:"text-gray-600 dark:text-gray-300 mb-3 flex-grow",children:[u(e.excerpt.rendered).substring(0,100),"..."]}),s.jsxs("div",{className:"flex justify-between items-center mt-auto",children:[s.jsx("p",{className:"text-gray-400 text-sm",children:l.a(e.date)}),s.jsx("span",{className:"text-blue-500 text-sm",children:"Read more →"})]})]})]},t))}),r<d.length&&s.jsx("div",{className:"flex justify-start",children:s.jsx("button",{className:"bg-[#006fee] rounded-full text-white font-medium text-sm px-5 py-1.5 h-8 mt-8",onClick:()=>{n(e=>e+8)},children:"See more"})})]})},d=({categories:e,onSelectCategory:t,selectedCategoryId:r})=>{let[l,i]=(0,a.useState)(new Set),{rootCategories:o,childCategories:n}=(()=>{let t=[],r={};return e.forEach(e=>{0===e.parent?t.push(e):(r[e.parent]||(r[e.parent]=[]),r[e.parent].push(e))}),t.sort((e,t)=>e.name.localeCompare(t.name)),Object.keys(r).forEach(e=>{r[Number(e)].sort((e,t)=>e.name.localeCompare(t.name))}),{rootCategories:t,childCategories:r}})(),d=e=>{let t=new Set(l);t.has(e)?t.delete(e):t.add(e),i(t)},c=(e,a=0)=>{let i=n[e.id]&&n[e.id].length>0,o=l.has(e.id),u=r===e.id;return(0,s.jsxs)("div",{className:"category-item",children:[(0,s.jsxs)("div",{className:`flex items-center py-1 px-2 rounded cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 ${u?"bg-blue-100 dark:bg-blue-900":""}`,style:{paddingLeft:`${12*a+8}px`},children:[i&&s.jsx("button",{onClick:()=>d(e.id),className:"mr-1 w-4 h-4 flex items-center justify-center text-gray-500",children:o?"−":"+"}),!i&&s.jsx("span",{className:"mr-1 w-4 h-4"}),(0,s.jsxs)("span",{onClick:()=>t(e.id),className:"flex-grow truncate",children:[e.name," ",(0,s.jsxs)("span",{className:"text-gray-500 text-xs",children:["(",e.count,")"]})]})]}),i&&o&&s.jsx("div",{className:"ml-2",children:n[e.id].map(e=>c(e,a+1))})]},e.id)};return(0,s.jsxs)("div",{className:"category-tree bg-white dark:bg-[#1E232C] p-4 rounded-xl shadow-lg dark:border border-solid border-[#373C53] mb-6",children:[s.jsx("h3",{className:"text-lg font-semibold mb-3 dark:text-white",children:"Categories"}),(0,s.jsxs)("div",{className:`flex items-center py-1 px-2 rounded cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 mb-2 ${null===r?"bg-blue-100 dark:bg-blue-900":""}`,onClick:()=>t(null),children:[s.jsx("span",{className:"mr-1 w-4 h-4"}),s.jsx("span",{className:"flex-grow",children:"All Posts"})]}),o.map(e=>c(e))]})};var c=r(4721);let u=({articles:e,categories:t,showAds:r=!1})=>{let[l,i]=(0,a.useState)(null);return(0,s.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row gap-8",children:[s.jsx("div",{className:"md:w-1/4 md:min-w-[250px] md:max-w-[300px]",children:(0,s.jsxs)("div",{className:"md:sticky md:top-4",children:[s.jsx(d,{categories:t,onSelectCategory:i,selectedCategoryId:l}),r&&s.jsx("div",{className:"hidden md:block mt-6 w-full max-w-[300px]",children:s.jsx(c.default,{type:"multiplex-vertical",className:"sidebar-ad"})})]})}),(0,s.jsxs)("div",{className:"md:flex-1",children:[s.jsx(n,{articles:e,selectedCategoryId:l}),r&&s.jsx("div",{className:"mt-8",children:s.jsx(c.default,{type:"in-feed"})})]})]}),r&&s.jsx("div",{className:"w-full mt-8",children:s.jsx(c.default,{type:"display-horizontal"})})]})}},84274:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>n});var s=r(19510),a=r(1542),l=r(7264),i=r(57939);let o=(0,r(68570).createProxy)(String.raw`C:\Projects\PersonalPortal\src\app\blog\_assets\components\BlogContainer.tsx#default`);r(46246),r(63948);let n=(0,l.A)({title:`${a.Z.appName} Blog | Stripe Chargeback Protection`,description:"Learn how to prevent chargebacks, how to accept payments online, and keep your Stripe account in good standing",canonicalUrlRelative:"/blog/?populate[author][populate]=*&populate=blog_image"});async function d(){let e=await i.G.getAllPosts(),t=await i.G.getAllCategories();return s.jsx(s.Fragment,{children:s.jsx("div",{className:"mt-[40px] mb-[20px]",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 md:px-8 lg:px-12",children:[(0,s.jsxs)("div",{className:"text-center mb-4 max-w-3xl mx-auto",children:[s.jsx("h1",{className:"text-4xl font-bold mb-3",children:"All Articles"}),s.jsx("p",{className:"text-lg leading-relaxed dark:text-gray-200 text-gray-700 mb-2",children:"Explore insights on software development, cloud architecture, and tech innovations. Stay updated with my latest projects and technical discoveries."})]}),s.jsx(o,{articles:e,categories:t,showAds:!0})]})})})}},46246:()=>{},56594:()=>{},63948:()=>{},54534:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[948,70,251,684,762,746,944],()=>r(86870));module.exports=s})();