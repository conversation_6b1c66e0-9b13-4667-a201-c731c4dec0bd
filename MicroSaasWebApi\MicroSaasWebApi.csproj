﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net7.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<UserSecretsId>aspnet-MicroSaasWebApi-ba0483c2-66a7-482d-abad-f27be7006d0c</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<DockerfileContext>.</DockerfileContext>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<NoWarn>$(NoWarn);1591</NoWarn>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="Data\**" />
		<Compile Remove="Functions\**" />
		<Compile Remove="Models\PHW\**" />
		<Compile Remove="Models\Sharepoint\**" />
		<Compile Remove="Properties\PHW\**" />
		<Compile Remove="Services\DataAPI\**" />
		<Compile Remove="Services\PHW\**" />
		<Compile Remove="Tenants\**" />
		<Content Remove="Data\**" />
		<Content Remove="Functions\**" />
		<Content Remove="Models\PHW\**" />
		<Content Remove="Models\Sharepoint\**" />
		<Content Remove="Properties\PHW\**" />
		<Content Remove="Services\DataAPI\**" />
		<Content Remove="Services\PHW\**" />
		<Content Remove="Tenants\**" />
		<EmbeddedResource Remove="Data\**" />
		<EmbeddedResource Remove="Functions\**" />
		<EmbeddedResource Remove="Models\PHW\**" />
		<EmbeddedResource Remove="Models\Sharepoint\**" />
		<EmbeddedResource Remove="Properties\PHW\**" />
		<EmbeddedResource Remove="Services\DataAPI\**" />
		<EmbeddedResource Remove="Services\PHW\**" />
		<EmbeddedResource Remove="Tenants\**" />
		<None Remove="Data\**" />
		<None Remove="Functions\**" />
		<None Remove="Models\PHW\**" />
		<None Remove="Models\Sharepoint\**" />
		<None Remove="Properties\PHW\**" />
		<None Remove="Services\DataAPI\**" />
		<None Remove="Services\PHW\**" />
		<None Remove="Tenants\**" />
		<_WebToolingArtifacts Remove="Properties\PHW\**" />
	</ItemGroup>

	<ItemGroup>
		<Compile Remove="Controllers\PXW\AccountController.cs" />
		<Compile Remove="Controllers\PXW\AlphaCCOController.cs" />
		<Compile Remove="Controllers\PXW\TokenController.cs" />
		<Compile Remove="Extensions\IdentityExtension.cs" />
		<Compile Remove="Middlewares\TenantIdMiddleware.cs" />
		<Compile Remove="Models\AlphaCCO\AlphaCCOQuestions.cs" />
		<Compile Remove="Models\Documents\PDFHelper.cs" />
		<Compile Remove="Models\PXW\ApplicationUser.cs" />
		<Compile Remove="Models\PXW\Tenant.cs" />
		<Compile Remove="Models\PXW\TenantConfiguration.cs" />
		<Compile Remove="Program - Copy.cs" />
		<Compile Remove="Program-diff-swagger.cs" />
		<Compile Remove="Program-old.cs" />
		<Compile Remove="Services\Auth\PXW\AppSettingsService.cs" />
		<Compile Remove="Services\Auth\PXW\IAppSettingsService.cs" />
		<Compile Remove="Services\PXW\BusinessEngine.cs" />
		<Compile Remove="Services\PXW\ControllerActionInvoker.cs" />
		<Compile Remove="Services\PXW\Interface\IControllerActionInvoker.cs" />
		<Compile Remove="Services\Sharepoint\PHW\GetFiles.cs" />
		<Compile Remove="Startup.cs" />
	</ItemGroup>

	<ItemGroup>
	  <Content Remove="Assets\PAL\AppSettings\appsettings_PAL_UAT.json" />
	</ItemGroup>

	<ItemGroup>
		<Content Include=".config\dotnet-tools.json" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AspNetCore.HealthChecks.UI" Version="7.0.2" />
		<PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="7.1.0" />
		<PackageReference Include="AspNetCore.HealthChecks.UI.Core" Version="7.0.0" />
		<PackageReference Include="AspNetCore.HealthChecks.UI.InMemory.Storage" Version="7.0.0" />
		<PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.1" />
		<PackageReference Include="Azure.Identity" Version="1.12.0" />
		<PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.6.0" />
		<PackageReference Include="Markdig" Version="0.34.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.AzureADB2C.UI" Version="6.0.26" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="7.0.11" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="7.0.11" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.1.0" />
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="7.0.11" />
		<PackageReference Include="Microsoft.Azure.AppConfiguration.AspNetCore" Version="7.1.0" />
		<PackageReference Include="Microsoft.Azure.KeyVault" Version="3.0.5" />
		<PackageReference Include="Microsoft.Extensions.Configuration.AzureAppConfiguration" Version="7.1.0" />
		<PackageReference Include="Microsoft.Identity.Web" Version="2.16.1" />
		<PackageReference Include="Microsoft.Identity.Web.UI" Version="2.16.1" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.19.6" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="NWebsec.AspNetCore.Middleware" Version="3.0.0" />
		<PackageReference Include="RestSharp" Version="112.0.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.5.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="8.0.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.Filters.Abstractions" Version="8.0.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.Newtonsoft" Version="6.5.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.ReDoc" Version="6.5.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="6.5.0" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="6.5.0" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="6.5.0" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Assets\PXW\Postman\" />
		<Folder Include="Services\Sharepoint\PHW\" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\SharePointAPI\SharePointAPI.csproj" />
	</ItemGroup>
	<ItemGroup>
		<AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
			<_Parameter1>MicroSaasWebApi.Tests.Unit</_Parameter1>
		</AssemblyAttribute>
	</ItemGroup>
	<ItemGroup>
	  <None Include="Assets\PAL\AppSettings\appsettings_PAL_UAT.json" />
	</ItemGroup>

	<ItemGroup>
		<Content Update="appsettings.phw-react.Development.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Update="appsettings.phw-react.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
	</ItemGroup>

</Project>
