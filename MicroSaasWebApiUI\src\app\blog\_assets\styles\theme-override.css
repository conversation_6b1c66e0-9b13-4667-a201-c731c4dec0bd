/* Theme override styles */

/* Light theme styles */
.light-theme,
html.light .blog-content {
  /* Add copy button styles for light theme */
  --copy-button-bg: #1364FF;
  --copy-button-hover-bg: #0056e0;
  --copy-button-text: white;
  background-color: white !important;
  color: #2d3748 !important; /* text-gray-800 */
}

.light-theme h1,
.light-theme h2,
.light-theme h3,
.light-theme h4,
.light-theme h5,
.light-theme h6,
html.light .blog-content h1,
html.light .blog-content h2,
html.light .blog-content h3,
html.light .blog-content h4,
html.light .blog-content h5,
html.light .blog-content h6 {
  color: #1a202c !important; /* text-gray-900 */
}

.light-theme p,
.light-theme li,
.light-theme td,
.light-theme th,
html.light .blog-content p,
html.light .blog-content li,
html.light .blog-content td,
html.light .blog-content th {
  color: #2d3748 !important; /* text-gray-800 */
}

.light-theme pre,
html.light .blog-content pre {
  background-color: #f7fafc !important; /* bg-gray-50 */
  color: #2d3748 !important; /* text-gray-800 */
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.light-theme code,
html.light .blog-content code {
  background-color: transparent !important; /* Removed bg-gray-50 */
  color: #2d3748 !important; /* text-gray-800 */
  padding: 0 !important;
  margin: 0 0.1rem !important;
  border: none !important;
  display: inline !important;
  line-height: inherit !important;
  vertical-align: baseline !important;
}

.light-theme .copy-code-button,
html.light .blog-content .copy-code-button {
  background-color: var(--copy-button-bg) !important;
  color: var(--copy-button-text) !important;
  position: absolute !important;
  top: 0.5rem !important;
  right: 0.5rem !important;
  z-index: 10 !important;
  border-radius: 4px !important;
  padding: 0.25rem 0.5rem !important;
  font-size: 0.75rem !important;
  cursor: pointer !important;
}

.light-theme .copy-code-button:hover,
html.light .blog-content .copy-code-button:hover {
  background-color: var(--copy-button-hover-bg) !important;
  transform: scale(1.05) !important;
}

.light-theme blockquote,
html.light .blog-content blockquote {
  color: #4a5568 !important; /* text-gray-600 */
  border-left-color: #e2e8f0 !important; /* border-gray-300 */
}

.light-theme a,
html.light .blog-content a {
  color: #3182ce !important; /* text-blue-500 */
}

.light-theme table th,
html.light .blog-content table th {
  background-color: #f7fafc !important; /* bg-gray-50 */
  border-color: #e2e8f0 !important; /* border-gray-200 */
}

.light-theme table td,
html.light .blog-content table td {
  border-color: #e2e8f0 !important; /* border-gray-200 */
}

/* Dark theme styles */
.dark-theme,
html.dark .blog-content {
  /* Add copy button styles for dark theme */
  --copy-button-bg: #1364FF;
  --copy-button-hover-bg: #0056e0;
  --copy-button-text: white;
  background-color: #1E232C !important;
  color: white !important;
  border-color: #373C53 !important;
}

.dark-theme h1,
.dark-theme h2,
.dark-theme h3,
.dark-theme h4,
.dark-theme h5,
.dark-theme h6,
html.dark .blog-content h1,
html.dark .blog-content h2,
html.dark .blog-content h3,
html.dark .blog-content h4,
html.dark .blog-content h5,
html.dark .blog-content h6 {
  color: white !important;
}

.dark-theme pre,
html.dark .blog-content pre {
  background-color: transparent !important;
  color: #e2e8f0 !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.dark-theme code,
html.dark .blog-content code {
  background-color: transparent !important;
  color: #e2e8f0 !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
  display: inline !important;
  line-height: inherit !important;
  vertical-align: baseline !important;
}

.dark-theme .copy-code-button,
html.dark .blog-content .copy-code-button {
  background-color: var(--copy-button-bg) !important;
  color: var(--copy-button-text) !important;
  position: absolute !important;
  top: 0.5rem !important;
  right: 0.5rem !important;
  z-index: 10 !important;
  border-radius: 4px !important;
  padding: 0.25rem 0.5rem !important;
  font-size: 0.75rem !important;
  cursor: pointer !important;
}

.dark-theme .copy-code-button:hover,
html.dark .blog-content .copy-code-button:hover {
  background-color: var(--copy-button-hover-bg) !important;
  transform: scale(1.05) !important;
}

/* Light theme overrides */
@media (prefers-color-scheme: light) {
    pre {
        background: #f8f9fa;
        border-color: #e9ecef;
    }

    pre code {
        color: #1a1a1a;
    }

    .copy-code-button svg {
        color: #1a1a1a;
    }
}