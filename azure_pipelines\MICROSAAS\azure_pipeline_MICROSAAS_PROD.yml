extends:
  template: ../../azure_pipeline_template.yml
  parameters:
    environment: PROD
    client: MICROSAAS
    dockerRegistryServiceConnection: "************************************"
    imageRepository: "microsaas-microsaas-prod"
    containerRegistry: "prodmicroSaasacr.azurecr.io"
    dockerfilePath: "$(Build.SourcesDirectory)/Dockerfile"
    tag: "MICROSAAS-PROD-$(Build.BuildId)"
    poolName: "terraform-pool"
    azureSubscription: "MICROSAAS-PROD(f88e9b38-87cc-476a-b1df-dccd1cfb968b)"
    webAppName: "microsaas-microsaas-prod"
    appSettingsEnvironment: "-ASPNETCORE_ENVIRONMENT Staging"
