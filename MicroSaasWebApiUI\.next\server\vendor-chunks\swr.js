"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/swr";
exports.ids = ["vendor-chunks/swr"];
exports.modules = {

/***/ "(ssr)/./node_modules/swr/dist/_internal/index.mjs":
/*!***************************************************!*\
  !*** ./node_modules/swr/dist/_internal/index.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INFINITE_PREFIX: () => (/* binding */ INFINITE_PREFIX),\n/* harmony export */   IS_REACT_LEGACY: () => (/* binding */ IS_REACT_LEGACY),\n/* harmony export */   IS_SERVER: () => (/* binding */ IS_SERVER),\n/* harmony export */   OBJECT: () => (/* binding */ OBJECT),\n/* harmony export */   SWRConfig: () => (/* binding */ SWRConfig),\n/* harmony export */   SWRGlobalState: () => (/* binding */ SWRGlobalState),\n/* harmony export */   UNDEFINED: () => (/* binding */ UNDEFINED),\n/* harmony export */   cache: () => (/* binding */ cache),\n/* harmony export */   compare: () => (/* binding */ compare),\n/* harmony export */   createCacheHelper: () => (/* binding */ createCacheHelper),\n/* harmony export */   defaultConfig: () => (/* binding */ defaultConfig),\n/* harmony export */   defaultConfigOptions: () => (/* binding */ defaultConfigOptions),\n/* harmony export */   getTimestamp: () => (/* binding */ getTimestamp),\n/* harmony export */   hasRequestAnimationFrame: () => (/* binding */ hasRequestAnimationFrame),\n/* harmony export */   initCache: () => (/* binding */ initCache),\n/* harmony export */   internalMutate: () => (/* binding */ internalMutate),\n/* harmony export */   isDocumentDefined: () => (/* binding */ isDocumentDefined),\n/* harmony export */   isFunction: () => (/* binding */ isFunction),\n/* harmony export */   isPromiseLike: () => (/* binding */ isPromiseLike),\n/* harmony export */   isUndefined: () => (/* binding */ isUndefined),\n/* harmony export */   isWindowDefined: () => (/* binding */ isWindowDefined),\n/* harmony export */   mergeConfigs: () => (/* binding */ mergeConfigs),\n/* harmony export */   mergeObjects: () => (/* binding */ mergeObjects),\n/* harmony export */   mutate: () => (/* binding */ mutate),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   normalize: () => (/* binding */ normalize),\n/* harmony export */   preload: () => (/* binding */ preload),\n/* harmony export */   preset: () => (/* binding */ preset),\n/* harmony export */   rAF: () => (/* binding */ rAF),\n/* harmony export */   revalidateEvents: () => (/* binding */ events),\n/* harmony export */   serialize: () => (/* binding */ serialize),\n/* harmony export */   slowConnection: () => (/* binding */ slowConnection),\n/* harmony export */   stableHash: () => (/* binding */ stableHash),\n/* harmony export */   subscribeCallback: () => (/* binding */ subscribeCallback),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   useSWRConfig: () => (/* binding */ useSWRConfig),\n/* harmony export */   withArgs: () => (/* binding */ withArgs),\n/* harmony export */   withMiddleware: () => (/* binding */ withMiddleware)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\nconst mergeObjects = (a, b)=>({\n        ...a,\n        ...b\n    });\nconst isPromiseLike = (x)=>isFunction(x.then);\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const constructor = arg && arg.constructor;\n    const isDate = constructor == Date;\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && constructor != RegExp) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (constructor == Array) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (constructor == OBJECT) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\n\n// Global state used to deduplicate requests and store listeners\nconst SWRGlobalState = new WeakMap();\n\nconst EMPTY_CACHE = {};\nconst INITIAL_CACHE = {};\nconst STR_UNDEFINED = 'undefined';\n// NOTE: Use the function to guarantee it's re-evaluated between jsdom and node runtime for tests.\nconst isWindowDefined = typeof window != STR_UNDEFINED;\nconst isDocumentDefined = typeof document != STR_UNDEFINED;\nconst hasRequestAnimationFrame = ()=>isWindowDefined && typeof window['requestAnimationFrame'] != STR_UNDEFINED;\nconst createCacheHelper = (cache, key)=>{\n    const state = SWRGlobalState.get(cache);\n    return [\n        // Getter\n        ()=>!isUndefined(key) && cache.get(key) || EMPTY_CACHE,\n        // Setter\n        (info)=>{\n            if (!isUndefined(key)) {\n                const prev = cache.get(key);\n                // Before writing to the store, we keep the value in the initial cache\n                // if it's not there yet.\n                if (!(key in INITIAL_CACHE)) {\n                    INITIAL_CACHE[key] = prev;\n                }\n                state[5](key, mergeObjects(prev, info), prev || EMPTY_CACHE);\n            }\n        },\n        // Subscriber\n        state[6],\n        // Get server cache snapshot\n        ()=>{\n            if (!isUndefined(key)) {\n                // If the cache was updated on the client, we return the stored initial value.\n                if (key in INITIAL_CACHE) return INITIAL_CACHE[key];\n            }\n            // If we haven't done any client-side updates, we return the current value.\n            return !isUndefined(key) && cache.get(key) || EMPTY_CACHE;\n        }\n    ];\n} // export { UNDEFINED, OBJECT, isUndefined, isFunction, mergeObjects, isPromiseLike }\n;\n\n/**\n * Due to the bug https://bugs.chromium.org/p/chromium/issues/detail?id=678075,\n * it's not reliable to detect if the browser is currently online or offline\n * based on `navigator.onLine`.\n * As a workaround, we always assume it's online on the first load, and change\n * the status upon `online` or `offline` events.\n */ let online = true;\nconst isOnline = ()=>online;\n// For node and React Native, `add/removeEventListener` doesn't exist on window.\nconst [onWindowEvent, offWindowEvent] = isWindowDefined && window.addEventListener ? [\n    window.addEventListener.bind(window),\n    window.removeEventListener.bind(window)\n] : [\n    noop,\n    noop\n];\nconst isVisible = ()=>{\n    const visibilityState = isDocumentDefined && document.visibilityState;\n    return isUndefined(visibilityState) || visibilityState !== 'hidden';\n};\nconst initFocus = (callback)=>{\n    // focus revalidate\n    if (isDocumentDefined) {\n        document.addEventListener('visibilitychange', callback);\n    }\n    onWindowEvent('focus', callback);\n    return ()=>{\n        if (isDocumentDefined) {\n            document.removeEventListener('visibilitychange', callback);\n        }\n        offWindowEvent('focus', callback);\n    };\n};\nconst initReconnect = (callback)=>{\n    // revalidate on reconnected\n    const onOnline = ()=>{\n        online = true;\n        callback();\n    };\n    // nothing to revalidate, just update the status\n    const onOffline = ()=>{\n        online = false;\n    };\n    onWindowEvent('online', onOnline);\n    onWindowEvent('offline', onOffline);\n    return ()=>{\n        offWindowEvent('online', onOnline);\n        offWindowEvent('offline', onOffline);\n    };\n};\nconst preset = {\n    isOnline,\n    isVisible\n};\nconst defaultConfigOptions = {\n    initFocus,\n    initReconnect\n};\n\nconst IS_REACT_LEGACY = !react__WEBPACK_IMPORTED_MODULE_0__.useId;\nconst IS_SERVER = !isWindowDefined || 'Deno' in window;\n// Polyfill requestAnimationFrame\nconst rAF = (f)=>hasRequestAnimationFrame() ? window['requestAnimationFrame'](f) : setTimeout(f, 1);\n// React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\nconst useIsomorphicLayoutEffect = IS_SERVER ? react__WEBPACK_IMPORTED_MODULE_0__.useEffect : react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect;\n// This assignment is to extend the Navigator type to use effectiveType.\nconst navigatorConnection = typeof navigator !== 'undefined' && navigator.connection;\n// Adjust the config based on slow connection status (<= 70Kbps).\nconst slowConnection = !IS_SERVER && navigatorConnection && ([\n    'slow-2g',\n    '2g'\n].includes(navigatorConnection.effectiveType) || navigatorConnection.saveData);\n\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n\n// Global timestamp.\nlet __timestamp = 0;\nconst getTimestamp = ()=>++__timestamp;\n\nconst FOCUS_EVENT = 0;\nconst RECONNECT_EVENT = 1;\nconst MUTATE_EVENT = 2;\nconst ERROR_REVALIDATE_EVENT = 3;\n\nvar events = {\n  __proto__: null,\n  ERROR_REVALIDATE_EVENT: ERROR_REVALIDATE_EVENT,\n  FOCUS_EVENT: FOCUS_EVENT,\n  MUTATE_EVENT: MUTATE_EVENT,\n  RECONNECT_EVENT: RECONNECT_EVENT\n};\n\nasync function internalMutate(...args) {\n    const [cache, _key, _data, _opts] = args;\n    // When passing as a boolean, it's explicitly used to disable/enable\n    // revalidation.\n    const options = mergeObjects({\n        populateCache: true,\n        throwOnError: true\n    }, typeof _opts === 'boolean' ? {\n        revalidate: _opts\n    } : _opts || {});\n    let populateCache = options.populateCache;\n    const rollbackOnErrorOption = options.rollbackOnError;\n    let optimisticData = options.optimisticData;\n    const rollbackOnError = (error)=>{\n        return typeof rollbackOnErrorOption === 'function' ? rollbackOnErrorOption(error) : rollbackOnErrorOption !== false;\n    };\n    const throwOnError = options.throwOnError;\n    // If the second argument is a key filter, return the mutation results for all\n    // filtered keys.\n    if (isFunction(_key)) {\n        const keyFilter = _key;\n        const matchedKeys = [];\n        const it = cache.keys();\n        for (const key of it){\n            if (// Skip the special useSWRInfinite and useSWRSubscription keys.\n            !/^\\$(inf|sub)\\$/.test(key) && keyFilter(cache.get(key)._k)) {\n                matchedKeys.push(key);\n            }\n        }\n        return Promise.all(matchedKeys.map(mutateByKey));\n    }\n    return mutateByKey(_key);\n    async function mutateByKey(_k) {\n        // Serialize key\n        const [key] = serialize(_k);\n        if (!key) return;\n        const [get, set] = createCacheHelper(cache, key);\n        const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n        const startRevalidate = ()=>{\n            const revalidators = EVENT_REVALIDATORS[key];\n            const revalidate = isFunction(options.revalidate) ? options.revalidate(get().data, _k) : options.revalidate !== false;\n            if (revalidate) {\n                // Invalidate the key by deleting the concurrent request markers so new\n                // requests will not be deduped.\n                delete FETCH[key];\n                delete PRELOAD[key];\n                if (revalidators && revalidators[0]) {\n                    return revalidators[0](MUTATE_EVENT).then(()=>get().data);\n                }\n            }\n            return get().data;\n        };\n        // If there is no new data provided, revalidate the key with current state.\n        if (args.length < 3) {\n            // Revalidate and broadcast state.\n            return startRevalidate();\n        }\n        let data = _data;\n        let error;\n        // Update global timestamps.\n        const beforeMutationTs = getTimestamp();\n        MUTATION[key] = [\n            beforeMutationTs,\n            0\n        ];\n        const hasOptimisticData = !isUndefined(optimisticData);\n        const state = get();\n        // `displayedData` is the current value on screen. It could be the optimistic value\n        // that is going to be overridden by a `committedData`, or get reverted back.\n        // `committedData` is the validated value that comes from a fetch or mutation.\n        const displayedData = state.data;\n        const currentData = state._c;\n        const committedData = isUndefined(currentData) ? displayedData : currentData;\n        // Do optimistic data update.\n        if (hasOptimisticData) {\n            optimisticData = isFunction(optimisticData) ? optimisticData(committedData, displayedData) : optimisticData;\n            // When we set optimistic data, backup the current committedData data in `_c`.\n            set({\n                data: optimisticData,\n                _c: committedData\n            });\n        }\n        if (isFunction(data)) {\n            // `data` is a function, call it passing current cache value.\n            try {\n                data = data(committedData);\n            } catch (err) {\n                // If it throws an error synchronously, we shouldn't update the cache.\n                error = err;\n            }\n        }\n        // `data` is a promise/thenable, resolve the final data first.\n        if (data && isPromiseLike(data)) {\n            // This means that the mutation is async, we need to check timestamps to\n            // avoid race conditions.\n            data = await data.catch((err)=>{\n                error = err;\n            });\n            // Check if other mutations have occurred since we've started this mutation.\n            // If there's a race we don't update cache or broadcast the change,\n            // just return the data.\n            if (beforeMutationTs !== MUTATION[key][0]) {\n                if (error) throw error;\n                return data;\n            } else if (error && hasOptimisticData && rollbackOnError(error)) {\n                // Rollback. Always populate the cache in this case but without\n                // transforming the data.\n                populateCache = true;\n                // Reset data to be the latest committed data, and clear the `_c` value.\n                set({\n                    data: committedData,\n                    _c: UNDEFINED\n                });\n            }\n        }\n        // If we should write back the cache after request.\n        if (populateCache) {\n            if (!error) {\n                // Transform the result into data.\n                if (isFunction(populateCache)) {\n                    const populateCachedData = populateCache(data, committedData);\n                    set({\n                        data: populateCachedData,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                } else {\n                    // Only update cached data and reset the error if there's no error. Data can be `undefined` here.\n                    set({\n                        data,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                }\n            }\n        }\n        // Reset the timestamp to mark the mutation has ended.\n        MUTATION[key][1] = getTimestamp();\n        // Update existing SWR Hooks' internal states:\n        Promise.resolve(startRevalidate()).then(()=>{\n            // The mutation and revalidation are ended, we can clear it since the data is\n            // not an optimistic value anymore.\n            set({\n                _c: UNDEFINED\n            });\n        });\n        // Throw error or return data\n        if (error) {\n            if (throwOnError) throw error;\n            return;\n        }\n        return data;\n    }\n}\n\nconst revalidateAllKeys = (revalidators, type)=>{\n    for(const key in revalidators){\n        if (revalidators[key][0]) revalidators[key][0](type);\n    }\n};\nconst initCache = (provider, options)=>{\n    // The global state for a specific provider will be used to deduplicate\n    // requests and store listeners. As well as a mutate function that is bound to\n    // the cache.\n    // The provider's global state might be already initialized. Let's try to get the\n    // global state associated with the provider first.\n    if (!SWRGlobalState.has(provider)) {\n        const opts = mergeObjects(defaultConfigOptions, options);\n        // If there's no global state bound to the provider, create a new one with the\n        // new mutate function.\n        const EVENT_REVALIDATORS = {};\n        const mutate = internalMutate.bind(UNDEFINED, provider);\n        let unmount = noop;\n        const subscriptions = {};\n        const subscribe = (key, callback)=>{\n            const subs = subscriptions[key] || [];\n            subscriptions[key] = subs;\n            subs.push(callback);\n            return ()=>subs.splice(subs.indexOf(callback), 1);\n        };\n        const setter = (key, value, prev)=>{\n            provider.set(key, value);\n            const subs = subscriptions[key];\n            if (subs) {\n                for (const fn of subs){\n                    fn(value, prev);\n                }\n            }\n        };\n        const initProvider = ()=>{\n            if (!SWRGlobalState.has(provider)) {\n                // Update the state if it's new, or if the provider has been extended.\n                SWRGlobalState.set(provider, [\n                    EVENT_REVALIDATORS,\n                    {},\n                    {},\n                    {},\n                    mutate,\n                    setter,\n                    subscribe\n                ]);\n                if (!IS_SERVER) {\n                    // When listening to the native events for auto revalidations,\n                    // we intentionally put a delay (setTimeout) here to make sure they are\n                    // fired after immediate JavaScript executions, which can be\n                    // React's state updates.\n                    // This avoids some unnecessary revalidations such as\n                    // https://github.com/vercel/swr/issues/1680.\n                    const releaseFocus = opts.initFocus(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, FOCUS_EVENT)));\n                    const releaseReconnect = opts.initReconnect(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, RECONNECT_EVENT)));\n                    unmount = ()=>{\n                        releaseFocus && releaseFocus();\n                        releaseReconnect && releaseReconnect();\n                        // When un-mounting, we need to remove the cache provider from the state\n                        // storage too because it's a side-effect. Otherwise, when re-mounting we\n                        // will not re-register those event listeners.\n                        SWRGlobalState.delete(provider);\n                    };\n                }\n            }\n        };\n        initProvider();\n        // This is a new provider, we need to initialize it and setup DOM events\n        // listeners for `focus` and `reconnect` actions.\n        // We might want to inject an extra layer on top of `provider` in the future,\n        // such as key serialization, auto GC, etc.\n        // For now, it's just a `Map` interface without any modifications.\n        return [\n            provider,\n            mutate,\n            initProvider,\n            unmount\n        ];\n    }\n    return [\n        provider,\n        SWRGlobalState.get(provider)[4]\n    ];\n};\n\n// error retry\nconst onErrorRetry = (_, __, config, revalidate, opts)=>{\n    const maxRetryCount = config.errorRetryCount;\n    const currentRetryCount = opts.retryCount;\n    // Exponential backoff\n    const timeout = ~~((Math.random() + 0.5) * (1 << (currentRetryCount < 8 ? currentRetryCount : 8))) * config.errorRetryInterval;\n    if (!isUndefined(maxRetryCount) && currentRetryCount > maxRetryCount) {\n        return;\n    }\n    setTimeout(revalidate, timeout, opts);\n};\nconst compare = (currentData, newData)=>stableHash(currentData) == stableHash(newData);\n// Default cache provider\nconst [cache, mutate] = initCache(new Map());\n// Default config\nconst defaultConfig = mergeObjects({\n    // events\n    onLoadingSlow: noop,\n    onSuccess: noop,\n    onError: noop,\n    onErrorRetry,\n    onDiscarded: noop,\n    // switches\n    revalidateOnFocus: true,\n    revalidateOnReconnect: true,\n    revalidateIfStale: true,\n    shouldRetryOnError: true,\n    // timeouts\n    errorRetryInterval: slowConnection ? 10000 : 5000,\n    focusThrottleInterval: 5 * 1000,\n    dedupingInterval: 2 * 1000,\n    loadingTimeout: slowConnection ? 5000 : 3000,\n    // providers\n    compare,\n    isPaused: ()=>false,\n    cache,\n    mutate,\n    fallback: {}\n}, // use web preset by default\npreset);\n\nconst mergeConfigs = (a, b)=>{\n    // Need to create a new object to avoid mutating the original here.\n    const v = mergeObjects(a, b);\n    // If two configs are provided, merge their `use` and `fallback` options.\n    if (b) {\n        const { use: u1, fallback: f1 } = a;\n        const { use: u2, fallback: f2 } = b;\n        if (u1 && u2) {\n            v.use = u1.concat(u2);\n        }\n        if (f1 && f2) {\n            v.fallback = mergeObjects(f1, f2);\n        }\n    }\n    return v;\n};\n\nconst SWRConfigContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({});\nconst SWRConfig = (props)=>{\n    const { value } = props;\n    const parentConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(SWRConfigContext);\n    const isFunctionalConfig = isFunction(value);\n    const config = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>isFunctionalConfig ? value(parentConfig) : value, [\n        isFunctionalConfig,\n        parentConfig,\n        value\n    ]);\n    // Extend parent context values and middleware.\n    const extendedConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>isFunctionalConfig ? config : mergeConfigs(parentConfig, config), [\n        isFunctionalConfig,\n        parentConfig,\n        config\n    ]);\n    // Should not use the inherited provider.\n    const provider = config && config.provider;\n    // initialize the cache only on first access.\n    const cacheContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(UNDEFINED);\n    if (provider && !cacheContextRef.current) {\n        cacheContextRef.current = initCache(provider(extendedConfig.cache || cache), config);\n    }\n    const cacheContext = cacheContextRef.current;\n    // Override the cache if a new provider is given.\n    if (cacheContext) {\n        extendedConfig.cache = cacheContext[0];\n        extendedConfig.mutate = cacheContext[1];\n    }\n    // Unsubscribe events.\n    useIsomorphicLayoutEffect(()=>{\n        if (cacheContext) {\n            cacheContext[2] && cacheContext[2]();\n            return cacheContext[3];\n        }\n    }, []);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(SWRConfigContext.Provider, mergeObjects(props, {\n        value: extendedConfig\n    }));\n};\n\nconst INFINITE_PREFIX = '$inf$';\n\n// @ts-expect-error\nconst enableDevtools = isWindowDefined && window.__SWR_DEVTOOLS_USE__;\nconst use = enableDevtools ? window.__SWR_DEVTOOLS_USE__ : [];\nconst setupDevTools = ()=>{\n    if (enableDevtools) {\n        // @ts-expect-error\n        window.__SWR_DEVTOOLS_REACT__ = react__WEBPACK_IMPORTED_MODULE_0__;\n    }\n};\n\nconst normalize = (args)=>{\n    return isFunction(args[1]) ? [\n        args[0],\n        args[1],\n        args[2] || {}\n    ] : [\n        args[0],\n        null,\n        (args[1] === null ? args[2] : args[1]) || {}\n    ];\n};\n\nconst useSWRConfig = ()=>{\n    return mergeObjects(defaultConfig, (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(SWRConfigContext));\n};\n\nconst preload = (key_, fetcher)=>{\n    const [key, fnArg] = serialize(key_);\n    const [, , , PRELOAD] = SWRGlobalState.get(cache);\n    // Prevent preload to be called multiple times before used.\n    if (PRELOAD[key]) return PRELOAD[key];\n    const req = fetcher(fnArg);\n    PRELOAD[key] = req;\n    return req;\n};\nconst middleware = (useSWRNext)=>(key_, fetcher_, config)=>{\n        // fetcher might be a sync function, so this should not be an async function\n        const fetcher = fetcher_ && ((...args)=>{\n            const [key] = serialize(key_);\n            const [, , , PRELOAD] = SWRGlobalState.get(cache);\n            if (key.startsWith(INFINITE_PREFIX)) {\n                // we want the infinite fetcher to be called.\n                // handling of the PRELOAD cache happens there.\n                return fetcher_(...args);\n            }\n            const req = PRELOAD[key];\n            if (isUndefined(req)) return fetcher_(...args);\n            delete PRELOAD[key];\n            return req;\n        });\n        return useSWRNext(key_, fetcher, config);\n    };\n\nconst BUILT_IN_MIDDLEWARE = use.concat(middleware);\n\n// It's tricky to pass generic types as parameters, so we just directly override\n// the types here.\nconst withArgs = (hook)=>{\n    return function useSWRArgs(...args) {\n        // Get the default and inherited configuration.\n        const fallbackConfig = useSWRConfig();\n        // Normalize arguments.\n        const [key, fn, _config] = normalize(args);\n        // Merge configurations.\n        const config = mergeConfigs(fallbackConfig, _config);\n        // Apply middleware\n        let next = hook;\n        const { use } = config;\n        const middleware = (use || []).concat(BUILT_IN_MIDDLEWARE);\n        for(let i = middleware.length; i--;){\n            next = middleware[i](next);\n        }\n        return next(key, fn || config.fetcher || null, config);\n    };\n};\n\n// Add a callback function to a list of keyed callback functions and return\n// the unsubscribe function.\nconst subscribeCallback = (key, callbacks, callback)=>{\n    const keyedRevalidators = callbacks[key] || (callbacks[key] = []);\n    keyedRevalidators.push(callback);\n    return ()=>{\n        const index = keyedRevalidators.indexOf(callback);\n        if (index >= 0) {\n            // O(1): faster than splice\n            keyedRevalidators[index] = keyedRevalidators[keyedRevalidators.length - 1];\n            keyedRevalidators.pop();\n        }\n    };\n};\n\n// Create a custom hook with a middleware\nconst withMiddleware = (useSWR, middleware)=>{\n    return (...args)=>{\n        const [key, fn, config] = normalize(args);\n        const uses = (config.use || []).concat(middleware);\n        return useSWR(key, fn, {\n            ...config,\n            use: uses\n        });\n    };\n};\n\nsetupDevTools();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/swr/dist/_internal/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/swr/dist/core/index.mjs":
/*!**********************************************!*\
  !*** ./node_modules/swr/dist/core/index.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SWRConfig: () => (/* binding */ SWRConfig),\n/* harmony export */   \"default\": () => (/* binding */ useSWR),\n/* harmony export */   mutate: () => (/* reexport safe */ swr_internal__WEBPACK_IMPORTED_MODULE_3__.mutate),\n/* harmony export */   preload: () => (/* reexport safe */ swr_internal__WEBPACK_IMPORTED_MODULE_3__.preload),\n/* harmony export */   unstable_serialize: () => (/* binding */ unstable_serialize),\n/* harmony export */   useSWRConfig: () => (/* reexport safe */ swr_internal__WEBPACK_IMPORTED_MODULE_3__.useSWRConfig)\n/* harmony export */ });\n/* harmony import */ var client_only__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! client-only */ \"(ssr)/./node_modules/next/dist/compiled/client-only/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! use-sync-external-store/shim/index.js */ \"(ssr)/./node_modules/use-sync-external-store/shim/index.js\");\n/* harmony import */ var swr_internal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! swr/_internal */ \"(ssr)/./node_modules/swr/dist/_internal/index.mjs\");\n\n\n\n\n\n\nconst unstable_serialize = (key)=>(0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.serialize)(key)[0];\n\n/// <reference types=\"react/experimental\" />\nconst use = react__WEBPACK_IMPORTED_MODULE_1__.use || ((promise)=>{\n    if (promise.status === 'pending') {\n        throw promise;\n    } else if (promise.status === 'fulfilled') {\n        return promise.value;\n    } else if (promise.status === 'rejected') {\n        throw promise.reason;\n    } else {\n        promise.status = 'pending';\n        promise.then((v)=>{\n            promise.status = 'fulfilled';\n            promise.value = v;\n        }, (e)=>{\n            promise.status = 'rejected';\n            promise.reason = e;\n        });\n        throw promise;\n    }\n});\nconst WITH_DEDUPE = {\n    dedupe: true\n};\nconst useSWRHandler = (_key, fetcher, config)=>{\n    const { cache, compare, suspense, fallbackData, revalidateOnMount, revalidateIfStale, refreshInterval, refreshWhenHidden, refreshWhenOffline, keepPreviousData } = config;\n    const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = swr_internal__WEBPACK_IMPORTED_MODULE_3__.SWRGlobalState.get(cache);\n    // `key` is the identifier of the SWR internal state,\n    // `fnArg` is the argument/arguments parsed from the key, which will be passed\n    // to the fetcher.\n    // All of them are derived from `_key`.\n    const [key, fnArg] = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.serialize)(_key);\n    // If it's the initial render of this hook.\n    const initialMountedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // If the hook is unmounted already. This will be used to prevent some effects\n    // to be called after unmounting.\n    const unmountedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Refs to keep the key and config.\n    const keyRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(key);\n    const fetcherRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(fetcher);\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(config);\n    const getConfig = ()=>configRef.current;\n    const isActive = ()=>getConfig().isVisible() && getConfig().isOnline();\n    const [getCache, setCache, subscribeCache, getInitialCache] = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.createCacheHelper)(cache, key);\n    const stateDependencies = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({}).current;\n    const fallback = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(fallbackData) ? config.fallback[key] : fallbackData;\n    const isEqual = (prev, current)=>{\n        for(const _ in stateDependencies){\n            const t = _;\n            if (t === 'data') {\n                if (!compare(prev[t], current[t])) {\n                    if (!(0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(prev[t])) {\n                        return false;\n                    }\n                    if (!compare(returnedData, current[t])) {\n                        return false;\n                    }\n                }\n            } else {\n                if (current[t] !== prev[t]) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    };\n    const getSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const shouldStartRequest = (()=>{\n            if (!key) return false;\n            if (!fetcher) return false;\n            // If `revalidateOnMount` is set, we take the value directly.\n            if (!(0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(revalidateOnMount)) return revalidateOnMount;\n            // If it's paused, we skip revalidation.\n            if (getConfig().isPaused()) return false;\n            if (suspense) return false;\n            if (!(0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(revalidateIfStale)) return revalidateIfStale;\n            return true;\n        })();\n        // Get the cache and merge it with expected states.\n        const getSelectedCache = (state)=>{\n            // We only select the needed fields from the state.\n            const snapshot = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.mergeObjects)(state);\n            delete snapshot._k;\n            if (!shouldStartRequest) {\n                return snapshot;\n            }\n            return {\n                isValidating: true,\n                isLoading: true,\n                ...snapshot\n            };\n        };\n        const cachedData = getCache();\n        const initialData = getInitialCache();\n        const clientSnapshot = getSelectedCache(cachedData);\n        const serverSnapshot = cachedData === initialData ? clientSnapshot : getSelectedCache(initialData);\n        // To make sure that we are returning the same object reference to avoid\n        // unnecessary re-renders, we keep the previous snapshot and use deep\n        // comparison to check if we need to return a new one.\n        let memorizedSnapshot = clientSnapshot;\n        return [\n            ()=>{\n                const newSnapshot = getSelectedCache(getCache());\n                const compareResult = isEqual(newSnapshot, memorizedSnapshot);\n                if (compareResult) {\n                    // Mentally, we should always return the `memorizedSnapshot` here\n                    // as there's no change between the new and old snapshots.\n                    // However, since the `isEqual` function only compares selected fields,\n                    // the values of the unselected fields might be changed. That's\n                    // simply because we didn't track them.\n                    // To support the case in https://github.com/vercel/swr/pull/2576,\n                    // we need to update these fields in the `memorizedSnapshot` too\n                    // with direct mutations to ensure the snapshot is always up-to-date\n                    // even for the unselected fields, but only trigger re-renders when\n                    // the selected fields are changed.\n                    memorizedSnapshot.data = newSnapshot.data;\n                    memorizedSnapshot.isLoading = newSnapshot.isLoading;\n                    memorizedSnapshot.isValidating = newSnapshot.isValidating;\n                    memorizedSnapshot.error = newSnapshot.error;\n                    return memorizedSnapshot;\n                } else {\n                    memorizedSnapshot = newSnapshot;\n                    return newSnapshot;\n                }\n            },\n            ()=>serverSnapshot\n        ];\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        cache,\n        key\n    ]);\n    // Get the current state that SWR should return.\n    const cached = (0,use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_2__.useSyncExternalStore)((0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((callback)=>subscribeCache(key, (current, prev)=>{\n            if (!isEqual(prev, current)) callback();\n        }), // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        cache,\n        key\n    ]), getSnapshot[0], getSnapshot[1]);\n    const isInitialMount = !initialMountedRef.current;\n    const hasRevalidator = EVENT_REVALIDATORS[key] && EVENT_REVALIDATORS[key].length > 0;\n    const cachedData = cached.data;\n    const data = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(cachedData) ? fallback : cachedData;\n    const error = cached.error;\n    // Use a ref to store previously returned data. Use the initial data as its initial value.\n    const laggyDataRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(data);\n    const returnedData = keepPreviousData ? (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(cachedData) ? laggyDataRef.current : cachedData : data;\n    // - Suspense mode and there's stale data for the initial render.\n    // - Not suspense mode and there is no fallback data and `revalidateIfStale` is enabled.\n    // - `revalidateIfStale` is enabled but `data` is not defined.\n    const shouldDoInitialRevalidation = (()=>{\n        // if a key already has revalidators and also has error, we should not trigger revalidation\n        if (hasRevalidator && !(0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(error)) return false;\n        // If `revalidateOnMount` is set, we take the value directly.\n        if (isInitialMount && !(0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(revalidateOnMount)) return revalidateOnMount;\n        // If it's paused, we skip revalidation.\n        if (getConfig().isPaused()) return false;\n        // Under suspense mode, it will always fetch on render if there is no\n        // stale data so no need to revalidate immediately mount it again.\n        // If data exists, only revalidate if `revalidateIfStale` is true.\n        if (suspense) return (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(data) ? false : revalidateIfStale;\n        // If there is no stale data, we need to revalidate when mount;\n        // If `revalidateIfStale` is set to true, we will always revalidate.\n        return (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(data) || revalidateIfStale;\n    })();\n    // Resolve the default validating state:\n    // If it's able to validate, and it should revalidate when mount, this will be true.\n    const defaultValidatingState = !!(key && fetcher && isInitialMount && shouldDoInitialRevalidation);\n    const isValidating = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(cached.isValidating) ? defaultValidatingState : cached.isValidating;\n    const isLoading = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(cached.isLoading) ? defaultValidatingState : cached.isLoading;\n    // The revalidation function is a carefully crafted wrapper of the original\n    // `fetcher`, to correctly handle the many edge cases.\n    const revalidate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (revalidateOpts)=>{\n        const currentFetcher = fetcherRef.current;\n        if (!key || !currentFetcher || unmountedRef.current || getConfig().isPaused()) {\n            return false;\n        }\n        let newData;\n        let startAt;\n        let loading = true;\n        const opts = revalidateOpts || {};\n        // If there is no ongoing concurrent request, or `dedupe` is not set, a\n        // new request should be initiated.\n        const shouldStartNewRequest = !FETCH[key] || !opts.dedupe;\n        /*\n         For React 17\n         Do unmount check for calls:\n         If key has changed during the revalidation, or the component has been\n         unmounted, old dispatch and old event callbacks should not take any\n         effect\n\n        For React 18\n        only check if key has changed\n        https://github.com/reactwg/react-18/discussions/82\n      */ const callbackSafeguard = ()=>{\n            if (swr_internal__WEBPACK_IMPORTED_MODULE_3__.IS_REACT_LEGACY) {\n                return !unmountedRef.current && key === keyRef.current && initialMountedRef.current;\n            }\n            return key === keyRef.current;\n        };\n        // The final state object when the request finishes.\n        const finalState = {\n            isValidating: false,\n            isLoading: false\n        };\n        const finishRequestAndUpdateState = ()=>{\n            setCache(finalState);\n        };\n        const cleanupState = ()=>{\n            // Check if it's still the same request before deleting it.\n            const requestInfo = FETCH[key];\n            if (requestInfo && requestInfo[1] === startAt) {\n                delete FETCH[key];\n            }\n        };\n        // Start fetching. Change the `isValidating` state, update the cache.\n        const initialState = {\n            isValidating: true\n        };\n        // It is in the `isLoading` state, if and only if there is no cached data.\n        // This bypasses fallback data and laggy data.\n        if ((0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(getCache().data)) {\n            initialState.isLoading = true;\n        }\n        try {\n            if (shouldStartNewRequest) {\n                setCache(initialState);\n                // If no cache is being rendered currently (it shows a blank page),\n                // we trigger the loading slow event.\n                if (config.loadingTimeout && (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(getCache().data)) {\n                    setTimeout(()=>{\n                        if (loading && callbackSafeguard()) {\n                            getConfig().onLoadingSlow(key, config);\n                        }\n                    }, config.loadingTimeout);\n                }\n                // Start the request and save the timestamp.\n                // Key must be truthy if entering here.\n                FETCH[key] = [\n                    currentFetcher(fnArg),\n                    (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.getTimestamp)()\n                ];\n            }\n            [newData, startAt] = FETCH[key];\n            newData = await newData;\n            if (shouldStartNewRequest) {\n                // If the request isn't interrupted, clean it up after the\n                // deduplication interval.\n                setTimeout(cleanupState, config.dedupingInterval);\n            }\n            // If there're other ongoing request(s), started after the current one,\n            // we need to ignore the current one to avoid possible race conditions:\n            //   req1------------------>res1        (current one)\n            //        req2---------------->res2\n            // the request that fired later will always be kept.\n            // The timestamp maybe be `undefined` or a number\n            if (!FETCH[key] || FETCH[key][1] !== startAt) {\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Clear error.\n            finalState.error = swr_internal__WEBPACK_IMPORTED_MODULE_3__.UNDEFINED;\n            // If there're other mutations(s), that overlapped with the current revalidation:\n            // case 1:\n            //   req------------------>res\n            //       mutate------>end\n            // case 2:\n            //         req------------>res\n            //   mutate------>end\n            // case 3:\n            //   req------------------>res\n            //       mutate-------...---------->\n            // we have to ignore the revalidation result (res) because it's no longer fresh.\n            // meanwhile, a new revalidation should be triggered when the mutation ends.\n            const mutationInfo = MUTATION[key];\n            if (!(0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(mutationInfo) && // case 1\n            (startAt <= mutationInfo[0] || // case 2\n            startAt <= mutationInfo[1] || // case 3\n            mutationInfo[1] === 0)) {\n                finishRequestAndUpdateState();\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Deep compare with the latest state to avoid extra re-renders.\n            // For local state, compare and assign.\n            const cacheData = getCache().data;\n            // Since the compare fn could be custom fn\n            // cacheData might be different from newData even when compare fn returns True\n            finalState.data = compare(cacheData, newData) ? cacheData : newData;\n            // Trigger the successful callback if it's the original request.\n            if (shouldStartNewRequest) {\n                if (callbackSafeguard()) {\n                    getConfig().onSuccess(newData, key, config);\n                }\n            }\n        } catch (err) {\n            cleanupState();\n            const currentConfig = getConfig();\n            const { shouldRetryOnError } = currentConfig;\n            // Not paused, we continue handling the error. Otherwise, discard it.\n            if (!currentConfig.isPaused()) {\n                // Get a new error, don't use deep comparison for errors.\n                finalState.error = err;\n                // Error event and retry logic. Only for the actual request, not\n                // deduped ones.\n                if (shouldStartNewRequest && callbackSafeguard()) {\n                    currentConfig.onError(err, key, currentConfig);\n                    if (shouldRetryOnError === true || (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isFunction)(shouldRetryOnError) && shouldRetryOnError(err)) {\n                        if (!getConfig().revalidateOnFocus || !getConfig().revalidateOnReconnect || isActive()) {\n                            // If it's inactive, stop. It will auto-revalidate when\n                            // refocusing or reconnecting.\n                            // When retrying, deduplication is always enabled.\n                            currentConfig.onErrorRetry(err, key, currentConfig, (_opts)=>{\n                                const revalidators = EVENT_REVALIDATORS[key];\n                                if (revalidators && revalidators[0]) {\n                                    revalidators[0](swr_internal__WEBPACK_IMPORTED_MODULE_3__.revalidateEvents.ERROR_REVALIDATE_EVENT, _opts);\n                                }\n                            }, {\n                                retryCount: (opts.retryCount || 0) + 1,\n                                dedupe: true\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        // Mark loading as stopped.\n        loading = false;\n        // Update the current hook's state.\n        finishRequestAndUpdateState();\n        return true;\n    }, // `setState` is immutable, and `eventsCallback`, `fnArg`, and\n    // `keyValidating` are depending on `key`, so we can exclude them from\n    // the deps array.\n    //\n    // FIXME:\n    // `fn` and `config` might be changed during the lifecycle,\n    // but they might be changed every render like this.\n    // `useSWR('key', () => fetch('/api/'), { suspense: true })`\n    // So we omit the values from the deps array\n    // even though it might cause unexpected behaviors.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        key,\n        cache\n    ]);\n    // Similar to the global mutate but bound to the current cache and key.\n    // `cache` isn't allowed to change during the lifecycle.\n    const boundMutate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(// Use callback to make sure `keyRef.current` returns latest result every time\n    (...args)=>{\n        return (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.internalMutate)(cache, keyRef.current, ...args);\n    }, // eslint-disable-next-line react-hooks/exhaustive-deps\n    []);\n    // The logic for updating refs.\n    (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.useIsomorphicLayoutEffect)(()=>{\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        // Handle laggy data updates. If there's cached data of the current key,\n        // it'll be the correct reference.\n        if (!(0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(cachedData)) {\n            laggyDataRef.current = cachedData;\n        }\n    });\n    // After mounted or key changed.\n    (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.useIsomorphicLayoutEffect)(()=>{\n        if (!key) return;\n        const softRevalidate = revalidate.bind(swr_internal__WEBPACK_IMPORTED_MODULE_3__.UNDEFINED, WITH_DEDUPE);\n        // Expose revalidators to global event listeners. So we can trigger\n        // revalidation from the outside.\n        let nextFocusRevalidatedAt = 0;\n        const onRevalidate = (type, opts = {})=>{\n            if (type == swr_internal__WEBPACK_IMPORTED_MODULE_3__.revalidateEvents.FOCUS_EVENT) {\n                const now = Date.now();\n                if (getConfig().revalidateOnFocus && now > nextFocusRevalidatedAt && isActive()) {\n                    nextFocusRevalidatedAt = now + getConfig().focusThrottleInterval;\n                    softRevalidate();\n                }\n            } else if (type == swr_internal__WEBPACK_IMPORTED_MODULE_3__.revalidateEvents.RECONNECT_EVENT) {\n                if (getConfig().revalidateOnReconnect && isActive()) {\n                    softRevalidate();\n                }\n            } else if (type == swr_internal__WEBPACK_IMPORTED_MODULE_3__.revalidateEvents.MUTATE_EVENT) {\n                return revalidate();\n            } else if (type == swr_internal__WEBPACK_IMPORTED_MODULE_3__.revalidateEvents.ERROR_REVALIDATE_EVENT) {\n                return revalidate(opts);\n            }\n            return;\n        };\n        const unsubEvents = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.subscribeCallback)(key, EVENT_REVALIDATORS, onRevalidate);\n        // Mark the component as mounted and update corresponding refs.\n        unmountedRef.current = false;\n        keyRef.current = key;\n        initialMountedRef.current = true;\n        // Keep the original key in the cache.\n        setCache({\n            _k: fnArg\n        });\n        // Trigger a revalidation\n        if (shouldDoInitialRevalidation) {\n            if ((0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(data) || swr_internal__WEBPACK_IMPORTED_MODULE_3__.IS_SERVER) {\n                // Revalidate immediately.\n                softRevalidate();\n            } else {\n                // Delay the revalidate if we have data to return so we won't block\n                // rendering.\n                (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.rAF)(softRevalidate);\n            }\n        }\n        return ()=>{\n            // Mark it as unmounted.\n            unmountedRef.current = true;\n            unsubEvents();\n        };\n    }, [\n        key\n    ]);\n    // Polling\n    (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.useIsomorphicLayoutEffect)(()=>{\n        let timer;\n        function next() {\n            // Use the passed interval\n            // ...or invoke the function with the updated data to get the interval\n            const interval = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isFunction)(refreshInterval) ? refreshInterval(getCache().data) : refreshInterval;\n            // We only start the next interval if `refreshInterval` is not 0, and:\n            // - `force` is true, which is the start of polling\n            // - or `timer` is not 0, which means the effect wasn't canceled\n            if (interval && timer !== -1) {\n                timer = setTimeout(execute, interval);\n            }\n        }\n        function execute() {\n            // Check if it's OK to execute:\n            // Only revalidate when the page is visible, online, and not errored.\n            if (!getCache().error && (refreshWhenHidden || getConfig().isVisible()) && (refreshWhenOffline || getConfig().isOnline())) {\n                revalidate(WITH_DEDUPE).then(next);\n            } else {\n                // Schedule the next interval to check again.\n                next();\n            }\n        }\n        next();\n        return ()=>{\n            if (timer) {\n                clearTimeout(timer);\n                timer = -1;\n            }\n        };\n    }, [\n        refreshInterval,\n        refreshWhenHidden,\n        refreshWhenOffline,\n        key\n    ]);\n    // Display debug info in React DevTools.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useDebugValue)(returnedData);\n    // In Suspense mode, we can't return the empty `data` state.\n    // If there is an `error`, the `error` needs to be thrown to the error boundary.\n    // If there is no `error`, the `revalidation` promise needs to be thrown to\n    // the suspense boundary.\n    if (suspense && (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(data) && key) {\n        // SWR should throw when trying to use Suspense on the server with React 18,\n        // without providing any initial data. See:\n        // https://github.com/vercel/swr/issues/1832\n        if (!swr_internal__WEBPACK_IMPORTED_MODULE_3__.IS_REACT_LEGACY && swr_internal__WEBPACK_IMPORTED_MODULE_3__.IS_SERVER) {\n            throw new Error('Fallback data is required when using suspense in SSR.');\n        }\n        // Always update fetcher and config refs even with the Suspense mode.\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        unmountedRef.current = false;\n        const req = PRELOAD[key];\n        if (!(0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(req)) {\n            const promise = boundMutate(req);\n            use(promise);\n        }\n        if ((0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(error)) {\n            const promise = revalidate(WITH_DEDUPE);\n            if (!(0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(returnedData)) {\n                promise.status = 'fulfilled';\n                promise.value = true;\n            }\n            use(promise);\n        } else {\n            throw error;\n        }\n    }\n    return {\n        mutate: boundMutate,\n        get data () {\n            stateDependencies.data = true;\n            return returnedData;\n        },\n        get error () {\n            stateDependencies.error = true;\n            return error;\n        },\n        get isValidating () {\n            stateDependencies.isValidating = true;\n            return isValidating;\n        },\n        get isLoading () {\n            stateDependencies.isLoading = true;\n            return isLoading;\n        }\n    };\n};\nconst SWRConfig = swr_internal__WEBPACK_IMPORTED_MODULE_3__.OBJECT.defineProperty(swr_internal__WEBPACK_IMPORTED_MODULE_3__.SWRConfig, 'defaultValue', {\n    value: swr_internal__WEBPACK_IMPORTED_MODULE_3__.defaultConfig\n});\n/**\n * A hook to fetch data.\n *\n * @link https://swr.vercel.app\n * @example\n * ```jsx\n * import useSWR from 'swr'\n * function Profile() {\n *   const { data, error, isLoading } = useSWR('/api/user', fetcher)\n *   if (error) return <div>failed to load</div>\n *   if (isLoading) return <div>loading...</div>\n *   return <div>hello {data.name}!</div>\n * }\n * ```\n */ const useSWR = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.withArgs)(useSWRHandler);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/swr/dist/core/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/swr/dist/infinite/index.mjs":
/*!**************************************************!*\
  !*** ./node_modules/swr/dist/infinite/index.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useSWRInfinite),\n/* harmony export */   infinite: () => (/* binding */ infinite),\n/* harmony export */   unstable_serialize: () => (/* binding */ unstable_serialize)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var client_only__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! client-only */ \"(ssr)/./node_modules/next/dist/compiled/client-only/index.js\");\n/* harmony import */ var use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! use-sync-external-store/shim/index.js */ \"(ssr)/./node_modules/use-sync-external-store/shim/index.js\");\n/* harmony import */ var swr_internal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! swr/_internal */ \"(ssr)/./node_modules/swr/dist/_internal/index.mjs\");\n\n\n\n\n\n/// <reference types=\"react/experimental\" />\nconst use = react__WEBPACK_IMPORTED_MODULE_0__.use || ((promise)=>{\n    if (promise.status === 'pending') {\n        throw promise;\n    } else if (promise.status === 'fulfilled') {\n        return promise.value;\n    } else if (promise.status === 'rejected') {\n        throw promise.reason;\n    } else {\n        promise.status = 'pending';\n        promise.then((v)=>{\n            promise.status = 'fulfilled';\n            promise.value = v;\n        }, (e)=>{\n            promise.status = 'rejected';\n            promise.reason = e;\n        });\n        throw promise;\n    }\n});\nconst WITH_DEDUPE = {\n    dedupe: true\n};\nconst useSWRHandler = (_key, fetcher, config)=>{\n    const { cache, compare, suspense, fallbackData, revalidateOnMount, revalidateIfStale, refreshInterval, refreshWhenHidden, refreshWhenOffline, keepPreviousData } = config;\n    const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = swr_internal__WEBPACK_IMPORTED_MODULE_3__.SWRGlobalState.get(cache);\n    // `key` is the identifier of the SWR internal state,\n    // `fnArg` is the argument/arguments parsed from the key, which will be passed\n    // to the fetcher.\n    // All of them are derived from `_key`.\n    const [key, fnArg] = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.serialize)(_key);\n    // If it's the initial render of this hook.\n    const initialMountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // If the hook is unmounted already. This will be used to prevent some effects\n    // to be called after unmounting.\n    const unmountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Refs to keep the key and config.\n    const keyRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(key);\n    const fetcherRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(fetcher);\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(config);\n    const getConfig = ()=>configRef.current;\n    const isActive = ()=>getConfig().isVisible() && getConfig().isOnline();\n    const [getCache, setCache, subscribeCache, getInitialCache] = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.createCacheHelper)(cache, key);\n    const stateDependencies = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({}).current;\n    const fallback = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(fallbackData) ? config.fallback[key] : fallbackData;\n    const isEqual = (prev, current)=>{\n        for(const _ in stateDependencies){\n            const t = _;\n            if (t === 'data') {\n                if (!compare(prev[t], current[t])) {\n                    if (!(0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(prev[t])) {\n                        return false;\n                    }\n                    if (!compare(returnedData, current[t])) {\n                        return false;\n                    }\n                }\n            } else {\n                if (current[t] !== prev[t]) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    };\n    const getSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const shouldStartRequest = (()=>{\n            if (!key) return false;\n            if (!fetcher) return false;\n            // If `revalidateOnMount` is set, we take the value directly.\n            if (!(0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(revalidateOnMount)) return revalidateOnMount;\n            // If it's paused, we skip revalidation.\n            if (getConfig().isPaused()) return false;\n            if (suspense) return false;\n            if (!(0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(revalidateIfStale)) return revalidateIfStale;\n            return true;\n        })();\n        // Get the cache and merge it with expected states.\n        const getSelectedCache = (state)=>{\n            // We only select the needed fields from the state.\n            const snapshot = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.mergeObjects)(state);\n            delete snapshot._k;\n            if (!shouldStartRequest) {\n                return snapshot;\n            }\n            return {\n                isValidating: true,\n                isLoading: true,\n                ...snapshot\n            };\n        };\n        const cachedData = getCache();\n        const initialData = getInitialCache();\n        const clientSnapshot = getSelectedCache(cachedData);\n        const serverSnapshot = cachedData === initialData ? clientSnapshot : getSelectedCache(initialData);\n        // To make sure that we are returning the same object reference to avoid\n        // unnecessary re-renders, we keep the previous snapshot and use deep\n        // comparison to check if we need to return a new one.\n        let memorizedSnapshot = clientSnapshot;\n        return [\n            ()=>{\n                const newSnapshot = getSelectedCache(getCache());\n                const compareResult = isEqual(newSnapshot, memorizedSnapshot);\n                if (compareResult) {\n                    // Mentally, we should always return the `memorizedSnapshot` here\n                    // as there's no change between the new and old snapshots.\n                    // However, since the `isEqual` function only compares selected fields,\n                    // the values of the unselected fields might be changed. That's\n                    // simply because we didn't track them.\n                    // To support the case in https://github.com/vercel/swr/pull/2576,\n                    // we need to update these fields in the `memorizedSnapshot` too\n                    // with direct mutations to ensure the snapshot is always up-to-date\n                    // even for the unselected fields, but only trigger re-renders when\n                    // the selected fields are changed.\n                    memorizedSnapshot.data = newSnapshot.data;\n                    memorizedSnapshot.isLoading = newSnapshot.isLoading;\n                    memorizedSnapshot.isValidating = newSnapshot.isValidating;\n                    memorizedSnapshot.error = newSnapshot.error;\n                    return memorizedSnapshot;\n                } else {\n                    memorizedSnapshot = newSnapshot;\n                    return newSnapshot;\n                }\n            },\n            ()=>serverSnapshot\n        ];\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        cache,\n        key\n    ]);\n    // Get the current state that SWR should return.\n    const cached = (0,use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_2__.useSyncExternalStore)((0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((callback)=>subscribeCache(key, (current, prev)=>{\n            if (!isEqual(prev, current)) callback();\n        }), // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        cache,\n        key\n    ]), getSnapshot[0], getSnapshot[1]);\n    const isInitialMount = !initialMountedRef.current;\n    const hasRevalidator = EVENT_REVALIDATORS[key] && EVENT_REVALIDATORS[key].length > 0;\n    const cachedData = cached.data;\n    const data = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(cachedData) ? fallback : cachedData;\n    const error = cached.error;\n    // Use a ref to store previously returned data. Use the initial data as its initial value.\n    const laggyDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(data);\n    const returnedData = keepPreviousData ? (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(cachedData) ? laggyDataRef.current : cachedData : data;\n    // - Suspense mode and there's stale data for the initial render.\n    // - Not suspense mode and there is no fallback data and `revalidateIfStale` is enabled.\n    // - `revalidateIfStale` is enabled but `data` is not defined.\n    const shouldDoInitialRevalidation = (()=>{\n        // if a key already has revalidators and also has error, we should not trigger revalidation\n        if (hasRevalidator && !(0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(error)) return false;\n        // If `revalidateOnMount` is set, we take the value directly.\n        if (isInitialMount && !(0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(revalidateOnMount)) return revalidateOnMount;\n        // If it's paused, we skip revalidation.\n        if (getConfig().isPaused()) return false;\n        // Under suspense mode, it will always fetch on render if there is no\n        // stale data so no need to revalidate immediately mount it again.\n        // If data exists, only revalidate if `revalidateIfStale` is true.\n        if (suspense) return (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(data) ? false : revalidateIfStale;\n        // If there is no stale data, we need to revalidate when mount;\n        // If `revalidateIfStale` is set to true, we will always revalidate.\n        return (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(data) || revalidateIfStale;\n    })();\n    // Resolve the default validating state:\n    // If it's able to validate, and it should revalidate when mount, this will be true.\n    const defaultValidatingState = !!(key && fetcher && isInitialMount && shouldDoInitialRevalidation);\n    const isValidating = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(cached.isValidating) ? defaultValidatingState : cached.isValidating;\n    const isLoading = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(cached.isLoading) ? defaultValidatingState : cached.isLoading;\n    // The revalidation function is a carefully crafted wrapper of the original\n    // `fetcher`, to correctly handle the many edge cases.\n    const revalidate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (revalidateOpts)=>{\n        const currentFetcher = fetcherRef.current;\n        if (!key || !currentFetcher || unmountedRef.current || getConfig().isPaused()) {\n            return false;\n        }\n        let newData;\n        let startAt;\n        let loading = true;\n        const opts = revalidateOpts || {};\n        // If there is no ongoing concurrent request, or `dedupe` is not set, a\n        // new request should be initiated.\n        const shouldStartNewRequest = !FETCH[key] || !opts.dedupe;\n        /*\n         For React 17\n         Do unmount check for calls:\n         If key has changed during the revalidation, or the component has been\n         unmounted, old dispatch and old event callbacks should not take any\n         effect\n\n        For React 18\n        only check if key has changed\n        https://github.com/reactwg/react-18/discussions/82\n      */ const callbackSafeguard = ()=>{\n            if (swr_internal__WEBPACK_IMPORTED_MODULE_3__.IS_REACT_LEGACY) {\n                return !unmountedRef.current && key === keyRef.current && initialMountedRef.current;\n            }\n            return key === keyRef.current;\n        };\n        // The final state object when the request finishes.\n        const finalState = {\n            isValidating: false,\n            isLoading: false\n        };\n        const finishRequestAndUpdateState = ()=>{\n            setCache(finalState);\n        };\n        const cleanupState = ()=>{\n            // Check if it's still the same request before deleting it.\n            const requestInfo = FETCH[key];\n            if (requestInfo && requestInfo[1] === startAt) {\n                delete FETCH[key];\n            }\n        };\n        // Start fetching. Change the `isValidating` state, update the cache.\n        const initialState = {\n            isValidating: true\n        };\n        // It is in the `isLoading` state, if and only if there is no cached data.\n        // This bypasses fallback data and laggy data.\n        if ((0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(getCache().data)) {\n            initialState.isLoading = true;\n        }\n        try {\n            if (shouldStartNewRequest) {\n                setCache(initialState);\n                // If no cache is being rendered currently (it shows a blank page),\n                // we trigger the loading slow event.\n                if (config.loadingTimeout && (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(getCache().data)) {\n                    setTimeout(()=>{\n                        if (loading && callbackSafeguard()) {\n                            getConfig().onLoadingSlow(key, config);\n                        }\n                    }, config.loadingTimeout);\n                }\n                // Start the request and save the timestamp.\n                // Key must be truthy if entering here.\n                FETCH[key] = [\n                    currentFetcher(fnArg),\n                    (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.getTimestamp)()\n                ];\n            }\n            [newData, startAt] = FETCH[key];\n            newData = await newData;\n            if (shouldStartNewRequest) {\n                // If the request isn't interrupted, clean it up after the\n                // deduplication interval.\n                setTimeout(cleanupState, config.dedupingInterval);\n            }\n            // If there're other ongoing request(s), started after the current one,\n            // we need to ignore the current one to avoid possible race conditions:\n            //   req1------------------>res1        (current one)\n            //        req2---------------->res2\n            // the request that fired later will always be kept.\n            // The timestamp maybe be `undefined` or a number\n            if (!FETCH[key] || FETCH[key][1] !== startAt) {\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Clear error.\n            finalState.error = swr_internal__WEBPACK_IMPORTED_MODULE_3__.UNDEFINED;\n            // If there're other mutations(s), that overlapped with the current revalidation:\n            // case 1:\n            //   req------------------>res\n            //       mutate------>end\n            // case 2:\n            //         req------------>res\n            //   mutate------>end\n            // case 3:\n            //   req------------------>res\n            //       mutate-------...---------->\n            // we have to ignore the revalidation result (res) because it's no longer fresh.\n            // meanwhile, a new revalidation should be triggered when the mutation ends.\n            const mutationInfo = MUTATION[key];\n            if (!(0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(mutationInfo) && // case 1\n            (startAt <= mutationInfo[0] || // case 2\n            startAt <= mutationInfo[1] || // case 3\n            mutationInfo[1] === 0)) {\n                finishRequestAndUpdateState();\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Deep compare with the latest state to avoid extra re-renders.\n            // For local state, compare and assign.\n            const cacheData = getCache().data;\n            // Since the compare fn could be custom fn\n            // cacheData might be different from newData even when compare fn returns True\n            finalState.data = compare(cacheData, newData) ? cacheData : newData;\n            // Trigger the successful callback if it's the original request.\n            if (shouldStartNewRequest) {\n                if (callbackSafeguard()) {\n                    getConfig().onSuccess(newData, key, config);\n                }\n            }\n        } catch (err) {\n            cleanupState();\n            const currentConfig = getConfig();\n            const { shouldRetryOnError } = currentConfig;\n            // Not paused, we continue handling the error. Otherwise, discard it.\n            if (!currentConfig.isPaused()) {\n                // Get a new error, don't use deep comparison for errors.\n                finalState.error = err;\n                // Error event and retry logic. Only for the actual request, not\n                // deduped ones.\n                if (shouldStartNewRequest && callbackSafeguard()) {\n                    currentConfig.onError(err, key, currentConfig);\n                    if (shouldRetryOnError === true || (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isFunction)(shouldRetryOnError) && shouldRetryOnError(err)) {\n                        if (!getConfig().revalidateOnFocus || !getConfig().revalidateOnReconnect || isActive()) {\n                            // If it's inactive, stop. It will auto-revalidate when\n                            // refocusing or reconnecting.\n                            // When retrying, deduplication is always enabled.\n                            currentConfig.onErrorRetry(err, key, currentConfig, (_opts)=>{\n                                const revalidators = EVENT_REVALIDATORS[key];\n                                if (revalidators && revalidators[0]) {\n                                    revalidators[0](swr_internal__WEBPACK_IMPORTED_MODULE_3__.revalidateEvents.ERROR_REVALIDATE_EVENT, _opts);\n                                }\n                            }, {\n                                retryCount: (opts.retryCount || 0) + 1,\n                                dedupe: true\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        // Mark loading as stopped.\n        loading = false;\n        // Update the current hook's state.\n        finishRequestAndUpdateState();\n        return true;\n    }, // `setState` is immutable, and `eventsCallback`, `fnArg`, and\n    // `keyValidating` are depending on `key`, so we can exclude them from\n    // the deps array.\n    //\n    // FIXME:\n    // `fn` and `config` might be changed during the lifecycle,\n    // but they might be changed every render like this.\n    // `useSWR('key', () => fetch('/api/'), { suspense: true })`\n    // So we omit the values from the deps array\n    // even though it might cause unexpected behaviors.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        key,\n        cache\n    ]);\n    // Similar to the global mutate but bound to the current cache and key.\n    // `cache` isn't allowed to change during the lifecycle.\n    const boundMutate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(// Use callback to make sure `keyRef.current` returns latest result every time\n    (...args)=>{\n        return (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.internalMutate)(cache, keyRef.current, ...args);\n    }, // eslint-disable-next-line react-hooks/exhaustive-deps\n    []);\n    // The logic for updating refs.\n    (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.useIsomorphicLayoutEffect)(()=>{\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        // Handle laggy data updates. If there's cached data of the current key,\n        // it'll be the correct reference.\n        if (!(0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(cachedData)) {\n            laggyDataRef.current = cachedData;\n        }\n    });\n    // After mounted or key changed.\n    (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.useIsomorphicLayoutEffect)(()=>{\n        if (!key) return;\n        const softRevalidate = revalidate.bind(swr_internal__WEBPACK_IMPORTED_MODULE_3__.UNDEFINED, WITH_DEDUPE);\n        // Expose revalidators to global event listeners. So we can trigger\n        // revalidation from the outside.\n        let nextFocusRevalidatedAt = 0;\n        const onRevalidate = (type, opts = {})=>{\n            if (type == swr_internal__WEBPACK_IMPORTED_MODULE_3__.revalidateEvents.FOCUS_EVENT) {\n                const now = Date.now();\n                if (getConfig().revalidateOnFocus && now > nextFocusRevalidatedAt && isActive()) {\n                    nextFocusRevalidatedAt = now + getConfig().focusThrottleInterval;\n                    softRevalidate();\n                }\n            } else if (type == swr_internal__WEBPACK_IMPORTED_MODULE_3__.revalidateEvents.RECONNECT_EVENT) {\n                if (getConfig().revalidateOnReconnect && isActive()) {\n                    softRevalidate();\n                }\n            } else if (type == swr_internal__WEBPACK_IMPORTED_MODULE_3__.revalidateEvents.MUTATE_EVENT) {\n                return revalidate();\n            } else if (type == swr_internal__WEBPACK_IMPORTED_MODULE_3__.revalidateEvents.ERROR_REVALIDATE_EVENT) {\n                return revalidate(opts);\n            }\n            return;\n        };\n        const unsubEvents = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.subscribeCallback)(key, EVENT_REVALIDATORS, onRevalidate);\n        // Mark the component as mounted and update corresponding refs.\n        unmountedRef.current = false;\n        keyRef.current = key;\n        initialMountedRef.current = true;\n        // Keep the original key in the cache.\n        setCache({\n            _k: fnArg\n        });\n        // Trigger a revalidation\n        if (shouldDoInitialRevalidation) {\n            if ((0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(data) || swr_internal__WEBPACK_IMPORTED_MODULE_3__.IS_SERVER) {\n                // Revalidate immediately.\n                softRevalidate();\n            } else {\n                // Delay the revalidate if we have data to return so we won't block\n                // rendering.\n                (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.rAF)(softRevalidate);\n            }\n        }\n        return ()=>{\n            // Mark it as unmounted.\n            unmountedRef.current = true;\n            unsubEvents();\n        };\n    }, [\n        key\n    ]);\n    // Polling\n    (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.useIsomorphicLayoutEffect)(()=>{\n        let timer;\n        function next() {\n            // Use the passed interval\n            // ...or invoke the function with the updated data to get the interval\n            const interval = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isFunction)(refreshInterval) ? refreshInterval(getCache().data) : refreshInterval;\n            // We only start the next interval if `refreshInterval` is not 0, and:\n            // - `force` is true, which is the start of polling\n            // - or `timer` is not 0, which means the effect wasn't canceled\n            if (interval && timer !== -1) {\n                timer = setTimeout(execute, interval);\n            }\n        }\n        function execute() {\n            // Check if it's OK to execute:\n            // Only revalidate when the page is visible, online, and not errored.\n            if (!getCache().error && (refreshWhenHidden || getConfig().isVisible()) && (refreshWhenOffline || getConfig().isOnline())) {\n                revalidate(WITH_DEDUPE).then(next);\n            } else {\n                // Schedule the next interval to check again.\n                next();\n            }\n        }\n        next();\n        return ()=>{\n            if (timer) {\n                clearTimeout(timer);\n                timer = -1;\n            }\n        };\n    }, [\n        refreshInterval,\n        refreshWhenHidden,\n        refreshWhenOffline,\n        key\n    ]);\n    // Display debug info in React DevTools.\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue)(returnedData);\n    // In Suspense mode, we can't return the empty `data` state.\n    // If there is an `error`, the `error` needs to be thrown to the error boundary.\n    // If there is no `error`, the `revalidation` promise needs to be thrown to\n    // the suspense boundary.\n    if (suspense && (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(data) && key) {\n        // SWR should throw when trying to use Suspense on the server with React 18,\n        // without providing any initial data. See:\n        // https://github.com/vercel/swr/issues/1832\n        if (!swr_internal__WEBPACK_IMPORTED_MODULE_3__.IS_REACT_LEGACY && swr_internal__WEBPACK_IMPORTED_MODULE_3__.IS_SERVER) {\n            throw new Error('Fallback data is required when using suspense in SSR.');\n        }\n        // Always update fetcher and config refs even with the Suspense mode.\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        unmountedRef.current = false;\n        const req = PRELOAD[key];\n        if (!(0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(req)) {\n            const promise = boundMutate(req);\n            use(promise);\n        }\n        if ((0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(error)) {\n            const promise = revalidate(WITH_DEDUPE);\n            if (!(0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(returnedData)) {\n                promise.status = 'fulfilled';\n                promise.value = true;\n            }\n            use(promise);\n        } else {\n            throw error;\n        }\n    }\n    return {\n        mutate: boundMutate,\n        get data () {\n            stateDependencies.data = true;\n            return returnedData;\n        },\n        get error () {\n            stateDependencies.error = true;\n            return error;\n        },\n        get isValidating () {\n            stateDependencies.isValidating = true;\n            return isValidating;\n        },\n        get isLoading () {\n            stateDependencies.isLoading = true;\n            return isLoading;\n        }\n    };\n};\nswr_internal__WEBPACK_IMPORTED_MODULE_3__.OBJECT.defineProperty(swr_internal__WEBPACK_IMPORTED_MODULE_3__.SWRConfig, 'defaultValue', {\n    value: swr_internal__WEBPACK_IMPORTED_MODULE_3__.defaultConfig\n});\n/**\n * A hook to fetch data.\n *\n * @link https://swr.vercel.app\n * @example\n * ```jsx\n * import useSWR from 'swr'\n * function Profile() {\n *   const { data, error, isLoading } = useSWR('/api/user', fetcher)\n *   if (error) return <div>failed to load</div>\n *   if (isLoading) return <div>loading...</div>\n *   return <div>hello {data.name}!</div>\n * }\n * ```\n */ const useSWR = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.withArgs)(useSWRHandler);\n\nconst getFirstPageKey = (getKey)=>{\n    return (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.serialize)(getKey ? getKey(0, null) : null)[0];\n};\nconst unstable_serialize = (getKey)=>{\n    return swr_internal__WEBPACK_IMPORTED_MODULE_3__.INFINITE_PREFIX + getFirstPageKey(getKey);\n};\n\n// We have to several type castings here because `useSWRInfinite` is a special\n// hook where `key` and return type are not like the normal `useSWR` types.\n// const INFINITE_PREFIX = '$inf$'\nconst EMPTY_PROMISE = Promise.resolve();\n// export const unstable_serialize = (getKey: SWRInfiniteKeyLoader) => {\n//   return INFINITE_PREFIX + getFirstPageKey(getKey)\n// }\nconst infinite = (useSWRNext)=>(getKey, fn, config)=>{\n        const didMountRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n        const { cache: cache$1, initialSize = 1, revalidateAll = false, persistSize = false, revalidateFirstPage = true, revalidateOnMount = false, parallel = false } = config;\n        const [, , , PRELOAD] = swr_internal__WEBPACK_IMPORTED_MODULE_3__.SWRGlobalState.get(swr_internal__WEBPACK_IMPORTED_MODULE_3__.cache);\n        // The serialized key of the first page. This key will be used to store\n        // metadata of this SWR infinite hook.\n        let infiniteKey;\n        try {\n            infiniteKey = getFirstPageKey(getKey);\n            if (infiniteKey) infiniteKey = swr_internal__WEBPACK_IMPORTED_MODULE_3__.INFINITE_PREFIX + infiniteKey;\n        } catch (err) {\n        // Not ready yet.\n        }\n        const [get, set, subscribeCache] = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.createCacheHelper)(cache$1, infiniteKey);\n        const getSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n            const size = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(get()._l) ? initialSize : get()._l;\n            return size;\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            cache$1,\n            infiniteKey,\n            initialSize\n        ]);\n        (0,use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_2__.useSyncExternalStore)((0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((callback)=>{\n            if (infiniteKey) return subscribeCache(infiniteKey, ()=>{\n                callback();\n            });\n            return ()=>{};\n        }, // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            cache$1,\n            infiniteKey\n        ]), getSnapshot, getSnapshot);\n        const resolvePageSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n            const cachedPageSize = get()._l;\n            return (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(cachedPageSize) ? initialSize : cachedPageSize;\n        // `cache` isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            infiniteKey,\n            initialSize\n        ]);\n        // keep the last page size to restore it with the persistSize option\n        const lastPageSizeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(resolvePageSize());\n        // When the page key changes, we reset the page size if it's not persisted\n        (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.useIsomorphicLayoutEffect)(()=>{\n            if (!didMountRef.current) {\n                didMountRef.current = true;\n                return;\n            }\n            if (infiniteKey) {\n                // If the key has been changed, we keep the current page size if persistSize is enabled\n                // Otherwise, we reset the page size to cached pageSize\n                set({\n                    _l: persistSize ? lastPageSizeRef.current : resolvePageSize()\n                });\n            }\n        // `initialSize` isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            infiniteKey,\n            cache$1\n        ]);\n        // Needs to check didMountRef during mounting, not in the fetcher\n        const shouldRevalidateOnMount = revalidateOnMount && !didMountRef.current;\n        // Actual SWR hook to load all pages in one fetcher.\n        const swr = useSWRNext(infiniteKey, async (key)=>{\n            // get the revalidate context\n            const forceRevalidateAll = get()._i;\n            const shouldRevalidatePage = get()._r;\n            set({\n                _r: swr_internal__WEBPACK_IMPORTED_MODULE_3__.UNDEFINED\n            });\n            // return an array of page data\n            const data = [];\n            const pageSize = resolvePageSize();\n            const [getCache] = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.createCacheHelper)(cache$1, key);\n            const cacheData = getCache().data;\n            const revalidators = [];\n            let previousPageData = null;\n            for(let i = 0; i < pageSize; ++i){\n                const [pageKey, pageArg] = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.serialize)(getKey(i, parallel ? null : previousPageData));\n                if (!pageKey) {\n                    break;\n                }\n                const [getSWRCache, setSWRCache] = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.createCacheHelper)(cache$1, pageKey);\n                // Get the cached page data.\n                let pageData = getSWRCache().data;\n                // should fetch (or revalidate) if:\n                // - `revalidateAll` is enabled\n                // - `mutate()` called\n                // - the cache is missing\n                // - it's the first page and it's not the initial render\n                // - `revalidateOnMount` is enabled and it's on mount\n                // - cache for that page has changed\n                const shouldFetchPage = revalidateAll || forceRevalidateAll || (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(pageData) || revalidateFirstPage && !i && !(0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(cacheData) || shouldRevalidateOnMount || cacheData && !(0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(cacheData[i]) && !config.compare(cacheData[i], pageData);\n                if (fn && (typeof shouldRevalidatePage === 'function' ? shouldRevalidatePage(pageData, pageArg) : shouldFetchPage)) {\n                    const revalidate = async ()=>{\n                        const hasPreloadedRequest = pageKey in PRELOAD;\n                        if (!hasPreloadedRequest) {\n                            pageData = await fn(pageArg);\n                        } else {\n                            const req = PRELOAD[pageKey];\n                            // delete the preload cache key before resolving it\n                            // in case there's an error\n                            delete PRELOAD[pageKey];\n                            // get the page data from the preload cache\n                            pageData = await req;\n                        }\n                        setSWRCache({\n                            data: pageData,\n                            _k: pageArg\n                        });\n                        data[i] = pageData;\n                    };\n                    if (parallel) {\n                        revalidators.push(revalidate);\n                    } else {\n                        await revalidate();\n                    }\n                } else {\n                    data[i] = pageData;\n                }\n                if (!parallel) {\n                    previousPageData = pageData;\n                }\n            }\n            // flush all revalidateions in parallel\n            if (parallel) {\n                await Promise.all(revalidators.map((r)=>r()));\n            }\n            // once we executed the data fetching based on the context, clear the context\n            set({\n                _i: swr_internal__WEBPACK_IMPORTED_MODULE_3__.UNDEFINED\n            });\n            // return the data\n            return data;\n        }, config);\n        const mutate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(// eslint-disable-next-line func-names\n        function(data, opts) {\n            // When passing as a boolean, it's explicitly used to disable/enable\n            // revalidation.\n            const options = typeof opts === 'boolean' ? {\n                revalidate: opts\n            } : opts || {};\n            // Default to true.\n            const shouldRevalidate = options.revalidate !== false;\n            // It is possible that the key is still falsy.\n            if (!infiniteKey) return EMPTY_PROMISE;\n            if (shouldRevalidate) {\n                if (!(0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(data)) {\n                    // We only revalidate the pages that are changed\n                    set({\n                        _i: false,\n                        _r: options.revalidate\n                    });\n                } else {\n                    // Calling `mutate()`, we revalidate all pages\n                    set({\n                        _i: true,\n                        _r: options.revalidate\n                    });\n                }\n            }\n            return arguments.length ? swr.mutate(data, {\n                ...options,\n                revalidate: shouldRevalidate\n            }) : swr.mutate();\n        }, // swr.mutate is always the same reference\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            infiniteKey,\n            cache$1\n        ]);\n        // Extend the SWR API\n        const setSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((arg)=>{\n            // It is possible that the key is still falsy.\n            if (!infiniteKey) return EMPTY_PROMISE;\n            const [, changeSize] = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.createCacheHelper)(cache$1, infiniteKey);\n            let size;\n            if ((0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isFunction)(arg)) {\n                size = arg(resolvePageSize());\n            } else if (typeof arg == 'number') {\n                size = arg;\n            }\n            if (typeof size != 'number') return EMPTY_PROMISE;\n            changeSize({\n                _l: size\n            });\n            lastPageSizeRef.current = size;\n            // Calculate the page data after the size change.\n            const data = [];\n            const [getInfiniteCache] = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.createCacheHelper)(cache$1, infiniteKey);\n            let previousPageData = null;\n            for(let i = 0; i < size; ++i){\n                const [pageKey] = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.serialize)(getKey(i, previousPageData));\n                const [getCache] = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.createCacheHelper)(cache$1, pageKey);\n                // Get the cached page data.\n                const pageData = pageKey ? getCache().data : swr_internal__WEBPACK_IMPORTED_MODULE_3__.UNDEFINED;\n                // Call `mutate` with infinte cache data if we can't get it from the page cache.\n                if ((0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.isUndefined)(pageData)) {\n                    return mutate(getInfiniteCache().data);\n                }\n                data.push(pageData);\n                previousPageData = pageData;\n            }\n            return mutate(data);\n        }, // exclude getKey from the dependencies, which isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            infiniteKey,\n            cache$1,\n            mutate,\n            resolvePageSize\n        ]);\n        // Use getter functions to avoid unnecessary re-renders caused by triggering\n        // all the getters of the returned swr object.\n        return {\n            size: resolvePageSize(),\n            setSize,\n            mutate,\n            get data () {\n                return swr.data;\n            },\n            get error () {\n                return swr.error;\n            },\n            get isValidating () {\n                return swr.isValidating;\n            },\n            get isLoading () {\n                return swr.isLoading;\n            }\n        };\n    };\nconst useSWRInfinite = (0,swr_internal__WEBPACK_IMPORTED_MODULE_3__.withMiddleware)(useSWR, infinite);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/swr/dist/infinite/index.mjs\n");

/***/ })

};
;