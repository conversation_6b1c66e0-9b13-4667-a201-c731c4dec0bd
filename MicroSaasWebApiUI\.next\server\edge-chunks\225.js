(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[225],{3089:(e,n,t)=>{e.exports={...t(9852)}},9852:(e,n,t)=>{Object.defineProperty(n,"__esModule",{value:!0});let{Decimal:i,objectEnumValues:r,makeStrictEnum:s,Public:o,getRuntime:u,skip:a}=t(6108),c={};n.Prisma=c,n.$Enums={},c.prismaVersion={client:"6.5.0",engine:"173f8d54f8d52e692c7e27e72a88314ec7aeff60"},c.PrismaClientKnownRequestError=()=>{let e=u().prettyName;throw Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.PrismaClientUnknownRequestError=()=>{let e=u().prettyName;throw Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.PrismaClientRustPanicError=()=>{let e=u().prettyName;throw Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.PrismaClientInitializationError=()=>{let e=u().prettyName;throw Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.PrismaClientValidationError=()=>{let e=u().prettyName;throw Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.Decimal=i,c.sql=()=>{let e=u().prettyName;throw Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.empty=()=>{let e=u().prettyName;throw Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.join=()=>{let e=u().prettyName;throw Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.raw=()=>{let e=u().prettyName;throw Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.validator=o.validator,c.getExtensionContext=()=>{let e=u().prettyName;throw Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.defineExtension=()=>{let e=u().prettyName;throw Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},c.DbNull=r.instances.DbNull,c.JsonNull=r.instances.JsonNull,c.AnyNull=r.instances.AnyNull,c.NullTypes={DbNull:r.classes.DbNull,JsonNull:r.classes.JsonNull,AnyNull:r.classes.AnyNull},n.Prisma.TransactionIsolationLevel=s({ReadUncommitted:"ReadUncommitted",ReadCommitted:"ReadCommitted",RepeatableRead:"RepeatableRead",Serializable:"Serializable"}),n.Prisma.SubscriptionScalarFieldEnum={id:"id",user_email:"user_email",sub_status:"sub_status",sub_type:"sub_type",createdAt:"createdAt",updatedAt:"updatedAt",last_stripe_cs_id:"last_stripe_cs_id",stripe_customer_id:"stripe_customer_id",sub_stripe_id:"sub_stripe_id",user_clerk_id:"user_clerk_id"},n.Prisma.ProjectScalarFieldEnum={id:"id",connection_id:"connection_id",webhook_id:"webhook_id",scenario_id:"scenario_id",assistant_id:"assistant_id",user_clerk_id:"user_clerk_id",type:"type",status:"status",webhookLink:"webhookLink",createdAt:"createdAt",updatedAt:"updatedAt"},n.Prisma.AudiencesScalarFieldEnum={id:"id",resend_id:"resend_id",name:"name"},n.Prisma.SortOrder={asc:"asc",desc:"desc"},n.Prisma.QueryMode={default:"default",insensitive:"insensitive"},n.Prisma.NullsOrder={first:"first",last:"last"},n.SubscriptionStatus=n.$Enums.SubscriptionStatus={active:"active",inactive:"inactive"},n.Prisma.ModelName={Subscription:"Subscription",Project:"Project",Audiences:"Audiences"};class l{constructor(){return new Proxy(this,{get(e,n){let t=u();throw Error((t.isEdge?`PrismaClient is not configured to run in ${t.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`:"PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `"+t.prettyName+"`).")+`
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`)}})}}n.PrismaClient=l,Object.assign(n,c)},7225:(e,n,t)=>{e.exports={...t(3089)}},6108:e=>{"use strict";var n=Object.defineProperty,t=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,r=Object.prototype.hasOwnProperty,s=(e,t)=>{for(var i in t)n(e,i,{get:t[i],enumerable:!0})},o={};s(o,{Decimal:()=>e5,Public:()=>u,getRuntime:()=>S,makeStrictEnum:()=>v,objectEnumValues:()=>g}),e.exports=((e,s,o,u)=>{if(s&&"object"==typeof s||"function"==typeof s)for(let a of i(s))r.call(e,a)||a===o||n(e,a,{get:()=>s[a],enumerable:!(u=t(s,a))||u.enumerable});return e})(n({},"__esModule",{value:!0}),o);var u={};function a(...e){return e=>e}s(u,{validator:()=>a});var c=Symbol(),l=new WeakMap,h=class{constructor(e){e===c?l.set(this,"Prisma.".concat(this._getName())):l.set(this,"new Prisma.".concat(this._getNamespace(),".").concat(this._getName(),"()"))}_getName(){return this.constructor.name}toString(){return l.get(this)}},d=class extends h{_getNamespace(){return"NullTypes"}},f=class extends d{};w(f,"DbNull");var p=class extends d{};w(p,"JsonNull");var m=class extends d{};w(m,"AnyNull");var g={classes:{DbNull:f,JsonNull:p,AnyNull:m},instances:{DbNull:new f(c),JsonNull:new p(c),AnyNull:new m(c)}};function w(e,n){Object.defineProperty(e,"name",{value:n,configurable:!0})}var b=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function v(e){return new Proxy(e,{get(e,n){if(n in e)return e[n];if(!b.has(n))throw TypeError("Invalid enum value: ".concat(String(n)))}})}var N=()=>{var e,n;return(null==(n=null==(e=globalThis.process)?void 0:e.release)?void 0:n.name)==="node"},y=()=>{var e,n;return!!globalThis.Bun||!!(null!=(n=null==(e=globalThis.process)?void 0:e.versions)&&n.bun)},E=()=>!!globalThis.Deno,x=()=>"object"==typeof globalThis.Netlify,_=()=>"object"==typeof globalThis.EdgeRuntime,P=()=>{var e;return(null==(e=globalThis.navigator)?void 0:e.userAgent)==="Cloudflare-Workers"},M={node:"Node.js",workerd:"Cloudflare Workers",deno:"Deno and Deno Deploy",netlify:"Netlify Edge Functions","edge-light":"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)"};function S(){var e;let n=null!=(e=[[x,"netlify"],[_,"edge-light"],[P,"workerd"],[E,"deno"],[y,"bun"],[N,"node"]].flatMap(e=>e[0]()?[e[1]]:[]).at(0))?e:"";return{id:n,prettyName:M[n]||n,isEdge:["workerd","deno","netlify","edge-light"].includes(n)}}var A,O,R="0123456789abcdef",q="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",k="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",D={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-9e15,maxE:9e15,crypto:!1},C=!0,I="[DecimalError] ",T=I+"Invalid argument: ",F=I+"Precision limit exceeded",j=I+"crypto unavailable",Z="[object Decimal]",$=Math.floor,U=Math.pow,L=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,V=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,H=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,J=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,B=q.length-1,W=k.length-1,z={toStringTag:Z};function K(e){var n,t,i,r=e.length-1,s="",o=e[0];if(r>0){for(s+=o,n=1;n<r;n++)(t=7-(i=e[n]+"").length)&&(s+=eo(t)),s+=i;(t=7-(i=(o=e[n])+"").length)&&(s+=eo(t))}else if(0===o)return"0";for(;o%10==0;)o/=10;return s+o}function Q(e,n,t){if(e!==~~e||e<n||e>t)throw Error(T+e)}function G(e,n,t,i){var r,s,o,u;for(s=e[0];s>=10;s/=10)--n;return--n<0?(n+=7,r=0):(r=Math.ceil((n+1)/7),n%=7),s=U(10,7-n),u=e[r]%s|0,null==i?n<3?(0==n?u=u/100|0:1==n&&(u=u/10|0),o=t<4&&99999==u||t>3&&49999==u||5e4==u||0==u):o=(t<4&&u+1==s||t>3&&u+1==s/2)&&(e[r+1]/s/100|0)==U(10,n-2)-1||(u==s/2||0==u)&&(e[r+1]/s/100|0)==0:n<4?(0==n?u=u/1e3|0:1==n?u=u/100|0:2==n&&(u=u/10|0),o=(i||t<4)&&9999==u||!i&&t>3&&4999==u):o=((i||t<4)&&u+1==s||!i&&t>3&&u+1==s/2)&&(e[r+1]/s/1e3|0)==U(10,n-3)-1,o}function X(e,n,t){for(var i,r,s=[0],o=0,u=e.length;o<u;){for(r=s.length;r--;)s[r]*=n;for(s[0]+=R.indexOf(e.charAt(o++)),i=0;i<s.length;i++)s[i]>t-1&&(void 0===s[i+1]&&(s[i+1]=0),s[i+1]+=s[i]/t|0,s[i]%=t)}return s.reverse()}z.absoluteValue=z.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),ee(e)},z.ceil=function(){return ee(new this.constructor(this),this.e+1,2)},z.clampedTo=z.clamp=function(e,n){var t=this.constructor;if(e=new t(e),n=new t(n),!e.s||!n.s)return new t(NaN);if(e.gt(n))throw Error(T+n);return 0>this.cmp(e)?e:this.cmp(n)>0?n:new t(this)},z.comparedTo=z.cmp=function(e){var n,t,i,r,s=this.d,o=(e=new this.constructor(e)).d,u=this.s,a=e.s;if(!s||!o)return u&&a?u!==a?u:s===o?0:!s^u<0?1:-1:NaN;if(!s[0]||!o[0])return s[0]?u:o[0]?-a:0;if(u!==a)return u;if(this.e!==e.e)return this.e>e.e^u<0?1:-1;for(i=s.length,r=o.length,n=0,t=i<r?i:r;n<t;++n)if(s[n]!==o[n])return s[n]>o[n]^u<0?1:-1;return i===r?0:i>r^u<0?1:-1},z.cosine=z.cos=function(){var e,n,t=this,i=t.constructor;return t.d?t.d[0]?(e=i.precision,n=i.rounding,i.precision=e+Math.max(t.e,t.sd())+7,i.rounding=1,t=function(e,n){var t,i,r;if(n.isZero())return n;(i=n.d.length)<32?r=(1/em(4,t=Math.ceil(i/3))).toString():(t=16,r="2.3283064365386962890625e-10"),e.precision+=t,n=ep(e,1,n.times(r),new e(1));for(var s=t;s--;){var o=n.times(n);n=o.times(o).minus(o).times(8).plus(1)}return e.precision-=t,n}(i,eg(i,t)),i.precision=e,i.rounding=n,ee(2==O||3==O?t.neg():t,e,n,!0)):new i(1):new i(NaN)},z.cubeRoot=z.cbrt=function(){var e,n,t,i,r,s,o,u,a,c,l=this.constructor;if(!this.isFinite()||this.isZero())return new l(this);for(C=!1,(s=this.s*U(this.s*this,1/3))&&Math.abs(s)!=1/0?i=new l(s.toString()):(t=K(this.d),(s=((e=this.e)-t.length+1)%3)&&(t+=1==s||-2==s?"0":"00"),s=U(t,1/3),e=$((e+1)/3)-(e%3==(e<0?-1:2)),(i=new l(t=s==1/0?"5e"+e:(t=s.toExponential()).slice(0,t.indexOf("e")+1)+e)).s=this.s),o=(e=l.precision)+3;;)if(i=Y((c=(a=(u=i).times(u).times(u)).plus(this)).plus(this).times(u),c.plus(a),o+2,1),K(u.d).slice(0,o)===(t=K(i.d)).slice(0,o)){if("9999"!=(t=t.slice(o-3,o+1))&&(r||"4999"!=t)){+t&&(+t.slice(1)||"5"!=t.charAt(0))||(ee(i,e+1,1),n=!i.times(i).times(i).eq(this));break}if(!r&&(ee(u,e+1,0),u.times(u).times(u).eq(this))){i=u;break}o+=4,r=1}return C=!0,ee(i,e,l.rounding,n)},z.decimalPlaces=z.dp=function(){var e,n=this.d,t=NaN;if(n){if(t=((e=n.length-1)-$(this.e/7))*7,e=n[e])for(;e%10==0;e/=10)t--;t<0&&(t=0)}return t},z.dividedBy=z.div=function(e){return Y(this,new this.constructor(e))},z.dividedToIntegerBy=z.divToInt=function(e){var n=this.constructor;return ee(Y(this,new n(e),0,1,1),n.precision,n.rounding)},z.equals=z.eq=function(e){return 0===this.cmp(e)},z.floor=function(){return ee(new this.constructor(this),this.e+1,3)},z.greaterThan=z.gt=function(e){return this.cmp(e)>0},z.greaterThanOrEqualTo=z.gte=function(e){var n=this.cmp(e);return 1==n||0===n},z.hyperbolicCosine=z.cosh=function(){var e,n,t,i,r,s=this,o=s.constructor,u=new o(1);if(!s.isFinite())return new o(s.s?1/0:NaN);if(s.isZero())return u;t=o.precision,i=o.rounding,o.precision=t+Math.max(s.e,s.sd())+4,o.rounding=1,(r=s.d.length)<32?n=(1/em(4,e=Math.ceil(r/3))).toString():(e=16,n="2.3283064365386962890625e-10"),s=ep(o,1,s.times(n),new o(1),!0);for(var a,c=e,l=new o(8);c--;)a=s.times(s),s=u.minus(a.times(l.minus(a.times(l))));return ee(s,o.precision=t,o.rounding=i,!0)},z.hyperbolicSine=z.sinh=function(){var e,n,t,i,r=this,s=r.constructor;if(!r.isFinite()||r.isZero())return new s(r);if(n=s.precision,t=s.rounding,s.precision=n+Math.max(r.e,r.sd())+4,s.rounding=1,(i=r.d.length)<3)r=ep(s,2,r,r,!0);else{e=(e=1.4*Math.sqrt(i))>16?16:0|e,r=ep(s,2,r=r.times(1/em(5,e)),r,!0);for(var o,u=new s(5),a=new s(16),c=new s(20);e--;)o=r.times(r),r=r.times(u.plus(o.times(a.times(o).plus(c))))}return s.precision=n,s.rounding=t,ee(r,n,t,!0)},z.hyperbolicTangent=z.tanh=function(){var e,n,t=this.constructor;return this.isFinite()?this.isZero()?new t(this):(e=t.precision,n=t.rounding,t.precision=e+7,t.rounding=1,Y(this.sinh(),this.cosh(),t.precision=e,t.rounding=n)):new t(this.s)},z.inverseCosine=z.acos=function(){var e=this,n=e.constructor,t=e.abs().cmp(1),i=n.precision,r=n.rounding;return -1!==t?0===t?e.isNeg()?er(n,i,r):new n(0):new n(NaN):e.isZero()?er(n,i+4,r).times(.5):(n.precision=i+6,n.rounding=1,e=new n(1).minus(e).div(e.plus(1)).sqrt().atan(),n.precision=i,n.rounding=r,e.times(2))},z.inverseHyperbolicCosine=z.acosh=function(){var e,n,t=this,i=t.constructor;return t.lte(1)?new i(t.eq(1)?0:NaN):t.isFinite()?(e=i.precision,n=i.rounding,i.precision=e+Math.max(Math.abs(t.e),t.sd())+4,i.rounding=1,C=!1,t=t.times(t).minus(1).sqrt().plus(t),C=!0,i.precision=e,i.rounding=n,t.ln()):new i(t)},z.inverseHyperbolicSine=z.asinh=function(){var e,n,t=this,i=t.constructor;return!t.isFinite()||t.isZero()?new i(t):(e=i.precision,n=i.rounding,i.precision=e+2*Math.max(Math.abs(t.e),t.sd())+6,i.rounding=1,C=!1,t=t.times(t).plus(1).sqrt().plus(t),C=!0,i.precision=e,i.rounding=n,t.ln())},z.inverseHyperbolicTangent=z.atanh=function(){var e,n,t,i,r=this,s=r.constructor;return r.isFinite()?r.e>=0?new s(r.abs().eq(1)?r.s/0:r.isZero()?r:NaN):(e=s.precision,n=s.rounding,Math.max(i=r.sd(),e)<-(2*r.e)-1?ee(new s(r),e,n,!0):(s.precision=t=i-r.e,r=Y(r.plus(1),new s(1).minus(r),t+e,1),s.precision=e+4,s.rounding=1,r=r.ln(),s.precision=e,s.rounding=n,r.times(.5))):new s(NaN)},z.inverseSine=z.asin=function(){var e,n,t,i,r=this,s=r.constructor;return r.isZero()?new s(r):(n=r.abs().cmp(1),t=s.precision,i=s.rounding,-1!==n?0===n?((e=er(s,t+4,i).times(.5)).s=r.s,e):new s(NaN):(s.precision=t+6,s.rounding=1,r=r.div(new s(1).minus(r.times(r)).sqrt().plus(1)).atan(),s.precision=t,s.rounding=i,r.times(2)))},z.inverseTangent=z.atan=function(){var e,n,t,i,r,s,o,u,a,c=this,l=c.constructor,h=l.precision,d=l.rounding;if(c.isFinite()){if(c.isZero())return new l(c);if(c.abs().eq(1)&&h+4<=W)return(o=er(l,h+4,d).times(.25)).s=c.s,o}else{if(!c.s)return new l(NaN);if(h+4<=W)return(o=er(l,h+4,d).times(.5)).s=c.s,o}for(l.precision=u=h+10,l.rounding=1,e=t=Math.min(28,u/7+2|0);e;--e)c=c.div(c.times(c).plus(1).sqrt().plus(1));for(C=!1,n=Math.ceil(u/7),i=1,a=c.times(c),o=new l(c),r=c;-1!==e;)if(r=r.times(a),s=o.minus(r.div(i+=2)),r=r.times(a),void 0!==(o=s.plus(r.div(i+=2))).d[n])for(e=n;o.d[e]===s.d[e]&&e--;);return t&&(o=o.times(2<<t-1)),C=!0,ee(o,l.precision=h,l.rounding=d,!0)},z.isFinite=function(){return!!this.d},z.isInteger=z.isInt=function(){return!!this.d&&$(this.e/7)>this.d.length-2},z.isNaN=function(){return!this.s},z.isNegative=z.isNeg=function(){return this.s<0},z.isPositive=z.isPos=function(){return this.s>0},z.isZero=function(){return!!this.d&&0===this.d[0]},z.lessThan=z.lt=function(e){return 0>this.cmp(e)},z.lessThanOrEqualTo=z.lte=function(e){return 1>this.cmp(e)},z.logarithm=z.log=function(e){var n,t,i,r,s,o,u,a=this.constructor,c=a.precision,l=a.rounding;if(null==e)e=new a(10),n=!0;else{if(t=(e=new a(e)).d,e.s<0||!t||!t[0]||e.eq(1))return new a(NaN);n=e.eq(10)}if(t=this.d,this.s<0||!t||!t[0]||this.eq(1))return new a(t&&!t[0]?-1/0:1!=this.s?NaN:t?0:1/0);if(n){if(t.length>1)r=!0;else{for(i=t[0];i%10==0;)i/=10;r=1!==i}}if(C=!1,G((u=Y(eh(this,o=c+5),n?ei(a,o+10):eh(e,o),o,1)).d,i=c,l))do if(o+=10,u=Y(eh(this,o),n?ei(a,o+10):eh(e,o),o,1),!r){+K(u.d).slice(i+1,i+15)+1==1e14&&(u=ee(u,c+1,0));break}while(G(u.d,i+=10,l));return C=!0,ee(u,c,l)},z.minus=z.sub=function(e){var n,t,i,r,s,o,u,a,c,l,h,d,f=this.constructor;if(e=new f(e),!this.d||!e.d)return this.s&&e.s?this.d?e.s=-e.s:e=new f(e.d||this.s!==e.s?this:NaN):e=new f(NaN),e;if(this.s!=e.s)return e.s=-e.s,this.plus(e);if(c=this.d,d=e.d,u=f.precision,a=f.rounding,!c[0]||!d[0]){if(d[0])e.s=-e.s;else{if(!c[0])return new f(3===a?-0:0);e=new f(this)}return C?ee(e,u,a):e}if(t=$(e.e/7),l=$(this.e/7),c=c.slice(),s=l-t){for((h=s<0)?(n=c,s=-s,o=d.length):(n=d,t=l,o=c.length),s>(i=Math.max(Math.ceil(u/7),o)+2)&&(s=i,n.length=1),n.reverse(),i=s;i--;)n.push(0);n.reverse()}else{for((h=(i=c.length)<(o=d.length))&&(o=i),i=0;i<o;i++)if(c[i]!=d[i]){h=c[i]<d[i];break}s=0}for(h&&(n=c,c=d,d=n,e.s=-e.s),o=c.length,i=d.length-o;i>0;--i)c[o++]=0;for(i=d.length;i>s;){if(c[--i]<d[i]){for(r=i;r&&0===c[--r];)c[r]=1e7-1;--c[r],c[i]+=1e7}c[i]-=d[i]}for(;0===c[--o];)c.pop();for(;0===c[0];c.shift())--t;return c[0]?(e.d=c,e.e=et(c,t),C?ee(e,u,a):e):new f(3===a?-0:0)},z.modulo=z.mod=function(e){var n,t=this.constructor;return e=new t(e),this.d&&e.s&&(!e.d||e.d[0])?e.d&&(!this.d||this.d[0])?(C=!1,9==t.modulo?(n=Y(this,e.abs(),0,3,1),n.s*=e.s):n=Y(this,e,0,t.modulo,1),n=n.times(e),C=!0,this.minus(n)):ee(new t(this),t.precision,t.rounding):new t(NaN)},z.naturalExponential=z.exp=function(){return el(this)},z.naturalLogarithm=z.ln=function(){return eh(this)},z.negated=z.neg=function(){var e=new this.constructor(this);return e.s=-e.s,ee(e)},z.plus=z.add=function(e){var n,t,i,r,s,o,u,a,c,l,h=this.constructor;if(e=new h(e),!this.d||!e.d)return this.s&&e.s?this.d||(e=new h(e.d||this.s===e.s?this:NaN)):e=new h(NaN),e;if(this.s!=e.s)return e.s=-e.s,this.minus(e);if(c=this.d,l=e.d,u=h.precision,a=h.rounding,!c[0]||!l[0])return l[0]||(e=new h(this)),C?ee(e,u,a):e;if(s=$(this.e/7),i=$(e.e/7),c=c.slice(),r=s-i){for(r<0?(t=c,r=-r,o=l.length):(t=l,i=s,o=c.length),r>(o=(s=Math.ceil(u/7))>o?s+1:o+1)&&(r=o,t.length=1),t.reverse();r--;)t.push(0);t.reverse()}for((o=c.length)-(r=l.length)<0&&(r=o,t=l,l=c,c=t),n=0;r;)n=(c[--r]=c[r]+l[r]+n)/1e7|0,c[r]%=1e7;for(n&&(c.unshift(n),++i),o=c.length;0==c[--o];)c.pop();return e.d=c,e.e=et(c,i),C?ee(e,u,a):e},z.precision=z.sd=function(e){var n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(T+e);return this.d?(n=es(this.d),e&&this.e+1>n&&(n=this.e+1)):n=NaN,n},z.round=function(){var e=this.constructor;return ee(new e(this),this.e+1,e.rounding)},z.sine=z.sin=function(){var e,n,t=this,i=t.constructor;return t.isFinite()?t.isZero()?new i(t):(e=i.precision,n=i.rounding,i.precision=e+Math.max(t.e,t.sd())+7,i.rounding=1,t=function(e,n){var t,i=n.d.length;if(i<3)return n.isZero()?n:ep(e,2,n,n);t=(t=1.4*Math.sqrt(i))>16?16:0|t,n=ep(e,2,n=n.times(1/em(5,t)),n);for(var r,s=new e(5),o=new e(16),u=new e(20);t--;)r=n.times(n),n=n.times(s.plus(r.times(o.times(r).minus(u))));return n}(i,eg(i,t)),i.precision=e,i.rounding=n,ee(O>2?t.neg():t,e,n,!0)):new i(NaN)},z.squareRoot=z.sqrt=function(){var e,n,t,i,r,s,o=this.d,u=this.e,a=this.s,c=this.constructor;if(1!==a||!o||!o[0])return new c(!a||a<0&&(!o||o[0])?NaN:o?this:1/0);for(C=!1,0==(a=Math.sqrt(+this))||a==1/0?(((n=K(o)).length+u)%2==0&&(n+="0"),a=Math.sqrt(n),u=$((u+1)/2)-(u<0||u%2),i=new c(n=a==1/0?"5e"+u:(n=a.toExponential()).slice(0,n.indexOf("e")+1)+u)):i=new c(a.toString()),t=(u=c.precision)+3;;)if(i=(s=i).plus(Y(this,s,t+2,1)).times(.5),K(s.d).slice(0,t)===(n=K(i.d)).slice(0,t)){if("9999"!=(n=n.slice(t-3,t+1))&&(r||"4999"!=n)){+n&&(+n.slice(1)||"5"!=n.charAt(0))||(ee(i,u+1,1),e=!i.times(i).eq(this));break}if(!r&&(ee(s,u+1,0),s.times(s).eq(this))){i=s;break}t+=4,r=1}return C=!0,ee(i,u,c.rounding,e)},z.tangent=z.tan=function(){var e,n,t=this,i=t.constructor;return t.isFinite()?t.isZero()?new i(t):(e=i.precision,n=i.rounding,i.precision=e+10,i.rounding=1,(t=t.sin()).s=1,t=Y(t,new i(1).minus(t.times(t)).sqrt(),e+10,0),i.precision=e,i.rounding=n,ee(2==O||4==O?t.neg():t,e,n,!0)):new i(NaN)},z.times=z.mul=function(e){var n,t,i,r,s,o,u,a,c,l=this.constructor,h=this.d,d=(e=new l(e)).d;if(e.s*=this.s,!h||!h[0]||!d||!d[0])return new l(e.s&&(!h||h[0]||d)&&(!d||d[0]||h)?h&&d?0*e.s:e.s/0:NaN);for(t=$(this.e/7)+$(e.e/7),(a=h.length)<(c=d.length)&&(s=h,h=d,d=s,o=a,a=c,c=o),s=[],i=o=a+c;i--;)s.push(0);for(i=c;--i>=0;){for(n=0,r=a+i;r>i;)u=s[r]+d[i]*h[r-i-1]+n,s[r--]=u%1e7|0,n=u/1e7|0;s[r]=(s[r]+n)%1e7|0}for(;!s[--o];)s.pop();return n?++t:s.shift(),e.d=s,e.e=et(s,t),C?ee(e,l.precision,l.rounding):e},z.toBinary=function(e,n){return ew(this,2,e,n)},z.toDecimalPlaces=z.toDP=function(e,n){var t=this,i=t.constructor;return t=new i(t),void 0===e?t:(Q(e,0,1e9),void 0===n?n=i.rounding:Q(n,0,8),ee(t,e+t.e+1,n))},z.toExponential=function(e,n){var t,i=this,r=i.constructor;return void 0===e?t=en(i,!0):(Q(e,0,1e9),void 0===n?n=r.rounding:Q(n,0,8),t=en(i=ee(new r(i),e+1,n),!0,e+1)),i.isNeg()&&!i.isZero()?"-"+t:t},z.toFixed=function(e,n){var t,i,r=this.constructor;return void 0===e?t=en(this):(Q(e,0,1e9),void 0===n?n=r.rounding:Q(n,0,8),t=en(i=ee(new r(this),e+this.e+1,n),!1,e+i.e+1)),this.isNeg()&&!this.isZero()?"-"+t:t},z.toFraction=function(e){var n,t,i,r,s,o,u,a,c,l,h,d,f=this.d,p=this.constructor;if(!f)return new p(this);if(c=t=new p(1),i=a=new p(0),o=(s=(n=new p(i)).e=es(f)-this.e-1)%7,n.d[0]=U(10,o<0?7+o:o),null==e)e=s>0?n:c;else{if(!(u=new p(e)).isInt()||u.lt(c))throw Error(T+u);e=u.gt(n)?s>0?n:c:u}for(C=!1,u=new p(K(f)),l=p.precision,p.precision=s=14*f.length;h=Y(u,n,0,1,1),1!=(r=t.plus(h.times(i))).cmp(e);)t=i,i=r,r=c,c=a.plus(h.times(r)),a=r,r=n,n=u.minus(h.times(r)),u=r;return r=Y(e.minus(t),i,0,1,1),a=a.plus(r.times(c)),t=t.plus(r.times(i)),a.s=c.s=this.s,d=1>Y(c,i,s,1).minus(this).abs().cmp(Y(a,t,s,1).minus(this).abs())?[c,i]:[a,t],p.precision=l,C=!0,d},z.toHexadecimal=z.toHex=function(e,n){return ew(this,16,e,n)},z.toNearest=function(e,n){var t=this,i=t.constructor;if(t=new i(t),null==e){if(!t.d)return t;e=new i(1),n=i.rounding}else{if(e=new i(e),void 0===n?n=i.rounding:Q(n,0,8),!t.d)return e.s?t:e;if(!e.d)return e.s&&(e.s=t.s),e}return e.d[0]?(C=!1,t=Y(t,e,0,n,1).times(e),C=!0,ee(t)):(e.s=t.s,t=e),t},z.toNumber=function(){return+this},z.toOctal=function(e,n){return ew(this,8,e,n)},z.toPower=z.pow=function(e){var n,t,i,r,s,o,u=this,a=u.constructor,c=+(e=new a(e));if(!u.d||!e.d||!u.d[0]||!e.d[0])return new a(U(+u,c));if((u=new a(u)).eq(1))return u;if(i=a.precision,s=a.rounding,e.eq(1))return ee(u,i,s);if((n=$(e.e/7))>=e.d.length-1&&(t=c<0?-c:c)<=9007199254740991)return r=eu(a,u,t,i),e.s<0?new a(1).div(r):ee(r,i,s);if((o=u.s)<0){if(n<e.d.length-1)return new a(NaN);if(1&e.d[n]||(o=1),0==u.e&&1==u.d[0]&&1==u.d.length)return u.s=o,u}return(n=0!=(t=U(+u,c))&&isFinite(t)?new a(t+"").e:$(c*(Math.log("0."+K(u.d))/Math.LN10+u.e+1)))>a.maxE+1||n<a.minE-1?new a(n>0?o/0:0):(C=!1,a.rounding=u.s=1,t=Math.min(12,(n+"").length),(r=el(e.times(eh(u,i+t)),i)).d&&G((r=ee(r,i+5,1)).d,i,s)&&(n=i+10,+K((r=ee(el(e.times(eh(u,n+t)),n),n+5,1)).d).slice(i+1,i+15)+1==1e14&&(r=ee(r,i+1,0))),r.s=o,C=!0,a.rounding=s,ee(r,i,s))},z.toPrecision=function(e,n){var t,i=this,r=i.constructor;return void 0===e?t=en(i,i.e<=r.toExpNeg||i.e>=r.toExpPos):(Q(e,1,1e9),void 0===n?n=r.rounding:Q(n,0,8),t=en(i=ee(new r(i),e,n),e<=i.e||i.e<=r.toExpNeg,e)),i.isNeg()&&!i.isZero()?"-"+t:t},z.toSignificantDigits=z.toSD=function(e,n){var t=this.constructor;return void 0===e?(e=t.precision,n=t.rounding):(Q(e,1,1e9),void 0===n?n=t.rounding:Q(n,0,8)),ee(new t(this),e,n)},z.toString=function(){var e=this.constructor,n=en(this,this.e<=e.toExpNeg||this.e>=e.toExpPos);return this.isNeg()&&!this.isZero()?"-"+n:n},z.truncated=z.trunc=function(){return ee(new this.constructor(this),this.e+1,1)},z.valueOf=z.toJSON=function(){var e=this.constructor,n=en(this,this.e<=e.toExpNeg||this.e>=e.toExpPos);return this.isNeg()?"-"+n:n};var Y=function(){function e(e,n,t){var i,r=0,s=e.length;for(e=e.slice();s--;)i=e[s]*n+r,e[s]=i%t|0,r=i/t|0;return r&&e.unshift(r),e}function n(e,n,t,i){var r,s;if(t!=i)s=t>i?1:-1;else for(r=s=0;r<t;r++)if(e[r]!=n[r]){s=e[r]>n[r]?1:-1;break}return s}function t(e,n,t,i){for(var r=0;t--;)e[t]-=r,r=e[t]<n[t]?1:0,e[t]=r*i+e[t]-n[t];for(;!e[0]&&e.length>1;)e.shift()}return function(i,r,s,o,u,a){var c,l,h,d,f,p,m,g,w,b,v,N,y,E,x,_,P,M,S,O,R=i.constructor,q=i.s==r.s?1:-1,k=i.d,D=r.d;if(!k||!k[0]||!D||!D[0])return new R(i.s&&r.s&&(k?!D||k[0]!=D[0]:D)?k&&0==k[0]||!D?0*q:q/0:NaN);for(a?(f=1,l=i.e-r.e):(a=1e7,f=7,l=$(i.e/f)-$(r.e/f)),S=D.length,P=k.length,b=(w=new R(q)).d=[],h=0;D[h]==(k[h]||0);h++);if(D[h]>(k[h]||0)&&l--,null==s?(E=s=R.precision,o=R.rounding):E=u?s+(i.e-r.e)+1:s,E<0)b.push(1),p=!0;else{if(E=E/f+2|0,h=0,1==S){for(d=0,D=D[0],E++;(h<P||d)&&E--;h++)x=d*a+(k[h]||0),b[h]=x/D|0,d=x%D|0;p=d||h<P}else{for((d=a/(D[0]+1)|0)>1&&(D=e(D,d,a),k=e(k,d,a),S=D.length,P=k.length),_=S,N=(v=k.slice(0,S)).length;N<S;)v[N++]=0;(O=D.slice()).unshift(0),M=D[0],D[1]>=a/2&&++M;do d=0,(c=n(D,v,S,N))<0?(y=v[0],S!=N&&(y=y*a+(v[1]||0)),(d=y/M|0)>1?(d>=a&&(d=a-1),g=(m=e(D,d,a)).length,N=v.length,1==(c=n(m,v,g,N))&&(d--,t(m,S<g?O:D,g,a))):(0==d&&(c=d=1),m=D.slice()),(g=m.length)<N&&m.unshift(0),t(v,m,N,a),-1==c&&(N=v.length,(c=n(D,v,S,N))<1&&(d++,t(v,S<N?O:D,N,a))),N=v.length):0===c&&(d++,v=[0]),b[h++]=d,c&&v[0]?v[N++]=k[_]||0:(v=[k[_]],N=1);while((_++<P||void 0!==v[0])&&E--);p=void 0!==v[0]}b[0]||b.shift()}if(1==f)w.e=l,A=p;else{for(h=1,d=b[0];d>=10;d/=10)h++;w.e=h+l*f-1,ee(w,u?s+w.e+1:s,o,p)}return w}}();function ee(e,n,t,i){var r,s,o,u,a,c,l,h,d,f=e.constructor;e:if(null!=n){if(!(h=e.d))return e;for(r=1,u=h[0];u>=10;u/=10)r++;if((s=n-r)<0)s+=7,o=n,a=(l=h[d=0])/U(10,r-o-1)%10|0;else if((d=Math.ceil((s+1)/7))>=(u=h.length)){if(i){for(;u++<=d;)h.push(0);l=a=0,r=1,s%=7,o=s-7+1}else break e}else{for(l=u=h[d],r=1;u>=10;u/=10)r++;s%=7,a=(o=s-7+r)<0?0:l/U(10,r-o-1)%10|0}if(i=i||n<0||void 0!==h[d+1]||(o<0?l:l%U(10,r-o-1)),c=t<4?(a||i)&&(0==t||t==(e.s<0?3:2)):a>5||5==a&&(4==t||i||6==t&&(s>0?o>0?l/U(10,r-o):0:h[d-1])%10&1||t==(e.s<0?8:7)),n<1||!h[0])return h.length=0,c?(n-=e.e+1,h[0]=U(10,(7-n%7)%7),e.e=-n||0):h[0]=e.e=0,e;if(0==s?(h.length=d,u=1,d--):(h.length=d+1,u=U(10,7-s),h[d]=o>0?(l/U(10,r-o)%U(10,o)|0)*u:0),c)for(;;)if(0==d){for(s=1,o=h[0];o>=10;o/=10)s++;for(o=h[0]+=u,u=1;o>=10;o/=10)u++;s!=u&&(e.e++,1e7==h[0]&&(h[0]=1));break}else{if(h[d]+=u,1e7!=h[d])break;h[d--]=0,u=1}for(s=h.length;0===h[--s];)h.pop()}return C&&(e.e>f.maxE?(e.d=null,e.e=NaN):e.e<f.minE&&(e.e=0,e.d=[0])),e}function en(e,n,t){if(!e.isFinite())return ed(e);var i,r=e.e,s=K(e.d),o=s.length;return n?(t&&(i=t-o)>0?s=s.charAt(0)+"."+s.slice(1)+eo(i):o>1&&(s=s.charAt(0)+"."+s.slice(1)),s=s+(e.e<0?"e":"e+")+e.e):r<0?(s="0."+eo(-r-1)+s,t&&(i=t-o)>0&&(s+=eo(i))):r>=o?(s+=eo(r+1-o),t&&(i=t-r-1)>0&&(s=s+"."+eo(i))):((i=r+1)<o&&(s=s.slice(0,i)+"."+s.slice(i)),t&&(i=t-o)>0&&(r+1===o&&(s+="."),s+=eo(i))),s}function et(e,n){var t=e[0];for(n*=7;t>=10;t/=10)n++;return n}function ei(e,n,t){if(n>B)throw C=!0,t&&(e.precision=t),Error(F);return ee(new e(q),n,1,!0)}function er(e,n,t){if(n>W)throw Error(F);return ee(new e(k),n,t,!0)}function es(e){var n=e.length-1,t=7*n+1;if(n=e[n]){for(;n%10==0;n/=10)t--;for(n=e[0];n>=10;n/=10)t++}return t}function eo(e){for(var n="";e--;)n+="0";return n}function eu(e,n,t,i){var r,s=new e(1),o=Math.ceil(i/7+4);for(C=!1;;){if(t%2&&eb((s=s.times(n)).d,o)&&(r=!0),0===(t=$(t/2))){t=s.d.length-1,r&&0===s.d[t]&&++s.d[t];break}eb((n=n.times(n)).d,o)}return C=!0,s}function ea(e){return 1&e.d[e.d.length-1]}function ec(e,n,t){for(var i,r,s=new e(n[0]),o=0;++o<n.length;){if(!(r=new e(n[o])).s){s=r;break}((i=s.cmp(r))===t||0===i&&s.s===t)&&(s=r)}return s}function el(e,n){var t,i,r,s,o,u,a,c=0,l=0,h=0,d=e.constructor,f=d.rounding,p=d.precision;if(!e.d||!e.d[0]||e.e>17)return new d(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(null==n?(C=!1,a=p):a=n,u=new d(.03125);e.e>-2;)e=e.times(u),h+=5;for(a+=i=Math.log(U(2,h))/Math.LN10*2+5|0,t=s=o=new d(1),d.precision=a;;){if(s=ee(s.times(e),a,1),t=t.times(++l),K((u=o.plus(Y(s,t,a,1))).d).slice(0,a)===K(o.d).slice(0,a)){for(r=h;r--;)o=ee(o.times(o),a,1);if(null!=n)return d.precision=p,o;if(!(c<3&&G(o.d,a-i,f,c)))return ee(o,d.precision=p,f,C=!0);d.precision=a+=10,t=s=u=new d(1),l=0,c++}o=u}}function eh(e,n){var t,i,r,s,o,u,a,c,l,h,d,f=1,p=e,m=p.d,g=p.constructor,w=g.rounding,b=g.precision;if(p.s<0||!m||!m[0]||!p.e&&1==m[0]&&1==m.length)return new g(m&&!m[0]?-1/0:1!=p.s?NaN:m?0:p);if(null==n?(C=!1,l=b):l=n,g.precision=l+=10,i=(t=K(m)).charAt(0),!(15e14>Math.abs(s=p.e)))return c=ei(g,l+2,b).times(s+""),p=eh(new g(i+"."+t.slice(1)),l-10).plus(c),g.precision=b,null==n?ee(p,b,w,C=!0):p;for(;i<7&&1!=i||1==i&&t.charAt(1)>3;)i=(t=K((p=p.times(e)).d)).charAt(0),f++;for(s=p.e,i>1?(p=new g("0."+t),s++):p=new g(i+"."+t.slice(1)),h=p,a=o=p=Y(p.minus(1),p.plus(1),l,1),d=ee(p.times(p),l,1),r=3;;){if(o=ee(o.times(d),l,1),K((c=a.plus(Y(o,new g(r),l,1))).d).slice(0,l)===K(a.d).slice(0,l)){if(a=a.times(2),0!==s&&(a=a.plus(ei(g,l+2,b).times(s+""))),a=Y(a,new g(f),l,1),null!=n)return g.precision=b,a;if(!G(a.d,l-10,w,u))return ee(a,g.precision=b,w,C=!0);g.precision=l+=10,c=o=p=Y(h.minus(1),h.plus(1),l,1),d=ee(p.times(p),l,1),r=u=1}a=c,r+=2}}function ed(e){return String(e.s*e.s/0)}function ef(e,n){var t,i,r;for((t=n.indexOf("."))>-1&&(n=n.replace(".","")),(i=n.search(/e/i))>0?(t<0&&(t=i),t+=+n.slice(i+1),n=n.substring(0,i)):t<0&&(t=n.length),i=0;48===n.charCodeAt(i);i++);for(r=n.length;48===n.charCodeAt(r-1);--r);if(n=n.slice(i,r)){if(r-=i,e.e=t=t-i-1,e.d=[],i=(t+1)%7,t<0&&(i+=7),i<r){for(i&&e.d.push(+n.slice(0,i)),r-=7;i<r;)e.d.push(+n.slice(i,i+=7));i=7-(n=n.slice(i)).length}else i-=r;for(;i--;)n+="0";e.d.push(+n),C&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function ep(e,n,t,i,r){var s,o,u,a,c=e.precision,l=Math.ceil(c/7);for(C=!1,a=t.times(t),u=new e(i);;){if(o=Y(u.times(a),new e(n++*n++),c,1),u=r?i.plus(o):i.minus(o),i=Y(o.times(a),new e(n++*n++),c,1),void 0!==(o=u.plus(i)).d[l]){for(s=l;o.d[s]===u.d[s]&&s--;);if(-1==s)break}s=u,u=i,i=o,o=s}return C=!0,o.d.length=l+1,o}function em(e,n){for(var t=e;--n;)t*=e;return t}function eg(e,n){var t,i=n.s<0,r=er(e,e.precision,1),s=r.times(.5);if((n=n.abs()).lte(s))return O=i?4:1,n;if((t=n.divToInt(r)).isZero())O=i?3:2;else{if((n=n.minus(t.times(r))).lte(s))return O=ea(t)?i?2:3:i?4:1,n;O=ea(t)?i?1:4:i?3:2}return n.minus(r).abs()}function ew(e,n,t,i){var r,s,o,u,a,c,l,h,d,f=e.constructor,p=void 0!==t;if(p?(Q(t,1,1e9),void 0===i?i=f.rounding:Q(i,0,8)):(t=f.precision,i=f.rounding),e.isFinite()){for(o=(l=en(e)).indexOf("."),p?(r=2,16==n?t=4*t-3:8==n&&(t=3*t-2)):r=n,o>=0&&(l=l.replace(".",""),(d=new f(1)).e=l.length-o,d.d=X(en(d),10,r),d.e=d.d.length),s=a=(h=X(l,10,r)).length;0==h[--a];)h.pop();if(h[0]){if(o<0?s--:((e=new f(e)).d=h,e.e=s,h=(e=Y(e,d,t,i,0,r)).d,s=e.e,c=A),o=h[t],u=r/2,c=c||void 0!==h[t+1],c=i<4?(void 0!==o||c)&&(0===i||i===(e.s<0?3:2)):o>u||o===u&&(4===i||c||6===i&&1&h[t-1]||i===(e.s<0?8:7)),h.length=t,c)for(;++h[--t]>r-1;)h[t]=0,t||(++s,h.unshift(1));for(a=h.length;!h[a-1];--a);for(o=0,l="";o<a;o++)l+=R.charAt(h[o]);if(p){if(a>1){if(16==n||8==n){for(o=16==n?4:3,--a;a%o;a++)l+="0";for(a=(h=X(l,r,n)).length;!h[a-1];--a);for(o=1,l="1.";o<a;o++)l+=R.charAt(h[o])}else l=l.charAt(0)+"."+l.slice(1)}l=l+(s<0?"p":"p+")+s}else if(s<0){for(;++s;)l="0"+l;l="0."+l}else if(++s>a)for(s-=a;s--;)l+="0";else s<a&&(l=l.slice(0,s)+"."+l.slice(s))}else l=p?"0p+0":"0";l=(16==n?"0x":2==n?"0b":8==n?"0o":"")+l}else l=ed(e);return e.s<0?"-"+l:l}function eb(e,n){if(e.length>n)return e.length=n,!0}function ev(e){return new this(e).abs()}function eN(e){return new this(e).acos()}function ey(e){return new this(e).acosh()}function eE(e,n){return new this(e).plus(n)}function ex(e){return new this(e).asin()}function e_(e){return new this(e).asinh()}function eP(e){return new this(e).atan()}function eM(e){return new this(e).atanh()}function eS(e,n){e=new this(e),n=new this(n);var t,i=this.precision,r=this.rounding,s=i+4;return e.s&&n.s?e.d||n.d?!n.d||e.isZero()?(t=n.s<0?er(this,i,r):new this(0)).s=e.s:!e.d||n.isZero()?(t=er(this,s,1).times(.5)).s=e.s:n.s<0?(this.precision=s,this.rounding=1,t=this.atan(Y(e,n,s,1)),n=er(this,s,1),this.precision=i,this.rounding=r,t=e.s<0?t.minus(n):t.plus(n)):t=this.atan(Y(e,n,s,1)):(t=er(this,s,1).times(n.s>0?.25:.75)).s=e.s:t=new this(NaN),t}function eA(e){return new this(e).cbrt()}function eO(e){return ee(e=new this(e),e.e+1,2)}function eR(e,n,t){return new this(e).clamp(n,t)}function eq(e){if(!e||"object"!=typeof e)throw Error(I+"Object expected");var n,t,i,r=!0===e.defaults,s=["precision",1,1e9,"rounding",0,8,"toExpNeg",-9e15,0,"toExpPos",0,9e15,"maxE",0,9e15,"minE",-9e15,0,"modulo",0,9];for(n=0;n<s.length;n+=3)if(t=s[n],r&&(this[t]=D[t]),void 0!==(i=e[t])){if($(i)===i&&i>=s[n+1]&&i<=s[n+2])this[t]=i;else throw Error(T+t+": "+i)}if(t="crypto",r&&(this[t]=D[t]),void 0!==(i=e[t])){if(!0===i||!1===i||0===i||1===i){if(i){if("u">typeof crypto&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[t]=!0;else throw Error(j)}else this[t]=!1}else throw Error(T+t+": "+i)}return this}function ek(e){return new this(e).cos()}function eD(e){return new this(e).cosh()}function eC(e,n){return new this(e).div(n)}function eI(e){return new this(e).exp()}function eT(e){return ee(e=new this(e),e.e+1,3)}function eF(){var e,n,t=new this(0);for(C=!1,e=0;e<arguments.length;)if(n=new this(arguments[e++]),n.d)t.d&&(t=t.plus(n.times(n)));else{if(n.s)return C=!0,new this(1/0);t=n}return C=!0,t.sqrt()}function ej(e){return e instanceof e4||e&&e.toStringTag===Z||!1}function eZ(e){return new this(e).ln()}function e$(e,n){return new this(e).log(n)}function eU(e){return new this(e).log(2)}function eL(e){return new this(e).log(10)}function eV(){return ec(this,arguments,-1)}function eH(){return ec(this,arguments,1)}function eJ(e,n){return new this(e).mod(n)}function eB(e,n){return new this(e).mul(n)}function eW(e,n){return new this(e).pow(n)}function ez(e){var n,t,i,r,s=0,o=new this(1),u=[];if(void 0===e?e=this.precision:Q(e,1,1e9),i=Math.ceil(e/7),this.crypto){if(crypto.getRandomValues)for(n=crypto.getRandomValues(new Uint32Array(i));s<i;)(r=n[s])>=429e7?n[s]=crypto.getRandomValues(new Uint32Array(1))[0]:u[s++]=r%1e7;else if(crypto.randomBytes){for(n=crypto.randomBytes(i*=4);s<i;)(r=n[s]+(n[s+1]<<8)+(n[s+2]<<16)+((127&n[s+3])<<24))>=214e7?crypto.randomBytes(4).copy(n,s):(u.push(r%1e7),s+=4);s=i/4}else throw Error(j)}else for(;s<i;)u[s++]=1e7*Math.random()|0;for(i=u[--s],e%=7,i&&e&&(r=U(10,7-e),u[s]=(i/r|0)*r);0===u[s];s--)u.pop();if(s<0)t=0,u=[0];else{for(t=-1;0===u[0];t-=7)u.shift();for(i=1,r=u[0];r>=10;r/=10)i++;i<7&&(t-=7-i)}return o.e=t,o.d=u,o}function eK(e){return ee(e=new this(e),e.e+1,this.rounding)}function eQ(e){return(e=new this(e)).d?e.d[0]?e.s:0*e.s:e.s||NaN}function eG(e){return new this(e).sin()}function eX(e){return new this(e).sinh()}function eY(e){return new this(e).sqrt()}function e0(e,n){return new this(e).sub(n)}function e1(){var e=0,n=arguments,t=new this(n[0]);for(C=!1;t.s&&++e<n.length;)t=t.plus(n[e]);return C=!0,ee(t,this.precision,this.rounding)}function e2(e){return new this(e).tan()}function e7(e){return new this(e).tanh()}function e9(e){return ee(e=new this(e),e.e+1,1)}z[Symbol.for("nodejs.util.inspect.custom")]=z.toString,z[Symbol.toStringTag]="Decimal";var e4=z.constructor=function e(n){var t,i,r;function s(e){var n,t,i;if(!(this instanceof s))return new s(e);if(this.constructor=s,ej(e)){this.s=e.s,C?!e.d||e.e>s.maxE?(this.e=NaN,this.d=null):e.e<s.minE?(this.e=0,this.d=[0]):(this.e=e.e,this.d=e.d.slice()):(this.e=e.e,this.d=e.d?e.d.slice():e.d);return}if("number"==(i=typeof e)){if(0===e){this.s=1/e<0?-1:1,this.e=0,this.d=[0];return}if(e<0?(e=-e,this.s=-1):this.s=1,e===~~e&&e<1e7){for(n=0,t=e;t>=10;t/=10)n++;C?n>s.maxE?(this.e=NaN,this.d=null):n<s.minE?(this.e=0,this.d=[0]):(this.e=n,this.d=[e]):(this.e=n,this.d=[e]);return}if(0*e!=0){e||(this.s=NaN),this.e=NaN,this.d=null;return}return ef(this,e.toString())}if("string"===i)return 45===(t=e.charCodeAt(0))?(e=e.slice(1),this.s=-1):(43===t&&(e=e.slice(1)),this.s=1),J.test(e)?ef(this,e):function(e,n){var t,i,r,s,o,u,a,c,l;if(n.indexOf("_")>-1){if(n=n.replace(/(\d)_(?=\d)/g,"$1"),J.test(n))return ef(e,n)}else if("Infinity"===n||"NaN"===n)return+n||(e.s=NaN),e.e=NaN,e.d=null,e;if(V.test(n))t=16,n=n.toLowerCase();else if(L.test(n))t=2;else if(H.test(n))t=8;else throw Error(T+n);for((s=n.search(/p/i))>0?(a=+n.slice(s+1),n=n.substring(2,s)):n=n.slice(2),o=(s=n.indexOf("."))>=0,i=e.constructor,o&&(s=(u=(n=n.replace(".","")).length)-s,r=eu(i,new i(t),s,2*s)),s=l=(c=X(n,t,1e7)).length-1;0===c[s];--s)c.pop();return s<0?new i(0*e.s):(e.e=et(c,l),e.d=c,C=!1,o&&(e=Y(e,r,4*u)),a&&(e=e.times(54>Math.abs(a)?U(2,a):e4.pow(2,a))),C=!0,e)}(this,e);if("bigint"===i)return e<0?(e=-e,this.s=-1):this.s=1,ef(this,e.toString());throw Error(T+e)}if(s.prototype=z,s.ROUND_UP=0,s.ROUND_DOWN=1,s.ROUND_CEIL=2,s.ROUND_FLOOR=3,s.ROUND_HALF_UP=4,s.ROUND_HALF_DOWN=5,s.ROUND_HALF_EVEN=6,s.ROUND_HALF_CEIL=7,s.ROUND_HALF_FLOOR=8,s.EUCLID=9,s.config=s.set=eq,s.clone=e,s.isDecimal=ej,s.abs=ev,s.acos=eN,s.acosh=ey,s.add=eE,s.asin=ex,s.asinh=e_,s.atan=eP,s.atanh=eM,s.atan2=eS,s.cbrt=eA,s.ceil=eO,s.clamp=eR,s.cos=ek,s.cosh=eD,s.div=eC,s.exp=eI,s.floor=eT,s.hypot=eF,s.ln=eZ,s.log=e$,s.log10=eL,s.log2=eU,s.max=eV,s.min=eH,s.mod=eJ,s.mul=eB,s.pow=eW,s.random=ez,s.round=eK,s.sign=eQ,s.sin=eG,s.sinh=eX,s.sqrt=eY,s.sub=e0,s.sum=e1,s.tan=e2,s.tanh=e7,s.trunc=e9,void 0===n&&(n={}),n&&!0!==n.defaults)for(r=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],t=0;t<r.length;)n.hasOwnProperty(i=r[t++])||(n[i]=this[i]);return s.config(n),s}(D);q=new e4(q),k=new e4(k);var e5=e4}}]);
//# sourceMappingURL=225.js.map