﻿namespace MicroSaasWebApi.Properties.PXW
{
    public static class Constants
    {
        #region General
        public static string English = "English";
        public static string French = "French";
        public static readonly string regexPattern = @"^[a-zA-Z0-9-_/]+$"; // Alphanumeric characters, including hyphens, underscores, and slashes
        #endregion
        #region AppSettings
        public static string Authorization = "Authorization";

        public static string AcceptLanguage = "Accept-Language";
        public static string Settings = "Settings";
        public static string Tenants = "Tenants";
        public static string Tenant = "";
        public static string Configurations = "Configurations";
        public static string Endpoints = "Endpoints";
        public static string AlphaCCOWorkflows = "AlphaCCOWorkflows";
        #endregion
        public static string Bearer = "Bearer";
        public static string AuthorizationHeaderPresent = "Authorization header present.";
        public static string BearerTokenSuccessful = "Bearer Token Request Handled successfully.";
        public static string AuthorizationHeaderNotPresent = "Authorization header not present.Passing through unprotected API.";
        public static string InvalidClientRequest = "Invalid Client in the request.";
        public static string Unit_Test_Project = "MicroSaasWebApi.Tests.Unit";
        public static string Integration_Test_Project = "MicroSaasWebApi.Tests.Integration";
        public static string B2C_MAGIC_LINK_POLICY = "B2C_1A_SIGNIN_WITH_MAGIC_LINK";

        #region Messages

        #endregion

        #region Request Body
        public static string api = "/api";
        public static string api_redirect = "/api-redirect";
        public static string RequestBodyText = "RequestBodyText";
        public static string RequestBody = "RequestBody";

        #endregion

        #region Tenants
        //public static string Tenant001PHW = "PHW";
        #endregion

        #region Controller
        public static string ControllerNames = "Names";
        #endregion

        #region Action
        public static string ActionGetNames = "GetNames";
        #endregion
    }
}
