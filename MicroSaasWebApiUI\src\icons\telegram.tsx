const Telegram = () => {
  return (
    <svg
      width="78"
      height="18"
      viewBox="0 0 78 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_649_189)">
        <path
          d="M9.00701 18.0152C13.9815 18.0152 18.014 13.9826 18.014 9.00817C18.014 4.03374 13.9815 0.00115967 9.00701 0.00115967C4.03258 0.00115967 0 4.03374 0 9.00817C0 13.9826 4.03258 18.0152 9.00701 18.0152Z"
          fill="url(#paint0_linear_649_189)"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M4.07069 8.91055C6.69423 7.76012 8.44793 7.01655 9.3318 6.65178C11.8291 5.61359 12.3482 5.4312 12.6849 5.41718C12.755 5.41718 12.9234 5.43121 13.0356 5.51538C13.1198 5.58553 13.1479 5.68374 13.1619 5.75389C13.1759 5.82403 13.1899 5.97836 13.1759 6.10463C13.0356 7.52162 12.4604 10.9869 12.1517 12.5723C12.0255 13.2457 11.7729 13.4702 11.5344 13.4982C11.0153 13.5403 10.6085 13.1475 10.1034 12.8248C9.31777 12.3057 8.86882 11.983 8.09719 11.478C7.21332 10.8887 7.78854 10.566 8.2936 10.0469C8.41987 9.90665 10.7348 7.81624 10.7768 7.61983C10.7768 7.59177 10.7909 7.50759 10.7348 7.4655C10.6786 7.42341 10.6085 7.43744 10.5524 7.45147C10.4682 7.4655 9.20553 8.30728 6.75035 9.96277C6.38558 10.2153 6.06289 10.3275 5.76827 10.3275C5.44559 10.3275 4.82829 10.1452 4.36531 9.99083C3.80413 9.80845 3.35518 9.71024 3.39727 9.40159C3.42533 9.24726 3.6498 9.07891 4.07069 8.91055Z"
          fill="white"
        />
      </g>
      <path
        d="M24.5284 5.90625V4.77273H31.2827V5.90625H28.5597V13.5H27.2472V5.90625H24.5284ZM34.5447 13.6321C33.8999 13.6321 33.3445 13.4943 32.8786 13.2188C32.4155 12.9403 32.0575 12.5497 31.8047 12.0469C31.5547 11.5412 31.4297 10.9489 31.4297 10.2699C31.4297 9.59943 31.5547 9.00852 31.8047 8.49716C32.0575 7.9858 32.4098 7.58665 32.8615 7.29972C33.3161 7.01278 33.8473 6.86932 34.4553 6.86932C34.8246 6.86932 35.1825 6.9304 35.5291 7.05256C35.8757 7.17472 36.1868 7.36648 36.4624 7.62784C36.7379 7.8892 36.9553 8.22869 37.1143 8.64631C37.2734 9.06108 37.353 9.56534 37.353 10.1591V10.6108H32.1499V9.65625H36.1044C36.1044 9.32102 36.0362 9.02415 35.8999 8.76562C35.7635 8.50426 35.5717 8.2983 35.3246 8.14773C35.0803 7.99716 34.7933 7.92188 34.4638 7.92188C34.1058 7.92188 33.7933 8.00994 33.5263 8.18608C33.2621 8.35937 33.0575 8.58665 32.9126 8.8679C32.7706 9.14631 32.6996 9.44886 32.6996 9.77557V10.5213C32.6996 10.9588 32.7763 11.331 32.9297 11.6378C33.0859 11.9446 33.3033 12.179 33.5817 12.3409C33.8601 12.5 34.1854 12.5795 34.5575 12.5795C34.799 12.5795 35.0192 12.5455 35.218 12.4773C35.4169 12.4062 35.5888 12.3011 35.7337 12.1619C35.8786 12.0227 35.9893 11.8509 36.0661 11.6463L37.272 11.8636C37.1754 12.2187 37.0021 12.5298 36.7521 12.7969C36.505 13.0611 36.1939 13.267 35.8189 13.4148C35.4467 13.5597 35.022 13.6321 34.5447 13.6321ZM40.0408 4.77273V13.5H38.7667V4.77273H40.0408ZM44.576 13.6321C43.9311 13.6321 43.3757 13.4943 42.9098 13.2188C42.4467 12.9403 42.0888 12.5497 41.8359 12.0469C41.5859 11.5412 41.4609 10.9489 41.4609 10.2699C41.4609 9.59943 41.5859 9.00852 41.8359 8.49716C42.0888 7.9858 42.4411 7.58665 42.8928 7.29972C43.3473 7.01278 43.8786 6.86932 44.4865 6.86932C44.8558 6.86932 45.2138 6.9304 45.5604 7.05256C45.907 7.17472 46.218 7.36648 46.4936 7.62784C46.7692 7.8892 46.9865 8.22869 47.1456 8.64631C47.3047 9.06108 47.3842 9.56534 47.3842 10.1591V10.6108H42.1811V9.65625H46.1357C46.1357 9.32102 46.0675 9.02415 45.9311 8.76562C45.7947 8.50426 45.603 8.2983 45.3558 8.14773C45.1115 7.99716 44.8246 7.92188 44.495 7.92188C44.1371 7.92188 43.8246 8.00994 43.5575 8.18608C43.2933 8.35937 43.0888 8.58665 42.9439 8.8679C42.8018 9.14631 42.7308 9.44886 42.7308 9.77557V10.5213C42.7308 10.9588 42.8075 11.331 42.9609 11.6378C43.1172 11.9446 43.3345 12.179 43.6129 12.3409C43.8913 12.5 44.2166 12.5795 44.5888 12.5795C44.8303 12.5795 45.0504 12.5455 45.2493 12.4773C45.4482 12.4062 45.62 12.3011 45.7649 12.1619C45.9098 12.0227 46.0206 11.8509 46.0973 11.6463L47.3033 11.8636C47.2067 12.2187 47.0334 12.5298 46.7834 12.7969C46.5362 13.0611 46.2251 13.267 45.8501 13.4148C45.478 13.5597 45.0533 13.6321 44.576 13.6321ZM51.5423 16.0909C51.0224 16.0909 50.5749 16.0227 50.1999 15.8864C49.8278 15.75 49.5238 15.5696 49.288 15.3452C49.0522 15.1207 48.8761 14.875 48.7596 14.608L49.8548 14.1562C49.9315 14.2812 50.0337 14.4134 50.1616 14.5526C50.2923 14.6946 50.4684 14.8153 50.69 14.9148C50.9144 15.0142 51.2028 15.0639 51.555 15.0639C52.038 15.0639 52.4371 14.946 52.7525 14.7102C53.0678 14.4773 53.2255 14.1051 53.2255 13.5938V12.3068H53.1445C53.0678 12.446 52.957 12.6009 52.8121 12.7713C52.6701 12.9418 52.4741 13.0895 52.2241 13.2145C51.9741 13.3395 51.6488 13.402 51.2482 13.402C50.7312 13.402 50.2653 13.2812 49.8505 13.0398C49.4386 12.7955 49.1119 12.4361 48.8704 11.9616C48.6317 11.4844 48.5124 10.8977 48.5124 10.2017C48.5124 9.50568 48.6303 8.90909 48.8661 8.41193C49.1048 7.91477 49.4315 7.53409 49.8462 7.26989C50.261 7.00284 50.7312 6.86932 51.2567 6.86932C51.663 6.86932 51.9911 6.9375 52.2411 7.07386C52.4911 7.20739 52.6857 7.36364 52.8249 7.54261C52.967 7.72159 53.0763 7.87926 53.1531 8.01562H53.2468V6.95455H54.4954V13.6449C54.4954 14.2074 54.3647 14.669 54.1033 15.0298C53.842 15.3906 53.4883 15.6577 53.0423 15.831C52.5991 16.0043 52.0991 16.0909 51.5423 16.0909ZM51.5295 12.3452C51.896 12.3452 52.2056 12.2599 52.4585 12.0895C52.7141 11.9162 52.9073 11.669 53.038 11.348C53.1715 11.0241 53.2383 10.6364 53.2383 10.1847C53.2383 9.74432 53.1729 9.35653 53.0423 9.02131C52.9116 8.68608 52.7198 8.42472 52.467 8.23722C52.2141 8.04687 51.9016 7.9517 51.5295 7.9517C51.146 7.9517 50.8263 8.05114 50.5707 8.25C50.315 8.44602 50.1218 8.71307 49.9911 9.05114C49.8633 9.3892 49.7994 9.76705 49.7994 10.1847C49.7994 10.6136 49.8647 10.9901 49.9954 11.3139C50.1261 11.6378 50.3192 11.8906 50.5749 12.0724C50.8335 12.2543 51.1516 12.3452 51.5295 12.3452ZM56.2042 13.5V6.95455H57.4357V7.99432H57.5039C57.6232 7.64205 57.8335 7.36506 58.1346 7.16335C58.4386 6.95881 58.7823 6.85653 59.1658 6.85653C59.2454 6.85653 59.3391 6.85937 59.4471 6.86506C59.5579 6.87074 59.6445 6.87784 59.707 6.88636V8.10511C59.6559 8.09091 59.565 8.07528 59.4343 8.05824C59.3036 8.03835 59.1729 8.02841 59.0423 8.02841C58.7411 8.02841 58.4727 8.09233 58.2369 8.22017C58.0039 8.34517 57.8192 8.51989 57.6829 8.74432C57.5465 8.96591 57.4783 9.21875 57.4783 9.50284V13.5H56.2042ZM62.6559 13.6449C62.2411 13.6449 61.8661 13.5682 61.5309 13.4148C61.1957 13.2585 60.93 13.0327 60.734 12.7372C60.5408 12.4418 60.4442 12.0795 60.4442 11.6506C60.4442 11.2812 60.5153 10.9773 60.6573 10.7386C60.7994 10.5 60.9911 10.3111 61.2326 10.1719C61.4741 10.0327 61.744 9.92756 62.0423 9.85653C62.3406 9.78551 62.6445 9.73153 62.9542 9.6946C63.3462 9.64915 63.6644 9.61222 63.9087 9.58381C64.1531 9.55256 64.3306 9.50284 64.4414 9.43466C64.5522 9.36648 64.6076 9.25568 64.6076 9.10227V9.07244C64.6076 8.70028 64.5025 8.41193 64.2923 8.20739C64.0849 8.00284 63.7752 7.90057 63.3633 7.90057C62.9343 7.90057 62.5962 7.99574 62.3491 8.18608C62.1048 8.37358 61.9357 8.58239 61.842 8.8125L60.6445 8.53977C60.7866 8.14205 60.994 7.82102 61.2667 7.5767C61.5423 7.32955 61.859 7.15057 62.217 7.03977C62.5749 6.92614 62.9513 6.86932 63.3462 6.86932C63.6076 6.86932 63.8846 6.90057 64.1772 6.96307C64.4727 7.02273 64.7482 7.13352 65.0039 7.29545C65.2624 7.45739 65.4741 7.68892 65.6388 7.99006C65.8036 8.28835 65.886 8.67614 65.886 9.15341V13.5H64.6417V12.6051H64.5906C64.5082 12.7699 64.3846 12.9318 64.2198 13.0909C64.055 13.25 63.8434 13.3821 63.5849 13.4872C63.3263 13.5923 63.0167 13.6449 62.6559 13.6449ZM62.9329 12.6222C63.2852 12.6222 63.5863 12.5526 63.8363 12.4134C64.0891 12.2741 64.2809 12.0923 64.4116 11.8679C64.5451 11.6406 64.6119 11.3977 64.6119 11.1392V10.2955C64.5664 10.3409 64.4783 10.3835 64.3477 10.4233C64.2198 10.4602 64.0735 10.4929 63.9087 10.5213C63.744 10.5469 63.5835 10.571 63.4272 10.5938C63.271 10.6136 63.1403 10.6307 63.0352 10.6449C62.788 10.6761 62.5621 10.7287 62.3576 10.8026C62.1559 10.8764 61.994 10.983 61.8718 11.1222C61.7525 11.2585 61.6928 11.4403 61.6928 11.6676C61.6928 11.983 61.8093 12.2216 62.0423 12.3835C62.2752 12.5426 62.5721 12.6222 62.9329 12.6222ZM67.5831 13.5V6.95455H68.8061V8.01989H68.8871C69.0234 7.65909 69.2464 7.37784 69.5561 7.17614C69.8658 6.97159 70.2365 6.86932 70.6683 6.86932C71.1058 6.86932 71.4723 6.97159 71.7678 7.17614C72.0661 7.38068 72.2862 7.66193 72.4283 8.01989H72.4964C72.6527 7.67045 72.9013 7.39205 73.2422 7.18466C73.5831 6.97443 73.9893 6.86932 74.4609 6.86932C75.0547 6.86932 75.5391 7.0554 75.9141 7.42756C76.2919 7.79972 76.4808 8.3608 76.4808 9.1108V13.5H75.2067V9.23011C75.2067 8.78693 75.0859 8.46591 74.8445 8.26705C74.603 8.06818 74.3146 7.96875 73.9794 7.96875C73.5646 7.96875 73.2422 8.09659 73.0121 8.35227C72.782 8.60511 72.6669 8.9304 72.6669 9.32812V13.5H71.397V9.14915C71.397 8.79403 71.2862 8.50852 71.0646 8.29261C70.843 8.0767 70.5547 7.96875 70.1996 7.96875C69.9581 7.96875 69.7351 8.03267 69.5305 8.16051C69.3288 8.28551 69.1655 8.46023 69.0405 8.68466C68.9183 8.90909 68.8572 9.16903 68.8572 9.46449V13.5H67.5831Z"
        fill="#010610"
      />
      <defs>
        <linearGradient
          id="paint0_linear_649_189"
          x1="0"
          y1="9.00165"
          x2="18.001"
          y2="9.00165"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2AABEE" />
          <stop offset="1" stopColor="#229ED9" />
        </linearGradient>
        <clipPath id="clip0_649_189">
          <rect width="18" height="18" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default Telegram;
