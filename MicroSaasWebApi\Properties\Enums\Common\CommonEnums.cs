namespace MicroSaasWebApi.Properties.Enums.Common
{
    /// <summary>
    /// User account status enumeration
    /// </summary>
    public enum UserStatus
    {
        Pending = 0,
        Active = 1,
        Inactive = 2,
        Suspended = 3,
        Deleted = 4
    }

    /// <summary>
    /// Subscription status enumeration
    /// </summary>
    public enum SubscriptionStatus
    {
        None = 0,
        Trialing = 1,
        Active = 2,
        PastDue = 3,
        Canceled = 4,
        Unpaid = 5,
        Incomplete = 6,
        IncompleteExpired = 7
    }

    /// <summary>
    /// Payment status enumeration
    /// </summary>
    public enum PaymentStatus
    {
        Pending = 0,
        Processing = 1,
        Succeeded = 2,
        Failed = 3,
        Canceled = 4,
        RequiresAction = 5,
        RequiresPaymentMethod = 6,
        RequiresConfirmation = 7
    }

    /// <summary>
    /// Plan type enumeration
    /// </summary>
    public enum PlanType
    {
        Free = 0,
        Basic = 1,
        Premium = 2,
        Enterprise = 3
    }

    /// <summary>
    /// User role enumeration
    /// </summary>
    public enum UserRole
    {
        User = 0,
        Admin = 1,
        SuperAdmin = 2
    }

    /// <summary>
    /// API response status enumeration
    /// </summary>
    public enum ApiResponseStatus
    {
        Success = 200,
        Created = 201,
        NoContent = 204,
        BadRequest = 400,
        Unauthorized = 401,
        Forbidden = 403,
        NotFound = 404,
        Conflict = 409,
        UnprocessableEntity = 422,
        InternalServerError = 500,
        ServiceUnavailable = 503
    }

    /// <summary>
    /// Log level enumeration
    /// </summary>
    public enum LogLevel
    {
        Trace = 0,
        Debug = 1,
        Information = 2,
        Warning = 3,
        Error = 4,
        Critical = 5
    }

    /// <summary>
    /// Email template type enumeration
    /// </summary>
    public enum EmailTemplateType
    {
        Welcome = 0,
        EmailVerification = 1,
        PasswordReset = 2,
        PaymentSuccess = 3,
        PaymentFailed = 4,
        SubscriptionCreated = 5,
        SubscriptionCanceled = 6,
        TrialExpiring = 7,
        TrialExpired = 8
    }

    /// <summary>
    /// Notification type enumeration
    /// </summary>
    public enum NotificationType
    {
        Info = 0,
        Success = 1,
        Warning = 2,
        Error = 3
    }
}
