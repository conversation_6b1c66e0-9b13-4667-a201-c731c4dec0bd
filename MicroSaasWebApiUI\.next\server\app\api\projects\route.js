(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[871],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},6195:e=>{"use strict";e.exports=require("node:buffer")},2480:()=>{},912:(e,r,t)=>{"use strict";t.r(r),t.d(r,{ComponentMod:()=>E,default:()=>P});var s={};t.r(s),t.d(s,{GET:()=>h,dynamic:()=>l,runtime:()=>f});var o={};t.r(o),t.d(o,{originalPathname:()=>m,patchFetch:()=>y,requestAsyncStorage:()=>k,routeModule:()=>j,serverHooks:()=>g,staticGenerationAsyncStorage:()=>_});var n=t(932),a=t(2561),u=t(4828),c=t(6631),i=t(6333),p=t(462),d=t(9985);let l="force-dynamic",f="edge";async function h(){try{let e=await (0,p.a)();if(!e)return d.xk.json({error:"Unauthorized"},{status:401});let r=await i.Z.project.findMany({where:{user_clerk_id:e.id}});if(!r||0===r.length)return d.xk.json({success:!0,projects:[],message:"No projects found"});return d.xk.json({success:!0,projects:r})}catch(e){return console.error("Connection error:",e),d.xk.json({error:e?.message||"An unexpected error occurred"},{status:500})}}let j=new a.AppRouteRouteModule({definition:{kind:u.x.APP_ROUTE,page:"/api/projects/route",pathname:"/api/projects",filename:"route",bundlePath:"app/api/projects/route"},resolvedPagePath:"C:\\Projects\\PersonalPortal\\src\\app\\api\\projects\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:k,staticGenerationAsyncStorage:_,serverHooks:g}=j,m="/api/projects/route";function y(){return(0,c.XH)({serverHooks:g,staticGenerationAsyncStorage:_})}let E=o,P=n.a.wrap(j)},462:(e,r,t)=>{"use strict";t.d(r,{a:()=>n});var s=t(5657),o=t(2436);async function n(){t(7185);let{userId:e}=(0,o.I)();return e?(0,s.N)().users.getUser(e):null}},6333:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=new(t(7225)).PrismaClient}},e=>{var r=r=>e(e.s=r);e.O(0,[520,288,225,436,657],()=>r(912));var t=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/projects/route"]=t}]);
//# sourceMappingURL=route.js.map