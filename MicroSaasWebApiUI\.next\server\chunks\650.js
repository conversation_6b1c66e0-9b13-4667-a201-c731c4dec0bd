exports.id=650,exports.ids=[650],exports.modules={18401:t=>{"use strict";var e=function(t){var e;return!!t&&"object"==typeof t&&"[object RegExp]"!==(e=Object.prototype.toString.call(t))&&"[object Date]"!==e&&t.$$typeof!==i},i="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function n(t,e){return!1!==e.clone&&e.isMergeableObject(t)?o(Array.isArray(t)?[]:{},t,e):t}function s(t,e,i){return t.concat(e).map(function(t){return n(t,i)})}function r(t){return Object.keys(t).concat(Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(t).filter(function(e){return Object.propertyIsEnumerable.call(t,e)}):[])}function a(t,e){try{return e in t}catch(t){return!1}}function o(t,i,_){(_=_||{}).arrayMerge=_.arrayMerge||s,_.isMergeableObject=_.isMergeableObject||e,_.cloneUnlessOtherwiseSpecified=n;var h,u,l=Array.isArray(i);return l!==Array.isArray(t)?n(i,_):l?_.arrayMerge(t,i,_):(u={},(h=_).isMergeableObject(t)&&r(t).forEach(function(e){u[e]=n(t[e],h)}),r(i).forEach(function(e){(!a(t,e)||Object.hasOwnProperty.call(t,e)&&Object.propertyIsEnumerable.call(t,e))&&(a(t,e)&&h.isMergeableObject(i[e])?u[e]=(function(t,e){if(!e.customMerge)return o;var i=e.customMerge(t);return"function"==typeof i?i:o})(e,h)(t[e],i[e],h):u[e]=n(i[e],h))}),u)}o.all=function(t,e){if(!Array.isArray(t))throw Error("first argument should be an array");return t.reduce(function(t,i){return o(t,i,e)},{})},t.exports=o},76468:(t,e,i)=>{"use strict";var n;void 0!==(n=(function(t,e,i){var n;return(n=function(e,i){return t.js_beautify(e,i)}).js=t.js_beautify,n.css=e.css_beautify,n.html=i.html_beautify,n.js_beautify=t.js_beautify,n.css_beautify=e.css_beautify,n.html_beautify=i.html_beautify,n}).apply(e,[i(1760),i(96271),i(13764)]))&&(t.exports=n)},96271:(t,e)=>{var i;!function(){n=[,,function(t){function e(t){this.__parent=t,this.__character_count=0,this.__indent_count=-1,this.__alignment_count=0,this.__wrap_point_index=0,this.__wrap_point_character_count=0,this.__wrap_point_indent_count=-1,this.__wrap_point_alignment_count=0,this.__items=[]}function i(t,e){this.__cache=[""],this.__indent_size=t.indent_size,this.__indent_string=t.indent_char,t.indent_with_tabs||(this.__indent_string=Array(t.indent_size+1).join(t.indent_char)),e=e||"",t.indent_level>0&&(e=Array(t.indent_level+1).join(this.__indent_string)),this.__base_string=e,this.__base_string_length=e.length}function n(t,n){this.__indent_cache=new i(t,n),this.raw=!1,this._end_with_newline=t.end_with_newline,this.indent_size=t.indent_size,this.wrap_line_length=t.wrap_line_length,this.indent_empty_lines=t.indent_empty_lines,this.__lines=[],this.previous_line=null,this.current_line=null,this.next_line=new e(this),this.space_before_token=!1,this.non_breaking_space=!1,this.previous_token_wrapped=!1,this.__add_outputline()}e.prototype.clone_empty=function(){var t=new e(this.__parent);return t.set_indent(this.__indent_count,this.__alignment_count),t},e.prototype.item=function(t){return t<0?this.__items[this.__items.length+t]:this.__items[t]},e.prototype.has_match=function(t){for(var e=this.__items.length-1;e>=0;e--)if(this.__items[e].match(t))return!0;return!1},e.prototype.set_indent=function(t,e){this.is_empty()&&(this.__indent_count=t||0,this.__alignment_count=e||0,this.__character_count=this.__parent.get_indent_size(this.__indent_count,this.__alignment_count))},e.prototype._set_wrap_point=function(){this.__parent.wrap_line_length&&(this.__wrap_point_index=this.__items.length,this.__wrap_point_character_count=this.__character_count,this.__wrap_point_indent_count=this.__parent.next_line.__indent_count,this.__wrap_point_alignment_count=this.__parent.next_line.__alignment_count)},e.prototype._should_wrap=function(){return this.__wrap_point_index&&this.__character_count>this.__parent.wrap_line_length&&this.__wrap_point_character_count>this.__parent.next_line.__character_count},e.prototype._allow_wrap=function(){if(this._should_wrap()){this.__parent.add_new_line();var t=this.__parent.current_line;return t.set_indent(this.__wrap_point_indent_count,this.__wrap_point_alignment_count),t.__items=this.__items.slice(this.__wrap_point_index),this.__items=this.__items.slice(0,this.__wrap_point_index),t.__character_count+=this.__character_count-this.__wrap_point_character_count,this.__character_count=this.__wrap_point_character_count," "===t.__items[0]&&(t.__items.splice(0,1),t.__character_count-=1),!0}return!1},e.prototype.is_empty=function(){return 0===this.__items.length},e.prototype.last=function(){return this.is_empty()?null:this.__items[this.__items.length-1]},e.prototype.push=function(t){this.__items.push(t);var e=t.lastIndexOf("\n");-1!==e?this.__character_count=t.length-e:this.__character_count+=t.length},e.prototype.pop=function(){var t=null;return this.is_empty()||(t=this.__items.pop(),this.__character_count-=t.length),t},e.prototype._remove_indent=function(){this.__indent_count>0&&(this.__indent_count-=1,this.__character_count-=this.__parent.indent_size)},e.prototype._remove_wrap_indent=function(){this.__wrap_point_indent_count>0&&(this.__wrap_point_indent_count-=1)},e.prototype.trim=function(){for(;" "===this.last();)this.__items.pop(),this.__character_count-=1},e.prototype.toString=function(){var t="";return this.is_empty()?this.__parent.indent_empty_lines&&(t=this.__parent.get_indent_string(this.__indent_count)):t=this.__parent.get_indent_string(this.__indent_count,this.__alignment_count)+this.__items.join(""),t},i.prototype.get_indent_size=function(t,e){var i=this.__base_string_length;return e=e||0,t<0&&(i=0),i+=t*this.__indent_size+e},i.prototype.get_indent_string=function(t,e){var i=this.__base_string;return t<0&&(t=0,i=""),e=(e||0)+t*this.__indent_size,this.__ensure_cache(e),i+=this.__cache[e]},i.prototype.__ensure_cache=function(t){for(;t>=this.__cache.length;)this.__add_column()},i.prototype.__add_column=function(){var t=this.__cache.length,e=0,i="";this.__indent_size&&t>=this.__indent_size&&(e=Math.floor(t/this.__indent_size),t-=e*this.__indent_size,i=Array(e+1).join(this.__indent_string)),t&&(i+=Array(t+1).join(" ")),this.__cache.push(i)},n.prototype.__add_outputline=function(){this.previous_line=this.current_line,this.current_line=this.next_line.clone_empty(),this.__lines.push(this.current_line)},n.prototype.get_line_number=function(){return this.__lines.length},n.prototype.get_indent_string=function(t,e){return this.__indent_cache.get_indent_string(t,e)},n.prototype.get_indent_size=function(t,e){return this.__indent_cache.get_indent_size(t,e)},n.prototype.is_empty=function(){return!this.previous_line&&this.current_line.is_empty()},n.prototype.add_new_line=function(t){return!(this.is_empty()||!t&&this.just_added_newline())&&(this.raw||this.__add_outputline(),!0)},n.prototype.get_code=function(t){this.trim(!0);var e=this.current_line.pop();e&&("\n"===e[e.length-1]&&(e=e.replace(/\n+$/g,"")),this.current_line.push(e)),this._end_with_newline&&this.__add_outputline();var i=this.__lines.join("\n");return"\n"!==t&&(i=i.replace(/[\n]/g,t)),i},n.prototype.set_wrap_point=function(){this.current_line._set_wrap_point()},n.prototype.set_indent=function(t,e){return(t=t||0,e=e||0,this.next_line.set_indent(t,e),this.__lines.length>1)?(this.current_line.set_indent(t,e),!0):(this.current_line.set_indent(),!1)},n.prototype.add_raw_token=function(t){for(var e=0;e<t.newlines;e++)this.__add_outputline();this.current_line.set_indent(-1),this.current_line.push(t.whitespace_before),this.current_line.push(t.text),this.space_before_token=!1,this.non_breaking_space=!1,this.previous_token_wrapped=!1},n.prototype.add_token=function(t){this.__add_space_before_token(),this.current_line.push(t),this.space_before_token=!1,this.non_breaking_space=!1,this.previous_token_wrapped=this.current_line._allow_wrap()},n.prototype.__add_space_before_token=function(){this.space_before_token&&!this.just_added_newline()&&(this.non_breaking_space||this.set_wrap_point(),this.current_line.push(" "))},n.prototype.remove_indent=function(t){for(var e=this.__lines.length;t<e;)this.__lines[t]._remove_indent(),t++;this.current_line._remove_wrap_indent()},n.prototype.trim=function(t){for(t=void 0!==t&&t,this.current_line.trim();t&&this.__lines.length>1&&this.current_line.is_empty();)this.__lines.pop(),this.current_line=this.__lines[this.__lines.length-1],this.current_line.trim();this.previous_line=this.__lines.length>1?this.__lines[this.__lines.length-2]:null},n.prototype.just_added_newline=function(){return this.current_line.is_empty()},n.prototype.just_added_blankline=function(){return this.is_empty()||this.current_line.is_empty()&&this.previous_line.is_empty()},n.prototype.ensure_empty_line_above=function(t,i){for(var n=this.__lines.length-2;n>=0;){var s=this.__lines[n];if(s.is_empty())break;if(0!==s.item(0).indexOf(t)&&s.item(-1)!==i){this.__lines.splice(n+1,0,new e(this)),this.previous_line=this.__lines[this.__lines.length-2];break}n--}},t.exports.Output=n},,,,function(t){function e(t,e){this.raw_options=i(t,e),this.disabled=this._get_boolean("disabled"),this.eol=this._get_characters("eol","auto"),this.end_with_newline=this._get_boolean("end_with_newline"),this.indent_size=this._get_number("indent_size",4),this.indent_char=this._get_characters("indent_char"," "),this.indent_level=this._get_number("indent_level"),this.preserve_newlines=this._get_boolean("preserve_newlines",!0),this.max_preserve_newlines=this._get_number("max_preserve_newlines",32786),this.preserve_newlines||(this.max_preserve_newlines=0),this.indent_with_tabs=this._get_boolean("indent_with_tabs","	"===this.indent_char),this.indent_with_tabs&&(this.indent_char="	",1===this.indent_size&&(this.indent_size=4)),this.wrap_line_length=this._get_number("wrap_line_length",this._get_number("max_char")),this.indent_empty_lines=this._get_boolean("indent_empty_lines"),this.templating=this._get_selection_list("templating",["auto","none","angular","django","erb","handlebars","php","smarty"],["auto"])}function i(t,e){var i,s={};for(i in t=n(t))i!==e&&(s[i]=t[i]);if(e&&t[e])for(i in t[e])s[i]=t[e][i];return s}function n(t){var e,i={};for(e in t)i[e.replace(/-/g,"_")]=t[e];return i}e.prototype._get_array=function(t,e){var i=this.raw_options[t],n=e||[];return"object"==typeof i?null!==i&&"function"==typeof i.concat&&(n=i.concat()):"string"==typeof i&&(n=i.split(/[^a-zA-Z0-9_\/\-]+/)),n},e.prototype._get_boolean=function(t,e){var i=this.raw_options[t];return void 0===i?!!e:!!i},e.prototype._get_characters=function(t,e){var i=this.raw_options[t],n=e||"";return"string"==typeof i&&(n=i.replace(/\\r/,"\r").replace(/\\n/,"\n").replace(/\\t/,"	")),n},e.prototype._get_number=function(t,e){var i=this.raw_options[t];isNaN(e=parseInt(e,10))&&(e=0);var n=parseInt(i,10);return isNaN(n)&&(n=e),n},e.prototype._get_selection=function(t,e,i){var n=this._get_selection_list(t,e,i);if(1!==n.length)throw Error("Invalid Option Value: The option '"+t+"' can only be one of the following values:\n"+e+"\nYou passed in: '"+this.raw_options[t]+"'");return n[0]},e.prototype._get_selection_list=function(t,e,i){if(!e||0===e.length)throw Error("Selection list cannot be empty.");if(i=i||[e[0]],!this._is_valid_selection(i,e))throw Error("Invalid Default Value!");var n=this._get_array(t,i);if(!this._is_valid_selection(n,e))throw Error("Invalid Option Value: The option '"+t+"' can contain only the following values:\n"+e+"\nYou passed in: '"+this.raw_options[t]+"'");return n},e.prototype._is_valid_selection=function(t,e){return t.length&&e.length&&!t.some(function(t){return -1===e.indexOf(t)})},t.exports.Options=e,t.exports.normalizeOpts=n,t.exports.mergeOpts=i},,function(t){var e=RegExp.prototype.hasOwnProperty("sticky");function i(t){this.__input=t||"",this.__input_length=this.__input.length,this.__position=0}i.prototype.restart=function(){this.__position=0},i.prototype.back=function(){this.__position>0&&(this.__position-=1)},i.prototype.hasNext=function(){return this.__position<this.__input_length},i.prototype.next=function(){var t=null;return this.hasNext()&&(t=this.__input.charAt(this.__position),this.__position+=1),t},i.prototype.peek=function(t){var e=null;return(t=(t||0)+this.__position)>=0&&t<this.__input_length&&(e=this.__input.charAt(t)),e},i.prototype.__match=function(t,i){t.lastIndex=i;var n=t.exec(this.__input);return n&&!(e&&t.sticky)&&n.index!==i&&(n=null),n},i.prototype.test=function(t,e){return(e=(e||0)+this.__position)>=0&&e<this.__input_length&&!!this.__match(t,e)},i.prototype.testChar=function(t,e){var i=this.peek(e);return t.lastIndex=0,null!==i&&t.test(i)},i.prototype.match=function(t){var e=this.__match(t,this.__position);return e?this.__position+=e[0].length:e=null,e},i.prototype.read=function(t,e,i){var n,s="";return t&&(n=this.match(t))&&(s+=n[0]),e&&(n||!t)&&(s+=this.readUntil(e,i)),s},i.prototype.readUntil=function(t,e){var i="",n=this.__position;t.lastIndex=this.__position;var s=t.exec(this.__input);return s?(n=s.index,e&&(n+=s[0].length)):n=this.__input_length,i=this.__input.substring(this.__position,n),this.__position=n,i},i.prototype.readUntilAfter=function(t){return this.readUntil(t,!0)},i.prototype.get_regexp=function(t,i){var n=null,s="g";return i&&e&&(s="y"),"string"==typeof t&&""!==t?n=new RegExp(t,s):t&&(n=new RegExp(t.source,s)),n},i.prototype.get_literal_regexp=function(t){return RegExp(t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&"))},i.prototype.peekUntilAfter=function(t){var e=this.__position,i=this.readUntilAfter(t);return this.__position=e,i},i.prototype.lookBack=function(t){var e=this.__position-1;return e>=t.length&&this.__input.substring(e-t.length,e).toLowerCase()===t},t.exports.InputScanner=i},,,,,function(t){function e(t,e){t="string"==typeof t?t:t.source,e="string"==typeof e?e:e.source,this.__directives_block_pattern=RegExp(t+/ beautify( \w+[:]\w+)+ /.source+e,"g"),this.__directive_pattern=/ (\w+)[:](\w+)/g,this.__directives_end_ignore_pattern=RegExp(t+/\sbeautify\signore:end\s/.source+e,"g")}e.prototype.get_directives=function(t){if(!t.match(this.__directives_block_pattern))return null;var e={};this.__directive_pattern.lastIndex=0;for(var i=this.__directive_pattern.exec(t);i;)e[i[1]]=i[2],i=this.__directive_pattern.exec(t);return e},e.prototype.readIgnored=function(t){return t.readUntilAfter(this.__directives_end_ignore_pattern)},t.exports.Directives=e},,function(t,e,i){var n=i(16).Beautifier,s=i(17).Options;t.exports=function(t,e){return new n(t,e).beautify()},t.exports.defaultOptions=function(){return new s}},function(t,e,i){var n=i(17).Options,s=i(2).Output,r=i(8).InputScanner,a=new(i(13)).Directives(/\/\*/,/\*\//),o=/\r\n|[\r\n]/,_=/\r\n|[\r\n]/g,h=/\s/,u=/(?:\s|\n)+/g,l=/\/\*(?:[\s\S]*?)((?:\*\/)|$)/g,p=/\/\/(?:[^\n\r\u2028\u2029]*)/g;function c(t,e){this._source_text=t||"",this._options=new n(e),this._ch=null,this._input=null,this.NESTED_AT_RULE={page:!0,"font-face":!0,keyframes:!0,media:!0,supports:!0,document:!0},this.CONDITIONAL_GROUP_RULE={media:!0,supports:!0,document:!0},this.NON_SEMICOLON_NEWLINE_PROPERTY=["grid-template-areas","grid-template"]}c.prototype.eatString=function(t){var e="";for(this._ch=this._input.next();this._ch;){if(e+=this._ch,"\\"===this._ch)e+=this._input.next();else if(-1!==t.indexOf(this._ch)||"\n"===this._ch)break;this._ch=this._input.next()}return e},c.prototype.eatWhitespace=function(t){for(var e=h.test(this._input.peek()),i=0;h.test(this._input.peek());)this._ch=this._input.next(),t&&"\n"===this._ch&&(0===i||i<this._options.max_preserve_newlines)&&(i++,this._output.add_new_line(!0));return e},c.prototype.foundNestedPseudoClass=function(){for(var t=0,e=1,i=this._input.peek(e);i;){if("{"===i)return!0;if("("===i)t+=1;else if(")"===i){if(0===t)return!1;t-=1}else if(";"===i||"}"===i)break;e++,i=this._input.peek(e)}return!1},c.prototype.print_string=function(t){this._output.set_indent(this._indentLevel),this._output.non_breaking_space=!0,this._output.add_token(t)},c.prototype.preserveSingleSpace=function(t){t&&(this._output.space_before_token=!0)},c.prototype.indent=function(){this._indentLevel++},c.prototype.outdent=function(){this._indentLevel>0&&this._indentLevel--},c.prototype.beautify=function(){if(this._options.disabled)return this._source_text;var t,e,i=this._source_text,n=this._options.eol;"auto"===n&&(n="\n",i&&o.test(i||"")&&(n=i.match(o)[0]));var c=(i=i.replace(_,"\n")).match(/^[\t ]*/)[0];this._output=new s(this._options,c),this._input=new r(i),this._indentLevel=0,this._nestedLevel=0,this._ch=null;for(var d=0,f=!1,g=!1,m=!1,b=!1,y=!1,x=this._ch,w=!1;;)if(t=""!==this._input.read(u),e=x,this._ch=this._input.next(),"\\"===this._ch&&this._input.hasNext()&&(this._ch+=this._input.next()),x=this._ch,this._ch){if("/"===this._ch&&"*"===this._input.peek()){this._output.add_new_line(),this._input.back();var k=this._input.read(l),v=a.get_directives(k);v&&"start"===v.ignore&&(k+=a.readIgnored(this._input)),this.print_string(k),this.eatWhitespace(!0),this._output.add_new_line()}else if("/"===this._ch&&"/"===this._input.peek())this._output.space_before_token=!0,this._input.back(),this.print_string(this._input.read(p)),this.eatWhitespace(!0);else if("$"===this._ch){this.preserveSingleSpace(t),this.print_string(this._ch);var E=this._input.peekUntilAfter(/[: ,;{}()[\]\/='"]/g);E.match(/[ :]$/)&&(E=this.eatString(": ").replace(/\s+$/,""),this.print_string(E),this._output.space_before_token=!0),0===d&&-1!==E.indexOf(":")&&(g=!0,this.indent())}else if("@"===this._ch){if(this.preserveSingleSpace(t),"{"===this._input.peek())this.print_string(this._ch+this.eatString("}"));else{this.print_string(this._ch);var T=this._input.peekUntilAfter(/[: ,;{}()[\]\/='"]/g);T.match(/[ :]$/)&&(T=this.eatString(": ").replace(/\s+$/,""),this.print_string(T),this._output.space_before_token=!0),0===d&&-1!==T.indexOf(":")?(g=!0,this.indent()):T in this.NESTED_AT_RULE?(this._nestedLevel+=1,T in this.CONDITIONAL_GROUP_RULE&&(m=!0)):0!==d||g||(b=!0)}}else if("#"===this._ch&&"{"===this._input.peek())this.preserveSingleSpace(t),this.print_string(this._ch+this.eatString("}"));else if("{"===this._ch)g&&(g=!1,this.outdent()),b=!1,m?(m=!1,f=this._indentLevel>=this._nestedLevel):f=this._indentLevel>=this._nestedLevel-1,this._options.newline_between_rules&&f&&this._output.previous_line&&"{"!==this._output.previous_line.item(-1)&&this._output.ensure_empty_line_above("/",","),this._output.space_before_token=!0,"expand"===this._options.brace_style?(this._output.add_new_line(),this.print_string(this._ch),this.indent(),this._output.set_indent(this._indentLevel)):("("===e?this._output.space_before_token=!1:","!==e&&this.indent(),this.print_string(this._ch)),this.eatWhitespace(!0),this._output.add_new_line();else if("}"===this._ch)this.outdent(),this._output.add_new_line(),"{"===e&&this._output.trim(!0),g&&(this.outdent(),g=!1),this.print_string(this._ch),f=!1,this._nestedLevel&&this._nestedLevel--,this.eatWhitespace(!0),this._output.add_new_line(),this._options.newline_between_rules&&!this._output.just_added_blankline()&&"}"!==this._input.peek()&&this._output.add_new_line(!0),")"===this._input.peek()&&(this._output.trim(!0),"expand"===this._options.brace_style&&this._output.add_new_line(!0));else if(":"===this._ch){for(var S=0;S<this.NON_SEMICOLON_NEWLINE_PROPERTY.length;S++)if(this._input.lookBack(this.NON_SEMICOLON_NEWLINE_PROPERTY[S])){w=!0;break}!f&&!m||this._input.lookBack("&")||this.foundNestedPseudoClass()||this._input.lookBack("(")||b||0!==d?(this._input.lookBack(" ")&&(this._output.space_before_token=!0),":"===this._input.peek()?(this._ch=this._input.next(),this.print_string("::")):this.print_string(":")):(this.print_string(":"),g||(g=!0,this._output.space_before_token=!0,this.eatWhitespace(!0),this.indent()))}else if('"'===this._ch||"'"===this._ch){var L='"'===e||"'"===e;this.preserveSingleSpace(L||t),this.print_string(this._ch+this.eatString(this._ch)),this.eatWhitespace(!0)}else if(";"===this._ch)w=!1,0===d?(g&&(this.outdent(),g=!1),b=!1,this.print_string(this._ch),this.eatWhitespace(!0),"/"!==this._input.peek()&&this._output.add_new_line()):(this.print_string(this._ch),this.eatWhitespace(!0),this._output.space_before_token=!0);else if("("===this._ch){if(this._input.lookBack("url"))this.print_string(this._ch),this.eatWhitespace(),d++,this.indent(),this._ch=this._input.next(),")"===this._ch||'"'===this._ch||"'"===this._ch?this._input.back():this._ch&&(this.print_string(this._ch+this.eatString(")")),d&&(d--,this.outdent()));else{var A=!1;this._input.lookBack("with")&&(A=!0),this.preserveSingleSpace(t||A),this.print_string(this._ch),g&&"$"===e&&this._options.selector_separator_newline?(this._output.add_new_line(),y=!0):(this.eatWhitespace(),d++,this.indent())}}else if(")"===this._ch)d&&(d--,this.outdent()),y&&";"===this._input.peek()&&this._options.selector_separator_newline&&(y=!1,this.outdent(),this._output.add_new_line()),this.print_string(this._ch);else if(","===this._ch)this.print_string(this._ch),this.eatWhitespace(!0),this._options.selector_separator_newline&&(!g||y)&&0===d&&!b?this._output.add_new_line():this._output.space_before_token=!0;else if(">"!==this._ch&&"+"!==this._ch&&"~"!==this._ch||g||0!==d){if("]"===this._ch)this.print_string(this._ch);else if("["===this._ch)this.preserveSingleSpace(t),this.print_string(this._ch);else if("="===this._ch)this.eatWhitespace(),this.print_string("="),h.test(this._ch)&&(this._ch="");else if("!"!==this._ch||this._input.lookBack("\\")){var N='"'===e||"'"===e;this.preserveSingleSpace(N||t),this.print_string(this._ch),!this._output.just_added_newline()&&"\n"===this._input.peek()&&w&&this._output.add_new_line()}else this._output.space_before_token=!0,this.print_string(this._ch)}else this._options.space_around_combinator?(this._output.space_before_token=!0,this.print_string(this._ch),this._output.space_before_token=!0):(this.print_string(this._ch),this.eatWhitespace(),this._ch&&h.test(this._ch)&&(this._ch=""))}else break;return this._output.get_code(n)},t.exports.Beautifier=c},function(t,e,i){var n=i(6).Options;function s(t){n.call(this,t,"css"),this.selector_separator_newline=this._get_boolean("selector_separator_newline",!0),this.newline_between_rules=this._get_boolean("newline_between_rules",!0);var e=this._get_boolean("space_around_selector_separator");this.space_around_combinator=this._get_boolean("space_around_combinator")||e;var i=this._get_selection_list("brace_style",["collapse","expand","end-expand","none","preserve-inline"]);this.brace_style="collapse";for(var s=0;s<i.length;s++)"expand"!==i[s]?this.brace_style="collapse":this.brace_style=i[s]}s.prototype=new n,t.exports.Options=s}],s={};var n,s,r=function t(e){var i=s[e];if(void 0!==i)return i.exports;var r=s[e]={exports:{}};return n[e](r,r.exports,t),r.exports}(15);void 0!==(i=(function(){return{css_beautify:r}}).apply(e,[]))&&(t.exports=i)}()},13764:(t,e,i)=>{var n;!function(){s=[,,function(t){function e(t){this.__parent=t,this.__character_count=0,this.__indent_count=-1,this.__alignment_count=0,this.__wrap_point_index=0,this.__wrap_point_character_count=0,this.__wrap_point_indent_count=-1,this.__wrap_point_alignment_count=0,this.__items=[]}function i(t,e){this.__cache=[""],this.__indent_size=t.indent_size,this.__indent_string=t.indent_char,t.indent_with_tabs||(this.__indent_string=Array(t.indent_size+1).join(t.indent_char)),e=e||"",t.indent_level>0&&(e=Array(t.indent_level+1).join(this.__indent_string)),this.__base_string=e,this.__base_string_length=e.length}function n(t,n){this.__indent_cache=new i(t,n),this.raw=!1,this._end_with_newline=t.end_with_newline,this.indent_size=t.indent_size,this.wrap_line_length=t.wrap_line_length,this.indent_empty_lines=t.indent_empty_lines,this.__lines=[],this.previous_line=null,this.current_line=null,this.next_line=new e(this),this.space_before_token=!1,this.non_breaking_space=!1,this.previous_token_wrapped=!1,this.__add_outputline()}e.prototype.clone_empty=function(){var t=new e(this.__parent);return t.set_indent(this.__indent_count,this.__alignment_count),t},e.prototype.item=function(t){return t<0?this.__items[this.__items.length+t]:this.__items[t]},e.prototype.has_match=function(t){for(var e=this.__items.length-1;e>=0;e--)if(this.__items[e].match(t))return!0;return!1},e.prototype.set_indent=function(t,e){this.is_empty()&&(this.__indent_count=t||0,this.__alignment_count=e||0,this.__character_count=this.__parent.get_indent_size(this.__indent_count,this.__alignment_count))},e.prototype._set_wrap_point=function(){this.__parent.wrap_line_length&&(this.__wrap_point_index=this.__items.length,this.__wrap_point_character_count=this.__character_count,this.__wrap_point_indent_count=this.__parent.next_line.__indent_count,this.__wrap_point_alignment_count=this.__parent.next_line.__alignment_count)},e.prototype._should_wrap=function(){return this.__wrap_point_index&&this.__character_count>this.__parent.wrap_line_length&&this.__wrap_point_character_count>this.__parent.next_line.__character_count},e.prototype._allow_wrap=function(){if(this._should_wrap()){this.__parent.add_new_line();var t=this.__parent.current_line;return t.set_indent(this.__wrap_point_indent_count,this.__wrap_point_alignment_count),t.__items=this.__items.slice(this.__wrap_point_index),this.__items=this.__items.slice(0,this.__wrap_point_index),t.__character_count+=this.__character_count-this.__wrap_point_character_count,this.__character_count=this.__wrap_point_character_count," "===t.__items[0]&&(t.__items.splice(0,1),t.__character_count-=1),!0}return!1},e.prototype.is_empty=function(){return 0===this.__items.length},e.prototype.last=function(){return this.is_empty()?null:this.__items[this.__items.length-1]},e.prototype.push=function(t){this.__items.push(t);var e=t.lastIndexOf("\n");-1!==e?this.__character_count=t.length-e:this.__character_count+=t.length},e.prototype.pop=function(){var t=null;return this.is_empty()||(t=this.__items.pop(),this.__character_count-=t.length),t},e.prototype._remove_indent=function(){this.__indent_count>0&&(this.__indent_count-=1,this.__character_count-=this.__parent.indent_size)},e.prototype._remove_wrap_indent=function(){this.__wrap_point_indent_count>0&&(this.__wrap_point_indent_count-=1)},e.prototype.trim=function(){for(;" "===this.last();)this.__items.pop(),this.__character_count-=1},e.prototype.toString=function(){var t="";return this.is_empty()?this.__parent.indent_empty_lines&&(t=this.__parent.get_indent_string(this.__indent_count)):t=this.__parent.get_indent_string(this.__indent_count,this.__alignment_count)+this.__items.join(""),t},i.prototype.get_indent_size=function(t,e){var i=this.__base_string_length;return e=e||0,t<0&&(i=0),i+=t*this.__indent_size+e},i.prototype.get_indent_string=function(t,e){var i=this.__base_string;return t<0&&(t=0,i=""),e=(e||0)+t*this.__indent_size,this.__ensure_cache(e),i+=this.__cache[e]},i.prototype.__ensure_cache=function(t){for(;t>=this.__cache.length;)this.__add_column()},i.prototype.__add_column=function(){var t=this.__cache.length,e=0,i="";this.__indent_size&&t>=this.__indent_size&&(e=Math.floor(t/this.__indent_size),t-=e*this.__indent_size,i=Array(e+1).join(this.__indent_string)),t&&(i+=Array(t+1).join(" ")),this.__cache.push(i)},n.prototype.__add_outputline=function(){this.previous_line=this.current_line,this.current_line=this.next_line.clone_empty(),this.__lines.push(this.current_line)},n.prototype.get_line_number=function(){return this.__lines.length},n.prototype.get_indent_string=function(t,e){return this.__indent_cache.get_indent_string(t,e)},n.prototype.get_indent_size=function(t,e){return this.__indent_cache.get_indent_size(t,e)},n.prototype.is_empty=function(){return!this.previous_line&&this.current_line.is_empty()},n.prototype.add_new_line=function(t){return!(this.is_empty()||!t&&this.just_added_newline())&&(this.raw||this.__add_outputline(),!0)},n.prototype.get_code=function(t){this.trim(!0);var e=this.current_line.pop();e&&("\n"===e[e.length-1]&&(e=e.replace(/\n+$/g,"")),this.current_line.push(e)),this._end_with_newline&&this.__add_outputline();var i=this.__lines.join("\n");return"\n"!==t&&(i=i.replace(/[\n]/g,t)),i},n.prototype.set_wrap_point=function(){this.current_line._set_wrap_point()},n.prototype.set_indent=function(t,e){return(t=t||0,e=e||0,this.next_line.set_indent(t,e),this.__lines.length>1)?(this.current_line.set_indent(t,e),!0):(this.current_line.set_indent(),!1)},n.prototype.add_raw_token=function(t){for(var e=0;e<t.newlines;e++)this.__add_outputline();this.current_line.set_indent(-1),this.current_line.push(t.whitespace_before),this.current_line.push(t.text),this.space_before_token=!1,this.non_breaking_space=!1,this.previous_token_wrapped=!1},n.prototype.add_token=function(t){this.__add_space_before_token(),this.current_line.push(t),this.space_before_token=!1,this.non_breaking_space=!1,this.previous_token_wrapped=this.current_line._allow_wrap()},n.prototype.__add_space_before_token=function(){this.space_before_token&&!this.just_added_newline()&&(this.non_breaking_space||this.set_wrap_point(),this.current_line.push(" "))},n.prototype.remove_indent=function(t){for(var e=this.__lines.length;t<e;)this.__lines[t]._remove_indent(),t++;this.current_line._remove_wrap_indent()},n.prototype.trim=function(t){for(t=void 0!==t&&t,this.current_line.trim();t&&this.__lines.length>1&&this.current_line.is_empty();)this.__lines.pop(),this.current_line=this.__lines[this.__lines.length-1],this.current_line.trim();this.previous_line=this.__lines.length>1?this.__lines[this.__lines.length-2]:null},n.prototype.just_added_newline=function(){return this.current_line.is_empty()},n.prototype.just_added_blankline=function(){return this.is_empty()||this.current_line.is_empty()&&this.previous_line.is_empty()},n.prototype.ensure_empty_line_above=function(t,i){for(var n=this.__lines.length-2;n>=0;){var s=this.__lines[n];if(s.is_empty())break;if(0!==s.item(0).indexOf(t)&&s.item(-1)!==i){this.__lines.splice(n+1,0,new e(this)),this.previous_line=this.__lines[this.__lines.length-2];break}n--}},t.exports.Output=n},function(t){t.exports.Token=function(t,e,i,n){this.type=t,this.text=e,this.comments_before=null,this.newlines=i||0,this.whitespace_before=n||"",this.parent=null,this.next=null,this.previous=null,this.opened=null,this.closed=null,this.directives=null}},,,function(t){function e(t,e){this.raw_options=i(t,e),this.disabled=this._get_boolean("disabled"),this.eol=this._get_characters("eol","auto"),this.end_with_newline=this._get_boolean("end_with_newline"),this.indent_size=this._get_number("indent_size",4),this.indent_char=this._get_characters("indent_char"," "),this.indent_level=this._get_number("indent_level"),this.preserve_newlines=this._get_boolean("preserve_newlines",!0),this.max_preserve_newlines=this._get_number("max_preserve_newlines",32786),this.preserve_newlines||(this.max_preserve_newlines=0),this.indent_with_tabs=this._get_boolean("indent_with_tabs","	"===this.indent_char),this.indent_with_tabs&&(this.indent_char="	",1===this.indent_size&&(this.indent_size=4)),this.wrap_line_length=this._get_number("wrap_line_length",this._get_number("max_char")),this.indent_empty_lines=this._get_boolean("indent_empty_lines"),this.templating=this._get_selection_list("templating",["auto","none","angular","django","erb","handlebars","php","smarty"],["auto"])}function i(t,e){var i,s={};for(i in t=n(t))i!==e&&(s[i]=t[i]);if(e&&t[e])for(i in t[e])s[i]=t[e][i];return s}function n(t){var e,i={};for(e in t)i[e.replace(/-/g,"_")]=t[e];return i}e.prototype._get_array=function(t,e){var i=this.raw_options[t],n=e||[];return"object"==typeof i?null!==i&&"function"==typeof i.concat&&(n=i.concat()):"string"==typeof i&&(n=i.split(/[^a-zA-Z0-9_\/\-]+/)),n},e.prototype._get_boolean=function(t,e){var i=this.raw_options[t];return void 0===i?!!e:!!i},e.prototype._get_characters=function(t,e){var i=this.raw_options[t],n=e||"";return"string"==typeof i&&(n=i.replace(/\\r/,"\r").replace(/\\n/,"\n").replace(/\\t/,"	")),n},e.prototype._get_number=function(t,e){var i=this.raw_options[t];isNaN(e=parseInt(e,10))&&(e=0);var n=parseInt(i,10);return isNaN(n)&&(n=e),n},e.prototype._get_selection=function(t,e,i){var n=this._get_selection_list(t,e,i);if(1!==n.length)throw Error("Invalid Option Value: The option '"+t+"' can only be one of the following values:\n"+e+"\nYou passed in: '"+this.raw_options[t]+"'");return n[0]},e.prototype._get_selection_list=function(t,e,i){if(!e||0===e.length)throw Error("Selection list cannot be empty.");if(i=i||[e[0]],!this._is_valid_selection(i,e))throw Error("Invalid Default Value!");var n=this._get_array(t,i);if(!this._is_valid_selection(n,e))throw Error("Invalid Option Value: The option '"+t+"' can contain only the following values:\n"+e+"\nYou passed in: '"+this.raw_options[t]+"'");return n},e.prototype._is_valid_selection=function(t,e){return t.length&&e.length&&!t.some(function(t){return -1===e.indexOf(t)})},t.exports.Options=e,t.exports.normalizeOpts=n,t.exports.mergeOpts=i},,function(t){var e=RegExp.prototype.hasOwnProperty("sticky");function i(t){this.__input=t||"",this.__input_length=this.__input.length,this.__position=0}i.prototype.restart=function(){this.__position=0},i.prototype.back=function(){this.__position>0&&(this.__position-=1)},i.prototype.hasNext=function(){return this.__position<this.__input_length},i.prototype.next=function(){var t=null;return this.hasNext()&&(t=this.__input.charAt(this.__position),this.__position+=1),t},i.prototype.peek=function(t){var e=null;return(t=(t||0)+this.__position)>=0&&t<this.__input_length&&(e=this.__input.charAt(t)),e},i.prototype.__match=function(t,i){t.lastIndex=i;var n=t.exec(this.__input);return n&&!(e&&t.sticky)&&n.index!==i&&(n=null),n},i.prototype.test=function(t,e){return(e=(e||0)+this.__position)>=0&&e<this.__input_length&&!!this.__match(t,e)},i.prototype.testChar=function(t,e){var i=this.peek(e);return t.lastIndex=0,null!==i&&t.test(i)},i.prototype.match=function(t){var e=this.__match(t,this.__position);return e?this.__position+=e[0].length:e=null,e},i.prototype.read=function(t,e,i){var n,s="";return t&&(n=this.match(t))&&(s+=n[0]),e&&(n||!t)&&(s+=this.readUntil(e,i)),s},i.prototype.readUntil=function(t,e){var i="",n=this.__position;t.lastIndex=this.__position;var s=t.exec(this.__input);return s?(n=s.index,e&&(n+=s[0].length)):n=this.__input_length,i=this.__input.substring(this.__position,n),this.__position=n,i},i.prototype.readUntilAfter=function(t){return this.readUntil(t,!0)},i.prototype.get_regexp=function(t,i){var n=null,s="g";return i&&e&&(s="y"),"string"==typeof t&&""!==t?n=new RegExp(t,s):t&&(n=new RegExp(t.source,s)),n},i.prototype.get_literal_regexp=function(t){return RegExp(t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&"))},i.prototype.peekUntilAfter=function(t){var e=this.__position,i=this.readUntilAfter(t);return this.__position=e,i},i.prototype.lookBack=function(t){var e=this.__position-1;return e>=t.length&&this.__input.substring(e-t.length,e).toLowerCase()===t},t.exports.InputScanner=i},function(t,e,i){var n=i(8).InputScanner,s=i(3).Token,r=i(10).TokenStream,a=i(11).WhitespacePattern,o={START:"TK_START",RAW:"TK_RAW",EOF:"TK_EOF"},_=function(t,e){this._input=new n(t),this._options=e||{},this.__tokens=null,this._patterns={},this._patterns.whitespace=new a(this._input)};_.prototype.tokenize=function(){this._input.restart(),this.__tokens=new r,this._reset();for(var t,e=new s(o.START,""),i=null,n=[],a=new r;e.type!==o.EOF;){for(t=this._get_next_token(e,i);this._is_comment(t);)a.add(t),t=this._get_next_token(e,i);a.isEmpty()||(t.comments_before=a,a=new r),t.parent=i,this._is_opening(t)?(n.push(i),i=t):i&&this._is_closing(t,i)&&(t.opened=i,i.closed=t,i=n.pop(),t.parent=i),t.previous=e,e.next=t,this.__tokens.add(t),e=t}return this.__tokens},_.prototype._is_first_token=function(){return this.__tokens.isEmpty()},_.prototype._reset=function(){},_.prototype._get_next_token=function(t,e){this._readWhitespace();var i=this._input.read(/.+/g);return i?this._create_token(o.RAW,i):this._create_token(o.EOF,"")},_.prototype._is_comment=function(t){return!1},_.prototype._is_opening=function(t){return!1},_.prototype._is_closing=function(t,e){return!1},_.prototype._create_token=function(t,e){return new s(t,e,this._patterns.whitespace.newline_count,this._patterns.whitespace.whitespace_before_token)},_.prototype._readWhitespace=function(){return this._patterns.whitespace.read()},t.exports.Tokenizer=_,t.exports.TOKEN=o},function(t){function e(t){this.__tokens=[],this.__tokens_length=this.__tokens.length,this.__position=0,this.__parent_token=t}e.prototype.restart=function(){this.__position=0},e.prototype.isEmpty=function(){return 0===this.__tokens_length},e.prototype.hasNext=function(){return this.__position<this.__tokens_length},e.prototype.next=function(){var t=null;return this.hasNext()&&(t=this.__tokens[this.__position],this.__position+=1),t},e.prototype.peek=function(t){var e=null;return(t=(t||0)+this.__position)>=0&&t<this.__tokens_length&&(e=this.__tokens[t]),e},e.prototype.add=function(t){this.__parent_token&&(t.parent=this.__parent_token),this.__tokens.push(t),this.__tokens_length+=1},t.exports.TokenStream=e},function(t,e,i){var n=i(12).Pattern;function s(t,e){n.call(this,t,e),e?this._line_regexp=this._input.get_regexp(e._line_regexp):this.__set_whitespace_patterns("",""),this.newline_count=0,this.whitespace_before_token=""}s.prototype=new n,s.prototype.__set_whitespace_patterns=function(t,e){t+="\\t ",e+="\\n\\r",this._match_pattern=this._input.get_regexp("["+t+e+"]+",!0),this._newline_regexp=this._input.get_regexp("\\r\\n|["+e+"]")},s.prototype.read=function(){this.newline_count=0,this.whitespace_before_token="";var t=this._input.read(this._match_pattern);if(" "===t)this.whitespace_before_token=" ";else if(t){var e=this.__split(this._newline_regexp,t);this.newline_count=e.length-1,this.whitespace_before_token=e[this.newline_count]}return t},s.prototype.matching=function(t,e){var i=this._create();return i.__set_whitespace_patterns(t,e),i._update(),i},s.prototype._create=function(){return new s(this._input,this)},s.prototype.__split=function(t,e){t.lastIndex=0;for(var i=0,n=[],s=t.exec(e);s;)n.push(e.substring(i,s.index)),i=s.index+s[0].length,s=t.exec(e);return i<e.length?n.push(e.substring(i,e.length)):n.push(""),n},t.exports.WhitespacePattern=s},function(t){function e(t,e){this._input=t,this._starting_pattern=null,this._match_pattern=null,this._until_pattern=null,this._until_after=!1,e&&(this._starting_pattern=this._input.get_regexp(e._starting_pattern,!0),this._match_pattern=this._input.get_regexp(e._match_pattern,!0),this._until_pattern=this._input.get_regexp(e._until_pattern),this._until_after=e._until_after)}e.prototype.read=function(){var t=this._input.read(this._starting_pattern);return(!this._starting_pattern||t)&&(t+=this._input.read(this._match_pattern,this._until_pattern,this._until_after)),t},e.prototype.read_match=function(){return this._input.match(this._match_pattern)},e.prototype.until_after=function(t){var e=this._create();return e._until_after=!0,e._until_pattern=this._input.get_regexp(t),e._update(),e},e.prototype.until=function(t){var e=this._create();return e._until_after=!1,e._until_pattern=this._input.get_regexp(t),e._update(),e},e.prototype.starting_with=function(t){var e=this._create();return e._starting_pattern=this._input.get_regexp(t,!0),e._update(),e},e.prototype.matching=function(t){var e=this._create();return e._match_pattern=this._input.get_regexp(t,!0),e._update(),e},e.prototype._create=function(){return new e(this._input,this)},e.prototype._update=function(){},t.exports.Pattern=e},function(t){function e(t,e){t="string"==typeof t?t:t.source,e="string"==typeof e?e:e.source,this.__directives_block_pattern=RegExp(t+/ beautify( \w+[:]\w+)+ /.source+e,"g"),this.__directive_pattern=/ (\w+)[:](\w+)/g,this.__directives_end_ignore_pattern=RegExp(t+/\sbeautify\signore:end\s/.source+e,"g")}e.prototype.get_directives=function(t){if(!t.match(this.__directives_block_pattern))return null;var e={};this.__directive_pattern.lastIndex=0;for(var i=this.__directive_pattern.exec(t);i;)e[i[1]]=i[2],i=this.__directive_pattern.exec(t);return e},e.prototype.readIgnored=function(t){return t.readUntilAfter(this.__directives_end_ignore_pattern)},t.exports.Directives=e},function(t,e,i){var n=i(12).Pattern,s={django:!1,erb:!1,handlebars:!1,php:!1,smarty:!1,angular:!1};function r(t,e){n.call(this,t,e),this.__template_pattern=null,this._disabled=Object.assign({},s),this._excluded=Object.assign({},s),e&&(this.__template_pattern=this._input.get_regexp(e.__template_pattern),this._excluded=Object.assign(this._excluded,e._excluded),this._disabled=Object.assign(this._disabled,e._disabled));var i=new n(t);this.__patterns={handlebars_comment:i.starting_with(/{{!--/).until_after(/--}}/),handlebars_unescaped:i.starting_with(/{{{/).until_after(/}}}/),handlebars:i.starting_with(/{{/).until_after(/}}/),php:i.starting_with(/<\?(?:[= ]|php)/).until_after(/\?>/),erb:i.starting_with(/<%[^%]/).until_after(/[^%]%>/),django:i.starting_with(/{%/).until_after(/%}/),django_value:i.starting_with(/{{/).until_after(/}}/),django_comment:i.starting_with(/{#/).until_after(/#}/),smarty:i.starting_with(/{(?=[^}{\s\n])/).until_after(/[^\s\n]}/),smarty_comment:i.starting_with(/{\*/).until_after(/\*}/),smarty_literal:i.starting_with(/{literal}/).until_after(/{\/literal}/)}}r.prototype=new n,r.prototype._create=function(){return new r(this._input,this)},r.prototype._update=function(){this.__set_templated_pattern()},r.prototype.disable=function(t){var e=this._create();return e._disabled[t]=!0,e._update(),e},r.prototype.read_options=function(t){var e=this._create();for(var i in s)e._disabled[i]=-1===t.templating.indexOf(i);return e._update(),e},r.prototype.exclude=function(t){var e=this._create();return e._excluded[t]=!0,e._update(),e},r.prototype.read=function(){var t="";t=this._match_pattern?this._input.read(this._starting_pattern):this._input.read(this._starting_pattern,this.__template_pattern);for(var e=this._read_template();e;)this._match_pattern?e+=this._input.read(this._match_pattern):e+=this._input.readUntil(this.__template_pattern),t+=e,e=this._read_template();return this._until_after&&(t+=this._input.readUntilAfter(this._until_pattern)),t},r.prototype.__set_templated_pattern=function(){var t=[];this._disabled.php||t.push(this.__patterns.php._starting_pattern.source),this._disabled.handlebars||t.push(this.__patterns.handlebars._starting_pattern.source),this._disabled.erb||t.push(this.__patterns.erb._starting_pattern.source),this._disabled.django||(t.push(this.__patterns.django._starting_pattern.source),t.push(this.__patterns.django_value._starting_pattern.source),t.push(this.__patterns.django_comment._starting_pattern.source)),this._disabled.smarty||t.push(this.__patterns.smarty._starting_pattern.source),this._until_pattern&&t.push(this._until_pattern.source),this.__template_pattern=this._input.get_regexp("(?:"+t.join("|")+")")},r.prototype._read_template=function(){var t="",e=this._input.peek();if("<"===e){var i=this._input.peek(1);this._disabled.php||this._excluded.php||"?"!==i||(t=t||this.__patterns.php.read()),this._disabled.erb||this._excluded.erb||"%"!==i||(t=t||this.__patterns.erb.read())}else"{"===e&&(this._disabled.handlebars||this._excluded.handlebars||(t=(t=(t=t||this.__patterns.handlebars_comment.read())||this.__patterns.handlebars_unescaped.read())||this.__patterns.handlebars.read()),this._disabled.django||(this._excluded.django||this._excluded.handlebars||(t=t||this.__patterns.django_value.read()),this._excluded.django||(t=(t=t||this.__patterns.django_comment.read())||this.__patterns.django.read())),!this._disabled.smarty&&this._disabled.django&&this._disabled.handlebars&&(t=(t=(t=t||this.__patterns.smarty_comment.read())||this.__patterns.smarty_literal.read())||this.__patterns.smarty.read()));return t},t.exports.TemplatablePattern=r},,,,function(t,e,i){var n=i(19).Beautifier,s=i(20).Options;t.exports=function(t,e,i,s){return new n(t,e,i,s).beautify()},t.exports.defaultOptions=function(){return new s}},function(t,e,i){var n=i(20).Options,s=i(2).Output,r=i(21).Tokenizer,a=i(21).TOKEN,o=/\r\n|[\r\n]/,_=/\r\n|[\r\n]/g,h=function(t,e){this.indent_level=0,this.alignment_size=0,this.max_preserve_newlines=t.max_preserve_newlines,this.preserve_newlines=t.preserve_newlines,this._output=new s(t,e)};h.prototype.current_line_has_match=function(t){return this._output.current_line.has_match(t)},h.prototype.set_space_before_token=function(t,e){this._output.space_before_token=t,this._output.non_breaking_space=e},h.prototype.set_wrap_point=function(){this._output.set_indent(this.indent_level,this.alignment_size),this._output.set_wrap_point()},h.prototype.add_raw_token=function(t){this._output.add_raw_token(t)},h.prototype.print_preserved_newlines=function(t){var e=0;t.type!==a.TEXT&&t.previous.type!==a.TEXT&&(e=t.newlines?1:0),this.preserve_newlines&&(e=t.newlines<this.max_preserve_newlines+1?t.newlines:this.max_preserve_newlines+1);for(var i=0;i<e;i++)this.print_newline(i>0);return 0!==e},h.prototype.traverse_whitespace=function(t){return(!!t.whitespace_before||!!t.newlines)&&(this.print_preserved_newlines(t)||(this._output.space_before_token=!0),!0)},h.prototype.previous_token_wrapped=function(){return this._output.previous_token_wrapped},h.prototype.print_newline=function(t){this._output.add_new_line(t)},h.prototype.print_token=function(t){t.text&&(this._output.set_indent(this.indent_level,this.alignment_size),this._output.add_token(t.text))},h.prototype.indent=function(){this.indent_level++},h.prototype.deindent=function(){this.indent_level>0&&(this.indent_level--,this._output.set_indent(this.indent_level,this.alignment_size))},h.prototype.get_full_indent=function(t){return(t=this.indent_level+(t||0))<1?"":this._output.get_indent_string(t)};var u=function(t){for(var e=null,i=t.next;i.type!==a.EOF&&t.closed!==i;){if(i.type===a.ATTRIBUTE&&"type"===i.text){i.next&&i.next.type===a.EQUALS&&i.next.next&&i.next.next.type===a.VALUE&&(e=i.next.next.text);break}i=i.next}return e},l=function(t,e){var i=null,n=null;return e.closed?("script"===t?i="text/javascript":"style"===t&&(i="text/css"),(i=u(e)||i).search("text/css")>-1?n="css":i.search(/module|((text|application|dojo)\/(x-)?(javascript|ecmascript|jscript|livescript|(ld\+)?json|method|aspect))/)>-1?n="javascript":i.search(/(text|application|dojo)\/(x-)?(html)/)>-1?n="html":i.search(/test\/null/)>-1&&(n="null"),n):null};function p(t,e){return -1!==e.indexOf(t)}function c(t,e,i){this.parent=t||null,this.tag=e?e.tag_name:"",this.indent_level=i||0,this.parser_token=e||null}function d(t){this._printer=t,this._current_frame=null}function f(t,e,i,s){this._source_text=t||"",e=e||{},this._js_beautify=i,this._css_beautify=s,this._tag_stack=null;var r=new n(e,"html");this._options=r,this._is_wrap_attributes_force="force"===this._options.wrap_attributes.substr(0,5),this._is_wrap_attributes_force_expand_multiline="force-expand-multiline"===this._options.wrap_attributes,this._is_wrap_attributes_force_aligned="force-aligned"===this._options.wrap_attributes,this._is_wrap_attributes_aligned_multiple="aligned-multiple"===this._options.wrap_attributes,this._is_wrap_attributes_preserve="preserve"===this._options.wrap_attributes.substr(0,8),this._is_wrap_attributes_preserve_aligned="preserve-aligned"===this._options.wrap_attributes}d.prototype.get_parser_token=function(){return this._current_frame?this._current_frame.parser_token:null},d.prototype.record_tag=function(t){var e=new c(this._current_frame,t,this._printer.indent_level);this._current_frame=e},d.prototype._try_pop_frame=function(t){var e=null;return t&&(e=t.parser_token,this._printer.indent_level=t.indent_level,this._current_frame=t.parent),e},d.prototype._get_frame=function(t,e){for(var i=this._current_frame;i&&-1===t.indexOf(i.tag);){if(e&&-1!==e.indexOf(i.tag)){i=null;break}i=i.parent}return i},d.prototype.try_pop=function(t,e){var i=this._get_frame([t],e);return this._try_pop_frame(i)},d.prototype.indent_to_tag=function(t){var e=this._get_frame(t);e&&(this._printer.indent_level=e.indent_level)},f.prototype.beautify=function(){if(this._options.disabled)return this._source_text;var t=this._source_text,e=this._options.eol;"auto"===this._options.eol&&(e="\n",t&&o.test(t)&&(e=t.match(o)[0]));var i=(t=t.replace(_,"\n")).match(/^[\t ]*/)[0],n={text:"",type:""},s=new g,u=new h(this._options,i),l=new r(t,this._options).tokenize();this._tag_stack=new d(u);for(var p=null,c=l.next();c.type!==a.EOF;)c.type===a.TAG_OPEN||c.type===a.COMMENT?s=p=this._handle_tag_open(u,c,s,n,l):c.type!==a.ATTRIBUTE&&c.type!==a.EQUALS&&c.type!==a.VALUE&&(c.type!==a.TEXT||s.tag_complete)?c.type===a.TAG_CLOSE?p=this._handle_tag_close(u,c,s):c.type===a.TEXT?p=this._handle_text(u,c,s):c.type===a.CONTROL_FLOW_OPEN?p=this._handle_control_flow_open(u,c):c.type===a.CONTROL_FLOW_CLOSE?p=this._handle_control_flow_close(u,c):u.add_raw_token(c):p=this._handle_inside_tag(u,c,s,n),n=p,c=l.next();return u._output.get_code(e)},f.prototype._handle_control_flow_open=function(t,e){var i={text:e.text,type:e.type};return t.set_space_before_token(e.newlines||""!==e.whitespace_before,!0),e.newlines?t.print_preserved_newlines(e):t.set_space_before_token(e.newlines||""!==e.whitespace_before,!0),t.print_token(e),t.indent(),i},f.prototype._handle_control_flow_close=function(t,e){var i={text:e.text,type:e.type};return t.deindent(),e.newlines?t.print_preserved_newlines(e):t.set_space_before_token(e.newlines||""!==e.whitespace_before,!0),t.print_token(e),i},f.prototype._handle_tag_close=function(t,e,i){var n={text:e.text,type:e.type};return t.alignment_size=0,i.tag_complete=!0,t.set_space_before_token(e.newlines||""!==e.whitespace_before,!0),i.is_unformatted?t.add_raw_token(e):("<"===i.tag_start_char&&(t.set_space_before_token("/"===e.text[0],!0),this._is_wrap_attributes_force_expand_multiline&&i.has_wrapped_attrs&&t.print_newline(!1)),t.print_token(e)),i.indent_content&&!(i.is_unformatted||i.is_content_unformatted)&&(t.indent(),i.indent_content=!1),i.is_inline_element||i.is_unformatted||i.is_content_unformatted||t.set_wrap_point(),n},f.prototype._handle_inside_tag=function(t,e,i,n){var s=i.has_wrapped_attrs,r={text:e.text,type:e.type};return t.set_space_before_token(e.newlines||""!==e.whitespace_before,!0),i.is_unformatted?t.add_raw_token(e):"{"===i.tag_start_char&&e.type===a.TEXT?t.print_preserved_newlines(e)?(e.newlines=0,t.add_raw_token(e)):t.print_token(e):(e.type===a.ATTRIBUTE?t.set_space_before_token(!0):e.type===a.EQUALS?t.set_space_before_token(!1):e.type===a.VALUE&&e.previous.type===a.EQUALS&&t.set_space_before_token(!1),e.type===a.ATTRIBUTE&&"<"===i.tag_start_char&&((this._is_wrap_attributes_preserve||this._is_wrap_attributes_preserve_aligned)&&(t.traverse_whitespace(e),s=s||0!==e.newlines),this._is_wrap_attributes_force&&i.attr_count>=this._options.wrap_attributes_min_attrs&&(n.type!==a.TAG_OPEN||this._is_wrap_attributes_force_expand_multiline)&&(t.print_newline(!1),s=!0)),t.print_token(e),s=s||t.previous_token_wrapped(),i.has_wrapped_attrs=s),r},f.prototype._handle_text=function(t,e,i){var n={text:e.text,type:"TK_CONTENT"};return i.custom_beautifier_name?this._print_custom_beatifier_text(t,e,i):i.is_unformatted||i.is_content_unformatted?t.add_raw_token(e):(t.traverse_whitespace(e),t.print_token(e)),n},f.prototype._print_custom_beatifier_text=function(t,e,i){var n=this;if(""!==e.text){var s,r=e.text,a=1,o="",_="";"javascript"===i.custom_beautifier_name&&"function"==typeof this._js_beautify?s=this._js_beautify:"css"===i.custom_beautifier_name&&"function"==typeof this._css_beautify?s=this._css_beautify:"html"===i.custom_beautifier_name&&(s=function(t,e){return new f(t,e,n._js_beautify,n._css_beautify).beautify()}),"keep"===this._options.indent_scripts?a=0:"separate"===this._options.indent_scripts&&(a=-t.indent_level);var h=t.get_full_indent(a);if(r=r.replace(/\n[ \t]*$/,""),"html"!==i.custom_beautifier_name&&"<"===r[0]&&r.match(/^(<!--|<!\[CDATA\[)/)){var u=/^(<!--[^\n]*|<!\[CDATA\[)(\n?)([ \t\n]*)([\s\S]*)(-->|]]>)$/.exec(r);if(!u){t.add_raw_token(e);return}o=h+u[1]+"\n",r=u[4],u[5]&&(_=h+u[5]),r=r.replace(/\n[ \t]*$/,""),(u[2]||-1!==u[3].indexOf("\n"))&&(u=u[3].match(/[ \t]+$/))&&(e.whitespace_before=u[0])}if(r){if(s){var l=function(){this.eol="\n"};l.prototype=this._options.raw_options,r=s(h+r,new l)}else{var p=e.whitespace_before;p&&(r=r.replace(RegExp("\n("+p+")?","g"),"\n")),r=h+r.replace(/\n/g,"\n"+h)}}o&&(r=r?o+r+"\n"+_:o+_),t.print_newline(!1),r&&(e.text=r,e.whitespace_before="",e.newlines=0,t.add_raw_token(e),t.print_newline(!0))}},f.prototype._handle_tag_open=function(t,e,i,n,s){var r=this._get_tag_open_token(e);if((i.is_unformatted||i.is_content_unformatted)&&!i.is_empty_element&&e.type===a.TAG_OPEN&&!r.is_start_tag?(t.add_raw_token(e),r.start_tag_token=this._tag_stack.try_pop(r.tag_name)):(t.traverse_whitespace(e),this._set_tag_position(t,e,r,i,n),r.is_inline_element||t.set_wrap_point(),t.print_token(e)),r.is_start_tag&&this._is_wrap_attributes_force){var o,_=0;do(o=s.peek(_)).type===a.ATTRIBUTE&&(r.attr_count+=1),_+=1;while(o.type!==a.EOF&&o.type!==a.TAG_CLOSE)}return(this._is_wrap_attributes_force_aligned||this._is_wrap_attributes_aligned_multiple||this._is_wrap_attributes_preserve_aligned)&&(r.alignment_size=e.text.length+1),r.tag_complete||r.is_unformatted||(t.alignment_size=r.alignment_size),r};var g=function(t,e){if(this.parent=t||null,this.text="",this.type="TK_TAG_OPEN",this.tag_name="",this.is_inline_element=!1,this.is_unformatted=!1,this.is_content_unformatted=!1,this.is_empty_element=!1,this.is_start_tag=!1,this.is_end_tag=!1,this.indent_content=!1,this.multiline_content=!1,this.custom_beautifier_name=null,this.start_tag_token=null,this.attr_count=0,this.has_wrapped_attrs=!1,this.alignment_size=0,this.tag_complete=!1,this.tag_start_char="",this.tag_check="",e){this.tag_start_char=e.text[0],this.text=e.text,"<"===this.tag_start_char?(i=e.text.match(/^<([^\s>]*)/),this.tag_check=i?i[1]:""):(i=e.text.match(/^{{~?(?:[\^]|#\*?)?([^\s}]+)/),this.tag_check=i?i[1]:"",(e.text.startsWith("{{#>")||e.text.startsWith("{{~#>"))&&">"===this.tag_check[0]&&(">"===this.tag_check&&null!==e.next?this.tag_check=e.next.text.split(" ")[0]:this.tag_check=e.text.split(">")[1])),this.tag_check=this.tag_check.toLowerCase(),e.type===a.COMMENT&&(this.tag_complete=!0),this.is_start_tag="/"!==this.tag_check.charAt(0),this.tag_name=this.is_start_tag?this.tag_check:this.tag_check.substr(1),this.is_end_tag=!this.is_start_tag||e.closed&&"/>"===e.closed.text;var i,n=2;"{"===this.tag_start_char&&this.text.length>=3&&"~"===this.text.charAt(2)&&(n=3),this.is_end_tag=this.is_end_tag||"{"===this.tag_start_char&&(this.text.length<3||/[^#\^]/.test(this.text.charAt(n)))}else this.tag_complete=!0};f.prototype._get_tag_open_token=function(t){var e=new g(this._tag_stack.get_parser_token(),t);return e.alignment_size=this._options.wrap_attributes_indent_size,e.is_end_tag=e.is_end_tag||p(e.tag_check,this._options.void_elements),e.is_empty_element=e.tag_complete||e.is_start_tag&&e.is_end_tag,e.is_unformatted=!e.tag_complete&&p(e.tag_check,this._options.unformatted),e.is_content_unformatted=!e.is_empty_element&&p(e.tag_check,this._options.content_unformatted),e.is_inline_element=p(e.tag_name,this._options.inline)||this._options.inline_custom_elements&&e.tag_name.includes("-")||"{"===e.tag_start_char,e},f.prototype._set_tag_position=function(t,e,i,n,s){if(i.is_empty_element||(i.is_end_tag?i.start_tag_token=this._tag_stack.try_pop(i.tag_name):(this._do_optional_end_element(i)&&!i.is_inline_element&&t.print_newline(!1),this._tag_stack.record_tag(i),"script"!==i.tag_name&&"style"!==i.tag_name||i.is_unformatted||i.is_content_unformatted||(i.custom_beautifier_name=l(i.tag_check,e)))),p(i.tag_check,this._options.extra_liners)&&(t.print_newline(!1),t._output.just_added_blankline()||t.print_newline(!0)),i.is_empty_element)"{"!==i.tag_start_char||"else"!==i.tag_check||(this._tag_stack.indent_to_tag(["if","unless","each"]),i.indent_content=!0,t.current_line_has_match(/{{#if/)||t.print_newline(!1)),"!--"===i.tag_name&&s.type===a.TAG_CLOSE&&n.is_end_tag&&-1===i.text.indexOf("\n")||(i.is_inline_element||i.is_unformatted||t.print_newline(!1),this._calcluate_parent_multiline(t,i));else if(i.is_end_tag){var r=!1;r=(r=i.start_tag_token&&i.start_tag_token.multiline_content)||!i.is_inline_element&&!(n.is_inline_element||n.is_unformatted)&&!(s.type===a.TAG_CLOSE&&i.start_tag_token===n)&&"TK_CONTENT"!==s.type,(i.is_content_unformatted||i.is_unformatted)&&(r=!1),r&&t.print_newline(!1)}else i.indent_content=!i.custom_beautifier_name,"<"===i.tag_start_char&&("html"===i.tag_name?i.indent_content=this._options.indent_inner_html:"head"===i.tag_name?i.indent_content=this._options.indent_head_inner_html:"body"===i.tag_name&&(i.indent_content=this._options.indent_body_inner_html)),!(i.is_inline_element||i.is_unformatted)&&("TK_CONTENT"!==s.type||i.is_content_unformatted)&&t.print_newline(!1),this._calcluate_parent_multiline(t,i)},f.prototype._calcluate_parent_multiline=function(t,e){e.parent&&t._output.just_added_newline()&&!((e.is_inline_element||e.is_unformatted)&&e.parent.is_inline_element)&&(e.parent.multiline_content=!0)};var m=["address","article","aside","blockquote","details","div","dl","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hr","main","menu","nav","ol","p","pre","section","table","ul"],b=["a","audio","del","ins","map","noscript","video"];f.prototype._do_optional_end_element=function(t){var e=null;if(!t.is_empty_element&&t.is_start_tag&&t.parent){if("body"===t.tag_name)e=e||this._tag_stack.try_pop("head");else if("li"===t.tag_name)e=e||this._tag_stack.try_pop("li",["ol","ul","menu"]);else if("dd"===t.tag_name||"dt"===t.tag_name)e=(e=e||this._tag_stack.try_pop("dt",["dl"]))||this._tag_stack.try_pop("dd",["dl"]);else if("p"===t.parent.tag_name&&-1!==m.indexOf(t.tag_name)){var i=t.parent.parent;i&&-1!==b.indexOf(i.tag_name)||(e=e||this._tag_stack.try_pop("p"))}else"rp"===t.tag_name||"rt"===t.tag_name?e=(e=e||this._tag_stack.try_pop("rt",["ruby","rtc"]))||this._tag_stack.try_pop("rp",["ruby","rtc"]):"optgroup"===t.tag_name?e=e||this._tag_stack.try_pop("optgroup",["select"]):"option"===t.tag_name?e=e||this._tag_stack.try_pop("option",["select","datalist","optgroup"]):"colgroup"===t.tag_name?e=e||this._tag_stack.try_pop("caption",["table"]):"thead"===t.tag_name?e=(e=e||this._tag_stack.try_pop("caption",["table"]))||this._tag_stack.try_pop("colgroup",["table"]):"tbody"===t.tag_name||"tfoot"===t.tag_name?e=(e=(e=(e=e||this._tag_stack.try_pop("caption",["table"]))||this._tag_stack.try_pop("colgroup",["table"]))||this._tag_stack.try_pop("thead",["table"]))||this._tag_stack.try_pop("tbody",["table"]):"tr"===t.tag_name?e=(e=(e=e||this._tag_stack.try_pop("caption",["table"]))||this._tag_stack.try_pop("colgroup",["table"]))||this._tag_stack.try_pop("tr",["table","thead","tbody","tfoot"]):("th"===t.tag_name||"td"===t.tag_name)&&(e=(e=e||this._tag_stack.try_pop("td",["table","thead","tbody","tfoot","tr"]))||this._tag_stack.try_pop("th",["table","thead","tbody","tfoot","tr"]));return t.parent=this._tag_stack.get_parser_token(),e}},t.exports.Beautifier=f},function(t,e,i){var n=i(6).Options;function s(t){n.call(this,t,"html"),1===this.templating.length&&"auto"===this.templating[0]&&(this.templating=["django","erb","handlebars","php"]),this.indent_inner_html=this._get_boolean("indent_inner_html"),this.indent_body_inner_html=this._get_boolean("indent_body_inner_html",!0),this.indent_head_inner_html=this._get_boolean("indent_head_inner_html",!0),this.indent_handlebars=this._get_boolean("indent_handlebars",!0),this.wrap_attributes=this._get_selection("wrap_attributes",["auto","force","force-aligned","force-expand-multiline","aligned-multiple","preserve","preserve-aligned"]),this.wrap_attributes_min_attrs=this._get_number("wrap_attributes_min_attrs",2),this.wrap_attributes_indent_size=this._get_number("wrap_attributes_indent_size",this.indent_size),this.extra_liners=this._get_array("extra_liners",["head","body","/html"]),this.inline=this._get_array("inline",["a","abbr","area","audio","b","bdi","bdo","br","button","canvas","cite","code","data","datalist","del","dfn","em","embed","i","iframe","img","input","ins","kbd","keygen","label","map","mark","math","meter","noscript","object","output","progress","q","ruby","s","samp","select","small","span","strong","sub","sup","svg","template","textarea","time","u","var","video","wbr","text","acronym","big","strike","tt"]),this.inline_custom_elements=this._get_boolean("inline_custom_elements",!0),this.void_elements=this._get_array("void_elements",["area","base","br","col","embed","hr","img","input","keygen","link","menuitem","meta","param","source","track","wbr","!doctype","?xml","basefont","isindex"]),this.unformatted=this._get_array("unformatted",[]),this.content_unformatted=this._get_array("content_unformatted",["pre","textarea"]),this.unformatted_content_delimiter=this._get_characters("unformatted_content_delimiter"),this.indent_scripts=this._get_selection("indent_scripts",["normal","keep","separate"])}s.prototype=new n,t.exports.Options=s},function(t,e,i){var n=i(9).Tokenizer,s=i(9).TOKEN,r=i(13).Directives,a=i(14).TemplatablePattern,o=i(12).Pattern,_={TAG_OPEN:"TK_TAG_OPEN",TAG_CLOSE:"TK_TAG_CLOSE",CONTROL_FLOW_OPEN:"TK_CONTROL_FLOW_OPEN",CONTROL_FLOW_CLOSE:"TK_CONTROL_FLOW_CLOSE",ATTRIBUTE:"TK_ATTRIBUTE",EQUALS:"TK_EQUALS",VALUE:"TK_VALUE",COMMENT:"TK_COMMENT",TEXT:"TK_TEXT",UNKNOWN:"TK_UNKNOWN",START:s.START,RAW:s.RAW,EOF:s.EOF},h=new r(/<\!--/,/-->/),u=function(t,e){n.call(this,t,e),this._current_tag_name="";var i=new a(this._input).read_options(this._options),s=new o(this._input);if(this.__patterns={word:i.until(/[\n\r\t <]/),word_control_flow_close_excluded:i.until(/[\n\r\t <}]/),single_quote:i.until_after(/'/),double_quote:i.until_after(/"/),attribute:i.until(/[\n\r\t =>]|\/>/),element_name:i.until(/[\n\r\t >\/]/),angular_control_flow_start:s.matching(/\@[a-zA-Z]+[^({]*[({]/),handlebars_comment:s.starting_with(/{{!--/).until_after(/--}}/),handlebars:s.starting_with(/{{/).until_after(/}}/),handlebars_open:s.until(/[\n\r\t }]/),handlebars_raw_close:s.until(/}}/),comment:s.starting_with(/<!--/).until_after(/-->/),cdata:s.starting_with(/<!\[CDATA\[/).until_after(/]]>/),conditional_comment:s.starting_with(/<!\[/).until_after(/]>/),processing:s.starting_with(/<\?/).until_after(/\?>/)},this._options.indent_handlebars&&(this.__patterns.word=this.__patterns.word.exclude("handlebars"),this.__patterns.word_control_flow_close_excluded=this.__patterns.word_control_flow_close_excluded.exclude("handlebars")),this._unformatted_content_delimiter=null,this._options.unformatted_content_delimiter){var r=this._input.get_literal_regexp(this._options.unformatted_content_delimiter);this.__patterns.unformatted_content_delimiter=s.matching(r).until_after(r)}};u.prototype=new n,u.prototype._is_comment=function(t){return!1},u.prototype._is_opening=function(t){return t.type===_.TAG_OPEN||t.type===_.CONTROL_FLOW_OPEN},u.prototype._is_closing=function(t,e){return t.type===_.TAG_CLOSE&&e&&((">"===t.text||"/>"===t.text)&&"<"===e.text[0]||"}}"===t.text&&"{"===e.text[0]&&"{"===e.text[1])||t.type===_.CONTROL_FLOW_CLOSE&&"}"===t.text&&e.text.endsWith("{")},u.prototype._reset=function(){this._current_tag_name=""},u.prototype._get_next_token=function(t,e){var i=null;this._readWhitespace();var n=this._input.peek();return null===n?this._create_token(_.EOF,""):i=(i=(i=(i=(i=(i=(i=(i=(i=(i=i||this._read_open_handlebars(n,e))||this._read_attribute(n,t,e))||this._read_close(n,e))||this._read_control_flows(n,e))||this._read_raw_content(n,t,e))||this._read_content_word(n,e))||this._read_comment_or_cdata(n))||this._read_processing(n))||this._read_open(n,e))||this._create_token(_.UNKNOWN,this._input.next())},u.prototype._read_comment_or_cdata=function(t){var e=null,i=null,n=null;return"<"===t&&("!"===this._input.peek(1)&&((i=this.__patterns.comment.read())?(n=h.get_directives(i))&&"start"===n.ignore&&(i+=h.readIgnored(this._input)):i=this.__patterns.cdata.read()),i&&((e=this._create_token(_.COMMENT,i)).directives=n)),e},u.prototype._read_processing=function(t){var e=null,i=null;if("<"===t){var n=this._input.peek(1);("!"===n||"?"===n)&&(i=(i=this.__patterns.conditional_comment.read())||this.__patterns.processing.read()),i&&((e=this._create_token(_.COMMENT,i)).directives=null)}return e},u.prototype._read_open=function(t,e){var i=null,n=null;return e&&e.type!==_.CONTROL_FLOW_OPEN||"<"!==t||(i=this._input.next(),"/"===this._input.peek()&&(i+=this._input.next()),i+=this.__patterns.element_name.read(),n=this._create_token(_.TAG_OPEN,i)),n},u.prototype._read_open_handlebars=function(t,e){var i=null,n=null;return(!e||e.type===_.CONTROL_FLOW_OPEN)&&this._options.indent_handlebars&&"{"===t&&"{"===this._input.peek(1)&&("!"===this._input.peek(2)?(i=(i=this.__patterns.handlebars_comment.read())||this.__patterns.handlebars.read(),n=this._create_token(_.COMMENT,i)):(i=this.__patterns.handlebars_open.read(),n=this._create_token(_.TAG_OPEN,i))),n},u.prototype._read_control_flows=function(t,e){var i="",n=null;if(!this._options.templating.includes("angular")||!this._options.indent_handlebars)return n;if("@"===t){if(""===(i=this.__patterns.angular_control_flow_start.read()))return n;for(var s=i.endsWith("(")?1:0,r=0;!(i.endsWith("{")&&s===r);){var a=this._input.next();if(null===a)break;"("===a?s++:")"===a&&r++,i+=a}n=this._create_token(_.CONTROL_FLOW_OPEN,i)}else"}"===t&&e&&e.type===_.CONTROL_FLOW_OPEN&&(i=this._input.next(),n=this._create_token(_.CONTROL_FLOW_CLOSE,i));return n},u.prototype._read_close=function(t,e){var i=null,n=null;return e&&e.type===_.TAG_OPEN&&("<"===e.text[0]&&(">"===t||"/"===t&&">"===this._input.peek(1))?(i=this._input.next(),"/"===t&&(i+=this._input.next()),n=this._create_token(_.TAG_CLOSE,i)):"{"===e.text[0]&&"}"===t&&"}"===this._input.peek(1)&&(this._input.next(),this._input.next(),n=this._create_token(_.TAG_CLOSE,"}}"))),n},u.prototype._read_attribute=function(t,e,i){var n=null,s="";if(i&&"<"===i.text[0]){if("="===t)n=this._create_token(_.EQUALS,this._input.next());else if('"'===t||"'"===t){var r=this._input.next();'"'===t?r+=this.__patterns.double_quote.read():r+=this.__patterns.single_quote.read(),n=this._create_token(_.VALUE,r)}else(s=this.__patterns.attribute.read())&&(n=e.type===_.EQUALS?this._create_token(_.VALUE,s):this._create_token(_.ATTRIBUTE,s))}return n},u.prototype._is_content_unformatted=function(t){return -1===this._options.void_elements.indexOf(t)&&(-1!==this._options.content_unformatted.indexOf(t)||-1!==this._options.unformatted.indexOf(t))},u.prototype._read_raw_content=function(t,e,i){var n="";if(i&&"{"===i.text[0])n=this.__patterns.handlebars_raw_close.read();else if(e.type===_.TAG_CLOSE&&"<"===e.opened.text[0]&&"/"!==e.text[0]){var s=e.opened.text.substr(1).toLowerCase();if("script"===s||"style"===s){var r=this._read_comment_or_cdata(t);if(r)return r.type=_.TEXT,r;n=this._input.readUntil(RegExp("</"+s+"[\\n\\r\\t ]*?>","ig"))}else this._is_content_unformatted(s)&&(n=this._input.readUntil(RegExp("</"+s+"[\\n\\r\\t ]*?>","ig")))}return n?this._create_token(_.TEXT,n):null},u.prototype._read_content_word=function(t,e){var i="";if(this._options.unformatted_content_delimiter&&t===this._options.unformatted_content_delimiter[0]&&(i=this.__patterns.unformatted_content_delimiter.read()),i||(i=e&&e.type===_.CONTROL_FLOW_OPEN?this.__patterns.word_control_flow_close_excluded.read():this.__patterns.word.read()),i)return this._create_token(_.TEXT,i)},t.exports.Tokenizer=u,t.exports.TOKEN=_}],r={};var s,r,a=function t(e){var i=r[e];if(void 0!==i)return i.exports;var n=r[e]={exports:{}};return s[e](n,n.exports,t),n.exports}(18);void 0!==(n=(function(t){var e=i(1760),n=i(96271);return{html_beautify:function(t,i){return a(t,i,e.js_beautify,n.css_beautify)}}}).apply(e,[i,i(1760),i(96271)]))&&(t.exports=n)}()},1760:(t,e)=>{var i;!function(){n=[function(t,e,i){var n=i(1).Beautifier,s=i(5).Options;t.exports=function(t,e){return new n(t,e).beautify()},t.exports.defaultOptions=function(){return new s}},function(t,e,i){var n=i(2).Output,s=i(3).Token,r=i(4),a=i(5).Options,o=i(7).Tokenizer,_=i(7).line_starters,h=i(7).positionable_operators,u=i(7).TOKEN;function l(t,e){return -1!==e.indexOf(t)}function p(t,e){return t&&t.type===u.RESERVED&&t.text===e}function c(t,e){return t&&t.type===u.RESERVED&&l(t.text,e)}var d=["case","return","do","if","throw","else","await","break","continue","async"],f=function(t){for(var e={},i=0;i<t.length;i++)e[t[i].replace(/-/g,"_")]=t[i];return e}(["before-newline","after-newline","preserve-newline"]),g=[f.before_newline,f.preserve_newline],m={BlockStatement:"BlockStatement",Statement:"Statement",ObjectLiteral:"ObjectLiteral",ArrayLiteral:"ArrayLiteral",ForInitializer:"ForInitializer",Conditional:"Conditional",Expression:"Expression"};function b(t,e){e.multiline_frame||e.mode===m.ForInitializer||e.mode===m.Conditional||t.remove_indent(e.start_line_index)}function y(t){return t===m.ArrayLiteral}function x(t){return l(t,[m.Expression,m.ForInitializer,m.Conditional])}function w(t,e){e=e||{},this._source_text=t||"",this._output=null,this._tokens=null,this._last_last_text=null,this._flags=null,this._previous_flags=null,this._flag_store=null,this._options=new a(e)}w.prototype.create_flags=function(t,e){var i=0;return t&&(i=t.indentation_level,!this._output.just_added_newline()&&t.line_indent_level>i&&(i=t.line_indent_level)),{mode:e,parent:t,last_token:t?t.last_token:new s(u.START_BLOCK,""),last_word:t?t.last_word:"",declaration_statement:!1,declaration_assignment:!1,multiline_frame:!1,inline_frame:!1,if_block:!1,else_block:!1,class_start_block:!1,do_block:!1,do_while:!1,import_block:!1,in_case_statement:!1,in_case:!1,case_body:!1,case_block:!1,indentation_level:i,alignment:0,line_indent_level:t?t.line_indent_level:i,start_line_index:this._output.get_line_number(),ternary_depth:0}},w.prototype._reset=function(t){var e=t.match(/^[\t ]*/)[0];this._last_last_text="",this._output=new n(this._options,e),this._output.raw=this._options.test_output_raw,this._flag_store=[],this.set_mode(m.BlockStatement);var i=new o(t,this._options);return this._tokens=i.tokenize(),t},w.prototype.beautify=function(){if(this._options.disabled)return this._source_text;var t=this._reset(this._source_text),e=this._options.eol;"auto"===this._options.eol&&(e="\n",t&&r.lineBreak.test(t||"")&&(e=t.match(r.lineBreak)[0]));for(var i=this._tokens.next();i;)this.handle_token(i),this._last_last_text=this._flags.last_token.text,this._flags.last_token=i,i=this._tokens.next();return this._output.get_code(e)},w.prototype.handle_token=function(t,e){t.type===u.START_EXPR?this.handle_start_expr(t):t.type===u.END_EXPR?this.handle_end_expr(t):t.type===u.START_BLOCK?this.handle_start_block(t):t.type===u.END_BLOCK?this.handle_end_block(t):t.type===u.WORD?this.handle_word(t):t.type===u.RESERVED?this.handle_word(t):t.type===u.SEMICOLON?this.handle_semicolon(t):t.type===u.STRING?this.handle_string(t):t.type===u.EQUALS?this.handle_equals(t):t.type===u.OPERATOR?this.handle_operator(t):t.type===u.COMMA?this.handle_comma(t):t.type===u.BLOCK_COMMENT?this.handle_block_comment(t,e):t.type===u.COMMENT?this.handle_comment(t,e):t.type===u.DOT?this.handle_dot(t):t.type===u.EOF?this.handle_eof(t):(t.type,u.UNKNOWN,this.handle_unknown(t,e))},w.prototype.handle_whitespace_and_comments=function(t,e){var i=t.newlines,n=this._options.keep_array_indentation&&y(this._flags.mode);if(t.comments_before)for(var s=t.comments_before.next();s;)this.handle_whitespace_and_comments(s,e),this.handle_token(s,e),s=t.comments_before.next();if(n)for(var r=0;r<i;r+=1)this.print_newline(r>0,e);else if(this._options.max_preserve_newlines&&i>this._options.max_preserve_newlines&&(i=this._options.max_preserve_newlines),this._options.preserve_newlines&&i>1){this.print_newline(!1,e);for(var a=1;a<i;a+=1)this.print_newline(!0,e)}};var k=["async","break","continue","return","throw","yield"];w.prototype.allow_wrap_or_preserved_newline=function(t,e){if(e=void 0!==e&&e,!this._output.just_added_newline()){var i=this._options.preserve_newlines&&t.newlines||e;if(l(this._flags.last_token.text,h)||l(t.text,h)){var n=l(this._flags.last_token.text,h)&&l(this._options.operator_position,g)||l(t.text,h);i=i&&n}if(i)this.print_newline(!1,!0);else if(this._options.wrap_line_length){if(c(this._flags.last_token,k))return;this._output.set_wrap_point()}}},w.prototype.print_newline=function(t,e){if(!e&&";"!==this._flags.last_token.text&&","!==this._flags.last_token.text&&"="!==this._flags.last_token.text&&(this._flags.last_token.type!==u.OPERATOR||"--"===this._flags.last_token.text||"++"===this._flags.last_token.text))for(var i=this._tokens.peek();this._flags.mode===m.Statement&&!(this._flags.if_block&&p(i,"else"))&&!this._flags.do_block;)this.restore_mode();this._output.add_new_line(t)&&(this._flags.multiline_frame=!0)},w.prototype.print_token_line_indentation=function(t){this._output.just_added_newline()&&(this._options.keep_array_indentation&&t.newlines&&("["===t.text||y(this._flags.mode))?(this._output.current_line.set_indent(-1),this._output.current_line.push(t.whitespace_before),this._output.space_before_token=!1):this._output.set_indent(this._flags.indentation_level,this._flags.alignment)&&(this._flags.line_indent_level=this._flags.indentation_level))},w.prototype.print_token=function(t){if(this._output.raw){this._output.add_raw_token(t);return}if(this._options.comma_first&&t.previous&&t.previous.type===u.COMMA&&this._output.just_added_newline()&&","===this._output.previous_line.last()){var e=this._output.previous_line.pop();this._output.previous_line.is_empty()&&(this._output.previous_line.push(e),this._output.trim(!0),this._output.current_line.pop(),this._output.trim()),this.print_token_line_indentation(t),this._output.add_token(","),this._output.space_before_token=!0}this.print_token_line_indentation(t),this._output.non_breaking_space=!0,this._output.add_token(t.text),this._output.previous_token_wrapped&&(this._flags.multiline_frame=!0)},w.prototype.indent=function(){this._flags.indentation_level+=1,this._output.set_indent(this._flags.indentation_level,this._flags.alignment)},w.prototype.deindent=function(){this._flags.indentation_level>0&&(!this._flags.parent||this._flags.indentation_level>this._flags.parent.indentation_level)&&(this._flags.indentation_level-=1,this._output.set_indent(this._flags.indentation_level,this._flags.alignment))},w.prototype.set_mode=function(t){this._flags?(this._flag_store.push(this._flags),this._previous_flags=this._flags):this._previous_flags=this.create_flags(null,t),this._flags=this.create_flags(this._previous_flags,t),this._output.set_indent(this._flags.indentation_level,this._flags.alignment)},w.prototype.restore_mode=function(){this._flag_store.length>0&&(this._previous_flags=this._flags,this._flags=this._flag_store.pop(),this._previous_flags.mode===m.Statement&&b(this._output,this._previous_flags),this._output.set_indent(this._flags.indentation_level,this._flags.alignment))},w.prototype.start_of_object_property=function(){return this._flags.parent.mode===m.ObjectLiteral&&this._flags.mode===m.Statement&&(":"===this._flags.last_token.text&&0===this._flags.ternary_depth||c(this._flags.last_token,["get","set"]))},w.prototype.start_of_statement=function(t){return!!(c(this._flags.last_token,["var","let","const"])&&t.type===u.WORD||p(this._flags.last_token,"do")||!(this._flags.parent.mode===m.ObjectLiteral&&this._flags.mode===m.Statement)&&c(this._flags.last_token,k)&&!t.newlines||p(this._flags.last_token,"else")&&!(p(t,"if")&&!t.comments_before)||this._flags.last_token.type===u.END_EXPR&&(this._previous_flags.mode===m.ForInitializer||this._previous_flags.mode===m.Conditional)||this._flags.last_token.type===u.WORD&&this._flags.mode===m.BlockStatement&&!this._flags.in_case&&!("--"===t.text||"++"===t.text)&&"function"!==this._last_last_text&&t.type!==u.WORD&&t.type!==u.RESERVED||this._flags.mode===m.ObjectLiteral&&(":"===this._flags.last_token.text&&0===this._flags.ternary_depth||c(this._flags.last_token,["get","set"])))&&(this.set_mode(m.Statement),this.indent(),this.handle_whitespace_and_comments(t,!0),this.start_of_object_property()||this.allow_wrap_or_preserved_newline(t,c(t,["do","for","if","while"])),!0)},w.prototype.handle_start_expr=function(t){this.start_of_statement(t)||this.handle_whitespace_and_comments(t);var e=m.Expression;if("["===t.text){if(this._flags.last_token.type===u.WORD||")"===this._flags.last_token.text){c(this._flags.last_token,_)&&(this._output.space_before_token=!0),this.print_token(t),this.set_mode(e),this.indent(),this._options.space_in_paren&&(this._output.space_before_token=!0);return}e=m.ArrayLiteral,y(this._flags.mode)&&("["===this._flags.last_token.text||","===this._flags.last_token.text&&("]"===this._last_last_text||"}"===this._last_last_text))&&!this._options.keep_array_indentation&&this.print_newline(),l(this._flags.last_token.type,[u.START_EXPR,u.END_EXPR,u.WORD,u.OPERATOR,u.DOT])||(this._output.space_before_token=!0)}else{if(this._flags.last_token.type===u.RESERVED)"for"===this._flags.last_token.text?(this._output.space_before_token=this._options.space_before_conditional,e=m.ForInitializer):l(this._flags.last_token.text,["if","while","switch"])?(this._output.space_before_token=this._options.space_before_conditional,e=m.Conditional):l(this._flags.last_word,["await","async"])?this._output.space_before_token=!0:"import"===this._flags.last_token.text&&""===t.whitespace_before?this._output.space_before_token=!1:(l(this._flags.last_token.text,_)||"catch"===this._flags.last_token.text)&&(this._output.space_before_token=!0);else if(this._flags.last_token.type===u.EQUALS||this._flags.last_token.type===u.OPERATOR)this.start_of_object_property()||this.allow_wrap_or_preserved_newline(t);else if(this._flags.last_token.type===u.WORD){this._output.space_before_token=!1;var i=this._tokens.peek(-3);if(this._options.space_after_named_function&&i){var n=this._tokens.peek(-4);c(i,["async","function"])||"*"===i.text&&c(n,["async","function"])?this._output.space_before_token=!0:this._flags.mode===m.ObjectLiteral?("{"===i.text||","===i.text||"*"===i.text&&("{"===n.text||","===n.text))&&(this._output.space_before_token=!0):this._flags.parent&&this._flags.parent.class_start_block&&(this._output.space_before_token=!0)}}else this.allow_wrap_or_preserved_newline(t);(this._flags.last_token.type===u.RESERVED&&("function"===this._flags.last_word||"typeof"===this._flags.last_word)||"*"===this._flags.last_token.text&&(l(this._last_last_text,["function","yield"])||this._flags.mode===m.ObjectLiteral&&l(this._last_last_text,["{",","])))&&(this._output.space_before_token=this._options.space_after_anon_function)}";"===this._flags.last_token.text||this._flags.last_token.type===u.START_BLOCK?this.print_newline():(this._flags.last_token.type===u.END_EXPR||this._flags.last_token.type===u.START_EXPR||this._flags.last_token.type===u.END_BLOCK||"."===this._flags.last_token.text||this._flags.last_token.type===u.COMMA)&&this.allow_wrap_or_preserved_newline(t,t.newlines),this.print_token(t),this.set_mode(e),this._options.space_in_paren&&(this._output.space_before_token=!0),this.indent()},w.prototype.handle_end_expr=function(t){for(;this._flags.mode===m.Statement;)this.restore_mode();this.handle_whitespace_and_comments(t),this._flags.multiline_frame&&this.allow_wrap_or_preserved_newline(t,"]"===t.text&&y(this._flags.mode)&&!this._options.keep_array_indentation),this._options.space_in_paren&&(this._flags.last_token.type!==u.START_EXPR||this._options.space_in_empty_paren?this._output.space_before_token=!0:(this._output.trim(),this._output.space_before_token=!1)),this.deindent(),this.print_token(t),this.restore_mode(),b(this._output,this._previous_flags),this._flags.do_while&&this._previous_flags.mode===m.Conditional&&(this._previous_flags.mode=m.Expression,this._flags.do_block=!1,this._flags.do_while=!1)},w.prototype.handle_start_block=function(t){this.handle_whitespace_and_comments(t);var e=this._tokens.peek(),i=this._tokens.peek(1);"switch"===this._flags.last_word&&this._flags.last_token.type===u.END_EXPR?(this.set_mode(m.BlockStatement),this._flags.in_case_statement=!0):this._flags.case_body?this.set_mode(m.BlockStatement):i&&(l(i.text,[":",","])&&l(e.type,[u.STRING,u.WORD,u.RESERVED])||l(e.text,["get","set","..."])&&l(i.type,[u.WORD,u.RESERVED]))?l(this._last_last_text,["class","interface"])&&!l(i.text,[":",","])?this.set_mode(m.BlockStatement):this.set_mode(m.ObjectLiteral):this._flags.last_token.type===u.OPERATOR&&"=>"===this._flags.last_token.text?this.set_mode(m.BlockStatement):l(this._flags.last_token.type,[u.EQUALS,u.START_EXPR,u.COMMA,u.OPERATOR])||c(this._flags.last_token,["return","throw","import","default"])?this.set_mode(m.ObjectLiteral):this.set_mode(m.BlockStatement),this._flags.last_token&&c(this._flags.last_token.previous,["class","extends"])&&(this._flags.class_start_block=!0);var n=!e.comments_before&&"}"===e.text,s=n&&"function"===this._flags.last_word&&this._flags.last_token.type===u.END_EXPR;if(this._options.brace_preserve_inline){var r=0,a=null;this._flags.inline_frame=!0;do if(r+=1,(a=this._tokens.peek(r-1)).newlines){this._flags.inline_frame=!1;break}while(a.type!==u.EOF&&!(a.type===u.END_BLOCK&&a.opened===t))}("expand"===this._options.brace_style||"none"===this._options.brace_style&&t.newlines)&&!this._flags.inline_frame?this._flags.last_token.type!==u.OPERATOR&&(s||this._flags.last_token.type===u.EQUALS||c(this._flags.last_token,d)&&"else"!==this._flags.last_token.text)?this._output.space_before_token=!0:this.print_newline(!1,!0):(y(this._previous_flags.mode)&&(this._flags.last_token.type===u.START_EXPR||this._flags.last_token.type===u.COMMA)&&((this._flags.last_token.type===u.COMMA||this._options.space_in_paren)&&(this._output.space_before_token=!0),(this._flags.last_token.type===u.COMMA||this._flags.last_token.type===u.START_EXPR&&this._flags.inline_frame)&&(this.allow_wrap_or_preserved_newline(t),this._previous_flags.multiline_frame=this._previous_flags.multiline_frame||this._flags.multiline_frame,this._flags.multiline_frame=!1)),this._flags.last_token.type!==u.OPERATOR&&this._flags.last_token.type!==u.START_EXPR&&(l(this._flags.last_token.type,[u.START_BLOCK,u.SEMICOLON])&&!this._flags.inline_frame?this.print_newline():this._output.space_before_token=!0)),this.print_token(t),this.indent(),n||this._options.brace_preserve_inline&&this._flags.inline_frame||this.print_newline()},w.prototype.handle_end_block=function(t){for(this.handle_whitespace_and_comments(t);this._flags.mode===m.Statement;)this.restore_mode();var e=this._flags.last_token.type===u.START_BLOCK;this._flags.inline_frame&&!e?this._output.space_before_token=!0:"expand"===this._options.brace_style?e||this.print_newline():e||(y(this._flags.mode)&&this._options.keep_array_indentation?(this._options.keep_array_indentation=!1,this.print_newline(),this._options.keep_array_indentation=!0):this.print_newline()),this.restore_mode(),this.print_token(t)},w.prototype.handle_word=function(t){if(t.type===u.RESERVED&&(l(t.text,["set","get"])&&this._flags.mode!==m.ObjectLiteral?t.type=u.WORD:"import"===t.text&&l(this._tokens.peek().text,["(","."])?t.type=u.WORD:l(t.text,["as","from"])&&!this._flags.import_block?t.type=u.WORD:this._flags.mode===m.ObjectLiteral&&":"===this._tokens.peek().text&&(t.type=u.WORD)),this.start_of_statement(t)?c(this._flags.last_token,["var","let","const"])&&t.type===u.WORD&&(this._flags.declaration_statement=!0):t.newlines&&!x(this._flags.mode)&&(this._flags.last_token.type!==u.OPERATOR||"--"===this._flags.last_token.text||"++"===this._flags.last_token.text)&&this._flags.last_token.type!==u.EQUALS&&(this._options.preserve_newlines||!c(this._flags.last_token,["var","let","const","set","get"]))?(this.handle_whitespace_and_comments(t),this.print_newline()):this.handle_whitespace_and_comments(t),this._flags.do_block&&!this._flags.do_while){if(p(t,"while")){this._output.space_before_token=!0,this.print_token(t),this._output.space_before_token=!0,this._flags.do_while=!0;return}this.print_newline(),this._flags.do_block=!1}if(this._flags.if_block){if(!this._flags.else_block&&p(t,"else"))this._flags.else_block=!0;else{for(;this._flags.mode===m.Statement;)this.restore_mode();this._flags.if_block=!1,this._flags.else_block=!1}}if(this._flags.in_case_statement&&c(t,["case","default"])){this.print_newline(),!this._flags.case_block&&(this._flags.case_body||this._options.jslint_happy)&&this.deindent(),this._flags.case_body=!1,this.print_token(t),this._flags.in_case=!0;return}if(this._flags.last_token.type!==u.COMMA&&this._flags.last_token.type!==u.START_EXPR&&this._flags.last_token.type!==u.EQUALS&&this._flags.last_token.type!==u.OPERATOR||this.start_of_object_property()||l(this._flags.last_token.text,["+","-"])&&":"===this._last_last_text&&this._flags.parent.mode===m.ObjectLiteral||this.allow_wrap_or_preserved_newline(t),p(t,"function")){(l(this._flags.last_token.text,["}",";"])||this._output.just_added_newline()&&!(l(this._flags.last_token.text,["(","[","{",":","=",","])||this._flags.last_token.type===u.OPERATOR))&&!this._output.just_added_blankline()&&!t.comments_before&&(this.print_newline(),this.print_newline(!0)),this._flags.last_token.type===u.RESERVED||this._flags.last_token.type===u.WORD?c(this._flags.last_token,["get","set","new","export"])||c(this._flags.last_token,k)?this._output.space_before_token=!0:p(this._flags.last_token,"default")&&"export"===this._last_last_text?this._output.space_before_token=!0:"declare"===this._flags.last_token.text?this._output.space_before_token=!0:this.print_newline():this._flags.last_token.type===u.OPERATOR||"="===this._flags.last_token.text?this._output.space_before_token=!0:!this._flags.multiline_frame&&(x(this._flags.mode)||y(this._flags.mode))||this.print_newline(),this.print_token(t),this._flags.last_word=t.text;return}var e="NONE";this._flags.last_token.type===u.END_BLOCK?this._previous_flags.inline_frame?e="SPACE":c(t,["else","catch","finally","from"])?"expand"===this._options.brace_style||"end-expand"===this._options.brace_style||"none"===this._options.brace_style&&t.newlines?e="NEWLINE":(e="SPACE",this._output.space_before_token=!0):e="NEWLINE":this._flags.last_token.type===u.SEMICOLON&&this._flags.mode===m.BlockStatement?e="NEWLINE":this._flags.last_token.type===u.SEMICOLON&&x(this._flags.mode)?e="SPACE":this._flags.last_token.type===u.STRING?e="NEWLINE":this._flags.last_token.type===u.RESERVED||this._flags.last_token.type===u.WORD||"*"===this._flags.last_token.text&&(l(this._last_last_text,["function","yield"])||this._flags.mode===m.ObjectLiteral&&l(this._last_last_text,["{",","]))?e="SPACE":this._flags.last_token.type===u.START_BLOCK?e=this._flags.inline_frame?"SPACE":"NEWLINE":this._flags.last_token.type===u.END_EXPR&&(this._output.space_before_token=!0,e="NEWLINE"),c(t,_)&&")"!==this._flags.last_token.text&&(e=this._flags.inline_frame||"else"===this._flags.last_token.text||"export"===this._flags.last_token.text?"SPACE":"NEWLINE"),c(t,["else","catch","finally"])?this._flags.last_token.type===u.END_BLOCK&&this._previous_flags.mode===m.BlockStatement&&"expand"!==this._options.brace_style&&"end-expand"!==this._options.brace_style&&("none"!==this._options.brace_style||!t.newlines)||this._flags.inline_frame?(this._output.trim(!0),"}"!==this._output.current_line.last()&&this.print_newline(),this._output.space_before_token=!0):this.print_newline():"NEWLINE"===e?c(this._flags.last_token,d)?this._output.space_before_token=!0:"declare"===this._flags.last_token.text&&c(t,["var","let","const"])?this._output.space_before_token=!0:this._flags.last_token.type!==u.END_EXPR?this._flags.last_token.type===u.START_EXPR&&c(t,["var","let","const"])||":"===this._flags.last_token.text||(p(t,"if")&&p(t.previous,"else")?this._output.space_before_token=!0:this.print_newline()):c(t,_)&&")"!==this._flags.last_token.text&&this.print_newline():this._flags.multiline_frame&&y(this._flags.mode)&&","===this._flags.last_token.text&&"}"===this._last_last_text?this.print_newline():"SPACE"===e&&(this._output.space_before_token=!0),t.previous&&(t.previous.type===u.WORD||t.previous.type===u.RESERVED)&&(this._output.space_before_token=!0),this.print_token(t),this._flags.last_word=t.text,t.type===u.RESERVED&&("do"===t.text?this._flags.do_block=!0:"if"===t.text?this._flags.if_block=!0:"import"===t.text?this._flags.import_block=!0:this._flags.import_block&&p(t,"from")&&(this._flags.import_block=!1))},w.prototype.handle_semicolon=function(t){this.start_of_statement(t)?this._output.space_before_token=!1:this.handle_whitespace_and_comments(t);for(var e=this._tokens.peek();this._flags.mode===m.Statement&&!(this._flags.if_block&&p(e,"else"))&&!this._flags.do_block;)this.restore_mode();this._flags.import_block&&(this._flags.import_block=!1),this.print_token(t)},w.prototype.handle_string=function(t){t.text.startsWith("`")&&0===t.newlines&&""===t.whitespace_before&&(")"===t.previous.text||this._flags.last_token.type===u.WORD)||(this.start_of_statement(t)?this._output.space_before_token=!0:(this.handle_whitespace_and_comments(t),this._flags.last_token.type===u.RESERVED||this._flags.last_token.type===u.WORD||this._flags.inline_frame?this._output.space_before_token=!0:this._flags.last_token.type===u.COMMA||this._flags.last_token.type===u.START_EXPR||this._flags.last_token.type===u.EQUALS||this._flags.last_token.type===u.OPERATOR?this.start_of_object_property()||this.allow_wrap_or_preserved_newline(t):t.text.startsWith("`")&&this._flags.last_token.type===u.END_EXPR&&("]"===t.previous.text||")"===t.previous.text)&&0===t.newlines?this._output.space_before_token=!0:this.print_newline())),this.print_token(t)},w.prototype.handle_equals=function(t){this.start_of_statement(t)||this.handle_whitespace_and_comments(t),this._flags.declaration_statement&&(this._flags.declaration_assignment=!0),this._output.space_before_token=!0,this.print_token(t),this._output.space_before_token=!0},w.prototype.handle_comma=function(t){this.handle_whitespace_and_comments(t,!0),this.print_token(t),this._output.space_before_token=!0,this._flags.declaration_statement?(x(this._flags.parent.mode)&&(this._flags.declaration_assignment=!1),this._flags.declaration_assignment?(this._flags.declaration_assignment=!1,this.print_newline(!1,!0)):this._options.comma_first&&this.allow_wrap_or_preserved_newline(t)):this._flags.mode===m.ObjectLiteral||this._flags.mode===m.Statement&&this._flags.parent.mode===m.ObjectLiteral?(this._flags.mode===m.Statement&&this.restore_mode(),this._flags.inline_frame||this.print_newline()):this._options.comma_first&&this.allow_wrap_or_preserved_newline(t)},w.prototype.handle_operator=function(t){var e="*"===t.text&&(c(this._flags.last_token,["function","yield"])||l(this._flags.last_token.type,[u.START_BLOCK,u.COMMA,u.END_BLOCK,u.SEMICOLON])),i=l(t.text,["-","+"])&&(l(this._flags.last_token.type,[u.START_BLOCK,u.START_EXPR,u.EQUALS,u.OPERATOR])||l(this._flags.last_token.text,_)||","===this._flags.last_token.text);if(this.start_of_statement(t)||this.handle_whitespace_and_comments(t,!e),"*"===t.text&&this._flags.last_token.type===u.DOT||"::"===t.text||l(t.text,["-","+"])&&this.start_of_object_property()){this.print_token(t);return}if(this._flags.last_token.type===u.OPERATOR&&l(this._options.operator_position,g)&&this.allow_wrap_or_preserved_newline(t),":"===t.text&&this._flags.in_case){this.print_token(t),this._flags.in_case=!1,this._flags.case_body=!0,this._tokens.peek().type!==u.START_BLOCK?(this.indent(),this.print_newline(),this._flags.case_block=!1):(this._flags.case_block=!0,this._output.space_before_token=!0);return}var n=!0,s=!0,r=!1;if(":"===t.text?0===this._flags.ternary_depth?n=!1:(this._flags.ternary_depth-=1,r=!0):"?"===t.text&&(this._flags.ternary_depth+=1),!i&&!e&&this._options.preserve_newlines&&l(t.text,h)){var a=":"===t.text,o=a&&r,p=a&&!r;switch(this._options.operator_position){case f.before_newline:this._output.space_before_token=!p,this.print_token(t),(!a||o)&&this.allow_wrap_or_preserved_newline(t),this._output.space_before_token=!0;return;case f.after_newline:this._output.space_before_token=!0,!a||o?this._tokens.peek().newlines?this.print_newline(!1,!0):this.allow_wrap_or_preserved_newline(t):this._output.space_before_token=!1,this.print_token(t),this._output.space_before_token=!0;return;case f.preserve_newline:p||this.allow_wrap_or_preserved_newline(t),n=!(this._output.just_added_newline()||p),this._output.space_before_token=n,this.print_token(t),this._output.space_before_token=!0;return}}if(e){this.allow_wrap_or_preserved_newline(t),n=!1;var b=this._tokens.peek();s=b&&l(b.type,[u.WORD,u.RESERVED])}else if("..."===t.text)this.allow_wrap_or_preserved_newline(t),n=this._flags.last_token.type===u.START_BLOCK,s=!1;else if(l(t.text,["--","++","!","~"])||i){if((this._flags.last_token.type===u.COMMA||this._flags.last_token.type===u.START_EXPR)&&this.allow_wrap_or_preserved_newline(t),n=!1,s=!1,t.newlines&&("--"===t.text||"++"===t.text||"~"===t.text)){var y=c(this._flags.last_token,d)&&t.newlines;y&&(this._previous_flags.if_block||this._previous_flags.else_block)&&this.restore_mode(),this.print_newline(y,!0)}";"===this._flags.last_token.text&&x(this._flags.mode)&&(n=!0),this._flags.last_token.type===u.RESERVED?n=!0:this._flags.last_token.type===u.END_EXPR?n=!("]"===this._flags.last_token.text&&("--"===t.text||"++"===t.text)):this._flags.last_token.type===u.OPERATOR&&(n=l(t.text,["--","-","++","+"])&&l(this._flags.last_token.text,["--","-","++","+"]),l(t.text,["+","-"])&&l(this._flags.last_token.text,["--","++"])&&(s=!0)),(this._flags.mode!==m.BlockStatement||this._flags.inline_frame)&&this._flags.mode!==m.Statement||"{"!==this._flags.last_token.text&&";"!==this._flags.last_token.text||this.print_newline()}this._output.space_before_token=this._output.space_before_token||n,this.print_token(t),this._output.space_before_token=s},w.prototype.handle_block_comment=function(t,e){if(this._output.raw){this._output.add_raw_token(t),t.directives&&"end"===t.directives.preserve&&(this._output.raw=this._options.test_output_raw);return}if(t.directives){this.print_newline(!1,e),this.print_token(t),"start"===t.directives.preserve&&(this._output.raw=!0),this.print_newline(!1,!0);return}if(r.newline.test(t.text)||t.newlines)this.print_block_commment(t,e);else{this._output.space_before_token=!0,this.print_token(t),this._output.space_before_token=!0;return}},w.prototype.print_block_commment=function(t,e){var i,n=function(t){t=t.replace(r.allLineBreaks,"\n");for(var e=[],i=t.indexOf("\n");-1!==i;)e.push(t.substring(0,i)),i=(t=t.substring(i+1)).indexOf("\n");return t.length&&e.push(t),e}(t.text),s=!1,a=!1,o=t.whitespace_before,_=o.length;if(this.print_newline(!1,e),this.print_token_line_indentation(t),this._output.add_token(n[0]),this.print_newline(!1,e),n.length>1){for(s=function(t,e){for(var i=0;i<t.length;i++)if("*"!==t[i].trim().charAt(0))return!1;return!0}(n=n.slice(1),0),a=function(t,e){for(var i,n=0,s=t.length;n<s;n++)if((i=t[n])&&0!==i.indexOf(e))return!1;return!0}(n,o),s&&(this._flags.alignment=1),i=0;i<n.length;i++)s?(this.print_token_line_indentation(t),this._output.add_token(n[i].replace(/^\s+/g,""))):a&&n[i]?(this.print_token_line_indentation(t),this._output.add_token(n[i].substring(_))):(this._output.current_line.set_indent(-1),this._output.add_token(n[i])),this.print_newline(!1,e);this._flags.alignment=0}},w.prototype.handle_comment=function(t,e){t.newlines?this.print_newline(!1,e):this._output.trim(!0),this._output.space_before_token=!0,this.print_token(t),this.print_newline(!1,e)},w.prototype.handle_dot=function(t){this.start_of_statement(t)||this.handle_whitespace_and_comments(t,!0),this._flags.last_token.text.match("^[0-9]+$")&&(this._output.space_before_token=!0),c(this._flags.last_token,d)?this._output.space_before_token=!1:this.allow_wrap_or_preserved_newline(t,")"===this._flags.last_token.text&&this._options.break_chained_methods),this._options.unindent_chained_methods&&this._output.just_added_newline()&&this.deindent(),this.print_token(t)},w.prototype.handle_unknown=function(t,e){this.print_token(t),"\n"===t.text[t.text.length-1]&&this.print_newline(!1,e)},w.prototype.handle_eof=function(t){for(;this._flags.mode===m.Statement;)this.restore_mode();this.handle_whitespace_and_comments(t)},t.exports.Beautifier=w},function(t){function e(t){this.__parent=t,this.__character_count=0,this.__indent_count=-1,this.__alignment_count=0,this.__wrap_point_index=0,this.__wrap_point_character_count=0,this.__wrap_point_indent_count=-1,this.__wrap_point_alignment_count=0,this.__items=[]}function i(t,e){this.__cache=[""],this.__indent_size=t.indent_size,this.__indent_string=t.indent_char,t.indent_with_tabs||(this.__indent_string=Array(t.indent_size+1).join(t.indent_char)),e=e||"",t.indent_level>0&&(e=Array(t.indent_level+1).join(this.__indent_string)),this.__base_string=e,this.__base_string_length=e.length}function n(t,n){this.__indent_cache=new i(t,n),this.raw=!1,this._end_with_newline=t.end_with_newline,this.indent_size=t.indent_size,this.wrap_line_length=t.wrap_line_length,this.indent_empty_lines=t.indent_empty_lines,this.__lines=[],this.previous_line=null,this.current_line=null,this.next_line=new e(this),this.space_before_token=!1,this.non_breaking_space=!1,this.previous_token_wrapped=!1,this.__add_outputline()}e.prototype.clone_empty=function(){var t=new e(this.__parent);return t.set_indent(this.__indent_count,this.__alignment_count),t},e.prototype.item=function(t){return t<0?this.__items[this.__items.length+t]:this.__items[t]},e.prototype.has_match=function(t){for(var e=this.__items.length-1;e>=0;e--)if(this.__items[e].match(t))return!0;return!1},e.prototype.set_indent=function(t,e){this.is_empty()&&(this.__indent_count=t||0,this.__alignment_count=e||0,this.__character_count=this.__parent.get_indent_size(this.__indent_count,this.__alignment_count))},e.prototype._set_wrap_point=function(){this.__parent.wrap_line_length&&(this.__wrap_point_index=this.__items.length,this.__wrap_point_character_count=this.__character_count,this.__wrap_point_indent_count=this.__parent.next_line.__indent_count,this.__wrap_point_alignment_count=this.__parent.next_line.__alignment_count)},e.prototype._should_wrap=function(){return this.__wrap_point_index&&this.__character_count>this.__parent.wrap_line_length&&this.__wrap_point_character_count>this.__parent.next_line.__character_count},e.prototype._allow_wrap=function(){if(this._should_wrap()){this.__parent.add_new_line();var t=this.__parent.current_line;return t.set_indent(this.__wrap_point_indent_count,this.__wrap_point_alignment_count),t.__items=this.__items.slice(this.__wrap_point_index),this.__items=this.__items.slice(0,this.__wrap_point_index),t.__character_count+=this.__character_count-this.__wrap_point_character_count,this.__character_count=this.__wrap_point_character_count," "===t.__items[0]&&(t.__items.splice(0,1),t.__character_count-=1),!0}return!1},e.prototype.is_empty=function(){return 0===this.__items.length},e.prototype.last=function(){return this.is_empty()?null:this.__items[this.__items.length-1]},e.prototype.push=function(t){this.__items.push(t);var e=t.lastIndexOf("\n");-1!==e?this.__character_count=t.length-e:this.__character_count+=t.length},e.prototype.pop=function(){var t=null;return this.is_empty()||(t=this.__items.pop(),this.__character_count-=t.length),t},e.prototype._remove_indent=function(){this.__indent_count>0&&(this.__indent_count-=1,this.__character_count-=this.__parent.indent_size)},e.prototype._remove_wrap_indent=function(){this.__wrap_point_indent_count>0&&(this.__wrap_point_indent_count-=1)},e.prototype.trim=function(){for(;" "===this.last();)this.__items.pop(),this.__character_count-=1},e.prototype.toString=function(){var t="";return this.is_empty()?this.__parent.indent_empty_lines&&(t=this.__parent.get_indent_string(this.__indent_count)):t=this.__parent.get_indent_string(this.__indent_count,this.__alignment_count)+this.__items.join(""),t},i.prototype.get_indent_size=function(t,e){var i=this.__base_string_length;return e=e||0,t<0&&(i=0),i+=t*this.__indent_size+e},i.prototype.get_indent_string=function(t,e){var i=this.__base_string;return t<0&&(t=0,i=""),e=(e||0)+t*this.__indent_size,this.__ensure_cache(e),i+=this.__cache[e]},i.prototype.__ensure_cache=function(t){for(;t>=this.__cache.length;)this.__add_column()},i.prototype.__add_column=function(){var t=this.__cache.length,e=0,i="";this.__indent_size&&t>=this.__indent_size&&(e=Math.floor(t/this.__indent_size),t-=e*this.__indent_size,i=Array(e+1).join(this.__indent_string)),t&&(i+=Array(t+1).join(" ")),this.__cache.push(i)},n.prototype.__add_outputline=function(){this.previous_line=this.current_line,this.current_line=this.next_line.clone_empty(),this.__lines.push(this.current_line)},n.prototype.get_line_number=function(){return this.__lines.length},n.prototype.get_indent_string=function(t,e){return this.__indent_cache.get_indent_string(t,e)},n.prototype.get_indent_size=function(t,e){return this.__indent_cache.get_indent_size(t,e)},n.prototype.is_empty=function(){return!this.previous_line&&this.current_line.is_empty()},n.prototype.add_new_line=function(t){return!(this.is_empty()||!t&&this.just_added_newline())&&(this.raw||this.__add_outputline(),!0)},n.prototype.get_code=function(t){this.trim(!0);var e=this.current_line.pop();e&&("\n"===e[e.length-1]&&(e=e.replace(/\n+$/g,"")),this.current_line.push(e)),this._end_with_newline&&this.__add_outputline();var i=this.__lines.join("\n");return"\n"!==t&&(i=i.replace(/[\n]/g,t)),i},n.prototype.set_wrap_point=function(){this.current_line._set_wrap_point()},n.prototype.set_indent=function(t,e){return(t=t||0,e=e||0,this.next_line.set_indent(t,e),this.__lines.length>1)?(this.current_line.set_indent(t,e),!0):(this.current_line.set_indent(),!1)},n.prototype.add_raw_token=function(t){for(var e=0;e<t.newlines;e++)this.__add_outputline();this.current_line.set_indent(-1),this.current_line.push(t.whitespace_before),this.current_line.push(t.text),this.space_before_token=!1,this.non_breaking_space=!1,this.previous_token_wrapped=!1},n.prototype.add_token=function(t){this.__add_space_before_token(),this.current_line.push(t),this.space_before_token=!1,this.non_breaking_space=!1,this.previous_token_wrapped=this.current_line._allow_wrap()},n.prototype.__add_space_before_token=function(){this.space_before_token&&!this.just_added_newline()&&(this.non_breaking_space||this.set_wrap_point(),this.current_line.push(" "))},n.prototype.remove_indent=function(t){for(var e=this.__lines.length;t<e;)this.__lines[t]._remove_indent(),t++;this.current_line._remove_wrap_indent()},n.prototype.trim=function(t){for(t=void 0!==t&&t,this.current_line.trim();t&&this.__lines.length>1&&this.current_line.is_empty();)this.__lines.pop(),this.current_line=this.__lines[this.__lines.length-1],this.current_line.trim();this.previous_line=this.__lines.length>1?this.__lines[this.__lines.length-2]:null},n.prototype.just_added_newline=function(){return this.current_line.is_empty()},n.prototype.just_added_blankline=function(){return this.is_empty()||this.current_line.is_empty()&&this.previous_line.is_empty()},n.prototype.ensure_empty_line_above=function(t,i){for(var n=this.__lines.length-2;n>=0;){var s=this.__lines[n];if(s.is_empty())break;if(0!==s.item(0).indexOf(t)&&s.item(-1)!==i){this.__lines.splice(n+1,0,new e(this)),this.previous_line=this.__lines[this.__lines.length-2];break}n--}},t.exports.Output=n},function(t){t.exports.Token=function(t,e,i,n){this.type=t,this.text=e,this.comments_before=null,this.newlines=i||0,this.whitespace_before=n||"",this.parent=null,this.next=null,this.previous=null,this.opened=null,this.closed=null,this.directives=null}},function(t,e){var i="\\x24\\x30-\\x39\\x41-\\x5a\\x5f\\x61-\\x7a",n="\\xaa\\xb5\\xba\\xc0-\\xd6\\xd8-\\xf6\\xf8-\\u02c1\\u02c6-\\u02d1\\u02e0-\\u02e4\\u02ec\\u02ee\\u0370-\\u0374\\u0376\\u0377\\u037a-\\u037d\\u0386\\u0388-\\u038a\\u038c\\u038e-\\u03a1\\u03a3-\\u03f5\\u03f7-\\u0481\\u048a-\\u0527\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u05d0-\\u05ea\\u05f0-\\u05f2\\u0620-\\u064a\\u066e\\u066f\\u0671-\\u06d3\\u06d5\\u06e5\\u06e6\\u06ee\\u06ef\\u06fa-\\u06fc\\u06ff\\u0710\\u0712-\\u072f\\u074d-\\u07a5\\u07b1\\u07ca-\\u07ea\\u07f4\\u07f5\\u07fa\\u0800-\\u0815\\u081a\\u0824\\u0828\\u0840-\\u0858\\u08a0\\u08a2-\\u08ac\\u0904-\\u0939\\u093d\\u0950\\u0958-\\u0961\\u0971-\\u0977\\u0979-\\u097f\\u0985-\\u098c\\u098f\\u0990\\u0993-\\u09a8\\u09aa-\\u09b0\\u09b2\\u09b6-\\u09b9\\u09bd\\u09ce\\u09dc\\u09dd\\u09df-\\u09e1\\u09f0\\u09f1\\u0a05-\\u0a0a\\u0a0f\\u0a10\\u0a13-\\u0a28\\u0a2a-\\u0a30\\u0a32\\u0a33\\u0a35\\u0a36\\u0a38\\u0a39\\u0a59-\\u0a5c\\u0a5e\\u0a72-\\u0a74\\u0a85-\\u0a8d\\u0a8f-\\u0a91\\u0a93-\\u0aa8\\u0aaa-\\u0ab0\\u0ab2\\u0ab3\\u0ab5-\\u0ab9\\u0abd\\u0ad0\\u0ae0\\u0ae1\\u0b05-\\u0b0c\\u0b0f\\u0b10\\u0b13-\\u0b28\\u0b2a-\\u0b30\\u0b32\\u0b33\\u0b35-\\u0b39\\u0b3d\\u0b5c\\u0b5d\\u0b5f-\\u0b61\\u0b71\\u0b83\\u0b85-\\u0b8a\\u0b8e-\\u0b90\\u0b92-\\u0b95\\u0b99\\u0b9a\\u0b9c\\u0b9e\\u0b9f\\u0ba3\\u0ba4\\u0ba8-\\u0baa\\u0bae-\\u0bb9\\u0bd0\\u0c05-\\u0c0c\\u0c0e-\\u0c10\\u0c12-\\u0c28\\u0c2a-\\u0c33\\u0c35-\\u0c39\\u0c3d\\u0c58\\u0c59\\u0c60\\u0c61\\u0c85-\\u0c8c\\u0c8e-\\u0c90\\u0c92-\\u0ca8\\u0caa-\\u0cb3\\u0cb5-\\u0cb9\\u0cbd\\u0cde\\u0ce0\\u0ce1\\u0cf1\\u0cf2\\u0d05-\\u0d0c\\u0d0e-\\u0d10\\u0d12-\\u0d3a\\u0d3d\\u0d4e\\u0d60\\u0d61\\u0d7a-\\u0d7f\\u0d85-\\u0d96\\u0d9a-\\u0db1\\u0db3-\\u0dbb\\u0dbd\\u0dc0-\\u0dc6\\u0e01-\\u0e30\\u0e32\\u0e33\\u0e40-\\u0e46\\u0e81\\u0e82\\u0e84\\u0e87\\u0e88\\u0e8a\\u0e8d\\u0e94-\\u0e97\\u0e99-\\u0e9f\\u0ea1-\\u0ea3\\u0ea5\\u0ea7\\u0eaa\\u0eab\\u0ead-\\u0eb0\\u0eb2\\u0eb3\\u0ebd\\u0ec0-\\u0ec4\\u0ec6\\u0edc-\\u0edf\\u0f00\\u0f40-\\u0f47\\u0f49-\\u0f6c\\u0f88-\\u0f8c\\u1000-\\u102a\\u103f\\u1050-\\u1055\\u105a-\\u105d\\u1061\\u1065\\u1066\\u106e-\\u1070\\u1075-\\u1081\\u108e\\u10a0-\\u10c5\\u10c7\\u10cd\\u10d0-\\u10fa\\u10fc-\\u1248\\u124a-\\u124d\\u1250-\\u1256\\u1258\\u125a-\\u125d\\u1260-\\u1288\\u128a-\\u128d\\u1290-\\u12b0\\u12b2-\\u12b5\\u12b8-\\u12be\\u12c0\\u12c2-\\u12c5\\u12c8-\\u12d6\\u12d8-\\u1310\\u1312-\\u1315\\u1318-\\u135a\\u1380-\\u138f\\u13a0-\\u13f4\\u1401-\\u166c\\u166f-\\u167f\\u1681-\\u169a\\u16a0-\\u16ea\\u16ee-\\u16f0\\u1700-\\u170c\\u170e-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176c\\u176e-\\u1770\\u1780-\\u17b3\\u17d7\\u17dc\\u1820-\\u1877\\u1880-\\u18a8\\u18aa\\u18b0-\\u18f5\\u1900-\\u191c\\u1950-\\u196d\\u1970-\\u1974\\u1980-\\u19ab\\u19c1-\\u19c7\\u1a00-\\u1a16\\u1a20-\\u1a54\\u1aa7\\u1b05-\\u1b33\\u1b45-\\u1b4b\\u1b83-\\u1ba0\\u1bae\\u1baf\\u1bba-\\u1be5\\u1c00-\\u1c23\\u1c4d-\\u1c4f\\u1c5a-\\u1c7d\\u1ce9-\\u1cec\\u1cee-\\u1cf1\\u1cf5\\u1cf6\\u1d00-\\u1dbf\\u1e00-\\u1f15\\u1f18-\\u1f1d\\u1f20-\\u1f45\\u1f48-\\u1f4d\\u1f50-\\u1f57\\u1f59\\u1f5b\\u1f5d\\u1f5f-\\u1f7d\\u1f80-\\u1fb4\\u1fb6-\\u1fbc\\u1fbe\\u1fc2-\\u1fc4\\u1fc6-\\u1fcc\\u1fd0-\\u1fd3\\u1fd6-\\u1fdb\\u1fe0-\\u1fec\\u1ff2-\\u1ff4\\u1ff6-\\u1ffc\\u2071\\u207f\\u2090-\\u209c\\u2102\\u2107\\u210a-\\u2113\\u2115\\u2119-\\u211d\\u2124\\u2126\\u2128\\u212a-\\u212d\\u212f-\\u2139\\u213c-\\u213f\\u2145-\\u2149\\u214e\\u2160-\\u2188\\u2c00-\\u2c2e\\u2c30-\\u2c5e\\u2c60-\\u2ce4\\u2ceb-\\u2cee\\u2cf2\\u2cf3\\u2d00-\\u2d25\\u2d27\\u2d2d\\u2d30-\\u2d67\\u2d6f\\u2d80-\\u2d96\\u2da0-\\u2da6\\u2da8-\\u2dae\\u2db0-\\u2db6\\u2db8-\\u2dbe\\u2dc0-\\u2dc6\\u2dc8-\\u2dce\\u2dd0-\\u2dd6\\u2dd8-\\u2dde\\u2e2f\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303c\\u3041-\\u3096\\u309d-\\u309f\\u30a1-\\u30fa\\u30fc-\\u30ff\\u3105-\\u312d\\u3131-\\u318e\\u31a0-\\u31ba\\u31f0-\\u31ff\\u3400-\\u4db5\\u4e00-\\u9fcc\\ua000-\\ua48c\\ua4d0-\\ua4fd\\ua500-\\ua60c\\ua610-\\ua61f\\ua62a\\ua62b\\ua640-\\ua66e\\ua67f-\\ua697\\ua6a0-\\ua6ef\\ua717-\\ua71f\\ua722-\\ua788\\ua78b-\\ua78e\\ua790-\\ua793\\ua7a0-\\ua7aa\\ua7f8-\\ua801\\ua803-\\ua805\\ua807-\\ua80a\\ua80c-\\ua822\\ua840-\\ua873\\ua882-\\ua8b3\\ua8f2-\\ua8f7\\ua8fb\\ua90a-\\ua925\\ua930-\\ua946\\ua960-\\ua97c\\ua984-\\ua9b2\\ua9cf\\uaa00-\\uaa28\\uaa40-\\uaa42\\uaa44-\\uaa4b\\uaa60-\\uaa76\\uaa7a\\uaa80-\\uaaaf\\uaab1\\uaab5\\uaab6\\uaab9-\\uaabd\\uaac0\\uaac2\\uaadb-\\uaadd\\uaae0-\\uaaea\\uaaf2-\\uaaf4\\uab01-\\uab06\\uab09-\\uab0e\\uab11-\\uab16\\uab20-\\uab26\\uab28-\\uab2e\\uabc0-\\uabe2\\uac00-\\ud7a3\\ud7b0-\\ud7c6\\ud7cb-\\ud7fb\\uf900-\\ufa6d\\ufa70-\\ufad9\\ufb00-\\ufb06\\ufb13-\\ufb17\\ufb1d\\ufb1f-\\ufb28\\ufb2a-\\ufb36\\ufb38-\\ufb3c\\ufb3e\\ufb40\\ufb41\\ufb43\\ufb44\\ufb46-\\ufbb1\\ufbd3-\\ufd3d\\ufd50-\\ufd8f\\ufd92-\\ufdc7\\ufdf0-\\ufdfb\\ufe70-\\ufe74\\ufe76-\\ufefc\\uff21-\\uff3a\\uff41-\\uff5a\\uff66-\\uffbe\\uffc2-\\uffc7\\uffca-\\uffcf\\uffd2-\\uffd7\\uffda-\\uffdc",s="\\u0300-\\u036f\\u0483-\\u0487\\u0591-\\u05bd\\u05bf\\u05c1\\u05c2\\u05c4\\u05c5\\u05c7\\u0610-\\u061a\\u0620-\\u0649\\u0672-\\u06d3\\u06e7-\\u06e8\\u06fb-\\u06fc\\u0730-\\u074a\\u0800-\\u0814\\u081b-\\u0823\\u0825-\\u0827\\u0829-\\u082d\\u0840-\\u0857\\u08e4-\\u08fe\\u0900-\\u0903\\u093a-\\u093c\\u093e-\\u094f\\u0951-\\u0957\\u0962-\\u0963\\u0966-\\u096f\\u0981-\\u0983\\u09bc\\u09be-\\u09c4\\u09c7\\u09c8\\u09d7\\u09df-\\u09e0\\u0a01-\\u0a03\\u0a3c\\u0a3e-\\u0a42\\u0a47\\u0a48\\u0a4b-\\u0a4d\\u0a51\\u0a66-\\u0a71\\u0a75\\u0a81-\\u0a83\\u0abc\\u0abe-\\u0ac5\\u0ac7-\\u0ac9\\u0acb-\\u0acd\\u0ae2-\\u0ae3\\u0ae6-\\u0aef\\u0b01-\\u0b03\\u0b3c\\u0b3e-\\u0b44\\u0b47\\u0b48\\u0b4b-\\u0b4d\\u0b56\\u0b57\\u0b5f-\\u0b60\\u0b66-\\u0b6f\\u0b82\\u0bbe-\\u0bc2\\u0bc6-\\u0bc8\\u0bca-\\u0bcd\\u0bd7\\u0be6-\\u0bef\\u0c01-\\u0c03\\u0c46-\\u0c48\\u0c4a-\\u0c4d\\u0c55\\u0c56\\u0c62-\\u0c63\\u0c66-\\u0c6f\\u0c82\\u0c83\\u0cbc\\u0cbe-\\u0cc4\\u0cc6-\\u0cc8\\u0cca-\\u0ccd\\u0cd5\\u0cd6\\u0ce2-\\u0ce3\\u0ce6-\\u0cef\\u0d02\\u0d03\\u0d46-\\u0d48\\u0d57\\u0d62-\\u0d63\\u0d66-\\u0d6f\\u0d82\\u0d83\\u0dca\\u0dcf-\\u0dd4\\u0dd6\\u0dd8-\\u0ddf\\u0df2\\u0df3\\u0e34-\\u0e3a\\u0e40-\\u0e45\\u0e50-\\u0e59\\u0eb4-\\u0eb9\\u0ec8-\\u0ecd\\u0ed0-\\u0ed9\\u0f18\\u0f19\\u0f20-\\u0f29\\u0f35\\u0f37\\u0f39\\u0f41-\\u0f47\\u0f71-\\u0f84\\u0f86-\\u0f87\\u0f8d-\\u0f97\\u0f99-\\u0fbc\\u0fc6\\u1000-\\u1029\\u1040-\\u1049\\u1067-\\u106d\\u1071-\\u1074\\u1082-\\u108d\\u108f-\\u109d\\u135d-\\u135f\\u170e-\\u1710\\u1720-\\u1730\\u1740-\\u1750\\u1772\\u1773\\u1780-\\u17b2\\u17dd\\u17e0-\\u17e9\\u180b-\\u180d\\u1810-\\u1819\\u1920-\\u192b\\u1930-\\u193b\\u1951-\\u196d\\u19b0-\\u19c0\\u19c8-\\u19c9\\u19d0-\\u19d9\\u1a00-\\u1a15\\u1a20-\\u1a53\\u1a60-\\u1a7c\\u1a7f-\\u1a89\\u1a90-\\u1a99\\u1b46-\\u1b4b\\u1b50-\\u1b59\\u1b6b-\\u1b73\\u1bb0-\\u1bb9\\u1be6-\\u1bf3\\u1c00-\\u1c22\\u1c40-\\u1c49\\u1c5b-\\u1c7d\\u1cd0-\\u1cd2\\u1d00-\\u1dbe\\u1e01-\\u1f15\\u200c\\u200d\\u203f\\u2040\\u2054\\u20d0-\\u20dc\\u20e1\\u20e5-\\u20f0\\u2d81-\\u2d96\\u2de0-\\u2dff\\u3021-\\u3028\\u3099\\u309a\\ua640-\\ua66d\\ua674-\\ua67d\\ua69f\\ua6f0-\\ua6f1\\ua7f8-\\ua800\\ua806\\ua80b\\ua823-\\ua827\\ua880-\\ua881\\ua8b4-\\ua8c4\\ua8d0-\\ua8d9\\ua8f3-\\ua8f7\\ua900-\\ua909\\ua926-\\ua92d\\ua930-\\ua945\\ua980-\\ua983\\ua9b3-\\ua9c0\\uaa00-\\uaa27\\uaa40-\\uaa41\\uaa4c-\\uaa4d\\uaa50-\\uaa59\\uaa7b\\uaae0-\\uaae9\\uaaf2-\\uaaf3\\uabc0-\\uabe1\\uabec\\uabed\\uabf0-\\uabf9\\ufb20-\\ufb28\\ufe00-\\ufe0f\\ufe20-\\ufe26\\ufe33\\ufe34\\ufe4d-\\ufe4f\\uff10-\\uff19\\uff3f",r="\\\\u[0-9a-fA-F]{4}|\\\\u\\{[0-9a-fA-F]+\\}",a="(?:"+r+"|[\\x23\\x24\\x40\\x41-\\x5a\\x5f\\x61-\\x7a"+n+"])";e.identifier=RegExp(a+("(?:"+r+"|["+i)+n+s+"])*","g"),e.identifierStart=new RegExp(a),e.identifierMatch=RegExp("(?:"+r+"|["+i+n+s+"])+"),e.newline=/[\n\r\u2028\u2029]/,e.lineBreak=RegExp("\r\n|"+e.newline.source),e.allLineBreaks=RegExp(e.lineBreak.source,"g")},function(t,e,i){var n=i(6).Options,s=["before-newline","after-newline","preserve-newline"];function r(t){n.call(this,t,"js");var e=this.raw_options.brace_style||null;"expand-strict"===e?this.raw_options.brace_style="expand":"collapse-preserve-inline"===e?this.raw_options.brace_style="collapse,preserve-inline":void 0!==this.raw_options.braces_on_own_line&&(this.raw_options.brace_style=this.raw_options.braces_on_own_line?"expand":"collapse");var i=this._get_selection_list("brace_style",["collapse","expand","end-expand","none","preserve-inline"]);this.brace_preserve_inline=!1,this.brace_style="collapse";for(var r=0;r<i.length;r++)"preserve-inline"===i[r]?this.brace_preserve_inline=!0:this.brace_style=i[r];this.unindent_chained_methods=this._get_boolean("unindent_chained_methods"),this.break_chained_methods=this._get_boolean("break_chained_methods"),this.space_in_paren=this._get_boolean("space_in_paren"),this.space_in_empty_paren=this._get_boolean("space_in_empty_paren"),this.jslint_happy=this._get_boolean("jslint_happy"),this.space_after_anon_function=this._get_boolean("space_after_anon_function"),this.space_after_named_function=this._get_boolean("space_after_named_function"),this.keep_array_indentation=this._get_boolean("keep_array_indentation"),this.space_before_conditional=this._get_boolean("space_before_conditional",!0),this.unescape_strings=this._get_boolean("unescape_strings"),this.e4x=this._get_boolean("e4x"),this.comma_first=this._get_boolean("comma_first"),this.operator_position=this._get_selection("operator_position",s),this.test_output_raw=this._get_boolean("test_output_raw"),this.jslint_happy&&(this.space_after_anon_function=!0)}r.prototype=new n,t.exports.Options=r},function(t){function e(t,e){this.raw_options=i(t,e),this.disabled=this._get_boolean("disabled"),this.eol=this._get_characters("eol","auto"),this.end_with_newline=this._get_boolean("end_with_newline"),this.indent_size=this._get_number("indent_size",4),this.indent_char=this._get_characters("indent_char"," "),this.indent_level=this._get_number("indent_level"),this.preserve_newlines=this._get_boolean("preserve_newlines",!0),this.max_preserve_newlines=this._get_number("max_preserve_newlines",32786),this.preserve_newlines||(this.max_preserve_newlines=0),this.indent_with_tabs=this._get_boolean("indent_with_tabs","	"===this.indent_char),this.indent_with_tabs&&(this.indent_char="	",1===this.indent_size&&(this.indent_size=4)),this.wrap_line_length=this._get_number("wrap_line_length",this._get_number("max_char")),this.indent_empty_lines=this._get_boolean("indent_empty_lines"),this.templating=this._get_selection_list("templating",["auto","none","angular","django","erb","handlebars","php","smarty"],["auto"])}function i(t,e){var i,s={};for(i in t=n(t))i!==e&&(s[i]=t[i]);if(e&&t[e])for(i in t[e])s[i]=t[e][i];return s}function n(t){var e,i={};for(e in t)i[e.replace(/-/g,"_")]=t[e];return i}e.prototype._get_array=function(t,e){var i=this.raw_options[t],n=e||[];return"object"==typeof i?null!==i&&"function"==typeof i.concat&&(n=i.concat()):"string"==typeof i&&(n=i.split(/[^a-zA-Z0-9_\/\-]+/)),n},e.prototype._get_boolean=function(t,e){var i=this.raw_options[t];return void 0===i?!!e:!!i},e.prototype._get_characters=function(t,e){var i=this.raw_options[t],n=e||"";return"string"==typeof i&&(n=i.replace(/\\r/,"\r").replace(/\\n/,"\n").replace(/\\t/,"	")),n},e.prototype._get_number=function(t,e){var i=this.raw_options[t];isNaN(e=parseInt(e,10))&&(e=0);var n=parseInt(i,10);return isNaN(n)&&(n=e),n},e.prototype._get_selection=function(t,e,i){var n=this._get_selection_list(t,e,i);if(1!==n.length)throw Error("Invalid Option Value: The option '"+t+"' can only be one of the following values:\n"+e+"\nYou passed in: '"+this.raw_options[t]+"'");return n[0]},e.prototype._get_selection_list=function(t,e,i){if(!e||0===e.length)throw Error("Selection list cannot be empty.");if(i=i||[e[0]],!this._is_valid_selection(i,e))throw Error("Invalid Default Value!");var n=this._get_array(t,i);if(!this._is_valid_selection(n,e))throw Error("Invalid Option Value: The option '"+t+"' can contain only the following values:\n"+e+"\nYou passed in: '"+this.raw_options[t]+"'");return n},e.prototype._is_valid_selection=function(t,e){return t.length&&e.length&&!t.some(function(t){return -1===e.indexOf(t)})},t.exports.Options=e,t.exports.normalizeOpts=n,t.exports.mergeOpts=i},function(t,e,i){var n,s=i(8).InputScanner,r=i(9).Tokenizer,a=i(9).TOKEN,o=i(13).Directives,_=i(4),h=i(12).Pattern,u=i(14).TemplatablePattern;function l(t,e){return -1!==e.indexOf(t)}var p={START_EXPR:"TK_START_EXPR",END_EXPR:"TK_END_EXPR",START_BLOCK:"TK_START_BLOCK",END_BLOCK:"TK_END_BLOCK",WORD:"TK_WORD",RESERVED:"TK_RESERVED",SEMICOLON:"TK_SEMICOLON",STRING:"TK_STRING",EQUALS:"TK_EQUALS",OPERATOR:"TK_OPERATOR",COMMA:"TK_COMMA",BLOCK_COMMENT:"TK_BLOCK_COMMENT",COMMENT:"TK_COMMENT",DOT:"TK_DOT",UNKNOWN:"TK_UNKNOWN",START:a.START,RAW:a.RAW,EOF:a.EOF},c=new o(/\/\*/,/\*\//),d=/0[xX][0123456789abcdefABCDEF_]*n?|0[oO][01234567_]*n?|0[bB][01_]*n?|\d[\d_]*n|(?:\.\d[\d_]*|\d[\d_]*\.?[\d_]*)(?:[eE][+-]?[\d_]+)?/,f=/[0-9]/,g=/[^\d\.]/,m=">>>= ... >>= <<= === >>> !== **= &&= ??= ||= => ^= :: /= << <= == && -= >= >> != -- += ** || ?? ++ %= &= *= |= |> = ! ? > < : / ^ - + * & % ~ |",b=new RegExp(m=(m="\\?\\.(?!\\d) "+(m=m.replace(/[-[\]{}()*+?.,\\^$|#]/g,"\\$&"))).replace(/ /g,"|")),y="continue,try,throw,return,var,let,const,if,switch,case,default,for,while,break,function,import,export".split(","),x=RegExp("^(?:"+y.concat(["do","in","of","else","get","set","new","catch","finally","typeof","yield","async","await","from","as","class","extends"]).join("|")+")$"),w=function(t,e){r.call(this,t,e),this._patterns.whitespace=this._patterns.whitespace.matching(/\u00A0\u1680\u180e\u2000-\u200a\u202f\u205f\u3000\ufeff/.source,/\u2028\u2029/.source);var i=new h(this._input),n=new u(this._input).read_options(this._options);this.__patterns={template:n,identifier:n.starting_with(_.identifier).matching(_.identifierMatch),number:i.matching(d),punct:i.matching(b),comment:i.starting_with(/\/\//).until(/[\n\r\u2028\u2029]/),block_comment:i.starting_with(/\/\*/).until_after(/\*\//),html_comment_start:i.matching(/<!--/),html_comment_end:i.matching(/-->/),include:i.starting_with(/#include/).until_after(_.lineBreak),shebang:i.starting_with(/#!/).until_after(_.lineBreak),xml:i.matching(/[\s\S]*?<(\/?)([-a-zA-Z:0-9_.]+|{[^}]+?}|!\[CDATA\[[^\]]*?\]\]|)(\s*{[^}]+?}|\s+[-a-zA-Z:0-9_.]+|\s+[-a-zA-Z:0-9_.]+\s*=\s*('[^']*'|"[^"]*"|{([^{}]|{[^}]+?})+?}))*\s*(\/?)\s*>/),single_quote:n.until(/['\\\n\r\u2028\u2029]/),double_quote:n.until(/["\\\n\r\u2028\u2029]/),template_text:n.until(/[`\\$]/),template_expression:n.until(/[`}\\]/)}};w.prototype=new r,w.prototype._is_comment=function(t){return t.type===p.COMMENT||t.type===p.BLOCK_COMMENT||t.type===p.UNKNOWN},w.prototype._is_opening=function(t){return t.type===p.START_BLOCK||t.type===p.START_EXPR},w.prototype._is_closing=function(t,e){return(t.type===p.END_BLOCK||t.type===p.END_EXPR)&&e&&("]"===t.text&&"["===e.text||")"===t.text&&"("===e.text||"}"===t.text&&"{"===e.text)},w.prototype._reset=function(){n=!1},w.prototype._get_next_token=function(t,e){var i=null;this._readWhitespace();var n=this._input.peek();return null===n?this._create_token(p.EOF,""):i=(i=(i=(i=(i=(i=(i=(i=(i=(i=i||this._read_non_javascript(n))||this._read_string(n))||this._read_pair(n,this._input.peek(1)))||this._read_word(t))||this._read_singles(n))||this._read_comment(n))||this._read_regexp(n,t))||this._read_xml(n,t))||this._read_punctuation())||this._create_token(p.UNKNOWN,this._input.next())},w.prototype._read_word=function(t){var e;return""!==(e=this.__patterns.identifier.read())?(e=e.replace(_.allLineBreaks,"\n"),!(t.type===p.DOT||t.type===p.RESERVED&&("set"===t.text||"get"===t.text))&&x.test(e))?("in"===e||"of"===e)&&(t.type===p.WORD||t.type===p.STRING)?this._create_token(p.OPERATOR,e):this._create_token(p.RESERVED,e):this._create_token(p.WORD,e):""!==(e=this.__patterns.number.read())?this._create_token(p.WORD,e):void 0},w.prototype._read_singles=function(t){var e=null;return"("===t||"["===t?e=this._create_token(p.START_EXPR,t):")"===t||"]"===t?e=this._create_token(p.END_EXPR,t):"{"===t?e=this._create_token(p.START_BLOCK,t):"}"===t?e=this._create_token(p.END_BLOCK,t):";"===t?e=this._create_token(p.SEMICOLON,t):"."===t&&g.test(this._input.peek(1))?e=this._create_token(p.DOT,t):","===t&&(e=this._create_token(p.COMMA,t)),e&&this._input.next(),e},w.prototype._read_pair=function(t,e){var i=null;return"#"===t&&"{"===e&&(i=this._create_token(p.START_BLOCK,t+e)),i&&(this._input.next(),this._input.next()),i},w.prototype._read_punctuation=function(){var t=this.__patterns.punct.read();if(""!==t)return"="===t?this._create_token(p.EQUALS,t):"?."===t?this._create_token(p.DOT,t):this._create_token(p.OPERATOR,t)},w.prototype._read_non_javascript=function(t){var e="";if("#"===t){if(this._is_first_token()&&(e=this.__patterns.shebang.read())||(e=this.__patterns.include.read()))return this._create_token(p.UNKNOWN,e.trim()+"\n");t=this._input.next();var i="#";if(this._input.hasNext()&&this._input.testChar(f)){do i+=t=this._input.next();while(this._input.hasNext()&&"#"!==t&&"="!==t);return"#"===t||("["===this._input.peek()&&"]"===this._input.peek(1)?(i+="[]",this._input.next(),this._input.next()):"{"===this._input.peek()&&"}"===this._input.peek(1)&&(i+="{}",this._input.next(),this._input.next())),this._create_token(p.WORD,i)}this._input.back()}else if("<"===t&&this._is_first_token()){if(e=this.__patterns.html_comment_start.read()){for(;this._input.hasNext()&&!this._input.testChar(_.newline);)e+=this._input.next();return n=!0,this._create_token(p.COMMENT,e)}}else if(n&&"-"===t&&(e=this.__patterns.html_comment_end.read()))return n=!1,this._create_token(p.COMMENT,e);return null},w.prototype._read_comment=function(t){var e=null;if("/"===t){var i="";if("*"===this._input.peek(1)){i=this.__patterns.block_comment.read();var n=c.get_directives(i);n&&"start"===n.ignore&&(i+=c.readIgnored(this._input)),i=i.replace(_.allLineBreaks,"\n"),(e=this._create_token(p.BLOCK_COMMENT,i)).directives=n}else"/"===this._input.peek(1)&&(i=this.__patterns.comment.read(),e=this._create_token(p.COMMENT,i))}return e},w.prototype._read_string=function(t){if("`"===t||"'"===t||'"'===t){var e=this._input.next();return this.has_char_escapes=!1,"`"===t?e+=this._read_string_recursive("`",!0,"${"):e+=this._read_string_recursive(t),this.has_char_escapes&&this._options.unescape_strings&&(e=function(t){for(var e="",i=0,n=new s(t),r=null;n.hasNext();)if((r=n.match(/([\s]|[^\\]|\\\\)+/g))&&(e+=r[0]),"\\"===n.peek()){if(n.next(),"x"===n.peek())r=n.match(/x([0-9A-Fa-f]{2})/g);else if("u"===n.peek())(r=n.match(/u([0-9A-Fa-f]{4})/g))||(r=n.match(/u\{([0-9A-Fa-f]+)\}/g));else{e+="\\",n.hasNext()&&(e+=n.next());continue}if(!r||(i=parseInt(r[1],16))>126&&i<=255&&0===r[0].indexOf("x"))return t;i>=0&&i<32?e+="\\"+r[0]:i>1114111?e+="\\"+r[0]:34===i||39===i||92===i?e+="\\"+String.fromCharCode(i):e+=String.fromCharCode(i)}return e}(e)),this._input.peek()===t&&(e+=this._input.next()),e=e.replace(_.allLineBreaks,"\n"),this._create_token(p.STRING,e)}return null},w.prototype._allow_regexp_or_xml=function(t){return t.type===p.RESERVED&&l(t.text,["return","case","throw","else","do","typeof","yield"])||t.type===p.END_EXPR&&")"===t.text&&t.opened.previous.type===p.RESERVED&&l(t.opened.previous.text,["if","while","for"])||l(t.type,[p.COMMENT,p.START_EXPR,p.START_BLOCK,p.START,p.END_BLOCK,p.OPERATOR,p.EQUALS,p.EOF,p.SEMICOLON,p.COMMA])},w.prototype._read_regexp=function(t,e){if("/"===t&&this._allow_regexp_or_xml(e)){for(var i=this._input.next(),n=!1,s=!1;this._input.hasNext()&&(n||s||this._input.peek()!==t)&&!this._input.testChar(_.newline);)i+=this._input.peek(),n?n=!1:(n="\\"===this._input.peek(),"["===this._input.peek()?s=!0:"]"===this._input.peek()&&(s=!1)),this._input.next();return this._input.peek()===t&&(i+=this._input.next()+this._input.read(_.identifier)),this._create_token(p.STRING,i)}return null},w.prototype._read_xml=function(t,e){if(this._options.e4x&&"<"===t&&this._allow_regexp_or_xml(e)){var i="",n=this.__patterns.xml.read_match();if(n){for(var s=n[2].replace(/^{\s+/,"{").replace(/\s+}$/,"}"),r=0===s.indexOf("{"),a=0;n;){var o=!!n[1],h=n[2];if(!(n[n.length-1]||"![CDATA["===h.slice(0,8))&&(h===s||r&&h.replace(/^{\s+/,"{").replace(/\s+}$/,"}"))&&(o?--a:++a),i+=n[0],a<=0)break;n=this.__patterns.xml.read_match()}return n||(i+=this._input.match(/[\s\S]*/g)[0]),i=i.replace(_.allLineBreaks,"\n"),this._create_token(p.STRING,i)}}return null},w.prototype._read_string_recursive=function(t,e,i){"'"===t?s=this.__patterns.single_quote:'"'===t?s=this.__patterns.double_quote:"`"===t?s=this.__patterns.template_text:"}"===t&&(s=this.__patterns.template_expression);for(var n,s,r=s.read(),a="";this._input.hasNext();){if((a=this._input.next())===t||!e&&_.newline.test(a)){this._input.back();break}"\\"===a&&this._input.hasNext()?("x"===(n=this._input.peek())||"u"===n?this.has_char_escapes=!0:"\r"===n&&"\n"===this._input.peek(1)&&this._input.next(),a+=this._input.next()):i&&("${"===i&&"$"===a&&"{"===this._input.peek()&&(a+=this._input.next()),i===a&&("`"===t?a+=this._read_string_recursive("}",e,"`"):a+=this._read_string_recursive("`",e,"${"),this._input.hasNext()&&(a+=this._input.next()))),a+=s.read(),r+=a}return r},t.exports.Tokenizer=w,t.exports.TOKEN=p,t.exports.positionable_operators=">>> === !== &&= ??= ||= << && >= ** != == <= >> || ?? |> < / - + > : & % ? ^ | *".split(" ").slice(),t.exports.line_starters=y.slice()},function(t){var e=RegExp.prototype.hasOwnProperty("sticky");function i(t){this.__input=t||"",this.__input_length=this.__input.length,this.__position=0}i.prototype.restart=function(){this.__position=0},i.prototype.back=function(){this.__position>0&&(this.__position-=1)},i.prototype.hasNext=function(){return this.__position<this.__input_length},i.prototype.next=function(){var t=null;return this.hasNext()&&(t=this.__input.charAt(this.__position),this.__position+=1),t},i.prototype.peek=function(t){var e=null;return(t=(t||0)+this.__position)>=0&&t<this.__input_length&&(e=this.__input.charAt(t)),e},i.prototype.__match=function(t,i){t.lastIndex=i;var n=t.exec(this.__input);return n&&!(e&&t.sticky)&&n.index!==i&&(n=null),n},i.prototype.test=function(t,e){return(e=(e||0)+this.__position)>=0&&e<this.__input_length&&!!this.__match(t,e)},i.prototype.testChar=function(t,e){var i=this.peek(e);return t.lastIndex=0,null!==i&&t.test(i)},i.prototype.match=function(t){var e=this.__match(t,this.__position);return e?this.__position+=e[0].length:e=null,e},i.prototype.read=function(t,e,i){var n,s="";return t&&(n=this.match(t))&&(s+=n[0]),e&&(n||!t)&&(s+=this.readUntil(e,i)),s},i.prototype.readUntil=function(t,e){var i="",n=this.__position;t.lastIndex=this.__position;var s=t.exec(this.__input);return s?(n=s.index,e&&(n+=s[0].length)):n=this.__input_length,i=this.__input.substring(this.__position,n),this.__position=n,i},i.prototype.readUntilAfter=function(t){return this.readUntil(t,!0)},i.prototype.get_regexp=function(t,i){var n=null,s="g";return i&&e&&(s="y"),"string"==typeof t&&""!==t?n=new RegExp(t,s):t&&(n=new RegExp(t.source,s)),n},i.prototype.get_literal_regexp=function(t){return RegExp(t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&"))},i.prototype.peekUntilAfter=function(t){var e=this.__position,i=this.readUntilAfter(t);return this.__position=e,i},i.prototype.lookBack=function(t){var e=this.__position-1;return e>=t.length&&this.__input.substring(e-t.length,e).toLowerCase()===t},t.exports.InputScanner=i},function(t,e,i){var n=i(8).InputScanner,s=i(3).Token,r=i(10).TokenStream,a=i(11).WhitespacePattern,o={START:"TK_START",RAW:"TK_RAW",EOF:"TK_EOF"},_=function(t,e){this._input=new n(t),this._options=e||{},this.__tokens=null,this._patterns={},this._patterns.whitespace=new a(this._input)};_.prototype.tokenize=function(){this._input.restart(),this.__tokens=new r,this._reset();for(var t,e=new s(o.START,""),i=null,n=[],a=new r;e.type!==o.EOF;){for(t=this._get_next_token(e,i);this._is_comment(t);)a.add(t),t=this._get_next_token(e,i);a.isEmpty()||(t.comments_before=a,a=new r),t.parent=i,this._is_opening(t)?(n.push(i),i=t):i&&this._is_closing(t,i)&&(t.opened=i,i.closed=t,i=n.pop(),t.parent=i),t.previous=e,e.next=t,this.__tokens.add(t),e=t}return this.__tokens},_.prototype._is_first_token=function(){return this.__tokens.isEmpty()},_.prototype._reset=function(){},_.prototype._get_next_token=function(t,e){this._readWhitespace();var i=this._input.read(/.+/g);return i?this._create_token(o.RAW,i):this._create_token(o.EOF,"")},_.prototype._is_comment=function(t){return!1},_.prototype._is_opening=function(t){return!1},_.prototype._is_closing=function(t,e){return!1},_.prototype._create_token=function(t,e){return new s(t,e,this._patterns.whitespace.newline_count,this._patterns.whitespace.whitespace_before_token)},_.prototype._readWhitespace=function(){return this._patterns.whitespace.read()},t.exports.Tokenizer=_,t.exports.TOKEN=o},function(t){function e(t){this.__tokens=[],this.__tokens_length=this.__tokens.length,this.__position=0,this.__parent_token=t}e.prototype.restart=function(){this.__position=0},e.prototype.isEmpty=function(){return 0===this.__tokens_length},e.prototype.hasNext=function(){return this.__position<this.__tokens_length},e.prototype.next=function(){var t=null;return this.hasNext()&&(t=this.__tokens[this.__position],this.__position+=1),t},e.prototype.peek=function(t){var e=null;return(t=(t||0)+this.__position)>=0&&t<this.__tokens_length&&(e=this.__tokens[t]),e},e.prototype.add=function(t){this.__parent_token&&(t.parent=this.__parent_token),this.__tokens.push(t),this.__tokens_length+=1},t.exports.TokenStream=e},function(t,e,i){var n=i(12).Pattern;function s(t,e){n.call(this,t,e),e?this._line_regexp=this._input.get_regexp(e._line_regexp):this.__set_whitespace_patterns("",""),this.newline_count=0,this.whitespace_before_token=""}s.prototype=new n,s.prototype.__set_whitespace_patterns=function(t,e){t+="\\t ",e+="\\n\\r",this._match_pattern=this._input.get_regexp("["+t+e+"]+",!0),this._newline_regexp=this._input.get_regexp("\\r\\n|["+e+"]")},s.prototype.read=function(){this.newline_count=0,this.whitespace_before_token="";var t=this._input.read(this._match_pattern);if(" "===t)this.whitespace_before_token=" ";else if(t){var e=this.__split(this._newline_regexp,t);this.newline_count=e.length-1,this.whitespace_before_token=e[this.newline_count]}return t},s.prototype.matching=function(t,e){var i=this._create();return i.__set_whitespace_patterns(t,e),i._update(),i},s.prototype._create=function(){return new s(this._input,this)},s.prototype.__split=function(t,e){t.lastIndex=0;for(var i=0,n=[],s=t.exec(e);s;)n.push(e.substring(i,s.index)),i=s.index+s[0].length,s=t.exec(e);return i<e.length?n.push(e.substring(i,e.length)):n.push(""),n},t.exports.WhitespacePattern=s},function(t){function e(t,e){this._input=t,this._starting_pattern=null,this._match_pattern=null,this._until_pattern=null,this._until_after=!1,e&&(this._starting_pattern=this._input.get_regexp(e._starting_pattern,!0),this._match_pattern=this._input.get_regexp(e._match_pattern,!0),this._until_pattern=this._input.get_regexp(e._until_pattern),this._until_after=e._until_after)}e.prototype.read=function(){var t=this._input.read(this._starting_pattern);return(!this._starting_pattern||t)&&(t+=this._input.read(this._match_pattern,this._until_pattern,this._until_after)),t},e.prototype.read_match=function(){return this._input.match(this._match_pattern)},e.prototype.until_after=function(t){var e=this._create();return e._until_after=!0,e._until_pattern=this._input.get_regexp(t),e._update(),e},e.prototype.until=function(t){var e=this._create();return e._until_after=!1,e._until_pattern=this._input.get_regexp(t),e._update(),e},e.prototype.starting_with=function(t){var e=this._create();return e._starting_pattern=this._input.get_regexp(t,!0),e._update(),e},e.prototype.matching=function(t){var e=this._create();return e._match_pattern=this._input.get_regexp(t,!0),e._update(),e},e.prototype._create=function(){return new e(this._input,this)},e.prototype._update=function(){},t.exports.Pattern=e},function(t){function e(t,e){t="string"==typeof t?t:t.source,e="string"==typeof e?e:e.source,this.__directives_block_pattern=RegExp(t+/ beautify( \w+[:]\w+)+ /.source+e,"g"),this.__directive_pattern=/ (\w+)[:](\w+)/g,this.__directives_end_ignore_pattern=RegExp(t+/\sbeautify\signore:end\s/.source+e,"g")}e.prototype.get_directives=function(t){if(!t.match(this.__directives_block_pattern))return null;var e={};this.__directive_pattern.lastIndex=0;for(var i=this.__directive_pattern.exec(t);i;)e[i[1]]=i[2],i=this.__directive_pattern.exec(t);return e},e.prototype.readIgnored=function(t){return t.readUntilAfter(this.__directives_end_ignore_pattern)},t.exports.Directives=e},function(t,e,i){var n=i(12).Pattern,s={django:!1,erb:!1,handlebars:!1,php:!1,smarty:!1,angular:!1};function r(t,e){n.call(this,t,e),this.__template_pattern=null,this._disabled=Object.assign({},s),this._excluded=Object.assign({},s),e&&(this.__template_pattern=this._input.get_regexp(e.__template_pattern),this._excluded=Object.assign(this._excluded,e._excluded),this._disabled=Object.assign(this._disabled,e._disabled));var i=new n(t);this.__patterns={handlebars_comment:i.starting_with(/{{!--/).until_after(/--}}/),handlebars_unescaped:i.starting_with(/{{{/).until_after(/}}}/),handlebars:i.starting_with(/{{/).until_after(/}}/),php:i.starting_with(/<\?(?:[= ]|php)/).until_after(/\?>/),erb:i.starting_with(/<%[^%]/).until_after(/[^%]%>/),django:i.starting_with(/{%/).until_after(/%}/),django_value:i.starting_with(/{{/).until_after(/}}/),django_comment:i.starting_with(/{#/).until_after(/#}/),smarty:i.starting_with(/{(?=[^}{\s\n])/).until_after(/[^\s\n]}/),smarty_comment:i.starting_with(/{\*/).until_after(/\*}/),smarty_literal:i.starting_with(/{literal}/).until_after(/{\/literal}/)}}r.prototype=new n,r.prototype._create=function(){return new r(this._input,this)},r.prototype._update=function(){this.__set_templated_pattern()},r.prototype.disable=function(t){var e=this._create();return e._disabled[t]=!0,e._update(),e},r.prototype.read_options=function(t){var e=this._create();for(var i in s)e._disabled[i]=-1===t.templating.indexOf(i);return e._update(),e},r.prototype.exclude=function(t){var e=this._create();return e._excluded[t]=!0,e._update(),e},r.prototype.read=function(){var t="";t=this._match_pattern?this._input.read(this._starting_pattern):this._input.read(this._starting_pattern,this.__template_pattern);for(var e=this._read_template();e;)this._match_pattern?e+=this._input.read(this._match_pattern):e+=this._input.readUntil(this.__template_pattern),t+=e,e=this._read_template();return this._until_after&&(t+=this._input.readUntilAfter(this._until_pattern)),t},r.prototype.__set_templated_pattern=function(){var t=[];this._disabled.php||t.push(this.__patterns.php._starting_pattern.source),this._disabled.handlebars||t.push(this.__patterns.handlebars._starting_pattern.source),this._disabled.erb||t.push(this.__patterns.erb._starting_pattern.source),this._disabled.django||(t.push(this.__patterns.django._starting_pattern.source),t.push(this.__patterns.django_value._starting_pattern.source),t.push(this.__patterns.django_comment._starting_pattern.source)),this._disabled.smarty||t.push(this.__patterns.smarty._starting_pattern.source),this._until_pattern&&t.push(this._until_pattern.source),this.__template_pattern=this._input.get_regexp("(?:"+t.join("|")+")")},r.prototype._read_template=function(){var t="",e=this._input.peek();if("<"===e){var i=this._input.peek(1);this._disabled.php||this._excluded.php||"?"!==i||(t=t||this.__patterns.php.read()),this._disabled.erb||this._excluded.erb||"%"!==i||(t=t||this.__patterns.erb.read())}else"{"===e&&(this._disabled.handlebars||this._excluded.handlebars||(t=(t=(t=t||this.__patterns.handlebars_comment.read())||this.__patterns.handlebars_unescaped.read())||this.__patterns.handlebars.read()),this._disabled.django||(this._excluded.django||this._excluded.handlebars||(t=t||this.__patterns.django_value.read()),this._excluded.django||(t=(t=t||this.__patterns.django_comment.read())||this.__patterns.django.read())),!this._disabled.smarty&&this._disabled.django&&this._disabled.handlebars&&(t=(t=(t=t||this.__patterns.smarty_comment.read())||this.__patterns.smarty_literal.read())||this.__patterns.smarty.read()));return t},t.exports.TemplatablePattern=r}],s={};var n,s,r=function t(e){var i=s[e];if(void 0!==i)return i.exports;var r=s[e]={exports:{}};return n[e](r,r.exports,t),r.exports}(0);void 0!==(i=(function(){return{js_beautify:r}}).apply(e,[]))&&(t.exports=i)}()},62650:(t,e,i)=>{"use strict";i.r(e),i.d(e,{plainTextSelectors:()=>iI,render:()=>iB,renderAsync:()=>iW});var n,s,r,a,o,_,h,u,l,p,c,d,f=i(84492);!function(t){t.Root="root",t.Text="text",t.Directive="directive",t.Comment="comment",t.Script="script",t.Style="style",t.Tag="tag",t.CDATA="cdata",t.Doctype="doctype"}(n||(n={}));let g=n.Root,m=n.Text,b=n.Directive,y=n.Comment,x=n.Script,w=n.Style,k=n.Tag,v=n.CDATA,E=n.Doctype;class T{constructor(){this.parent=null,this.prev=null,this.next=null,this.startIndex=null,this.endIndex=null}get parentNode(){return this.parent}set parentNode(t){this.parent=t}get previousSibling(){return this.prev}set previousSibling(t){this.prev=t}get nextSibling(){return this.next}set nextSibling(t){this.next=t}cloneNode(t=!1){return P(this,t)}}class S extends T{constructor(t){super(),this.data=t}get nodeValue(){return this.data}set nodeValue(t){this.data=t}}class L extends S{constructor(){super(...arguments),this.type=n.Text}get nodeType(){return 3}}class A extends S{constructor(){super(...arguments),this.type=n.Comment}get nodeType(){return 8}}class N extends S{constructor(t,e){super(e),this.name=t,this.type=n.Directive}get nodeType(){return 1}}class O extends T{constructor(t){super(),this.children=t}get firstChild(){var t;return null!==(t=this.children[0])&&void 0!==t?t:null}get lastChild(){return this.children.length>0?this.children[this.children.length-1]:null}get childNodes(){return this.children}set childNodes(t){this.children=t}}class I extends O{constructor(){super(...arguments),this.type=n.CDATA}get nodeType(){return 4}}class C extends O{constructor(){super(...arguments),this.type=n.Root}get nodeType(){return 9}}class R extends O{constructor(t,e,i=[],s="script"===t?n.Script:"style"===t?n.Style:n.Tag){super(i),this.name=t,this.attribs=e,this.type=s}get nodeType(){return 1}get tagName(){return this.name}set tagName(t){this.name=t}get attributes(){return Object.keys(this.attribs).map(t=>{var e,i;return{name:t,value:this.attribs[t],namespace:null===(e=this["x-attribsNamespace"])||void 0===e?void 0:e[t],prefix:null===(i=this["x-attribsPrefix"])||void 0===i?void 0:i[t]}})}}function B(t){return t.type===n.Tag||t.type===n.Script||t.type===n.Style}function D(t){return t.type===n.Text}function P(t,e=!1){let i;if(D(t))i=new L(t.data);else if(t.type===n.Comment)i=new A(t.data);else if(B(t)){let n=e?W(t.children):[],s=new R(t.name,{...t.attribs},n);n.forEach(t=>t.parent=s),null!=t.namespace&&(s.namespace=t.namespace),t["x-attribsNamespace"]&&(s["x-attribsNamespace"]={...t["x-attribsNamespace"]}),t["x-attribsPrefix"]&&(s["x-attribsPrefix"]={...t["x-attribsPrefix"]}),i=s}else if(t.type===n.CDATA){let n=e?W(t.children):[],s=new I(n);n.forEach(t=>t.parent=s),i=s}else if(t.type===n.Root){let n=e?W(t.children):[],s=new C(n);n.forEach(t=>t.parent=s),t["x-mode"]&&(s["x-mode"]=t["x-mode"]),i=s}else if(t.type===n.Directive){let e=new N(t.name,t.data);null!=t["x-name"]&&(e["x-name"]=t["x-name"],e["x-publicId"]=t["x-publicId"],e["x-systemId"]=t["x-systemId"]),i=e}else throw Error(`Not implemented yet: ${t.type}`);return i.startIndex=t.startIndex,i.endIndex=t.endIndex,null!=t.sourceCodeLocation&&(i.sourceCodeLocation=t.sourceCodeLocation),i}function W(t){let e=t.map(t=>P(t,!0));for(let t=1;t<e.length;t++)e[t].prev=e[t-1],e[t-1].next=e[t];return e}let q={withStartIndices:!1,withEndIndices:!1,xmlMode:!1};class j{constructor(t,e,i){this.dom=[],this.root=new C(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null,"function"==typeof e&&(i=e,e=q),"object"==typeof t&&(e=t,t=void 0),this.callback=null!=t?t:null,this.options=null!=e?e:q,this.elementCB=null!=i?i:null}onparserinit(t){this.parser=t}onreset(){this.dom=[],this.root=new C(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null}onend(){this.done||(this.done=!0,this.parser=null,this.handleCallback(null))}onerror(t){this.handleCallback(t)}onclosetag(){this.lastNode=null;let t=this.tagStack.pop();this.options.withEndIndices&&(t.endIndex=this.parser.endIndex),this.elementCB&&this.elementCB(t)}onopentag(t,e){let i=new R(t,e,void 0,this.options.xmlMode?n.Tag:void 0);this.addNode(i),this.tagStack.push(i)}ontext(t){let{lastNode:e}=this;if(e&&e.type===n.Text)e.data+=t,this.options.withEndIndices&&(e.endIndex=this.parser.endIndex);else{let e=new L(t);this.addNode(e),this.lastNode=e}}oncomment(t){if(this.lastNode&&this.lastNode.type===n.Comment){this.lastNode.data+=t;return}let e=new A(t);this.addNode(e),this.lastNode=e}oncommentend(){this.lastNode=null}oncdatastart(){let t=new L(""),e=new I([t]);this.addNode(e),t.parent=e,this.lastNode=t}oncdataend(){this.lastNode=null}onprocessinginstruction(t,e){let i=new N(t,e);this.addNode(i)}handleCallback(t){if("function"==typeof this.callback)this.callback(t,this.dom);else if(t)throw t}addNode(t){let e=this.tagStack[this.tagStack.length-1],i=e.children[e.children.length-1];this.options.withStartIndices&&(t.startIndex=this.parser.startIndex),this.options.withEndIndices&&(t.endIndex=this.parser.endIndex),e.children.push(t),i&&(t.prev=i,i.next=t),t.parent=e,this.lastNode=null}}let M=/\n/g;function U(t,e="",i={}){let n="string"==typeof e?e:"",s=t.map(z),r=!!("string"!=typeof e?e:i).lineNumbers;return function(t,e=0){let i=r?function(t){let e=[...t.matchAll(M)].map(t=>t.index||0);e.unshift(-1);let i=function t(e,i,n){if(n-i==1)return{offset:e[i],index:i+1};let s=Math.ceil((i+n)/2),r=t(e,i,s),a=t(e,s,n);return{offset:r.offset,low:r,high:a}}(e,0,e.length);return t=>(function t(e,i){return Object.prototype.hasOwnProperty.call(e,"index")?{line:e.index,column:i-e.offset}:t(e.high.offset<i?e.high:e.low,i)})(i,t)}(t):()=>({line:0,column:0}),a=e,o=[];t:for(;a<t.length;){let e=!1;for(let r of s){r.regex.lastIndex=a;let s=r.regex.exec(t);if(s&&s[0].length>0){if(!r.discard){let t=i(a),e="string"==typeof r.replace?s[0].replace(new RegExp(r.regex.source,r.regex.flags),r.replace):s[0];o.push({state:n,name:r.name,text:e,offset:a,len:s[0].length,line:t.line,column:t.column})}if(a=r.regex.lastIndex,e=!0,r.push){let e=r.push(t,a);o.push(...e.tokens),a=e.offset}if(r.pop)break t;break}}if(!e)break}return{tokens:o,offset:a,complete:t.length<=a}}}function z(t,e){return{...t,regex:function(t,e){if(0===t.name.length)throw Error(`Rule #${e} has empty name, which is not allowed.`);if(Object.prototype.hasOwnProperty.call(t,"regex"))return function(t){if(t.global)throw Error(`Regular expression /${t.source}/${t.flags} contains the global flag, which is not allowed.`);return t.sticky?t:RegExp(t.source,t.flags+"y")}(t.regex);if(Object.prototype.hasOwnProperty.call(t,"str")){if(0===t.str.length)throw Error(`Rule #${e} ("${t.name}") has empty "str" property, which is not allowed.`);return RegExp(V(t.str),"y")}return RegExp(V(t.name),"y")}(t,e)}}function V(t){return t.replace(/[-[\]{}()*+!<=:?./\\^$|#\s,]/g,"\\$&")}function $(t,e){return(i,n)=>{let s,r=n;return n<i.tokens.length?void 0!==(s=t(i.tokens[n],i,n))&&r++:e?.(i,n),void 0===s?{matched:!1}:{matched:!0,position:r,value:s}}}function F(t,e){return t.matched?{matched:!0,position:t.position,value:e(t.value,t.position)}:t}function G(t,e){return t.matched?e(t):t}function K(t,e){return(i,n)=>F(t(i,n),(t,s)=>e(t,i,n,s))}function H(t,e){return(i,n)=>{let s=t(i,n);return s.matched?s:{matched:!0,position:n,value:e}}}function X(...t){return(e,i)=>{for(let n of t){let t=n(e,i);if(t.matched)return t}return{matched:!1}}}function Q(t,e){return(i,n)=>{let s=t(i,n);return s.matched?s:e(i,n)}}function Z(t){var e;return e=()=>!0,(i,n)=>{let s=[],r=!0;do{let a=t(i,n);a.matched&&e(a.value,s.length+1,i,n,a.position)?(s.push(a.value),n=a.position):r=!1}while(r);return{matched:!0,position:n,value:s}}}function Y(t,e,i){return(n,s)=>G(t(n,s),t=>F(e(n,t.position),(e,r)=>i(t.value,e,n,s,r)))}function J(t,e){return Y(t,e,(t,e)=>e)}function tt(t,e,i,n){return(s,r)=>G(t(s,r),t=>G(e(s,t.position),e=>F(i(s,e.position),(i,a)=>n(t.value,e.value,i,s,r,a))))}function te(t,e,i){return tt(t,e,i,(t,e)=>e)}function ti(t,e,i){var n,s;return n=t,s=t=>{var n,s,r;return n=Y(e,i,(t,e)=>[t,e]),s=(t,[e,i])=>e(t,i),r=t=>K(n,(e,i,n,r)=>s(t,e,i,n,r)),(e,i)=>{let n=!0,s=t,a=i;do{let t=r(s,e,a)(e,a);t.matched?(s=t.value,a=t.position):n=!1}while(n);return{matched:!0,position:a,value:s}}},(t,e)=>G(n(t,e),i=>s(i.value,t,e,i.position)(t,i.position))}let tn=`(?:\\n|\\r\\n|\\r|\\f)`,ts=`[^\\x00-\\x7F]`,tr=`(?:\\\\[0-9a-f]{1,6}(?:\\r\\n|[ \\n\\r\\t\\f])?)`,ta=`(?:\\\\[^\\n\\r\\f0-9a-f])`,to=`(?:[_a-z]|${ts}|${tr}|${ta})`,t_=`(?:[_a-z0-9-]|${ts}|${tr}|${ta})`,th=`(?:${t_}+)`,tu=`(?:[-]?${to}${t_}*)`,tl=`'([^\\n\\r\\f\\\\']|\\\\${tn}|${ts}|${tr}|${ta})*'`,tp=`"([^\\n\\r\\f\\\\"]|\\\\${tn}|${ts}|${tr}|${ta})*"`,tc=U([{name:"ws",regex:RegExp(`(?:[ \\t\\r\\n\\f]*)`)},{name:"hash",regex:RegExp(`#${th}`,"i")},{name:"ident",regex:RegExp(tu,"i")},{name:"str1",regex:RegExp(tl,"i")},{name:"str2",regex:RegExp(tp,"i")},{name:"*"},{name:"."},{name:","},{name:"["},{name:"]"},{name:"="},{name:">"},{name:"|"},{name:"+"},{name:"~"},{name:"^"},{name:"$"}]),td=U([{name:"unicode",regex:RegExp(tr,"i")},{name:"escape",regex:RegExp(ta,"i")},{name:"any",regex:RegExp("[\\s\\S]","i")}]);function tf([t,e,i],[n,s,r]){return[t+n,e+s,i+r]}let tg=K(Z(X($(t=>"unicode"===t.name?String.fromCodePoint(parseInt(t.text.slice(1),16)):void 0),$(t=>"escape"===t.name?t.text.slice(1):void 0),$(t=>"any"===t.name?t.text:void 0))),t=>t.join(""));function tm(t){return tg({tokens:td(t).tokens,options:void 0},0).value}function tb(t){return $(e=>e.name===t||void 0)}let ty=$(t=>"ws"===t.name?null:void 0),tx=H(ty,null);function tw(t){return te(tx,t,tx)}let tk=$(t=>"ident"===t.name?tm(t.text):void 0),tv=$(t=>"hash"===t.name?tm(t.text.slice(1)):void 0),tE=$(t=>t.name.startsWith("str")?tm(t.text.slice(1,-1)):void 0),tT=Y(H(tk,""),tb("|"),t=>t),tS=Q(Y(tT,tk,(t,e)=>({name:e,namespace:t})),K(tk,t=>({name:t,namespace:null}))),tL=Q(Y(tT,tb("*"),t=>({type:"universal",namespace:t,specificity:[0,0,0]})),K(tb("*"),()=>({type:"universal",namespace:null,specificity:[0,0,0]}))),tA=K(tS,({name:t,namespace:e})=>({type:"tag",name:t,namespace:e,specificity:[0,0,1]})),tN=Y(tb("."),tk,(t,e)=>({type:"class",name:e,specificity:[0,1,0]})),tO=K(tv,t=>({type:"id",name:t,specificity:[1,0,0]})),tI=$(t=>{if("ident"===t.name){if("i"===t.text||"I"===t.text)return"i";if("s"===t.text||"S"===t.text)return"s"}}),tC=Q(Y(tE,H(J(tx,tI),null),(t,e)=>({value:t,modifier:e})),Y(tk,H(J(ty,tI),null),(t,e)=>({value:t,modifier:e}))),tR=X(K(tb("="),()=>"="),Y(tb("~"),tb("="),()=>"~="),Y(tb("|"),tb("="),()=>"|="),Y(tb("^"),tb("="),()=>"^="),Y(tb("$"),tb("="),()=>"$="),Y(tb("*"),tb("="),()=>"*=")),tB=Q(tt(tb("["),tw(tS),tb("]"),(t,{name:e,namespace:i})=>({type:"attrPresence",name:e,namespace:i,specificity:[0,1,0]})),te(tb("["),tt(tw(tS),tR,tw(tC),({name:t,namespace:e},i,{value:n,modifier:s})=>({type:"attrValue",name:t,namespace:e,matcher:i,value:n,modifier:s,specificity:[0,1,0]})),tb("]"))),tD=Q(tL,tA),tP=X(tO,tN,tB),tW=K(Q(function(...t){return K(function(...t){return(e,i)=>{let n=[],s=i;for(let i of t){let t=i(e,s);if(!t.matched)return{matched:!1};n.push(t.value),s=t.position}return{matched:!0,position:s,value:n}}}(...t),t=>t.flatMap(t=>t))}(tD,Z(tP)),function(t){return Y(t,Z(t),(t,e)=>[t,...e])}(tP)),t=>({type:"compound",list:t,specificity:t.map(t=>t.specificity).reduce(tf,[0,0,0])})),tq=Q(tw(X(K(tb(">"),()=>">"),K(tb("+"),()=>"+"),K(tb("~"),()=>"~"),Y(tb("|"),tb("|"),()=>"||"))),K(ty,()=>" ")),tj=ti(tW,K(tq,t=>(e,i)=>({type:"compound",list:[...i.list,{type:"combinator",combinator:t,left:e,specificity:e.specificity}],specificity:tf(e.specificity,i.specificity)})),tW);function tM(t,e,i=1){return`${t.replace(/(\t)|(\r)|(\n)/g,(t,e,i)=>e?"␉":i?"␍":"␊")}
${"".padEnd(e)}${"^".repeat(i)}`}function tU(t){if(!t.type)throw Error("This is not an AST node.");switch(t.type){case"universal":return tz(t.namespace)+"*";case"tag":return tz(t.namespace)+t$(t.name);case"class":return"."+t$(t.name);case"id":return"#"+t$(t.name);case"attrPresence":return`[${tz(t.namespace)}${t$(t.name)}]`;case"attrValue":return`[${tz(t.namespace)}${t$(t.name)}${t.matcher}"${t.value.replace(/(")|(\\)|(\x00)|([\x01-\x1f]|\x7f)/g,(t,e,i,n,s)=>e?'\\"':i?"\\\\":n?"�":tV(s))}"${t.modifier?t.modifier:""}]`;case"combinator":return tU(t.left)+t.combinator;case"compound":return t.list.reduce((t,e)=>"combinator"===e.type?tU(e)+t:t+tU(e),"");case"list":return t.list.map(tU).join(",")}}function tz(t){return t||""===t?t$(t)+"|":""}function tV(t){return`\\${t.codePointAt(0).toString(16)} `}function t$(t){return t.replace(/(^[0-9])|(^-[0-9])|(^-$)|([-0-9a-zA-Z_]|[^\x00-\x7F])|(\x00)|([\x01-\x1f]|\x7f)|([\s\S])/g,(t,e,i,n,s,r,a,o)=>e?tV(e):i?"-"+tV(i.slice(1)):n?"\\-":s||(r?"�":a?tV(a):"\\"+o))}function tF(t){switch(t.type){case"universal":case"tag":return[1];case"id":return[2];case"class":return[3,t.name];case"attrPresence":return[4,tU(t)];case"attrValue":return[5,tU(t)];case"combinator":return[15,tU(t)]}}function tG(t,e){if(!Array.isArray(t)||!Array.isArray(e))throw Error("Arguments must be arrays.");let i=t.length<e.length?t.length:e.length;for(let n=0;n<i;n++)if(t[n]!==e[n])return t[n]<e[n]?-1:1;return t.length-e.length}ti(K(tj,t=>({type:"list",list:[t]})),K(tw(tb(",")),()=>(t,e)=>({type:"list",list:[...t.list,e]})),tj);class tK{constructor(t){this.branches=tH(function(t){let e=t.length,i=Array(e);for(let s=0;s<e;s++){var n;let[e,r]=t[s],a=(function t(e){let i=[];e.list.forEach(e=>{switch(e.type){case"class":i.push({matcher:"~=",modifier:null,name:"class",namespace:null,specificity:e.specificity,type:"attrValue",value:e.name});break;case"id":i.push({matcher:"=",modifier:null,name:"id",namespace:null,specificity:e.specificity,type:"attrValue",value:e.name});break;case"combinator":t(e.left),i.push(e);break;case"universal":break;default:i.push(e)}}),e.list=i}(n=function(t,e){if(!("string"==typeof e||e instanceof String))throw Error("Expected a selector string. Actual input is not a string!");let i=tc(e);if(!i.complete)throw Error(`The input "${e}" was only partially tokenized, stopped at offset ${i.offset}!
`+tM(e,i.offset));let n=tw(t)({tokens:i.tokens,options:void 0},0);if(!n.matched)throw Error(`No match for "${e}" input!`);if(n.position<i.tokens.length){let t=i.tokens[n.position];throw Error(`The input "${e}" was only partially parsed, stopped at offset ${t.offset}!
`+tM(e,t.offset,t.len))}return n.value}(tj,e)),function t(e){if(!e.type)throw Error("This is not an AST node.");switch(e.type){case"compound":e.list.forEach(t),e.list.sort((t,e)=>tG(tF(t),tF(e)));break;case"combinator":t(e.left);break;case"list":e.list.forEach(t),e.list.sort((t,e)=>tU(t)<tU(e)?-1:1)}return e}(n),n);i[s]={ast:a,terminal:{type:"terminal",valueContainer:{index:s,value:r,specificity:a.specificity}}}}return i}(t))}build(t){return t(this.branches)}}function tH(t){let e=[];for(;t.length;){let i=tJ(t,t=>!0,tX),{matches:n,nonmatches:s,empty:r}=function(t,e){let i=[],n=[],s=[];for(let r of t){let t=r.ast.list;t.length?(t.some(t=>tX(t)===e)?i:n).push(r):s.push(r)}return{matches:i,nonmatches:n,empty:s}}(t,i);t=s,n.length&&e.push(function(t,e){if("tag"===t)return{type:"tagName",variants:Object.entries(tZ(e,t=>"tag"===t.type,t=>t.name)).map(([t,e])=>({type:"variant",value:t,cont:tH(e.items)}))};if(t.startsWith("attrValue "))return function(t,e){let i=tZ(e,e=>"attrValue"===e.type&&e.name===t,t=>`${t.matcher} ${t.modifier||""} ${t.value}`),n=[];for(let t of Object.values(i)){let e=t.oneSimpleSelector,i=function(t){if("i"===t.modifier){let e=t.value.toLowerCase();switch(t.matcher){case"=":return t=>e===t.toLowerCase();case"~=":return t=>t.toLowerCase().split(/[ \t]+/).includes(e);case"^=":return t=>t.toLowerCase().startsWith(e);case"$=":return t=>t.toLowerCase().endsWith(e);case"*=":return t=>t.toLowerCase().includes(e);case"|=":return t=>{let i=t.toLowerCase();return e===i||i.startsWith(e)&&"-"===i[e.length]}}}else{let e=t.value;switch(t.matcher){case"=":return t=>e===t;case"~=":return t=>t.split(/[ \t]+/).includes(e);case"^=":return t=>t.startsWith(e);case"$=":return t=>t.endsWith(e);case"*=":return t=>t.includes(e);case"|=":return t=>e===t||t.startsWith(e)&&"-"===t[e.length]}}}(e),s=tH(t.items);n.push({type:"matcher",matcher:e.matcher,modifier:e.modifier,value:e.value,predicate:i,cont:s})}return{type:"attrValue",name:t,matchers:n}}(t.substring(10),e);if(t.startsWith("attrPresence "))return function(t,e){for(let i of e)tY(i,e=>"attrPresence"===e.type&&e.name===t);return{type:"attrPresence",name:t,cont:tH(e)}}(t.substring(13),e);if("combinator >"===t)return tQ(">",e);if("combinator +"===t)return tQ("+",e);throw Error(`Unsupported selector kind: ${t}`)}(i,n)),r.length&&e.push(...function(t){let e=[];for(let i of t){let t=i.terminal;if("terminal"===t.type)e.push(t);else{let{matches:i,rest:n}=function(t,e){let i=[],n=[];for(let s of t)e(s)?i.push(s):n.push(s);return{matches:i,rest:n}}(t.cont,t=>"terminal"===t.type);i.forEach(t=>e.push(t)),n.length&&(t.cont=n,e.push(t))}}return e}(r))}return e}function tX(t){switch(t.type){case"attrPresence":return`attrPresence ${t.name}`;case"attrValue":return`attrValue ${t.name}`;case"combinator":return`combinator ${t.combinator}`;default:return t.type}}function tQ(t,e){let i=tZ(e,e=>"combinator"===e.type&&e.combinator===t,t=>tU(t.left)),n=[];for(let t of Object.values(i)){let e=tH(t.items),i=t.oneSimpleSelector.left;n.push({ast:i,terminal:{type:"popElement",cont:e}})}return{type:"pushElement",combinator:t,cont:tH(n)}}function tZ(t,e,i){let n={};for(;t.length;){let s=tJ(t,e,i),r=t=>e(t)&&i(t)===s,{matches:a,rest:o}=function(t,e){let i=[],n=[];for(let s of t)e(s)?i.push(s):n.push(s);return{matches:i,rest:n}}(t,t=>t.ast.list.some(r)),_=null;for(let t of a){let e=tY(t,r);_||(_=e)}if(null==_)throw Error("No simple selector is found.");n[s]={oneSimpleSelector:_,items:a},t=o}return n}function tY(t,e){let i=t.ast.list,n=Array(i.length),s=-1;for(let t=i.length;t-- >0;)e(i[t])&&(n[t]=!0,s=t);if(-1==s)throw Error("Couldn't find the required simple selector.");let r=i[s];return t.ast.list=i.filter((t,e)=>!n[e]),r}function tJ(t,e,i){let n={};for(let s of t){let t={};for(let n of s.ast.list.filter(e))t[i(n)]=!0;for(let e of Object.keys(t))n[e]?n[e]++:n[e]=1}let s="",r=0;for(let t of Object.entries(n))t[1]>r&&(s=t[0],r=t[1]);return s}class t0{constructor(t){this.f=t}pickAll(t){return this.f(t)}pick1(t,e=!1){let i=this.f(t),n=i.length;if(0===n)return null;if(1===n)return i[0].value;let s=e?t1:t2,r=i[0];for(let t=1;t<n;t++){let e=i[t];s(r,e)&&(r=e)}return r.value}}function t1(t,e){let i=tG(e.specificity,t.specificity);return i>0||0===i&&e.index<t.index}function t2(t,e){let i=tG(e.specificity,t.specificity);return i>0||0===i&&e.index>t.index}function t3(t){return new t0(t8(t))}function t8(t){let e=t.map(t5);return(t,...i)=>e.flatMap(e=>e(t,...i))}function t5(t){switch(t.type){case"terminal":{let e=[t.valueContainer];return(t,...i)=>e}case"tagName":return function(t){let e={};for(let i of t.variants)e[i.value]=t8(i.cont);return(t,...i)=>{let n=e[t.name];return n?n(t,...i):[]}}(t);case"attrValue":return function(t){let e=[];for(let i of t.matchers){let t=i.predicate,n=t8(i.cont);e.push((e,i,...s)=>t(e)?n(i,...s):[])}let i=t.name;return(t,...n)=>{let s=t.attribs[i];return s||""===s?e.flatMap(e=>e(s,t,...n)):[]}}(t);case"attrPresence":return function(t){let e=t.name,i=t8(t.cont);return(t,...n)=>Object.prototype.hasOwnProperty.call(t.attribs,e)?i(t,...n):[]}(t);case"pushElement":return function(t){let e=t8(t.cont),i="+"===t.combinator?t6:t9;return(t,...n)=>{let s=i(t);return null===s?[]:e(s,t,...n)}}(t);case"popElement":return function(t){let e=t8(t.cont);return(t,i,...n)=>e(i,...n)}(t)}}let t6=t=>{let e=t.prev;return null===e?null:B(e)?e:t6(e)},t9=t=>{let e=t.parent;return e&&B(e)?e:null},t4=new Uint16Array('ᵁ<\xd5ıʊҝջאٵ۞ޢߖࠏ੊ઑඡ๭༉༦჊ረዡᐕᒝᓃᓟᔥ\0\0\0\0\0\0ᕫᛍᦍᰒᷝ὾⁠↰⊍⏀⏻⑂⠤⤒ⴈ⹈⿎〖㊺㘹㞬㣾㨨㩱㫠㬮ࠀEMabcfglmnoprstu\\bfms\x7f\x84\x8b\x90\x95\x98\xa6\xb3\xb9\xc8\xcflig耻\xc6䃆P耻&䀦cute耻\xc1䃁reve;䄂Āiyx}rc耻\xc2䃂;䐐r;쀀\ud835\udd04rave耻\xc0䃀pha;䎑acr;䄀d;橓Āgp\x9d\xa1on;䄄f;쀀\ud835\udd38plyFunction;恡ing耻\xc5䃅Ācs\xbe\xc3r;쀀\ud835\udc9cign;扔ilde耻\xc3䃃ml耻\xc4䃄Ѐaceforsu\xe5\xfb\xfeėĜĢħĪĀcr\xea\xf2kslash;或Ŷ\xf6\xf8;櫧ed;挆y;䐑ƀcrtąċĔause;戵noullis;愬a;䎒r;쀀\ud835\udd05pf;쀀\ud835\udd39eve;䋘c\xf2ēmpeq;扎܀HOacdefhilorsuōőŖƀƞƢƵƷƺǜȕɳɸɾcy;䐧PY耻\xa9䂩ƀcpyŝŢźute;䄆Ā;iŧŨ拒talDifferentialD;慅leys;愭ȀaeioƉƎƔƘron;䄌dil耻\xc7䃇rc;䄈nint;戰ot;䄊ĀdnƧƭilla;䂸terDot;䂷\xf2ſi;䎧rcleȀDMPTǇǋǑǖot;抙inus;抖lus;投imes;抗oĀcsǢǸkwiseContourIntegral;戲eCurlyĀDQȃȏoubleQuote;思uote;怙ȀlnpuȞȨɇɕonĀ;eȥȦ户;橴ƀgitȯȶȺruent;扡nt;戯ourIntegral;戮ĀfrɌɎ;愂oduct;成nterClockwiseContourIntegral;戳oss;樯cr;쀀\ud835\udc9epĀ;Cʄʅ拓ap;才րDJSZacefiosʠʬʰʴʸˋ˗ˡ˦̳ҍĀ;oŹʥtrahd;椑cy;䐂cy;䐅cy;䐏ƀgrsʿ˄ˇger;怡r;憡hv;櫤Āayː˕ron;䄎;䐔lĀ;t˝˞戇a;䎔r;쀀\ud835\udd07Āaf˫̧Ācm˰̢riticalȀADGT̖̜̀̆cute;䂴oŴ̋̍;䋙bleAcute;䋝rave;䁠ilde;䋜ond;拄ferentialD;慆Ѱ̽\0\0\0͔͂\0Ѕf;쀀\ud835\udd3bƀ;DE͈͉͍䂨ot;惜qual;扐blèCDLRUVͣͲ΂ϏϢϸontourIntegra\xecȹoɴ͹\0\0ͻ\xbb͉nArrow;懓Āeo·ΤftƀARTΐΖΡrrow;懐ightArrow;懔e\xe5ˊngĀLRΫτeftĀARγιrrow;柸ightArrow;柺ightArrow;柹ightĀATϘϞrrow;懒ee;抨pɁϩ\0\0ϯrrow;懑ownArrow;懕erticalBar;戥ǹABLRTaВЪаўѿͼrrowƀ;BUНОТ憓ar;椓pArrow;懵reve;䌑eft˒к\0ц\0ѐightVector;楐eeVector;楞ectorĀ;Bљњ憽ar;楖ightǔѧ\0ѱeeVector;楟ectorĀ;BѺѻ懁ar;楗eeĀ;A҆҇护rrow;憧ĀctҒҗr;쀀\ud835\udc9frok;䄐ࠀNTacdfglmopqstuxҽӀӄӋӞӢӧӮӵԡԯԶՒ՝ՠեG;䅊H耻\xd0䃐cute耻\xc9䃉ƀaiyӒӗӜron;䄚rc耻\xca䃊;䐭ot;䄖r;쀀\ud835\udd08rave耻\xc8䃈ement;戈ĀapӺӾcr;䄒tyɓԆ\0\0ԒmallSquare;旻erySmallSquare;斫ĀgpԦԪon;䄘f;쀀\ud835\udd3csilon;䎕uĀaiԼՉlĀ;TՂՃ橵ilde;扂librium;懌Āci՗՚r;愰m;橳a;䎗ml耻\xcb䃋Āipժկsts;戃onentialE;慇ʀcfiosօֈ֍ֲ׌y;䐤r;쀀\ud835\udd09lledɓ֗\0\0֣mallSquare;旼erySmallSquare;斪Ͱֺ\0ֿ\0\0ׄf;쀀\ud835\udd3dAll;戀riertrf;愱c\xf2׋؀JTabcdfgorstר׬ׯ׺؀ؒؖ؛؝أ٬ٲcy;䐃耻>䀾mmaĀ;d׷׸䎓;䏜reve;䄞ƀeiy؇،ؐdil;䄢rc;䄜;䐓ot;䄠r;쀀\ud835\udd0a;拙pf;쀀\ud835\udd3eeater̀EFGLSTصلَٖٛ٦qualĀ;Lؾؿ扥ess;招ullEqual;执reater;檢ess;扷lantEqual;橾ilde;扳cr;쀀\ud835\udca2;扫ЀAacfiosuڅڋږڛڞڪھۊRDcy;䐪Āctڐڔek;䋇;䁞irc;䄤r;愌lbertSpace;愋ǰگ\0ڲf;愍izontalLine;攀Āctۃۅ\xf2کrok;䄦mpńېۘownHum\xf0įqual;扏܀EJOacdfgmnostuۺ۾܃܇܎ܚܞܡܨ݄ݸދޏޕcy;䐕lig;䄲cy;䐁cute耻\xcd䃍Āiyܓܘrc耻\xce䃎;䐘ot;䄰r;愑rave耻\xcc䃌ƀ;apܠܯܿĀcgܴܷr;䄪inaryI;慈lie\xf3ϝǴ݉\0ݢĀ;eݍݎ戬Āgrݓݘral;戫section;拂isibleĀCTݬݲomma;恣imes;恢ƀgptݿރވon;䄮f;쀀\ud835\udd40a;䎙cr;愐ilde;䄨ǫޚ\0ޞcy;䐆l耻\xcf䃏ʀcfosuެ޷޼߂ߐĀiyޱ޵rc;䄴;䐙r;쀀\ud835\udd0dpf;쀀\ud835\udd41ǣ߇\0ߌr;쀀\ud835\udca5rcy;䐈kcy;䐄΀HJacfosߤߨ߽߬߱ࠂࠈcy;䐥cy;䐌ppa;䎚Āey߶߻dil;䄶;䐚r;쀀\ud835\udd0epf;쀀\ud835\udd42cr;쀀\ud835\udca6րJTaceflmostࠥࠩࠬࡐࡣ঳সে্਷ੇcy;䐉耻<䀼ʀcmnpr࠷࠼ࡁࡄࡍute;䄹bda;䎛g;柪lacetrf;愒r;憞ƀaeyࡗ࡜ࡡron;䄽dil;䄻;䐛Āfsࡨ॰tԀACDFRTUVarࡾࢩࢱࣦ࣠ࣼयज़ΐ४Ānrࢃ࢏gleBracket;柨rowƀ;BR࢙࢚࢞憐ar;懤ightArrow;懆eiling;挈oǵࢷ\0ࣃbleBracket;柦nǔࣈ\0࣒eeVector;楡ectorĀ;Bࣛࣜ懃ar;楙loor;挊ightĀAV࣯ࣵrrow;憔ector;楎Āerँगeƀ;AVउऊऐ抣rrow;憤ector;楚iangleƀ;BEतथऩ抲ar;槏qual;抴pƀDTVषूौownVector;楑eeVector;楠ectorĀ;Bॖॗ憿ar;楘ectorĀ;B॥०憼ar;楒ight\xe1Μs̀EFGLSTॾঋকঝঢভqualGreater;拚ullEqual;扦reater;扶ess;檡lantEqual;橽ilde;扲r;쀀\ud835\udd0fĀ;eঽা拘ftarrow;懚idot;䄿ƀnpw৔ਖਛgȀLRlr৞৷ਂਐeftĀAR০৬rrow;柵ightArrow;柷ightArrow;柶eftĀarγਊight\xe1οight\xe1ϊf;쀀\ud835\udd43erĀLRਢਬeftArrow;憙ightArrow;憘ƀchtਾੀੂ\xf2ࡌ;憰rok;䅁;扪Ѐacefiosuਗ਼੝੠੷੼અઋ઎p;椅y;䐜Ādl੥੯iumSpace;恟lintrf;愳r;쀀\ud835\udd10nusPlus;戓pf;쀀\ud835\udd44c\xf2੶;䎜ҀJacefostuણધભીଔଙඑ඗ඞcy;䐊cute;䅃ƀaey઴હાron;䅇dil;䅅;䐝ƀgswે૰଎ativeƀMTV૓૟૨ediumSpace;怋hiĀcn૦૘\xeb૙eryThi\xee૙tedĀGL૸ଆreaterGreate\xf2ٳessLes\xf3ੈLine;䀊r;쀀\ud835\udd11ȀBnptଢନଷ଺reak;恠BreakingSpace;䂠f;愕ڀ;CDEGHLNPRSTV୕ୖ୪୼஡௫ఄ౞಄ದ೘ൡඅ櫬Āou୛୤ngruent;扢pCap;扭oubleVerticalBar;戦ƀlqxஃஊ஛ement;戉ualĀ;Tஒஓ扠ilde;쀀≂̸ists;戄reater΀;EFGLSTஶஷ஽௉௓௘௥扯qual;扱ullEqual;쀀≧̸reater;쀀≫̸ess;批lantEqual;쀀⩾̸ilde;扵umpń௲௽ownHump;쀀≎̸qual;쀀≏̸eĀfsఊధtTriangleƀ;BEచఛడ拪ar;쀀⧏̸qual;括s̀;EGLSTవశ఼ౄోౘ扮qual;扰reater;扸ess;쀀≪̸lantEqual;쀀⩽̸ilde;扴estedĀGL౨౹reaterGreater;쀀⪢̸essLess;쀀⪡̸recedesƀ;ESಒಓಛ技qual;쀀⪯̸lantEqual;拠ĀeiಫಹverseElement;戌ghtTriangleƀ;BEೋೌ೒拫ar;쀀⧐̸qual;拭ĀquೝഌuareSuĀbp೨೹setĀ;E೰ೳ쀀⊏̸qual;拢ersetĀ;Eഃആ쀀⊐̸qual;拣ƀbcpഓതൎsetĀ;Eഛഞ쀀⊂⃒qual;抈ceedsȀ;ESTലള഻െ抁qual;쀀⪰̸lantEqual;拡ilde;쀀≿̸ersetĀ;E൘൛쀀⊃⃒qual;抉ildeȀ;EFT൮൯൵ൿ扁qual;扄ullEqual;扇ilde;扉erticalBar;戤cr;쀀\ud835\udca9ilde耻\xd1䃑;䎝܀Eacdfgmoprstuvලෂ෉෕ෛ෠෧෼ขภยา฿ไlig;䅒cute耻\xd3䃓Āiy෎ීrc耻\xd4䃔;䐞blac;䅐r;쀀\ud835\udd12rave耻\xd2䃒ƀaei෮ෲ෶cr;䅌ga;䎩cron;䎟pf;쀀\ud835\udd46enCurlyĀDQฎบoubleQuote;怜uote;怘;橔Āclวฬr;쀀\ud835\udcaaash耻\xd8䃘iŬื฼de耻\xd5䃕es;樷ml耻\xd6䃖erĀBP๋๠Āar๐๓r;怾acĀek๚๜;揞et;掴arenthesis;揜Ҁacfhilors๿ງຊຏຒດຝະ໼rtialD;戂y;䐟r;쀀\ud835\udd13i;䎦;䎠usMinus;䂱Āipຢອncareplan\xe5ڝf;愙Ȁ;eio຺ູ໠໤檻cedesȀ;EST່້໏໚扺qual;檯lantEqual;扼ilde;找me;怳Ādp໩໮uct;戏ortionĀ;aȥ໹l;戝Āci༁༆r;쀀\ud835\udcab;䎨ȀUfos༑༖༛༟OT耻"䀢r;쀀\ud835\udd14pf;愚cr;쀀\ud835\udcac؀BEacefhiorsu༾གྷཇའཱིྦྷྪྭ႖ႩႴႾarr;椐G耻\xae䂮ƀcnrཎནབute;䅔g;柫rĀ;tཛྷཝ憠l;椖ƀaeyཧཬཱron;䅘dil;䅖;䐠Ā;vླྀཹ愜erseĀEUྂྙĀlq྇ྎement;戋uilibrium;懋pEquilibrium;楯r\xbbཹo;䎡ghtЀACDFTUVa࿁࿫࿳ဢဨၛႇϘĀnr࿆࿒gleBracket;柩rowƀ;BL࿜࿝࿡憒ar;懥eftArrow;懄eiling;按oǵ࿹\0စbleBracket;柧nǔည\0နeeVector;楝ectorĀ;Bဝသ懂ar;楕loor;挋Āerိ၃eƀ;AVဵံြ抢rrow;憦ector;楛iangleƀ;BEၐၑၕ抳ar;槐qual;抵pƀDTVၣၮၸownVector;楏eeVector;楜ectorĀ;Bႂႃ憾ar;楔ectorĀ;B႑႒懀ar;楓Āpuႛ႞f;愝ndImplies;楰ightarrow;懛ĀchႹႼr;愛;憱leDelayed;槴ڀHOacfhimoqstuფჱჷჽᄙᄞᅑᅖᅡᅧᆵᆻᆿĀCcჩხHcy;䐩y;䐨FTcy;䐬cute;䅚ʀ;aeiyᄈᄉᄎᄓᄗ檼ron;䅠dil;䅞rc;䅜;䐡r;쀀\ud835\udd16ortȀDLRUᄪᄴᄾᅉownArrow\xbbОeftArrow\xbb࢚ightArrow\xbb࿝pArrow;憑gma;䎣allCircle;战pf;쀀\ud835\udd4aɲᅭ\0\0ᅰt;戚areȀ;ISUᅻᅼᆉᆯ斡ntersection;抓uĀbpᆏᆞsetĀ;Eᆗᆘ抏qual;抑ersetĀ;Eᆨᆩ抐qual;抒nion;抔cr;쀀\ud835\udcaear;拆ȀbcmpᇈᇛሉላĀ;sᇍᇎ拐etĀ;Eᇍᇕqual;抆ĀchᇠህeedsȀ;ESTᇭᇮᇴᇿ扻qual;檰lantEqual;扽ilde;承Th\xe1ྌ;我ƀ;esሒሓሣ拑rsetĀ;Eሜም抃qual;抇et\xbbሓրHRSacfhiorsሾቄ቉ቕ቞ቱቶኟዂወዑORN耻\xde䃞ADE;愢ĀHc቎ቒcy;䐋y;䐦Ābuቚቜ;䀉;䎤ƀaeyብቪቯron;䅤dil;䅢;䐢r;쀀\ud835\udd17Āeiቻ኉ǲኀ\0ኇefore;戴a;䎘Ācn኎ኘkSpace;쀀  Space;怉ldeȀ;EFTካኬኲኼ戼qual;扃ullEqual;扅ilde;扈pf;쀀\ud835\udd4bipleDot;惛Āctዖዛr;쀀\ud835\udcafrok;䅦ૡዷጎጚጦ\0ጬጱ\0\0\0\0\0ጸጽ፷ᎅ\0᏿ᐄᐊᐐĀcrዻጁute耻\xda䃚rĀ;oጇገ憟cir;楉rǣጓ\0጖y;䐎ve;䅬Āiyጞጣrc耻\xdb䃛;䐣blac;䅰r;쀀\ud835\udd18rave耻\xd9䃙acr;䅪Ādiፁ፩erĀBPፈ፝Āarፍፐr;䁟acĀekፗፙ;揟et;掵arenthesis;揝onĀ;P፰፱拃lus;抎Āgp፻፿on;䅲f;쀀\ud835\udd4cЀADETadps᎕ᎮᎸᏄϨᏒᏗᏳrrowƀ;BDᅐᎠᎤar;椒ownArrow;懅ownArrow;憕quilibrium;楮eeĀ;AᏋᏌ报rrow;憥own\xe1ϳerĀLRᏞᏨeftArrow;憖ightArrow;憗iĀ;lᏹᏺ䏒on;䎥ing;䅮cr;쀀\ud835\udcb0ilde;䅨ml耻\xdc䃜ҀDbcdefosvᐧᐬᐰᐳᐾᒅᒊᒐᒖash;披ar;櫫y;䐒ashĀ;lᐻᐼ抩;櫦Āerᑃᑅ;拁ƀbtyᑌᑐᑺar;怖Ā;iᑏᑕcalȀBLSTᑡᑥᑪᑴar;戣ine;䁼eparator;杘ilde;所ThinSpace;怊r;쀀\ud835\udd19pf;쀀\ud835\udd4dcr;쀀\ud835\udcb1dash;抪ʀcefosᒧᒬᒱᒶᒼirc;䅴dge;拀r;쀀\ud835\udd1apf;쀀\ud835\udd4ecr;쀀\ud835\udcb2Ȁfiosᓋᓐᓒᓘr;쀀\ud835\udd1b;䎞pf;쀀\ud835\udd4fcr;쀀\ud835\udcb3ҀAIUacfosuᓱᓵᓹᓽᔄᔏᔔᔚᔠcy;䐯cy;䐇cy;䐮cute耻\xdd䃝Āiyᔉᔍrc;䅶;䐫r;쀀\ud835\udd1cpf;쀀\ud835\udd50cr;쀀\ud835\udcb4ml;䅸ЀHacdefosᔵᔹᔿᕋᕏᕝᕠᕤcy;䐖cute;䅹Āayᕄᕉron;䅽;䐗ot;䅻ǲᕔ\0ᕛoWidt\xe8૙a;䎖r;愨pf;愤cr;쀀\ud835\udcb5௡ᖃᖊᖐ\0ᖰᖶᖿ\0\0\0\0ᗆᗛᗫᙟ᙭\0ᚕ᚛ᚲᚹ\0ᚾcute耻\xe1䃡reve;䄃̀;Ediuyᖜᖝᖡᖣᖨᖭ戾;쀀∾̳;房rc耻\xe2䃢te肻\xb4̆;䐰lig耻\xe6䃦Ā;r\xb2ᖺ;쀀\ud835\udd1erave耻\xe0䃠ĀepᗊᗖĀfpᗏᗔsym;愵\xe8ᗓha;䎱ĀapᗟcĀclᗤᗧr;䄁g;樿ɤᗰ\0\0ᘊʀ;adsvᗺᗻᗿᘁᘇ戧nd;橕;橜lope;橘;橚΀;elmrszᘘᘙᘛᘞᘿᙏᙙ戠;榤e\xbbᘙsdĀ;aᘥᘦ戡ѡᘰᘲᘴᘶᘸᘺᘼᘾ;榨;榩;榪;榫;榬;榭;榮;榯tĀ;vᙅᙆ戟bĀ;dᙌᙍ抾;榝Āptᙔᙗh;戢\xbb\xb9arr;捼Āgpᙣᙧon;䄅f;쀀\ud835\udd52΀;Eaeiop዁ᙻᙽᚂᚄᚇᚊ;橰cir;橯;扊d;手s;䀧roxĀ;e዁ᚒ\xf1ᚃing耻\xe5䃥ƀctyᚡᚦᚨr;쀀\ud835\udcb6;䀪mpĀ;e዁ᚯ\xf1ʈilde耻\xe3䃣ml耻\xe4䃤Āciᛂᛈonin\xf4ɲnt;樑ࠀNabcdefiklnoprsu᛭ᛱᜰ᜼ᝃᝈ᝸᝽០៦ᠹᡐᜍ᤽᥈ᥰot;櫭Ācrᛶ᜞kȀcepsᜀᜅᜍᜓong;扌psilon;䏶rime;怵imĀ;e᜚᜛戽q;拍Ŷᜢᜦee;抽edĀ;gᜬᜭ挅e\xbbᜭrkĀ;t፜᜷brk;掶Āoyᜁᝁ;䐱quo;怞ʀcmprtᝓ᝛ᝡᝤᝨausĀ;eĊĉptyv;榰s\xe9ᜌno\xf5ēƀahwᝯ᝱ᝳ;䎲;愶een;扬r;쀀\ud835\udd1fg΀costuvwឍឝឳេ៕៛៞ƀaiuបពរ\xf0ݠrc;旯p\xbb፱ƀdptឤឨឭot;樀lus;樁imes;樂ɱឹ\0\0ើcup;樆ar;昅riangleĀdu៍្own;施p;斳plus;樄e\xe5ᑄ\xe5ᒭarow;植ƀako៭ᠦᠵĀcn៲ᠣkƀlst៺֫᠂ozenge;槫riangleȀ;dlr᠒᠓᠘᠝斴own;斾eft;旂ight;斸k;搣Ʊᠫ\0ᠳƲᠯ\0ᠱ;斒;斑4;斓ck;斈ĀeoᠾᡍĀ;qᡃᡆ쀀=⃥uiv;쀀≡⃥t;挐Ȁptwxᡙᡞᡧᡬf;쀀\ud835\udd53Ā;tᏋᡣom\xbbᏌtie;拈؀DHUVbdhmptuvᢅᢖᢪᢻᣗᣛᣬ᣿ᤅᤊᤐᤡȀLRlrᢎᢐᢒᢔ;敗;敔;敖;敓ʀ;DUduᢡᢢᢤᢦᢨ敐;敦;敩;敤;敧ȀLRlrᢳᢵᢷᢹ;敝;敚;敜;教΀;HLRhlrᣊᣋᣍᣏᣑᣓᣕ救;敬;散;敠;敫;敢;敟ox;槉ȀLRlrᣤᣦᣨᣪ;敕;敒;攐;攌ʀ;DUduڽ᣷᣹᣻᣽;敥;敨;攬;攴inus;抟lus;択imes;抠ȀLRlrᤙᤛᤝ᤟;敛;敘;攘;攔΀;HLRhlrᤰᤱᤳᤵᤷ᤻᤹攂;敪;敡;敞;攼;攤;攜Āevģ᥂bar耻\xa6䂦Ȁceioᥑᥖᥚᥠr;쀀\ud835\udcb7mi;恏mĀ;e᜚᜜lƀ;bhᥨᥩᥫ䁜;槅sub;柈Ŭᥴ᥾lĀ;e᥹᥺怢t\xbb᥺pƀ;Eeįᦅᦇ;檮Ā;qۜۛೡᦧ\0᧨ᨑᨕᨲ\0ᨷᩐ\0\0᪴\0\0᫁\0\0ᬡᬮ᭍᭒\0᯽\0ᰌƀcpr᦭ᦲ᧝ute;䄇̀;abcdsᦿᧀᧄ᧊᧕᧙戩nd;橄rcup;橉Āau᧏᧒p;橋p;橇ot;橀;쀀∩︀Āeo᧢᧥t;恁\xeeړȀaeiu᧰᧻ᨁᨅǰ᧵\0᧸s;橍on;䄍dil耻\xe7䃧rc;䄉psĀ;sᨌᨍ橌m;橐ot;䄋ƀdmnᨛᨠᨦil肻\xb8ƭptyv;榲t脀\xa2;eᨭᨮ䂢r\xe4Ʋr;쀀\ud835\udd20ƀceiᨽᩀᩍy;䑇ckĀ;mᩇᩈ朓ark\xbbᩈ;䏇r΀;Ecefms᩟᩠ᩢᩫ᪤᪪᪮旋;槃ƀ;elᩩᩪᩭ䋆q;扗eɡᩴ\0\0᪈rrowĀlr᩼᪁eft;憺ight;憻ʀRSacd᪒᪔᪖᪚᪟\xbbཇ;擈st;抛irc;抚ash;抝nint;樐id;櫯cir;槂ubsĀ;u᪻᪼晣it\xbb᪼ˬ᫇᫔᫺\0ᬊonĀ;eᫍᫎ䀺Ā;q\xc7\xc6ɭ᫙\0\0᫢aĀ;t᫞᫟䀬;䁀ƀ;fl᫨᫩᫫戁\xeeᅠeĀmx᫱᫶ent\xbb᫩e\xf3ɍǧ᫾\0ᬇĀ;dኻᬂot;橭n\xf4Ɇƀfryᬐᬔᬗ;쀀\ud835\udd54o\xe4ɔ脀\xa9;sŕᬝr;愗Āaoᬥᬩrr;憵ss;朗Ācuᬲᬷr;쀀\ud835\udcb8Ābpᬼ᭄Ā;eᭁᭂ櫏;櫑Ā;eᭉᭊ櫐;櫒dot;拯΀delprvw᭠᭬᭷ᮂᮬᯔ᯹arrĀlr᭨᭪;椸;椵ɰ᭲\0\0᭵r;拞c;拟arrĀ;p᭿ᮀ憶;椽̀;bcdosᮏᮐᮖᮡᮥᮨ截rcap;橈Āauᮛᮞp;橆p;橊ot;抍r;橅;쀀∪︀Ȁalrv᮵ᮿᯞᯣrrĀ;mᮼᮽ憷;椼yƀevwᯇᯔᯘqɰᯎ\0\0ᯒre\xe3᭳u\xe3᭵ee;拎edge;拏en耻\xa4䂤earrowĀlrᯮ᯳eft\xbbᮀight\xbbᮽe\xe4ᯝĀciᰁᰇonin\xf4Ƿnt;戱lcty;挭ঀAHabcdefhijlorstuwz᰸᰻᰿ᱝᱩᱵᲊᲞᲬᲷ᳻᳿ᴍᵻᶑᶫᶻ᷆᷍r\xf2΁ar;楥Ȁglrs᱈ᱍ᱒᱔ger;怠eth;愸\xf2ᄳhĀ;vᱚᱛ怐\xbbऊūᱡᱧarow;椏a\xe3̕Āayᱮᱳron;䄏;䐴ƀ;ao̲ᱼᲄĀgrʿᲁr;懊tseq;橷ƀglmᲑᲔᲘ耻\xb0䂰ta;䎴ptyv;榱ĀirᲣᲨsht;楿;쀀\ud835\udd21arĀlrᲳᲵ\xbbࣜ\xbbသʀaegsv᳂͸᳖᳜᳠mƀ;oș᳊᳔ndĀ;ș᳑uit;晦amma;䏝in;拲ƀ;io᳧᳨᳸䃷de脀\xf7;o᳧ᳰntimes;拇n\xf8᳷cy;䑒cɯᴆ\0\0ᴊrn;挞op;挍ʀlptuwᴘᴝᴢᵉᵕlar;䀤f;쀀\ud835\udd55ʀ;emps̋ᴭᴷᴽᵂqĀ;d͒ᴳot;扑inus;戸lus;戔quare;抡blebarwedg\xe5\xfanƀadhᄮᵝᵧownarrow\xf3ᲃarpoonĀlrᵲᵶef\xf4Ჴigh\xf4ᲶŢᵿᶅkaro\xf7གɯᶊ\0\0ᶎrn;挟op;挌ƀcotᶘᶣᶦĀryᶝᶡ;쀀\ud835\udcb9;䑕l;槶rok;䄑Ādrᶰᶴot;拱iĀ;fᶺ᠖斿Āah᷀᷃r\xf2Щa\xf2ྦangle;榦Āci᷒ᷕy;䑟grarr;柿ऀDacdefglmnopqrstuxḁḉḙḸոḼṉṡṾấắẽỡἪἷὄ὎὚ĀDoḆᴴo\xf4ᲉĀcsḎḔute耻\xe9䃩ter;橮ȀaioyḢḧḱḶron;䄛rĀ;cḭḮ扖耻\xea䃪lon;払;䑍ot;䄗ĀDrṁṅot;扒;쀀\ud835\udd22ƀ;rsṐṑṗ檚ave耻\xe8䃨Ā;dṜṝ檖ot;檘Ȁ;ilsṪṫṲṴ檙nters;揧;愓Ā;dṹṺ檕ot;檗ƀapsẅẉẗcr;䄓tyƀ;svẒẓẕ戅et\xbbẓpĀ1;ẝẤĳạả;怄;怅怃ĀgsẪẬ;䅋p;怂ĀgpẴẸon;䄙f;쀀\ud835\udd56ƀalsỄỎỒrĀ;sỊị拕l;槣us;橱iƀ;lvỚớở䎵on\xbbớ;䏵ȀcsuvỪỳἋἣĀioữḱrc\xbbḮɩỹ\0\0ỻ\xedՈantĀglἂἆtr\xbbṝess\xbbṺƀaeiἒ἖Ἒls;䀽st;扟vĀ;DȵἠD;橸parsl;槥ĀDaἯἳot;打rr;楱ƀcdiἾὁỸr;愯o\xf4͒ĀahὉὋ;䎷耻\xf0䃰Āmrὓὗl耻\xeb䃫o;悬ƀcipὡὤὧl;䀡s\xf4ծĀeoὬὴctatio\xeeՙnential\xe5չৡᾒ\0ᾞ\0ᾡᾧ\0\0ῆῌ\0ΐ\0ῦῪ \0 ⁚llingdotse\xf1Ṅy;䑄male;晀ƀilrᾭᾳ῁lig;耀ﬃɩᾹ\0\0᾽g;耀ﬀig;耀ﬄ;쀀\ud835\udd23lig;耀ﬁlig;쀀fjƀaltῙ῜ῡt;晭ig;耀ﬂns;斱of;䆒ǰ΅\0ῳf;쀀\ud835\udd57ĀakֿῷĀ;vῼ´拔;櫙artint;樍Āao‌⁕Ācs‑⁒α‚‰‸⁅⁈\0⁐β•‥‧‪‬\0‮耻\xbd䂽;慓耻\xbc䂼;慕;慙;慛Ƴ‴\0‶;慔;慖ʴ‾⁁\0\0⁃耻\xbe䂾;慗;慜5;慘ƶ⁌\0⁎;慚;慝8;慞l;恄wn;挢cr;쀀\ud835\udcbbࢀEabcdefgijlnorstv₂₉₟₥₰₴⃰⃵⃺⃿℃ℒℸ̗ℾ⅒↞Ā;lٍ₇;檌ƀcmpₐₕ₝ute;䇵maĀ;dₜ᳚䎳;檆reve;䄟Āiy₪₮rc;䄝;䐳ot;䄡Ȁ;lqsؾق₽⃉ƀ;qsؾٌ⃄lan\xf4٥Ȁ;cdl٥⃒⃥⃕c;檩otĀ;o⃜⃝檀Ā;l⃢⃣檂;檄Ā;e⃪⃭쀀⋛︀s;檔r;쀀\ud835\udd24Ā;gٳ؛mel;愷cy;䑓Ȁ;Eajٚℌℎℐ;檒;檥;檤ȀEaesℛℝ℩ℴ;扩pĀ;p℣ℤ檊rox\xbbℤĀ;q℮ℯ檈Ā;q℮ℛim;拧pf;쀀\ud835\udd58Āci⅃ⅆr;愊mƀ;el٫ⅎ⅐;檎;檐茀>;cdlqr׮ⅠⅪⅮⅳⅹĀciⅥⅧ;檧r;橺ot;拗Par;榕uest;橼ʀadelsↄⅪ←ٖ↛ǰ↉\0↎pro\xf8₞r;楸qĀlqؿ↖les\xf3₈i\xed٫Āen↣↭rtneqq;쀀≩︀\xc5↪ԀAabcefkosy⇄⇇⇱⇵⇺∘∝∯≨≽r\xf2ΠȀilmr⇐⇔⇗⇛rs\xf0ᒄf\xbb․il\xf4کĀdr⇠⇤cy;䑊ƀ;cwࣴ⇫⇯ir;楈;憭ar;意irc;䄥ƀalr∁∎∓rtsĀ;u∉∊晥it\xbb∊lip;怦con;抹r;쀀\ud835\udd25sĀew∣∩arow;椥arow;椦ʀamopr∺∾≃≞≣rr;懿tht;戻kĀlr≉≓eftarrow;憩ightarrow;憪f;쀀\ud835\udd59bar;怕ƀclt≯≴≸r;쀀\ud835\udcbdas\xe8⇴rok;䄧Ābp⊂⊇ull;恃hen\xbbᱛૡ⊣\0⊪\0⊸⋅⋎\0⋕⋳\0\0⋸⌢⍧⍢⍿\0⎆⎪⎴cute耻\xed䃭ƀ;iyݱ⊰⊵rc耻\xee䃮;䐸Ācx⊼⊿y;䐵cl耻\xa1䂡ĀfrΟ⋉;쀀\ud835\udd26rave耻\xec䃬Ȁ;inoܾ⋝⋩⋮Āin⋢⋦nt;樌t;戭fin;槜ta;愩lig;䄳ƀaop⋾⌚⌝ƀcgt⌅⌈⌗r;䄫ƀelpܟ⌏⌓in\xe5ގar\xf4ܠh;䄱f;抷ed;䆵ʀ;cfotӴ⌬⌱⌽⍁are;愅inĀ;t⌸⌹戞ie;槝do\xf4⌙ʀ;celpݗ⍌⍐⍛⍡al;抺Āgr⍕⍙er\xf3ᕣ\xe3⍍arhk;樗rod;樼Ȁcgpt⍯⍲⍶⍻y;䑑on;䄯f;쀀\ud835\udd5aa;䎹uest耻\xbf䂿Āci⎊⎏r;쀀\ud835\udcbenʀ;EdsvӴ⎛⎝⎡ӳ;拹ot;拵Ā;v⎦⎧拴;拳Ā;iݷ⎮lde;䄩ǫ⎸\0⎼cy;䑖l耻\xef䃯̀cfmosu⏌⏗⏜⏡⏧⏵Āiy⏑⏕rc;䄵;䐹r;쀀\ud835\udd27ath;䈷pf;쀀\ud835\udd5bǣ⏬\0⏱r;쀀\ud835\udcbfrcy;䑘kcy;䑔Ѐacfghjos␋␖␢␧␭␱␵␻ppaĀ;v␓␔䎺;䏰Āey␛␠dil;䄷;䐺r;쀀\ud835\udd28reen;䄸cy;䑅cy;䑜pf;쀀\ud835\udd5ccr;쀀\ud835\udcc0஀ABEHabcdefghjlmnoprstuv⑰⒁⒆⒍⒑┎┽╚▀♎♞♥♹♽⚚⚲⛘❝❨➋⟀⠁⠒ƀart⑷⑺⑼r\xf2৆\xf2Εail;椛arr;椎Ā;gঔ⒋;檋ar;楢ॣ⒥\0⒪\0⒱\0\0\0\0\0⒵Ⓔ\0ⓆⓈⓍ\0⓹ute;䄺mptyv;榴ra\xeeࡌbda;䎻gƀ;dlࢎⓁⓃ;榑\xe5ࢎ;檅uo耻\xab䂫rЀ;bfhlpst࢙ⓞⓦⓩ⓫⓮⓱⓵Ā;f࢝ⓣs;椟s;椝\xeb≒p;憫l;椹im;楳l;憢ƀ;ae⓿─┄檫il;椙Ā;s┉┊檭;쀀⪭︀ƀabr┕┙┝rr;椌rk;杲Āak┢┬cĀek┨┪;䁻;䁛Āes┱┳;榋lĀdu┹┻;榏;榍Ȁaeuy╆╋╖╘ron;䄾Ādi═╔il;䄼\xecࢰ\xe2┩;䐻Ȁcqrs╣╦╭╽a;椶uoĀ;rนᝆĀdu╲╷har;楧shar;楋h;憲ʀ;fgqs▋▌উ◳◿扤tʀahlrt▘▤▷◂◨rrowĀ;t࢙□a\xe9⓶arpoonĀdu▯▴own\xbbњp\xbb०eftarrows;懇ightƀahs◍◖◞rrowĀ;sࣴࢧarpoon\xf3྘quigarro\xf7⇰hreetimes;拋ƀ;qs▋ও◺lan\xf4বʀ;cdgsব☊☍☝☨c;檨otĀ;o☔☕橿Ā;r☚☛檁;檃Ā;e☢☥쀀⋚︀s;檓ʀadegs☳☹☽♉♋ppro\xf8Ⓠot;拖qĀgq♃♅\xf4উgt\xf2⒌\xf4ছi\xedলƀilr♕࣡♚sht;楼;쀀\ud835\udd29Ā;Eজ♣;檑š♩♶rĀdu▲♮Ā;l॥♳;楪lk;斄cy;䑙ʀ;achtੈ⚈⚋⚑⚖r\xf2◁orne\xf2ᴈard;楫ri;旺Āio⚟⚤dot;䅀ustĀ;a⚬⚭掰che\xbb⚭ȀEaes⚻⚽⛉⛔;扨pĀ;p⛃⛄檉rox\xbb⛄Ā;q⛎⛏檇Ā;q⛎⚻im;拦Ѐabnoptwz⛩⛴⛷✚✯❁❇❐Ānr⛮⛱g;柬r;懽r\xebࣁgƀlmr⛿✍✔eftĀar০✇ight\xe1৲apsto;柼ight\xe1৽parrowĀlr✥✩ef\xf4⓭ight;憬ƀafl✶✹✽r;榅;쀀\ud835\udd5dus;樭imes;樴š❋❏st;戗\xe1ፎƀ;ef❗❘᠀旊nge\xbb❘arĀ;l❤❥䀨t;榓ʀachmt❳❶❼➅➇r\xf2ࢨorne\xf2ᶌarĀ;d྘➃;業;怎ri;抿̀achiqt➘➝ੀ➢➮➻quo;怹r;쀀\ud835\udcc1mƀ;egল➪➬;檍;檏Ābu┪➳oĀ;rฟ➹;怚rok;䅂萀<;cdhilqrࠫ⟒☹⟜⟠⟥⟪⟰Āci⟗⟙;檦r;橹re\xe5◲mes;拉arr;楶uest;橻ĀPi⟵⟹ar;榖ƀ;ef⠀भ᠛旃rĀdu⠇⠍shar;楊har;楦Āen⠗⠡rtneqq;쀀≨︀\xc5⠞܀Dacdefhilnopsu⡀⡅⢂⢎⢓⢠⢥⢨⣚⣢⣤ઃ⣳⤂Dot;戺Ȁclpr⡎⡒⡣⡽r耻\xaf䂯Āet⡗⡙;時Ā;e⡞⡟朠se\xbb⡟Ā;sျ⡨toȀ;dluျ⡳⡷⡻ow\xeeҌef\xf4ए\xf0Ꮡker;斮Āoy⢇⢌mma;権;䐼ash;怔asuredangle\xbbᘦr;쀀\ud835\udd2ao;愧ƀcdn⢯⢴⣉ro耻\xb5䂵Ȁ;acdᑤ⢽⣀⣄s\xf4ᚧir;櫰ot肻\xb7Ƶusƀ;bd⣒ᤃ⣓戒Ā;uᴼ⣘;横ţ⣞⣡p;櫛\xf2−\xf0ઁĀdp⣩⣮els;抧f;쀀\ud835\udd5eĀct⣸⣽r;쀀\ud835\udcc2pos\xbbᖝƀ;lm⤉⤊⤍䎼timap;抸ఀGLRVabcdefghijlmoprstuvw⥂⥓⥾⦉⦘⧚⧩⨕⨚⩘⩝⪃⪕⪤⪨⬄⬇⭄⭿⮮ⰴⱧⱼ⳩Āgt⥇⥋;쀀⋙̸Ā;v⥐௏쀀≫⃒ƀelt⥚⥲⥶ftĀar⥡⥧rrow;懍ightarrow;懎;쀀⋘̸Ā;v⥻ే쀀≪⃒ightarrow;懏ĀDd⦎⦓ash;抯ash;抮ʀbcnpt⦣⦧⦬⦱⧌la\xbb˞ute;䅄g;쀀∠⃒ʀ;Eiop඄⦼⧀⧅⧈;쀀⩰̸d;쀀≋̸s;䅉ro\xf8඄urĀ;a⧓⧔普lĀ;s⧓ସǳ⧟\0⧣p肻\xa0ଷmpĀ;e௹ఀʀaeouy⧴⧾⨃⨐⨓ǰ⧹\0⧻;橃on;䅈dil;䅆ngĀ;dൾ⨊ot;쀀⩭̸p;橂;䐽ash;怓΀;Aadqsxஒ⨩⨭⨻⩁⩅⩐rr;懗rĀhr⨳⨶k;椤Ā;oᏲᏰot;쀀≐̸ui\xf6ୣĀei⩊⩎ar;椨\xed஘istĀ;s஠டr;쀀\ud835\udd2bȀEest௅⩦⩹⩼ƀ;qs஼⩭௡ƀ;qs஼௅⩴lan\xf4௢i\xed௪Ā;rஶ⪁\xbbஷƀAap⪊⪍⪑r\xf2⥱rr;憮ar;櫲ƀ;svྍ⪜ྌĀ;d⪡⪢拼;拺cy;䑚΀AEadest⪷⪺⪾⫂⫅⫶⫹r\xf2⥦;쀀≦̸rr;憚r;急Ȁ;fqs఻⫎⫣⫯tĀar⫔⫙rro\xf7⫁ightarro\xf7⪐ƀ;qs఻⪺⫪lan\xf4ౕĀ;sౕ⫴\xbbశi\xedౝĀ;rవ⫾iĀ;eచథi\xe4ඐĀpt⬌⬑f;쀀\ud835\udd5f膀\xac;in⬙⬚⬶䂬nȀ;Edvஉ⬤⬨⬮;쀀⋹̸ot;쀀⋵̸ǡஉ⬳⬵;拷;拶iĀ;vಸ⬼ǡಸ⭁⭃;拾;拽ƀaor⭋⭣⭩rȀ;ast୻⭕⭚⭟lle\xec୻l;쀀⫽⃥;쀀∂̸lint;樔ƀ;ceಒ⭰⭳u\xe5ಥĀ;cಘ⭸Ā;eಒ⭽\xf1ಘȀAait⮈⮋⮝⮧r\xf2⦈rrƀ;cw⮔⮕⮙憛;쀀⤳̸;쀀↝̸ghtarrow\xbb⮕riĀ;eೋೖ΀chimpqu⮽⯍⯙⬄୸⯤⯯Ȁ;cerല⯆ഷ⯉u\xe5൅;쀀\ud835\udcc3ortɭ⬅\0\0⯖ar\xe1⭖mĀ;e൮⯟Ā;q൴൳suĀbp⯫⯭\xe5೸\xe5ഋƀbcp⯶ⰑⰙȀ;Ees⯿ⰀഢⰄ抄;쀀⫅̸etĀ;eഛⰋqĀ;qണⰀcĀ;eലⰗ\xf1സȀ;EesⰢⰣൟⰧ抅;쀀⫆̸etĀ;e൘ⰮqĀ;qൠⰣȀgilrⰽⰿⱅⱇ\xecௗlde耻\xf1䃱\xe7ృiangleĀlrⱒⱜeftĀ;eచⱚ\xf1దightĀ;eೋⱥ\xf1೗Ā;mⱬⱭ䎽ƀ;esⱴⱵⱹ䀣ro;愖p;怇ҀDHadgilrsⲏⲔⲙⲞⲣⲰⲶⳓⳣash;抭arr;椄p;쀀≍⃒ash;抬ĀetⲨⲬ;쀀≥⃒;쀀>⃒nfin;槞ƀAetⲽⳁⳅrr;椂;쀀≤⃒Ā;rⳊⳍ쀀<⃒ie;쀀⊴⃒ĀAtⳘⳜrr;椃rie;쀀⊵⃒im;쀀∼⃒ƀAan⳰⳴ⴂrr;懖rĀhr⳺⳽k;椣Ā;oᏧᏥear;椧ቓ᪕\0\0\0\0\0\0\0\0\0\0\0\0\0ⴭ\0ⴸⵈⵠⵥ⵲ⶄᬇ\0\0ⶍⶫ\0ⷈⷎ\0ⷜ⸙⸫⸾⹃Ācsⴱ᪗ute耻\xf3䃳ĀiyⴼⵅrĀ;c᪞ⵂ耻\xf4䃴;䐾ʀabios᪠ⵒⵗǈⵚlac;䅑v;樸old;榼lig;䅓Ācr⵩⵭ir;榿;쀀\ud835\udd2cͯ⵹\0\0⵼\0ⶂn;䋛ave耻\xf2䃲;槁Ābmⶈ෴ar;榵Ȁacitⶕ⶘ⶥⶨr\xf2᪀Āir⶝ⶠr;榾oss;榻n\xe5๒;槀ƀaeiⶱⶵⶹcr;䅍ga;䏉ƀcdnⷀⷅǍron;䎿;榶pf;쀀\ud835\udd60ƀaelⷔ⷗ǒr;榷rp;榹΀;adiosvⷪⷫⷮ⸈⸍⸐⸖戨r\xf2᪆Ȁ;efmⷷⷸ⸂⸅橝rĀ;oⷾⷿ愴f\xbbⷿ耻\xaa䂪耻\xba䂺gof;抶r;橖lope;橗;橛ƀclo⸟⸡⸧\xf2⸁ash耻\xf8䃸l;折iŬⸯ⸴de耻\xf5䃵esĀ;aǛ⸺s;樶ml耻\xf6䃶bar;挽ૡ⹞\0⹽\0⺀⺝\0⺢⺹\0\0⻋ຜ\0⼓\0\0⼫⾼\0⿈rȀ;astЃ⹧⹲຅脀\xb6;l⹭⹮䂶le\xecЃɩ⹸\0\0⹻m;櫳;櫽y;䐿rʀcimpt⺋⺏⺓ᡥ⺗nt;䀥od;䀮il;怰enk;怱r;쀀\ud835\udd2dƀimo⺨⺰⺴Ā;v⺭⺮䏆;䏕ma\xf4੶ne;明ƀ;tv⺿⻀⻈䏀chfork\xbb´;䏖Āau⻏⻟nĀck⻕⻝kĀ;h⇴⻛;愎\xf6⇴sҀ;abcdemst⻳⻴ᤈ⻹⻽⼄⼆⼊⼎䀫cir;樣ir;樢Āouᵀ⼂;樥;橲n肻\xb1ຝim;樦wo;樧ƀipu⼙⼠⼥ntint;樕f;쀀\ud835\udd61nd耻\xa3䂣Ԁ;Eaceinosu່⼿⽁⽄⽇⾁⾉⾒⽾⾶;檳p;檷u\xe5໙Ā;c໎⽌̀;acens່⽙⽟⽦⽨⽾ppro\xf8⽃urlye\xf1໙\xf1໎ƀaes⽯⽶⽺pprox;檹qq;檵im;拨i\xedໟmeĀ;s⾈ຮ怲ƀEas⽸⾐⽺\xf0⽵ƀdfp໬⾙⾯ƀals⾠⾥⾪lar;挮ine;挒urf;挓Ā;t໻⾴\xef໻rel;抰Āci⿀⿅r;쀀\ud835\udcc5;䏈ncsp;怈̀fiopsu⿚⋢⿟⿥⿫⿱r;쀀\ud835\udd2epf;쀀\ud835\udd62rime;恗cr;쀀\ud835\udcc6ƀaeo⿸〉〓tĀei⿾々rnion\xf3ڰnt;樖stĀ;e【】䀿\xf1Ἑ\xf4༔઀ABHabcdefhilmnoprstux぀けさすムㄎㄫㅇㅢㅲㆎ㈆㈕㈤㈩㉘㉮㉲㊐㊰㊷ƀartぇおがr\xf2Ⴓ\xf2ϝail;検ar\xf2ᱥar;楤΀cdenqrtとふへみわゔヌĀeuねぱ;쀀∽̱te;䅕i\xe3ᅮmptyv;榳gȀ;del࿑らるろ;榒;榥\xe5࿑uo耻\xbb䂻rր;abcfhlpstw࿜ガクシスゼゾダッデナp;極Ā;f࿠ゴs;椠;椳s;椞\xeb≝\xf0✮l;楅im;楴l;憣;憝Āaiパフil;椚oĀ;nホボ戶al\xf3༞ƀabrョリヮr\xf2៥rk;杳ĀakンヽcĀekヹ・;䁽;䁝Āes㄂㄄;榌lĀduㄊㄌ;榎;榐Ȁaeuyㄗㄜㄧㄩron;䅙Ādiㄡㄥil;䅗\xec࿲\xe2ヺ;䑀Ȁclqsㄴㄷㄽㅄa;椷dhar;楩uoĀ;rȎȍh;憳ƀacgㅎㅟངlȀ;ipsླྀㅘㅛႜn\xe5Ⴛar\xf4ྩt;断ƀilrㅩဣㅮsht;楽;쀀\ud835\udd2fĀaoㅷㆆrĀduㅽㅿ\xbbѻĀ;l႑ㆄ;楬Ā;vㆋㆌ䏁;䏱ƀgns㆕ㇹㇼht̀ahlrstㆤㆰ㇂㇘㇤㇮rrowĀ;t࿜ㆭa\xe9トarpoonĀduㆻㆿow\xeeㅾp\xbb႒eftĀah㇊㇐rrow\xf3࿪arpoon\xf3Ցightarrows;應quigarro\xf7ニhreetimes;拌g;䋚ingdotse\xf1ἲƀahm㈍㈐㈓r\xf2࿪a\xf2Ց;怏oustĀ;a㈞㈟掱che\xbb㈟mid;櫮Ȁabpt㈲㈽㉀㉒Ānr㈷㈺g;柭r;懾r\xebဃƀafl㉇㉊㉎r;榆;쀀\ud835\udd63us;樮imes;樵Āap㉝㉧rĀ;g㉣㉤䀩t;榔olint;樒ar\xf2㇣Ȁachq㉻㊀Ⴜ㊅quo;怺r;쀀\ud835\udcc7Ābu・㊊oĀ;rȔȓƀhir㊗㊛㊠re\xe5ㇸmes;拊iȀ;efl㊪ၙᠡ㊫方tri;槎luhar;楨;愞ൡ㋕㋛㋟㌬㌸㍱\0㍺㎤\0\0㏬㏰\0㐨㑈㑚㒭㒱㓊㓱\0㘖\0\0㘳cute;䅛qu\xef➺Ԁ;Eaceinpsyᇭ㋳㋵㋿㌂㌋㌏㌟㌦㌩;檴ǰ㋺\0㋼;檸on;䅡u\xe5ᇾĀ;dᇳ㌇il;䅟rc;䅝ƀEas㌖㌘㌛;檶p;檺im;择olint;樓i\xedሄ;䑁otƀ;be㌴ᵇ㌵担;橦΀Aacmstx㍆㍊㍗㍛㍞㍣㍭rr;懘rĀhr㍐㍒\xeb∨Ā;oਸ਼਴t耻\xa7䂧i;䀻war;椩mĀin㍩\xf0nu\xf3\xf1t;朶rĀ;o㍶⁕쀀\ud835\udd30Ȁacoy㎂㎆㎑㎠rp;景Āhy㎋㎏cy;䑉;䑈rtɭ㎙\0\0㎜i\xe4ᑤara\xec⹯耻\xad䂭Āgm㎨㎴maƀ;fv㎱㎲㎲䏃;䏂Ѐ;deglnprካ㏅㏉㏎㏖㏞㏡㏦ot;橪Ā;q኱ኰĀ;E㏓㏔檞;檠Ā;E㏛㏜檝;檟e;扆lus;樤arr;楲ar\xf2ᄽȀaeit㏸㐈㐏㐗Āls㏽㐄lsetm\xe9㍪hp;樳parsl;槤Ādlᑣ㐔e;挣Ā;e㐜㐝檪Ā;s㐢㐣檬;쀀⪬︀ƀflp㐮㐳㑂tcy;䑌Ā;b㐸㐹䀯Ā;a㐾㐿槄r;挿f;쀀\ud835\udd64aĀdr㑍ЂesĀ;u㑔㑕晠it\xbb㑕ƀcsu㑠㑹㒟Āau㑥㑯pĀ;sᆈ㑫;쀀⊓︀pĀ;sᆴ㑵;쀀⊔︀uĀbp㑿㒏ƀ;esᆗᆜ㒆etĀ;eᆗ㒍\xf1ᆝƀ;esᆨᆭ㒖etĀ;eᆨ㒝\xf1ᆮƀ;afᅻ㒦ְrť㒫ֱ\xbbᅼar\xf2ᅈȀcemt㒹㒾㓂㓅r;쀀\ud835\udcc8tm\xee\xf1i\xec㐕ar\xe6ᆾĀar㓎㓕rĀ;f㓔ឿ昆Āan㓚㓭ightĀep㓣㓪psilo\xeeỠh\xe9⺯s\xbb⡒ʀbcmnp㓻㕞ሉ㖋㖎Ҁ;Edemnprs㔎㔏㔑㔕㔞㔣㔬㔱㔶抂;櫅ot;檽Ā;dᇚ㔚ot;櫃ult;櫁ĀEe㔨㔪;櫋;把lus;檿arr;楹ƀeiu㔽㕒㕕tƀ;en㔎㕅㕋qĀ;qᇚ㔏eqĀ;q㔫㔨m;櫇Ābp㕚㕜;櫕;櫓c̀;acensᇭ㕬㕲㕹㕻㌦ppro\xf8㋺urlye\xf1ᇾ\xf1ᇳƀaes㖂㖈㌛ppro\xf8㌚q\xf1㌗g;晪ڀ123;Edehlmnps㖩㖬㖯ሜ㖲㖴㗀㗉㗕㗚㗟㗨㗭耻\xb9䂹耻\xb2䂲耻\xb3䂳;櫆Āos㖹㖼t;檾ub;櫘Ā;dሢ㗅ot;櫄sĀou㗏㗒l;柉b;櫗arr;楻ult;櫂ĀEe㗤㗦;櫌;抋lus;櫀ƀeiu㗴㘉㘌tƀ;enሜ㗼㘂qĀ;qሢ㖲eqĀ;q㗧㗤m;櫈Ābp㘑㘓;櫔;櫖ƀAan㘜㘠㘭rr;懙rĀhr㘦㘨\xeb∮Ā;oਫ਩war;椪lig耻\xdf䃟௡㙑㙝㙠ዎ㙳㙹\0㙾㛂\0\0\0\0\0㛛㜃\0㜉㝬\0\0\0㞇ɲ㙖\0\0㙛get;挖;䏄r\xeb๟ƀaey㙦㙫㙰ron;䅥dil;䅣;䑂lrec;挕r;쀀\ud835\udd31Ȁeiko㚆㚝㚵㚼ǲ㚋\0㚑eĀ4fኄኁaƀ;sv㚘㚙㚛䎸ym;䏑Ācn㚢㚲kĀas㚨㚮ppro\xf8዁im\xbbኬs\xf0ኞĀas㚺㚮\xf0዁rn耻\xfe䃾Ǭ̟㛆⋧es膀\xd7;bd㛏㛐㛘䃗Ā;aᤏ㛕r;樱;樰ƀeps㛡㛣㜀\xe1⩍Ȁ;bcf҆㛬㛰㛴ot;挶ir;櫱Ā;o㛹㛼쀀\ud835\udd65rk;櫚\xe1㍢rime;怴ƀaip㜏㜒㝤d\xe5ቈ΀adempst㜡㝍㝀㝑㝗㝜㝟ngleʀ;dlqr㜰㜱㜶㝀㝂斵own\xbbᶻeftĀ;e⠀㜾\xf1म;扜ightĀ;e㊪㝋\xf1ၚot;旬inus;樺lus;樹b;槍ime;樻ezium;揢ƀcht㝲㝽㞁Āry㝷㝻;쀀\ud835\udcc9;䑆cy;䑛rok;䅧Āio㞋㞎x\xf4᝷headĀlr㞗㞠eftarro\xf7ࡏightarrow\xbbཝऀAHabcdfghlmoprstuw㟐㟓㟗㟤㟰㟼㠎㠜㠣㠴㡑㡝㡫㢩㣌㣒㣪㣶r\xf2ϭar;楣Ācr㟜㟢ute耻\xfa䃺\xf2ᅐrǣ㟪\0㟭y;䑞ve;䅭Āiy㟵㟺rc耻\xfb䃻;䑃ƀabh㠃㠆㠋r\xf2Ꭽlac;䅱a\xf2ᏃĀir㠓㠘sht;楾;쀀\ud835\udd32rave耻\xf9䃹š㠧㠱rĀlr㠬㠮\xbbॗ\xbbႃlk;斀Āct㠹㡍ɯ㠿\0\0㡊rnĀ;e㡅㡆挜r\xbb㡆op;挏ri;旸Āal㡖㡚cr;䅫肻\xa8͉Āgp㡢㡦on;䅳f;쀀\ud835\udd66̀adhlsuᅋ㡸㡽፲㢑㢠own\xe1ᎳarpoonĀlr㢈㢌ef\xf4㠭igh\xf4㠯iƀ;hl㢙㢚㢜䏅\xbbᏺon\xbb㢚parrows;懈ƀcit㢰㣄㣈ɯ㢶\0\0㣁rnĀ;e㢼㢽挝r\xbb㢽op;挎ng;䅯ri;旹cr;쀀\ud835\udccaƀdir㣙㣝㣢ot;拰lde;䅩iĀ;f㜰㣨\xbb᠓Āam㣯㣲r\xf2㢨l耻\xfc䃼angle;榧ހABDacdeflnoprsz㤜㤟㤩㤭㦵㦸㦽㧟㧤㧨㧳㧹㧽㨁㨠r\xf2ϷarĀ;v㤦㤧櫨;櫩as\xe8ϡĀnr㤲㤷grt;榜΀eknprst㓣㥆㥋㥒㥝㥤㦖app\xe1␕othin\xe7ẖƀhir㓫⻈㥙op\xf4⾵Ā;hᎷ㥢\xefㆍĀiu㥩㥭gm\xe1㎳Ābp㥲㦄setneqĀ;q㥽㦀쀀⊊︀;쀀⫋︀setneqĀ;q㦏㦒쀀⊋︀;쀀⫌︀Āhr㦛㦟et\xe1㚜iangleĀlr㦪㦯eft\xbbथight\xbbၑy;䐲ash\xbbံƀelr㧄㧒㧗ƀ;beⷪ㧋㧏ar;抻q;扚lip;拮Ābt㧜ᑨa\xf2ᑩr;쀀\ud835\udd33tr\xe9㦮suĀbp㧯㧱\xbbജ\xbb൙pf;쀀\ud835\udd67ro\xf0໻tr\xe9㦴Ācu㨆㨋r;쀀\ud835\udccbĀbp㨐㨘nĀEe㦀㨖\xbb㥾nĀEe㦒㨞\xbb㦐igzag;榚΀cefoprs㨶㨻㩖㩛㩔㩡㩪irc;䅵Ādi㩀㩑Ābg㩅㩉ar;機eĀ;qᗺ㩏;扙erp;愘r;쀀\ud835\udd34pf;쀀\ud835\udd68Ā;eᑹ㩦at\xe8ᑹcr;쀀\ud835\udcccૣណ㪇\0㪋\0㪐㪛\0\0㪝㪨㪫㪯\0\0㫃㫎\0㫘ៜ៟tr\xe9៑r;쀀\ud835\udd35ĀAa㪔㪗r\xf2σr\xf2৶;䎾ĀAa㪡㪤r\xf2θr\xf2৫a\xf0✓is;拻ƀdptឤ㪵㪾Āfl㪺ឩ;쀀\ud835\udd69im\xe5ឲĀAa㫇㫊r\xf2ώr\xf2ਁĀcq㫒ីr;쀀\ud835\udccdĀpt៖㫜r\xe9។Ѐacefiosu㫰㫽㬈㬌㬑㬕㬛㬡cĀuy㫶㫻te耻\xfd䃽;䑏Āiy㬂㬆rc;䅷;䑋n耻\xa5䂥r;쀀\ud835\udd36cy;䑗pf;쀀\ud835\udd6acr;쀀\ud835\udcceĀcm㬦㬩y;䑎l耻\xff䃿Ԁacdefhiosw㭂㭈㭔㭘㭤㭩㭭㭴㭺㮀cute;䅺Āay㭍㭒ron;䅾;䐷ot;䅼Āet㭝㭡tr\xe6ᕟa;䎶r;쀀\ud835\udd37cy;䐶grarr;懝pf;쀀\ud835\udd6bcr;쀀\ud835\udccfĀjn㮅㮇;怍j;怌'.split("").map(t=>t.charCodeAt(0))),t7=new Uint16Array("Ȁaglq	\x15\x18\x1bɭ\x0f\0\0\x12p;䀦os;䀧t;䀾t;䀼uot;䀢".split("").map(t=>t.charCodeAt(0))),et=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]),ee=null!==(s=String.fromCodePoint)&&void 0!==s?s:function(t){let e="";return t>65535&&(t-=65536,e+=String.fromCharCode(t>>>10&1023|55296),t=56320|1023&t),e+=String.fromCharCode(t)};function ei(t){var e;return t>=55296&&t<=57343||t>1114111?65533:null!==(e=et.get(t))&&void 0!==e?e:t}function en(t){return t>=r.ZERO&&t<=r.NINE}(function(t){t[t.NUM=35]="NUM",t[t.SEMI=59]="SEMI",t[t.EQUALS=61]="EQUALS",t[t.ZERO=48]="ZERO",t[t.NINE=57]="NINE",t[t.LOWER_A=97]="LOWER_A",t[t.LOWER_F=102]="LOWER_F",t[t.LOWER_X=120]="LOWER_X",t[t.LOWER_Z=122]="LOWER_Z",t[t.UPPER_A=65]="UPPER_A",t[t.UPPER_F=70]="UPPER_F",t[t.UPPER_Z=90]="UPPER_Z"})(r||(r={})),function(t){t[t.VALUE_LENGTH=49152]="VALUE_LENGTH",t[t.BRANCH_LENGTH=16256]="BRANCH_LENGTH",t[t.JUMP_TABLE=127]="JUMP_TABLE"}(a||(a={})),function(t){t[t.EntityStart=0]="EntityStart",t[t.NumericStart=1]="NumericStart",t[t.NumericDecimal=2]="NumericDecimal",t[t.NumericHex=3]="NumericHex",t[t.NamedEntity=4]="NamedEntity"}(o||(o={})),function(t){t[t.Legacy=0]="Legacy",t[t.Strict=1]="Strict",t[t.Attribute=2]="Attribute"}(_||(_={}));class es{constructor(t,e,i){this.decodeTree=t,this.emitCodePoint=e,this.errors=i,this.state=o.EntityStart,this.consumed=1,this.result=0,this.treeIndex=0,this.excess=1,this.decodeMode=_.Strict}startEntity(t){this.decodeMode=t,this.state=o.EntityStart,this.result=0,this.treeIndex=0,this.excess=1,this.consumed=1}write(t,e){switch(this.state){case o.EntityStart:if(t.charCodeAt(e)===r.NUM)return this.state=o.NumericStart,this.consumed+=1,this.stateNumericStart(t,e+1);return this.state=o.NamedEntity,this.stateNamedEntity(t,e);case o.NumericStart:return this.stateNumericStart(t,e);case o.NumericDecimal:return this.stateNumericDecimal(t,e);case o.NumericHex:return this.stateNumericHex(t,e);case o.NamedEntity:return this.stateNamedEntity(t,e)}}stateNumericStart(t,e){return e>=t.length?-1:(32|t.charCodeAt(e))===r.LOWER_X?(this.state=o.NumericHex,this.consumed+=1,this.stateNumericHex(t,e+1)):(this.state=o.NumericDecimal,this.stateNumericDecimal(t,e))}addToNumericResult(t,e,i,n){if(e!==i){let s=i-e;this.result=this.result*Math.pow(n,s)+parseInt(t.substr(e,s),n),this.consumed+=s}}stateNumericHex(t,e){let i=e;for(;e<t.length;){var n;let s=t.charCodeAt(e);if(!en(s)&&(!((n=s)>=r.UPPER_A)||!(n<=r.UPPER_F))&&(!(n>=r.LOWER_A)||!(n<=r.LOWER_F)))return this.addToNumericResult(t,i,e,16),this.emitNumericEntity(s,3);e+=1}return this.addToNumericResult(t,i,e,16),-1}stateNumericDecimal(t,e){let i=e;for(;e<t.length;){let n=t.charCodeAt(e);if(!en(n))return this.addToNumericResult(t,i,e,10),this.emitNumericEntity(n,2);e+=1}return this.addToNumericResult(t,i,e,10),-1}emitNumericEntity(t,e){var i;if(this.consumed<=e)return null===(i=this.errors)||void 0===i||i.absenceOfDigitsInNumericCharacterReference(this.consumed),0;if(t===r.SEMI)this.consumed+=1;else if(this.decodeMode===_.Strict)return 0;return this.emitCodePoint(ei(this.result),this.consumed),this.errors&&(t!==r.SEMI&&this.errors.missingSemicolonAfterCharacterReference(),this.errors.validateNumericCharacterReference(this.result)),this.consumed}stateNamedEntity(t,e){let{decodeTree:i}=this,n=i[this.treeIndex],s=(n&a.VALUE_LENGTH)>>14;for(;e<t.length;e++,this.excess++){let o=t.charCodeAt(e);if(this.treeIndex=ea(i,n,this.treeIndex+Math.max(1,s),o),this.treeIndex<0)return 0===this.result||this.decodeMode===_.Attribute&&(0===s||function(t){var e;return t===r.EQUALS||(e=t)>=r.UPPER_A&&e<=r.UPPER_Z||e>=r.LOWER_A&&e<=r.LOWER_Z||en(e)}(o))?0:this.emitNotTerminatedNamedEntity();if(0!=(s=((n=i[this.treeIndex])&a.VALUE_LENGTH)>>14)){if(o===r.SEMI)return this.emitNamedEntityData(this.treeIndex,s,this.consumed+this.excess);this.decodeMode!==_.Strict&&(this.result=this.treeIndex,this.consumed+=this.excess,this.excess=0)}}return -1}emitNotTerminatedNamedEntity(){var t;let{result:e,decodeTree:i}=this,n=(i[e]&a.VALUE_LENGTH)>>14;return this.emitNamedEntityData(e,n,this.consumed),null===(t=this.errors)||void 0===t||t.missingSemicolonAfterCharacterReference(),this.consumed}emitNamedEntityData(t,e,i){let{decodeTree:n}=this;return this.emitCodePoint(1===e?n[t]&~a.VALUE_LENGTH:n[t+1],i),3===e&&this.emitCodePoint(n[t+2],i),i}end(){var t;switch(this.state){case o.NamedEntity:return 0!==this.result&&(this.decodeMode!==_.Attribute||this.result===this.treeIndex)?this.emitNotTerminatedNamedEntity():0;case o.NumericDecimal:return this.emitNumericEntity(0,2);case o.NumericHex:return this.emitNumericEntity(0,3);case o.NumericStart:return null===(t=this.errors)||void 0===t||t.absenceOfDigitsInNumericCharacterReference(this.consumed),0;case o.EntityStart:return 0}}}function er(t){let e="",i=new es(t,t=>e+=ee(t));return function(t,n){let s=0,r=0;for(;(r=t.indexOf("&",r))>=0;){e+=t.slice(s,r),i.startEntity(n);let a=i.write(t,r+1);if(a<0){s=r+i.end();break}s=r+a,r=0===a?s+1:s}let a=e+t.slice(s);return e="",a}}function ea(t,e,i,n){let s=(e&a.BRANCH_LENGTH)>>7,r=e&a.JUMP_TABLE;if(0===s)return 0!==r&&n===r?i:-1;if(r){let e=n-r;return e<0||e>=s?-1:t[i+e]-1}let o=i,_=o+s-1;for(;o<=_;){let e=o+_>>>1,i=t[e];if(i<n)o=e+1;else{if(!(i>n))return t[e+s];_=e-1}}return -1}function eo(t){return t===h.Space||t===h.NewLine||t===h.Tab||t===h.FormFeed||t===h.CarriageReturn}function e_(t){return t===h.Slash||t===h.Gt||eo(t)}function eh(t){return t>=h.Zero&&t<=h.Nine}er(t4),er(t7),function(t){t[t.Tab=9]="Tab",t[t.NewLine=10]="NewLine",t[t.FormFeed=12]="FormFeed",t[t.CarriageReturn=13]="CarriageReturn",t[t.Space=32]="Space",t[t.ExclamationMark=33]="ExclamationMark",t[t.Number=35]="Number",t[t.Amp=38]="Amp",t[t.SingleQuote=39]="SingleQuote",t[t.DoubleQuote=34]="DoubleQuote",t[t.Dash=45]="Dash",t[t.Slash=47]="Slash",t[t.Zero=48]="Zero",t[t.Nine=57]="Nine",t[t.Semi=59]="Semi",t[t.Lt=60]="Lt",t[t.Eq=61]="Eq",t[t.Gt=62]="Gt",t[t.Questionmark=63]="Questionmark",t[t.UpperA=65]="UpperA",t[t.LowerA=97]="LowerA",t[t.UpperF=70]="UpperF",t[t.LowerF=102]="LowerF",t[t.UpperZ=90]="UpperZ",t[t.LowerZ=122]="LowerZ",t[t.LowerX=120]="LowerX",t[t.OpeningSquareBracket=91]="OpeningSquareBracket"}(h||(h={})),function(t){t[t.Text=1]="Text",t[t.BeforeTagName=2]="BeforeTagName",t[t.InTagName=3]="InTagName",t[t.InSelfClosingTag=4]="InSelfClosingTag",t[t.BeforeClosingTagName=5]="BeforeClosingTagName",t[t.InClosingTagName=6]="InClosingTagName",t[t.AfterClosingTagName=7]="AfterClosingTagName",t[t.BeforeAttributeName=8]="BeforeAttributeName",t[t.InAttributeName=9]="InAttributeName",t[t.AfterAttributeName=10]="AfterAttributeName",t[t.BeforeAttributeValue=11]="BeforeAttributeValue",t[t.InAttributeValueDq=12]="InAttributeValueDq",t[t.InAttributeValueSq=13]="InAttributeValueSq",t[t.InAttributeValueNq=14]="InAttributeValueNq",t[t.BeforeDeclaration=15]="BeforeDeclaration",t[t.InDeclaration=16]="InDeclaration",t[t.InProcessingInstruction=17]="InProcessingInstruction",t[t.BeforeComment=18]="BeforeComment",t[t.CDATASequence=19]="CDATASequence",t[t.InSpecialComment=20]="InSpecialComment",t[t.InCommentLike=21]="InCommentLike",t[t.BeforeSpecialS=22]="BeforeSpecialS",t[t.SpecialStartSequence=23]="SpecialStartSequence",t[t.InSpecialTag=24]="InSpecialTag",t[t.BeforeEntity=25]="BeforeEntity",t[t.BeforeNumericEntity=26]="BeforeNumericEntity",t[t.InNamedEntity=27]="InNamedEntity",t[t.InNumericEntity=28]="InNumericEntity",t[t.InHexEntity=29]="InHexEntity"}(u||(u={})),function(t){t[t.NoValue=0]="NoValue",t[t.Unquoted=1]="Unquoted",t[t.Single=2]="Single",t[t.Double=3]="Double"}(l||(l={}));let eu={Cdata:new Uint8Array([67,68,65,84,65,91]),CdataEnd:new Uint8Array([93,93,62]),CommentEnd:new Uint8Array([45,45,62]),ScriptEnd:new Uint8Array([60,47,115,99,114,105,112,116]),StyleEnd:new Uint8Array([60,47,115,116,121,108,101]),TitleEnd:new Uint8Array([60,47,116,105,116,108,101])};class el{constructor({xmlMode:t=!1,decodeEntities:e=!0},i){this.cbs=i,this.state=u.Text,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=u.Text,this.isSpecial=!1,this.running=!0,this.offset=0,this.currentSequence=void 0,this.sequenceIndex=0,this.trieIndex=0,this.trieCurrent=0,this.entityResult=0,this.entityExcess=0,this.xmlMode=t,this.decodeEntities=e,this.entityTrie=t?t7:t4}reset(){this.state=u.Text,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=u.Text,this.currentSequence=void 0,this.running=!0,this.offset=0}write(t){this.offset+=this.buffer.length,this.buffer=t,this.parse()}end(){this.running&&this.finish()}pause(){this.running=!1}resume(){this.running=!0,this.index<this.buffer.length+this.offset&&this.parse()}getIndex(){return this.index}getSectionStart(){return this.sectionStart}stateText(t){t===h.Lt||!this.decodeEntities&&this.fastForwardTo(h.Lt)?(this.index>this.sectionStart&&this.cbs.ontext(this.sectionStart,this.index),this.state=u.BeforeTagName,this.sectionStart=this.index):this.decodeEntities&&t===h.Amp&&(this.state=u.BeforeEntity)}stateSpecialStartSequence(t){let e=this.sequenceIndex===this.currentSequence.length;if(e?e_(t):(32|t)===this.currentSequence[this.sequenceIndex]){if(!e){this.sequenceIndex++;return}}else this.isSpecial=!1;this.sequenceIndex=0,this.state=u.InTagName,this.stateInTagName(t)}stateInSpecialTag(t){if(this.sequenceIndex===this.currentSequence.length){if(t===h.Gt||eo(t)){let e=this.index-this.currentSequence.length;if(this.sectionStart<e){let t=this.index;this.index=e,this.cbs.ontext(this.sectionStart,e),this.index=t}this.isSpecial=!1,this.sectionStart=e+2,this.stateInClosingTagName(t);return}this.sequenceIndex=0}(32|t)===this.currentSequence[this.sequenceIndex]?this.sequenceIndex+=1:0===this.sequenceIndex?this.currentSequence===eu.TitleEnd?this.decodeEntities&&t===h.Amp&&(this.state=u.BeforeEntity):this.fastForwardTo(h.Lt)&&(this.sequenceIndex=1):this.sequenceIndex=Number(t===h.Lt)}stateCDATASequence(t){t===eu.Cdata[this.sequenceIndex]?++this.sequenceIndex===eu.Cdata.length&&(this.state=u.InCommentLike,this.currentSequence=eu.CdataEnd,this.sequenceIndex=0,this.sectionStart=this.index+1):(this.sequenceIndex=0,this.state=u.InDeclaration,this.stateInDeclaration(t))}fastForwardTo(t){for(;++this.index<this.buffer.length+this.offset;)if(this.buffer.charCodeAt(this.index-this.offset)===t)return!0;return this.index=this.buffer.length+this.offset-1,!1}stateInCommentLike(t){t===this.currentSequence[this.sequenceIndex]?++this.sequenceIndex===this.currentSequence.length&&(this.currentSequence===eu.CdataEnd?this.cbs.oncdata(this.sectionStart,this.index,2):this.cbs.oncomment(this.sectionStart,this.index,2),this.sequenceIndex=0,this.sectionStart=this.index+1,this.state=u.Text):0===this.sequenceIndex?this.fastForwardTo(this.currentSequence[0])&&(this.sequenceIndex=1):t!==this.currentSequence[this.sequenceIndex-1]&&(this.sequenceIndex=0)}isTagStartChar(t){return this.xmlMode?!e_(t):t>=h.LowerA&&t<=h.LowerZ||t>=h.UpperA&&t<=h.UpperZ}startSpecial(t,e){this.isSpecial=!0,this.currentSequence=t,this.sequenceIndex=e,this.state=u.SpecialStartSequence}stateBeforeTagName(t){if(t===h.ExclamationMark)this.state=u.BeforeDeclaration,this.sectionStart=this.index+1;else if(t===h.Questionmark)this.state=u.InProcessingInstruction,this.sectionStart=this.index+1;else if(this.isTagStartChar(t)){let e=32|t;this.sectionStart=this.index,this.xmlMode||e!==eu.TitleEnd[2]?this.state=this.xmlMode||e!==eu.ScriptEnd[2]?u.InTagName:u.BeforeSpecialS:this.startSpecial(eu.TitleEnd,3)}else t===h.Slash?this.state=u.BeforeClosingTagName:(this.state=u.Text,this.stateText(t))}stateInTagName(t){e_(t)&&(this.cbs.onopentagname(this.sectionStart,this.index),this.sectionStart=-1,this.state=u.BeforeAttributeName,this.stateBeforeAttributeName(t))}stateBeforeClosingTagName(t){eo(t)||(t===h.Gt?this.state=u.Text:(this.state=this.isTagStartChar(t)?u.InClosingTagName:u.InSpecialComment,this.sectionStart=this.index))}stateInClosingTagName(t){(t===h.Gt||eo(t))&&(this.cbs.onclosetag(this.sectionStart,this.index),this.sectionStart=-1,this.state=u.AfterClosingTagName,this.stateAfterClosingTagName(t))}stateAfterClosingTagName(t){(t===h.Gt||this.fastForwardTo(h.Gt))&&(this.state=u.Text,this.baseState=u.Text,this.sectionStart=this.index+1)}stateBeforeAttributeName(t){t===h.Gt?(this.cbs.onopentagend(this.index),this.isSpecial?(this.state=u.InSpecialTag,this.sequenceIndex=0):this.state=u.Text,this.baseState=this.state,this.sectionStart=this.index+1):t===h.Slash?this.state=u.InSelfClosingTag:eo(t)||(this.state=u.InAttributeName,this.sectionStart=this.index)}stateInSelfClosingTag(t){t===h.Gt?(this.cbs.onselfclosingtag(this.index),this.state=u.Text,this.baseState=u.Text,this.sectionStart=this.index+1,this.isSpecial=!1):eo(t)||(this.state=u.BeforeAttributeName,this.stateBeforeAttributeName(t))}stateInAttributeName(t){(t===h.Eq||e_(t))&&(this.cbs.onattribname(this.sectionStart,this.index),this.sectionStart=-1,this.state=u.AfterAttributeName,this.stateAfterAttributeName(t))}stateAfterAttributeName(t){t===h.Eq?this.state=u.BeforeAttributeValue:t===h.Slash||t===h.Gt?(this.cbs.onattribend(l.NoValue,this.index),this.state=u.BeforeAttributeName,this.stateBeforeAttributeName(t)):eo(t)||(this.cbs.onattribend(l.NoValue,this.index),this.state=u.InAttributeName,this.sectionStart=this.index)}stateBeforeAttributeValue(t){t===h.DoubleQuote?(this.state=u.InAttributeValueDq,this.sectionStart=this.index+1):t===h.SingleQuote?(this.state=u.InAttributeValueSq,this.sectionStart=this.index+1):eo(t)||(this.sectionStart=this.index,this.state=u.InAttributeValueNq,this.stateInAttributeValueNoQuotes(t))}handleInAttributeValue(t,e){t===e||!this.decodeEntities&&this.fastForwardTo(e)?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(e===h.DoubleQuote?l.Double:l.Single,this.index),this.state=u.BeforeAttributeName):this.decodeEntities&&t===h.Amp&&(this.baseState=this.state,this.state=u.BeforeEntity)}stateInAttributeValueDoubleQuotes(t){this.handleInAttributeValue(t,h.DoubleQuote)}stateInAttributeValueSingleQuotes(t){this.handleInAttributeValue(t,h.SingleQuote)}stateInAttributeValueNoQuotes(t){eo(t)||t===h.Gt?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(l.Unquoted,this.index),this.state=u.BeforeAttributeName,this.stateBeforeAttributeName(t)):this.decodeEntities&&t===h.Amp&&(this.baseState=this.state,this.state=u.BeforeEntity)}stateBeforeDeclaration(t){t===h.OpeningSquareBracket?(this.state=u.CDATASequence,this.sequenceIndex=0):this.state=t===h.Dash?u.BeforeComment:u.InDeclaration}stateInDeclaration(t){(t===h.Gt||this.fastForwardTo(h.Gt))&&(this.cbs.ondeclaration(this.sectionStart,this.index),this.state=u.Text,this.sectionStart=this.index+1)}stateInProcessingInstruction(t){(t===h.Gt||this.fastForwardTo(h.Gt))&&(this.cbs.onprocessinginstruction(this.sectionStart,this.index),this.state=u.Text,this.sectionStart=this.index+1)}stateBeforeComment(t){t===h.Dash?(this.state=u.InCommentLike,this.currentSequence=eu.CommentEnd,this.sequenceIndex=2,this.sectionStart=this.index+1):this.state=u.InDeclaration}stateInSpecialComment(t){(t===h.Gt||this.fastForwardTo(h.Gt))&&(this.cbs.oncomment(this.sectionStart,this.index,0),this.state=u.Text,this.sectionStart=this.index+1)}stateBeforeSpecialS(t){let e=32|t;e===eu.ScriptEnd[3]?this.startSpecial(eu.ScriptEnd,4):e===eu.StyleEnd[3]?this.startSpecial(eu.StyleEnd,4):(this.state=u.InTagName,this.stateInTagName(t))}stateBeforeEntity(t){this.entityExcess=1,this.entityResult=0,t===h.Number?this.state=u.BeforeNumericEntity:t===h.Amp||(this.trieIndex=0,this.trieCurrent=this.entityTrie[0],this.state=u.InNamedEntity,this.stateInNamedEntity(t))}stateInNamedEntity(t){if(this.entityExcess+=1,this.trieIndex=ea(this.entityTrie,this.trieCurrent,this.trieIndex+1,t),this.trieIndex<0){this.emitNamedEntity(),this.index--;return}this.trieCurrent=this.entityTrie[this.trieIndex];let e=this.trieCurrent&a.VALUE_LENGTH;if(e){let i=(e>>14)-1;if(this.allowLegacyEntity()||t===h.Semi){let t=this.index-this.entityExcess+1;t>this.sectionStart&&this.emitPartial(this.sectionStart,t),this.entityResult=this.trieIndex,this.trieIndex+=i,this.entityExcess=0,this.sectionStart=this.index+1,0===i&&this.emitNamedEntity()}else this.trieIndex+=i}}emitNamedEntity(){if(this.state=this.baseState,0!==this.entityResult)switch((this.entityTrie[this.entityResult]&a.VALUE_LENGTH)>>14){case 1:this.emitCodePoint(this.entityTrie[this.entityResult]&~a.VALUE_LENGTH);break;case 2:this.emitCodePoint(this.entityTrie[this.entityResult+1]);break;case 3:this.emitCodePoint(this.entityTrie[this.entityResult+1]),this.emitCodePoint(this.entityTrie[this.entityResult+2])}}stateBeforeNumericEntity(t){(32|t)===h.LowerX?(this.entityExcess++,this.state=u.InHexEntity):(this.state=u.InNumericEntity,this.stateInNumericEntity(t))}emitNumericEntity(t){let e=this.index-this.entityExcess-1;e+2+Number(this.state===u.InHexEntity)!==this.index&&(e>this.sectionStart&&this.emitPartial(this.sectionStart,e),this.sectionStart=this.index+Number(t),this.emitCodePoint(ei(this.entityResult))),this.state=this.baseState}stateInNumericEntity(t){t===h.Semi?this.emitNumericEntity(!0):eh(t)?(this.entityResult=10*this.entityResult+(t-h.Zero),this.entityExcess++):(this.allowLegacyEntity()?this.emitNumericEntity(!1):this.state=this.baseState,this.index--)}stateInHexEntity(t){t===h.Semi?this.emitNumericEntity(!0):eh(t)?(this.entityResult=16*this.entityResult+(t-h.Zero),this.entityExcess++):t>=h.UpperA&&t<=h.UpperF||t>=h.LowerA&&t<=h.LowerF?(this.entityResult=16*this.entityResult+((32|t)-h.LowerA+10),this.entityExcess++):(this.allowLegacyEntity()?this.emitNumericEntity(!1):this.state=this.baseState,this.index--)}allowLegacyEntity(){return!this.xmlMode&&(this.baseState===u.Text||this.baseState===u.InSpecialTag)}cleanup(){this.running&&this.sectionStart!==this.index&&(this.state===u.Text||this.state===u.InSpecialTag&&0===this.sequenceIndex?(this.cbs.ontext(this.sectionStart,this.index),this.sectionStart=this.index):(this.state===u.InAttributeValueDq||this.state===u.InAttributeValueSq||this.state===u.InAttributeValueNq)&&(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=this.index))}shouldContinue(){return this.index<this.buffer.length+this.offset&&this.running}parse(){for(;this.shouldContinue();){let t=this.buffer.charCodeAt(this.index-this.offset);switch(this.state){case u.Text:this.stateText(t);break;case u.SpecialStartSequence:this.stateSpecialStartSequence(t);break;case u.InSpecialTag:this.stateInSpecialTag(t);break;case u.CDATASequence:this.stateCDATASequence(t);break;case u.InAttributeValueDq:this.stateInAttributeValueDoubleQuotes(t);break;case u.InAttributeName:this.stateInAttributeName(t);break;case u.InCommentLike:this.stateInCommentLike(t);break;case u.InSpecialComment:this.stateInSpecialComment(t);break;case u.BeforeAttributeName:this.stateBeforeAttributeName(t);break;case u.InTagName:this.stateInTagName(t);break;case u.InClosingTagName:this.stateInClosingTagName(t);break;case u.BeforeTagName:this.stateBeforeTagName(t);break;case u.AfterAttributeName:this.stateAfterAttributeName(t);break;case u.InAttributeValueSq:this.stateInAttributeValueSingleQuotes(t);break;case u.BeforeAttributeValue:this.stateBeforeAttributeValue(t);break;case u.BeforeClosingTagName:this.stateBeforeClosingTagName(t);break;case u.AfterClosingTagName:this.stateAfterClosingTagName(t);break;case u.BeforeSpecialS:this.stateBeforeSpecialS(t);break;case u.InAttributeValueNq:this.stateInAttributeValueNoQuotes(t);break;case u.InSelfClosingTag:this.stateInSelfClosingTag(t);break;case u.InDeclaration:this.stateInDeclaration(t);break;case u.BeforeDeclaration:this.stateBeforeDeclaration(t);break;case u.BeforeComment:this.stateBeforeComment(t);break;case u.InProcessingInstruction:this.stateInProcessingInstruction(t);break;case u.InNamedEntity:this.stateInNamedEntity(t);break;case u.BeforeEntity:this.stateBeforeEntity(t);break;case u.InHexEntity:this.stateInHexEntity(t);break;case u.InNumericEntity:this.stateInNumericEntity(t);break;default:this.stateBeforeNumericEntity(t)}this.index++}this.cleanup()}finish(){this.state===u.InNamedEntity&&this.emitNamedEntity(),this.sectionStart<this.index&&this.handleTrailingData(),this.cbs.onend()}handleTrailingData(){let t=this.buffer.length+this.offset;this.state===u.InCommentLike?this.currentSequence===eu.CdataEnd?this.cbs.oncdata(this.sectionStart,t,0):this.cbs.oncomment(this.sectionStart,t,0):this.state===u.InNumericEntity&&this.allowLegacyEntity()?this.emitNumericEntity(!1):this.state===u.InHexEntity&&this.allowLegacyEntity()?this.emitNumericEntity(!1):this.state===u.InTagName||this.state===u.BeforeAttributeName||this.state===u.BeforeAttributeValue||this.state===u.AfterAttributeName||this.state===u.InAttributeName||this.state===u.InAttributeValueSq||this.state===u.InAttributeValueDq||this.state===u.InAttributeValueNq||this.state===u.InClosingTagName||this.cbs.ontext(this.sectionStart,t)}emitPartial(t,e){this.baseState!==u.Text&&this.baseState!==u.InSpecialTag?this.cbs.onattribdata(t,e):this.cbs.ontext(t,e)}emitCodePoint(t){this.baseState!==u.Text&&this.baseState!==u.InSpecialTag?this.cbs.onattribentity(t):this.cbs.ontextentity(t)}}let ep=new Set(["input","option","optgroup","select","button","datalist","textarea"]),ec=new Set(["p"]),ed=new Set(["thead","tbody"]),ef=new Set(["dd","dt"]),eg=new Set(["rt","rp"]),em=new Map([["tr",new Set(["tr","th","td"])],["th",new Set(["th"])],["td",new Set(["thead","th","td"])],["body",new Set(["head","link","script"])],["li",new Set(["li"])],["p",ec],["h1",ec],["h2",ec],["h3",ec],["h4",ec],["h5",ec],["h6",ec],["select",ep],["input",ep],["output",ep],["button",ep],["datalist",ep],["textarea",ep],["option",new Set(["option"])],["optgroup",new Set(["optgroup","option"])],["dd",ef],["dt",ef],["address",ec],["article",ec],["aside",ec],["blockquote",ec],["details",ec],["div",ec],["dl",ec],["fieldset",ec],["figcaption",ec],["figure",ec],["footer",ec],["form",ec],["header",ec],["hr",ec],["main",ec],["nav",ec],["ol",ec],["pre",ec],["section",ec],["table",ec],["ul",ec],["rt",eg],["rp",eg],["tbody",ed],["tfoot",ed]]),eb=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]),ey=new Set(["math","svg"]),ex=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignobject","desc","title"]),ew=/\s|\//;class ek{constructor(t,e={}){var i,n,s,r,a;this.options=e,this.startIndex=0,this.endIndex=0,this.openTagStart=0,this.tagname="",this.attribname="",this.attribvalue="",this.attribs=null,this.stack=[],this.foreignContext=[],this.buffers=[],this.bufferOffset=0,this.writeIndex=0,this.ended=!1,this.cbs=null!=t?t:{},this.lowerCaseTagNames=null!==(i=e.lowerCaseTags)&&void 0!==i?i:!e.xmlMode,this.lowerCaseAttributeNames=null!==(n=e.lowerCaseAttributeNames)&&void 0!==n?n:!e.xmlMode,this.tokenizer=new(null!==(s=e.Tokenizer)&&void 0!==s?s:el)(this.options,this),null===(a=(r=this.cbs).onparserinit)||void 0===a||a.call(r,this)}ontext(t,e){var i,n;let s=this.getSlice(t,e);this.endIndex=e-1,null===(n=(i=this.cbs).ontext)||void 0===n||n.call(i,s),this.startIndex=e}ontextentity(t){var e,i;let n=this.tokenizer.getSectionStart();this.endIndex=n-1,null===(i=(e=this.cbs).ontext)||void 0===i||i.call(e,ee(t)),this.startIndex=n}isVoidElement(t){return!this.options.xmlMode&&eb.has(t)}onopentagname(t,e){this.endIndex=e;let i=this.getSlice(t,e);this.lowerCaseTagNames&&(i=i.toLowerCase()),this.emitOpenTag(i)}emitOpenTag(t){var e,i,n,s;this.openTagStart=this.startIndex,this.tagname=t;let r=!this.options.xmlMode&&em.get(t);if(r)for(;this.stack.length>0&&r.has(this.stack[this.stack.length-1]);){let t=this.stack.pop();null===(i=(e=this.cbs).onclosetag)||void 0===i||i.call(e,t,!0)}!this.isVoidElement(t)&&(this.stack.push(t),ey.has(t)?this.foreignContext.push(!0):ex.has(t)&&this.foreignContext.push(!1)),null===(s=(n=this.cbs).onopentagname)||void 0===s||s.call(n,t),this.cbs.onopentag&&(this.attribs={})}endOpenTag(t){var e,i;this.startIndex=this.openTagStart,this.attribs&&(null===(i=(e=this.cbs).onopentag)||void 0===i||i.call(e,this.tagname,this.attribs,t),this.attribs=null),this.cbs.onclosetag&&this.isVoidElement(this.tagname)&&this.cbs.onclosetag(this.tagname,!0),this.tagname=""}onopentagend(t){this.endIndex=t,this.endOpenTag(!1),this.startIndex=t+1}onclosetag(t,e){var i,n,s,r,a,o;this.endIndex=e;let _=this.getSlice(t,e);if(this.lowerCaseTagNames&&(_=_.toLowerCase()),(ey.has(_)||ex.has(_))&&this.foreignContext.pop(),this.isVoidElement(_))this.options.xmlMode||"br"!==_||(null===(n=(i=this.cbs).onopentagname)||void 0===n||n.call(i,"br"),null===(r=(s=this.cbs).onopentag)||void 0===r||r.call(s,"br",{},!0),null===(o=(a=this.cbs).onclosetag)||void 0===o||o.call(a,"br",!1));else{let t=this.stack.lastIndexOf(_);if(-1!==t){if(this.cbs.onclosetag){let e=this.stack.length-t;for(;e--;)this.cbs.onclosetag(this.stack.pop(),0!==e)}else this.stack.length=t}else this.options.xmlMode||"p"!==_||(this.emitOpenTag("p"),this.closeCurrentTag(!0))}this.startIndex=e+1}onselfclosingtag(t){this.endIndex=t,this.options.xmlMode||this.options.recognizeSelfClosing||this.foreignContext[this.foreignContext.length-1]?(this.closeCurrentTag(!1),this.startIndex=t+1):this.onopentagend(t)}closeCurrentTag(t){var e,i;let n=this.tagname;this.endOpenTag(t),this.stack[this.stack.length-1]===n&&(null===(i=(e=this.cbs).onclosetag)||void 0===i||i.call(e,n,!t),this.stack.pop())}onattribname(t,e){this.startIndex=t;let i=this.getSlice(t,e);this.attribname=this.lowerCaseAttributeNames?i.toLowerCase():i}onattribdata(t,e){this.attribvalue+=this.getSlice(t,e)}onattribentity(t){this.attribvalue+=ee(t)}onattribend(t,e){var i,n;this.endIndex=e,null===(n=(i=this.cbs).onattribute)||void 0===n||n.call(i,this.attribname,this.attribvalue,t===l.Double?'"':t===l.Single?"'":t===l.NoValue?void 0:null),this.attribs&&!Object.prototype.hasOwnProperty.call(this.attribs,this.attribname)&&(this.attribs[this.attribname]=this.attribvalue),this.attribvalue=""}getInstructionName(t){let e=t.search(ew),i=e<0?t:t.substr(0,e);return this.lowerCaseTagNames&&(i=i.toLowerCase()),i}ondeclaration(t,e){this.endIndex=e;let i=this.getSlice(t,e);if(this.cbs.onprocessinginstruction){let t=this.getInstructionName(i);this.cbs.onprocessinginstruction(`!${t}`,`!${i}`)}this.startIndex=e+1}onprocessinginstruction(t,e){this.endIndex=e;let i=this.getSlice(t,e);if(this.cbs.onprocessinginstruction){let t=this.getInstructionName(i);this.cbs.onprocessinginstruction(`?${t}`,`?${i}`)}this.startIndex=e+1}oncomment(t,e,i){var n,s,r,a;this.endIndex=e,null===(s=(n=this.cbs).oncomment)||void 0===s||s.call(n,this.getSlice(t,e-i)),null===(a=(r=this.cbs).oncommentend)||void 0===a||a.call(r),this.startIndex=e+1}oncdata(t,e,i){var n,s,r,a,o,_,h,u,l,p;this.endIndex=e;let c=this.getSlice(t,e-i);this.options.xmlMode||this.options.recognizeCDATA?(null===(s=(n=this.cbs).oncdatastart)||void 0===s||s.call(n),null===(a=(r=this.cbs).ontext)||void 0===a||a.call(r,c),null===(_=(o=this.cbs).oncdataend)||void 0===_||_.call(o)):(null===(u=(h=this.cbs).oncomment)||void 0===u||u.call(h,`[CDATA[${c}]]`),null===(p=(l=this.cbs).oncommentend)||void 0===p||p.call(l)),this.startIndex=e+1}onend(){var t,e;if(this.cbs.onclosetag){this.endIndex=this.startIndex;for(let t=this.stack.length;t>0;this.cbs.onclosetag(this.stack[--t],!0));}null===(e=(t=this.cbs).onend)||void 0===e||e.call(t)}reset(){var t,e,i,n;null===(e=(t=this.cbs).onreset)||void 0===e||e.call(t),this.tokenizer.reset(),this.tagname="",this.attribname="",this.attribs=null,this.stack.length=0,this.startIndex=0,this.endIndex=0,null===(n=(i=this.cbs).onparserinit)||void 0===n||n.call(i,this),this.buffers.length=0,this.bufferOffset=0,this.writeIndex=0,this.ended=!1}parseComplete(t){this.reset(),this.end(t)}getSlice(t,e){for(;t-this.bufferOffset>=this.buffers[0].length;)this.shiftBuffer();let i=this.buffers[0].slice(t-this.bufferOffset,e-this.bufferOffset);for(;e-this.bufferOffset>this.buffers[0].length;)this.shiftBuffer(),i+=this.buffers[0].slice(0,e-this.bufferOffset);return i}shiftBuffer(){this.bufferOffset+=this.buffers[0].length,this.writeIndex--,this.buffers.shift()}write(t){var e,i;if(this.ended){null===(i=(e=this.cbs).onerror)||void 0===i||i.call(e,Error(".write() after done!"));return}this.buffers.push(t),this.tokenizer.running&&(this.tokenizer.write(t),this.writeIndex++)}end(t){var e,i;if(this.ended){null===(i=(e=this.cbs).onerror)||void 0===i||i.call(e,Error(".end() after done!"));return}t&&this.write(t),this.ended=!0,this.tokenizer.end()}pause(){this.tokenizer.pause()}resume(){for(this.tokenizer.resume();this.tokenizer.running&&this.writeIndex<this.buffers.length;)this.tokenizer.write(this.buffers[this.writeIndex++]);this.ended&&this.tokenizer.end()}parseChunk(t){this.write(t)}done(t){this.end(t)}}let ev=/["&'<>$\x80-\uFFFF]/g,eE=new Map([[34,"&quot;"],[38,"&amp;"],[39,"&apos;"],[60,"&lt;"],[62,"&gt;"]]),eT=null!=String.prototype.codePointAt?(t,e)=>t.codePointAt(e):(t,e)=>(64512&t.charCodeAt(e))==55296?(t.charCodeAt(e)-55296)*1024+t.charCodeAt(e+1)-56320+65536:t.charCodeAt(e);function eS(t){let e,i="",n=0;for(;null!==(e=ev.exec(t));){let s=e.index,r=t.charCodeAt(s),a=eE.get(r);void 0!==a?(i+=t.substring(n,s)+a,n=s+1):(i+=`${t.substring(n,s)}&#x${eT(t,s).toString(16)};`,n=ev.lastIndex+=Number((64512&r)==55296))}return i+t.substr(n)}function eL(t,e){return function(i){let n;let s=0,r="";for(;n=t.exec(i);)s!==n.index&&(r+=i.substring(s,n.index)),r+=e.get(n[0].charCodeAt(0)),s=n.index+1;return r+i.substring(s)}}eL(/[&<>'"]/g,eE);let eA=eL(/["&\u00A0]/g,new Map([[34,"&quot;"],[38,"&amp;"],[160,"&nbsp;"]])),eN=eL(/[&<>\u00A0]/g,new Map([[38,"&amp;"],[60,"&lt;"],[62,"&gt;"],[160,"&nbsp;"]]));(function(t){t[t.XML=0]="XML",t[t.HTML=1]="HTML"})(p||(p={})),function(t){t[t.UTF8=0]="UTF8",t[t.ASCII=1]="ASCII",t[t.Extensive=2]="Extensive",t[t.Attribute=3]="Attribute",t[t.Text=4]="Text"}(c||(c={}));let eO=new Map(["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","textPath"].map(t=>[t.toLowerCase(),t])),eI=new Map(["definitionURL","attributeName","attributeType","baseFrequency","baseProfile","calcMode","clipPathUnits","diffuseConstant","edgeMode","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector","zoomAndPan"].map(t=>[t.toLowerCase(),t])),eC=new Set(["style","script","xmp","iframe","noembed","noframes","plaintext","noscript"]);function eR(t){return t.replace(/"/g,"&quot;")}let eB=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]);function eD(t,e={}){let i="length"in t?t:[t],n="";for(let t=0;t<i.length;t++)n+=function(t,e){switch(t.type){case g:return eD(t.children,e);case E:case b:return`<${t.data}>`;case y:return`<!--${t.data}-->`;case v:return`<![CDATA[${t.children[0].data}]]>`;case x:case w:case k:return function(t,e){var i;"foreign"===e.xmlMode&&(t.name=null!==(i=eO.get(t.name))&&void 0!==i?i:t.name,t.parent&&eP.has(t.parent.name)&&(e={...e,xmlMode:!1})),!e.xmlMode&&eW.has(t.name)&&(e={...e,xmlMode:"foreign"});let n=`<${t.name}`,s=function(t,e){var i;if(!t)return;let n=(null!==(i=e.encodeEntities)&&void 0!==i?i:e.decodeEntities)===!1?eR:e.xmlMode||"utf8"!==e.encodeEntities?eS:eA;return Object.keys(t).map(i=>{var s,r;let a=null!==(s=t[i])&&void 0!==s?s:"";return("foreign"===e.xmlMode&&(i=null!==(r=eI.get(i))&&void 0!==r?r:i),e.emptyAttrs||e.xmlMode||""!==a)?`${i}="${n(a)}"`:i}).join(" ")}(t.attribs,e);return s&&(n+=` ${s}`),0===t.children.length&&(e.xmlMode?!1!==e.selfClosingTags:e.selfClosingTags&&eB.has(t.name))?(e.xmlMode||(n+=" "),n+="/>"):(n+=">",t.children.length>0&&(n+=eD(t.children,e)),(e.xmlMode||!eB.has(t.name))&&(n+=`</${t.name}>`)),n}(t,e);case m:return function(t,e){var i;let n=t.data||"";return(null!==(i=e.encodeEntities)&&void 0!==i?i:e.decodeEntities)===!1||!e.xmlMode&&t.parent&&eC.has(t.parent.name)||(n=e.xmlMode||"utf8"!==e.encodeEntities?eS(n):eN(n)),n}(t,e)}}(i[t],e);return n}let eP=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignObject","desc","title"]),eW=new Set(["svg","math"]);function eq(t){return Array.isArray(t)?t.map(eq).join(""):isTag(t)?"br"===t.name?"\n":eq(t.children):isCDATA(t)?eq(t.children):isText(t)?t.data:""}function ej(t){return Array.isArray(t)?t.map(ej).join(""):hasChildren(t)&&!isComment(t)?ej(t.children):isText(t)?t.data:""}function eM(t){return Array.isArray(t)?t.map(eM).join(""):hasChildren(t)&&(t.type===ElementType.Tag||isCDATA(t))?eM(t.children):isText(t)?t.data:""}!function(t){t[t.DISCONNECTED=1]="DISCONNECTED",t[t.PRECEDING=2]="PRECEDING",t[t.FOLLOWING=4]="FOLLOWING",t[t.CONTAINS=8]="CONTAINS",t[t.CONTAINED_BY=16]="CONTAINED_BY"}(d||(d={}));var eU=i(18401);function ez(t,e,i=()=>void 0){if(void 0===t){let t=function(...i){return e(t,...i)};return t}return t>=0?function(...n){return e(ez(t-1,e,i),...n)}:i}function eV(t,e){let i=0,n=t.length;for(;i<n&&t[i]===e;)++i;for(;n>i&&t[n-1]===e;)--n;return i>0||n<t.length?t.substring(i,n):t}function e$(t,e){let i=new Map;for(let n=t.length;n-- >0;){let s=t[n],r=e(s);i.set(r,i.has(r)?eU(s,i.get(r),{arrayMerge:eF}):s)}return[...i.values()].reverse()}let eF=(t,e,i)=>[...e];function eG(t,e){for(let i of e){if(!t)return;t=t[i]}return t}function eK(t,e="a",i=26){let n=[];do n.push((t-=1)%i),t=t/i>>0;while(t>0);let s=e.charCodeAt(0);return n.reverse().map(t=>String.fromCharCode(s+t)).join("")}let eH=["I","X","C","M"],eX=["V","L","D"];function eQ(t){return[...t+""].map(t=>+t).reverse().map((t,e)=>t%5<4?(t<5?"":eX[e])+eH[e].repeat(t%5):eH[e]+(t<5?eX[e]:eH[e+1])).reverse().join("")}class eZ{constructor(t,e){this.lines=[],this.nextLineWords=[],this.maxLineLength=e||t.wordwrap||Number.MAX_VALUE,this.nextLineAvailableChars=this.maxLineLength,this.wrapCharacters=eG(t,["longWordSplit","wrapCharacters"])||[],this.forceWrapOnLimit=eG(t,["longWordSplit","forceWrapOnLimit"])||!1,this.stashedSpace=!1,this.wordBreakOpportunity=!1}pushWord(t,e=!1){this.nextLineAvailableChars<=0&&!e&&this.startNewLine();let i=0===this.nextLineWords.length,n=t.length+(i?0:1);if(n<=this.nextLineAvailableChars||e)this.nextLineWords.push(t),this.nextLineAvailableChars-=n;else{let[e,...n]=this.splitLongWord(t);for(let t of(i||this.startNewLine(),this.nextLineWords.push(e),this.nextLineAvailableChars-=e.length,n))this.startNewLine(),this.nextLineWords.push(t),this.nextLineAvailableChars-=t.length}}popWord(){let t=this.nextLineWords.pop();if(void 0!==t){let e=0===this.nextLineWords.length,i=t.length+(e?0:1);this.nextLineAvailableChars+=i}return t}concatWord(t,e=!1){if(this.wordBreakOpportunity&&t.length>this.nextLineAvailableChars)this.pushWord(t,e),this.wordBreakOpportunity=!1;else{let i=this.popWord();this.pushWord(i?i.concat(t):t,e)}}startNewLine(t=1){this.lines.push(this.nextLineWords),t>1&&this.lines.push(...Array.from({length:t-1},()=>[])),this.nextLineWords=[],this.nextLineAvailableChars=this.maxLineLength}isEmpty(){return 0===this.lines.length&&0===this.nextLineWords.length}clear(){this.lines.length=0,this.nextLineWords.length=0,this.nextLineAvailableChars=this.maxLineLength}toString(){return[...this.lines,this.nextLineWords].map(t=>t.join(" ")).join("\n")}splitLongWord(t){let e=[],i=0;for(;t.length>this.maxLineLength;){let n=t.substring(0,this.maxLineLength),s=t.substring(this.maxLineLength),r=n.lastIndexOf(this.wrapCharacters[i]);if(r>-1)t=n.substring(r+1)+s,e.push(n.substring(0,r+1));else if(++i<this.wrapCharacters.length)t=n+s;else{if(this.forceWrapOnLimit){if(e.push(n),(t=s).length>this.maxLineLength)continue}else t=n+s;break}}return e.push(t),e}}class eY{constructor(t=null){this.next=t}getRoot(){return this.next?this.next:this}}class eJ extends eY{constructor(t,e=null,i=1,n){super(e),this.leadingLineBreaks=i,this.inlineTextBuilder=new eZ(t,n),this.rawText="",this.stashedLineBreaks=0,this.isPre=e&&e.isPre,this.isNoWrap=e&&e.isNoWrap}}class e0 extends eJ{constructor(t,e=null,{interRowLineBreaks:i=1,leadingLineBreaks:n=2,maxLineLength:s,maxPrefixLength:r=0,prefixAlign:a="left"}={}){super(t,e,n,s),this.maxPrefixLength=r,this.prefixAlign=a,this.interRowLineBreaks=i}}class e1 extends eJ{constructor(t,e=null,{leadingLineBreaks:i=1,maxLineLength:n,prefix:s=""}={}){super(t,e,i,n),this.prefix=s}}class e2 extends eY{constructor(t=null){super(t),this.rows=[],this.isPre=t&&t.isPre,this.isNoWrap=t&&t.isNoWrap}}class e3 extends eY{constructor(t=null){super(t),this.cells=[],this.isPre=t&&t.isPre,this.isNoWrap=t&&t.isNoWrap}}class e8 extends eY{constructor(t,e=null,i){super(e),this.inlineTextBuilder=new eZ(t,i),this.rawText="",this.stashedLineBreaks=0,this.isPre=e&&e.isPre,this.isNoWrap=e&&e.isNoWrap}}class e5 extends eY{constructor(t=null,e){super(t),this.transform=e}}class e6{constructor(t){this.whitespaceChars=t.preserveNewlines?t.whitespaceCharacters.replace(/\n/g,""):t.whitespaceCharacters;let e=[...this.whitespaceChars].map(t=>"\\u"+t.charCodeAt(0).toString(16).padStart(4,"0")).join("");if(this.leadingWhitespaceRe=RegExp(`^[${e}]`),this.trailingWhitespaceRe=RegExp(`[${e}]$`),this.allWhitespaceOrEmptyRe=RegExp(`^[${e}]*$`),this.newlineOrNonWhitespaceRe=RegExp(`(\\n|[^\\n${e}])`,"g"),this.newlineOrNonNewlineStringRe=RegExp(`(\\n|[^\\n]+)`,"g"),t.preserveNewlines){let t=RegExp(`\\n|[^\\n${e}]+`,"gm");this.shrinkWrapAdd=function(e,i,n=t=>t,s=!1){if(!e)return;let r=i.stashedSpace,a=!1,o=t.exec(e);if(o)for(a=!0,"\n"===o[0]?i.startNewLine():r||this.testLeadingWhitespace(e)?i.pushWord(n(o[0]),s):i.concatWord(n(o[0]),s);null!==(o=t.exec(e));)"\n"===o[0]?i.startNewLine():i.pushWord(n(o[0]),s);i.stashedSpace=r&&!a||this.testTrailingWhitespace(e)}}else{let t=RegExp(`[^${e}]+`,"g");this.shrinkWrapAdd=function(e,i,n=t=>t,s=!1){if(!e)return;let r=i.stashedSpace,a=!1,o=t.exec(e);if(o)for(a=!0,r||this.testLeadingWhitespace(e)?i.pushWord(n(o[0]),s):i.concatWord(n(o[0]),s);null!==(o=t.exec(e));)i.pushWord(n(o[0]),s);i.stashedSpace=r&&!a||this.testTrailingWhitespace(e)}}}addLiteral(t,e,i=!0){if(!t)return;let n=e.stashedSpace,s=!1,r=this.newlineOrNonNewlineStringRe.exec(t);if(r)for(s=!0,"\n"===r[0]?e.startNewLine():n?e.pushWord(r[0],i):e.concatWord(r[0],i);null!==(r=this.newlineOrNonNewlineStringRe.exec(t));)"\n"===r[0]?e.startNewLine():e.pushWord(r[0],i);e.stashedSpace=n&&!s}testLeadingWhitespace(t){return this.leadingWhitespaceRe.test(t)}testTrailingWhitespace(t){return this.trailingWhitespaceRe.test(t)}testContainsWords(t){return!this.allWhitespaceOrEmptyRe.test(t)}countNewlinesNoWords(t){let e;this.newlineOrNonWhitespaceRe.lastIndex=0;let i=0;for(;null!==(e=this.newlineOrNonWhitespaceRe.exec(t));){if("\n"!==e[0])return 0;i++}return i}}class e9{constructor(t,e,i){this.options=t,this.picker=e,this.metadata=i,this.whitespaceProcessor=new e6(t),this._stackItem=new eJ(t),this._wordTransformer=void 0}pushWordTransform(t){this._wordTransformer=new e5(this._wordTransformer,t)}popWordTransform(){if(!this._wordTransformer)return;let t=this._wordTransformer.transform;return this._wordTransformer=this._wordTransformer.next,t}startNoWrap(){this._stackItem.isNoWrap=!0}stopNoWrap(){this._stackItem.isNoWrap=!1}_getCombinedWordTransformer(){let t=this._wordTransformer?t=>(function t(e,i){return i?t(i.transform(e),i.next):e})(t,this._wordTransformer):void 0,e=this.options.encodeCharacters;return t?e?i=>e(t(i)):t:e}_popStackItem(){let t=this._stackItem;return this._stackItem=t.next,t}addLineBreak(){(this._stackItem instanceof eJ||this._stackItem instanceof e1||this._stackItem instanceof e8)&&(this._stackItem.isPre?this._stackItem.rawText+="\n":this._stackItem.inlineTextBuilder.startNewLine())}addWordBreakOpportunity(){(this._stackItem instanceof eJ||this._stackItem instanceof e1||this._stackItem instanceof e8)&&(this._stackItem.inlineTextBuilder.wordBreakOpportunity=!0)}addInline(t,{noWordTransform:e=!1}={}){if(this._stackItem instanceof eJ||this._stackItem instanceof e1||this._stackItem instanceof e8){if(this._stackItem.isPre){this._stackItem.rawText+=t;return}if(0!==t.length&&(!this._stackItem.stashedLineBreaks||this.whitespaceProcessor.testContainsWords(t))){if(this.options.preserveNewlines){let e=this.whitespaceProcessor.countNewlinesNoWords(t);if(e>0){this._stackItem.inlineTextBuilder.startNewLine(e);return}}this._stackItem.stashedLineBreaks&&this._stackItem.inlineTextBuilder.startNewLine(this._stackItem.stashedLineBreaks),this.whitespaceProcessor.shrinkWrapAdd(t,this._stackItem.inlineTextBuilder,e?void 0:this._getCombinedWordTransformer(),this._stackItem.isNoWrap),this._stackItem.stashedLineBreaks=0}}}addLiteral(t){if((this._stackItem instanceof eJ||this._stackItem instanceof e1||this._stackItem instanceof e8)&&0!==t.length){if(this._stackItem.isPre){this._stackItem.rawText+=t;return}this._stackItem.stashedLineBreaks&&this._stackItem.inlineTextBuilder.startNewLine(this._stackItem.stashedLineBreaks),this.whitespaceProcessor.addLiteral(t,this._stackItem.inlineTextBuilder,this._stackItem.isNoWrap),this._stackItem.stashedLineBreaks=0}}openBlock({leadingLineBreaks:t=1,reservedLineLength:e=0,isPre:i=!1}={}){let n=Math.max(20,this._stackItem.inlineTextBuilder.maxLineLength-e);this._stackItem=new eJ(this.options,this._stackItem,t,n),i&&(this._stackItem.isPre=!0)}closeBlock({trailingLineBreaks:t=1,blockTransform:e}={}){let i=this._popStackItem(),n=e?e(e4(i)):e4(i);e7(this._stackItem,n,i.leadingLineBreaks,Math.max(i.stashedLineBreaks,t))}openList({maxPrefixLength:t=0,prefixAlign:e="left",interRowLineBreaks:i=1,leadingLineBreaks:n=2}={}){this._stackItem=new e0(this.options,this._stackItem,{interRowLineBreaks:i,leadingLineBreaks:n,maxLineLength:this._stackItem.inlineTextBuilder.maxLineLength,maxPrefixLength:t,prefixAlign:e})}openListItem({prefix:t=""}={}){if(!(this._stackItem instanceof e0))throw Error("Can't add a list item to something that is not a list! Check the formatter.");let e=this._stackItem,i=Math.max(t.length,e.maxPrefixLength),n=Math.max(20,e.inlineTextBuilder.maxLineLength-i);this._stackItem=new e1(this.options,e,{prefix:t,maxLineLength:n,leadingLineBreaks:e.interRowLineBreaks})}closeListItem(){let t=this._popStackItem(),e=t.next,i=Math.max(t.prefix.length,e.maxPrefixLength),n="\n"+" ".repeat(i),s=("right"===e.prefixAlign?t.prefix.padStart(i):t.prefix.padEnd(i))+e4(t).replace(/\n/g,n);e7(e,s,t.leadingLineBreaks,Math.max(t.stashedLineBreaks,e.interRowLineBreaks))}closeList({trailingLineBreaks:t=2}={}){let e=this._popStackItem(),i=e4(e);i&&e7(this._stackItem,i,e.leadingLineBreaks,t)}openTable(){this._stackItem=new e2(this._stackItem)}openTableRow(){if(!(this._stackItem instanceof e2))throw Error("Can't add a table row to something that is not a table! Check the formatter.");this._stackItem=new e3(this._stackItem)}openTableCell({maxColumnWidth:t}={}){if(!(this._stackItem instanceof e3))throw Error("Can't add a table cell to something that is not a table row! Check the formatter.");this._stackItem=new e8(this.options,this._stackItem,t)}closeTableCell({colspan:t=1,rowspan:e=1}={}){let i=this._popStackItem(),n=eV(e4(i),"\n");i.next.cells.push({colspan:t,rowspan:e,text:n})}closeTableRow(){let t=this._popStackItem();t.next.rows.push(t.cells)}closeTable({tableToString:t,leadingLineBreaks:e=2,trailingLineBreaks:i=2}){let n=t(this._popStackItem().rows);n&&e7(this._stackItem,n,e,i)}toString(){return e4(this._stackItem.getRoot())}}function e4(t){if(!(t instanceof eJ||t instanceof e1||t instanceof e8))throw Error("Only blocks, list items and table cells can be requested for text contents.");return t.inlineTextBuilder.isEmpty()?t.rawText:t.rawText+t.inlineTextBuilder.toString()}function e7(t,e,i,n){if(!(t instanceof eJ||t instanceof e1||t instanceof e8))throw Error("Only blocks, list items and table cells can contain text.");let s=e4(t),r=Math.max(t.stashedLineBreaks,i);t.inlineTextBuilder.clear(),s?t.rawText=s+"\n".repeat(r)+e:(t.rawText=e,t.leadingLineBreaks=r),t.stashedLineBreaks=n}function it(t,e,i){if(!e)return;let n=i.options;for(let s of(e.length>n.limits.maxChildNodes&&(e=e.slice(0,n.limits.maxChildNodes)).push({data:n.limits.ellipsis,type:"text"}),e))switch(s.type){case"text":i.addInline(s.data);break;case"tag":{let e=i.picker.pick1(s);(0,n.formatters[e.format])(s,t,i,e.options||{})}}}function ie(t){let e=t.attribs&&t.attribs.length?" "+Object.entries(t.attribs).map(([t,e])=>""===e?t:`${t}=${e.replace(/"/g,"&quot;")}`).join(" "):"";return`<${t.name}${e}>`}function ii(t){return`</${t.name}>`}var is=Object.freeze({__proto__:null,block:function(t,e,i,n){i.openBlock({leadingLineBreaks:n.leadingLineBreaks||2}),e(t.children,i),i.closeBlock({trailingLineBreaks:n.trailingLineBreaks||2})},blockHtml:function(t,e,i,n){i.openBlock({leadingLineBreaks:n.leadingLineBreaks||2}),i.startNoWrap(),i.addLiteral(eD(t,{decodeEntities:i.options.decodeEntities})),i.stopNoWrap(),i.closeBlock({trailingLineBreaks:n.trailingLineBreaks||2})},blockString:function(t,e,i,n){i.openBlock({leadingLineBreaks:n.leadingLineBreaks||2}),i.addLiteral(n.string||""),i.closeBlock({trailingLineBreaks:n.trailingLineBreaks||2})},blockTag:function(t,e,i,n){i.openBlock({leadingLineBreaks:n.leadingLineBreaks||2}),i.startNoWrap(),i.addLiteral(ie(t)),i.stopNoWrap(),e(t.children,i),i.startNoWrap(),i.addLiteral(ii(t)),i.stopNoWrap(),i.closeBlock({trailingLineBreaks:n.trailingLineBreaks||2})},inline:function(t,e,i,n){e(t.children,i)},inlineHtml:function(t,e,i,n){i.startNoWrap(),i.addLiteral(eD(t,{decodeEntities:i.options.decodeEntities})),i.stopNoWrap()},inlineString:function(t,e,i,n){i.addLiteral(n.string||"")},inlineSurround:function(t,e,i,n){i.addLiteral(n.prefix||""),e(t.children,i),i.addLiteral(n.suffix||"")},inlineTag:function(t,e,i,n){i.startNoWrap(),i.addLiteral(ie(t)),i.stopNoWrap(),e(t.children,i),i.startNoWrap(),i.addLiteral(ii(t)),i.stopNoWrap()},skip:function(t,e,i,n){}});function ir(t,e){return t[e]||(t[e]=[]),t[e]}function ia(t,e){return void 0===t[e]&&(t[e]=0===e?0:1+ia(t,e-1)),t[e]}function io(t,e,i,n){t[e+i]=Math.max(ia(t,e+i),ia(t,e)+n)}function i_(t,e){return e?("string"==typeof e[0]?e[0]:"[")+t+("string"==typeof e[1]?e[1]:"]"):t}function ih(t,e,i,n,s){let r="function"==typeof e?e(t,n,s):t;return"/"===r[0]&&i?function(t,e){let i=t.length;for(;i>0&&"/"===t[i-1];)--i;return i<t.length?t.substring(0,i):t}(i,0)+r:r}function iu(t,e,i,n,s){let r="li"===eG(t,["parent","name"]),a=0,o=(t.children||[]).filter(t=>"text"!==t.type||!/^\s*$/.test(t.data)).map(function(t){if("li"!==t.name)return{node:t,prefix:""};let e=r?s().trimStart():s();return e.length>a&&(a=e.length),{node:t,prefix:e}});if(o.length){for(let{node:t,prefix:s}of(i.openList({interRowLineBreaks:1,leadingLineBreaks:r?1:n.leadingLineBreaks||2,maxPrefixLength:a,prefixAlign:"left"}),o))i.openListItem({prefix:s}),e([t],i),i.closeListItem();i.closeList({trailingLineBreaks:r?1:n.trailingLineBreaks||2})}}function il(t,e,i,n){function s(t){let s=+eG(t,["attribs","colspan"])||1,r=+eG(t,["attribs","rowspan"])||1;i.openTableCell({maxColumnWidth:n.maxColumnWidth}),e(t.children,i),i.closeTableCell({colspan:s,rowspan:r})}i.openTable(),t.children.forEach(function t(e){if("tag"!==e.type)return;let r=!1!==n.uppercaseHeaderCells?t=>{i.pushWordTransform(t=>t.toUpperCase()),s(t),i.popWordTransform()}:s;switch(e.name){case"thead":case"tbody":case"tfoot":case"center":e.children.forEach(t);return;case"tr":for(let t of(i.openTableRow(),e.children))if("tag"===t.type)switch(t.name){case"th":r(t);break;case"td":s(t)}i.closeTableRow()}}),i.closeTable({tableToString:t=>(function(t,e,i){let n=[],s=0,r=t.length,a=[0];for(let i=0;i<r;i++){let r=ir(n,i),o=t[i],_=0;for(let t=0;t<o.length;t++){let s=o[t];(function(t,e,i,n){for(let s=0;s<t.rowspan;s++){let r=ir(e,i+s);for(let e=0;e<t.colspan;e++)r[n+e]=t}})(s,n,i,_=function(t,e=0){for(;t[e];)e++;return e}(r,_)),_+=s.colspan,s.lines=s.text.split("\n");let h=s.lines.length;io(a,i,s.rowspan,h+e)}s=r.length>s?r.length:s}!function(t,e){for(let i=0;i<e;i++){let e=ir(t,i);for(let n=0;n<i;n++){let s=ir(t,n);if(e[n]||s[i]){let t=e[n];e[n]=s[i],s[i]=t}}}}(n,r>s?r:s);let o=[],_=[0];for(let t=0;t<s;t++){let e,s=0,h=Math.min(r,n[t].length);for(;s<h;)if(e=n[t][s]){if(!e.rendered){let n=0;for(let i=0;i<e.lines.length;i++){let r=e.lines[i],h=a[s]+i;o[h]=(o[h]||"").padEnd(_[t])+r,n=r.length>n?r.length:n}io(_,t,e.colspan,n+i),e.rendered=!0}s+=e.rowspan}else{let t=a[s];o[t]=o[t]||"",s++}}return o.join("\n")})(t,n.rowSpacing??0,n.colSpacing??3),leadingLineBreaks:n.leadingLineBreaks,trailingLineBreaks:n.trailingLineBreaks})}var ip=Object.freeze({__proto__:null,anchor:function(t,e,i,n){let s=function(){if(n.ignoreHref||!t.attribs||!t.attribs.href)return"";let e=t.attribs.href.replace(/^mailto:/,"");return n.noAnchorUrl&&"#"===e[0]?"":e=ih(e,n.pathRewrite,n.baseUrl,i.metadata,t)}();if(s){let r="";i.pushWordTransform(t=>(t&&(r+=t),t)),e(t.children,i),i.popWordTransform(),n.hideLinkHrefIfSameAsText&&s===r||i.addInline(r?" "+i_(s,n.linkBrackets):s,{noWordTransform:!0})}else e(t.children,i)},blockquote:function(t,e,i,n){i.openBlock({leadingLineBreaks:n.leadingLineBreaks||2,reservedLineLength:2}),e(t.children,i),i.closeBlock({trailingLineBreaks:n.trailingLineBreaks||2,blockTransform:t=>(!1!==n.trimEmptyLines?eV(t,"\n"):t).split("\n").map(t=>"> "+t).join("\n")})},dataTable:il,heading:function(t,e,i,n){i.openBlock({leadingLineBreaks:n.leadingLineBreaks||2}),!1!==n.uppercase?(i.pushWordTransform(t=>t.toUpperCase()),e(t.children,i),i.popWordTransform()):e(t.children,i),i.closeBlock({trailingLineBreaks:n.trailingLineBreaks||2})},horizontalLine:function(t,e,i,n){i.openBlock({leadingLineBreaks:n.leadingLineBreaks||2}),i.addInline("-".repeat(n.length||i.options.wordwrap||40)),i.closeBlock({trailingLineBreaks:n.trailingLineBreaks||2})},image:function(t,e,i,n){let s=t.attribs||{},r=s.alt?s.alt:"",a=s.src?ih(s.src,n.pathRewrite,n.baseUrl,i.metadata,t):"",o=a?r?r+" "+i_(a,n.linkBrackets):i_(a,n.linkBrackets):r;i.addInline(o,{noWordTransform:!0})},lineBreak:function(t,e,i,n){i.addLineBreak()},orderedList:function(t,e,i,n){let s=Number(t.attribs.start||"1"),r=function(t="1"){switch(t){case"a":return t=>eK(t,"a");case"A":return t=>eK(t,"A");case"i":return t=>eQ(t).toLowerCase();case"I":return t=>eQ(t);default:return t=>t.toString()}}(t.attribs.type);return iu(t,e,i,n,()=>" "+r(s++)+". ")},paragraph:function(t,e,i,n){i.openBlock({leadingLineBreaks:n.leadingLineBreaks||2}),e(t.children,i),i.closeBlock({trailingLineBreaks:n.trailingLineBreaks||2})},pre:function(t,e,i,n){i.openBlock({isPre:!0,leadingLineBreaks:n.leadingLineBreaks||2}),e(t.children,i),i.closeBlock({trailingLineBreaks:n.trailingLineBreaks||2})},table:function(t,e,i,n){return!function(t,e){if(!0===e)return!0;if(!t)return!1;let{classes:i,ids:n}=function(t){let e=[],i=[];for(let n of t)n.startsWith(".")?e.push(n.substring(1)):n.startsWith("#")&&i.push(n.substring(1));return{classes:e,ids:i}}(e),s=(t.class||"").split(" "),r=(t.id||"").split(" ");return s.some(t=>i.includes(t))||r.some(t=>n.includes(t))}(t.attribs,i.options.tables)?void(i.openBlock({leadingLineBreaks:n.leadingLineBreaks}),e(t.children,i),i.closeBlock({trailingLineBreaks:n.trailingLineBreaks})):il(t,e,i,n)},unorderedList:function(t,e,i,n){let s=n.itemPrefix||" * ";return iu(t,e,i,n,()=>s)},wbr:function(t,e,i,n){i.addWordBreakOpportunity()}});let ic={baseElements:{selectors:["body"],orderBy:"selectors",returnDomByDefault:!0},decodeEntities:!0,encodeCharacters:{},formatters:{},limits:{ellipsis:"...",maxBaseElements:void 0,maxChildNodes:void 0,maxDepth:void 0,maxInputLength:16777216},longWordSplit:{forceWrapOnLimit:!1,wrapCharacters:[]},preserveNewlines:!1,selectors:[{selector:"*",format:"inline"},{selector:"a",format:"anchor",options:{baseUrl:null,hideLinkHrefIfSameAsText:!1,ignoreHref:!1,linkBrackets:["[","]"],noAnchorUrl:!0}},{selector:"article",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"aside",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"blockquote",format:"blockquote",options:{leadingLineBreaks:2,trailingLineBreaks:2,trimEmptyLines:!0}},{selector:"br",format:"lineBreak"},{selector:"div",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"footer",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"form",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"h1",format:"heading",options:{leadingLineBreaks:3,trailingLineBreaks:2,uppercase:!0}},{selector:"h2",format:"heading",options:{leadingLineBreaks:3,trailingLineBreaks:2,uppercase:!0}},{selector:"h3",format:"heading",options:{leadingLineBreaks:3,trailingLineBreaks:2,uppercase:!0}},{selector:"h4",format:"heading",options:{leadingLineBreaks:2,trailingLineBreaks:2,uppercase:!0}},{selector:"h5",format:"heading",options:{leadingLineBreaks:2,trailingLineBreaks:2,uppercase:!0}},{selector:"h6",format:"heading",options:{leadingLineBreaks:2,trailingLineBreaks:2,uppercase:!0}},{selector:"header",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"hr",format:"horizontalLine",options:{leadingLineBreaks:2,length:void 0,trailingLineBreaks:2}},{selector:"img",format:"image",options:{baseUrl:null,linkBrackets:["[","]"]}},{selector:"main",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"nav",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"ol",format:"orderedList",options:{leadingLineBreaks:2,trailingLineBreaks:2}},{selector:"p",format:"paragraph",options:{leadingLineBreaks:2,trailingLineBreaks:2}},{selector:"pre",format:"pre",options:{leadingLineBreaks:2,trailingLineBreaks:2}},{selector:"section",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"table",format:"table",options:{colSpacing:3,leadingLineBreaks:2,maxColumnWidth:60,rowSpacing:0,trailingLineBreaks:2,uppercaseHeaderCells:!0}},{selector:"ul",format:"unorderedList",options:{itemPrefix:" * ",leadingLineBreaks:2,trailingLineBreaks:2}},{selector:"wbr",format:"wbr"}],tables:[],whitespaceCharacters:" 	\r\n\f​",wordwrap:80},id=(t,e,i)=>[...t,...e],ig=(t,e,i)=>[...e],im=(t,e,i)=>t.some(t=>"object"==typeof t)?id(t,e):ig(t,e);function ib(t,e={},i){return(function(t={}){return(t=eU(ic,t,{arrayMerge:ig,customMerge:t=>"selectors"===t?im:void 0})).formatters=Object.assign({},is,ip,t.formatters),t.selectors=e$(t.selectors,t=>t.selector),function(t){if(t.tags){let e=Object.entries(t.tags).map(([t,e])=>({...e,selector:t||"*"}));t.selectors.push(...e),t.selectors=e$(t.selectors,t=>t.selector)}function e(t,e,i){let n=e.pop();for(let i of e){let e=t[i];e||(e={},t[i]=e),t=e}t[n]=i}if(t.baseElement){let i=t.baseElement;e(t,["baseElements","selectors"],Array.isArray(i)?i:[i])}for(let i of(void 0!==t.returnDomByDefault&&e(t,["baseElements","returnDomByDefault"],t.returnDomByDefault),t.selectors))"anchor"===i.format&&eG(i,["options","noLinkBrackets"])&&e(i,["options","linkBrackets"],!1)}(t),function(t={}){let e=t.selectors.filter(t=>!t.format);if(e.length)throw Error("Following selectors have no specified format: "+e.map(t=>`\`${t.selector}\``).join(", "));let i=new tK(t.selectors.map(t=>[t.selector,t])).build(t3);"function"!=typeof t.encodeCharacters&&(t.encodeCharacters=function(t){if(!t||0===Object.keys(t).length)return;let e=Object.entries(t).filter(([,t])=>!1!==t),i=RegExp(e.map(([t])=>`(${[...t][0].replace(/[\s\S]/g,t=>"\\u"+t.charCodeAt().toString(16).padStart(4,"0"))})`).join("|"),"g"),n=e.map(([,t])=>t),s=(t,...e)=>n[e.findIndex(t=>t)];return t=>t.replace(i,s)}(t.encodeCharacters));let n=new tK(t.baseElements.selectors.map((t,e)=>[t,e+1])).build(t3);function s(e){return function(t,e,i){let n=[];return ez(e.limits.maxDepth,function(t,s){for(let r of s=s.slice(0,e.limits.maxChildNodes)){if("tag"!==r.type)continue;let s=i.pick1(r);if(s>0?n.push({selectorIndex:s,element:r}):r.children&&t(r.children),n.length>=e.limits.maxBaseElements)return}})(t),"occurrence"!==e.baseElements.orderBy&&n.sort((t,e)=>t.selectorIndex-e.selectorIndex),e.baseElements.returnDomByDefault&&0===n.length?t:n.map(t=>t.element)}(e,t,n)}let r=ez(t.limits.maxDepth,it,function(e,i){i.addInline(t.limits.ellipsis||"")});return function(e,n){return function(t,e,i,n,s,r){let a=i.limits.maxInputLength;a&&t&&t.length>a&&(console.warn(`Input length ${t.length} is above allowed limit of ${a}. Truncating without ellipsis.`),t=t.substring(0,a));let o=s(function(t,e){let i=new j(void 0,e);return new ek(i,e).end(t),i.root}(t,{decodeEntities:i.decodeEntities}).children),_=new e9(i,n,e);return r(o,_),_.toString()}(e,n,t,i,s,r)}}(t)})(e)(t,i)}var iy=i(76468),ix=i(71159),iw=i(19510),ik=Object.defineProperty,iv=Object.getOwnPropertySymbols,iE=Object.prototype.hasOwnProperty,iT=Object.prototype.propertyIsEnumerable,iS=(t,e,i)=>e in t?ik(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i,iL=(t,e)=>{for(var i in e||(e={}))iE.call(e,i)&&iS(t,i,e[i]);if(iv)for(var i of iv(e))iT.call(e,i)&&iS(t,i,e[i]);return t},iA=(t,e,i)=>new Promise((n,s)=>{var r=t=>{try{o(i.next(t))}catch(t){s(t)}},a=t=>{try{o(i.throw(t))}catch(t){s(t)}},o=t=>t.done?n(t.value):Promise.resolve(t.value).then(r,a);o((i=i.apply(t,e)).next())}),iN={unformatted:["code","pre","em","strong","span"],indent_inner_html:!0,indent_char:" ",indent_size:2,sep:"\n"},iO=(t,e={})=>iy.html(t,iL(iL({},iN),e)),iI=[{selector:"img",format:"skip"},{selector:"#__react-email-preview",format:"skip"},{selector:"a",options:{linkBrackets:!1}}],iC=new TextDecoder("utf-8"),iR=t=>iA(void 0,null,function*(){let e="";if("pipeTo"in t){let i=new WritableStream({write(t){e+=iC.decode(t)}});yield t.pipeTo(i)}else{let i=new f.Writable({write(t,i,n){e+=iC.decode(t),n()}});return t.pipe(i),new Promise((t,n)=>{i.on("error",n),i.on("close",()=>{t(e)})})}return e}),iB=(t,e)=>iA(void 0,null,function*(){let n;let s=(0,iw.jsx)(ix.Suspense,{children:t}),{default:r}=yield i.e(257).then(i.t.bind(i,43257,19));if(Object.hasOwn(r,"renderToReadableStream")?n=yield iR((yield r.renderToReadableStream(s))):yield new Promise((t,e)=>{let i=r.renderToPipeableStream(s,{onAllReady(){return iA(this,null,function*(){n=yield iR(i),t()})},onError(t){e(t)}})}),null==e?void 0:e.plainText)return ib(n,iL({selectors:iI},e.htmlToTextOptions));let a=`<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">${n.replace(/<!DOCTYPE.*?>/,"")}`;return(null==e?void 0:e.pretty)?iO(a):a}),iD=new TextDecoder("utf-8"),iP=t=>iA(void 0,null,function*(){let e="";if("pipeTo"in t){let i=new WritableStream({write(t){e+=iD.decode(t)}});yield t.pipeTo(i)}else{let i=new f.Writable({write(t,i,n){e+=iD.decode(t),n()}});return t.pipe(i),new Promise((t,n)=>{i.on("error",n),i.on("close",()=>{t(e)})})}return e}),iW=(t,e)=>iA(void 0,null,function*(){let n;let s=(0,iw.jsx)(ix.Suspense,{children:t}),{default:r}=yield i.e(257).then(i.t.bind(i,43257,19));if(Object.hasOwn(r,"renderToReadableStream")?n=yield iP((yield r.renderToReadableStream(s))):yield new Promise((t,e)=>{let i=r.renderToPipeableStream(s,{onAllReady(){return iA(this,null,function*(){n=yield iP(i),t()})},onError(t){e(t)}})}),null==e?void 0:e.plainText)return ib(n,iL({selectors:iI},e.htmlToTextOptions));let a=`<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">${n.replace(/<!DOCTYPE.*?>/,"")}`;return(null==e?void 0:e.pretty)?iO(a):a})}};