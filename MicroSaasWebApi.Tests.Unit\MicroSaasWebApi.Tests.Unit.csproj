﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net7.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<IsPackable>false</IsPackable>
		<IsTestProject>true</IsTestProject>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="Services\FakeServiceScopePXW.cs" />
	</ItemGroup>

	<ItemGroup>
	  <None Remove="appsettings.json" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="FakeItEasy" Version="8.1.0" />
		<PackageReference Include="FluentAssertions" Version="6.12.0" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.11.1" />
		<PackageReference Include="xunit" Version="2.6.6" />
		<PackageReference Include="xunit.runner.visualstudio" Version="2.5.6">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="coverlet.collector" Version="6.0.0">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\MicroSaasWebApi\MicroSaasWebApi.csproj" />
	    <PackageReference Include="coverlet.msbuild" Version="6.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
</ItemGroup>

	<ItemGroup>
	  <Folder Include="Services\" />
	</ItemGroup>

</Project>
