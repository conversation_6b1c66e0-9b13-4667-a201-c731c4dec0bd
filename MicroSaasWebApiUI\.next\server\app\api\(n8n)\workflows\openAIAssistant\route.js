(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[956],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},6195:e=>{"use strict";e.exports=require("node:buffer")},2480:()=>{},9777:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>g,default:()=>N});var o={};r.r(o),r.d(o,{POST:()=>k,dynamic:()=>u,runtime:()=>f});var a={};r.r(a),r.d(a,{originalPathname:()=>m,patchFetch:()=>I,requestAsyncStorage:()=>h,routeModule:()=>A,serverHooks:()=>_,staticGenerationAsyncStorage:()=>E});var s=r(932),n=r(2561),i=r(4828),p=r(6631),d=r(6333),c=r(462),w=r(1175),l=r(9985);let u="force-dynamic",f="edge";async function k(e){try{let{id:t,apiKey:r,assistantId:o}=await e.json(),a=await (0,c.a)();if(!a)throw Error("User not authenticated");if(!t)throw Error("Workflow ID is required");if(!r)throw Error("API Key is required");if(!o)throw Error("Assistant ID is required");let s=process.env.N8N_API_KEY,n=process.env.N8N_API_URL,i=process.env.N8N_WEBHOOK_URL;if(!s||!n)throw Error("N8N configuration is missing");let p=new Date().toISOString(),u={"X-N8N-API-KEY":s},f={name:`OpenAI ${p}`,type:"openAiApi",data:{apiKey:r}},k=(await w.Z.post(`${n}/credentials`,f,{headers:u})).data.id;if(!k)throw Error("Failed to create credentials");let A=(await w.Z.get(`${n}/workflows/${t}`,{headers:u})).data,h=p.replace(/[^a-zA-Z0-9]/g,""),E=A.nodes.map(e=>"n8n-nodes-base.webhook"===e.type?{...e,parameters:{...e.parameters,path:h}}:"n8n-nodes-base.openAi"===e.type?{...e,credentials:{openAiApi:k},parameters:{...e.parameters,assistantId:o}}:e),_=(await w.Z.post(`${n}/workflows`,{name:`Cloned Workflow ${p}`,nodes:E,connections:A.connections,settings:A.settings},{headers:u})).data.id;if(!_)throw Error("Failed to create new workflow");let m=`${i}/${h}`;if(!await w.Z.post(`${n}/workflows/${_}/activate`,{},{headers:u}))throw Error("Failed to activate workflow");if(!await d.Z.project.create({data:{connection_id:k,webhook_id:_,scenario_id:_,user_clerk_id:a.id,webhookLink:m,assistant_id:o,type:"n8n",status:"active"}}))throw Error("Failed to create project in database");return l.xk.json({success:!0,id:_,webhookLink:m,credentialsId:k})}catch(e){return console.error("Error in workflow cloning process:",e),l.xk.json({success:!1,error:e.response?.data?.message||"Failed to clone and update workflow",details:e.response?.data||e.message},{status:e.response?.status||500})}}let A=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/(n8n)/workflows/openAIAssistant/route",pathname:"/api/workflows/openAIAssistant",filename:"route",bundlePath:"app/api/(n8n)/workflows/openAIAssistant/route"},resolvedPagePath:"C:\\Projects\\PersonalPortal\\src\\app\\api\\(n8n)\\workflows\\openAIAssistant\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:h,staticGenerationAsyncStorage:E,serverHooks:_}=A,m="/api/(n8n)/workflows/openAIAssistant/route";function I(){return(0,p.XH)({serverHooks:_,staticGenerationAsyncStorage:E})}let g=a,N=s.a.wrap(A)},6333:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});let o=new(r(7225)).PrismaClient}},e=>{var t=t=>e(e.s=t);e.O(0,[520,288,225,436,657,551],()=>t(9777));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/(n8n)/workflows/openAIAssistant/route"]=r}]);
//# sourceMappingURL=route.js.map