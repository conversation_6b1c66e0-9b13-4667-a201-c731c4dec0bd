(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[403],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},6195:e=>{"use strict";e.exports=require("node:buffer")},2480:()=>{},4654:()=>{},9427:(e,r,t)=>{"use strict";t.r(r),t.d(r,{ComponentMod:()=>w,default:()=>E});var s={};t.r(s),t.d(s,{POST:()=>f,dynamic:()=>h,runtime:()=>_});var a={};t.r(a),t.d(a,{originalPathname:()=>P,patchFetch:()=>v,requestAsyncStorage:()=>m,routeModule:()=>k,serverHooks:()=>y,staticGenerationAsyncStorage:()=>g});var i=t(932),o=t(2561),n=t(4828),u=t(6631),c=t(9985),p=t(8552),l=t(6333),d=t(2436);let h="force-dynamic",_="edge";async function f(e){let{userId:r}=(0,d.I)();if(!r)return c.xk.json({error:"Not signed in"},{status:401});try{let t=await l.Z.subscription.findFirst({where:{user_clerk_id:r}});if(!t?.stripe_customer_id)return c.xk.json({error:"You don't have a billing account yet. Make a purchase first."},{status:400});let s=`${e.headers.get("origin")}/dashboard`,a=await p.R.createCustomerPortal(t.stripe_customer_id,s);return c.xk.json({url:a})}catch(e){return console.error(e),c.xk.json({error:e?.message},{status:500})}}let k=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/stripe/create-portal/route",pathname:"/api/stripe/create-portal",filename:"route",bundlePath:"app/api/stripe/create-portal/route"},resolvedPagePath:"C:\\Projects\\PersonalPortal\\src\\app\\api\\stripe\\create-portal\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:m,staticGenerationAsyncStorage:g,serverHooks:y}=k,P="/api/stripe/create-portal/route";function v(){return(0,u.XH)({serverHooks:y,staticGenerationAsyncStorage:g})}let w=a,E=i.a.wrap(k)},6333:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=new(t(7225)).PrismaClient},8552:(e,r,t)=>{"use strict";t.d(r,{R:()=>i});var s=t(1735);class a{async createCustomerPortal(e,r){return(await this.stripe.billingPortal.sessions.create({customer:e,return_url:r})).url}async findCheckoutSession(e){try{return await this.stripe.checkout.sessions.retrieve(e,{expand:["line_items"]})}catch(e){return console.error(e),null}}async getSubscription(e){return this.stripe.subscriptions.retrieve(e)}async getCheckoutSession(e){return this.stripe.checkout.sessions.retrieve(e)}constructor(){this.stripe=new s.Z(process.env.STRIPE_SECRET_KEY,{typescript:!0})}}let i=new a}},e=>{var r=r=>e(e.s=r);e.O(0,[520,288,225,436,735],()=>r(9427));var t=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/stripe/create-portal/route"]=t}]);
//# sourceMappingURL=route.js.map