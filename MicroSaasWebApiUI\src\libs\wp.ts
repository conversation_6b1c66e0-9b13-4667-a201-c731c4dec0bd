import axios from 'axios'
import WPA<PERSON> from 'wpapi'
//http://localhost:8080/wp-json/wp/v2/posts?_fields=id,slug,title,featured_media,_embedded&_embed=true

interface WPPost {
	id: number
	slug: string
	title: {
		rendered: string
	}
	featured_media: number
	date: string
	author: number
	image_url?: string
	categories?: number[]
	content?: {
		rendered: string
	}
	excerpt?: {
		rendered: string
	}
}

interface WPCategory {
	id: number
	count: number
	description: string
	link: string
	name: string
	slug: string
	taxonomy: string
	parent: number
}

interface WPImage {
	guid: {
		rendered: string
	}
	source_url?: string
}

class WordpressService {
	private baseUrl: string
	private auth: {
		username: string
		password: string
	}
	private wp: WPAPI

	constructor() {
		// Get base URL and ensure it's properly formatted
		const wpEndpoint = process.env.WP_REST_ENDPOINT || ''
		// Remove trailing slashes and wp-admin if present
		this.baseUrl = wpEndpoint
			.replace(/\/+$/, '')
			.replace('/wp-admin', '')
			.replace('/wp-json', '') // Remove wp-json if it's in the URL

		this.auth = {
			username: process.env.WORDPRESS_DB_USER || '',
			password: process.env.WORDPRESS_DB_PASSWORD || ''
		}

		this.wp = new WPAPI({
			endpoint: `${this.baseUrl}/wp-json`,
			username: this.auth.username,
			password: this.auth.password
		})
	}

	private getApiUrl(endpoint: string): string {
		return `${this.baseUrl}/wp-json${endpoint}`
	}

	private async getImageURLById(id: number): Promise<WPImage | null> {
		try {
			const response = await axios.get(
				this.getApiUrl(`/wp/v2/media/${id}`),
				{
					auth: this.auth,
					headers: {
						'Accept': 'application/json',
						'Referrer-Policy': 'unsafe-url' // Add referrer policy to handle mixed content
					}
				}
			)

			return response.data
		} catch (e) {
			return null
		}
	}

	public async getAllCategories(): Promise<WPCategory[]> {
		try {
			const response = await axios.get(
				this.getApiUrl('/wp/v2/categories'),
				{
					auth: this.auth,
					headers: {
						'Accept': 'application/json',
						'Referrer-Policy': 'unsafe-url' // Add referrer policy to handle mixed content
					},
					params: {
						per_page: 100
					}
				}
			)

			if (!response?.data) {
				console.error('No categories data in WordPress response')
				return []
			}

			return Array.isArray(response.data) ? response.data : []
		} catch (e) {
			if (axios.isAxiosError(e)) {
				console.error('WordPress Categories API Error:', {
					status: e.response?.status,
					url: e.config?.url
				})
			}
			return []
		}
	}

	public async getAllPosts() {
		try {
			const response = await axios.get(
				this.getApiUrl('/wp/v2/posts'),
				{
					auth: this.auth,
					headers: {
						'Accept': 'application/json',
						'Referrer-Policy': 'unsafe-url' // Add referrer policy to handle mixed content
					},
					params: {
						_fields: 'id,slug,title,featured_media,date,author,categories,excerpt',
						per_page: 100,
						status: 'publish',
						orderby: 'date',
						order: 'desc'
					}
				}
			)

			if (!response?.data) {
				console.error('No data in WordPress response')
				return []
			}

			const posts: WPPost[] = Array.isArray(response.data) ? response.data : []
			if (posts.length === 0) {
				console.error('No posts found in WordPress response')
				return []
			}

			const finalList = await Promise.all(posts.map(async (post) => {
				let imageUrl = ''
				if (post.featured_media > 0) {
					const image = await this.getImageURLById(post.featured_media)
					imageUrl = image?.guid?.rendered || ''
				}

				// Get author information
				let authorName = 'Javian Picardo' // Default author name
				if (post.author) {
					try {
						const authorResponse = await axios.get(
							this.getApiUrl(`/wp/v2/users/${post.author}`),
							{
								auth: this.auth,
								headers: {
									'Accept': 'application/json',
									'Referrer-Policy': 'unsafe-url' // Add referrer policy to handle mixed content
								}
							}
						)
						if (authorResponse?.data?.name) {
							authorName = authorResponse.data.name
						}
					} catch (error) {
						console.error('Error fetching author:', error)
						// Keep default author name
					}
				}

				// Return the post data without modifying content
				return {
					...post,
					image_url: imageUrl || '',
					author: authorName
				}
			}))

			return finalList
		} catch (e) {
			if (axios.isAxiosError(e)) {
				console.error('WordPress API Error:', {
					status: e.response?.status,
					url: e.config?.url
				})
			}
			return []
		}
	}

	public async getPostsForSitemap(): Promise<{ slug: string; date: string }[]> {
		try {
			const response = await axios.get(
				this.getApiUrl('/wp/v2/posts'),
				{
					auth: this.auth,
					headers: {
						'Accept': 'application/json'
					},
					params: {
						_fields: 'slug,date',
						status: 'publish'
					}
				}
			)
			return Array.isArray(response.data) ? response.data : []
		} catch (e) {
			return []
		}
	}

	public async getPost(slug: string): Promise<any> {
		try {
			const response = await axios.get(
				this.getApiUrl('/wp/v2/posts'),
				{
					auth: this.auth,
					headers: {
						'Accept': 'application/json',
						'Referrer-Policy': 'unsafe-url' // Add referrer policy to handle mixed content
					},
					params: {
						slug: slug,
						_embed: true,
						status: 'publish'
					}
				}
			)

			if (!response.data?.length) {
				return {
					title: { rendered: 'Post not found' },
					content: { rendered: 'The requested post could not be found.' },
					excerpt: { rendered: 'Post not found' },
					slug
				}
			}

			const post = response.data[0]
			let imageUrl = ''

			if (post.featured_media > 0) {
				const image = await this.getImageURLById(post.featured_media)
				imageUrl = image?.guid?.rendered || ''
			}

			// Get author information
			let authorName = 'Javian Picardo' // Default author name
			if (post.author) {
				try {
					const authorResponse = await axios.get(
						this.getApiUrl(`/wp/v2/users/${post.author}`),
						{
							auth: this.auth,
							headers: {
								'Accept': 'application/json',
								'Referrer-Policy': 'unsafe-url' // Add referrer policy to handle mixed content
							}
						}
					)
					if (authorResponse?.data?.name) {
						authorName = authorResponse.data.name
					}
				} catch (error) {
					console.error('Error fetching author:', error)
					// Keep default author name
				}
			}

			return {
				...post,
				image_url: imageUrl || '',
				author: authorName
			}
		} catch (e) {
			return {
				title: { rendered: 'Post not found' },
				content: { rendered: 'The requested post could not be found.' },
				excerpt: { rendered: 'Post not found' },
				slug
			}
		}
	}
}

export const wordpressService = new WordpressService()
