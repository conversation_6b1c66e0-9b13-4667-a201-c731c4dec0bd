{"/_not-found/page": "app/_not-found/page.js", "/chat/[projectID]/page": "app/chat/[projectID]/page.js", "/page": "app/page.js", "/dashboard/page": "app/dashboard/page.js", "/apple-icon.png/route": "app/apple-icon.png/route.js", "/processing-page/[[...processing-page]]/page": "app/processing-page/[[...processing-page]]/page.js", "/sign-up/[[...sign-up]]/page": "app/sign-up/[[...sign-up]]/page.js", "/icon.png/route": "app/icon.png/route.js", "/test-email/page": "app/test-email/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/opengraph-image.png/route": "app/opengraph-image.png/route.js", "/sign-in/[[...sign-in]]/page": "app/sign-in/[[...sign-in]]/page.js", "/success/page": "app/success/page.js", "/sitemap.xml/route": "app/sitemap.xml/route.js", "/twitter-image.png/route": "app/twitter-image.png/route.js", "/ad-demo/page": "app/ad-demo/page.js", "/api/webhook/resend/route": "app/api/webhook/resend/route.js", "/api/waiting-list/route": "app/api/waiting-list/route.js", "/privacy-policy/page": "app/privacy-policy/page.js", "/cv/route": "app/cv/route.js", "/tos/page": "app/tos/page.js", "/security-policy/page": "app/security-policy/page.js", "/blog/[articleId]/page": "app/blog/[articleId]/page.js", "/blog/page": "app/blog/page.js", "/waiting-list/page": "app/waiting-list/page.js", "/api/(make)/active/route": "app/api/(make)/active/route.js", "/api/(make)/link/route": "app/api/(make)/link/route.js", "/api/(n8n)/workflows/openAIAssistant/route": "app/api/(n8n)/workflows/openAIAssistant/route.js", "/api/projects/route": "app/api/projects/route.js", "/api/stripe/create-checkout/route": "app/api/stripe/create-checkout/route.js", "/api/(make)/scenarios/route": "app/api/(make)/scenarios/route.js", "/api/(make)/scenarios/openAIAssistant/route": "app/api/(make)/scenarios/openAIAssistant/route.js", "/api/stripe/create-portal/route": "app/api/stripe/create-portal/route.js", "/api/test-email/route": "app/api/test-email/route.js", "/api/webhook/stripe/route": "app/api/webhook/stripe/route.js"}