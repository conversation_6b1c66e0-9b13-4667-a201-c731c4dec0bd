const Typescript = () => {
  return (
    <svg
      width="18"
      height="19"
      viewBox="0 0 18 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0.675 0.0947266H17.325C17.6978 0.0947266 18 0.396933 18 0.769727V17.4197C18 17.7925 17.6978 18.0947 17.325 18.0947H0.675C0.302206 18.0947 0 17.7925 0 17.4197V0.769727C0 0.396933 0.302206 0.0947266 0.675 0.0947266ZM10.0059 9.67073V8.19445H3.59986V9.67073H5.88697V16.2439H7.7076V9.67073H10.0059ZM10.7318 16.0704C11.0254 16.22 11.3727 16.3323 11.7736 16.4072C12.1744 16.482 12.597 16.5195 13.0411 16.5195C13.474 16.5195 13.8853 16.4783 14.2748 16.3959C14.6645 16.3136 15.006 16.1779 15.2997 15.9889C15.5933 15.7999 15.8257 15.553 15.9969 15.248C16.1682 14.9429 16.2539 14.5659 16.2539 14.1168C16.2539 13.7912 16.2049 13.5059 16.1071 13.2608C16.0092 13.0156 15.868 12.7976 15.6836 12.6067C15.4992 12.4159 15.278 12.2447 15.0202 12.0931C14.7623 11.9416 14.4715 11.7984 14.1478 11.6637C13.9107 11.5663 13.698 11.4718 13.5098 11.3802C13.3216 11.2885 13.1616 11.1949 13.0299 11.0995C12.8981 11.004 12.7965 10.903 12.725 10.7963C12.6534 10.6897 12.6177 10.569 12.6177 10.4343C12.6177 10.3108 12.6497 10.1994 12.7137 10.1003C12.7776 10.0011 12.868 9.91594 12.9847 9.84484C13.1014 9.77374 13.2444 9.71856 13.4138 9.67924C13.5832 9.63999 13.7714 9.62033 13.9785 9.62033C14.129 9.62033 14.2881 9.63154 14.4555 9.65397C14.6231 9.67647 14.7915 9.71109 14.9608 9.75782C15.1302 9.80462 15.2949 9.86353 15.4549 9.9347C15.6149 10.0058 15.7627 10.0881 15.8982 10.1817V8.50322C15.6234 8.3984 15.3232 8.32073 14.9976 8.27026C14.6719 8.21972 14.2984 8.19445 13.8768 8.19445C13.4477 8.19445 13.0411 8.24028 12.6572 8.33201C12.2732 8.42367 11.9355 8.56684 11.6437 8.76145C11.352 8.95606 11.1214 9.20397 10.952 9.5052C10.7826 9.80649 10.698 10.1667 10.698 10.5858C10.698 11.121 10.8532 11.5776 11.1638 11.9556C11.4743 12.3336 11.9458 12.6535 12.5782 12.9155C12.8266 13.0166 13.0581 13.1157 13.2726 13.2131C13.4872 13.3103 13.6726 13.4114 13.8288 13.5162C13.985 13.621 14.1083 13.7351 14.1986 13.8586C14.289 13.9821 14.3342 14.1224 14.3342 14.2796C14.3342 14.3957 14.3059 14.5032 14.2494 14.6024C14.193 14.7016 14.1074 14.7876 13.9926 14.8606C13.8777 14.9336 13.7347 14.9907 13.5634 15.0318C13.3921 15.073 13.1917 15.0936 12.9621 15.0936C12.5706 15.0936 12.183 15.0253 11.799 14.8887C11.415 14.7521 11.0593 14.5472 10.7318 14.274V16.0704Z"
        fill="#3178C6"
      />
    </svg>
  );
};

export default Typescript;
