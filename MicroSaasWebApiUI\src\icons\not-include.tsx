const NotInclude = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9 16.5C13.1421 16.5 16.5 13.1421 16.5 9C16.5 4.85786 13.1421 1.5 9 1.5C4.85786 1.5 1.5 4.85786 1.5 9C1.5 13.1421 4.85786 16.5 9 16.5Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.77154 12.1763L9.00249 9.93983L11.232 12.177L11.2319 12.1771L11.234 12.1791C11.3578 12.2947 11.5214 12.3577 11.6906 12.3547C11.8598 12.3517 12.0212 12.283 12.1407 12.163C12.2603 12.0431 12.3287 11.8813 12.3317 11.7118C12.3347 11.5424 12.272 11.3783 12.1567 11.2542L12.1568 11.2542L12.1548 11.2522L9.9264 9.01457L12.155 6.77824C12.2171 6.71798 12.2666 6.64597 12.3008 6.56639C12.335 6.48657 12.353 6.40072 12.3538 6.31386C12.3546 6.22699 12.3381 6.14082 12.3054 6.06039C12.2726 5.97996 12.2242 5.90686 12.163 5.84537C12.1017 5.78387 12.0289 5.73521 11.9487 5.70225C11.8684 5.66928 11.7825 5.65266 11.6958 5.65338C11.6091 5.65409 11.5234 5.67212 11.4437 5.70641C11.3643 5.7406 11.2924 5.79027 11.2323 5.85252L9.00253 8.08851L6.77406 5.85304C6.71452 5.78923 6.64283 5.73801 6.5632 5.70242C6.48311 5.66663 6.39665 5.64737 6.30898 5.64582C6.2213 5.64427 6.13422 5.66045 6.05292 5.69339C5.97163 5.72633 5.8978 5.77535 5.83583 5.83751C5.77386 5.89968 5.72502 5.97371 5.69222 6.05518C5.65941 6.13666 5.6433 6.22393 5.64485 6.31177C5.64639 6.39961 5.66556 6.48625 5.70121 6.56652C5.73666 6.64633 5.78769 6.71822 5.85129 6.77795L8.07863 9.01452L5.8488 11.2514L5.84876 11.2513L5.8468 11.2534C5.73153 11.3775 5.66882 11.5416 5.6718 11.7111C5.67479 11.8806 5.74323 12.0423 5.86279 12.1623C5.98236 12.2822 6.14372 12.351 6.3129 12.3539C6.48208 12.3569 6.64576 12.2939 6.76948 12.1783L6.76951 12.1783L6.77154 12.1763Z"
        fill="currentColor"
        stroke="currentColor"
        strokeWidth="0.163636"
      />
    </svg>
  );
};

export default NotInclude;
