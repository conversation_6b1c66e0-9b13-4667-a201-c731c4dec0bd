(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[448],{91642:function(e,t,r){Promise.resolve().then(r.bind(r,72494))},99376:function(e,t,r){"use strict";var n=r(35475);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},40257:function(e,t,r){"use strict";var n,s;e.exports=(null==(n=r.g.process)?void 0:n.env)&&"object"==typeof(null==(s=r.g.process)?void 0:s.env)?r.g.process:r(44227)},44227:function(e){!function(){var t={229:function(e){var t,r,n,s=e.exports={};function a(){throw Error("setTimeout has not been defined")}function o(){throw Error("clearTimeout has not been defined")}function i(e){if(t===setTimeout)return setTimeout(e,0);if((t===a||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(r){try{return t.call(null,e,0)}catch(r){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:a}catch(e){t=a}try{r="function"==typeof clearTimeout?clearTimeout:o}catch(e){r=o}}();var l=[],u=!1,c=-1;function d(){u&&n&&(u=!1,n.length?l=n.concat(l):c=-1,l.length&&f())}function f(){if(!u){var e=i(d);u=!0;for(var t=l.length;t;){for(n=l,l=[];++c<t;)n&&n[c].run();c=-1,t=l.length}n=null,u=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===o||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function h(e,t){this.fun=e,this.array=t}function m(){}s.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];l.push(new h(e,t)),1!==l.length||u||i(f)},h.prototype.run=function(){this.fun.apply(null,this.array)},s.title="browser",s.browser=!0,s.env={},s.argv=[],s.version="",s.versions={},s.on=m,s.addListener=m,s.once=m,s.off=m,s.removeListener=m,s.removeAllListeners=m,s.emit=m,s.prependListener=m,s.prependOnceListener=m,s.listeners=function(e){return[]},s.binding=function(e){throw Error("process.binding is not supported")},s.cwd=function(){return"/"},s.chdir=function(e){throw Error("process.chdir is not supported")},s.umask=function(){return 0}}},r={};function n(e){var s=r[e];if(void 0!==s)return s.exports;var a=r[e]={exports:{}},o=!0;try{t[e](a,a.exports,n),o=!1}finally{o&&delete r[e]}return a.exports}n.ab="//";var s=n(229);e.exports=s}()},72494:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return f}});var n=r(57437),s=r(12381),a=r(83464),o=r(77314);let i=(0,r(79205).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);var l=r(99376),u=r(2265),c=r(34422);let d=c.Ry().shape({message:c.Z_().trim().required("Message cannot be empty")});function f(){let{projectID:e}=(0,l.useParams)(),[t,r]=(0,u.useState)([]),[c,f]=(0,u.useState)(""),[h,m]=(0,u.useState)(!0),g=async()=>{try{let t=await a.Z.get("/api/link?projectID=".concat(e));console.log(t.data),t.data.webhookLink&&(f(t.data.webhookLink),m(!1))}catch(e){console.error("Error fetching webhook link:",e)}};(0,u.useEffect)(()=>{g()},[]);let p=async e=>{let t=await a.Z.post(c,{message:e});t.data&&r(e=>[...e,{id:Date.now().toString(),text:t.data.toString(),sender:"ai",timestamp:new Date}])},x=async(e,t)=>{let{resetForm:n}=t;if(!e.message.trim())return;let s={id:Date.now().toString(),text:e.message,sender:"user",timestamp:new Date};r(e=>[...e,s]),await p(e.message),n()};return h?(0,n.jsx)("div",{className:"flex justify-center items-center h-screen",children:(0,n.jsx)(i,{className:"animate-spin text-primary",size:32})}):(0,n.jsxs)("div",{className:"flex flex-col h-[800px] justify-between items-center w-full p-5",children:[(0,n.jsxs)("div",{className:"flex flex-col w-3/4 h-5/6 rounded-b-lg",children:[(0,n.jsx)("h1",{className:"text-2xl text-center font-bold",children:"Chat"}),(0,n.jsx)("div",{className:"flex flex-col gap-2 h-full w-full",children:t.map(e=>(0,n.jsx)("div",{className:"flex rounded-lg flex-col ".concat("user"===e.sender?"text-end":"text-start"),children:(0,n.jsx)("p",{children:e.text})},e.id))})]}),(0,n.jsx)(o.J9,{initialValues:{message:""},validationSchema:d,onSubmit:x,children:e=>{let{errors:t,touched:r,isSubmitting:a}=e;return(0,n.jsxs)(o.l0,{className:"flex gap-3 w-3/4 rounded-b-lg",children:[(0,n.jsx)(o.gN,{type:"text",id:"message",name:"message",placeholder:"Send message...",className:"flex-1 p-2 rounded-md focus:outline-none focus:ring-2 \n								".concat(t.message&&r.message?"focus:ring-red-500 border-red-500":"focus:ring-blue-500")}),(0,n.jsx)(s.z,{type:"submit",disabled:a,className:"px-5 py-2 rounded-md transition-colors duration-200",children:a?(0,n.jsx)(i,{className:"animate-spin",size:16}):"Send"})]})}})]})}},12381:function(e,t,r){"use strict";r.d(t,{z:function(){return u}});var n=r(57437),s=r(2265),a=r(37053),o=r(90535),i=r(10090);let l=(0,o.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 dark:ring-offset-slate-950 dark:focus-visible:ring-slate-300",{variants:{variant:{default:"bg-slate-900 text-slate-50 hover:bg-slate-900/90 dark:bg-slate-50 dark:text-slate-900 dark:hover:bg-slate-50/90",destructive:"bg-red-500 text-slate-50 hover:bg-red-500/90 dark:bg-red-900 dark:text-slate-50 dark:hover:bg-red-900/90",outline:"border border-slate-200 bg-white hover:bg-slate-100 hover:text-slate-900 dark:border-slate-800 dark:bg-slate-950 dark:hover:bg-slate-800 dark:hover:text-slate-50",secondary:"bg-slate-100 text-slate-900 hover:bg-slate-100/80 dark:bg-slate-800 dark:text-slate-50 dark:hover:bg-slate-800/80",ghost:"hover:bg-slate-100 hover:text-slate-900 dark:hover:bg-slate-800 dark:hover:text-slate-50",link:"text-slate-900 underline-offset-4 hover:underline dark:text-slate-50"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=s.forwardRef((e,t)=>{let{className:r,variant:s,size:o,asChild:u=!1,...c}=e,d=u?a.g7:"button";return(0,n.jsx)(d,{className:(0,i.cn)(l({variant:s,size:o,className:r})),ref:t,...c})});u.displayName="Button"},10090:function(e,t,r){"use strict";r.d(t,{cn:function(){return a}});var n=r(61994),s=r(53335);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.m6)((0,n.W)(t))}}},function(e){e.O(0,[203,376,971,117,744],function(){return e(e.s=91642)}),_N_E=e.O()}]);