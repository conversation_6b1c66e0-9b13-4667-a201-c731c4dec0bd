'use client'

import { useEffect, useState } from 'react'
import AdManager from './AdManager'

const HomePageAds = () => {
  const [mounted, setMounted] = useState(false)

  // Set mounted to true after component mounts to avoid hydration issues
  useEffect(() => {
    setMounted(true)
  }, [])

  // Only render on client side to avoid hydration issues
  if (!mounted) {
    // Return an empty div with the same dimensions to prevent layout shifts
    return (
      <section className="w-full py-8 bg-white dark:bg-[#010814]">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-center items-center gap-8">
            <div className="w-full max-w-4xl min-h-[280px]"></div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="w-full py-8 bg-white dark:bg-[#010814]">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-center items-center gap-8">
          {/* Horizontal ad unit */}
          <div className="w-full max-w-4xl">
            <AdManager type="display-horizontal" />
          </div>
        </div>
      </div>
    </section>
  )
}

export default HomePageAds
