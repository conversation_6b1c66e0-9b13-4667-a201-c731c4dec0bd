(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[528],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},6195:e=>{"use strict";e.exports=require("node:buffer")},4453:(e,t,s)=>{"use strict";s.r(t),s.d(t,{ComponentMod:()=>b,default:()=>v});var i={};s.r(i),s.d(i,{POST:()=>x,dynamic:()=>m,runtime:()=>p});var a={};s.r(a),s.d(a,{originalPathname:()=>j,patchFetch:()=>y,requestAsyncStorage:()=>u,routeModule:()=>h,serverHooks:()=>g,staticGenerationAsyncStorage:()=>f});var r=s(932),n=s(2561),o=s(4828),d=s(6631),l=s(2112),c=s(9985);let m="force-dynamic",p="edge";async function x(e){try{let{email:t}=await e.json();if(!t)return c.xk.json({error:"Email is required"},{status:400});console.log("Sending test email to:",t);let s=await l.g.sendThanksYouEmail(t);return console.log("Email sent successfully:",s),c.xk.json({message:"Test email sent successfully",id:s.id})}catch(e){return console.error("Error sending test email:",e),c.xk.json({error:"Failed to send test email",details:e.message},{status:500})}}let h=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/test-email/route",pathname:"/api/test-email",filename:"route",bundlePath:"app/api/test-email/route"},resolvedPagePath:"C:\\Projects\\PersonalPortal\\src\\app\\api\\test-email\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:u,staticGenerationAsyncStorage:f,serverHooks:g}=h,j="/api/test-email/route";function y(){return(0,d.XH)({serverHooks:g,staticGenerationAsyncStorage:f})}let b=a,v=r.a.wrap(h)},8753:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});var i=s(3155);let a={appName:"Javian Picardo",appDescription:"Portal for the services provided by Javian Picardo.",domainName:"www.javianpicardo.com",robots:{index:!0,follow:!0,"max-image-preview":"large","max-snippet":-1,googleBot:{index:!0,follow:!0,"max-image-preview":"large","max-snippet":-1}},stripe:{products:[{type:"one-time",title:"Quick Shoots",productId:"prod_S0c6NHMe0vINXA",subtitle:"Professional photography session",price:300,isBest:!1,linkTitle:"Book Quick Shoots",featuresTitle:"Package Features",priceId:"price_1R6aslP82YH9JfOlJZcweC4d",features:[{title:"One hour of footage time",disabled:!1},{title:"50 edited photos",disabled:!1},{title:"Help with posing",disabled:!1},{title:"Location list to pick from",disabled:!1},{title:"7 business days turn around time",disabled:!1}]},{type:"one-time",title:"Mini Shoots",productId:"prod_S0c9zDLLtK9rnS",subtitle:"Perfect for professional portraits",price:150,isBest:!0,linkTitle:"Book Mini Shoots",featuresTitle:"Package Features",priceId:"price_1R6avXP82YH9JfOlIGpAfwjU",features:[{title:"30 minutes of footage time",disabled:!1},{title:"25 edited photos",disabled:!1},{title:"Help with posing",disabled:!1},{title:"Location list to pick from",disabled:!1},{title:"7 business days turn around time",disabled:!1}]}]},colors:{theme:"light",main:s.n(i)().light.primary},resend:{fromAdmin:"Javian Picardo <<EMAIL>>",supportEmail:"<EMAIL>",forwardRepliesTo:"<EMAIL>",subjects:{thankYou:"Welcome to Javian Picardo"}}}},6333:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});let i=new(s(7225)).PrismaClient},2112:(e,t,s)=>{"use strict";s.d(t,{g:()=>y});var i=s(2416);s(7908);let a=e=>(0,i.jsxs)("div",{className:"max-w-3xl mx-auto p-8 bg-white",children:[(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Invoice"}),(0,i.jsx)("div",{className:"grid grid-cols-2 gap-4",children:(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,i.jsx)("span",{className:"font-medium",children:"Invoice number: "}),e.id]}),(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,i.jsx)("span",{className:"font-medium",children:"Date paid: "}),e.paid_date]})]})})]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-8 mb-8",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-lg font-semibold mb-3",children:"From:"}),(0,i.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,i.jsx)("p",{className:"font-medium",children:"DB2 Software Ltd"}),(0,i.jsx)("p",{children:"38 Fawkner Way"}),(0,i.jsx)("p",{children:"Stanford In The Vale"}),(0,i.jsx)("p",{children:"Faringdon"}),(0,i.jsx)("p",{children:"United Kingdom"}),(0,i.jsx)("p",{children:"SN7 8FF"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-lg font-semibold mb-3",children:"To:"}),(0,i.jsx)("div",{className:"text-sm text-gray-600 h-24 border border-dashed border-gray-300 rounded-md p-3",children:(0,i.jsx)("span",{children:e.data})})]})]}),(0,i.jsx)("div",{className:"mb-8",children:(0,i.jsxs)("table",{className:"w-full",children:[(0,i.jsx)("thead",{children:(0,i.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,i.jsx)("th",{className:"py-3 text-left font-semibold text-gray-700",children:"Description"}),(0,i.jsx)("th",{className:"py-3 text-center font-semibold text-gray-700",children:"Qty"}),(0,i.jsx)("th",{className:"py-3 text-right font-semibold text-gray-700",children:"Unit price"}),(0,i.jsx)("th",{className:"py-3 text-right font-semibold text-gray-700",children:"Amount"})]})}),(0,i.jsx)("tbody",{children:(0,i.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,i.jsx)("td",{className:"py-4 text-sm text-gray-600",children:"Nextjs code boilerplate"}),(0,i.jsx)("td",{className:"py-4 text-center text-sm text-gray-600",children:"1"}),(0,i.jsx)("td",{className:"py-4 text-right text-sm text-gray-600",children:e.price}),(0,i.jsx)("td",{className:"py-4 text-right text-sm text-gray-600",children:e.price})]})})]})}),(0,i.jsx)("div",{className:"border-t border-gray-200 pt-4",children:(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex justify-between text-sm text-gray-600",children:[(0,i.jsx)("span",{children:"Subtotal:"}),(0,i.jsx)("span",{})]}),(0,i.jsxs)("div",{className:"flex justify-between text-sm font-medium text-gray-800",children:[(0,i.jsx)("span",{children:"Total:"}),(0,i.jsx)("span",{})]}),(0,i.jsxs)("div",{className:"flex justify-between text-sm font-medium text-green-600",children:[(0,i.jsx)("span",{children:"Total amount paid:"}),(0,i.jsx)("span",{})]})]})})]});var r=s(4568),n=s(5631),o=s(7619),d=s(8150),l=s(1196),c=s(389),m=s(565),p=s(5649);let x={body:{backgroundColor:"#f3f4f6",fontFamily:"Arial, sans-serif"},container:{margin:"0 auto",padding:"20px 0 48px",width:"580px"},section:{backgroundColor:"#ffffff",borderRadius:"5px",padding:"40px"},h1:{color:"#3b82f6",fontSize:"32px",fontWeight:"bold",textAlign:"center",margin:"0 0 20px"},text:{color:"#374151",fontSize:"16px",lineHeight:"24px",textAlign:"center"},button:{backgroundColor:"#3b82f6",borderRadius:"5px",color:"#ffffff",display:"inline-block",fontSize:"16px",fontWeight:"bold",padding:"12px 24px",textDecoration:"none",textAlign:"center",marginTop:"32px"}},h=({email:e})=>(0,i.jsxs)(r.V,{children:[(0,i.jsx)(n.F,{}),(0,i.jsx)(o.M,{children:"Welcome to our community!"}),(0,i.jsx)(d.u,{style:x.body,children:(0,i.jsx)(l.W,{style:x.container,children:(0,i.jsxs)(c.$,{style:x.section,children:[(0,i.jsx)(m.x,{style:x.h1,children:"Welcome aboard!"}),(0,i.jsxs)(m.x,{style:x.text,children:["We're thrilled to have you join us, ",e,"!"]}),(0,i.jsx)(m.x,{style:x.text,children:"Get ready for an amazing journey. We can't wait to see what you'll achieve with us."}),(0,i.jsx)(c.$,{style:{textAlign:"center"},children:(0,i.jsx)(p.r,{href:"http://localhost:3000/dashboard",style:x.button,children:"Get Started"})})]})})})]});var u=s(8753),f=s(6333),g=s(7121);class j{async sendThanksYouEmail(e){let{data:t,error:s}=await this.resend.emails.send({from:u.Z.resend.fromAdmin,to:[e],replyTo:u.Z.resend.forwardRepliesTo,subject:u.Z.resend.subjects.thankYou,react:h({email:e})});if(s)throw s;return t}async sendInvoice(e,t){let{data:s,error:i}=await this.resend.emails.send({from:u.Z.resend.fromAdmin,to:[e],replyTo:u.Z.resend.forwardRepliesTo,subject:"Invoice: "+t.id,react:a(t)});if(i)throw i;return s}async addNewEmailAddress(e){let t=await this.upsertAudience();return this.resend.contacts.create({email:e,unsubscribed:!1,audienceId:t.resend_id})}async upsertAudience(){let e=await f.Z.audiences.findFirst();if(e)return e;let{data:{id:t,name:s}}=await this.resend.audiences.create({name:"Waiting List"});return f.Z.audiences.create({data:{resend_id:t,name:s}})}constructor(){this.resend=new g.R(process.env.RESEND_API_KEY)}}let y=new j}},e=>{var t=t=>e(e.s=t);e.O(0,[520,225,19],()=>t(4453));var s=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/test-email/route"]=s}]);
//# sourceMappingURL=route.js.map