'use client'

import { useEffect } from 'react'
import { useTheme } from 'next-themes'

interface CodeSnippetProps {
  code: string
  language?: string
}

const CodeSnippet = ({ code, language = 'javascript' }: CodeSnippetProps) => {
  const { theme } = useTheme()

  // Determine background color based on theme
  const getBgColor = () => {
    return theme === 'dark' ? 'bg-[#0F1121]' : 'bg-gray-50'
  }

  // Determine border color based on theme
  const getBorderColor = () => {
    return theme === 'dark' ? 'border-[#373C53]' : 'border-gray-200'
  }

  // Determine text color based on theme
  const getTextColor = () => {
    return theme === 'dark' ? 'text-gray-200' : 'text-gray-800'
  }

  // Listen for theme changes
  useEffect(() => {
    const handleThemeChange = (event: Event) => {
      const customEvent = event as CustomEvent
      const newTheme = customEvent.detail?.theme
      console.log('CodeSnippet: Theme changed to:', newTheme)
    }

    window.addEventListener('themeChange', handleThemeChange)

    return () => {
      window.removeEventListener('themeChange', handleThemeChange)
    }
  }, [])

  return (
    <div className="relative my-4">
      <pre className={`${getBgColor()} ${getBorderColor()} ${getTextColor()} border rounded-md p-4 overflow-x-auto`}>
        <code className={`language-${language}`}>{code}</code>
      </pre>
      {/* Copy button removed as requested */}
    </div>
  )
}

export default CodeSnippet
