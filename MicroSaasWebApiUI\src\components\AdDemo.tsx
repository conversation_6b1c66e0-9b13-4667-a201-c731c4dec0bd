'use client'

import { useState } from 'react'
import AdManager from './AdManager'
import AdUnit from './AdUnit'

/**
 * Demo component to showcase different ad units
 */
const AdDemo = () => {
  const [adType, setAdType] = useState<'predefined' | 'custom'>('predefined')

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Ad Units Demo</h1>
      
      <div className="mb-6">
        <div className="flex space-x-4 mb-4">
          <button
            className={`px-4 py-2 rounded ${adType === 'predefined' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
            onClick={() => setAdType('predefined')}
          >
            Predefined Ad Units
          </button>
          <button
            className={`px-4 py-2 rounded ${adType === 'custom' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
            onClick={() => setAdType('custom')}
          >
            Custom Ad Units
          </button>
        </div>
      </div>

      {adType === 'predefined' ? (
        <div className="space-y-8">
          <section>
            <h2 className="text-2xl font-semibold mb-4">Display Horizontal Ad</h2>
            <div className="p-4 border border-gray-300 rounded">
              <AdManager type="display-horizontal" />
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">Multiplex Vertical Ad</h2>
            <div className="p-4 border border-gray-300 rounded">
              <AdManager type="multiplex-vertical" />
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">In-Article Ad</h2>
            <div className="p-4 border border-gray-300 rounded">
              <AdManager type="in-article" />
            </div>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">In-Feed Ad</h2>
            <div className="p-4 border border-gray-300 rounded">
              <AdManager type="in-feed" />
            </div>
          </section>
        </div>
      ) : (
        <div className="space-y-8">
          <section>
            <h2 className="text-2xl font-semibold mb-4">Custom Ad Units</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="p-4 border border-gray-300 rounded">
                <h3 className="text-xl font-medium mb-2">Display Ad (Horizontal)</h3>
                <AdUnit position="horizontal" slot="4454393519" />
              </div>
              
              <div className="p-4 border border-gray-300 rounded">
                <h3 className="text-xl font-medium mb-2">Multiplex Ad (Sidebar)</h3>
                <AdUnit position="sidebar" slot="5891334939" />
              </div>
              
              <div className="p-4 border border-gray-300 rounded">
                <h3 className="text-xl font-medium mb-2">In-Article Ad</h3>
                <AdUnit position="in-article" slot="2944836887" />
              </div>
              
              <div className="p-4 border border-gray-300 rounded">
                <h3 className="text-xl font-medium mb-2">In-Feed Ad</h3>
                <AdUnit position="in-feed" slot="6889555982" />
              </div>
            </div>
          </section>
        </div>
      )}
    </div>
  )
}

export default AdDemo
