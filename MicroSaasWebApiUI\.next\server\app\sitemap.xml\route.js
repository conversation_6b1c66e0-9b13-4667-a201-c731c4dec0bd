(()=>{var e={};e.id=717,e.ids=[717],e.modules={20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},32694:e=>{"use strict";e.exports=require("http2")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},60577:(e,t,a)=>{"use strict";let i,n,o,s,r,p,c;a.r(t),a.d(t,{originalPathname:()=>as,patchFetch:()=>ar,requestAsyncStorage:()=>ai,routeModule:()=>aa,serverHooks:()=>ao,staticGenerationAsyncStorage:()=>an});var l,u,d,m={};a.r(m),a.d(m,{hasBrowserEnv:()=>eE,hasStandardBrowserEnv:()=>eT,hasStandardBrowserWebWorkerEnv:()=>eS,navigator:()=>ej,origin:()=>eA});var f={};a.r(f),a.d(f,{default:()=>t8});var h={};a.r(h),a.d(h,{GET:()=>at});var x=a(49303),v=a(88716),g=a(60670),b=a(55661);function y(e,t){return function(){return e.apply(t,arguments)}}let{toString:w}=Object.prototype,{getPrototypeOf:_}=Object,k=(i=Object.create(null),e=>{let t=w.call(e);return i[t]||(i[t]=t.slice(8,-1).toLowerCase())}),E=e=>(e=e.toLowerCase(),t=>k(t)===e),j=e=>t=>typeof t===e,{isArray:T}=Array,S=j("undefined"),A=E("ArrayBuffer"),O=j("string"),R=j("function"),C=j("number"),P=e=>null!==e&&"object"==typeof e,z=e=>{if("object"!==k(e))return!1;let t=_(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},L=E("Date"),D=E("File"),F=E("Blob"),q=E("FileList"),U=E("URLSearchParams"),[B,N,I,M]=["ReadableStream","Request","Response","Headers"].map(E);function H(e,t,{allOwnKeys:a=!1}={}){let i,n;if(null!=e){if("object"!=typeof e&&(e=[e]),T(e))for(i=0,n=e.length;i<n;i++)t.call(null,e[i],i,e);else{let n;let o=a?Object.getOwnPropertyNames(e):Object.keys(e),s=o.length;for(i=0;i<s;i++)n=o[i],t.call(null,e[n],n,e)}}}function G(e,t){let a;t=t.toLowerCase();let i=Object.keys(e),n=i.length;for(;n-- >0;)if(t===(a=i[n]).toLowerCase())return a;return null}let $="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,W=e=>!S(e)&&e!==$,K=(n="undefined"!=typeof Uint8Array&&_(Uint8Array),e=>n&&e instanceof n),V=E("HTMLFormElement"),J=(({hasOwnProperty:e})=>(t,a)=>e.call(t,a))(Object.prototype),Y=E("RegExp"),Q=(e,t)=>{let a=Object.getOwnPropertyDescriptors(e),i={};H(a,(a,n)=>{let o;!1!==(o=t(a,n,e))&&(i[n]=o||a)}),Object.defineProperties(e,i)},X=E("AsyncFunction"),Z=(l="function"==typeof setImmediate,u=R($.postMessage),l?setImmediate:u?(p=`axios@${Math.random()}`,c=[],$.addEventListener("message",({source:e,data:t})=>{e===$&&t===p&&c.length&&c.shift()()},!1),e=>{c.push(e),$.postMessage(p,"*")}):e=>setTimeout(e)),ee="undefined"!=typeof queueMicrotask?queueMicrotask.bind($):"undefined"!=typeof process&&process.nextTick||Z,et={isArray:T,isArrayBuffer:A,isBuffer:function(e){return null!==e&&!S(e)&&null!==e.constructor&&!S(e.constructor)&&R(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||R(e.append)&&("formdata"===(t=k(e))||"object"===t&&R(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&A(e.buffer)},isString:O,isNumber:C,isBoolean:e=>!0===e||!1===e,isObject:P,isPlainObject:z,isReadableStream:B,isRequest:N,isResponse:I,isHeaders:M,isUndefined:S,isDate:L,isFile:D,isBlob:F,isRegExp:Y,isFunction:R,isStream:e=>P(e)&&R(e.pipe),isURLSearchParams:U,isTypedArray:K,isFileList:q,forEach:H,merge:function e(){let{caseless:t}=W(this)&&this||{},a={},i=(i,n)=>{let o=t&&G(a,n)||n;z(a[o])&&z(i)?a[o]=e(a[o],i):z(i)?a[o]=e({},i):T(i)?a[o]=i.slice():a[o]=i};for(let e=0,t=arguments.length;e<t;e++)arguments[e]&&H(arguments[e],i);return a},extend:(e,t,a,{allOwnKeys:i}={})=>(H(t,(t,i)=>{a&&R(t)?e[i]=y(t,a):e[i]=t},{allOwnKeys:i}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,a,i)=>{e.prototype=Object.create(t.prototype,i),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),a&&Object.assign(e.prototype,a)},toFlatObject:(e,t,a,i)=>{let n,o,s;let r={};if(t=t||{},null==e)return t;do{for(o=(n=Object.getOwnPropertyNames(e)).length;o-- >0;)s=n[o],(!i||i(s,e,t))&&!r[s]&&(t[s]=e[s],r[s]=!0);e=!1!==a&&_(e)}while(e&&(!a||a(e,t))&&e!==Object.prototype);return t},kindOf:k,kindOfTest:E,endsWith:(e,t,a)=>{e=String(e),(void 0===a||a>e.length)&&(a=e.length),a-=t.length;let i=e.indexOf(t,a);return -1!==i&&i===a},toArray:e=>{if(!e)return null;if(T(e))return e;let t=e.length;if(!C(t))return null;let a=Array(t);for(;t-- >0;)a[t]=e[t];return a},forEachEntry:(e,t)=>{let a;let i=(e&&e[Symbol.iterator]).call(e);for(;(a=i.next())&&!a.done;){let i=a.value;t.call(e,i[0],i[1])}},matchAll:(e,t)=>{let a;let i=[];for(;null!==(a=e.exec(t));)i.push(a);return i},isHTMLForm:V,hasOwnProperty:J,hasOwnProp:J,reduceDescriptors:Q,freezeMethods:e=>{Q(e,(t,a)=>{if(R(e)&&-1!==["arguments","caller","callee"].indexOf(a))return!1;if(R(e[a])){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+a+"'")})}})},toObjectSet:(e,t)=>{let a={};return(e=>{e.forEach(e=>{a[e]=!0})})(T(e)?e:String(e).split(t)),a},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,a){return t.toUpperCase()+a}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:G,global:$,isContextDefined:W,isSpecCompliantForm:function(e){return!!(e&&R(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])},toJSONObject:e=>{let t=Array(10),a=(e,i)=>{if(P(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[i]=e;let n=T(e)?[]:{};return H(e,(e,t)=>{let o=a(e,i+1);S(o)||(n[t]=o)}),t[i]=void 0,n}}return e};return a(e,0)},isAsyncFn:X,isThenable:e=>e&&(P(e)||R(e))&&R(e.then)&&R(e.catch),setImmediate:Z,asap:ee};function ea(e,t,a,i,n){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),a&&(this.config=a),i&&(this.request=i),n&&(this.response=n,this.status=n.status?n.status:null)}et.inherits(ea,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:et.toJSONObject(this.config),code:this.code,status:this.status}}});let ei=ea.prototype,en={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{en[e]={value:e}}),Object.defineProperties(ea,en),Object.defineProperty(ei,"isAxiosError",{value:!0}),ea.from=(e,t,a,i,n,o)=>{let s=Object.create(ei);return et.toFlatObject(e,s,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),ea.call(s,e.message,t,a,i,n),s.cause=e,s.name=e.name,o&&Object.assign(s,o),s};var eo=a(21216);function es(e){return et.isPlainObject(e)||et.isArray(e)}function er(e){return et.endsWith(e,"[]")?e.slice(0,-2):e}function ep(e,t,a){return e?e.concat(t).map(function(e,t){return e=er(e),!a&&t?"["+e+"]":e}).join(a?".":""):t}let ec=et.toFlatObject(et,{},null,function(e){return/^is[A-Z]/.test(e)}),el=function(e,t,a){if(!et.isObject(e))throw TypeError("target must be an object");t=t||new(eo||FormData);let i=(a=et.toFlatObject(a,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!et.isUndefined(t[e])})).metaTokens,n=a.visitor||c,o=a.dots,s=a.indexes,r=(a.Blob||"undefined"!=typeof Blob&&Blob)&&et.isSpecCompliantForm(t);if(!et.isFunction(n))throw TypeError("visitor must be a function");function p(e){if(null===e)return"";if(et.isDate(e))return e.toISOString();if(!r&&et.isBlob(e))throw new ea("Blob is not supported. Use a Buffer instead.");return et.isArrayBuffer(e)||et.isTypedArray(e)?r&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function c(e,a,n){let r=e;if(e&&!n&&"object"==typeof e){if(et.endsWith(a,"{}"))a=i?a:a.slice(0,-2),e=JSON.stringify(e);else{var c;if(et.isArray(e)&&(c=e,et.isArray(c)&&!c.some(es))||(et.isFileList(e)||et.endsWith(a,"[]"))&&(r=et.toArray(e)))return a=er(a),r.forEach(function(e,i){et.isUndefined(e)||null===e||t.append(!0===s?ep([a],i,o):null===s?a:a+"[]",p(e))}),!1}}return!!es(e)||(t.append(ep(n,a,o),p(e)),!1)}let l=[],u=Object.assign(ec,{defaultVisitor:c,convertValue:p,isVisitable:es});if(!et.isObject(e))throw TypeError("data must be an object");return function e(a,i){if(!et.isUndefined(a)){if(-1!==l.indexOf(a))throw Error("Circular reference detected in "+i.join("."));l.push(a),et.forEach(a,function(a,o){!0===(!(et.isUndefined(a)||null===a)&&n.call(t,a,et.isString(o)?o.trim():o,i,u))&&e(a,i?i.concat(o):[o])}),l.pop()}}(e),t};function eu(e){let t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function ed(e,t){this._pairs=[],e&&el(e,this,t)}let em=ed.prototype;function ef(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function eh(e,t,a){let i;if(!t)return e;let n=a&&a.encode||ef;et.isFunction(a)&&(a={serialize:a});let o=a&&a.serialize;if(i=o?o(t,a):et.isURLSearchParams(t)?t.toString():new ed(t,a).toString(n)){let t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+i}return e}em.append=function(e,t){this._pairs.push([e,t])},em.toString=function(e){let t=e?function(t){return e.call(this,t,eu)}:eu;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};class ex{constructor(){this.handlers=[]}use(e,t,a){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!a&&a.synchronous,runWhen:a?a.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){et.forEach(this.handlers,function(t){null!==t&&e(t)})}}let ev={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1};var eg=a(84770);let eb=a(17360).URLSearchParams,ey="abcdefghijklmnopqrstuvwxyz",ew="0123456789",e_={DIGIT:ew,ALPHA:ey,ALPHA_DIGIT:ey+ey.toUpperCase()+ew},ek={isNode:!0,classes:{URLSearchParams:eb,FormData:eo,Blob:"undefined"!=typeof Blob&&Blob||null},ALPHABET:e_,generateString:(e=16,t=e_.ALPHA_DIGIT)=>{let a="",{length:i}=t,n=new Uint32Array(e);eg.randomFillSync(n);for(let o=0;o<e;o++)a+=t[n[o]%i];return a},protocols:["http","https","file","data"]},eE="undefined"!=typeof window&&"undefined"!=typeof document,ej="object"==typeof navigator&&navigator||void 0,eT=eE&&(!ej||0>["ReactNative","NativeScript","NS"].indexOf(ej.product)),eS="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,eA=eE&&window.location.href||"http://localhost",eO={...m,...ek},eR=function(e){if(et.isFormData(e)&&et.isFunction(e.entries)){let t={};return et.forEachEntry(e,(e,a)=>{!function e(t,a,i,n){let o=t[n++];if("__proto__"===o)return!0;let s=Number.isFinite(+o),r=n>=t.length;return(o=!o&&et.isArray(i)?i.length:o,r)?et.hasOwnProp(i,o)?i[o]=[i[o],a]:i[o]=a:(i[o]&&et.isObject(i[o])||(i[o]=[]),e(t,a,i[o],n)&&et.isArray(i[o])&&(i[o]=function(e){let t,a;let i={},n=Object.keys(e),o=n.length;for(t=0;t<o;t++)i[a=n[t]]=e[a];return i}(i[o]))),!s}(et.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0]),a,t,0)}),t}return null},eC={transitional:ev,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){let a;let i=t.getContentType()||"",n=i.indexOf("application/json")>-1,o=et.isObject(e);if(o&&et.isHTMLForm(e)&&(e=new FormData(e)),et.isFormData(e))return n?JSON.stringify(eR(e)):e;if(et.isArrayBuffer(e)||et.isBuffer(e)||et.isStream(e)||et.isFile(e)||et.isBlob(e)||et.isReadableStream(e))return e;if(et.isArrayBufferView(e))return e.buffer;if(et.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();if(o){if(i.indexOf("application/x-www-form-urlencoded")>-1){var s,r;return(s=e,r=this.formSerializer,el(s,new eO.classes.URLSearchParams,Object.assign({visitor:function(e,t,a,i){return eO.isNode&&et.isBuffer(e)?(this.append(t,e.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)}},r))).toString()}if((a=et.isFileList(e))||i.indexOf("multipart/form-data")>-1){let t=this.env&&this.env.FormData;return el(a?{"files[]":e}:e,t&&new t,this.formSerializer)}}return o||n?(t.setContentType("application/json",!1),function(e,t,a){if(et.isString(e))try{return(0,JSON.parse)(e),et.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){let t=this.transitional||eC.transitional,a=t&&t.forcedJSONParsing,i="json"===this.responseType;if(et.isResponse(e)||et.isReadableStream(e))return e;if(e&&et.isString(e)&&(a&&!this.responseType||i)){let a=t&&t.silentJSONParsing;try{return JSON.parse(e)}catch(e){if(!a&&i){if("SyntaxError"===e.name)throw ea.from(e,ea.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:eO.classes.FormData,Blob:eO.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};et.forEach(["delete","get","head","post","put","patch"],e=>{eC.headers[e]={}});let eP=et.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),ez=e=>{let t,a,i;let n={};return e&&e.split("\n").forEach(function(e){i=e.indexOf(":"),t=e.substring(0,i).trim().toLowerCase(),a=e.substring(i+1).trim(),!t||n[t]&&eP[t]||("set-cookie"===t?n[t]?n[t].push(a):n[t]=[a]:n[t]=n[t]?n[t]+", "+a:a)}),n},eL=Symbol("internals");function eD(e){return e&&String(e).trim().toLowerCase()}function eF(e){return!1===e||null==e?e:et.isArray(e)?e.map(eF):String(e)}let eq=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function eU(e,t,a,i,n){if(et.isFunction(i))return i.call(this,t,a);if(n&&(t=a),et.isString(t)){if(et.isString(i))return -1!==t.indexOf(i);if(et.isRegExp(i))return i.test(t)}}class eB{constructor(e){e&&this.set(e)}set(e,t,a){let i=this;function n(e,t,a){let n=eD(t);if(!n)throw Error("header name must be a non-empty string");let o=et.findKey(i,n);o&&void 0!==i[o]&&!0!==a&&(void 0!==a||!1===i[o])||(i[o||t]=eF(e))}let o=(e,t)=>et.forEach(e,(e,a)=>n(e,a,t));if(et.isPlainObject(e)||e instanceof this.constructor)o(e,t);else if(et.isString(e)&&(e=e.trim())&&!eq(e))o(ez(e),t);else if(et.isHeaders(e))for(let[t,i]of e.entries())n(i,t,a);else null!=e&&n(t,e,a);return this}get(e,t){if(e=eD(e)){let a=et.findKey(this,e);if(a){let e=this[a];if(!t)return e;if(!0===t)return function(e){let t;let a=Object.create(null),i=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;for(;t=i.exec(e);)a[t[1]]=t[2];return a}(e);if(et.isFunction(t))return t.call(this,e,a);if(et.isRegExp(t))return t.exec(e);throw TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=eD(e)){let a=et.findKey(this,e);return!!(a&&void 0!==this[a]&&(!t||eU(this,this[a],a,t)))}return!1}delete(e,t){let a=this,i=!1;function n(e){if(e=eD(e)){let n=et.findKey(a,e);n&&(!t||eU(a,a[n],n,t))&&(delete a[n],i=!0)}}return et.isArray(e)?e.forEach(n):n(e),i}clear(e){let t=Object.keys(this),a=t.length,i=!1;for(;a--;){let n=t[a];(!e||eU(this,this[n],n,e,!0))&&(delete this[n],i=!0)}return i}normalize(e){let t=this,a={};return et.forEach(this,(i,n)=>{let o=et.findKey(a,n);if(o){t[o]=eF(i),delete t[n];return}let s=e?n.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,a)=>t.toUpperCase()+a):String(n).trim();s!==n&&delete t[n],t[s]=eF(i),a[s]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let t=Object.create(null);return et.forEach(this,(a,i)=>{null!=a&&!1!==a&&(t[i]=e&&et.isArray(a)?a.join(", "):a)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){let a=new this(e);return t.forEach(e=>a.set(e)),a}static accessor(e){let t=(this[eL]=this[eL]={accessors:{}}).accessors,a=this.prototype;function i(e){let i=eD(e);t[i]||(function(e,t){let a=et.toCamelCase(" "+t);["get","set","has"].forEach(i=>{Object.defineProperty(e,i+a,{value:function(e,a,n){return this[i].call(this,t,e,a,n)},configurable:!0})})}(a,e),t[i]=!0)}return et.isArray(e)?e.forEach(i):i(e),this}}function eN(e,t){let a=this||eC,i=t||a,n=eB.from(i.headers),o=i.data;return et.forEach(e,function(e){o=e.call(a,o,n.normalize(),t?t.status:void 0)}),n.normalize(),o}function eI(e){return!!(e&&e.__CANCEL__)}function eM(e,t,a){ea.call(this,null==e?"canceled":e,ea.ERR_CANCELED,t,a),this.name="CanceledError"}function eH(e,t,a){let i=a.config.validateStatus;!a.status||!i||i(a.status)?e(a):t(new ea("Request failed with status code "+a.status,[ea.ERR_BAD_REQUEST,ea.ERR_BAD_RESPONSE][Math.floor(a.status/100)-4],a.config,a.request,a))}function eG(e,t,a){let i=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&i||!1==a?t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e:t}eB.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),et.reduceDescriptors(eB.prototype,({value:e},t)=>{let a=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[a]=e}}}),et.freezeMethods(eB),et.inherits(eM,ea,{__CANCEL__:!0});var e$=a(12027),eW=a(32615),eK=a(35240),eV=a(21764),eJ=a(78179),eY=a(71568);let eQ="1.8.3";function eX(e){let t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}let eZ=/^(?:([^;]+);)?(?:[^;]+;)?(base64|),([\s\S]*)$/;var e0=a(76162);let e1=Symbol("internals");class e2 extends e0.Transform{constructor(e){super({readableHighWaterMark:(e=et.toFlatObject(e,{maxRate:0,chunkSize:65536,minChunkSize:100,timeWindow:500,ticksRate:2,samplesCount:15},null,(e,t)=>!et.isUndefined(t[e]))).chunkSize});let t=this[e1]={timeWindow:e.timeWindow,chunkSize:e.chunkSize,maxRate:e.maxRate,minChunkSize:e.minChunkSize,bytesSeen:0,isCaptured:!1,notifiedBytesLoaded:0,ts:Date.now(),bytes:0,onReadCallback:null};this.on("newListener",e=>{"progress"!==e||t.isCaptured||(t.isCaptured=!0)})}_read(e){let t=this[e1];return t.onReadCallback&&t.onReadCallback(),super._read(e)}_transform(e,t,a){let i=this[e1],n=i.maxRate,o=this.readableHighWaterMark,s=i.timeWindow,r=n/(1e3/s),p=!1!==i.minChunkSize?Math.max(i.minChunkSize,.01*r):0,c=(e,t)=>{let a=Buffer.byteLength(e);i.bytesSeen+=a,i.bytes+=a,i.isCaptured&&this.emit("progress",i.bytesSeen),this.push(e)?process.nextTick(t):i.onReadCallback=()=>{i.onReadCallback=null,process.nextTick(t)}},l=(e,t)=>{let a;let l=Buffer.byteLength(e),u=null,d=o,m=0;if(n){let e=Date.now();(!i.ts||(m=e-i.ts)>=s)&&(i.ts=e,a=r-i.bytes,i.bytes=a<0?-a:0,m=0),a=r-i.bytes}if(n){if(a<=0)return setTimeout(()=>{t(null,e)},s-m);a<d&&(d=a)}d&&l>d&&l-d>p&&(u=e.subarray(d),e=e.subarray(0,d)),c(e,u?()=>{process.nextTick(t,null,u)}:t)};l(e,function e(t,i){if(t)return a(t);i?l(i,e):a(null)})}}var e3=a(17702);let{asyncIterator:e6}=Symbol,e4=async function*(e){e.stream?yield*e.stream():e.arrayBuffer?yield await e.arrayBuffer():e[e6]?yield*e[e6]():yield e},e8=eO.ALPHABET.ALPHA_DIGIT+"-_",e5="function"==typeof TextEncoder?new TextEncoder:new eV.TextEncoder,e7=e5.encode("\r\n");class e9{constructor(e,t){let{escapeName:a}=this.constructor,i=et.isString(t),n=`Content-Disposition: form-data; name="${a(e)}"${!i&&t.name?`; filename="${a(t.name)}"`:""}\r
`;i?t=e5.encode(String(t).replace(/\r?\n|\r\n?/g,"\r\n")):n+=`Content-Type: ${t.type||"application/octet-stream"}\r
`,this.headers=e5.encode(n+"\r\n"),this.contentLength=i?t.byteLength:t.size,this.size=this.headers.byteLength+this.contentLength+2,this.name=e,this.value=t}async *encode(){yield this.headers;let{value:e}=this;et.isTypedArray(e)?yield e:yield*e4(e),yield e7}static escapeName(e){return String(e).replace(/[\r\n"]/g,e=>({"\r":"%0D","\n":"%0A",'"':"%22"})[e])}}let te=(e,t,a)=>{let{tag:i="form-data-boundary",size:n=25,boundary:o=i+"-"+eO.generateString(n,e8)}=a||{};if(!et.isFormData(e))throw TypeError("FormData instance required");if(o.length<1||o.length>70)throw Error("boundary must be 10-70 characters long");let s=e5.encode("--"+o+"\r\n"),r=e5.encode("--"+o+"--\r\n\r\n"),p=r.byteLength,c=Array.from(e.entries()).map(([e,t])=>{let a=new e9(e,t);return p+=a.size,a});p+=s.byteLength*c.length;let l={"Content-Type":`multipart/form-data; boundary=${o}`};return Number.isFinite(p=et.toFiniteNumber(p))&&(l["Content-Length"]=p),t&&t(l),e0.Readable.from(async function*(){for(let e of c)yield s,yield*e.encode();yield r}())};class tt extends e0.Transform{__transform(e,t,a){this.push(e),a()}_transform(e,t,a){if(0!==e.length&&(this._transform=this.__transform,120!==e[0])){let e=Buffer.alloc(2);e[0]=120,e[1]=156,this.push(e,t)}this.__transform(e,t,a)}}let ta=(e,t)=>et.isAsyncFn(e)?function(...a){let i=a.pop();e.apply(this,a).then(e=>{try{t?i(null,...t(e)):i(null,e)}catch(e){i(e)}},i)}:e,ti=function(e,t){let a;let i=Array(e=e||10),n=Array(e),o=0,s=0;return t=void 0!==t?t:1e3,function(r){let p=Date.now(),c=n[s];a||(a=p),i[o]=r,n[o]=p;let l=s,u=0;for(;l!==o;)u+=i[l++],l%=e;if((o=(o+1)%e)===s&&(s=(s+1)%e),p-a<t)return;let d=c&&p-c;return d?Math.round(1e3*u/d):void 0}},tn=function(e,t){let a,i,n=0,o=1e3/t,s=(t,o=Date.now())=>{n=o,a=null,i&&(clearTimeout(i),i=null),e.apply(null,t)};return[(...e)=>{let t=Date.now(),r=t-n;r>=o?s(e,t):(a=e,i||(i=setTimeout(()=>{i=null,s(a)},o-r)))},()=>a&&s(a)]},to=(e,t,a=3)=>{let i=0,n=ti(50,250);return tn(a=>{let o=a.loaded,s=a.lengthComputable?a.total:void 0,r=o-i,p=n(r);i=o,e({loaded:o,total:s,progress:s?o/s:void 0,bytes:r,rate:p||void 0,estimated:p&&s&&o<=s?(s-o)/p:void 0,event:a,lengthComputable:null!=s,[t?"download":"upload"]:!0})},a)},ts=(e,t)=>{let a=null!=e;return[i=>t[0]({lengthComputable:a,total:e,loaded:i}),t[1]]},tr=e=>(...t)=>et.asap(()=>e(...t)),tp={flush:eY.constants.Z_SYNC_FLUSH,finishFlush:eY.constants.Z_SYNC_FLUSH},tc={flush:eY.constants.BROTLI_OPERATION_FLUSH,finishFlush:eY.constants.BROTLI_OPERATION_FLUSH},tl=et.isFunction(eY.createBrotliDecompress),{http:tu,https:td}=eJ,tm=/https:?/,tf=eO.protocols.map(e=>e+":"),th=(e,[t,a])=>(e.on("end",a).on("error",a),t);function tx(e,t){e.beforeRedirects.proxy&&e.beforeRedirects.proxy(e),e.beforeRedirects.config&&e.beforeRedirects.config(e,t)}let tv="undefined"!=typeof process&&"process"===et.kindOf(process),tg=e=>new Promise((t,a)=>{let i,n;let o=(e,t)=>{!n&&(n=!0,i&&i(e,t))},s=e=>{o(e,!0),a(e)};e(e=>{o(e),t(e)},s,e=>i=e).catch(s)}),tb=({address:e,family:t})=>{if(!et.isString(e))throw TypeError("address must be a string");return{address:e,family:t||(0>e.indexOf(".")?6:4)}},ty=(e,t)=>tb(et.isObject(e)?e:{address:e,family:t}),tw=tv&&function(e){return tg(async function(t,a,i){let n,o,s,r,p,c,l,{data:u,lookup:d,family:m}=e,{responseType:f,responseEncoding:h}=e,x=e.method.toUpperCase(),v=!1;if(d){let e=ta(d,e=>et.isArray(e)?e:[e]);d=(t,a,i)=>{e(t,a,(e,t,n)=>{if(e)return i(e);let o=et.isArray(t)?t.map(e=>ty(e)):[ty(t,n)];a.all?i(e,o):i(e,o[0].address,o[0].family)})}}let g=new e3.EventEmitter,b=()=>{e.cancelToken&&e.cancelToken.unsubscribe(y),e.signal&&e.signal.removeEventListener("abort",y),g.removeAllListeners()};function y(t){g.emit("abort",!t||t.type?new eM(null,e,p):t)}i((e,t)=>{r=!0,t&&(v=!0,b())}),g.once("abort",a),(e.cancelToken||e.signal)&&(e.cancelToken&&e.cancelToken.subscribe(y),e.signal&&(e.signal.aborted?y():e.signal.addEventListener("abort",y)));let w=new URL(eG(e.baseURL,e.url,e.allowAbsoluteUrls),eO.hasBrowserEnv?eO.origin:void 0),_=w.protocol||tf[0];if("data:"===_){let i;if("GET"!==x)return eH(t,a,{status:405,statusText:"method not allowed",headers:{},config:e});try{i=function(e,t,a){let i=a&&a.Blob||eO.classes.Blob,n=eX(e);if(void 0===t&&i&&(t=!0),"data"===n){e=n.length?e.slice(n.length+1):e;let a=eZ.exec(e);if(!a)throw new ea("Invalid URL",ea.ERR_INVALID_URL);let o=a[1],s=a[2],r=a[3],p=Buffer.from(decodeURIComponent(r),s?"base64":"utf8");if(t){if(!i)throw new ea("Blob is not supported",ea.ERR_NOT_SUPPORT);return new i([p],{type:o})}return p}throw new ea("Unsupported protocol "+n,ea.ERR_NOT_SUPPORT)}(e.url,"blob"===f,{Blob:e.env&&e.env.Blob})}catch(t){throw ea.from(t,ea.ERR_BAD_REQUEST,e)}return"text"===f?(i=i.toString(h),h&&"utf8"!==h||(i=et.stripBOM(i))):"stream"===f&&(i=e0.Readable.from(i)),eH(t,a,{data:i,status:200,statusText:"OK",headers:new eB,config:e})}if(-1===tf.indexOf(_))return a(new ea("Unsupported protocol "+_,ea.ERR_BAD_REQUEST,e));let k=eB.from(e.headers).normalize();k.set("User-Agent","axios/"+eQ,!1);let{onUploadProgress:E,onDownloadProgress:j}=e,T=e.maxRate;if(et.isSpecCompliantForm(u)){let e=k.getContentType(/boundary=([-_\w\d]{10,70})/i);u=te(u,e=>{k.set(e)},{tag:`axios-${eQ}-boundary`,boundary:e&&e[1]||void 0})}else if(et.isFormData(u)&&et.isFunction(u.getHeaders)){if(k.set(u.getHeaders()),!k.hasContentLength())try{let e=await eV.promisify(u.getLength).call(u);Number.isFinite(e)&&e>=0&&k.setContentLength(e)}catch(e){}}else if(et.isBlob(u)||et.isFile(u))u.size&&k.setContentType(u.type||"application/octet-stream"),k.setContentLength(u.size||0),u=e0.Readable.from(e4(u));else if(u&&!et.isStream(u)){if(Buffer.isBuffer(u));else if(et.isArrayBuffer(u))u=Buffer.from(new Uint8Array(u));else{if(!et.isString(u))return a(new ea("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",ea.ERR_BAD_REQUEST,e));u=Buffer.from(u,"utf-8")}if(k.setContentLength(u.length,!1),e.maxBodyLength>-1&&u.length>e.maxBodyLength)return a(new ea("Request body larger than maxBodyLength limit",ea.ERR_BAD_REQUEST,e))}let S=et.toFiniteNumber(k.getContentLength());et.isArray(T)?(n=T[0],o=T[1]):n=o=T,u&&(E||n)&&(et.isStream(u)||(u=e0.Readable.from(u,{objectMode:!1})),u=e0.pipeline([u,new e2({maxRate:et.toFiniteNumber(n)})],et.noop),E&&u.on("progress",th(u,ts(S,to(tr(E),!1,3))))),e.auth&&(s=(e.auth.username||"")+":"+(e.auth.password||"")),!s&&w.username&&(s=w.username+":"+w.password),s&&k.delete("authorization");try{c=eh(w.pathname+w.search,e.params,e.paramsSerializer).replace(/^\?/,"")}catch(i){let t=Error(i.message);return t.config=e,t.url=e.url,t.exists=!0,a(t)}k.set("Accept-Encoding","gzip, compress, deflate"+(tl?", br":""),!1);let A={path:c,method:x,headers:k.toJSON(),agents:{http:e.httpAgent,https:e.httpsAgent},auth:s,protocol:_,family:m,beforeRedirect:tx,beforeRedirects:{}};et.isUndefined(d)||(A.lookup=d),e.socketPath?A.socketPath=e.socketPath:(A.hostname=w.hostname.startsWith("[")?w.hostname.slice(1,-1):w.hostname,A.port=w.port,function e(t,a,i){let n=a;if(!n&&!1!==n){let e=e$.getProxyForUrl(i);e&&(n=new URL(e))}if(n){if(n.username&&(n.auth=(n.username||"")+":"+(n.password||"")),n.auth){(n.auth.username||n.auth.password)&&(n.auth=(n.auth.username||"")+":"+(n.auth.password||""));let e=Buffer.from(n.auth,"utf8").toString("base64");t.headers["Proxy-Authorization"]="Basic "+e}t.headers.host=t.hostname+(t.port?":"+t.port:"");let e=n.hostname||n.host;t.hostname=e,t.host=e,t.port=n.port,t.path=i,n.protocol&&(t.protocol=n.protocol.includes(":")?n.protocol:`${n.protocol}:`)}t.beforeRedirects.proxy=function(t){e(t,a,t.href)}}(A,e.proxy,_+"//"+w.hostname+(w.port?":"+w.port:"")+A.path));let O=tm.test(A.protocol);if(A.agent=O?e.httpsAgent:e.httpAgent,e.transport?l=e.transport:0===e.maxRedirects?l=O?eK:eW:(e.maxRedirects&&(A.maxRedirects=e.maxRedirects),e.beforeRedirect&&(A.beforeRedirects.config=e.beforeRedirect),l=O?td:tu),e.maxBodyLength>-1?A.maxBodyLength=e.maxBodyLength:A.maxBodyLength=1/0,e.insecureHTTPParser&&(A.insecureHTTPParser=e.insecureHTTPParser),p=l.request(A,function(i){if(p.destroyed)return;let n=[i],s=+i.headers["content-length"];if(j||o){let e=new e2({maxRate:et.toFiniteNumber(o)});j&&e.on("progress",th(e,ts(s,to(tr(j),!0,3)))),n.push(e)}let r=i,c=i.req||p;if(!1!==e.decompress&&i.headers["content-encoding"])switch(("HEAD"===x||204===i.statusCode)&&delete i.headers["content-encoding"],(i.headers["content-encoding"]||"").toLowerCase()){case"gzip":case"x-gzip":case"compress":case"x-compress":n.push(eY.createUnzip(tp)),delete i.headers["content-encoding"];break;case"deflate":n.push(new tt),n.push(eY.createUnzip(tp)),delete i.headers["content-encoding"];break;case"br":tl&&(n.push(eY.createBrotliDecompress(tc)),delete i.headers["content-encoding"])}r=n.length>1?e0.pipeline(n,et.noop):n[0];let l=e0.finished(r,()=>{l(),b()}),u={status:i.statusCode,statusText:i.statusMessage,headers:new eB(i.headers),config:e,request:c};if("stream"===f)u.data=r,eH(t,a,u);else{let i=[],n=0;r.on("data",function(t){i.push(t),n+=t.length,e.maxContentLength>-1&&n>e.maxContentLength&&(v=!0,r.destroy(),a(new ea("maxContentLength size of "+e.maxContentLength+" exceeded",ea.ERR_BAD_RESPONSE,e,c)))}),r.on("aborted",function(){if(v)return;let t=new ea("stream has been aborted",ea.ERR_BAD_RESPONSE,e,c);r.destroy(t),a(t)}),r.on("error",function(t){p.destroyed||a(ea.from(t,null,e,c))}),r.on("end",function(){try{let e=1===i.length?i[0]:Buffer.concat(i);"arraybuffer"===f||(e=e.toString(h),h&&"utf8"!==h||(e=et.stripBOM(e))),u.data=e}catch(t){return a(ea.from(t,null,e,u.request,u))}eH(t,a,u)})}g.once("abort",e=>{r.destroyed||(r.emit("error",e),r.destroy())})}),g.once("abort",e=>{a(e),p.destroy(e)}),p.on("error",function(t){a(ea.from(t,null,e,p))}),p.on("socket",function(e){e.setKeepAlive(!0,6e4)}),e.timeout){let t=parseInt(e.timeout,10);if(Number.isNaN(t)){a(new ea("error trying to parse `config.timeout` to int",ea.ERR_BAD_OPTION_VALUE,e,p));return}p.setTimeout(t,function(){if(r)return;let t=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded",i=e.transitional||ev;e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),a(new ea(t,i.clarifyTimeoutError?ea.ETIMEDOUT:ea.ECONNABORTED,e,p)),y()})}if(et.isStream(u)){let t=!1,a=!1;u.on("end",()=>{t=!0}),u.once("error",e=>{a=!0,p.destroy(e)}),u.on("close",()=>{t||a||y(new eM("Request stream has been aborted",e,p))}),u.pipe(p)}else p.end(u)})},t_=eO.hasStandardBrowserEnv?(o=new URL(eO.origin),s=eO.navigator&&/(msie|trident)/i.test(eO.navigator.userAgent),e=>(e=new URL(e,eO.origin),o.protocol===e.protocol&&o.host===e.host&&(s||o.port===e.port))):()=>!0,tk=eO.hasStandardBrowserEnv?{write(e,t,a,i,n,o){let s=[e+"="+encodeURIComponent(t)];et.isNumber(a)&&s.push("expires="+new Date(a).toGMTString()),et.isString(i)&&s.push("path="+i),et.isString(n)&&s.push("domain="+n),!0===o&&s.push("secure"),document.cookie=s.join("; ")},read(e){let t=document.cookie.match(RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}},tE=e=>e instanceof eB?{...e}:e;function tj(e,t){t=t||{};let a={};function i(e,t,a,i){return et.isPlainObject(e)&&et.isPlainObject(t)?et.merge.call({caseless:i},e,t):et.isPlainObject(t)?et.merge({},t):et.isArray(t)?t.slice():t}function n(e,t,a,n){return et.isUndefined(t)?et.isUndefined(e)?void 0:i(void 0,e,a,n):i(e,t,a,n)}function o(e,t){if(!et.isUndefined(t))return i(void 0,t)}function s(e,t){return et.isUndefined(t)?et.isUndefined(e)?void 0:i(void 0,e):i(void 0,t)}function r(a,n,o){return o in t?i(a,n):o in e?i(void 0,a):void 0}let p={url:o,method:o,data:o,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:r,headers:(e,t,a)=>n(tE(e),tE(t),a,!0)};return et.forEach(Object.keys(Object.assign({},e,t)),function(i){let o=p[i]||n,s=o(e[i],t[i],i);et.isUndefined(s)&&o!==r||(a[i]=s)}),a}let tT=e=>{let t;let a=tj({},e),{data:i,withXSRFToken:n,xsrfHeaderName:o,xsrfCookieName:s,headers:r,auth:p}=a;if(a.headers=r=eB.from(r),a.url=eh(eG(a.baseURL,a.url,a.allowAbsoluteUrls),e.params,e.paramsSerializer),p&&r.set("Authorization","Basic "+btoa((p.username||"")+":"+(p.password?unescape(encodeURIComponent(p.password)):""))),et.isFormData(i)){if(eO.hasStandardBrowserEnv||eO.hasStandardBrowserWebWorkerEnv)r.setContentType(void 0);else if(!1!==(t=r.getContentType())){let[e,...a]=t?t.split(";").map(e=>e.trim()).filter(Boolean):[];r.setContentType([e||"multipart/form-data",...a].join("; "))}}if(eO.hasStandardBrowserEnv&&(n&&et.isFunction(n)&&(n=n(a)),n||!1!==n&&t_(a.url))){let e=o&&s&&tk.read(s);e&&r.set(o,e)}return a},tS="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise(function(t,a){let i,n,o,s,r;let p=tT(e),c=p.data,l=eB.from(p.headers).normalize(),{responseType:u,onUploadProgress:d,onDownloadProgress:m}=p;function f(){s&&s(),r&&r(),p.cancelToken&&p.cancelToken.unsubscribe(i),p.signal&&p.signal.removeEventListener("abort",i)}let h=new XMLHttpRequest;function x(){if(!h)return;let i=eB.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders());eH(function(e){t(e),f()},function(e){a(e),f()},{data:u&&"text"!==u&&"json"!==u?h.response:h.responseText,status:h.status,statusText:h.statusText,headers:i,config:e,request:h}),h=null}h.open(p.method.toUpperCase(),p.url,!0),h.timeout=p.timeout,"onloadend"in h?h.onloadend=x:h.onreadystatechange=function(){h&&4===h.readyState&&(0!==h.status||h.responseURL&&0===h.responseURL.indexOf("file:"))&&setTimeout(x)},h.onabort=function(){h&&(a(new ea("Request aborted",ea.ECONNABORTED,e,h)),h=null)},h.onerror=function(){a(new ea("Network Error",ea.ERR_NETWORK,e,h)),h=null},h.ontimeout=function(){let t=p.timeout?"timeout of "+p.timeout+"ms exceeded":"timeout exceeded",i=p.transitional||ev;p.timeoutErrorMessage&&(t=p.timeoutErrorMessage),a(new ea(t,i.clarifyTimeoutError?ea.ETIMEDOUT:ea.ECONNABORTED,e,h)),h=null},void 0===c&&l.setContentType(null),"setRequestHeader"in h&&et.forEach(l.toJSON(),function(e,t){h.setRequestHeader(t,e)}),et.isUndefined(p.withCredentials)||(h.withCredentials=!!p.withCredentials),u&&"json"!==u&&(h.responseType=p.responseType),m&&([o,r]=to(m,!0),h.addEventListener("progress",o)),d&&h.upload&&([n,s]=to(d),h.upload.addEventListener("progress",n),h.upload.addEventListener("loadend",s)),(p.cancelToken||p.signal)&&(i=t=>{h&&(a(!t||t.type?new eM(null,e,h):t),h.abort(),h=null)},p.cancelToken&&p.cancelToken.subscribe(i),p.signal&&(p.signal.aborted?i():p.signal.addEventListener("abort",i)));let v=eX(p.url);if(v&&-1===eO.protocols.indexOf(v)){a(new ea("Unsupported protocol "+v+":",ea.ERR_BAD_REQUEST,e));return}h.send(c||null)})},tA=(e,t)=>{let{length:a}=e=e?e.filter(Boolean):[];if(t||a){let a,i=new AbortController,n=function(e){if(!a){a=!0,s();let t=e instanceof Error?e:this.reason;i.abort(t instanceof ea?t:new eM(t instanceof Error?t.message:t))}},o=t&&setTimeout(()=>{o=null,n(new ea(`timeout ${t} of ms exceeded`,ea.ETIMEDOUT))},t),s=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(n):e.removeEventListener("abort",n)}),e=null)};e.forEach(e=>e.addEventListener("abort",n));let{signal:r}=i;return r.unsubscribe=()=>et.asap(s),r}},tO=function*(e,t){let a,i=e.byteLength;if(!t||i<t){yield e;return}let n=0;for(;n<i;)a=n+t,yield e.slice(n,a),n=a},tR=async function*(e,t){for await(let a of tC(e))yield*tO(a,t)},tC=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}let t=e.getReader();try{for(;;){let{done:e,value:a}=await t.read();if(e)break;yield a}}finally{await t.cancel()}},tP=(e,t,a,i)=>{let n;let o=tR(e,t),s=0,r=e=>{!n&&(n=!0,i&&i(e))};return new ReadableStream({async pull(e){try{let{done:t,value:i}=await o.next();if(t){r(),e.close();return}let n=i.byteLength;if(a){let e=s+=n;a(e)}e.enqueue(new Uint8Array(i))}catch(e){throw r(e),e}},cancel:e=>(r(e),o.return())},{highWaterMark:2})},tz="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,tL=tz&&"function"==typeof ReadableStream,tD=tz&&("function"==typeof TextEncoder?(r=new TextEncoder,e=>r.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer())),tF=(e,...t)=>{try{return!!e(...t)}catch(e){return!1}},tq=tL&&tF(()=>{let e=!1,t=new Request(eO.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),tU=tL&&tF(()=>et.isReadableStream(new Response("").body)),tB={stream:tU&&(e=>e.body)};tz&&(d=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{tB[e]||(tB[e]=et.isFunction(d[e])?t=>t[e]():(t,a)=>{throw new ea(`Response type '${e}' is not supported`,ea.ERR_NOT_SUPPORT,a)})}));let tN=async e=>{if(null==e)return 0;if(et.isBlob(e))return e.size;if(et.isSpecCompliantForm(e)){let t=new Request(eO.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return et.isArrayBufferView(e)||et.isArrayBuffer(e)?e.byteLength:(et.isURLSearchParams(e)&&(e+=""),et.isString(e))?(await tD(e)).byteLength:void 0},tI=async(e,t)=>{let a=et.toFiniteNumber(e.getContentLength());return null==a?tN(t):a},tM={http:tw,xhr:tS,fetch:tz&&(async e=>{let t,a,{url:i,method:n,data:o,signal:s,cancelToken:r,timeout:p,onDownloadProgress:c,onUploadProgress:l,responseType:u,headers:d,withCredentials:m="same-origin",fetchOptions:f}=tT(e);u=u?(u+"").toLowerCase():"text";let h=tA([s,r&&r.toAbortSignal()],p),x=h&&h.unsubscribe&&(()=>{h.unsubscribe()});try{if(l&&tq&&"get"!==n&&"head"!==n&&0!==(a=await tI(d,o))){let e,t=new Request(i,{method:"POST",body:o,duplex:"half"});if(et.isFormData(o)&&(e=t.headers.get("content-type"))&&d.setContentType(e),t.body){let[e,i]=ts(a,to(tr(l)));o=tP(t.body,65536,e,i)}}et.isString(m)||(m=m?"include":"omit");let s="credentials"in Request.prototype;t=new Request(i,{...f,signal:h,method:n.toUpperCase(),headers:d.normalize().toJSON(),body:o,duplex:"half",credentials:s?m:void 0});let r=await fetch(t),p=tU&&("stream"===u||"response"===u);if(tU&&(c||p&&x)){let e={};["status","statusText","headers"].forEach(t=>{e[t]=r[t]});let t=et.toFiniteNumber(r.headers.get("content-length")),[a,i]=c&&ts(t,to(tr(c),!0))||[];r=new Response(tP(r.body,65536,a,()=>{i&&i(),x&&x()}),e)}u=u||"text";let v=await tB[et.findKey(tB,u)||"text"](r,e);return!p&&x&&x(),await new Promise((a,i)=>{eH(a,i,{data:v,headers:eB.from(r.headers),status:r.status,statusText:r.statusText,config:e,request:t})})}catch(a){if(x&&x(),a&&"TypeError"===a.name&&/fetch/i.test(a.message))throw Object.assign(new ea("Network Error",ea.ERR_NETWORK,e,t),{cause:a.cause||a});throw ea.from(a,a&&a.code,e,t)}})};et.forEach(tM,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(e){}Object.defineProperty(e,"adapterName",{value:t})}});let tH=e=>`- ${e}`,tG=e=>et.isFunction(e)||null===e||!1===e,t$={getAdapter:e=>{let t,a;let{length:i}=e=et.isArray(e)?e:[e],n={};for(let o=0;o<i;o++){let i;if(a=t=e[o],!tG(t)&&void 0===(a=tM[(i=String(t)).toLowerCase()]))throw new ea(`Unknown adapter '${i}'`);if(a)break;n[i||"#"+o]=a}if(!a){let e=Object.entries(n).map(([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build"));throw new ea("There is no suitable adapter to dispatch the request "+(i?e.length>1?"since :\n"+e.map(tH).join("\n"):" "+tH(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return a}};function tW(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new eM(null,e)}function tK(e){return tW(e),e.headers=eB.from(e.headers),e.data=eN.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),t$.getAdapter(e.adapter||eC.adapter)(e).then(function(t){return tW(e),t.data=eN.call(e,e.transformResponse,t),t.headers=eB.from(t.headers),t},function(t){return!eI(t)&&(tW(e),t&&t.response&&(t.response.data=eN.call(e,e.transformResponse,t.response),t.response.headers=eB.from(t.response.headers))),Promise.reject(t)})}let tV={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{tV[e]=function(a){return typeof a===e||"a"+(t<1?"n ":" ")+e}});let tJ={};tV.transitional=function(e,t,a){function i(e,t){return"[Axios v"+eQ+"] Transitional option '"+e+"'"+t+(a?". "+a:"")}return(a,n,o)=>{if(!1===e)throw new ea(i(n," has been removed"+(t?" in "+t:"")),ea.ERR_DEPRECATED);return t&&!tJ[n]&&(tJ[n]=!0,console.warn(i(n," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(a,n,o)}},tV.spelling=function(e){return(t,a)=>(console.warn(`${a} is likely a misspelling of ${e}`),!0)};let tY={assertOptions:function(e,t,a){if("object"!=typeof e)throw new ea("options must be an object",ea.ERR_BAD_OPTION_VALUE);let i=Object.keys(e),n=i.length;for(;n-- >0;){let o=i[n],s=t[o];if(s){let t=e[o],a=void 0===t||s(t,o,e);if(!0!==a)throw new ea("option "+o+" must be "+a,ea.ERR_BAD_OPTION_VALUE);continue}if(!0!==a)throw new ea("Unknown option "+o,ea.ERR_BAD_OPTION)}},validators:tV},tQ=tY.validators;class tX{constructor(e){this.defaults=e,this.interceptors={request:new ex,response:new ex}}async request(e,t){try{return await this._request(e,t)}catch(e){if(e instanceof Error){let t={};Error.captureStackTrace?Error.captureStackTrace(t):t=Error();let a=t.stack?t.stack.replace(/^.+\n/,""):"";try{e.stack?a&&!String(e.stack).endsWith(a.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+a):e.stack=a}catch(e){}}throw e}}_request(e,t){let a,i;"string"==typeof e?(t=t||{}).url=e:t=e||{};let{transitional:n,paramsSerializer:o,headers:s}=t=tj(this.defaults,t);void 0!==n&&tY.assertOptions(n,{silentJSONParsing:tQ.transitional(tQ.boolean),forcedJSONParsing:tQ.transitional(tQ.boolean),clarifyTimeoutError:tQ.transitional(tQ.boolean)},!1),null!=o&&(et.isFunction(o)?t.paramsSerializer={serialize:o}:tY.assertOptions(o,{encode:tQ.function,serialize:tQ.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),tY.assertOptions(t,{baseUrl:tQ.spelling("baseURL"),withXsrfToken:tQ.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let r=s&&et.merge(s.common,s[t.method]);s&&et.forEach(["delete","get","head","post","put","patch","common"],e=>{delete s[e]}),t.headers=eB.concat(r,s);let p=[],c=!0;this.interceptors.request.forEach(function(e){("function"!=typeof e.runWhen||!1!==e.runWhen(t))&&(c=c&&e.synchronous,p.unshift(e.fulfilled,e.rejected))});let l=[];this.interceptors.response.forEach(function(e){l.push(e.fulfilled,e.rejected)});let u=0;if(!c){let e=[tK.bind(this),void 0];for(e.unshift.apply(e,p),e.push.apply(e,l),i=e.length,a=Promise.resolve(t);u<i;)a=a.then(e[u++],e[u++]);return a}i=p.length;let d=t;for(u=0;u<i;){let e=p[u++],t=p[u++];try{d=e(d)}catch(e){t.call(this,e);break}}try{a=tK.call(this,d)}catch(e){return Promise.reject(e)}for(u=0,i=l.length;u<i;)a=a.then(l[u++],l[u++]);return a}getUri(e){return eh(eG((e=tj(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}et.forEach(["delete","get","head","options"],function(e){tX.prototype[e]=function(t,a){return this.request(tj(a||{},{method:e,url:t,data:(a||{}).data}))}}),et.forEach(["post","put","patch"],function(e){function t(t){return function(a,i,n){return this.request(tj(n||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:a,data:i}))}}tX.prototype[e]=t(),tX.prototype[e+"Form"]=t(!0)});class tZ{constructor(e){let t;if("function"!=typeof e)throw TypeError("executor must be a function.");this.promise=new Promise(function(e){t=e});let a=this;this.promise.then(e=>{if(!a._listeners)return;let t=a._listeners.length;for(;t-- >0;)a._listeners[t](e);a._listeners=null}),this.promise.then=e=>{let t;let i=new Promise(e=>{a.subscribe(e),t=e}).then(e);return i.cancel=function(){a.unsubscribe(t)},i},e(function(e,i,n){a.reason||(a.reason=new eM(e,i,n),t(a.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){let e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new tZ(function(t){e=t}),cancel:e}}}let t0={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(t0).forEach(([e,t])=>{t0[t]=e});let t1=function e(t){let a=new tX(t),i=y(tX.prototype.request,a);return et.extend(i,tX.prototype,a,{allOwnKeys:!0}),et.extend(i,a,null,{allOwnKeys:!0}),i.create=function(a){return e(tj(t,a))},i}(eC);t1.Axios=tX,t1.CanceledError=eM,t1.CancelToken=tZ,t1.isCancel=eI,t1.VERSION=eQ,t1.toFormData=el,t1.AxiosError=ea,t1.Cancel=t1.CanceledError,t1.all=function(e){return Promise.all(e)},t1.spread=function(e){return function(t){return e.apply(null,t)}},t1.isAxiosError=function(e){return et.isObject(e)&&!0===e.isAxiosError},t1.mergeConfig=tj,t1.AxiosHeaders=eB,t1.formToJSON=e=>eR(et.isHTMLForm(e)?new FormData(e):e),t1.getAdapter=t$.getAdapter,t1.HttpStatusCode=t0,t1.default=t1;var t2=a(28106),t3=a.n(t2);class t6{constructor(){let e=process.env.WP_REST_ENDPOINT||"";this.baseUrl=e.replace(/\/+$/,"").replace("/wp-admin","").replace("/wp-json",""),this.auth={username:process.env.WORDPRESS_DB_USER||"",password:process.env.WORDPRESS_DB_PASSWORD||""},this.wp=new(t3())({endpoint:`${this.baseUrl}/wp-json`,username:this.auth.username,password:this.auth.password})}getApiUrl(e){return`${this.baseUrl}/wp-json${e}`}async getImageURLById(e){try{return(await t1.get(this.getApiUrl(`/wp/v2/media/${e}`),{auth:this.auth,headers:{Accept:"application/json","Referrer-Policy":"unsafe-url"}})).data}catch(e){return null}}async getAllCategories(){try{let e=await t1.get(this.getApiUrl("/wp/v2/categories"),{auth:this.auth,headers:{Accept:"application/json","Referrer-Policy":"unsafe-url"},params:{per_page:100}});if(!e?.data)return console.error("No categories data in WordPress response"),[];return Array.isArray(e.data)?e.data:[]}catch(e){return t1.isAxiosError(e)&&console.error("WordPress Categories API Error:",{status:e.response?.status,url:e.config?.url}),[]}}async getAllPosts(){try{let e=await t1.get(this.getApiUrl("/wp/v2/posts"),{auth:this.auth,headers:{Accept:"application/json","Referrer-Policy":"unsafe-url"},params:{_fields:"id,slug,title,featured_media,date,author,categories,excerpt",per_page:100,status:"publish",orderby:"date",order:"desc"}});if(!e?.data)return console.error("No data in WordPress response"),[];let t=Array.isArray(e.data)?e.data:[];if(0===t.length)return console.error("No posts found in WordPress response"),[];return await Promise.all(t.map(async e=>{let t="";if(e.featured_media>0){let a=await this.getImageURLById(e.featured_media);t=a?.guid?.rendered||""}let a="Javian Picardo";if(e.author)try{let t=await t1.get(this.getApiUrl(`/wp/v2/users/${e.author}`),{auth:this.auth,headers:{Accept:"application/json","Referrer-Policy":"unsafe-url"}});t?.data?.name&&(a=t.data.name)}catch(e){console.error("Error fetching author:",e)}return{...e,image_url:t||"",author:a}}))}catch(e){return t1.isAxiosError(e)&&console.error("WordPress API Error:",{status:e.response?.status,url:e.config?.url}),[]}}async getPostsForSitemap(){try{let e=await t1.get(this.getApiUrl("/wp/v2/posts"),{auth:this.auth,headers:{Accept:"application/json"},params:{_fields:"slug,date",status:"publish"}});return Array.isArray(e.data)?e.data:[]}catch(e){return[]}}async getPost(e){try{let t=await t1.get(this.getApiUrl("/wp/v2/posts"),{auth:this.auth,headers:{Accept:"application/json","Referrer-Policy":"unsafe-url"},params:{slug:e,_embed:!0,status:"publish"}});if(!t.data?.length)return{title:{rendered:"Post not found"},content:{rendered:"The requested post could not be found."},excerpt:{rendered:"Post not found"},slug:e};let a=t.data[0],i="";if(a.featured_media>0){let e=await this.getImageURLById(a.featured_media);i=e?.guid?.rendered||""}let n="Javian Picardo";if(a.author)try{let e=await t1.get(this.getApiUrl(`/wp/v2/users/${a.author}`),{auth:this.auth,headers:{Accept:"application/json","Referrer-Policy":"unsafe-url"}});e?.data?.name&&(n=e.data.name)}catch(e){console.error("Error fetching author:",e)}return{...a,image_url:i||"",author:n}}catch(t){return{title:{rendered:"Post not found"},content:{rendered:"The requested post could not be found."},excerpt:{rendered:"Post not found"},slug:e}}}}let t4=new t6;async function t8(){let e="http://localhost:3000",t=(await t4.getPostsForSitemap().then(e=>e).catch(()=>[])).map(t=>({url:`${e}/blog/${t.slug}`,lastModified:new Date,changeFrequency:"yearly",priority:.4}));return[{url:e,lastModified:new Date,changeFrequency:"yearly",priority:1},{url:`${e}/privacy-policy`,lastModified:new Date,changeFrequency:"yearly",priority:.8},{url:`${e}/tos`,lastModified:new Date,changeFrequency:"yearly",priority:.7},{url:`${e}/blog`,lastModified:new Date,changeFrequency:"yearly",priority:.6},...t]}var t5=a(60707);let t7={...f},t9=t7.default,ae=t7.generateSitemaps;if("function"!=typeof t9)throw Error('Default export is missing in "C:\\Projects\\PersonalPortal\\src\\app\\sitemap.ts"');async function at(e,t){let a;let{__metadata_id__:i,...n}=t.params||{},o=ae?await ae():null;if(o&&null==(a=o.find(e=>{let t=e.id.toString();return(t+=".xml")===i})?.id))return new b.NextResponse("Not Found",{status:404});let s=await t9({id:a}),r=(0,t5.resolveRouteData)(s,"sitemap");return new b.NextResponse(r,{headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=0, must-revalidate"}})}let aa=new x.AppRouteRouteModule({definition:{kind:v.x.APP_ROUTE,page:"/sitemap.xml/route",pathname:"/sitemap.xml",filename:"sitemap",bundlePath:"app/sitemap.xml/route"},resolvedPagePath:"next-metadata-route-loader?page=%2Fsitemap.xml%2Froute&filePath=C%3A%5CProjects%5CPersonalPortal%5Csrc%5Capp%5Csitemap.ts&isDynamic=1!?__next_metadata_route__",nextConfigOutput:"",userland:h}),{requestAsyncStorage:ai,staticGenerationAsyncStorage:an,serverHooks:ao}=aa,as="/sitemap.xml/route";function ar(){return(0,g.patchFetch)({serverHooks:ao,staticGenerationAsyncStorage:an})}},37901:(e,t,a)=>{e.exports={parallel:a(90272),serial:a(45234),serialOrdered:a(53943)}},12687:e=>{e.exports=function(e){Object.keys(e.jobs).forEach(t.bind(e)),e.jobs={}};function t(e){"function"==typeof this.jobs[e]&&this.jobs[e]()}},3200:(e,t,a)=>{var i=a(19458);e.exports=function(e){var t=!1;return i(function(){t=!0}),function(a,n){t?e(a,n):i(function(){e(a,n)})}}},19458:e=>{e.exports=function(e){var t="function"==typeof setImmediate?setImmediate:"object"==typeof process&&"function"==typeof process.nextTick?process.nextTick:null;t?t(e):setTimeout(e,0)}},87466:(e,t,a)=>{var i=a(3200),n=a(12687);e.exports=function(e,t,a,o){var s,r,p=a.keyedList?a.keyedList[a.index]:a.index;a.jobs[p]=(s=e[p],r=function(e,t){p in a.jobs&&(delete a.jobs[p],e?n(a):a.results[p]=t,o(e,a.results))},2==t.length?t(s,i(r)):t(s,p,i(r)))}},85172:e=>{e.exports=function(e,t){var a=!Array.isArray(e),i={index:0,keyedList:a||t?Object.keys(e):null,jobs:{},results:a?{}:[],size:a?Object.keys(e).length:e.length};return t&&i.keyedList.sort(a?t:function(a,i){return t(e[a],e[i])}),i}},53545:(e,t,a)=>{var i=a(12687),n=a(3200);e.exports=function(e){Object.keys(this.jobs).length&&(this.index=this.size,i(this),n(e)(null,this.results))}},90272:(e,t,a)=>{var i=a(87466),n=a(85172),o=a(53545);e.exports=function(e,t,a){for(var s=n(e);s.index<(s.keyedList||e).length;)i(e,t,s,function(e,t){if(e){a(e,t);return}if(0===Object.keys(s.jobs).length){a(null,s.results);return}}),s.index++;return o.bind(s,a)}},45234:(e,t,a)=>{var i=a(53943);e.exports=function(e,t,a){return i(e,t,null,a)}},53943:(e,t,a)=>{var i=a(87466),n=a(85172),o=a(53545);function s(e,t){return e<t?-1:e>t?1:0}e.exports=function(e,t,a,s){var r=n(e,a);return i(e,t,r,function a(n,o){if(n){s(n,o);return}if(r.index++,r.index<(r.keyedList||e).length){i(e,t,r,a);return}s(null,r.results)}),o.bind(r,s)},e.exports.ascending=s,e.exports.descending=function(e,t){return -1*s(e,t)}},14093:(e,t,a)=>{"use strict";var i=a(1061),n=a(31145),o=a(29406),s=a(5823);e.exports=s||i.call(o,n)},85279:(e,t,a)=>{"use strict";var i=a(1061),n=a(31145),o=a(14093);e.exports=function(){return o(i,n,arguments)}},31145:e=>{"use strict";e.exports=Function.prototype.apply},29406:e=>{"use strict";e.exports=Function.prototype.call},76360:(e,t,a)=>{"use strict";var i=a(1061),n=a(26829),o=a(29406),s=a(14093);e.exports=function(e){if(e.length<1||"function"!=typeof e[0])throw new n("a function is required");return s(i,o,e)}},5823:e=>{"use strict";e.exports="function"==typeof Reflect&&Reflect.apply},71658:(e,t,a)=>{"use strict";var i=a(44868),n=a(43228),o=n(i("String.prototype.indexOf"));e.exports=function(e,t){var a=i(e,!!t);return"function"==typeof a&&o(e,".prototype.")>-1?n(a):a}},43228:(e,t,a)=>{"use strict";var i=a(547),n=a(36429),o=a(76360),s=a(85279);e.exports=function(e){var t=o(arguments),a=e.length-(arguments.length-1);return i(t,1+(a>0?a:0),!0)},n?n(e.exports,"apply",{value:s}):e.exports.apply=s},29227:(e,t,a)=>{var i=a(21764),n=a(76162).Stream,o=a(92647);function s(){this.writable=!1,this.readable=!0,this.dataSize=0,this.maxDataSize=2097152,this.pauseStreams=!0,this._released=!1,this._streams=[],this._currentStream=null,this._insideLoop=!1,this._pendingNext=!1}e.exports=s,i.inherits(s,n),s.create=function(e){var t=new this;for(var a in e=e||{})t[a]=e[a];return t},s.isStreamLike=function(e){return"function"!=typeof e&&"string"!=typeof e&&"boolean"!=typeof e&&"number"!=typeof e&&!Buffer.isBuffer(e)},s.prototype.append=function(e){if(s.isStreamLike(e)){if(!(e instanceof o)){var t=o.create(e,{maxDataSize:1/0,pauseStream:this.pauseStreams});e.on("data",this._checkDataSize.bind(this)),e=t}this._handleErrors(e),this.pauseStreams&&e.pause()}return this._streams.push(e),this},s.prototype.pipe=function(e,t){return n.prototype.pipe.call(this,e,t),this.resume(),e},s.prototype._getNext=function(){if(this._currentStream=null,this._insideLoop){this._pendingNext=!0;return}this._insideLoop=!0;try{do this._pendingNext=!1,this._realGetNext();while(this._pendingNext)}finally{this._insideLoop=!1}},s.prototype._realGetNext=function(){var e=this._streams.shift();if(void 0===e){this.end();return}if("function"!=typeof e){this._pipeNext(e);return}e((function(e){s.isStreamLike(e)&&(e.on("data",this._checkDataSize.bind(this)),this._handleErrors(e)),this._pipeNext(e)}).bind(this))},s.prototype._pipeNext=function(e){if(this._currentStream=e,s.isStreamLike(e)){e.on("end",this._getNext.bind(this)),e.pipe(this,{end:!1});return}this.write(e),this._getNext()},s.prototype._handleErrors=function(e){var t=this;e.on("error",function(e){t._emitError(e)})},s.prototype.write=function(e){this.emit("data",e)},s.prototype.pause=function(){this.pauseStreams&&(this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.pause&&this._currentStream.pause(),this.emit("pause"))},s.prototype.resume=function(){this._released||(this._released=!0,this.writable=!0,this._getNext()),this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.resume&&this._currentStream.resume(),this.emit("resume")},s.prototype.end=function(){this._reset(),this.emit("end")},s.prototype.destroy=function(){this._reset(),this.emit("close")},s.prototype._reset=function(){this.writable=!1,this._streams=[],this._currentStream=null},s.prototype._checkDataSize=function(){if(this._updateDataSize(),!(this.dataSize<=this.maxDataSize)){var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this._emitError(Error(e))}},s.prototype._updateDataSize=function(){this.dataSize=0;var e=this;this._streams.forEach(function(t){t.dataSize&&(e.dataSize+=t.dataSize)}),this._currentStream&&this._currentStream.dataSize&&(this.dataSize+=this._currentStream.dataSize)},s.prototype._emitError=function(e){this._reset(),this.emit("error",e)}},54591:(e,t)=>{!function(){"use strict";function e(t,a,i,n){return this instanceof e?(this.domain=t||void 0,this.path=a||"/",this.secure=!!i,this.script=!!n,this):new e(t,a,i,n)}function a(e,t,i){return e instanceof a?e:this instanceof a?(this.name=null,this.value=null,this.expiration_date=1/0,this.path=String(i||"/"),this.explicit_path=!1,this.domain=t||null,this.explicit_domain=!1,this.secure=!1,this.noscript=!1,e&&this.parse(e,t,i),this):new a(e,t,i)}e.All=Object.freeze(Object.create(null)),t.CookieAccessInfo=e,t.Cookie=a,a.prototype.toString=function(){var e=[this.name+"="+this.value];return this.expiration_date!==1/0&&e.push("expires="+new Date(this.expiration_date).toGMTString()),this.domain&&e.push("domain="+this.domain),this.path&&e.push("path="+this.path),this.secure&&e.push("secure"),this.noscript&&e.push("httponly"),e.join("; ")},a.prototype.toValueString=function(){return this.name+"="+this.value};var i=/[:](?=\s*[a-zA-Z0-9_\-]+\s*[=])/g;function n(){var e,t;return this instanceof n?(e=Object.create(null),this.setCookie=function(i,n,o){var s,r;if(s=(i=new a(i,n,o)).expiration_date<=Date.now(),void 0!==e[i.name]){for(r=0,t=e[i.name];r<t.length;r+=1)if(t[r].collidesWith(i)){if(s)return t.splice(r,1),0===t.length&&delete e[i.name],!1;return t[r]=i,i}return!s&&(t.push(i),i)}return!s&&(e[i.name]=[i],e[i.name])},this.getCookie=function(a,i){var n,o;if(t=e[a])for(o=0;o<t.length;o+=1){if((n=t[o]).expiration_date<=Date.now()){0===t.length&&delete e[n.name];continue}if(n.matches(i))return n}},this.getCookies=function(t){var a,i,n=[];for(a in e)(i=this.getCookie(a,t))&&n.push(i);return n.toString=function(){return n.join(":")},n.toValueString=function(){return n.map(function(e){return e.toValueString()}).join("; ")},n},this):new n}a.prototype.parse=function(e,t,i){if(this instanceof a){if(e.length>32768){console.warn("Cookie too long for parsing (>32768 characters)");return}var n,o=e.split(";").filter(function(e){return!!e}),s=o[0].match(/([^=]+)=([\s\S]*)/);if(!s){console.warn("Invalid cookie header encountered. Header: '"+e+"'");return}var r=s[1],p=s[2];if("string"!=typeof r||0===r.length||"string"!=typeof p){console.warn("Unable to extract values from cookie header. Cookie: '"+e+"'");return}for(n=1,this.name=r,this.value=p;n<o.length;n+=1)switch(r=(s=o[n].match(/([^=]+)(?:=([\s\S]*))?/))[1].trim().toLowerCase(),p=s[2],r){case"httponly":this.noscript=!0;break;case"expires":this.expiration_date=p?Number(Date.parse(p)):1/0;break;case"path":this.path=p?p.trim():"",this.explicit_path=!0;break;case"domain":this.domain=p?p.trim():"",this.explicit_domain=!!this.domain;break;case"secure":this.secure=!0}return this.explicit_path||(this.path=i||"/"),this.explicit_domain||(this.domain=t),this}return new a().parse(e,t,i)},a.prototype.matches=function(t){return t===e.All||(!this.noscript||!t.script)&&(!this.secure||!!t.secure)&&!!this.collidesWith(t)},a.prototype.collidesWith=function(e){if(this.path&&!e.path||this.domain&&!e.domain||this.path&&0!==e.path.indexOf(this.path)||this.explicit_path&&0!==e.path.indexOf(this.path))return!1;var t=e.domain&&e.domain.replace(/^[\.]/,""),a=this.domain&&this.domain.replace(/^[\.]/,"");if(a===t)return!0;if(a){if(!this.explicit_domain)return!1;var i=t.indexOf(a);if(-1===i||i!==t.length-a.length)return!1}return!0},t.CookieJar=n,n.prototype.setCookies=function(e,t,n){e=Array.isArray(e)?e:e.split(i);var o,s,r=[];for(o=0,e=e.map(function(e){return new a(e,t,n)});o<e.length;o+=1)s=e[o],this.setCookie(s,t,n)&&r.push(s);return r}}()},61166:(e,t,a)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let a="color: "+this.color;t.splice(1,0,a,"color: inherit");let i=0,n=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(i++,"%c"===e&&(n=i))}),t.splice(n,0,a)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=a(45344)(t);let{formatters:i}=e.exports;i.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},45344:(e,t,a)=>{e.exports=function(e){function t(e){let a,n,o;let s=null;function r(...e){if(!r.enabled)return;let i=Number(new Date),n=i-(a||i);r.diff=n,r.prev=a,r.curr=i,a=i,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let o=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(a,i)=>{if("%%"===a)return"%";o++;let n=t.formatters[i];if("function"==typeof n){let t=e[o];a=n.call(r,t),e.splice(o,1),o--}return a}),t.formatArgs.call(r,e),(r.log||t.log).apply(r,e)}return r.namespace=e,r.useColors=t.useColors(),r.color=t.selectColor(e),r.extend=i,r.destroy=t.destroy,Object.defineProperty(r,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(n!==t.namespaces&&(n=t.namespaces,o=t.enabled(e)),o),set:e=>{s=e}}),"function"==typeof t.init&&t.init(r),r}function i(e,a){let i=t(this.namespace+(void 0===a?":":a)+e);return i.log=this.log,i}function n(e,t){let a=0,i=0,n=-1,o=0;for(;a<e.length;)if(i<t.length&&(t[i]===e[a]||"*"===t[i]))"*"===t[i]?(n=i,o=a):a++,i++;else{if(-1===n)return!1;i=n+1,a=++o}for(;i<t.length&&"*"===t[i];)i++;return i===t.length}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names,...t.skips.map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){for(let a of(t.save(e),t.namespaces=e,t.names=[],t.skips=[],("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean)))"-"===a[0]?t.skips.push(a.slice(1)):t.names.push(a)},t.enabled=function(e){for(let a of t.skips)if(n(e,a))return!1;for(let a of t.names)if(n(e,a))return!0;return!1},t.humanize=a(54686),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(a=>{t[a]=e[a]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let a=0;for(let t=0;t<e.length;t++)a=(a<<5)-a+e.charCodeAt(t)|0;return t.colors[Math.abs(a)%t.colors.length]},t.enable(t.load()),t}},59273:(e,t,a)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=a(61166):e.exports=a(21025)},21025:(e,t,a)=>{let i=a(74175),n=a(21764);t.init=function(e){e.inspectOpts={};let a=Object.keys(t.inspectOpts);for(let i=0;i<a.length;i++)e.inspectOpts[a[i]]=t.inspectOpts[a[i]]},t.log=function(...e){return process.stderr.write(n.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(a){let{namespace:i,useColors:n}=this;if(n){let t=this.color,n="\x1b[3"+(t<8?t:"8;5;"+t),o=`  ${n};1m${i} \u001B[0m`;a[0]=o+a[0].split("\n").join("\n"+o),a.push(n+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else a[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+i+" "+a[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:i.isatty(process.stderr.fd)},t.destroy=n.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=a(95749);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let a=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),i=process.env[t];return i=!!/^(yes|on|true|enabled)$/i.test(i)||!/^(no|off|false|disabled)$/i.test(i)&&("null"===i?null:Number(i)),e[a]=i,e},{}),e.exports=a(45344)(t);let{formatters:o}=e.exports;o.o=function(e){return this.inspectOpts.colors=this.useColors,n.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},o.O=function(e){return this.inspectOpts.colors=this.useColors,n.inspect(e,this.inspectOpts)}},85595:(e,t,a)=>{"use strict";var i=a(36429),n=a(97756),o=a(26829),s=a(14237);e.exports=function(e,t,a){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new o("`obj` must be an object or a function`");if("string"!=typeof t&&"symbol"!=typeof t)throw new o("`property` must be a string or a symbol`");if(arguments.length>3&&"boolean"!=typeof arguments[3]&&null!==arguments[3])throw new o("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&"boolean"!=typeof arguments[4]&&null!==arguments[4])throw new o("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&"boolean"!=typeof arguments[5]&&null!==arguments[5])throw new o("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&"boolean"!=typeof arguments[6])throw new o("`loose`, if provided, must be a boolean");var r=arguments.length>3?arguments[3]:null,p=arguments.length>4?arguments[4]:null,c=arguments.length>5?arguments[5]:null,l=arguments.length>6&&arguments[6],u=!!s&&s(e,t);if(i)i(e,t,{configurable:null===c&&u?u.configurable:!c,enumerable:null===r&&u?u.enumerable:!r,value:a,writable:null===p&&u?u.writable:!p});else if(!l&&(r||p||c))throw new n("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.");else e[t]=a}},92647:(e,t,a)=>{var i=a(76162).Stream,n=a(21764);function o(){this.source=null,this.dataSize=0,this.maxDataSize=1048576,this.pauseStream=!0,this._maxDataSizeExceeded=!1,this._released=!1,this._bufferedEvents=[]}e.exports=o,n.inherits(o,i),o.create=function(e,t){var a=new this;for(var i in t=t||{})a[i]=t[i];a.source=e;var n=e.emit;return e.emit=function(){return a._handleEmit(arguments),n.apply(e,arguments)},e.on("error",function(){}),a.pauseStream&&e.pause(),a},Object.defineProperty(o.prototype,"readable",{configurable:!0,enumerable:!0,get:function(){return this.source.readable}}),o.prototype.setEncoding=function(){return this.source.setEncoding.apply(this.source,arguments)},o.prototype.resume=function(){this._released||this.release(),this.source.resume()},o.prototype.pause=function(){this.source.pause()},o.prototype.release=function(){this._released=!0,this._bufferedEvents.forEach((function(e){this.emit.apply(this,e)}).bind(this)),this._bufferedEvents=[]},o.prototype.pipe=function(){var e=i.prototype.pipe.apply(this,arguments);return this.resume(),e},o.prototype._handleEmit=function(e){if(this._released){this.emit.apply(this,e);return}"data"===e[0]&&(this.dataSize+=e[1].length,this._checkIfMaxDataSizeExceeded()),this._bufferedEvents.push(e)},o.prototype._checkIfMaxDataSizeExceeded=function(){if(!this._maxDataSizeExceeded&&!(this.dataSize<=this.maxDataSize)){this._maxDataSizeExceeded=!0;var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this.emit("error",Error(e))}}},36429:(e,t,a)=>{"use strict";var i=a(44868)("%Object.defineProperty%",!0)||!1;if(i)try{i({},"a",{value:1})}catch(e){i=!1}e.exports=i},18850:e=>{"use strict";e.exports=EvalError},2065:e=>{"use strict";e.exports=Error},43302:e=>{"use strict";e.exports=RangeError},45200:e=>{"use strict";e.exports=ReferenceError},97756:e=>{"use strict";e.exports=SyntaxError},26829:e=>{"use strict";e.exports=TypeError},64830:e=>{"use strict";e.exports=URIError},52005:(e,t,a)=>{var i;e.exports=function(){if(!i){try{i=a(59273)("follow-redirects")}catch(e){}"function"!=typeof i&&(i=function(){})}i.apply(null,arguments)}},78179:(e,t,a)=>{var i=a(17360),n=i.URL,o=a(32615),s=a(35240),r=a(76162).Writable,p=a(27790),c=a(52005);!function(){var e="undefined"!=typeof process,t="undefined"!=typeof window&&"undefined"!=typeof document,a=R(Error.captureStackTrace);e||!t&&a||console.warn("The follow-redirects package should be excluded from browser builds.")}();var l=!1;try{p(new n(""))}catch(e){l="ERR_INVALID_URL"===e.code}var u=["auth","host","hostname","href","path","pathname","port","protocol","query","search","hash"],d=["abort","aborted","connect","error","socket","timeout"],m=Object.create(null);d.forEach(function(e){m[e]=function(t,a,i){this._redirectable.emit(e,t,a,i)}});var f=S("ERR_INVALID_URL","Invalid URL",TypeError),h=S("ERR_FR_REDIRECTION_FAILURE","Redirected request failed"),x=S("ERR_FR_TOO_MANY_REDIRECTS","Maximum number of redirects exceeded",h),v=S("ERR_FR_MAX_BODY_LENGTH_EXCEEDED","Request body larger than maxBodyLength limit"),g=S("ERR_STREAM_WRITE_AFTER_END","write after end"),b=r.prototype.destroy||_;function y(e,t){r.call(this),this._sanitizeOptions(e),this._options=e,this._ended=!1,this._ending=!1,this._redirectCount=0,this._redirects=[],this._requestBodyLength=0,this._requestBodyBuffers=[],t&&this.on("response",t);var a=this;this._onNativeResponse=function(e){try{a._processResponse(e)}catch(e){a.emit("error",e instanceof h?e:new h({cause:e}))}},this._performRequest()}function w(e){var t={maxRedirects:21,maxBodyLength:10485760},a={};return Object.keys(e).forEach(function(i){var o=i+":",s=a[o]=e[i],r=t[i]=Object.create(s);Object.defineProperties(r,{request:{value:function(e,i,s){var r;return(r=e,n&&r instanceof n)?e=j(e):O(e)?e=j(k(e)):(s=i,i=E(e),e={protocol:o}),R(i)&&(s=i,i=null),(i=Object.assign({maxRedirects:t.maxRedirects,maxBodyLength:t.maxBodyLength},e,i)).nativeProtocols=a,O(i.host)||O(i.hostname)||(i.hostname="::1"),p.equal(i.protocol,o,"protocol mismatch"),c("options",i),new y(i,s)},configurable:!0,enumerable:!0,writable:!0},get:{value:function(e,t,a){var i=r.request(e,t,a);return i.end(),i},configurable:!0,enumerable:!0,writable:!0}})}),t}function _(){}function k(e){var t;if(l)t=new n(e);else if(!O((t=E(i.parse(e))).protocol))throw new f({input:e});return t}function E(e){if(/^\[/.test(e.hostname)&&!/^\[[:0-9a-f]+\]$/i.test(e.hostname)||/^\[/.test(e.host)&&!/^\[[:0-9a-f]+\](:\d+)?$/i.test(e.host))throw new f({input:e.href||e});return e}function j(e,t){var a=t||{};for(var i of u)a[i]=e[i];return a.hostname.startsWith("[")&&(a.hostname=a.hostname.slice(1,-1)),""!==a.port&&(a.port=Number(a.port)),a.path=a.search?a.pathname+a.search:a.pathname,a}function T(e,t){var a;for(var i in t)e.test(i)&&(a=t[i],delete t[i]);return null==a?void 0:String(a).trim()}function S(e,t,a){function i(a){R(Error.captureStackTrace)&&Error.captureStackTrace(this,this.constructor),Object.assign(this,a||{}),this.code=e,this.message=this.cause?t+": "+this.cause.message:t}return i.prototype=new(a||Error),Object.defineProperties(i.prototype,{constructor:{value:i,enumerable:!1},name:{value:"Error ["+e+"]",enumerable:!1}}),i}function A(e,t){for(var a of d)e.removeListener(a,m[a]);e.on("error",_),e.destroy(t)}function O(e){return"string"==typeof e||e instanceof String}function R(e){return"function"==typeof e}y.prototype=Object.create(r.prototype),y.prototype.abort=function(){A(this._currentRequest),this._currentRequest.abort(),this.emit("abort")},y.prototype.destroy=function(e){return A(this._currentRequest,e),b.call(this,e),this},y.prototype.write=function(e,t,a){if(this._ending)throw new g;if(!O(e)&&!("object"==typeof e&&"length"in e))throw TypeError("data should be a string, Buffer or Uint8Array");if(R(t)&&(a=t,t=null),0===e.length){a&&a();return}this._requestBodyLength+e.length<=this._options.maxBodyLength?(this._requestBodyLength+=e.length,this._requestBodyBuffers.push({data:e,encoding:t}),this._currentRequest.write(e,t,a)):(this.emit("error",new v),this.abort())},y.prototype.end=function(e,t,a){if(R(e)?(a=e,e=t=null):R(t)&&(a=t,t=null),e){var i=this,n=this._currentRequest;this.write(e,t,function(){i._ended=!0,n.end(null,null,a)}),this._ending=!0}else this._ended=this._ending=!0,this._currentRequest.end(null,null,a)},y.prototype.setHeader=function(e,t){this._options.headers[e]=t,this._currentRequest.setHeader(e,t)},y.prototype.removeHeader=function(e){delete this._options.headers[e],this._currentRequest.removeHeader(e)},y.prototype.setTimeout=function(e,t){var a=this;function i(t){t.setTimeout(e),t.removeListener("timeout",t.destroy),t.addListener("timeout",t.destroy)}function n(t){a._timeout&&clearTimeout(a._timeout),a._timeout=setTimeout(function(){a.emit("timeout"),o()},e),i(t)}function o(){a._timeout&&(clearTimeout(a._timeout),a._timeout=null),a.removeListener("abort",o),a.removeListener("error",o),a.removeListener("response",o),a.removeListener("close",o),t&&a.removeListener("timeout",t),a.socket||a._currentRequest.removeListener("socket",n)}return t&&this.on("timeout",t),this.socket?n(this.socket):this._currentRequest.once("socket",n),this.on("socket",i),this.on("abort",o),this.on("error",o),this.on("response",o),this.on("close",o),this},["flushHeaders","getHeader","setNoDelay","setSocketKeepAlive"].forEach(function(e){y.prototype[e]=function(t,a){return this._currentRequest[e](t,a)}}),["aborted","connection","socket"].forEach(function(e){Object.defineProperty(y.prototype,e,{get:function(){return this._currentRequest[e]}})}),y.prototype._sanitizeOptions=function(e){if(e.headers||(e.headers={}),e.host&&(e.hostname||(e.hostname=e.host),delete e.host),!e.pathname&&e.path){var t=e.path.indexOf("?");t<0?e.pathname=e.path:(e.pathname=e.path.substring(0,t),e.search=e.path.substring(t))}},y.prototype._performRequest=function(){var e=this._options.protocol,t=this._options.nativeProtocols[e];if(!t)throw TypeError("Unsupported protocol "+e);if(this._options.agents){var a=e.slice(0,-1);this._options.agent=this._options.agents[a]}var n=this._currentRequest=t.request(this._options,this._onNativeResponse);for(var o of(n._redirectable=this,d))n.on(o,m[o]);if(this._currentUrl=/^\//.test(this._options.path)?i.format(this._options):this._options.path,this._isRedirect){var s=0,r=this,p=this._requestBodyBuffers;!function e(t){if(n===r._currentRequest){if(t)r.emit("error",t);else if(s<p.length){var a=p[s++];n.finished||n.write(a.data,a.encoding,e)}else r._ended&&n.end()}}()}},y.prototype._processResponse=function(e){var t,a,o,s=e.statusCode;this._options.trackRedirects&&this._redirects.push({url:this._currentUrl,headers:e.headers,statusCode:s});var r=e.headers.location;if(!r||!1===this._options.followRedirects||s<300||s>=400){e.responseUrl=this._currentUrl,e.redirects=this._redirects,this.emit("response",e),this._requestBodyBuffers=[];return}if(A(this._currentRequest),e.destroy(),++this._redirectCount>this._options.maxRedirects)throw new x;var u=this._options.beforeRedirect;u&&(o=Object.assign({Host:e.req.getHeader("host")},this._options.headers));var d=this._options.method;(301!==s&&302!==s||"POST"!==this._options.method)&&(303!==s||/^(?:GET|HEAD)$/.test(this._options.method))||(this._options.method="GET",this._requestBodyBuffers=[],T(/^content-/i,this._options.headers));var m=T(/^host$/i,this._options.headers),f=k(this._currentUrl),h=m||f.host,v=/^\w+:/.test(r)?this._currentUrl:i.format(Object.assign(f,{host:h})),g=l?new n(r,v):k(i.resolve(v,r));if(c("redirecting to",g.href),this._isRedirect=!0,j(g,this._options),(g.protocol===f.protocol||"https:"===g.protocol)&&(g.host===h||(p(O(t=g.host)&&O(h)),(a=t.length-h.length-1)>0&&"."===t[a]&&t.endsWith(h)))||T(/^(?:(?:proxy-)?authorization|cookie)$/i,this._options.headers),R(u)){var b={headers:e.headers,statusCode:s},y={url:v,method:d,headers:o};u(this._options,b,y),this._sanitizeOptions(this._options)}this._performRequest()},e.exports=w({http:o,https:s}),e.exports.wrap=w},21216:(e,t,a)=>{var i=a(29227),n=a(21764),o=a(55315),s=a(32615),r=a(35240),p=a(17360).parse,c=a(92048),l=a(76162).Stream,u=a(33320),d=a(37901),m=a(91422);function f(e){if(!(this instanceof f))return new f(e);for(var t in this._overheadLength=0,this._valueLength=0,this._valuesToMeasure=[],i.call(this),e=e||{})this[t]=e[t]}e.exports=f,n.inherits(f,i),f.LINE_BREAK="\r\n",f.DEFAULT_CONTENT_TYPE="application/octet-stream",f.prototype.append=function(e,t,a){"string"==typeof(a=a||{})&&(a={filename:a});var n=i.prototype.append.bind(this);if("number"==typeof t&&(t=""+t),Array.isArray(t)){this._error(Error("Arrays are not supported."));return}var o=this._multiPartHeader(e,t,a),s=this._multiPartFooter();n(o),n(t),n(s),this._trackLength(o,t,a)},f.prototype._trackLength=function(e,t,a){var i=0;null!=a.knownLength?i+=+a.knownLength:Buffer.isBuffer(t)?i=t.length:"string"==typeof t&&(i=Buffer.byteLength(t)),this._valueLength+=i,this._overheadLength+=Buffer.byteLength(e)+f.LINE_BREAK.length,t&&(t.path||t.readable&&t.hasOwnProperty("httpVersion")||t instanceof l)&&(a.knownLength||this._valuesToMeasure.push(t))},f.prototype._lengthRetriever=function(e,t){e.hasOwnProperty("fd")?void 0!=e.end&&e.end!=1/0&&void 0!=e.start?t(null,e.end+1-(e.start?e.start:0)):c.stat(e.path,function(a,i){if(a){t(a);return}t(null,i.size-(e.start?e.start:0))}):e.hasOwnProperty("httpVersion")?t(null,+e.headers["content-length"]):e.hasOwnProperty("httpModule")?(e.on("response",function(a){e.pause(),t(null,+a.headers["content-length"])}),e.resume()):t("Unknown stream")},f.prototype._multiPartHeader=function(e,t,a){if("string"==typeof a.header)return a.header;var i,n=this._getContentDisposition(t,a),o=this._getContentType(t,a),s="",r={"Content-Disposition":["form-data",'name="'+e+'"'].concat(n||[]),"Content-Type":[].concat(o||[])};for(var p in"object"==typeof a.header&&m(r,a.header),r)if(r.hasOwnProperty(p)){if(null==(i=r[p]))continue;Array.isArray(i)||(i=[i]),i.length&&(s+=p+": "+i.join("; ")+f.LINE_BREAK)}return"--"+this.getBoundary()+f.LINE_BREAK+s+f.LINE_BREAK},f.prototype._getContentDisposition=function(e,t){var a,i;return"string"==typeof t.filepath?a=o.normalize(t.filepath).replace(/\\/g,"/"):t.filename||e.name||e.path?a=o.basename(t.filename||e.name||e.path):e.readable&&e.hasOwnProperty("httpVersion")&&(a=o.basename(e.client._httpMessage.path||"")),a&&(i='filename="'+a+'"'),i},f.prototype._getContentType=function(e,t){var a=t.contentType;return!a&&e.name&&(a=u.lookup(e.name)),!a&&e.path&&(a=u.lookup(e.path)),!a&&e.readable&&e.hasOwnProperty("httpVersion")&&(a=e.headers["content-type"]),!a&&(t.filepath||t.filename)&&(a=u.lookup(t.filepath||t.filename)),a||"object"!=typeof e||(a=f.DEFAULT_CONTENT_TYPE),a},f.prototype._multiPartFooter=function(){return(function(e){var t=f.LINE_BREAK;0===this._streams.length&&(t+=this._lastBoundary()),e(t)}).bind(this)},f.prototype._lastBoundary=function(){return"--"+this.getBoundary()+"--"+f.LINE_BREAK},f.prototype.getHeaders=function(e){var t,a={"content-type":"multipart/form-data; boundary="+this.getBoundary()};for(t in e)e.hasOwnProperty(t)&&(a[t.toLowerCase()]=e[t]);return a},f.prototype.setBoundary=function(e){this._boundary=e},f.prototype.getBoundary=function(){return this._boundary||this._generateBoundary(),this._boundary},f.prototype.getBuffer=function(){for(var e=new Buffer.alloc(0),t=this.getBoundary(),a=0,i=this._streams.length;a<i;a++)"function"!=typeof this._streams[a]&&(Buffer.isBuffer(this._streams[a])?e=Buffer.concat([e,this._streams[a]]):e=Buffer.concat([e,Buffer.from(this._streams[a])]),("string"!=typeof this._streams[a]||this._streams[a].substring(2,t.length+2)!==t)&&(e=Buffer.concat([e,Buffer.from(f.LINE_BREAK)])));return Buffer.concat([e,Buffer.from(this._lastBoundary())])},f.prototype._generateBoundary=function(){for(var e="--------------------------",t=0;t<24;t++)e+=Math.floor(10*Math.random()).toString(16);this._boundary=e},f.prototype.getLengthSync=function(){var e=this._overheadLength+this._valueLength;return this._streams.length&&(e+=this._lastBoundary().length),this.hasKnownLength()||this._error(Error("Cannot calculate proper length in synchronous way.")),e},f.prototype.hasKnownLength=function(){var e=!0;return this._valuesToMeasure.length&&(e=!1),e},f.prototype.getLength=function(e){var t=this._overheadLength+this._valueLength;if(this._streams.length&&(t+=this._lastBoundary().length),!this._valuesToMeasure.length){process.nextTick(e.bind(this,null,t));return}d.parallel(this._valuesToMeasure,this._lengthRetriever,function(a,i){if(a){e(a);return}i.forEach(function(e){t+=e}),e(null,t)})},f.prototype.submit=function(e,t){var a,i,n={method:"post"};return"string"==typeof e?i=m({port:(e=p(e)).port,path:e.pathname,host:e.hostname,protocol:e.protocol},n):(i=m(e,n)).port||(i.port="https:"==i.protocol?443:80),i.headers=this.getHeaders(e.headers),a="https:"==i.protocol?r.request(i):s.request(i),this.getLength((function(e,i){if(e&&"Unknown stream"!==e){this._error(e);return}if(i&&a.setHeader("Content-Length",i),this.pipe(a),t){var n,o=function(e,i){return a.removeListener("error",o),a.removeListener("response",n),t.call(this,e,i)};n=o.bind(this,null),a.on("error",o),a.on("response",n)}}).bind(this)),a},f.prototype._error=function(e){this.error||(this.error=e,this.pause(),this.emit("error",e))},f.prototype.toString=function(){return"[object FormData]"}},91422:e=>{e.exports=function(e,t){return Object.keys(t).forEach(function(a){e[a]=e[a]||t[a]}),e}},89360:(e,t,a)=>{var i=a(21764),n=a(92048),o=a(17702).EventEmitter,s=a(84770);function r(e){for(var t in o.call(this),this.size=0,this.path=null,this.name=null,this.type=null,this.hash=null,this.lastModifiedDate=null,this._writeStream=null,e)this[t]=e[t];"string"==typeof this.hash?this.hash=s.createHash(e.hash):this.hash=null}e.exports=r,i.inherits(r,o),r.prototype.open=function(){this._writeStream=new n.WriteStream(this.path)},r.prototype.toJSON=function(){var e={size:this.size,path:this.path,name:this.name,type:this.type,mtime:this.lastModifiedDate,length:this.length,filename:this.filename,mime:this.mime};return this.hash&&""!=this.hash&&(e.hash=this.hash),e},r.prototype.write=function(e,t){var a=this;if(a.hash&&a.hash.update(e),this._writeStream.closed)return t();this._writeStream.write(e,function(){a.lastModifiedDate=new Date,a.size+=e.length,a.emit("progress",a.size),t()})},r.prototype.end=function(e){var t=this;t.hash&&(t.hash=t.hash.digest("hex")),this._writeStream.end(function(){t.emit("end"),e()})}},99126:(e,t,a)=>{var i=a(84770),n=a(92048),o=a(21764),s=a(55315),r=a(89360),p=a(22043).MultipartParser,c=a(90291).l,l=a(88179).h,u=a(99731).c,d=a(74026).StringDecoder,m=a(17702).EventEmitter,f=a(76162).Stream,h=a(19801);function x(e){return this instanceof x?(m.call(this),e=e||{},this.error=null,this.ended=!1,this.maxFields=e.maxFields||1e3,this.maxFieldsSize=e.maxFieldsSize||20971520,this.maxFileSize=e.maxFileSize||209715200,this.keepExtensions=e.keepExtensions||!1,this.uploadDir=e.uploadDir||h.tmpdir&&h.tmpdir()||h.tmpDir(),this.encoding=e.encoding||"utf-8",this.headers=null,this.type=null,this.hash=e.hash||!1,this.multiples=e.multiples||!1,this.bytesReceived=null,this.bytesExpected=null,this._parser=null,this._flushing=0,this._fieldsSize=0,this._fileSize=0,this.openedFiles=[],this):new x(e)}o.inherits(x,m),t.c=x,x.prototype.parse=function(e,t){if(this.pause=function(){try{e.pause()}catch(e){return this.ended||this._error(e),!1}return!0},this.resume=function(){try{e.resume()}catch(e){return this.ended||this._error(e),!1}return!0},t){var a={},i={};this.on("field",function(e,t){a[e]=t}).on("file",function(e,t){this.multiples&&i[e]?(Array.isArray(i[e])||(i[e]=[i[e]]),i[e].push(t)):i[e]=t}).on("error",function(e){t(e,a,i)}).on("end",function(){t(null,a,i)})}this.writeHeaders(e.headers);var n=this;return e.on("error",function(e){n._error(e)}).on("aborted",function(){n.emit("aborted"),n._error(Error("Request aborted"))}).on("data",function(e){n.write(e)}).on("end",function(){if(!n.error){var e=n._parser.end();e&&n._error(e)}}),this},x.prototype.writeHeaders=function(e){this.headers=e,this._parseContentLength(),this._parseContentType()},x.prototype.write=function(e){if(!this.error){if(!this._parser){this._error(Error("uninitialized parser"));return}if("function"!=typeof this._parser.write){this._error(Error("did not expect data"));return}this.bytesReceived+=e.length,this.emit("progress",this.bytesReceived,this.bytesExpected);var t=this._parser.write(e);return t!==e.length&&this._error(Error("parser error, "+t+" of "+e.length+" bytes parsed")),t}},x.prototype.pause=function(){return!1},x.prototype.resume=function(){return!1},x.prototype.onPart=function(e){this.handlePart(e)},x.prototype.handlePart=function(e){var t=this;if(void 0===e.filename){var a="",i=new d(this.encoding);e.on("data",function(e){if(t._fieldsSize+=e.length,t._fieldsSize>t.maxFieldsSize){t._error(Error("maxFieldsSize exceeded, received "+t._fieldsSize+" bytes of field data"));return}a+=i.write(e)}),e.on("end",function(){t.emit("field",e.name,a)});return}this._flushing++;var n=new r({path:this._uploadPath(e.filename),name:e.filename,type:e.mime,hash:t.hash});this.emit("fileBegin",e.name,n),n.open(),this.openedFiles.push(n),e.on("data",function(e){if(t._fileSize+=e.length,t._fileSize>t.maxFileSize){t._error(Error("maxFileSize exceeded, received "+t._fileSize+" bytes of file data"));return}0!=e.length&&(t.pause(),n.write(e,function(){t.resume()}))}),e.on("end",function(){n.end(function(){t._flushing--,t.emit("file",e.name,n),t._maybeEnd()})})},x.prototype._parseContentType=function(){if(0===this.bytesExpected){var e;this._parser=(e=this,{end:function(){return e.ended=!0,e._maybeEnd(),null}});return}if(!this.headers["content-type"]){this._error(Error("bad content-type header, no content-type"));return}if(this.headers["content-type"].match(/octet-stream/i)){this._initOctetStream();return}if(this.headers["content-type"].match(/urlencoded/i)){this._initUrlencoded();return}if(this.headers["content-type"].match(/multipart/i)){var t=this.headers["content-type"].match(/boundary=(?:"([^"]+)"|([^;]+))/i);t?this._initMultipart(t[1]||t[2]):this._error(Error("bad content-type header, no multipart boundary"));return}if(this.headers["content-type"].match(/json/i)){this._initJSONencoded();return}this._error(Error("bad content-type header, unknown content-type: "+this.headers["content-type"]))},x.prototype._error=function(e){this.error||this.ended||(this.error=e,this.emit("error",e),Array.isArray(this.openedFiles)&&this.openedFiles.forEach(function(e){e._writeStream.on("error",function(){}).destroy(),setTimeout(n.unlink,0,e.path,function(e){})}))},x.prototype._parseContentLength=function(){this.bytesReceived=0,this.headers["content-length"]?this.bytesExpected=parseInt(this.headers["content-length"],10):void 0===this.headers["transfer-encoding"]&&(this.bytesExpected=0),null!==this.bytesExpected&&this.emit("progress",this.bytesReceived,this.bytesExpected)},x.prototype._newParser=function(){return new p},x.prototype._initMultipart=function(e){this.type="multipart";var t,a,i,n=new p,o=this;n.initWithBoundary(e),n.onPartBegin=function(){(i=new f).readable=!0,i.headers={},i.name=null,i.filename=null,i.mime=null,i.transferEncoding="binary",i.transferBuffer="",t="",a=""},n.onHeaderField=function(e,a,i){t+=e.toString(o.encoding,a,i)},n.onHeaderValue=function(e,t,i){a+=e.toString(o.encoding,t,i)},n.onHeaderEnd=function(){t=t.toLowerCase(),i.headers[t]=a;var e=a.match(/\bname=("([^"]*)"|([^\(\)<>@,;:\\"\/\[\]\?=\{\}\s\t/]+))/i);"content-disposition"==t?(e&&(i.name=e[2]||e[3]||""),i.filename=o._fileName(a)):"content-type"==t?i.mime=a:"content-transfer-encoding"==t&&(i.transferEncoding=a.toLowerCase()),t="",a=""},n.onHeadersEnd=function(){switch(i.transferEncoding){case"binary":case"7bit":case"8bit":n.onPartData=function(e,t,a){i.emit("data",e.slice(t,a))},n.onPartEnd=function(){i.emit("end")};break;case"base64":n.onPartData=function(e,t,a){i.transferBuffer+=e.slice(t,a).toString("ascii");var n=4*parseInt(i.transferBuffer.length/4,10);i.emit("data",new Buffer(i.transferBuffer.substring(0,n),"base64")),i.transferBuffer=i.transferBuffer.substring(n)},n.onPartEnd=function(){i.emit("data",new Buffer(i.transferBuffer,"base64")),i.emit("end")};break;default:return o._error(Error("unknown transfer-encoding"))}o.onPart(i)},n.onEnd=function(){o.ended=!0,o._maybeEnd()},this._parser=n},x.prototype._fileName=function(e){var t=e.match(/\bfilename=("(.*?)"|([^\(\)<>@,;:\\"\/\[\]\?=\{\}\s\t/]+))($|;\s)/i);if(t){var a=t[2]||t[3]||"",i=a.substr(a.lastIndexOf("\\")+1);return(i=i.replace(/%22/g,'"')).replace(/&#([\d]{4});/g,function(e,t){return String.fromCharCode(t)})}},x.prototype._initUrlencoded=function(){this.type="urlencoded";var e=new c(this.maxFields),t=this;e.onField=function(e,a){t.emit("field",e,a)},e.onEnd=function(){t.ended=!0,t._maybeEnd()},this._parser=e},x.prototype._initOctetStream=function(){this.type="octet-stream";var e=this.headers["x-file-name"],t=this.headers["content-type"],a=new r({path:this._uploadPath(e),name:e,type:t});this.emit("fileBegin",e,a),a.open(),this.openedFiles.push(a),this._flushing++;var i=this;i._parser=new l;var n=0;i._parser.on("data",function(e){i.pause(),n++,a.write(e,function(){n--,i.resume(),i.ended&&i._parser.emit("doneWritingFile")})}),i._parser.on("end",function(){i._flushing--,i.ended=!0;var e=function(){a.end(function(){i.emit("file","file",a),i._maybeEnd()})};0===n?e():i._parser.once("doneWritingFile",e)})},x.prototype._initJSONencoded=function(){this.type="json";var e=new u(this),t=this;e.onField=function(e,a){t.emit("field",e,a)},e.onEnd=function(){t.ended=!0,t._maybeEnd()},this._parser=e},x.prototype._uploadPath=function(e){var t="upload_"+i.randomBytes(16).toString("hex");if(this.keepExtensions){var a=s.extname(e);t+=a=a.replace(/(\.[a-z0-9]+).*/i,"$1")}return s.join(this.uploadDir,t)},x.prototype._maybeEnd=function(){!this.ended||this._flushing||this.error||this.emit("end")}},43684:(e,t,a)=>{var i=a(99126).c;i.IncomingForm=i,e.exports=i},99731:(e,t,a)=>{var i=a(78893).Buffer;function n(e){this.parent=e,this.chunks=[],this.bytesWritten=0}t.c=n,n.prototype.write=function(e){return this.bytesWritten+=e.length,this.chunks.push(e),e.length},n.prototype.end=function(){try{var e=JSON.parse(i.concat(this.chunks));for(var t in e)this.onField(t,e[t])}catch(e){this.parent.emit("error",e)}this.data=null,this.onEnd()}},22043:(e,t,a)=>{var i=a(78893).Buffer,n=0,o={PARSER_UNINITIALIZED:n++,START:n++,START_BOUNDARY:n++,HEADER_FIELD_START:n++,HEADER_FIELD:n++,HEADER_VALUE_START:n++,HEADER_VALUE:n++,HEADER_VALUE_ALMOST_DONE:n++,HEADERS_ALMOST_DONE:n++,PART_DATA_START:n++,PART_DATA:n++,PART_END:n++,END:n++},s={PART_BOUNDARY:1,LAST_BOUNDARY:2};for(n in o)t[n]=o[n];function r(){this.boundary=null,this.boundaryChars=null,this.lookbehind=null,this.state=o.PARSER_UNINITIALIZED,this.index=null,this.flags=0}t.MultipartParser=r,r.stateToString=function(e){for(var t in o)if(o[t]===e)return t},r.prototype.initWithBoundary=function(e){this.boundary=new i(e.length+4),this.boundary.write("\r\n--",0),this.boundary.write(e,4),this.lookbehind=new i(this.boundary.length+8),this.state=o.START,this.boundaryChars={};for(var t=0;t<this.boundary.length;t++)this.boundaryChars[this.boundary[t]]=!0},r.prototype.write=function(e){var t,a,i=this,n=0,r=e.length,p=this.index,c=this.index,l=this.state,u=this.flags,d=this.lookbehind,m=this.boundary,f=this.boundaryChars,h=this.boundary.length,x=h-1,v=e.length,g=function(e){i[e+"Mark"]=n},b=function(e,t,a,n){if(void 0===a||a!==n){var o="on"+e.substr(0,1).toUpperCase()+e.substr(1);o in i&&i[o](t,a,n)}},y=function(t,a){var o=t+"Mark";o in i&&(a?(b(t,e,i[o],n),delete i[o]):(b(t,e,i[o],e.length),i[o]=0))};for(n=0;n<r;n++)switch(t=e[n],l){case o.PARSER_UNINITIALIZED:return n;case o.START:c=0,l=o.START_BOUNDARY;case o.START_BOUNDARY:if(c==m.length-2){if(45==t)u|=s.LAST_BOUNDARY;else if(13!=t)return n;c++;break}if(c-1==m.length-2){if(u&s.LAST_BOUNDARY&&45==t)b("end"),l=o.END,u=0;else{if(u&s.LAST_BOUNDARY||10!=t)return n;c=0,b("partBegin"),l=o.HEADER_FIELD_START}break}t!=m[c+2]&&(c=-2),t==m[c+2]&&c++;break;case o.HEADER_FIELD_START:l=o.HEADER_FIELD,g("headerField"),c=0;case o.HEADER_FIELD:if(13==t){delete i["headerField"+"Mark"],l=o.HEADERS_ALMOST_DONE;break}if(c++,45==t)break;if(58==t){if(1==c)return n;y("headerField",!0),l=o.HEADER_VALUE_START;break}if((a=32|t)<97||a>122)return n;break;case o.HEADER_VALUE_START:if(32==t)break;g("headerValue"),l=o.HEADER_VALUE;case o.HEADER_VALUE:13==t&&(y("headerValue",!0),b("headerEnd"),l=o.HEADER_VALUE_ALMOST_DONE);break;case o.HEADER_VALUE_ALMOST_DONE:if(10!=t)return n;l=o.HEADER_FIELD_START;break;case o.HEADERS_ALMOST_DONE:if(10!=t)return n;b("headersEnd"),l=o.PART_DATA_START;break;case o.PART_DATA_START:l=o.PART_DATA,g("partData");case o.PART_DATA:if(p=c,0===c){for(n+=x;n<v&&!(e[n]in f);)n+=h;n-=x,t=e[n]}if(c<m.length)m[c]==t?(0===c&&y("partData",!0),c++):c=0;else if(c==m.length)c++,13==t?u|=s.PART_BOUNDARY:45==t?u|=s.LAST_BOUNDARY:c=0;else if(c-1==m.length){if(u&s.PART_BOUNDARY){if(c=0,10==t){u&=~s.PART_BOUNDARY,b("partEnd"),b("partBegin"),l=o.HEADER_FIELD_START;break}}else u&s.LAST_BOUNDARY&&45==t?(b("partEnd"),b("end"),l=o.END,u=0):c=0}c>0?d[c-1]=t:p>0&&(b("partData",d,0,p),p=0,g("partData"),n--);break;case o.END:break;default:return n}return y("headerField"),y("headerValue"),y("partData"),this.index=c,this.state=l,this.flags=u,r},r.prototype.end=function(){var e=function(e,t){var a="on"+t.substr(0,1).toUpperCase()+t.substr(1);a in e&&e[a]()};if(this.state==o.HEADER_FIELD_START&&0===this.index||this.state==o.PART_DATA&&this.index==this.boundary.length)e(this,"partEnd"),e(this,"end");else if(this.state!=o.END)return Error("MultipartParser.end(): stream ended unexpectedly: "+this.explain())},r.prototype.explain=function(){return"state = "+r.stateToString(this.state)}},88179:(e,t,a)=>{var i=a(17702).EventEmitter;function n(e){if(!(this instanceof n))return new n(e);i.call(this)}a(21764).inherits(n,i),t.h=n,n.prototype.write=function(e){return this.emit("data",e),e.length},n.prototype.end=function(){this.emit("end")}},90291:(e,t,a)=>{var i=a(86624);function n(e){this.maxKeys=e,this.buffer=""}t.l=n,n.prototype.write=function(e){return this.buffer+=e.toString("ascii"),e.length},n.prototype.end=function(){var e=i.parse(this.buffer,"&","=",{maxKeys:this.maxKeys});for(var t in e)this.onField(t,e[t]);this.buffer="",this.onEnd()}},78621:e=>{"use strict";var t=Object.prototype.toString,a=Math.max,i=function(e,t){for(var a=[],i=0;i<e.length;i+=1)a[i]=e[i];for(var n=0;n<t.length;n+=1)a[n+e.length]=t[n];return a},n=function(e,t){for(var a=[],i=t||0,n=0;i<e.length;i+=1,n+=1)a[n]=e[i];return a},o=function(e,t){for(var a="",i=0;i<e.length;i+=1)a+=e[i],i+1<e.length&&(a+=t);return a};e.exports=function(e){var s,r=this;if("function"!=typeof r||"[object Function]"!==t.apply(r))throw TypeError("Function.prototype.bind called on incompatible "+r);for(var p=n(arguments,1),c=a(0,r.length-p.length),l=[],u=0;u<c;u++)l[u]="$"+u;if(s=Function("binder","return function ("+o(l,",")+"){ return binder.apply(this,arguments); }")(function(){if(this instanceof s){var t=r.apply(this,i(p,arguments));return Object(t)===t?t:this}return r.apply(e,i(p,arguments))}),r.prototype){var d=function(){};d.prototype=r.prototype,s.prototype=new d,d.prototype=null}return s}},1061:(e,t,a)=>{"use strict";var i=a(78621);e.exports=Function.prototype.bind||i},44868:(e,t,a)=>{"use strict";var i,n=a(2065),o=a(18850),s=a(43302),r=a(45200),p=a(97756),c=a(26829),l=a(64830),u=Function,d=function(e){try{return u('"use strict"; return ('+e+").constructor;")()}catch(e){}},m=Object.getOwnPropertyDescriptor;if(m)try{m({},"")}catch(e){m=null}var f=function(){throw new c},h=m?function(){try{return arguments.callee,f}catch(e){try{return m(arguments,"callee").get}catch(e){return f}}}():f,x=a(4)(),v=a(82569)(),g=Object.getPrototypeOf||(v?function(e){return e.__proto__}:null),b={},y="undefined"!=typeof Uint8Array&&g?g(Uint8Array):i,w={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?i:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?i:ArrayBuffer,"%ArrayIteratorPrototype%":x&&g?g([][Symbol.iterator]()):i,"%AsyncFromSyncIteratorPrototype%":i,"%AsyncFunction%":b,"%AsyncGenerator%":b,"%AsyncGeneratorFunction%":b,"%AsyncIteratorPrototype%":b,"%Atomics%":"undefined"==typeof Atomics?i:Atomics,"%BigInt%":"undefined"==typeof BigInt?i:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?i:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?i:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?i:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":n,"%eval%":eval,"%EvalError%":o,"%Float32Array%":"undefined"==typeof Float32Array?i:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?i:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?i:FinalizationRegistry,"%Function%":u,"%GeneratorFunction%":b,"%Int8Array%":"undefined"==typeof Int8Array?i:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?i:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?i:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":x&&g?g(g([][Symbol.iterator]())):i,"%JSON%":"object"==typeof JSON?JSON:i,"%Map%":"undefined"==typeof Map?i:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&x&&g?g(new Map()[Symbol.iterator]()):i,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?i:Promise,"%Proxy%":"undefined"==typeof Proxy?i:Proxy,"%RangeError%":s,"%ReferenceError%":r,"%Reflect%":"undefined"==typeof Reflect?i:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?i:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&x&&g?g(new Set()[Symbol.iterator]()):i,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?i:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":x&&g?g(""[Symbol.iterator]()):i,"%Symbol%":x?Symbol:i,"%SyntaxError%":p,"%ThrowTypeError%":h,"%TypedArray%":y,"%TypeError%":c,"%Uint8Array%":"undefined"==typeof Uint8Array?i:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?i:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?i:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?i:Uint32Array,"%URIError%":l,"%WeakMap%":"undefined"==typeof WeakMap?i:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?i:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?i:WeakSet};if(g)try{null.error}catch(e){var _=g(g(e));w["%Error.prototype%"]=_}var k=function e(t){var a;if("%AsyncFunction%"===t)a=d("async function () {}");else if("%GeneratorFunction%"===t)a=d("function* () {}");else if("%AsyncGeneratorFunction%"===t)a=d("async function* () {}");else if("%AsyncGenerator%"===t){var i=e("%AsyncGeneratorFunction%");i&&(a=i.prototype)}else if("%AsyncIteratorPrototype%"===t){var n=e("%AsyncGenerator%");n&&g&&(a=g(n.prototype))}return w[t]=a,a},E={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},j=a(1061),T=a(46709),S=j.call(Function.call,Array.prototype.concat),A=j.call(Function.apply,Array.prototype.splice),O=j.call(Function.call,String.prototype.replace),R=j.call(Function.call,String.prototype.slice),C=j.call(Function.call,RegExp.prototype.exec),P=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,z=/\\(\\)?/g,L=function(e){var t=R(e,0,1),a=R(e,-1);if("%"===t&&"%"!==a)throw new p("invalid intrinsic syntax, expected closing `%`");if("%"===a&&"%"!==t)throw new p("invalid intrinsic syntax, expected opening `%`");var i=[];return O(e,P,function(e,t,a,n){i[i.length]=a?O(n,z,"$1"):t||e}),i},D=function(e,t){var a,i=e;if(T(E,i)&&(i="%"+(a=E[i])[0]+"%"),T(w,i)){var n=w[i];if(n===b&&(n=k(i)),void 0===n&&!t)throw new c("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:a,name:i,value:n}}throw new p("intrinsic "+e+" does not exist!")};e.exports=function(e,t){if("string"!=typeof e||0===e.length)throw new c("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new c('"allowMissing" argument must be a boolean');if(null===C(/^%?[^%]*%?$/,e))throw new p("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var a=L(e),i=a.length>0?a[0]:"",n=D("%"+i+"%",t),o=n.name,s=n.value,r=!1,l=n.alias;l&&(i=l[0],A(a,S([0,1],l)));for(var u=1,d=!0;u<a.length;u+=1){var f=a[u],h=R(f,0,1),x=R(f,-1);if(('"'===h||"'"===h||"`"===h||'"'===x||"'"===x||"`"===x)&&h!==x)throw new p("property names with quotes must have matching quotes");if("constructor"!==f&&d||(r=!0),i+="."+f,T(w,o="%"+i+"%"))s=w[o];else if(null!=s){if(!(f in s)){if(!t)throw new c("base intrinsic for "+e+" exists, but the property is not available.");return}if(m&&u+1>=a.length){var v=m(s,f);s=(d=!!v)&&"get"in v&&!("originalValue"in v.get)?v.get:s[f]}else d=T(s,f),s=s[f];d&&!r&&(w[o]=s)}}return s}},25350:e=>{"use strict";e.exports=Object.getOwnPropertyDescriptor},14237:(e,t,a)=>{"use strict";var i=a(25350);if(i)try{i([],"length")}catch(e){i=null}e.exports=i},78679:e=>{"use strict";e.exports=(e,t=process.argv)=>{let a=e.startsWith("-")?"":1===e.length?"-":"--",i=t.indexOf(a+e),n=t.indexOf("--");return -1!==i&&(-1===n||i<n)}},99295:(e,t,a)=>{"use strict";var i=a(36429),n=function(){return!!i};n.hasArrayLengthDefineBug=function(){if(!i)return null;try{return 1!==i([],"length",{value:1}).length}catch(e){return!0}},e.exports=n},82569:e=>{"use strict";var t={__proto__:null,foo:{}},a={__proto__:t}.foo===t.foo&&!(t instanceof Object);e.exports=function(){return a}},4:(e,t,a)=>{"use strict";var i="undefined"!=typeof Symbol&&Symbol,n=a(79672);e.exports=function(){return"function"==typeof i&&"function"==typeof Symbol&&"symbol"==typeof i("foo")&&"symbol"==typeof Symbol("bar")&&n()}},79672:e=>{"use strict";e.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),a=Object(t);if("string"==typeof t||"[object Symbol]"!==Object.prototype.toString.call(t)||"[object Symbol]"!==Object.prototype.toString.call(a))return!1;for(var i in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length||"function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var n=Object.getOwnPropertySymbols(e);if(1!==n.length||n[0]!==t||!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var o=Object.getOwnPropertyDescriptor(e,t);if(42!==o.value||!0!==o.enumerable)return!1}return!0}},46709:(e,t,a)=>{"use strict";var i=Function.prototype.call,n=Object.prototype.hasOwnProperty,o=a(1061);e.exports=o.call(i,n)},46362:function(e,t,a){var i,n;n=function(){var e=/^;\s*([^"=]+)=(?:"([^"]+)"|([^";,]+)(?:[;,]|$))/,t=/^<([^>]*)>/,a=/^\s*,\s*/;return{parse:function(i,n){for(var o,s,r,p=n&&n.extended||!1,c=[];i&&(i=i.trim(),s=t.exec(i));){for(var l={link:s[1]},u=(i=i.slice(s[0].length)).match(a);i&&(!u||u.index>0)&&(o=e.exec(i));)u=(i=i.slice(o[0].length)).match(a),"rel"===o[1]||"rev"===o[1]?(r=(o[2]||o[3]).split(/\s+/),l[o[1]]=r):l[o[1]]=o[2]||o[3];c.push(l),i=i.replace(a,"")}return p?c:c.reduce(function(e,t){return t.rel&&t.rel.forEach(function(a){e[a]=t.link}),e},{})},stringify:function(e){var t=Object.keys(e).reduce(function(t,a){return t[e[a]]=t[e[a]]||[],t[e[a]].push(a),t},{});return Object.keys(t).reduce(function(e,a){return e.concat("<"+a+'>; rel="'+t[a].join(" ")+'"')},[]).join(", ")}}},e.exports?e.exports=n():"function"==typeof this.define&&this.define.amd?void 0!==(i=n.call(t,a,t,e))&&(e.exports=i):this.li=n()},98529:(e,t,a)=>{"use strict";var i=a(32615);e.exports=i.METHODS&&i.METHODS.map(function(e){return e.toLowerCase()})||["get","post","put","head","delete","options","trace","copy","lock","mkcol","move","purge","propfind","proppatch","unlock","report","mkactivity","checkout","merge","m-search","notify","subscribe","unsubscribe","patch","search","connect"]},50424:(e,t,a)=>{e.exports=a(21882)},33320:(e,t,a)=>{"use strict";var i=a(50424),n=a(55315).extname,o=/^\s*([^;\s]*)(?:;|\s|$)/,s=/^text\//i;function r(e){if(!e||"string"!=typeof e)return!1;var t=o.exec(e),a=t&&i[t[1].toLowerCase()];return a&&a.charset?a.charset:!!(t&&s.test(t[1]))&&"UTF-8"}t.charset=r,t.charsets={lookup:r},t.contentType=function(e){if(!e||"string"!=typeof e)return!1;var a=-1===e.indexOf("/")?t.lookup(e):e;if(!a)return!1;if(-1===a.indexOf("charset")){var i=t.charset(a);i&&(a+="; charset="+i.toLowerCase())}return a},t.extension=function(e){if(!e||"string"!=typeof e)return!1;var a=o.exec(e),i=a&&t.extensions[a[1].toLowerCase()];return!!i&&!!i.length&&i[0]},t.extensions=Object.create(null),t.lookup=function(e){if(!e||"string"!=typeof e)return!1;var a=n("x."+e).toLowerCase().substr(1);return!!a&&(t.types[a]||!1)},t.types=Object.create(null),function(e,t){var a=["nginx","apache",void 0,"iana"];Object.keys(i).forEach(function(n){var o=i[n],s=o.extensions;if(s&&s.length){e[n]=s;for(var r=0;r<s.length;r++){var p=s[r];if(t[p]){var c=a.indexOf(i[t[p]].source),l=a.indexOf(o.source);if("application/octet-stream"!==t[p]&&(c>l||c===l&&"application/"===t[p].substr(0,12)))continue}t[p]=n}}})}(t.extensions,t.types)},17155:e=>{"use strict";function t(){this._types=Object.create(null),this._extensions=Object.create(null);for(let e=0;e<arguments.length;e++)this.define(arguments[e]);this.define=this.define.bind(this),this.getType=this.getType.bind(this),this.getExtension=this.getExtension.bind(this)}t.prototype.define=function(e,t){for(let a in e){let i=e[a].map(function(e){return e.toLowerCase()});a=a.toLowerCase();for(let e=0;e<i.length;e++){let n=i[e];if("*"!==n[0]){if(!t&&n in this._types)throw Error('Attempt to change mapping for "'+n+'" extension from "'+this._types[n]+'" to "'+a+'". Pass `force=true` to allow this, otherwise remove "'+n+'" from the list of extensions for "'+a+'".');this._types[n]=a}}if(t||!this._extensions[a]){let e=i[0];this._extensions[a]="*"!==e[0]?e:e.substr(1)}}},t.prototype.getType=function(e){let t=(e=String(e)).replace(/^.*[/\\]/,"").toLowerCase(),a=t.replace(/^.*\./,"").toLowerCase(),i=t.length<e.length;return(a.length<t.length-1||!i)&&this._types[a]||null},t.prototype.getExtension=function(e){return(e=/^\s*([^;\s]*)/.test(e)&&RegExp.$1)&&this._extensions[e.toLowerCase()]||null},e.exports=t},54891:(e,t,a)=>{"use strict";let i=a(17155);e.exports=new i(a(42938),a(8650))},8650:e=>{e.exports={"application/prs.cww":["cww"],"application/vnd.1000minds.decision-model+xml":["1km"],"application/vnd.3gpp.pic-bw-large":["plb"],"application/vnd.3gpp.pic-bw-small":["psb"],"application/vnd.3gpp.pic-bw-var":["pvb"],"application/vnd.3gpp2.tcap":["tcap"],"application/vnd.3m.post-it-notes":["pwn"],"application/vnd.accpac.simply.aso":["aso"],"application/vnd.accpac.simply.imp":["imp"],"application/vnd.acucobol":["acu"],"application/vnd.acucorp":["atc","acutc"],"application/vnd.adobe.air-application-installer-package+zip":["air"],"application/vnd.adobe.formscentral.fcdt":["fcdt"],"application/vnd.adobe.fxp":["fxp","fxpl"],"application/vnd.adobe.xdp+xml":["xdp"],"application/vnd.adobe.xfdf":["xfdf"],"application/vnd.ahead.space":["ahead"],"application/vnd.airzip.filesecure.azf":["azf"],"application/vnd.airzip.filesecure.azs":["azs"],"application/vnd.amazon.ebook":["azw"],"application/vnd.americandynamics.acc":["acc"],"application/vnd.amiga.ami":["ami"],"application/vnd.android.package-archive":["apk"],"application/vnd.anser-web-certificate-issue-initiation":["cii"],"application/vnd.anser-web-funds-transfer-initiation":["fti"],"application/vnd.antix.game-component":["atx"],"application/vnd.apple.installer+xml":["mpkg"],"application/vnd.apple.keynote":["key"],"application/vnd.apple.mpegurl":["m3u8"],"application/vnd.apple.numbers":["numbers"],"application/vnd.apple.pages":["pages"],"application/vnd.apple.pkpass":["pkpass"],"application/vnd.aristanetworks.swi":["swi"],"application/vnd.astraea-software.iota":["iota"],"application/vnd.audiograph":["aep"],"application/vnd.balsamiq.bmml+xml":["bmml"],"application/vnd.blueice.multipass":["mpm"],"application/vnd.bmi":["bmi"],"application/vnd.businessobjects":["rep"],"application/vnd.chemdraw+xml":["cdxml"],"application/vnd.chipnuts.karaoke-mmd":["mmd"],"application/vnd.cinderella":["cdy"],"application/vnd.citationstyles.style+xml":["csl"],"application/vnd.claymore":["cla"],"application/vnd.cloanto.rp9":["rp9"],"application/vnd.clonk.c4group":["c4g","c4d","c4f","c4p","c4u"],"application/vnd.cluetrust.cartomobile-config":["c11amc"],"application/vnd.cluetrust.cartomobile-config-pkg":["c11amz"],"application/vnd.commonspace":["csp"],"application/vnd.contact.cmsg":["cdbcmsg"],"application/vnd.cosmocaller":["cmc"],"application/vnd.crick.clicker":["clkx"],"application/vnd.crick.clicker.keyboard":["clkk"],"application/vnd.crick.clicker.palette":["clkp"],"application/vnd.crick.clicker.template":["clkt"],"application/vnd.crick.clicker.wordbank":["clkw"],"application/vnd.criticaltools.wbs+xml":["wbs"],"application/vnd.ctc-posml":["pml"],"application/vnd.cups-ppd":["ppd"],"application/vnd.curl.car":["car"],"application/vnd.curl.pcurl":["pcurl"],"application/vnd.dart":["dart"],"application/vnd.data-vision.rdz":["rdz"],"application/vnd.dbf":["dbf"],"application/vnd.dece.data":["uvf","uvvf","uvd","uvvd"],"application/vnd.dece.ttml+xml":["uvt","uvvt"],"application/vnd.dece.unspecified":["uvx","uvvx"],"application/vnd.dece.zip":["uvz","uvvz"],"application/vnd.denovo.fcselayout-link":["fe_launch"],"application/vnd.dna":["dna"],"application/vnd.dolby.mlp":["mlp"],"application/vnd.dpgraph":["dpg"],"application/vnd.dreamfactory":["dfac"],"application/vnd.ds-keypoint":["kpxx"],"application/vnd.dvb.ait":["ait"],"application/vnd.dvb.service":["svc"],"application/vnd.dynageo":["geo"],"application/vnd.ecowin.chart":["mag"],"application/vnd.enliven":["nml"],"application/vnd.epson.esf":["esf"],"application/vnd.epson.msf":["msf"],"application/vnd.epson.quickanime":["qam"],"application/vnd.epson.salt":["slt"],"application/vnd.epson.ssf":["ssf"],"application/vnd.eszigno3+xml":["es3","et3"],"application/vnd.ezpix-album":["ez2"],"application/vnd.ezpix-package":["ez3"],"application/vnd.fdf":["fdf"],"application/vnd.fdsn.mseed":["mseed"],"application/vnd.fdsn.seed":["seed","dataless"],"application/vnd.flographit":["gph"],"application/vnd.fluxtime.clip":["ftc"],"application/vnd.framemaker":["fm","frame","maker","book"],"application/vnd.frogans.fnc":["fnc"],"application/vnd.frogans.ltf":["ltf"],"application/vnd.fsc.weblaunch":["fsc"],"application/vnd.fujitsu.oasys":["oas"],"application/vnd.fujitsu.oasys2":["oa2"],"application/vnd.fujitsu.oasys3":["oa3"],"application/vnd.fujitsu.oasysgp":["fg5"],"application/vnd.fujitsu.oasysprs":["bh2"],"application/vnd.fujixerox.ddd":["ddd"],"application/vnd.fujixerox.docuworks":["xdw"],"application/vnd.fujixerox.docuworks.binder":["xbd"],"application/vnd.fuzzysheet":["fzs"],"application/vnd.genomatix.tuxedo":["txd"],"application/vnd.geogebra.file":["ggb"],"application/vnd.geogebra.tool":["ggt"],"application/vnd.geometry-explorer":["gex","gre"],"application/vnd.geonext":["gxt"],"application/vnd.geoplan":["g2w"],"application/vnd.geospace":["g3w"],"application/vnd.gmx":["gmx"],"application/vnd.google-apps.document":["gdoc"],"application/vnd.google-apps.presentation":["gslides"],"application/vnd.google-apps.spreadsheet":["gsheet"],"application/vnd.google-earth.kml+xml":["kml"],"application/vnd.google-earth.kmz":["kmz"],"application/vnd.grafeq":["gqf","gqs"],"application/vnd.groove-account":["gac"],"application/vnd.groove-help":["ghf"],"application/vnd.groove-identity-message":["gim"],"application/vnd.groove-injector":["grv"],"application/vnd.groove-tool-message":["gtm"],"application/vnd.groove-tool-template":["tpl"],"application/vnd.groove-vcard":["vcg"],"application/vnd.hal+xml":["hal"],"application/vnd.handheld-entertainment+xml":["zmm"],"application/vnd.hbci":["hbci"],"application/vnd.hhe.lesson-player":["les"],"application/vnd.hp-hpgl":["hpgl"],"application/vnd.hp-hpid":["hpid"],"application/vnd.hp-hps":["hps"],"application/vnd.hp-jlyt":["jlt"],"application/vnd.hp-pcl":["pcl"],"application/vnd.hp-pclxl":["pclxl"],"application/vnd.hydrostatix.sof-data":["sfd-hdstx"],"application/vnd.ibm.minipay":["mpy"],"application/vnd.ibm.modcap":["afp","listafp","list3820"],"application/vnd.ibm.rights-management":["irm"],"application/vnd.ibm.secure-container":["sc"],"application/vnd.iccprofile":["icc","icm"],"application/vnd.igloader":["igl"],"application/vnd.immervision-ivp":["ivp"],"application/vnd.immervision-ivu":["ivu"],"application/vnd.insors.igm":["igm"],"application/vnd.intercon.formnet":["xpw","xpx"],"application/vnd.intergeo":["i2g"],"application/vnd.intu.qbo":["qbo"],"application/vnd.intu.qfx":["qfx"],"application/vnd.ipunplugged.rcprofile":["rcprofile"],"application/vnd.irepository.package+xml":["irp"],"application/vnd.is-xpr":["xpr"],"application/vnd.isac.fcs":["fcs"],"application/vnd.jam":["jam"],"application/vnd.jcp.javame.midlet-rms":["rms"],"application/vnd.jisp":["jisp"],"application/vnd.joost.joda-archive":["joda"],"application/vnd.kahootz":["ktz","ktr"],"application/vnd.kde.karbon":["karbon"],"application/vnd.kde.kchart":["chrt"],"application/vnd.kde.kformula":["kfo"],"application/vnd.kde.kivio":["flw"],"application/vnd.kde.kontour":["kon"],"application/vnd.kde.kpresenter":["kpr","kpt"],"application/vnd.kde.kspread":["ksp"],"application/vnd.kde.kword":["kwd","kwt"],"application/vnd.kenameaapp":["htke"],"application/vnd.kidspiration":["kia"],"application/vnd.kinar":["kne","knp"],"application/vnd.koan":["skp","skd","skt","skm"],"application/vnd.kodak-descriptor":["sse"],"application/vnd.las.las+xml":["lasxml"],"application/vnd.llamagraphics.life-balance.desktop":["lbd"],"application/vnd.llamagraphics.life-balance.exchange+xml":["lbe"],"application/vnd.lotus-1-2-3":["123"],"application/vnd.lotus-approach":["apr"],"application/vnd.lotus-freelance":["pre"],"application/vnd.lotus-notes":["nsf"],"application/vnd.lotus-organizer":["org"],"application/vnd.lotus-screencam":["scm"],"application/vnd.lotus-wordpro":["lwp"],"application/vnd.macports.portpkg":["portpkg"],"application/vnd.mapbox-vector-tile":["mvt"],"application/vnd.mcd":["mcd"],"application/vnd.medcalcdata":["mc1"],"application/vnd.mediastation.cdkey":["cdkey"],"application/vnd.mfer":["mwf"],"application/vnd.mfmp":["mfm"],"application/vnd.micrografx.flo":["flo"],"application/vnd.micrografx.igx":["igx"],"application/vnd.mif":["mif"],"application/vnd.mobius.daf":["daf"],"application/vnd.mobius.dis":["dis"],"application/vnd.mobius.mbk":["mbk"],"application/vnd.mobius.mqy":["mqy"],"application/vnd.mobius.msl":["msl"],"application/vnd.mobius.plc":["plc"],"application/vnd.mobius.txf":["txf"],"application/vnd.mophun.application":["mpn"],"application/vnd.mophun.certificate":["mpc"],"application/vnd.mozilla.xul+xml":["xul"],"application/vnd.ms-artgalry":["cil"],"application/vnd.ms-cab-compressed":["cab"],"application/vnd.ms-excel":["xls","xlm","xla","xlc","xlt","xlw"],"application/vnd.ms-excel.addin.macroenabled.12":["xlam"],"application/vnd.ms-excel.sheet.binary.macroenabled.12":["xlsb"],"application/vnd.ms-excel.sheet.macroenabled.12":["xlsm"],"application/vnd.ms-excel.template.macroenabled.12":["xltm"],"application/vnd.ms-fontobject":["eot"],"application/vnd.ms-htmlhelp":["chm"],"application/vnd.ms-ims":["ims"],"application/vnd.ms-lrm":["lrm"],"application/vnd.ms-officetheme":["thmx"],"application/vnd.ms-outlook":["msg"],"application/vnd.ms-pki.seccat":["cat"],"application/vnd.ms-pki.stl":["*stl"],"application/vnd.ms-powerpoint":["ppt","pps","pot"],"application/vnd.ms-powerpoint.addin.macroenabled.12":["ppam"],"application/vnd.ms-powerpoint.presentation.macroenabled.12":["pptm"],"application/vnd.ms-powerpoint.slide.macroenabled.12":["sldm"],"application/vnd.ms-powerpoint.slideshow.macroenabled.12":["ppsm"],"application/vnd.ms-powerpoint.template.macroenabled.12":["potm"],"application/vnd.ms-project":["mpp","mpt"],"application/vnd.ms-word.document.macroenabled.12":["docm"],"application/vnd.ms-word.template.macroenabled.12":["dotm"],"application/vnd.ms-works":["wps","wks","wcm","wdb"],"application/vnd.ms-wpl":["wpl"],"application/vnd.ms-xpsdocument":["xps"],"application/vnd.mseq":["mseq"],"application/vnd.musician":["mus"],"application/vnd.muvee.style":["msty"],"application/vnd.mynfc":["taglet"],"application/vnd.neurolanguage.nlu":["nlu"],"application/vnd.nitf":["ntf","nitf"],"application/vnd.noblenet-directory":["nnd"],"application/vnd.noblenet-sealer":["nns"],"application/vnd.noblenet-web":["nnw"],"application/vnd.nokia.n-gage.ac+xml":["*ac"],"application/vnd.nokia.n-gage.data":["ngdat"],"application/vnd.nokia.n-gage.symbian.install":["n-gage"],"application/vnd.nokia.radio-preset":["rpst"],"application/vnd.nokia.radio-presets":["rpss"],"application/vnd.novadigm.edm":["edm"],"application/vnd.novadigm.edx":["edx"],"application/vnd.novadigm.ext":["ext"],"application/vnd.oasis.opendocument.chart":["odc"],"application/vnd.oasis.opendocument.chart-template":["otc"],"application/vnd.oasis.opendocument.database":["odb"],"application/vnd.oasis.opendocument.formula":["odf"],"application/vnd.oasis.opendocument.formula-template":["odft"],"application/vnd.oasis.opendocument.graphics":["odg"],"application/vnd.oasis.opendocument.graphics-template":["otg"],"application/vnd.oasis.opendocument.image":["odi"],"application/vnd.oasis.opendocument.image-template":["oti"],"application/vnd.oasis.opendocument.presentation":["odp"],"application/vnd.oasis.opendocument.presentation-template":["otp"],"application/vnd.oasis.opendocument.spreadsheet":["ods"],"application/vnd.oasis.opendocument.spreadsheet-template":["ots"],"application/vnd.oasis.opendocument.text":["odt"],"application/vnd.oasis.opendocument.text-master":["odm"],"application/vnd.oasis.opendocument.text-template":["ott"],"application/vnd.oasis.opendocument.text-web":["oth"],"application/vnd.olpc-sugar":["xo"],"application/vnd.oma.dd2+xml":["dd2"],"application/vnd.openblox.game+xml":["obgx"],"application/vnd.openofficeorg.extension":["oxt"],"application/vnd.openstreetmap.data+xml":["osm"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":["pptx"],"application/vnd.openxmlformats-officedocument.presentationml.slide":["sldx"],"application/vnd.openxmlformats-officedocument.presentationml.slideshow":["ppsx"],"application/vnd.openxmlformats-officedocument.presentationml.template":["potx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":["xlsx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.template":["xltx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":["docx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.template":["dotx"],"application/vnd.osgeo.mapguide.package":["mgp"],"application/vnd.osgi.dp":["dp"],"application/vnd.osgi.subsystem":["esa"],"application/vnd.palm":["pdb","pqa","oprc"],"application/vnd.pawaafile":["paw"],"application/vnd.pg.format":["str"],"application/vnd.pg.osasli":["ei6"],"application/vnd.picsel":["efif"],"application/vnd.pmi.widget":["wg"],"application/vnd.pocketlearn":["plf"],"application/vnd.powerbuilder6":["pbd"],"application/vnd.previewsystems.box":["box"],"application/vnd.proteus.magazine":["mgz"],"application/vnd.publishare-delta-tree":["qps"],"application/vnd.pvi.ptid1":["ptid"],"application/vnd.quark.quarkxpress":["qxd","qxt","qwd","qwt","qxl","qxb"],"application/vnd.rar":["rar"],"application/vnd.realvnc.bed":["bed"],"application/vnd.recordare.musicxml":["mxl"],"application/vnd.recordare.musicxml+xml":["musicxml"],"application/vnd.rig.cryptonote":["cryptonote"],"application/vnd.rim.cod":["cod"],"application/vnd.rn-realmedia":["rm"],"application/vnd.rn-realmedia-vbr":["rmvb"],"application/vnd.route66.link66+xml":["link66"],"application/vnd.sailingtracker.track":["st"],"application/vnd.seemail":["see"],"application/vnd.sema":["sema"],"application/vnd.semd":["semd"],"application/vnd.semf":["semf"],"application/vnd.shana.informed.formdata":["ifm"],"application/vnd.shana.informed.formtemplate":["itp"],"application/vnd.shana.informed.interchange":["iif"],"application/vnd.shana.informed.package":["ipk"],"application/vnd.simtech-mindmapper":["twd","twds"],"application/vnd.smaf":["mmf"],"application/vnd.smart.teacher":["teacher"],"application/vnd.software602.filler.form+xml":["fo"],"application/vnd.solent.sdkm+xml":["sdkm","sdkd"],"application/vnd.spotfire.dxp":["dxp"],"application/vnd.spotfire.sfs":["sfs"],"application/vnd.stardivision.calc":["sdc"],"application/vnd.stardivision.draw":["sda"],"application/vnd.stardivision.impress":["sdd"],"application/vnd.stardivision.math":["smf"],"application/vnd.stardivision.writer":["sdw","vor"],"application/vnd.stardivision.writer-global":["sgl"],"application/vnd.stepmania.package":["smzip"],"application/vnd.stepmania.stepchart":["sm"],"application/vnd.sun.wadl+xml":["wadl"],"application/vnd.sun.xml.calc":["sxc"],"application/vnd.sun.xml.calc.template":["stc"],"application/vnd.sun.xml.draw":["sxd"],"application/vnd.sun.xml.draw.template":["std"],"application/vnd.sun.xml.impress":["sxi"],"application/vnd.sun.xml.impress.template":["sti"],"application/vnd.sun.xml.math":["sxm"],"application/vnd.sun.xml.writer":["sxw"],"application/vnd.sun.xml.writer.global":["sxg"],"application/vnd.sun.xml.writer.template":["stw"],"application/vnd.sus-calendar":["sus","susp"],"application/vnd.svd":["svd"],"application/vnd.symbian.install":["sis","sisx"],"application/vnd.syncml+xml":["xsm"],"application/vnd.syncml.dm+wbxml":["bdm"],"application/vnd.syncml.dm+xml":["xdm"],"application/vnd.syncml.dmddf+xml":["ddf"],"application/vnd.tao.intent-module-archive":["tao"],"application/vnd.tcpdump.pcap":["pcap","cap","dmp"],"application/vnd.tmobile-livetv":["tmo"],"application/vnd.trid.tpt":["tpt"],"application/vnd.triscape.mxs":["mxs"],"application/vnd.trueapp":["tra"],"application/vnd.ufdl":["ufd","ufdl"],"application/vnd.uiq.theme":["utz"],"application/vnd.umajin":["umj"],"application/vnd.unity":["unityweb"],"application/vnd.uoml+xml":["uoml"],"application/vnd.vcx":["vcx"],"application/vnd.visio":["vsd","vst","vss","vsw"],"application/vnd.visionary":["vis"],"application/vnd.vsf":["vsf"],"application/vnd.wap.wbxml":["wbxml"],"application/vnd.wap.wmlc":["wmlc"],"application/vnd.wap.wmlscriptc":["wmlsc"],"application/vnd.webturbo":["wtb"],"application/vnd.wolfram.player":["nbp"],"application/vnd.wordperfect":["wpd"],"application/vnd.wqd":["wqd"],"application/vnd.wt.stf":["stf"],"application/vnd.xara":["xar"],"application/vnd.xfdl":["xfdl"],"application/vnd.yamaha.hv-dic":["hvd"],"application/vnd.yamaha.hv-script":["hvs"],"application/vnd.yamaha.hv-voice":["hvp"],"application/vnd.yamaha.openscoreformat":["osf"],"application/vnd.yamaha.openscoreformat.osfpvg+xml":["osfpvg"],"application/vnd.yamaha.smaf-audio":["saf"],"application/vnd.yamaha.smaf-phrase":["spf"],"application/vnd.yellowriver-custom-menu":["cmp"],"application/vnd.zul":["zir","zirz"],"application/vnd.zzazz.deck+xml":["zaz"],"application/x-7z-compressed":["7z"],"application/x-abiword":["abw"],"application/x-ace-compressed":["ace"],"application/x-apple-diskimage":["*dmg"],"application/x-arj":["arj"],"application/x-authorware-bin":["aab","x32","u32","vox"],"application/x-authorware-map":["aam"],"application/x-authorware-seg":["aas"],"application/x-bcpio":["bcpio"],"application/x-bdoc":["*bdoc"],"application/x-bittorrent":["torrent"],"application/x-blorb":["blb","blorb"],"application/x-bzip":["bz"],"application/x-bzip2":["bz2","boz"],"application/x-cbr":["cbr","cba","cbt","cbz","cb7"],"application/x-cdlink":["vcd"],"application/x-cfs-compressed":["cfs"],"application/x-chat":["chat"],"application/x-chess-pgn":["pgn"],"application/x-chrome-extension":["crx"],"application/x-cocoa":["cco"],"application/x-conference":["nsc"],"application/x-cpio":["cpio"],"application/x-csh":["csh"],"application/x-debian-package":["*deb","udeb"],"application/x-dgc-compressed":["dgc"],"application/x-director":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"],"application/x-doom":["wad"],"application/x-dtbncx+xml":["ncx"],"application/x-dtbook+xml":["dtb"],"application/x-dtbresource+xml":["res"],"application/x-dvi":["dvi"],"application/x-envoy":["evy"],"application/x-eva":["eva"],"application/x-font-bdf":["bdf"],"application/x-font-ghostscript":["gsf"],"application/x-font-linux-psf":["psf"],"application/x-font-pcf":["pcf"],"application/x-font-snf":["snf"],"application/x-font-type1":["pfa","pfb","pfm","afm"],"application/x-freearc":["arc"],"application/x-futuresplash":["spl"],"application/x-gca-compressed":["gca"],"application/x-glulx":["ulx"],"application/x-gnumeric":["gnumeric"],"application/x-gramps-xml":["gramps"],"application/x-gtar":["gtar"],"application/x-hdf":["hdf"],"application/x-httpd-php":["php"],"application/x-install-instructions":["install"],"application/x-iso9660-image":["*iso"],"application/x-iwork-keynote-sffkey":["*key"],"application/x-iwork-numbers-sffnumbers":["*numbers"],"application/x-iwork-pages-sffpages":["*pages"],"application/x-java-archive-diff":["jardiff"],"application/x-java-jnlp-file":["jnlp"],"application/x-keepass2":["kdbx"],"application/x-latex":["latex"],"application/x-lua-bytecode":["luac"],"application/x-lzh-compressed":["lzh","lha"],"application/x-makeself":["run"],"application/x-mie":["mie"],"application/x-mobipocket-ebook":["prc","mobi"],"application/x-ms-application":["application"],"application/x-ms-shortcut":["lnk"],"application/x-ms-wmd":["wmd"],"application/x-ms-wmz":["wmz"],"application/x-ms-xbap":["xbap"],"application/x-msaccess":["mdb"],"application/x-msbinder":["obd"],"application/x-mscardfile":["crd"],"application/x-msclip":["clp"],"application/x-msdos-program":["*exe"],"application/x-msdownload":["*exe","*dll","com","bat","*msi"],"application/x-msmediaview":["mvb","m13","m14"],"application/x-msmetafile":["*wmf","*wmz","*emf","emz"],"application/x-msmoney":["mny"],"application/x-mspublisher":["pub"],"application/x-msschedule":["scd"],"application/x-msterminal":["trm"],"application/x-mswrite":["wri"],"application/x-netcdf":["nc","cdf"],"application/x-ns-proxy-autoconfig":["pac"],"application/x-nzb":["nzb"],"application/x-perl":["pl","pm"],"application/x-pilot":["*prc","*pdb"],"application/x-pkcs12":["p12","pfx"],"application/x-pkcs7-certificates":["p7b","spc"],"application/x-pkcs7-certreqresp":["p7r"],"application/x-rar-compressed":["*rar"],"application/x-redhat-package-manager":["rpm"],"application/x-research-info-systems":["ris"],"application/x-sea":["sea"],"application/x-sh":["sh"],"application/x-shar":["shar"],"application/x-shockwave-flash":["swf"],"application/x-silverlight-app":["xap"],"application/x-sql":["sql"],"application/x-stuffit":["sit"],"application/x-stuffitx":["sitx"],"application/x-subrip":["srt"],"application/x-sv4cpio":["sv4cpio"],"application/x-sv4crc":["sv4crc"],"application/x-t3vm-image":["t3"],"application/x-tads":["gam"],"application/x-tar":["tar"],"application/x-tcl":["tcl","tk"],"application/x-tex":["tex"],"application/x-tex-tfm":["tfm"],"application/x-texinfo":["texinfo","texi"],"application/x-tgif":["*obj"],"application/x-ustar":["ustar"],"application/x-virtualbox-hdd":["hdd"],"application/x-virtualbox-ova":["ova"],"application/x-virtualbox-ovf":["ovf"],"application/x-virtualbox-vbox":["vbox"],"application/x-virtualbox-vbox-extpack":["vbox-extpack"],"application/x-virtualbox-vdi":["vdi"],"application/x-virtualbox-vhd":["vhd"],"application/x-virtualbox-vmdk":["vmdk"],"application/x-wais-source":["src"],"application/x-web-app-manifest+json":["webapp"],"application/x-x509-ca-cert":["der","crt","pem"],"application/x-xfig":["fig"],"application/x-xliff+xml":["*xlf"],"application/x-xpinstall":["xpi"],"application/x-xz":["xz"],"application/x-zmachine":["z1","z2","z3","z4","z5","z6","z7","z8"],"audio/vnd.dece.audio":["uva","uvva"],"audio/vnd.digital-winds":["eol"],"audio/vnd.dra":["dra"],"audio/vnd.dts":["dts"],"audio/vnd.dts.hd":["dtshd"],"audio/vnd.lucent.voice":["lvp"],"audio/vnd.ms-playready.media.pya":["pya"],"audio/vnd.nuera.ecelp4800":["ecelp4800"],"audio/vnd.nuera.ecelp7470":["ecelp7470"],"audio/vnd.nuera.ecelp9600":["ecelp9600"],"audio/vnd.rip":["rip"],"audio/x-aac":["aac"],"audio/x-aiff":["aif","aiff","aifc"],"audio/x-caf":["caf"],"audio/x-flac":["flac"],"audio/x-m4a":["*m4a"],"audio/x-matroska":["mka"],"audio/x-mpegurl":["m3u"],"audio/x-ms-wax":["wax"],"audio/x-ms-wma":["wma"],"audio/x-pn-realaudio":["ram","ra"],"audio/x-pn-realaudio-plugin":["rmp"],"audio/x-realaudio":["*ra"],"audio/x-wav":["*wav"],"chemical/x-cdx":["cdx"],"chemical/x-cif":["cif"],"chemical/x-cmdf":["cmdf"],"chemical/x-cml":["cml"],"chemical/x-csml":["csml"],"chemical/x-xyz":["xyz"],"image/prs.btif":["btif"],"image/prs.pti":["pti"],"image/vnd.adobe.photoshop":["psd"],"image/vnd.airzip.accelerator.azv":["azv"],"image/vnd.dece.graphic":["uvi","uvvi","uvg","uvvg"],"image/vnd.djvu":["djvu","djv"],"image/vnd.dvb.subtitle":["*sub"],"image/vnd.dwg":["dwg"],"image/vnd.dxf":["dxf"],"image/vnd.fastbidsheet":["fbs"],"image/vnd.fpx":["fpx"],"image/vnd.fst":["fst"],"image/vnd.fujixerox.edmics-mmr":["mmr"],"image/vnd.fujixerox.edmics-rlc":["rlc"],"image/vnd.microsoft.icon":["ico"],"image/vnd.ms-dds":["dds"],"image/vnd.ms-modi":["mdi"],"image/vnd.ms-photo":["wdp"],"image/vnd.net-fpx":["npx"],"image/vnd.pco.b16":["b16"],"image/vnd.tencent.tap":["tap"],"image/vnd.valve.source.texture":["vtf"],"image/vnd.wap.wbmp":["wbmp"],"image/vnd.xiff":["xif"],"image/vnd.zbrush.pcx":["pcx"],"image/x-3ds":["3ds"],"image/x-cmu-raster":["ras"],"image/x-cmx":["cmx"],"image/x-freehand":["fh","fhc","fh4","fh5","fh7"],"image/x-icon":["*ico"],"image/x-jng":["jng"],"image/x-mrsid-image":["sid"],"image/x-ms-bmp":["*bmp"],"image/x-pcx":["*pcx"],"image/x-pict":["pic","pct"],"image/x-portable-anymap":["pnm"],"image/x-portable-bitmap":["pbm"],"image/x-portable-graymap":["pgm"],"image/x-portable-pixmap":["ppm"],"image/x-rgb":["rgb"],"image/x-tga":["tga"],"image/x-xbitmap":["xbm"],"image/x-xpixmap":["xpm"],"image/x-xwindowdump":["xwd"],"message/vnd.wfa.wsc":["wsc"],"model/vnd.collada+xml":["dae"],"model/vnd.dwf":["dwf"],"model/vnd.gdl":["gdl"],"model/vnd.gtw":["gtw"],"model/vnd.mts":["mts"],"model/vnd.opengex":["ogex"],"model/vnd.parasolid.transmit.binary":["x_b"],"model/vnd.parasolid.transmit.text":["x_t"],"model/vnd.sap.vds":["vds"],"model/vnd.usdz+zip":["usdz"],"model/vnd.valve.source.compiled-map":["bsp"],"model/vnd.vtu":["vtu"],"text/prs.lines.tag":["dsc"],"text/vnd.curl":["curl"],"text/vnd.curl.dcurl":["dcurl"],"text/vnd.curl.mcurl":["mcurl"],"text/vnd.curl.scurl":["scurl"],"text/vnd.dvb.subtitle":["sub"],"text/vnd.fly":["fly"],"text/vnd.fmi.flexstor":["flx"],"text/vnd.graphviz":["gv"],"text/vnd.in3d.3dml":["3dml"],"text/vnd.in3d.spot":["spot"],"text/vnd.sun.j2me.app-descriptor":["jad"],"text/vnd.wap.wml":["wml"],"text/vnd.wap.wmlscript":["wmls"],"text/x-asm":["s","asm"],"text/x-c":["c","cc","cxx","cpp","h","hh","dic"],"text/x-component":["htc"],"text/x-fortran":["f","for","f77","f90"],"text/x-handlebars-template":["hbs"],"text/x-java-source":["java"],"text/x-lua":["lua"],"text/x-markdown":["mkd"],"text/x-nfo":["nfo"],"text/x-opml":["opml"],"text/x-org":["*org"],"text/x-pascal":["p","pas"],"text/x-processing":["pde"],"text/x-sass":["sass"],"text/x-scss":["scss"],"text/x-setext":["etx"],"text/x-sfv":["sfv"],"text/x-suse-ymp":["ymp"],"text/x-uuencode":["uu"],"text/x-vcalendar":["vcs"],"text/x-vcard":["vcf"],"video/vnd.dece.hd":["uvh","uvvh"],"video/vnd.dece.mobile":["uvm","uvvm"],"video/vnd.dece.pd":["uvp","uvvp"],"video/vnd.dece.sd":["uvs","uvvs"],"video/vnd.dece.video":["uvv","uvvv"],"video/vnd.dvb.file":["dvb"],"video/vnd.fvt":["fvt"],"video/vnd.mpegurl":["mxu","m4u"],"video/vnd.ms-playready.media.pyv":["pyv"],"video/vnd.uvvu.mp4":["uvu","uvvu"],"video/vnd.vivo":["viv"],"video/x-f4v":["f4v"],"video/x-fli":["fli"],"video/x-flv":["flv"],"video/x-m4v":["m4v"],"video/x-matroska":["mkv","mk3d","mks"],"video/x-mng":["mng"],"video/x-ms-asf":["asf","asx"],"video/x-ms-vob":["vob"],"video/x-ms-wm":["wm"],"video/x-ms-wmv":["wmv"],"video/x-ms-wmx":["wmx"],"video/x-ms-wvx":["wvx"],"video/x-msvideo":["avi"],"video/x-sgi-movie":["movie"],"video/x-smv":["smv"],"x-conference/x-cooltalk":["ice"]}},42938:e=>{e.exports={"application/andrew-inset":["ez"],"application/applixware":["aw"],"application/atom+xml":["atom"],"application/atomcat+xml":["atomcat"],"application/atomdeleted+xml":["atomdeleted"],"application/atomsvc+xml":["atomsvc"],"application/atsc-dwd+xml":["dwd"],"application/atsc-held+xml":["held"],"application/atsc-rsat+xml":["rsat"],"application/bdoc":["bdoc"],"application/calendar+xml":["xcs"],"application/ccxml+xml":["ccxml"],"application/cdfx+xml":["cdfx"],"application/cdmi-capability":["cdmia"],"application/cdmi-container":["cdmic"],"application/cdmi-domain":["cdmid"],"application/cdmi-object":["cdmio"],"application/cdmi-queue":["cdmiq"],"application/cu-seeme":["cu"],"application/dash+xml":["mpd"],"application/davmount+xml":["davmount"],"application/docbook+xml":["dbk"],"application/dssc+der":["dssc"],"application/dssc+xml":["xdssc"],"application/ecmascript":["es","ecma"],"application/emma+xml":["emma"],"application/emotionml+xml":["emotionml"],"application/epub+zip":["epub"],"application/exi":["exi"],"application/express":["exp"],"application/fdt+xml":["fdt"],"application/font-tdpfr":["pfr"],"application/geo+json":["geojson"],"application/gml+xml":["gml"],"application/gpx+xml":["gpx"],"application/gxf":["gxf"],"application/gzip":["gz"],"application/hjson":["hjson"],"application/hyperstudio":["stk"],"application/inkml+xml":["ink","inkml"],"application/ipfix":["ipfix"],"application/its+xml":["its"],"application/java-archive":["jar","war","ear"],"application/java-serialized-object":["ser"],"application/java-vm":["class"],"application/javascript":["js","mjs"],"application/json":["json","map"],"application/json5":["json5"],"application/jsonml+json":["jsonml"],"application/ld+json":["jsonld"],"application/lgr+xml":["lgr"],"application/lost+xml":["lostxml"],"application/mac-binhex40":["hqx"],"application/mac-compactpro":["cpt"],"application/mads+xml":["mads"],"application/manifest+json":["webmanifest"],"application/marc":["mrc"],"application/marcxml+xml":["mrcx"],"application/mathematica":["ma","nb","mb"],"application/mathml+xml":["mathml"],"application/mbox":["mbox"],"application/mediaservercontrol+xml":["mscml"],"application/metalink+xml":["metalink"],"application/metalink4+xml":["meta4"],"application/mets+xml":["mets"],"application/mmt-aei+xml":["maei"],"application/mmt-usd+xml":["musd"],"application/mods+xml":["mods"],"application/mp21":["m21","mp21"],"application/mp4":["mp4s","m4p"],"application/msword":["doc","dot"],"application/mxf":["mxf"],"application/n-quads":["nq"],"application/n-triples":["nt"],"application/node":["cjs"],"application/octet-stream":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"],"application/oda":["oda"],"application/oebps-package+xml":["opf"],"application/ogg":["ogx"],"application/omdoc+xml":["omdoc"],"application/onenote":["onetoc","onetoc2","onetmp","onepkg"],"application/oxps":["oxps"],"application/p2p-overlay+xml":["relo"],"application/patch-ops-error+xml":["xer"],"application/pdf":["pdf"],"application/pgp-encrypted":["pgp"],"application/pgp-signature":["asc","sig"],"application/pics-rules":["prf"],"application/pkcs10":["p10"],"application/pkcs7-mime":["p7m","p7c"],"application/pkcs7-signature":["p7s"],"application/pkcs8":["p8"],"application/pkix-attr-cert":["ac"],"application/pkix-cert":["cer"],"application/pkix-crl":["crl"],"application/pkix-pkipath":["pkipath"],"application/pkixcmp":["pki"],"application/pls+xml":["pls"],"application/postscript":["ai","eps","ps"],"application/provenance+xml":["provx"],"application/pskc+xml":["pskcxml"],"application/raml+yaml":["raml"],"application/rdf+xml":["rdf","owl"],"application/reginfo+xml":["rif"],"application/relax-ng-compact-syntax":["rnc"],"application/resource-lists+xml":["rl"],"application/resource-lists-diff+xml":["rld"],"application/rls-services+xml":["rs"],"application/route-apd+xml":["rapd"],"application/route-s-tsid+xml":["sls"],"application/route-usd+xml":["rusd"],"application/rpki-ghostbusters":["gbr"],"application/rpki-manifest":["mft"],"application/rpki-roa":["roa"],"application/rsd+xml":["rsd"],"application/rss+xml":["rss"],"application/rtf":["rtf"],"application/sbml+xml":["sbml"],"application/scvp-cv-request":["scq"],"application/scvp-cv-response":["scs"],"application/scvp-vp-request":["spq"],"application/scvp-vp-response":["spp"],"application/sdp":["sdp"],"application/senml+xml":["senmlx"],"application/sensml+xml":["sensmlx"],"application/set-payment-initiation":["setpay"],"application/set-registration-initiation":["setreg"],"application/shf+xml":["shf"],"application/sieve":["siv","sieve"],"application/smil+xml":["smi","smil"],"application/sparql-query":["rq"],"application/sparql-results+xml":["srx"],"application/srgs":["gram"],"application/srgs+xml":["grxml"],"application/sru+xml":["sru"],"application/ssdl+xml":["ssdl"],"application/ssml+xml":["ssml"],"application/swid+xml":["swidtag"],"application/tei+xml":["tei","teicorpus"],"application/thraud+xml":["tfi"],"application/timestamped-data":["tsd"],"application/toml":["toml"],"application/trig":["trig"],"application/ttml+xml":["ttml"],"application/ubjson":["ubj"],"application/urc-ressheet+xml":["rsheet"],"application/urc-targetdesc+xml":["td"],"application/voicexml+xml":["vxml"],"application/wasm":["wasm"],"application/widget":["wgt"],"application/winhlp":["hlp"],"application/wsdl+xml":["wsdl"],"application/wspolicy+xml":["wspolicy"],"application/xaml+xml":["xaml"],"application/xcap-att+xml":["xav"],"application/xcap-caps+xml":["xca"],"application/xcap-diff+xml":["xdf"],"application/xcap-el+xml":["xel"],"application/xcap-ns+xml":["xns"],"application/xenc+xml":["xenc"],"application/xhtml+xml":["xhtml","xht"],"application/xliff+xml":["xlf"],"application/xml":["xml","xsl","xsd","rng"],"application/xml-dtd":["dtd"],"application/xop+xml":["xop"],"application/xproc+xml":["xpl"],"application/xslt+xml":["*xsl","xslt"],"application/xspf+xml":["xspf"],"application/xv+xml":["mxml","xhvml","xvml","xvm"],"application/yang":["yang"],"application/yin+xml":["yin"],"application/zip":["zip"],"audio/3gpp":["*3gpp"],"audio/adpcm":["adp"],"audio/amr":["amr"],"audio/basic":["au","snd"],"audio/midi":["mid","midi","kar","rmi"],"audio/mobile-xmf":["mxmf"],"audio/mp3":["*mp3"],"audio/mp4":["m4a","mp4a"],"audio/mpeg":["mpga","mp2","mp2a","mp3","m2a","m3a"],"audio/ogg":["oga","ogg","spx","opus"],"audio/s3m":["s3m"],"audio/silk":["sil"],"audio/wav":["wav"],"audio/wave":["*wav"],"audio/webm":["weba"],"audio/xm":["xm"],"font/collection":["ttc"],"font/otf":["otf"],"font/ttf":["ttf"],"font/woff":["woff"],"font/woff2":["woff2"],"image/aces":["exr"],"image/apng":["apng"],"image/avif":["avif"],"image/bmp":["bmp"],"image/cgm":["cgm"],"image/dicom-rle":["drle"],"image/emf":["emf"],"image/fits":["fits"],"image/g3fax":["g3"],"image/gif":["gif"],"image/heic":["heic"],"image/heic-sequence":["heics"],"image/heif":["heif"],"image/heif-sequence":["heifs"],"image/hej2k":["hej2"],"image/hsj2":["hsj2"],"image/ief":["ief"],"image/jls":["jls"],"image/jp2":["jp2","jpg2"],"image/jpeg":["jpeg","jpg","jpe"],"image/jph":["jph"],"image/jphc":["jhc"],"image/jpm":["jpm"],"image/jpx":["jpx","jpf"],"image/jxr":["jxr"],"image/jxra":["jxra"],"image/jxrs":["jxrs"],"image/jxs":["jxs"],"image/jxsc":["jxsc"],"image/jxsi":["jxsi"],"image/jxss":["jxss"],"image/ktx":["ktx"],"image/ktx2":["ktx2"],"image/png":["png"],"image/sgi":["sgi"],"image/svg+xml":["svg","svgz"],"image/t38":["t38"],"image/tiff":["tif","tiff"],"image/tiff-fx":["tfx"],"image/webp":["webp"],"image/wmf":["wmf"],"message/disposition-notification":["disposition-notification"],"message/global":["u8msg"],"message/global-delivery-status":["u8dsn"],"message/global-disposition-notification":["u8mdn"],"message/global-headers":["u8hdr"],"message/rfc822":["eml","mime"],"model/3mf":["3mf"],"model/gltf+json":["gltf"],"model/gltf-binary":["glb"],"model/iges":["igs","iges"],"model/mesh":["msh","mesh","silo"],"model/mtl":["mtl"],"model/obj":["obj"],"model/step+xml":["stpx"],"model/step+zip":["stpz"],"model/step-xml+zip":["stpxz"],"model/stl":["stl"],"model/vrml":["wrl","vrml"],"model/x3d+binary":["*x3db","x3dbz"],"model/x3d+fastinfoset":["x3db"],"model/x3d+vrml":["*x3dv","x3dvz"],"model/x3d+xml":["x3d","x3dz"],"model/x3d-vrml":["x3dv"],"text/cache-manifest":["appcache","manifest"],"text/calendar":["ics","ifb"],"text/coffeescript":["coffee","litcoffee"],"text/css":["css"],"text/csv":["csv"],"text/html":["html","htm","shtml"],"text/jade":["jade"],"text/jsx":["jsx"],"text/less":["less"],"text/markdown":["markdown","md"],"text/mathml":["mml"],"text/mdx":["mdx"],"text/n3":["n3"],"text/plain":["txt","text","conf","def","list","log","in","ini"],"text/richtext":["rtx"],"text/rtf":["*rtf"],"text/sgml":["sgml","sgm"],"text/shex":["shex"],"text/slim":["slim","slm"],"text/spdx":["spdx"],"text/stylus":["stylus","styl"],"text/tab-separated-values":["tsv"],"text/troff":["t","tr","roff","man","me","ms"],"text/turtle":["ttl"],"text/uri-list":["uri","uris","urls"],"text/vcard":["vcard"],"text/vtt":["vtt"],"text/xml":["*xml"],"text/yaml":["yaml","yml"],"video/3gpp":["3gp","3gpp"],"video/3gpp2":["3g2"],"video/h261":["h261"],"video/h263":["h263"],"video/h264":["h264"],"video/iso.segment":["m4s"],"video/jpeg":["jpgv"],"video/jpm":["*jpm","jpgm"],"video/mj2":["mj2","mjp2"],"video/mp2t":["ts"],"video/mp4":["mp4","mp4v","mpg4"],"video/mpeg":["mpeg","mpg","mpe","m1v","m2v"],"video/ogg":["ogv"],"video/quicktime":["qt","mov"],"video/webm":["webm"]}},54686:e=>{function t(e,t,a,i){return Math.round(e/a)+" "+i+(t>=1.5*a?"s":"")}e.exports=function(e,a){a=a||{};var i,n,o=typeof e;if("string"===o&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var a=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*a;case"weeks":case"week":case"w":return 6048e5*a;case"days":case"day":case"d":return 864e5*a;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*a;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*a;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*a;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return a;default:return}}}}(e);if("number"===o&&isFinite(e))return a.long?(i=Math.abs(e))>=864e5?t(e,i,864e5,"day"):i>=36e5?t(e,i,36e5,"hour"):i>=6e4?t(e,i,6e4,"minute"):i>=1e3?t(e,i,1e3,"second"):e+" ms":(n=Math.abs(e))>=864e5?Math.round(e/864e5)+"d":n>=36e5?Math.round(e/36e5)+"h":n>=6e4?Math.round(e/6e4)+"m":n>=1e3?Math.round(e/1e3)+"s":e+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},60707:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{resolveManifest:function(){return s},resolveRobots:function(){return n},resolveRouteData:function(){return r},resolveSitemap:function(){return o}});let i=a(91389);function n(e){let t="";for(let a of Array.isArray(e.rules)?e.rules:[e.rules]){for(let e of(0,i.resolveArray)(a.userAgent||["*"]))t+=`User-Agent: ${e}
`;if(a.allow)for(let e of(0,i.resolveArray)(a.allow))t+=`Allow: ${e}
`;if(a.disallow)for(let e of(0,i.resolveArray)(a.disallow))t+=`Disallow: ${e}
`;a.crawlDelay&&(t+=`Crawl-delay: ${a.crawlDelay}
`),t+="\n"}return e.host&&(t+=`Host: ${e.host}
`),e.sitemap&&(0,i.resolveArray)(e.sitemap).forEach(e=>{t+=`Sitemap: ${e}
`}),t}function o(e){let t=e.some(e=>Object.keys(e.alternates??{}).length>0),a="";for(let n of(a+='<?xml version="1.0" encoding="UTF-8"?>\n<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"',t?a+=' xmlns:xhtml="http://www.w3.org/1999/xhtml">\n':a+=">\n",e)){var i;a+=`<url>
<loc>${n.url}</loc>
`;let e=null==(i=n.alternates)?void 0:i.languages;if(e&&Object.keys(e).length)for(let t in e)a+=`<xhtml:link rel="alternate" hreflang="${t}" href="${e[t]}" />
`;if(n.lastModified){let e=n.lastModified instanceof Date?n.lastModified.toISOString():n.lastModified;a+=`<lastmod>${e}</lastmod>
`}n.changeFrequency&&(a+=`<changefreq>${n.changeFrequency}</changefreq>
`),"number"==typeof n.priority&&(a+=`<priority>${n.priority}</priority>
`),a+="</url>\n"}return a+"</urlset>\n"}function s(e){return JSON.stringify(e)}function r(e,t){return"robots"===t?n(e):"sitemap"===t?o(e):"manifest"===t?s(e):""}},91389:(e,t)=>{"use strict";function a(e){return Array.isArray(e)?e:[e]}function i(e){if(null!=e)return a(e)}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{resolveArray:function(){return a},resolveAsArrayOrUndefined:function(){return i}})},53582:(e,t,a)=>{var i="function"==typeof Map&&Map.prototype,n=Object.getOwnPropertyDescriptor&&i?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,o=i&&n&&"function"==typeof n.get?n.get:null,s=i&&Map.prototype.forEach,r="function"==typeof Set&&Set.prototype,p=Object.getOwnPropertyDescriptor&&r?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,c=r&&p&&"function"==typeof p.get?p.get:null,l=r&&Set.prototype.forEach,u="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,d="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,m="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,f=Boolean.prototype.valueOf,h=Object.prototype.toString,x=Function.prototype.toString,v=String.prototype.match,g=String.prototype.slice,b=String.prototype.replace,y=String.prototype.toUpperCase,w=String.prototype.toLowerCase,_=RegExp.prototype.test,k=Array.prototype.concat,E=Array.prototype.join,j=Array.prototype.slice,T=Math.floor,S="function"==typeof BigInt?BigInt.prototype.valueOf:null,A=Object.getOwnPropertySymbols,O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,R="function"==typeof Symbol&&"object"==typeof Symbol.iterator,C="function"==typeof Symbol&&Symbol.toStringTag&&(typeof Symbol.toStringTag===R?"object":"symbol")?Symbol.toStringTag:null,P=Object.prototype.propertyIsEnumerable,z=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function L(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||_.call(/e/,t))return t;var a=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var i=e<0?-T(-e):T(e);if(i!==e){var n=String(i),o=g.call(t,n.length+1);return b.call(n,a,"$&_")+"."+b.call(b.call(o,/([0-9]{3})/g,"$&_"),/_$/,"")}}return b.call(t,a,"$&_")}var D=a(11904),F=D.custom,q=H(F)?F:null,U={__proto__:null,double:'"',single:"'"},B={__proto__:null,double:/(["\\])/g,single:/(['\\])/g};function N(e,t,a){var i=U[a.quoteStyle||t];return i+e+i}function I(e){return"[object Array]"===W(e)&&(!C||!("object"==typeof e&&C in e))}function M(e){return"[object RegExp]"===W(e)&&(!C||!("object"==typeof e&&C in e))}function H(e){if(R)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!O)return!1;try{return O.call(e),!0}catch(e){}return!1}e.exports=function e(t,a,i,n){var r=a||{};if($(r,"quoteStyle")&&!$(U,r.quoteStyle))throw TypeError('option "quoteStyle" must be "single" or "double"');if($(r,"maxStringLength")&&("number"==typeof r.maxStringLength?r.maxStringLength<0&&r.maxStringLength!==1/0:null!==r.maxStringLength))throw TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var p=!$(r,"customInspect")||r.customInspect;if("boolean"!=typeof p&&"symbol"!==p)throw TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if($(r,"indent")&&null!==r.indent&&"	"!==r.indent&&!(parseInt(r.indent,10)===r.indent&&r.indent>0))throw TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if($(r,"numericSeparator")&&"boolean"!=typeof r.numericSeparator)throw TypeError('option "numericSeparator", if provided, must be `true` or `false`');var h=r.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return function e(t,a){if(t.length>a.maxStringLength){var i=t.length-a.maxStringLength;return e(g.call(t,0,a.maxStringLength),a)+"... "+i+" more character"+(i>1?"s":"")}var n=B[a.quoteStyle||"single"];return n.lastIndex=0,N(b.call(b.call(t,n,"\\$1"),/[\x00-\x1f]/g,V),"single",a)}(t,r);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var y=String(t);return h?L(t,y):y}if("bigint"==typeof t){var _=String(t)+"n";return h?L(t,_):_}var T=void 0===r.depth?5:r.depth;if(void 0===i&&(i=0),i>=T&&T>0&&"object"==typeof t)return I(t)?"[Array]":"[Object]";var A=function(e,t){var a;if("	"===e.indent)a="	";else{if("number"!=typeof e.indent||!(e.indent>0))return null;a=E.call(Array(e.indent+1)," ")}return{base:a,prev:E.call(Array(t+1),a)}}(r,i);if(void 0===n)n=[];else if(K(n,t)>=0)return"[Circular]";function F(t,a,o){if(a&&(n=j.call(n)).push(a),o){var s={depth:r.depth};return $(r,"quoteStyle")&&(s.quoteStyle=r.quoteStyle),e(t,s,i+1,n)}return e(t,r,i+1,n)}if("function"==typeof t&&!M(t)){var G=function(e){if(e.name)return e.name;var t=v.call(x.call(e),/^function\s*([\w$]+)/);return t?t[1]:null}(t),ee=Z(t,F);return"[Function"+(G?": "+G:" (anonymous)")+"]"+(ee.length>0?" { "+E.call(ee,", ")+" }":"")}if(H(t)){var et=R?b.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):O.call(t);return"object"!=typeof t||R?et:J(et)}if(t&&"object"==typeof t&&("undefined"!=typeof HTMLElement&&t instanceof HTMLElement||"string"==typeof t.nodeName&&"function"==typeof t.getAttribute)){for(var ea,ei="<"+w.call(String(t.nodeName)),en=t.attributes||[],eo=0;eo<en.length;eo++)ei+=" "+en[eo].name+"="+N((ea=en[eo].value,b.call(String(ea),/"/g,"&quot;")),"double",r);return ei+=">",t.childNodes&&t.childNodes.length&&(ei+="..."),ei+="</"+w.call(String(t.nodeName))+">"}if(I(t)){if(0===t.length)return"[]";var es=Z(t,F);return A&&!function(e){for(var t=0;t<e.length;t++)if(K(e[t],"\n")>=0)return!1;return!0}(es)?"["+X(es,A)+"]":"[ "+E.call(es,", ")+" ]"}if("[object Error]"===W(t)&&(!C||!("object"==typeof t&&C in t))){var er=Z(t,F);return"cause"in Error.prototype||!("cause"in t)||P.call(t,"cause")?0===er.length?"["+String(t)+"]":"{ ["+String(t)+"] "+E.call(er,", ")+" }":"{ ["+String(t)+"] "+E.call(k.call("[cause]: "+F(t.cause),er),", ")+" }"}if("object"==typeof t&&p){if(q&&"function"==typeof t[q]&&D)return D(t,{depth:T-i});if("symbol"!==p&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!o||!e||"object"!=typeof e)return!1;try{o.call(e);try{c.call(e)}catch(e){return!0}return e instanceof Map}catch(e){}return!1}(t)){var ep=[];return s&&s.call(t,function(e,a){ep.push(F(a,t,!0)+" => "+F(e,t))}),Q("Map",o.call(t),ep,A)}if(function(e){if(!c||!e||"object"!=typeof e)return!1;try{c.call(e);try{o.call(e)}catch(e){return!0}return e instanceof Set}catch(e){}return!1}(t)){var ec=[];return l&&l.call(t,function(e){ec.push(F(e,t))}),Q("Set",c.call(t),ec,A)}if(function(e){if(!u||!e||"object"!=typeof e)return!1;try{u.call(e,u);try{d.call(e,d)}catch(e){return!0}return e instanceof WeakMap}catch(e){}return!1}(t))return Y("WeakMap");if(function(e){if(!d||!e||"object"!=typeof e)return!1;try{d.call(e,d);try{u.call(e,u)}catch(e){return!0}return e instanceof WeakSet}catch(e){}return!1}(t))return Y("WeakSet");if(function(e){if(!m||!e||"object"!=typeof e)return!1;try{return m.call(e),!0}catch(e){}return!1}(t))return Y("WeakRef");if("[object Number]"===W(t)&&(!C||!("object"==typeof t&&C in t)))return J(F(Number(t)));if(function(e){if(!e||"object"!=typeof e||!S)return!1;try{return S.call(e),!0}catch(e){}return!1}(t))return J(F(S.call(t)));if("[object Boolean]"===W(t)&&(!C||!("object"==typeof t&&C in t)))return J(f.call(t));if("[object String]"===W(t)&&(!C||!("object"==typeof t&&C in t)))return J(F(String(t)));if("undefined"!=typeof window&&t===window)return"{ [object Window] }";if("undefined"!=typeof globalThis&&t===globalThis||"undefined"!=typeof global&&t===global)return"{ [object globalThis] }";if(!("[object Date]"===W(t)&&(!C||!("object"==typeof t&&C in t)))&&!M(t)){var el=Z(t,F),eu=z?z(t)===Object.prototype:t instanceof Object||t.constructor===Object,ed=t instanceof Object?"":"null prototype",em=!eu&&C&&Object(t)===t&&C in t?g.call(W(t),8,-1):ed?"Object":"",ef=(eu||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(em||ed?"["+E.call(k.call([],em||[],ed||[]),": ")+"] ":"");return 0===el.length?ef+"{}":A?ef+"{"+X(el,A)+"}":ef+"{ "+E.call(el,", ")+" }"}return String(t)};var G=Object.prototype.hasOwnProperty||function(e){return e in this};function $(e,t){return G.call(e,t)}function W(e){return h.call(e)}function K(e,t){if(e.indexOf)return e.indexOf(t);for(var a=0,i=e.length;a<i;a++)if(e[a]===t)return a;return -1}function V(e){var t=e.charCodeAt(0),a={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return a?"\\"+a:"\\x"+(t<16?"0":"")+y.call(t.toString(16))}function J(e){return"Object("+e+")"}function Y(e){return e+" { ? }"}function Q(e,t,a,i){return e+" ("+t+") {"+(i?X(a,i):E.call(a,", "))+"}"}function X(e,t){if(0===e.length)return"";var a="\n"+t.prev+t.base;return a+E.call(e,","+a)+"\n"+t.prev}function Z(e,t){var a,i=I(e),n=[];if(i){n.length=e.length;for(var o=0;o<e.length;o++)n[o]=$(e,o)?t(e[o],e):""}var s="function"==typeof A?A(e):[];if(R){a={};for(var r=0;r<s.length;r++)a["$"+s[r]]=s[r]}for(var p in e)$(e,p)&&(!i||String(Number(p))!==p||!(p<e.length))&&(R&&a["$"+p]instanceof Symbol||(_.call(/[^\w$]/,p)?n.push(t(p,e)+": "+t(e[p],e)):n.push(p+": "+t(e[p],e))));if("function"==typeof A)for(var c=0;c<s.length;c++)P.call(e,s[c])&&n.push("["+t(s[c])+"]: "+t(e[s[c]],e));return n}},11904:(e,t,a)=>{e.exports=a(21764).inspect},12027:(e,t,a)=>{"use strict";var i=a(17360).parse,n={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},o=String.prototype.endsWith||function(e){return e.length<=this.length&&-1!==this.indexOf(e,this.length-e.length)};function s(e){return process.env[e.toLowerCase()]||process.env[e.toUpperCase()]||""}t.getProxyForUrl=function(e){var t,a,r,p="string"==typeof e?i(e):e||{},c=p.protocol,l=p.host,u=p.port;if("string"!=typeof l||!l||"string"!=typeof c||(c=c.split(":",1)[0],t=l=l.replace(/:\d*$/,""),a=u=parseInt(u)||n[c]||0,!(!(r=(s("npm_config_no_proxy")||s("no_proxy")).toLowerCase())||"*"!==r&&r.split(/[,\s]/).every(function(e){if(!e)return!0;var i=e.match(/^(.+):(\d+)$/),n=i?i[1]:e,s=i?parseInt(i[2]):0;return!!s&&s!==a||(/^[.*]/.test(n)?("*"===n.charAt(0)&&(n=n.slice(1)),!o.call(t,n)):t!==n)}))))return"";var d=s("npm_config_"+c+"_proxy")||s(c+"_proxy")||s("npm_config_proxy")||s("all_proxy");return d&&-1===d.indexOf("://")&&(d=c+"://"+d),d}},5489:e=>{"use strict";var t=String.prototype.replace,a=/%20/g,i={RFC1738:"RFC1738",RFC3986:"RFC3986"};e.exports={default:i.RFC3986,formatters:{RFC1738:function(e){return t.call(e,a,"+")},RFC3986:function(e){return String(e)}},RFC1738:i.RFC1738,RFC3986:i.RFC3986}},92993:(e,t,a)=>{"use strict";var i=a(29071),n=a(75362),o=a(5489);e.exports={formats:o,parse:n,stringify:i}},75362:(e,t,a)=>{"use strict";var i=a(52303),n=Object.prototype.hasOwnProperty,o=Array.isArray,s={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:i.decode,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictDepth:!1,strictNullHandling:!1},r=function(e,t){return e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1?e.split(","):e},p=function(e,t){var a={__proto__:null},p=t.ignoreQueryPrefix?e.replace(/^\?/,""):e;p=p.replace(/%5B/gi,"[").replace(/%5D/gi,"]");var c=t.parameterLimit===1/0?void 0:t.parameterLimit,l=p.split(t.delimiter,c),u=-1,d=t.charset;if(t.charsetSentinel)for(m=0;m<l.length;++m)0===l[m].indexOf("utf8=")&&("utf8=%E2%9C%93"===l[m]?d="utf-8":"utf8=%26%2310003%3B"===l[m]&&(d="iso-8859-1"),u=m,m=l.length);for(m=0;m<l.length;++m)if(m!==u){var m,f,h,x=l[m],v=x.indexOf("]="),g=-1===v?x.indexOf("="):v+1;-1===g?(f=t.decoder(x,s.decoder,d,"key"),h=t.strictNullHandling?null:""):(f=t.decoder(x.slice(0,g),s.decoder,d,"key"),h=i.maybeMap(r(x.slice(g+1),t),function(e){return t.decoder(e,s.decoder,d,"value")})),h&&t.interpretNumericEntities&&"iso-8859-1"===d&&(h=String(h).replace(/&#(\d+);/g,function(e,t){return String.fromCharCode(parseInt(t,10))})),x.indexOf("[]=")>-1&&(h=o(h)?[h]:h);var b=n.call(a,f);b&&"combine"===t.duplicates?a[f]=i.combine(a[f],h):b&&"last"!==t.duplicates||(a[f]=h)}return a},c=function(e,t,a,i){for(var n=i?t:r(t,a),o=e.length-1;o>=0;--o){var s,p=e[o];if("[]"===p&&a.parseArrays)s=a.allowEmptyArrays&&(""===n||a.strictNullHandling&&null===n)?[]:[].concat(n);else{s=a.plainObjects?{__proto__:null}:{};var c="["===p.charAt(0)&&"]"===p.charAt(p.length-1)?p.slice(1,-1):p,l=a.decodeDotInKeys?c.replace(/%2E/g,"."):c,u=parseInt(l,10);a.parseArrays||""!==l?!isNaN(u)&&p!==l&&String(u)===l&&u>=0&&a.parseArrays&&u<=a.arrayLimit?(s=[])[u]=n:"__proto__"!==l&&(s[l]=n):s={0:n}}n=s}return n},l=function(e,t,a,i){if(e){var o=a.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,s=/(\[[^[\]]*])/g,r=a.depth>0&&/(\[[^[\]]*])/.exec(o),p=r?o.slice(0,r.index):o,l=[];if(p){if(!a.plainObjects&&n.call(Object.prototype,p)&&!a.allowPrototypes)return;l.push(p)}for(var u=0;a.depth>0&&null!==(r=s.exec(o))&&u<a.depth;){if(u+=1,!a.plainObjects&&n.call(Object.prototype,r[1].slice(1,-1))&&!a.allowPrototypes)return;l.push(r[1])}if(r){if(!0===a.strictDepth)throw RangeError("Input depth exceeded depth option of "+a.depth+" and strictDepth is true");l.push("["+o.slice(r.index)+"]")}return c(l,t,a,i)}},u=function(e){if(!e)return s;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.decodeDotInKeys&&"boolean"!=typeof e.decodeDotInKeys)throw TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var t=void 0===e.charset?s.charset:e.charset,a=void 0===e.duplicates?s.duplicates:e.duplicates;if("combine"!==a&&"first"!==a&&"last"!==a)throw TypeError("The duplicates option must be either combine, first, or last");return{allowDots:void 0===e.allowDots?!0===e.decodeDotInKeys||s.allowDots:!!e.allowDots,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:s.allowEmptyArrays,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:s.allowPrototypes,allowSparse:"boolean"==typeof e.allowSparse?e.allowSparse:s.allowSparse,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:s.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:s.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:s.comma,decodeDotInKeys:"boolean"==typeof e.decodeDotInKeys?e.decodeDotInKeys:s.decodeDotInKeys,decoder:"function"==typeof e.decoder?e.decoder:s.decoder,delimiter:"string"==typeof e.delimiter||i.isRegExp(e.delimiter)?e.delimiter:s.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:s.depth,duplicates:a,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:s.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:s.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:s.plainObjects,strictDepth:"boolean"==typeof e.strictDepth?!!e.strictDepth:s.strictDepth,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:s.strictNullHandling}};e.exports=function(e,t){var a=u(t);if(""===e||null==e)return a.plainObjects?{__proto__:null}:{};for(var n="string"==typeof e?p(e,a):e,o=a.plainObjects?{__proto__:null}:{},s=Object.keys(n),r=0;r<s.length;++r){var c=s[r],d=l(c,n[c],a,"string"==typeof e);o=i.merge(o,d,a)}return!0===a.allowSparse?o:i.compact(o)}},29071:(e,t,a)=>{"use strict";var i=a(11284),n=a(52303),o=a(5489),s=Object.prototype.hasOwnProperty,r={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},p=Array.isArray,c=Array.prototype.push,l=function(e,t){c.apply(e,p(t)?t:[t])},u=Date.prototype.toISOString,d=o.default,m={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,commaRoundTrip:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:n.encode,encodeValuesOnly:!1,filter:void 0,format:d,formatter:o.formatters[d],indices:!1,serializeDate:function(e){return u.call(e)},skipNulls:!1,strictNullHandling:!1},f={},h=function e(t,a,o,s,r,c,u,d,h,x,v,g,b,y,w,_,k,E){for(var j,T,S=t,A=E,O=0,R=!1;void 0!==(A=A.get(f))&&!R;){var C=A.get(t);if(O+=1,void 0!==C){if(C===O)throw RangeError("Cyclic object value");R=!0}void 0===A.get(f)&&(O=0)}if("function"==typeof x?S=x(a,S):S instanceof Date?S=b(S):"comma"===o&&p(S)&&(S=n.maybeMap(S,function(e){return e instanceof Date?b(e):e})),null===S){if(c)return h&&!_?h(a,m.encoder,k,"key",y):a;S=""}if("string"==typeof(j=S)||"number"==typeof j||"boolean"==typeof j||"symbol"==typeof j||"bigint"==typeof j||n.isBuffer(S))return h?[w(_?a:h(a,m.encoder,k,"key",y))+"="+w(h(S,m.encoder,k,"value",y))]:[w(a)+"="+w(String(S))];var P=[];if(void 0===S)return P;if("comma"===o&&p(S))_&&h&&(S=n.maybeMap(S,h)),T=[{value:S.length>0?S.join(",")||null:void 0}];else if(p(x))T=x;else{var z=Object.keys(S);T=v?z.sort(v):z}var L=d?String(a).replace(/\./g,"%2E"):String(a),D=s&&p(S)&&1===S.length?L+"[]":L;if(r&&p(S)&&0===S.length)return D+"[]";for(var F=0;F<T.length;++F){var q=T[F],U="object"==typeof q&&q&&void 0!==q.value?q.value:S[q];if(!u||null!==U){var B=g&&d?String(q).replace(/\./g,"%2E"):String(q),N=p(S)?"function"==typeof o?o(D,B):D:D+(g?"."+B:"["+B+"]");E.set(t,O);var I=i();I.set(f,E),l(P,e(U,N,o,s,r,c,u,d,"comma"===o&&_&&p(S)?null:h,x,v,g,b,y,w,_,k,I))}}return P},x=function(e){if(!e)return m;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");var t,a=e.charset||m.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var i=o.default;if(void 0!==e.format){if(!s.call(o.formatters,e.format))throw TypeError("Unknown format option provided.");i=e.format}var n=o.formatters[i],c=m.filter;if(("function"==typeof e.filter||p(e.filter))&&(c=e.filter),t=e.arrayFormat in r?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":m.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");var l=void 0===e.allowDots?!0===e.encodeDotInKeys||m.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:m.addQueryPrefix,allowDots:l,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:m.allowEmptyArrays,arrayFormat:t,charset:a,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:m.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?m.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:m.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:m.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:m.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:m.encodeValuesOnly,filter:c,format:i,formatter:n,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:m.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:m.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:m.strictNullHandling}};e.exports=function(e,t){var a,n=e,o=x(t);"function"==typeof o.filter?n=(0,o.filter)("",n):p(o.filter)&&(a=o.filter);var s=[];if("object"!=typeof n||null===n)return"";var c=r[o.arrayFormat],u="comma"===c&&o.commaRoundTrip;a||(a=Object.keys(n)),o.sort&&a.sort(o.sort);for(var d=i(),m=0;m<a.length;++m){var f=a[m],v=n[f];o.skipNulls&&null===v||l(s,h(v,f,c,u,o.allowEmptyArrays,o.strictNullHandling,o.skipNulls,o.encodeDotInKeys,o.encode?o.encoder:null,o.filter,o.sort,o.allowDots,o.serializeDate,o.format,o.formatter,o.encodeValuesOnly,o.charset,d))}var g=s.join(o.delimiter),b=!0===o.addQueryPrefix?"?":"";return o.charsetSentinel&&("iso-8859-1"===o.charset?b+="utf8=%26%2310003%3B&":b+="utf8=%E2%9C%93&"),g.length>0?b+g:""}},52303:(e,t,a)=>{"use strict";var i=a(5489),n=Object.prototype.hasOwnProperty,o=Array.isArray,s=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),r=function(e){for(;e.length>1;){var t=e.pop(),a=t.obj[t.prop];if(o(a)){for(var i=[],n=0;n<a.length;++n)void 0!==a[n]&&i.push(a[n]);t.obj[t.prop]=i}}},p=function(e,t){for(var a=t&&t.plainObjects?{__proto__:null}:{},i=0;i<e.length;++i)void 0!==e[i]&&(a[i]=e[i]);return a};e.exports={arrayToObject:p,assign:function(e,t){return Object.keys(t).reduce(function(e,a){return e[a]=t[a],e},e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],a=[],i=0;i<t.length;++i)for(var n=t[i],o=n.obj[n.prop],s=Object.keys(o),p=0;p<s.length;++p){var c=s[p],l=o[c];"object"==typeof l&&null!==l&&-1===a.indexOf(l)&&(t.push({obj:o,prop:c}),a.push(l))}return r(t),e},decode:function(e,t,a){var i=e.replace(/\+/g," ");if("iso-8859-1"===a)return i.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(i)}catch(e){return i}},encode:function(e,t,a,n,o){if(0===e.length)return e;var r=e;if("symbol"==typeof e?r=Symbol.prototype.toString.call(e):"string"!=typeof e&&(r=String(e)),"iso-8859-1"===a)return escape(r).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});for(var p="",c=0;c<r.length;c+=1024){for(var l=r.length>=1024?r.slice(c,c+1024):r,u=[],d=0;d<l.length;++d){var m=l.charCodeAt(d);if(45===m||46===m||95===m||126===m||m>=48&&m<=57||m>=65&&m<=90||m>=97&&m<=122||o===i.RFC1738&&(40===m||41===m)){u[u.length]=l.charAt(d);continue}if(m<128){u[u.length]=s[m];continue}if(m<2048){u[u.length]=s[192|m>>6]+s[128|63&m];continue}if(m<55296||m>=57344){u[u.length]=s[224|m>>12]+s[128|m>>6&63]+s[128|63&m];continue}d+=1,m=65536+((1023&m)<<10|1023&l.charCodeAt(d)),u[u.length]=s[240|m>>18]+s[128|m>>12&63]+s[128|m>>6&63]+s[128|63&m]}p+=u.join("")}return p},isBuffer:function(e){return!!e&&"object"==typeof e&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(o(e)){for(var a=[],i=0;i<e.length;i+=1)a.push(t(e[i]));return a}return t(e)},merge:function e(t,a,i){if(!a)return t;if("object"!=typeof a&&"function"!=typeof a){if(o(t))t.push(a);else{if(!t||"object"!=typeof t)return[t,a];(i&&(i.plainObjects||i.allowPrototypes)||!n.call(Object.prototype,a))&&(t[a]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(a);var s=t;return(o(t)&&!o(a)&&(s=p(t,i)),o(t)&&o(a))?(a.forEach(function(a,o){if(n.call(t,o)){var s=t[o];s&&"object"==typeof s&&a&&"object"==typeof a?t[o]=e(s,a,i):t.push(a)}else t[o]=a}),t):Object.keys(a).reduce(function(t,o){var s=a[o];return n.call(t,o)?t[o]=e(t[o],s,i):t[o]=s,t},s)}}},65926:(e,t,a)=>{var i=a(78893),n=i.Buffer;function o(e,t){for(var a in e)t[a]=e[a]}function s(e,t,a){return n(e,t,a)}n.from&&n.alloc&&n.allocUnsafe&&n.allocUnsafeSlow?e.exports=i:(o(i,t),t.Buffer=s),s.prototype=Object.create(n.prototype),o(n,s),s.from=function(e,t,a){if("number"==typeof e)throw TypeError("Argument must not be a number");return n(e,t,a)},s.alloc=function(e,t,a){if("number"!=typeof e)throw TypeError("Argument must be a number");var i=n(e);return void 0!==t?"string"==typeof a?i.fill(t,a):i.fill(t):i.fill(0),i},s.allocUnsafe=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return n(e)},s.allocUnsafeSlow=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return i.SlowBuffer(e)}},547:(e,t,a)=>{"use strict";var i=a(44868),n=a(85595),o=a(99295)(),s=a(14237),r=a(26829),p=i("%Math.floor%");e.exports=function(e,t){if("function"!=typeof e)throw new r("`fn` is not a function");if("number"!=typeof t||t<0||t>4294967295||p(t)!==t)throw new r("`length` must be a positive 32-bit integer");var a=arguments.length>2&&!!arguments[2],i=!0,c=!0;if("length"in e&&s){var l=s(e,"length");l&&!l.configurable&&(i=!1),l&&!l.writable&&(c=!1)}return(i||c||!a)&&(o?n(e,"length",t,!0,!0):n(e,"length",t)),e}},11284:(e,t,a)=>{"use strict";var i=a(44868),n=a(71658),o=a(53582),s=a(26829),r=i("%WeakMap%",!0),p=i("%Map%",!0),c=n("WeakMap.prototype.get",!0),l=n("WeakMap.prototype.set",!0),u=n("WeakMap.prototype.has",!0),d=n("Map.prototype.get",!0),m=n("Map.prototype.set",!0),f=n("Map.prototype.has",!0),h=function(e,t){for(var a,i=e;null!==(a=i.next);i=a)if(a.key===t)return i.next=a.next,a.next=e.next,e.next=a,a},x=function(e,t){var a=h(e,t);return a&&a.value},v=function(e,t,a){var i=h(e,t);i?i.value=a:e.next={key:t,next:e.next,value:a}};e.exports=function(){var e,t,a,i={assert:function(e){if(!i.has(e))throw new s("Side channel does not contain "+o(e))},get:function(i){if(r&&i&&("object"==typeof i||"function"==typeof i)){if(e)return c(e,i)}else if(p){if(t)return d(t,i)}else if(a)return x(a,i)},has:function(i){if(r&&i&&("object"==typeof i||"function"==typeof i)){if(e)return u(e,i)}else if(p){if(t)return f(t,i)}else if(a)return!!h(a,i);return!1},set:function(i,n){r&&i&&("object"==typeof i||"function"==typeof i)?(e||(e=new r),l(e,i,n)):p?(t||(t=new p),m(t,i,n)):(a||(a={key:{},next:null}),v(a,i,n))}};return i}},2587:e=>{function t(){this._defaults=[]}["use","on","once","set","query","type","accept","auth","withCredentials","sortQuery","retry","ok","redirects","timeout","buffer","serialize","parse","ca","key","pfx","cert"].forEach(e=>{t.prototype[e]=function(...t){return this._defaults.push({fn:e,args:t}),this}}),t.prototype._setDefaults=function(e){this._defaults.forEach(t=>{e[t.fn].apply(e,t.args)})},e.exports=t},1327:e=>{"use strict";e.exports=function(e){return null!==e&&"object"==typeof e}},34579:(e,t,a)=>{"use strict";let i=a(54591).CookieJar,n=a(54591).CookieAccessInfo,o=a(17360).parse,s=a(67828),r=a(2587),p=a(98529);function c(e){if(!(this instanceof c))return new c(e);r.call(this),this.jar=new i,e&&(e.ca&&this.ca(e.ca),e.key&&this.key(e.key),e.pfx&&this.pfx(e.pfx),e.cert&&this.cert(e.cert))}e.exports=c,c.prototype=Object.create(r.prototype),c.prototype._saveCookies=function(e){let t=e.headers["set-cookie"];t&&this.jar.setCookies(t)},c.prototype._attachCookies=function(e){let t=o(e.url),a=n(t.hostname,t.pathname,"https:"==t.protocol),i=this.jar.getCookies(a).toValueString();e.cookies=i},p.forEach(e=>{let t=e.toUpperCase();c.prototype[e]=function(e,a){let i=new s.Request(t,e);return i.on("response",this._saveCookies.bind(this)),i.on("redirect",this._saveCookies.bind(this)),i.on("redirect",this._attachCookies.bind(this,i)),this._attachCookies(i),this._setDefaults(i),a&&i.end(a),i}}),c.prototype.del=c.prototype.delete},85850:(e,t,a)=>{"use strict";let i=a(32694),n=a(76162),o=a(21764),s=a(98216),r=a(82452),p=a(17360).parse,{HTTP2_HEADER_PATH:c,HTTP2_HEADER_STATUS:l,HTTP2_HEADER_METHOD:u,HTTP2_HEADER_AUTHORITY:d,HTTP2_HEADER_HOST:m,HTTP2_HEADER_SET_COOKIE:f,NGHTTP2_CANCEL:h}=i.constants;function x(e,t){n.call(this);let a=t.port||("https:"===e?443:80),o=t.host||"localhost";delete t.port,delete t.host,this.method=t.method,this.path=t.path,this.protocol=e,this.host=o,delete t.method,delete t.path;let s=Object.assign({},t);t.socketPath&&(s.socketPath=t.socketPath,s.createConnection=this.createUnixConnection.bind(this)),this._headers={};let r=i.connect(`${e}//${o}:${a}`,s);this.setHeader("host",`${o}:${a}`),r.on("error",e=>this.emit("error",e)),this.session=r}o.inherits(x,n),x.prototype.createUnixConnection=function(e,t){switch(this.protocol){case"http:":return s.connect(t.socketPath);case"https:":return t.ALPNProtocols=["h2"],t.servername=this.host,t.allowHalfOpen=!0,r.connect(t.socketPath,t);default:throw Error("Unsupported protocol",this.protocol)}},x.prototype.setNoDelay=function(e){},x.prototype.getFrame=function(){if(this.frame)return this.frame;let e={[c]:this.path,[u]:this.method},t=this.mapToHttp2Header(this._headers);t=Object.assign(t,e);let a=this.session.request(t);return a.once("response",(e,t)=>{e=this.mapToHttpHeader(e),a.headers=e,a.status=a.statusCode=e[l],this.emit("response",a)}),this._headerSent=!0,a.once("drain",()=>this.emit("drain")),a.on("error",e=>this.emit("error",e)),a.on("close",()=>this.session.close()),this.frame=a,a},x.prototype.mapToHttpHeader=function(e){let t=Object.keys(e),a={};for(var i=0;i<t.length;i++){let n=t[i],o=e[n];(n=n.toLowerCase())===f&&(o=Array.isArray(o)?o:[o]),a[n]=o}return a},x.prototype.mapToHttp2Header=function(e){let t=Object.keys(e),a={};for(var i=0;i<t.length;i++){let n=t[i],o=e[n];(n=n.toLowerCase())===m&&(n=d,o=/^http\:\/\/|^https\:\/\//.test(o)?p(o).host:o),a[n]=o}return a},x.prototype.setHeader=function(e,t){this._headers[e.toLowerCase()]=t},x.prototype.getHeader=function(e){return this._headers[e.toLowerCase()]},x.prototype.write=function(e,t){return this.getFrame().write(e,t)},x.prototype.pipe=function(e,t){return this.getFrame().pipe(e,t)},x.prototype.end=function(e){this.getFrame().end(e)},x.prototype.abort=function(e){this.getFrame().close(h),this.session.destroy()},t.setProtocol=function(e){return{request:function(t){return new x(e,t)}}}},67828:(e,t,a)=>{"use strict";let i;let n=a(59273)("superagent"),o=a(43684),s=a(44240),r=a(23627),p=a(17360).parse,c=a(17360).format,l=a(17360).resolve,u=a(98529),d=a(76162),m=a(66352),f=a(33057).R,h=a(54891),x=a(35240),v=a(32615),g=a(92048),b=a(92993),y=a(71568),w=a(21764),_=a(25337),k=a(14690),E=a(54591);try{i=a(85850)}catch(e){}function j(e,a){return"function"==typeof a?new t.Request("GET",e).end(a):1==arguments.length?new t.Request("GET",e):new t.Request(e,a)}function T(){}function S(e){let t=`node-superagent/${_.version}`;e._header={"user-agent":t},e.header={"User-Agent":t}}function A(e,t){d.call(this),"string"!=typeof t&&(t=c(t)),this._enableHttp2=!!process.env.HTTP2_TEST,this._agent=!1,this._formData=null,this.method=e,this.url=t,S(this),this.writable=!0,this._redirects=0,this.redirects("HEAD"===e?0:5),this.cookies="",this.qs={},this._query=[],this.qsRaw=this._query,this._redirectList=[],this._streamRequest=!1,this.once("end",this.clearTimeout.bind(this))}function O(e){return/[\/+]json($|[^-\w])/.test(e)}function R(e){return~[301,302,303,305,307,308].indexOf(e)}(t=e.exports=j).Request=A,t.agent=a(34579),t.Response=r,h.define({"application/x-www-form-urlencoded":["form","urlencoded","form-data"]},!0),t.protocols={"http:":v,"https:":x,"http2:":i},t.serialize={"application/x-www-form-urlencoded":b.stringify,"application/json":JSON.stringify},t.parse=a(91467),t.buffer={},w.inherits(A,d),k(A.prototype),A.prototype.http2=function(e){if(void 0===t.protocols["http2:"])throw Error("superagent: this version of Node.js does not support http2");return this._enableHttp2=void 0===e||e,this},A.prototype.attach=function(e,t,a){if(t){if(this._data)throw Error("superagent can't mix .send() and .attach()");let i=a||{};"string"==typeof a&&(i={filename:a}),"string"==typeof t?(i.filename||(i.filename=t),n("creating `fs.ReadStream` instance for file: %s",t),t=g.createReadStream(t)):!i.filename&&t.path&&(i.filename=t.path),this._getFormData().append(e,t,i)}return this},A.prototype._getFormData=function(){return this._formData||(this._formData=new s,this._formData.on("error",e=>{this.emit("error",e),this.abort()})),this._formData},A.prototype.agent=function(e){return arguments.length?(this._agent=e,this):this._agent},A.prototype.type=function(e){return this.set("Content-Type",~e.indexOf("/")?e:h.getType(e))},A.prototype.accept=function(e){return this.set("Accept",~e.indexOf("/")?e:h.getType(e))},A.prototype.query=function(e){return"string"==typeof e?this._query.push(e):Object.assign(this.qs,e),this},A.prototype.write=function(e,t){let a=this.request();return this._streamRequest||(this._streamRequest=!0),a.write(e,t)},A.prototype.pipe=function(e,t){return this.piped=!0,this.buffer(!1),this.end(),this._pipeContinue(e,t)},A.prototype._pipeContinue=function(e,t){return this.req.once("response",a=>{if(R(a.statusCode)&&this._redirects++!=this._maxRedirects)return this._redirect(a)._pipeContinue(e,t);if(this.res=a,this._emitResponse(),!this._aborted){if(this._shouldUnzip(a)){let i=y.createUnzip();i.on("error",t=>{if(t&&"Z_BUF_ERROR"===t.code){e.emit("end");return}e.emit("error",t)}),a.pipe(i).pipe(e,t)}else a.pipe(e,t);a.once("end",()=>{this.emit("end")})}}),e},A.prototype.buffer=function(e){return this._buffer=!1!==e,this},A.prototype._redirect=function(e){let t=e.headers.location;if(!t)return this.callback(Error("No location header for redirect"),e);n("redirect %s -> %s",this.url,t),t=l(this.url,t),e.resume();let a=this.req._headers,i=p(t).host!==p(this.url).host;return(301==e.statusCode||302==e.statusCode)&&(a=m.cleanHeader(this.req._headers,i),this.method="HEAD"==this.method?"HEAD":"GET",this._data=null),303==e.statusCode&&(a=m.cleanHeader(this.req._headers,i),this.method="GET",this._data=null),delete a.host,delete this.req,delete this._formData,S(this),this._endCalled=!1,this.url=t,this.qs={},this._query.length=0,this.set(a),this.emit("redirect",e),this._redirectList.push(this.url),this.end(this._callback),this},A.prototype.auth=function(e,t,a){return 1==arguments.length&&(t=""),"object"==typeof t&&null!==t&&(a=t,t=""),a||(a={type:"basic"}),this._auth(e,t,a,e=>new Buffer.from(e).toString("base64"))},A.prototype.ca=function(e){return this._ca=e,this},A.prototype.key=function(e){return this._key=e,this},A.prototype.pfx=function(e){return"object"!=typeof e||Buffer.isBuffer(e)?this._pfx=e:(this._pfx=e.pfx,this._passphrase=e.passphrase),this},A.prototype.cert=function(e){return this._cert=e,this},A.prototype.request=function(){let e;if(this.req)return this.req;let a={};try{let e=b.stringify(this.qs,{indices:!1,strictNullHandling:!0});e&&(this.qs={},this._query.push(e)),this._finalizeQueryString()}catch(e){return this.emit("error",e)}let i=this.url,n=this._retries;if(i.indexOf("`")>-1){let t=i.indexOf("?");-1!==t&&(e=i.substr(t+1).match(/`|\%60/g))}if(0!=i.indexOf("http")&&(i=`http://${i}`),i=p(i),e){let t=0;i.query=i.query.replace(/\%60/g,()=>e[t++]),i.search=`?${i.query}`,i.path=i.pathname+i.search}if(!0===/^https?\+unix:/.test(i.protocol)){i.protocol=`${i.protocol.split("+")[0]}:`;let e=i.path.match(/^([^/]+)(.+)$/);a.socketPath=e[1].replace(/%2F/g,"/"),i.path=e[2]}if(this._connectOverride){let e=i.hostname,t=e in this._connectOverride?this._connectOverride[e]:this._connectOverride["*"];t&&(this._header.host||this.set("host",i.host),i.host=/:/.test(t)?`[${t}]`:t,i.port&&(i.host+=`:${i.port}`),i.hostname=t)}a.method=this.method,a.port=i.port,a.path=i.path,a.host=i.hostname,a.ca=this._ca,a.key=this._key,a.pfx=this._pfx,a.cert=this._cert,a.passphrase=this._passphrase,a.agent=this._agent,this._header.host&&(a.servername=this._header.host.replace(/:[0-9]+$/,"")),this._trustLocalhost&&/^(?:localhost|127\.0\.0\.\d+|(0*:)+:0*1)$/.test(i.hostname)&&(a.rejectUnauthorized=!1);let o=this._enableHttp2?t.protocols["http2:"].setProtocol(i.protocol):t.protocols[i.protocol],s=this.req=o.request(a);if(s.setNoDelay(!0),"HEAD"!=a.method&&s.setHeader("Accept-Encoding","gzip, deflate"),this.protocol=i.protocol,this.host=i.host,s.once("drain",()=>{this.emit("drain")}),s.on("error",e=>{!this._aborted&&this._retries===n&&(this.response||this.callback(e))}),i.auth){let e=i.auth.split(":");this.auth(e[0],e[1])}for(let e in this.username&&this.password&&this.auth(this.username,this.password),this.header)this.header.hasOwnProperty(e)&&s.setHeader(e,this.header[e]);if(this.cookies){if(this._header.hasOwnProperty("cookie")){let e=new E.CookieJar;e.setCookies(this._header.cookie.split(";")),e.setCookies(this.cookies.split(";")),s.setHeader("Cookie",e.getCookies(E.CookieAccessInfo.All).toValueString())}else s.setHeader("Cookie",this.cookies)}return s},A.prototype.callback=function(e,t){if(this._shouldRetry(e,t))return this._retry();let a=this._callback||T;if(this.clearTimeout(),this.called)return console.warn("superagent: double callback bug");if(this.called=!0,!e)try{if(!this._isResponseOK(t)){let a="Unsuccessful HTTP response";t&&(a=v.STATUS_CODES[t.status]||a),(e=Error(a)).status=t?t.status:void 0}}catch(t){e=t}if(!e)return a(null,t);e.response=t,this._maxRetries&&(e.retries=this._retries-1),e&&this.listeners("error").length>0&&this.emit("error",e),a(e,t)},A.prototype._isHost=function(e){return Buffer.isBuffer(e)||e instanceof d||e instanceof s},A.prototype._emitResponse=function(e,t){let a=new r(this);return this.response=a,a.redirects=this._redirectList,void 0!==e&&(a.body=e),a.files=t,this._endCalled&&(a.pipe=function(){throw Error("end() has already been called, so it's too late to start piping")}),this.emit("response",a),a},A.prototype.end=function(e){if(this.request(),n("%s %s",this.method,this.url),this._endCalled)throw Error(".end() was called twice. This is not supported in superagent");this._endCalled=!0,this._callback=e||T,this._end()},A.prototype._end=function(){if(this._aborted)return this.callback(Error("The request has been aborted even before .end() was called"));let e=this._data,a=this.req,i=this.method;if(this._setTimeouts(),"HEAD"!=i&&!a._headerSent){if("string"!=typeof e){let i=a.getHeader("Content-Type");i&&(i=i.split(";")[0]);let n=this._serializer||t.serialize[i];!n&&O(i)&&(n=t.serialize["application/json"]),n&&(e=n(e))}e&&!a.getHeader("Content-Length")&&a.setHeader("Content-Length",Buffer.isBuffer(e)?e.length:Buffer.byteLength(e))}a.once("response",e=>{if(n("%s %s -> %s",this.method,this.url,e.statusCode),this._responseTimeoutTimer&&clearTimeout(this._responseTimeoutTimer),this.piped)return;let i=this._maxRedirects,s=m.type(e.headers["content-type"]||"")||"text/plain",r=s.split("/")[0],p="multipart"==r,c=R(e.statusCode),l=this._responseType;if(this.res=e,c&&this._redirects++!=i)return this._redirect(e);if("HEAD"==this.method){this.emit("end"),this.callback(null,this._emitResponse());return}this._shouldUnzip(e)&&f(a,e);let u=this._buffer;void 0===u&&s in t.buffer&&(u=!!t.buffer[s]);let d=this._parser;if(void 0===u&&d&&(console.warn("A custom superagent parser has been set, but buffering strategy for the parser hasn't been configured. Call `req.buffer(true or false)` or set `superagent.buffer[mime] = true or false`"),u=!0),!d){if(l)d=t.parse.image,u=!0;else if(p){let e=new o.IncomingForm;d=e.parse.bind(e),u=!0}else(function(e){let t=e.split("/")[0];return"image"==t||"video"==t})(s)?(d=t.parse.image,u=!0):t.parse[s]?d=t.parse[s]:"text"==r?(d=t.parse.text,u=!1!==u):O(s)?(d=t.parse["application/json"],u=!1!==u):u?d=t.parse.text:void 0===u&&(d=t.parse.image,u=!0)}(void 0===u&&function(e){let t=e.split("/"),a=t[0],i=t[1];return"text"==a||"x-www-form-urlencoded"==i}(s)||O(s))&&(u=!0),this._resBuffered=u;let h=!1;if(u){let t=this._maxResponseSize||2e8;e.on("data",a=>{if((t-=a.byteLength||a.length)<0){let t=Error("Maximum response size reached");t.code="ETOOLARGE",h=!1,e.destroy(t)}})}if(d)try{h=u,d(e,(e,t,a)=>{if(!this.timedout){if(e&&!this._aborted)return this.callback(e);h&&(this.emit("end"),this.callback(null,this._emitResponse(t,a)))}})}catch(e){this.callback(e);return}if(this.res=e,!u){if(n("unbuffered %s %s",this.method,this.url),this.callback(null,this._emitResponse()),p)return;e.once("end",()=>{n("end %s %s",this.method,this.url),this.emit("end")});return}e.once("error",e=>{h=!1,this.callback(e,null)}),h||e.once("end",()=>{n("end %s %s",this.method,this.url),this.emit("end"),this.callback(null,this._emitResponse())})}),this.emit("request",this);let s=()=>{let e=a.getHeader("Content-Length"),t=0,i=new d.Transform;return i._transform=(a,i,n)=>{t+=a.length,this.emit("progress",{direction:"upload",lengthComputable:!0,loaded:t,total:e}),n(null,a)},i},r=this._formData;if(r){let e=r.getHeaders();for(let t in e)n('setting FormData header: "%s: %s"',t,e[t]),a.setHeader(t,e[t]);r.getLength((e,t)=>{n("got FormData Content-Length: %s",t),"number"==typeof t&&a.setHeader("Content-Length",t),r.pipe(s()).pipe(a)})}else Buffer.isBuffer(e)?(e=>{let t=new d.Readable,a=e.length,i=a%16384,n=a-i;for(let a=0;a<n;a+=16384){let i=e.slice(a,a+16384);t.push(i)}if(i>0){let a=e.slice(-i);t.push(a)}return t.push(null),t})(e).pipe(s()).pipe(a):a.end(e)},A.prototype._shouldUnzip=e=>204!==e.statusCode&&304!==e.statusCode&&"0"!==e.headers["content-length"]&&/^\s*(?:deflate|gzip)\s*$/.test(e.headers["content-encoding"]),A.prototype.connect=function(e){return"string"==typeof e?this._connectOverride={"*":e}:"object"==typeof e?this._connectOverride=e:this._connectOverride=void 0,this},A.prototype.trustLocalhost=function(e){return this._trustLocalhost=void 0===e||e,this},-1==u.indexOf("del")&&(u=u.slice(0)).push("del"),u.forEach(e=>{let t=e;e=(e="del"==e?"delete":e).toUpperCase(),j[t]=(t,a,i)=>{let n=j(e,t);return"function"==typeof a&&(i=a,a=null),a&&("GET"===e||"HEAD"===e?n.query(a):n.send(a)),i&&n.end(i),n}})},49939:e=>{"use strict";e.exports=(e,t)=>{let a=[];e.on("data",e=>{a.push(e)}),e.on("end",()=>{t(null,Buffer.concat(a))})}},91467:(e,t,a)=>{"use strict";t["application/x-www-form-urlencoded"]=a(2727),t["application/json"]=a(12397),t.text=a(46560);let i=a(49939);t["application/octet-stream"]=i,t["application/pdf"]=i,t.image=i},12397:e=>{"use strict";e.exports=function(e,t){e.text="",e.setEncoding("utf8"),e.on("data",t=>{e.text+=t}),e.on("end",()=>{try{var a=e.text&&JSON.parse(e.text)}catch(t){var i=t;i.rawResponse=e.text||null,i.statusCode=e.statusCode}finally{t(i,a)}})}},46560:e=>{"use strict";e.exports=(e,t)=>{e.text="",e.setEncoding("utf8"),e.on("data",t=>{e.text+=t}),e.on("end",t)}},2727:(e,t,a)=>{"use strict";let i=a(92993);e.exports=(e,t)=>{e.text="",e.setEncoding("ascii"),e.on("data",t=>{e.text+=t}),e.on("end",()=>{try{t(null,i.parse(e.text))}catch(e){t(e)}})}},23627:(e,t,a)=>{"use strict";let i=a(21764),n=a(76162),o=a(29078);function s(e){n.call(this);let t=this.res=e.res;this.request=e,this.req=e.req,this.text=t.text,this.body=void 0!==t.body?t.body:{},this.files=t.files||{},this.buffered=e._resBuffered,this.header=this.headers=t.headers,this._setStatusProperties(t.statusCode),this._setHeaderProperties(this.header),this.setEncoding=t.setEncoding.bind(t),t.on("data",this.emit.bind(this,"data")),t.on("end",this.emit.bind(this,"end")),t.on("close",this.emit.bind(this,"close")),t.on("error",this.emit.bind(this,"error"))}e.exports=s,i.inherits(s,n),o(s.prototype),s.prototype.destroy=function(e){this.res.destroy(e)},s.prototype.pause=function(){this.res.pause()},s.prototype.resume=function(){this.res.resume()},s.prototype.toError=function(){let e=this.req,t=e.method,a=e.path,i=Error(`cannot ${t} ${a} (${this.status})`);return i.status=this.status,i.text=this.text,i.method=t,i.path=a,i},s.prototype.setStatusProperties=function(e){return console.warn("In superagent 2.x setStatusProperties is a private method"),this._setStatusProperties(e)},s.prototype.toJSON=function(){return{req:this.request.toJSON(),header:this.header,status:this.status,text:this.text}}},33057:(e,t,a)=>{"use strict";let i=a(74026).StringDecoder,n=a(76162),o=a(71568);t.R=(e,t)=>{let a;let s=o.createUnzip(),r=new n;r.req=e,s.on("error",e=>{if(e&&"Z_BUF_ERROR"===e.code){r.emit("end");return}r.emit("error",e)}),t.pipe(s),t.setEncoding=e=>{a=new i(e)},s.on("data",e=>{if(a){let t=a.write(e);t.length&&r.emit("data",t)}else r.emit("data",e)}),s.on("end",()=>{r.emit("end")});let p=t.on;t.on=function(e,a){return"data"==e||"end"==e?r.on(e,a.bind(t)):("error"==e&&r.on(e,a.bind(t)),p.call(t,e,a)),this}}},14690:(e,t,a)=>{"use strict";let i=a(1327);function n(e){if(e)return function(e){for(let t in n.prototype)e[t]=n.prototype[t];return e}(e)}e.exports=n,n.prototype.clearTimeout=function(){return clearTimeout(this._timer),clearTimeout(this._responseTimeoutTimer),delete this._timer,delete this._responseTimeoutTimer,this},n.prototype.parse=function(e){return this._parser=e,this},n.prototype.responseType=function(e){return this._responseType=e,this},n.prototype.serialize=function(e){return this._serializer=e,this},n.prototype.timeout=function(e){if(!e||"object"!=typeof e)return this._timeout=e,this._responseTimeout=0,this;for(let t in e)switch(t){case"deadline":this._timeout=e.deadline;break;case"response":this._responseTimeout=e.response;break;default:console.warn("Unknown timeout option",t)}return this},n.prototype.retry=function(e,t){return(0==arguments.length||!0===e)&&(e=1),e<=0&&(e=0),this._maxRetries=e,this._retries=0,this._retryCallback=t,this};let o=["ECONNRESET","ETIMEDOUT","EADDRINFO","ESOCKETTIMEDOUT"];n.prototype._shouldRetry=function(e,t){if(!this._maxRetries||this._retries++>=this._maxRetries)return!1;if(this._retryCallback)try{let a=this._retryCallback(e,t);if(!0===a)return!0;if(!1===a)return!1}catch(e){console.error(e)}return!!(t&&t.status&&t.status>=500&&501!=t.status||e&&(e.code&&~o.indexOf(e.code)||e.timeout&&"ECONNABORTED"==e.code||e.crossDomain))},n.prototype._retry=function(){return this.clearTimeout(),this.req&&(this.req=null,this.req=this.request()),this._aborted=!1,this.timedout=!1,this._end()},n.prototype.then=function(e,t){if(!this._fullfilledPromise){let e=this;this._endCalled&&console.warn("Warning: superagent request was sent twice, because both .end() and .then() were called. Never call .end() if you use promises"),this._fullfilledPromise=new Promise((t,a)=>{e.on("error",a),e.on("abort",()=>{let e=Error("Aborted");e.code="ABORTED",e.status=this.status,e.method=this.method,e.url=this.url,a(e)}),e.end((e,i)=>{e?a(e):t(i)})})}return this._fullfilledPromise.then(e,t)},n.prototype.catch=function(e){return this.then(void 0,e)},n.prototype.use=function(e){return e(this),this},n.prototype.ok=function(e){if("function"!=typeof e)throw Error("Callback required");return this._okCallback=e,this},n.prototype._isResponseOK=function(e){return!!e&&(this._okCallback?this._okCallback(e):e.status>=200&&e.status<300)},n.prototype.get=function(e){return this._header[e.toLowerCase()]},n.prototype.getHeader=n.prototype.get,n.prototype.set=function(e,t){if(i(e)){for(let t in e)this.set(t,e[t]);return this}return this._header[e.toLowerCase()]=t,this.header[e]=t,this},n.prototype.unset=function(e){return delete this._header[e.toLowerCase()],delete this.header[e],this},n.prototype.field=function(e,t){if(null==e)throw Error(".field(name, val) name can not be empty");if(this._data)throw Error(".field() can't be used if .send() is used. Please use only .send() or only .field() & .attach()");if(i(e)){for(let t in e)this.field(t,e[t]);return this}if(Array.isArray(t)){for(let a in t)this.field(e,t[a]);return this}if(null==t)throw Error(".field(name, val) val can not be empty");return"boolean"==typeof t&&(t=""+t),this._getFormData().append(e,t),this},n.prototype.abort=function(){return this._aborted||(this._aborted=!0,this.xhr&&this.xhr.abort(),this.req&&this.req.abort(),this.clearTimeout(),this.emit("abort")),this},n.prototype._auth=function(e,t,a,i){switch(a.type){case"basic":this.set("Authorization",`Basic ${i(`${e}:${t}`)}`);break;case"auto":this.username=e,this.password=t;break;case"bearer":this.set("Authorization",`Bearer ${e}`)}return this},n.prototype.withCredentials=function(e){return void 0==e&&(e=!0),this._withCredentials=e,this},n.prototype.redirects=function(e){return this._maxRedirects=e,this},n.prototype.maxResponseSize=function(e){if("number"!=typeof e)throw TypeError("Invalid argument");return this._maxResponseSize=e,this},n.prototype.toJSON=function(){return{method:this.method,url:this.url,data:this._data,headers:this._header}},n.prototype.send=function(e){let t=i(e),a=this._header["content-type"];if(this._formData)throw Error(".send() can't be used if .attach() or .field() is used. Please use only .send() or only .field() & .attach()");if(t&&!this._data)Array.isArray(e)?this._data=[]:this._isHost(e)||(this._data={});else if(e&&this._data&&this._isHost(this._data))throw Error("Can't merge these send calls");if(t&&i(this._data))for(let t in e)this._data[t]=e[t];else"string"==typeof e?(a||this.type("form"),"application/x-www-form-urlencoded"==(a=this._header["content-type"])?this._data=this._data?`${this._data}&${e}`:e:this._data=(this._data||"")+e):this._data=e;return!t||this._isHost(e)||a||this.type("json"),this},n.prototype.sortQuery=function(e){return this._sort=void 0===e||e,this},n.prototype._finalizeQueryString=function(){let e=this._query.join("&");if(e&&(this.url+=(this.url.indexOf("?")>=0?"&":"?")+e),this._query.length=0,this._sort){let e=this.url.indexOf("?");if(e>=0){let t=this.url.substring(e+1).split("&");"function"==typeof this._sort?t.sort(this._sort):t.sort(),this.url=this.url.substring(0,e)+"?"+t.join("&")}}},n.prototype._appendQueryString=()=>{console.trace("Unsupported")},n.prototype._timeoutError=function(e,t,a){if(this._aborted)return;let i=Error(`${e+t}ms exceeded`);i.timeout=t,i.code="ECONNABORTED",i.errno=a,this.timedout=!0,this.abort(),this.callback(i)},n.prototype._setTimeouts=function(){let e=this;this._timeout&&!this._timer&&(this._timer=setTimeout(()=>{e._timeoutError("Timeout of ",e._timeout,"ETIME")},this._timeout)),this._responseTimeout&&!this._responseTimeoutTimer&&(this._responseTimeoutTimer=setTimeout(()=>{e._timeoutError("Response timeout of ",e._responseTimeout,"ETIMEDOUT")},this._responseTimeout))}},29078:(e,t,a)=>{"use strict";let i=a(66352);function n(e){if(e)return function(e){for(let t in n.prototype)e[t]=n.prototype[t];return e}(e)}e.exports=n,n.prototype.get=function(e){return this.header[e.toLowerCase()]},n.prototype._setHeaderProperties=function(e){let t=e["content-type"]||"";this.type=i.type(t);let a=i.params(t);for(let e in a)this[e]=a[e];this.links={};try{e.link&&(this.links=i.parseLinks(e.link))}catch(e){}},n.prototype._setStatusProperties=function(e){let t=e/100|0;this.status=this.statusCode=e,this.statusType=t,this.info=1==t,this.ok=2==t,this.redirect=3==t,this.clientError=4==t,this.serverError=5==t,this.error=(4==t||5==t)&&this.toError(),this.created=201==e,this.accepted=202==e,this.noContent=204==e,this.badRequest=400==e,this.unauthorized=401==e,this.notAcceptable=406==e,this.forbidden=403==e,this.notFound=404==e,this.unprocessableEntity=422==e}},66352:(e,t)=>{"use strict";t.type=e=>e.split(/ *; */).shift(),t.params=e=>e.split(/ *; */).reduce((e,t)=>{let a=t.split(/ *= */),i=a.shift(),n=a.shift();return i&&n&&(e[i]=n),e},{}),t.parseLinks=e=>e.split(/ *, */).reduce((e,t)=>{let a=t.split(/ *; */),i=a[0].slice(1,-1);return e[a[1].split(/ *= */)[1].slice(1,-1)]=i,e},{}),t.cleanHeader=(e,t)=>(delete e["content-type"],delete e["content-length"],delete e["transfer-encoding"],delete e.host,t&&(delete e.authorization,delete e.cookie),e)},44240:(e,t,a)=>{var i=a(29227),n=a(21764),o=a(55315),s=a(32615),r=a(35240),p=a(17360).parse,c=a(92048),l=a(33320),u=a(37901),d=a(98594),m=a(65926).Buffer;function f(e){if(!(this instanceof f))return new f;for(var t in this._overheadLength=0,this._valueLength=0,this._valuesToMeasure=[],i.call(this),e=e||{})this[t]=e[t]}e.exports=f,n.inherits(f,i),f.LINE_BREAK="\r\n",f.DEFAULT_CONTENT_TYPE="application/octet-stream",f.prototype.append=function(e,t,a){"string"==typeof(a=a||{})&&(a={filename:a});var n=i.prototype.append.bind(this);if("number"==typeof t&&(t=""+t),Array.isArray(t)){this._error(Error("Arrays are not supported."));return}var o=this._multiPartHeader(e,t,a),s=this._multiPartFooter();n(o),n(t),n(s),this._trackLength(o,t,a)},f.prototype._trackLength=function(e,t,a){var i=0;null!=a.knownLength?i+=+a.knownLength:m.isBuffer(t)?i=t.length:"string"==typeof t&&(i=m.byteLength(t)),this._valueLength+=i,this._overheadLength+=m.byteLength(e)+f.LINE_BREAK.length,t&&(t.path||t.readable&&t.hasOwnProperty("httpVersion"))&&(a.knownLength||this._valuesToMeasure.push(t))},f.prototype._lengthRetriever=function(e,t){e.hasOwnProperty("fd")?void 0!=e.end&&e.end!=1/0&&void 0!=e.start?t(null,e.end+1-(e.start?e.start:0)):c.stat(e.path,function(a,i){if(a){t(a);return}t(null,i.size-(e.start?e.start:0))}):e.hasOwnProperty("httpVersion")?t(null,+e.headers["content-length"]):e.hasOwnProperty("httpModule")?(e.on("response",function(a){e.pause(),t(null,+a.headers["content-length"])}),e.resume()):t("Unknown stream")},f.prototype._multiPartHeader=function(e,t,a){if("string"==typeof a.header)return a.header;var i,n=this._getContentDisposition(t,a),o=this._getContentType(t,a),s="",r={"Content-Disposition":["form-data",'name="'+e+'"'].concat(n||[]),"Content-Type":[].concat(o||[])};for(var p in"object"==typeof a.header&&d(r,a.header),r)if(r.hasOwnProperty(p)){if(null==(i=r[p]))continue;Array.isArray(i)||(i=[i]),i.length&&(s+=p+": "+i.join("; ")+f.LINE_BREAK)}return"--"+this.getBoundary()+f.LINE_BREAK+s+f.LINE_BREAK},f.prototype._getContentDisposition=function(e,t){var a,i;return"string"==typeof t.filepath?a=o.normalize(t.filepath).replace(/\\/g,"/"):t.filename||e.name||e.path?a=o.basename(t.filename||e.name||e.path):e.readable&&e.hasOwnProperty("httpVersion")&&(a=o.basename(e.client._httpMessage.path||"")),a&&(i='filename="'+a+'"'),i},f.prototype._getContentType=function(e,t){var a=t.contentType;return!a&&e.name&&(a=l.lookup(e.name)),!a&&e.path&&(a=l.lookup(e.path)),!a&&e.readable&&e.hasOwnProperty("httpVersion")&&(a=e.headers["content-type"]),!a&&(t.filepath||t.filename)&&(a=l.lookup(t.filepath||t.filename)),a||"object"!=typeof e||(a=f.DEFAULT_CONTENT_TYPE),a},f.prototype._multiPartFooter=function(){return(function(e){var t=f.LINE_BREAK;0===this._streams.length&&(t+=this._lastBoundary()),e(t)}).bind(this)},f.prototype._lastBoundary=function(){return"--"+this.getBoundary()+"--"+f.LINE_BREAK},f.prototype.getHeaders=function(e){var t,a={"content-type":"multipart/form-data; boundary="+this.getBoundary()};for(t in e)e.hasOwnProperty(t)&&(a[t.toLowerCase()]=e[t]);return a},f.prototype.getBoundary=function(){return this._boundary||this._generateBoundary(),this._boundary},f.prototype.getBuffer=function(){for(var e=new m.alloc(0),t=this.getBoundary(),a=0,i=this._streams.length;a<i;a++)"function"!=typeof this._streams[a]&&(e=m.isBuffer(this._streams[a])?m.concat([e,this._streams[a]]):m.concat([e,m.from(this._streams[a])]),("string"!=typeof this._streams[a]||this._streams[a].substring(2,t.length+2)!==t)&&(e=m.concat([e,m.from(f.LINE_BREAK)])));return m.concat([e,m.from(this._lastBoundary())])},f.prototype._generateBoundary=function(){for(var e="--------------------------",t=0;t<24;t++)e+=Math.floor(10*Math.random()).toString(16);this._boundary=e},f.prototype.getLengthSync=function(){var e=this._overheadLength+this._valueLength;return this._streams.length&&(e+=this._lastBoundary().length),this.hasKnownLength()||this._error(Error("Cannot calculate proper length in synchronous way.")),e},f.prototype.hasKnownLength=function(){var e=!0;return this._valuesToMeasure.length&&(e=!1),e},f.prototype.getLength=function(e){var t=this._overheadLength+this._valueLength;if(this._streams.length&&(t+=this._lastBoundary().length),!this._valuesToMeasure.length){process.nextTick(e.bind(this,null,t));return}u.parallel(this._valuesToMeasure,this._lengthRetriever,function(a,i){if(a){e(a);return}i.forEach(function(e){t+=e}),e(null,t)})},f.prototype.submit=function(e,t){var a,i,n={method:"post"};return"string"==typeof e?i=d({port:(e=p(e)).port,path:e.pathname,host:e.hostname,protocol:e.protocol},n):(i=d(e,n)).port||(i.port="https:"==i.protocol?443:80),i.headers=this.getHeaders(e.headers),a="https:"==i.protocol?r.request(i):s.request(i),this.getLength((function(e,i){if(e){this._error(e);return}a.setHeader("Content-Length",i),this.pipe(a),t&&(a.on("error",t),a.on("response",t.bind(this,null)))}).bind(this)),a},f.prototype._error=function(e){this.error||(this.error=e,this.pause(),this.emit("error",e))},f.prototype.toString=function(){return"[object FormData]"}},98594:e=>{e.exports=function(e,t){return Object.keys(t).forEach(function(a){e[a]=e[a]||t[a]}),e}},95749:(e,t,a)=>{"use strict";let i;let n=a(19801),o=a(74175),s=a(78679),{env:r}=process;function p(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function c(e,t){if(0===i)return 0;if(s("color=16m")||s("color=full")||s("color=truecolor"))return 3;if(s("color=256"))return 2;if(e&&!t&&void 0===i)return 0;let a=i||0;if("dumb"===r.TERM)return a;if("win32"===process.platform){let e=n.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in r)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(e=>e in r)||"codeship"===r.CI_NAME?1:a;if("TEAMCITY_VERSION"in r)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(r.TEAMCITY_VERSION)?1:0;if("truecolor"===r.COLORTERM)return 3;if("TERM_PROGRAM"in r){let e=parseInt((r.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(r.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(r.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(r.TERM)||"COLORTERM"in r?1:a}s("no-color")||s("no-colors")||s("color=false")||s("color=never")?i=0:(s("color")||s("colors")||s("color=true")||s("color=always"))&&(i=1),"FORCE_COLOR"in r&&(i="true"===r.FORCE_COLOR?1:"false"===r.FORCE_COLOR?0:0===r.FORCE_COLOR.length?1:Math.min(parseInt(r.FORCE_COLOR,10),3)),e.exports={supportsColor:function(e){return p(c(e,e&&e.isTTY))},stdout:p(c(!0,o.isatty(1))),stderr:p(c(!0,o.isatty(2)))}},87539:(e,t,a)=>{"use strict";let i=a(46362).parse;e.exports={locateAPIRootHeader:function(e){let t="https://api.w.org/",a=i(e.link||e.headers&&e.headers.link),n=a&&a[t];if(n)return n;throw Error(`No header link found with rel="${t}"`)}}},52233:(e,t,a)=>{"use strict";let i=a(92993),n=a(26821),o=a(26590),s=a(50581),r=a(50443),p=a(1231);function c(e){this._options=["auth","endpoint","headers","username","password","nonce"].reduce((t,a)=>(e&&e[a]&&(t[a]=e[a]),t),{}),this.transport=e&&e.transport,this._params={},this._supportedMethods=["head","get","put","post","delete"],this._path={}}let l=e=>e,u=e=>e?r(e,(e,t,a)=>(null!=t&&""!==t&&(e[a]=t),e),{}):e,d=(e,t)=>{if(!e.reduce((e,a)=>!a.validate||e||a.validate(t),!1))throw Error(["Invalid path component:",t,"does not match"+(e.length>1?" any of":""),e.reduce((e,t)=>e.concat(t.component),[]).join(", ")].join(" "))};c.prototype._renderQuery=function(){var e;let t={...u(this._params)},a=(e=this._taxonomyFilters)?r(e,(e,t,a)=>(e[a]=t.map(e=>(e+"").trim().toLowerCase()).join("+"),e),{}):{};t.filter={...u(this._filters),...a};let n=i.stringify(t,{arrayFormat:"brackets"}).split("&").sort().join("&"),o=/\?/.test(this._options.endpoint)?"&":"?";return""===n?"":o+n},c.prototype._renderPath=function(){this.validatePath();let e=this._path,t=Object.keys(e).sort((e,t)=>parseInt(e,10)-parseInt(t,10)).map(t=>e[t]);return[this._namespace].concat(t).filter(l).join("/")},c.prototype.toString=function(){let e=this._renderPath(),t=this._renderQuery();return this._options.endpoint+e+t},c.prototype.setPathPart=function(e,t){if(this._path[e])throw Error("Cannot overwrite value "+this._path[e]);return this._path[e]=t,this},c.prototype.validatePath=function(){let e=Object.keys(this._path).map(e=>parseInt(e,10)).filter(e=>!isNaN(e)),t=Math.max.apply(null,e),a=[],i=!0;for(let e=0;e<=t;e++)this._levels&&this._levels[e]&&(this._path[e]?(d(this._levels[e],this._path[e]),a.push(this._path[e])):(a.push(" ??? "),i=!1));if(!i)throw Error("Incomplete URL! Missing component: /"+a.join("/"));return this},c.prototype.param=function(e,t){return e&&("string"!=typeof e||void 0!==t)&&("string"==typeof e&&(e=o(e,t)),Object.keys(e).forEach(t=>{let a=e[t];Array.isArray(a)&&(a=p(a).sort(n)),this._params[t]=a})),this},c.prototype.context=s("context"),c.prototype.edit=function(){return this.context("edit")},c.prototype.embed=function(){return this.param("_embed",!0)},c.prototype.page=s("page"),c.prototype.perPage=s("per_page"),c.prototype.offset=s("offset"),c.prototype.order=s("order"),c.prototype.orderby=s("orderby"),c.prototype.search=s("search"),c.prototype.include=s("include"),c.prototype.exclude=s("exclude"),c.prototype.slug=s("slug"),c.prototype.namespace=function(e){return this._namespace=e,this},c.prototype.auth=function(e){return"object"==typeof e&&("string"==typeof e.username&&(this._options.username=e.username),"string"==typeof e.password&&(this._options.password=e.password),e.nonce&&(this._options.nonce=e.nonce)),this._options.auth=!0,this},c.prototype.file=function(e,t){if(global.Buffer&&e instanceof global.Buffer&&!t)throw Error(".file(): File name is a required argument when uploading a Buffer");return this._attachment=e,this._attachmentName=t||void 0,this},c.prototype.setHeaders=function(e,t){return"string"==typeof e&&(e=o(e,t)),this._options.headers={...this._options.headers||{},...e},this},c.prototype.get=function(e){return this.transport.get(this,e)},c.prototype.headers=function(e){return this.transport.head(this,e)},c.prototype.create=function(e,t){return this.transport.post(this,e,t)},c.prototype.update=function(e,t){return this.transport.put(this,e,t)},c.prototype.delete=function(e,t){return this.transport.delete(this,e,t)},c.prototype.then=function(e,t){return this.transport.get(this).then(e,t)},e.exports=c},76260:(e,t,a)=>{"use strict";let i=a(25263).create,n=a(21377).create,o=a(50443);e.exports={generate:function(e){return o(e,(e,t,a)=>(e[a]=o(t,(e,t,o)=>{let s=n(i(t,o),o,a);return e[o]=function(e){return new s({...this._options,...e})},e[o].Ctor=s,e},{}),e),{})}}},21377:(e,t,a)=>{"use strict";let i=a(52233),n=a(11332),o=a(50001);e.exports={create:function(e,t,a){class s extends i{constructor(i){super(i),this._levels=e._levels,this.setPathPart(0,t).namespace(a)}}return"object"==typeof e._getArgs&&Object.keys(e._getArgs).forEach(e=>{let t=n[e];"object"==typeof t&&Object.keys(t).forEach(e=>{o(s.prototype,e,t[e])})}),Object.keys(e._setters).forEach(t=>{s.prototype[t]||(s.prototype[t]=e._setters[t])}),s}}},61732:(e,t,a)=>{"use strict";let i=a(67828),n=a(46362).parse,o=a(52233),s=a(55665),r=a(50443),p=a(97555);function c(e,t){return t.headers?r(t.headers,(e,t,a)=>e.set(a,t),e):e}function l(e,t,a){if(!a&&!t.auth&&!t.nonce)return e;if(t.nonce)return e.set("X-WP-Nonce",t.nonce),e;let i=t.username,n=t.password;return i&&n?e.auth(i,n):e}function u(e,t,a){return new Promise((t,a)=>{e.end((e,i)=>{e||i.error?a(e||i.error):t(i)})}).then(a).then(e=>(t&&"function"==typeof t&&t(null,e),e),e=>{if(e.response&&e.response.body&&e.response.body.code&&(e=e.response.body),t&&"function"==typeof t)t(e);else throw e})}function d(e,t){let a=function(e){let t=e.body;if(p(t)&&"text/html"===e.type)try{t=JSON.parse(e.text)}catch(e){}return t}(t),i=function(e,t,a){let i=null;if(!e.headers||(Object.keys(e.headers).forEach(t=>{e.headers[t.toLowerCase()]=e.headers[t]}),!e.headers["x-wp-totalpages"]))return i;let s=+e.headers["x-wp-totalpages"];if(!s||0===s)return i;let r=e.headers.link?n(e.headers.link):{};return i={total:+e.headers["x-wp-total"],totalPages:s,links:r},r.next&&(i.next=new o({...t,transport:a,endpoint:r.next})),r.prev&&(i.prev=new o({...t,transport:a,endpoint:r.prev})),i}(t,e._options,e.transport);return i&&(a._paging=i),a}function m(e){return e.headers}e.exports={delete:function(e,t,a){a||"function"!=typeof t||(a=t,t=null),s("delete",e);let n=e.toString(),o=l(i.del(n),e._options,!0).send(t);return u(o=c(o,e._options),a,d.bind(null,e))},get:function(e,t){s("get",e);let a=e.toString(),n=l(i.get(a),e._options);return u(n=c(n,e._options),t,d.bind(null,e))},head:function(e,t){s("head",e);let a=e.toString(),n=l(i.head(a),e._options);return u(n=c(n,e._options),t,m)},post:function(e,t,a){s("post",e);let n=e.toString();t=t||{};let o=l(i.post(n),e._options,!0);return o=c(o,e._options),u(o=e._attachment?r(t,(e,t,a)=>e.field(a,t),o.attach("file",e._attachment,e._attachmentName)):o.send(t),a,d.bind(null,e))},put:function(e,t,a){s("put",e);let n=e.toString();t=t||{};let o=l(i.put(n),e._options,!0).send(t);return u(o=c(o,e._options),a,d.bind(null,e))}}},37392:(e,t,a)=>{"use strict";let i=a(26821),n=a(26590),o=a(1231),s={};s.filter=function(e,t){return e&&("string"!=typeof e||void 0!==t)&&("string"==typeof e&&(e=n(e,t)),this._filters={...this._filters,...e}),this},s.taxonomy=function(e,t){let a=Array.isArray(t),n=a?t.reduce((e,t)=>e&&"number"==typeof t,!0):"number"==typeof t,s=a?t.reduce((e,t)=>e&&"string"==typeof t,!0):"string"==typeof t;if(!s&&!n)throw Error("term must be a number, string, or array of numbers or strings");"category"===e?e=s?"category_name":"cat":"post_tag"===e&&(e="tag"),this._taxonomyFilters=this._taxonomyFilters||{};let r=(this._taxonomyFilters[e]||[]).concat(t).sort(i);return this._taxonomyFilters[e]=o(r,!0),this},s.year=function(e){return s.filter.call(this,"year",e)},s.month=function(e){let t;if("string"==typeof e){if(isNaN(t=new Date(Date.parse(e+" 1, 2012"))))return this;e=t.getMonth()+1}return"number"==typeof e?s.filter.call(this,"monthnum",e):this},s.day=function(e){return s.filter.call(this,"day",e)},s.path=function(e){return s.filter.call(this,"pagename",e)},e.exports=s},11332:(e,t,a)=>{"use strict";let i=a(37392),n=a(73746),o={categories:{categories:n.categories,category:n.category},categories_exclude:{excludeCategories:n.excludeCategories},tags:{tags:n.tags,tag:n.tag},tags_exclude:{excludeTags:n.excludeTags},filter:i,post:{post:n.post,forPost:n.post}};["after","author","before","parent","password","status","sticky"].forEach(e=>{o[e]={},o[e][e]=n[e]}),e.exports=o},73746:(e,t,a)=>{"use strict";let i=a(50581),n=a(32122),o={},s=a(37392),r=s.filter,p=s.taxonomy;o.author=function(e){if(void 0===e)return this;if("string"==typeof e)return this.param("author",null),r.call(this,"author_name",e);if("number"==typeof e)return r.call(this,"author_name",null),this.param("author",e);if(null===e)return r.call(this,"author_name",null),this.param("author",null);throw Error("author must be either a nicename string or numeric ID")},o.parent=i("parent"),o.post=i("post"),o.password=i("password"),o.status=i("status"),o.sticky=i("sticky"),o.categories=i("categories"),o.category=function(e){return n(e)?o.categories.call(this,e):p.call(this,"category",e)},o.excludeCategories=i("categories_exclude"),o.tags=i("tags"),o.tag=function(e){return n(e)?o.tags.call(this,e):p.call(this,"tag",e)},o.excludeTags=i("tags_exclude"),o.before=function(e){return this.param("before",new Date(e).toISOString())},o.after=function(e){return this.param("after",new Date(e).toISOString())},e.exports=o},5682:e=>{"use strict";e.exports={create:function(e){let t=e.level,a=e.names[0],i=e.methods||[],n=e.children?Object.keys(e.children).map(t=>e.children[t]).filter(e=>!0===e.namedGroup):[],o=1===n.length&&n[0],s=o&&o.level;return e.namedGroup?function(e){return this.setPathPart(t,e),i.length&&(this._supportedMethods=i),this}:function(e){return this.setPathPart(t,a),void 0!==e&&s&&this.setPathPart(s,e),this}}}},25263:(e,t,a)=>{"use strict";let i=a(5682).create;e.exports={create:function(e,t){let a={_path:{0:t},_levels:{},_setters:{},_getArgs:e._getArgs};return Object.keys(e).forEach(t=>{"_getArgs"!==t&&function e(t,a){var n,o,s;let r;n=t._levels,o=a.level,s={component:a.component,validate:a.validate,methods:a.methods},n[o]=n[o]||[],n[o].push(s),a.level>0&&(r=i(a),a.names.forEach(e=>{let a=e.replace(/[_-]+\w/g,e=>e.replace(/[_-]+/,"").toUpperCase());t._setters[a]||(t._setters[a]=r)})),a.children&&Object.keys(a.children).forEach(i=>{e(t,a.children[i])})}(a,e[t])}),a}}},9521:(e,t,a)=>{"use strict";let i=a(13052).namedGroupRE,n=a(97600),o=a(98392),s=a(50443);function r(e,t,a,n,o,s){let r=n.match(i),p=r&&r[1],c=r&&r[2],l=r?c||p:n,u=r?p:n,d=a[l]||{component:n,namedGroup:!!r,level:o,names:[]};0>d.names.indexOf(u)&&d.names.push(u);let m=""===c?/.*/:RegExp(c?"^"+c+"$":n,"i");return d.validate=e=>m.test(e),s[o+1]?d.children=d.children||{}:(d.methods=(e.methods||[]).map(e=>e.toLowerCase()),d.methods.indexOf("get")>-1&&-1===d.methods.indexOf("head")&&d.methods.push("head"),e.endpoints&&(t._getArgs=t._getArgs||{},e.endpoints.forEach(e=>{e.methods.forEach(a=>{"get"===a.toLowerCase()&&Object.keys(e.args).forEach(a=>{t._getArgs[a]=e.args[a]})})}))),a[l]=d,d.children}function p(e,t,a){let i=t.namespace,s=a.replace("/"+i+"/","").replace(/\/\?$/,""),p=n(s);if(!i||"/"+i===a||!s)return e;o(e,i,{});let c=e[i],l=p[0];o(c,l,{});let u=c[l];return p.reduce(r.bind(null,t,u),u),e}e.exports={build:function(e){return s(e,p,{})}}},26821:e=>{"use strict";e.exports=(e,t)=>e>t?1:e<t?-1:0},50001:e=>{"use strict";e.exports=(e,t,a)=>{"function"!=typeof a||e[t]||(e[t]=a)}},32122:e=>{"use strict";let t=e=>{if("number"==typeof e)return!0;if("string"==typeof e)return/^\d+$/.test(e);if(Array.isArray(e)){for(let a=0;a<e.length;a++)if(!t(e[a]))return!1;return!0}return!1};e.exports=t},55665:e=>{"use strict";e.exports=(e,t)=>{if(-1===t._supportedMethods.indexOf(e.toLowerCase()))throw Error("Unsupported method; supported methods are: "+t._supportedMethods.join(", "));return!0}},98392:e=>{"use strict";e.exports=(e,t,a)=>{e&&void 0===e[t]&&(e[t]=a)}},97555:e=>{"use strict";e.exports=e=>{if("object"!=typeof e||Array.isArray(e))return!1;for(let t in e)if(e.hasOwnProperty(t))return!1;return!0}},26590:e=>{"use strict";e.exports=(e,t)=>{let a={};return a[e]=t,a}},13052:e=>{"use strict";let t="\\(\\?(?:P<|<|')([^>']+)[>']([^\\)]*(\\))?\\??)\\)";e.exports={pattern:t,namedGroupRE:new RegExp(t)}},50443:e=>{"use strict";e.exports=(e,t,a)=>Object.keys(e).reduce((a,i)=>t(a,e[i],i),a)},50581:e=>{"use strict";e.exports=e=>function(t){return this.param(e,t)}},97600:(e,t,a)=>{"use strict";let i=RegExp("([^/]*"+a(13052).pattern.replace(/([^\\])\(([^?])/g,"$1(?:$2")+"[^/]*)");e.exports=e=>e.split(i).reduce((e,t)=>t?i.test(t)?e.concat(t):e.concat(t.split("/").filter(Boolean)):e,[])},1231:e=>{e.exports=e=>Array.from(new Set(e))},87391:(e,t,a)=>{"use strict";let i=a(9521).build,n=a(76260).generate,o=a(50581),s=a(50001),r=a(11332);e.exports=function(e,t,a={}){let p=["head","get","patch","put","post","delete"];Array.isArray(a.methods)?p=a.methods.map(e=>e.trim().toLowerCase()):"string"==typeof a.methods&&(p=[a.methods.trim().toLowerCase()]),-1!==p.indexOf("get")&&-1===p.indexOf("head")?p.push("head"):-1!==p.indexOf("head")&&-1===p.indexOf("get")&&p.push("get");let c=e.replace(/^[\s/]*/,"/").replace(/[\s/]*$/,"/")+t.replace(/^[\s/]*/,""),l={};l[c]={namespace:e,methods:p};let u=n(i(l))[e],d=u[Object.keys(u)[0]].Ctor;function m(e={}){return new d({...e,...this?this._options:{}})}return a&&a.params&&a.params.forEach(e=>{if("string"==typeof e){if("object"==typeof r[e]){Object.keys(r[e]).forEach(t=>{s(d.prototype,t,r[e][t])});return}s(d.prototype,e,o(e))}}),a&&"object"==typeof a.mixins&&Object.keys(a.mixins).forEach(e=>{s(d.prototype,e,a.mixins[e])}),m.Ctor=d,m}},28106:(e,t,a)=>{"use strict";let i;let n=a(50443),o=a(19112),s=a(9521).build,r=a(76260).generate,p=a(87539),c=a(52233),l=a(61732);function u(e){if(this instanceof u==!1)return new u(e);if("string"!=typeof e.endpoint)throw Error("options hash must contain an API endpoint URL string");return this._ns={},this._options={endpoint:e.endpoint.replace(/\/?$/,"/")},e&&(e.username||e.password||e.nonce)&&this.auth(e),this.transport(e.transport).bootstrap(e&&e.routes)}u.prototype.transport=function(e){let t=this._options;return t.transport||(t.transport=Object.create(u.transport)),["get","head","post","put","delete"].forEach(a=>{e&&e[a]&&(t.transport[a]=e[a])}),this},u.transport=Object.create(l),Object.freeze(u.transport),u.site=function(e,t){return new u({endpoint:e,routes:t})},u.prototype.url=function(e){return new c({...this._options,endpoint:e})},u.prototype.root=function(e){e=e||"";let t=new c({...this._options});return t._path={0:e},t},u.prototype.setHeaders=c.prototype.setHeaders,u.prototype.auth=c.prototype.auth,u.prototype.registerRoute=a(87391),u.prototype.bootstrap=function(e){let t;return e?t=r(s(e)):(i||(i=r(s(o))),t=i),n(t,(e,t,a)=>(e._ns[a]=n(t,(e,t,a)=>(e[a]=t,e),e._ns[a]||{_options:e._options}),"wp/v2"===a&&Object.keys(e._ns[a]).forEach(t=>{e[t]=e._ns[a][t]}),e),this)},u.prototype.namespace=function(e){if(!this._ns[e])throw Error("Error: namespace "+e+" is not recognized");return this._ns[e]},u.discover=e=>{let t;let a=u.site(e).root();return a.headers().catch(()=>a.get()).then(p.locateAPIRootHeader).then(e=>(t=e,u.site(e).root().get())).then(e=>new u({endpoint:t,routes:e.routes})).catch(e=>{if(console.error(e),t)return console.warn("Endpoint detected, proceeding despite error..."),console.warn("Binding to "+t+" and assuming default routes"),new u.site(t);throw Error("Autodiscovery failed")})},e.exports=u},21882:e=>{"use strict";e.exports=JSON.parse('{"application/1d-interleaved-parityfec":{"source":"iana"},"application/3gpdash-qoe-report+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/3gpp-ims+xml":{"source":"iana","compressible":true},"application/3gpphal+json":{"source":"iana","compressible":true},"application/3gpphalforms+json":{"source":"iana","compressible":true},"application/a2l":{"source":"iana"},"application/ace+cbor":{"source":"iana"},"application/activemessage":{"source":"iana"},"application/activity+json":{"source":"iana","compressible":true},"application/alto-costmap+json":{"source":"iana","compressible":true},"application/alto-costmapfilter+json":{"source":"iana","compressible":true},"application/alto-directory+json":{"source":"iana","compressible":true},"application/alto-endpointcost+json":{"source":"iana","compressible":true},"application/alto-endpointcostparams+json":{"source":"iana","compressible":true},"application/alto-endpointprop+json":{"source":"iana","compressible":true},"application/alto-endpointpropparams+json":{"source":"iana","compressible":true},"application/alto-error+json":{"source":"iana","compressible":true},"application/alto-networkmap+json":{"source":"iana","compressible":true},"application/alto-networkmapfilter+json":{"source":"iana","compressible":true},"application/alto-updatestreamcontrol+json":{"source":"iana","compressible":true},"application/alto-updatestreamparams+json":{"source":"iana","compressible":true},"application/aml":{"source":"iana"},"application/andrew-inset":{"source":"iana","extensions":["ez"]},"application/applefile":{"source":"iana"},"application/applixware":{"source":"apache","extensions":["aw"]},"application/at+jwt":{"source":"iana"},"application/atf":{"source":"iana"},"application/atfx":{"source":"iana"},"application/atom+xml":{"source":"iana","compressible":true,"extensions":["atom"]},"application/atomcat+xml":{"source":"iana","compressible":true,"extensions":["atomcat"]},"application/atomdeleted+xml":{"source":"iana","compressible":true,"extensions":["atomdeleted"]},"application/atomicmail":{"source":"iana"},"application/atomsvc+xml":{"source":"iana","compressible":true,"extensions":["atomsvc"]},"application/atsc-dwd+xml":{"source":"iana","compressible":true,"extensions":["dwd"]},"application/atsc-dynamic-event-message":{"source":"iana"},"application/atsc-held+xml":{"source":"iana","compressible":true,"extensions":["held"]},"application/atsc-rdt+json":{"source":"iana","compressible":true},"application/atsc-rsat+xml":{"source":"iana","compressible":true,"extensions":["rsat"]},"application/atxml":{"source":"iana"},"application/auth-policy+xml":{"source":"iana","compressible":true},"application/bacnet-xdd+zip":{"source":"iana","compressible":false},"application/batch-smtp":{"source":"iana"},"application/bdoc":{"compressible":false,"extensions":["bdoc"]},"application/beep+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/calendar+json":{"source":"iana","compressible":true},"application/calendar+xml":{"source":"iana","compressible":true,"extensions":["xcs"]},"application/call-completion":{"source":"iana"},"application/cals-1840":{"source":"iana"},"application/captive+json":{"source":"iana","compressible":true},"application/cbor":{"source":"iana"},"application/cbor-seq":{"source":"iana"},"application/cccex":{"source":"iana"},"application/ccmp+xml":{"source":"iana","compressible":true},"application/ccxml+xml":{"source":"iana","compressible":true,"extensions":["ccxml"]},"application/cdfx+xml":{"source":"iana","compressible":true,"extensions":["cdfx"]},"application/cdmi-capability":{"source":"iana","extensions":["cdmia"]},"application/cdmi-container":{"source":"iana","extensions":["cdmic"]},"application/cdmi-domain":{"source":"iana","extensions":["cdmid"]},"application/cdmi-object":{"source":"iana","extensions":["cdmio"]},"application/cdmi-queue":{"source":"iana","extensions":["cdmiq"]},"application/cdni":{"source":"iana"},"application/cea":{"source":"iana"},"application/cea-2018+xml":{"source":"iana","compressible":true},"application/cellml+xml":{"source":"iana","compressible":true},"application/cfw":{"source":"iana"},"application/city+json":{"source":"iana","compressible":true},"application/clr":{"source":"iana"},"application/clue+xml":{"source":"iana","compressible":true},"application/clue_info+xml":{"source":"iana","compressible":true},"application/cms":{"source":"iana"},"application/cnrp+xml":{"source":"iana","compressible":true},"application/coap-group+json":{"source":"iana","compressible":true},"application/coap-payload":{"source":"iana"},"application/commonground":{"source":"iana"},"application/conference-info+xml":{"source":"iana","compressible":true},"application/cose":{"source":"iana"},"application/cose-key":{"source":"iana"},"application/cose-key-set":{"source":"iana"},"application/cpl+xml":{"source":"iana","compressible":true,"extensions":["cpl"]},"application/csrattrs":{"source":"iana"},"application/csta+xml":{"source":"iana","compressible":true},"application/cstadata+xml":{"source":"iana","compressible":true},"application/csvm+json":{"source":"iana","compressible":true},"application/cu-seeme":{"source":"apache","extensions":["cu"]},"application/cwt":{"source":"iana"},"application/cybercash":{"source":"iana"},"application/dart":{"compressible":true},"application/dash+xml":{"source":"iana","compressible":true,"extensions":["mpd"]},"application/dash-patch+xml":{"source":"iana","compressible":true,"extensions":["mpp"]},"application/dashdelta":{"source":"iana"},"application/davmount+xml":{"source":"iana","compressible":true,"extensions":["davmount"]},"application/dca-rft":{"source":"iana"},"application/dcd":{"source":"iana"},"application/dec-dx":{"source":"iana"},"application/dialog-info+xml":{"source":"iana","compressible":true},"application/dicom":{"source":"iana"},"application/dicom+json":{"source":"iana","compressible":true},"application/dicom+xml":{"source":"iana","compressible":true},"application/dii":{"source":"iana"},"application/dit":{"source":"iana"},"application/dns":{"source":"iana"},"application/dns+json":{"source":"iana","compressible":true},"application/dns-message":{"source":"iana"},"application/docbook+xml":{"source":"apache","compressible":true,"extensions":["dbk"]},"application/dots+cbor":{"source":"iana"},"application/dskpp+xml":{"source":"iana","compressible":true},"application/dssc+der":{"source":"iana","extensions":["dssc"]},"application/dssc+xml":{"source":"iana","compressible":true,"extensions":["xdssc"]},"application/dvcs":{"source":"iana"},"application/ecmascript":{"source":"iana","compressible":true,"extensions":["es","ecma"]},"application/edi-consent":{"source":"iana"},"application/edi-x12":{"source":"iana","compressible":false},"application/edifact":{"source":"iana","compressible":false},"application/efi":{"source":"iana"},"application/elm+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/elm+xml":{"source":"iana","compressible":true},"application/emergencycalldata.cap+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/emergencycalldata.comment+xml":{"source":"iana","compressible":true},"application/emergencycalldata.control+xml":{"source":"iana","compressible":true},"application/emergencycalldata.deviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.ecall.msd":{"source":"iana"},"application/emergencycalldata.providerinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.serviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.subscriberinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.veds+xml":{"source":"iana","compressible":true},"application/emma+xml":{"source":"iana","compressible":true,"extensions":["emma"]},"application/emotionml+xml":{"source":"iana","compressible":true,"extensions":["emotionml"]},"application/encaprtp":{"source":"iana"},"application/epp+xml":{"source":"iana","compressible":true},"application/epub+zip":{"source":"iana","compressible":false,"extensions":["epub"]},"application/eshop":{"source":"iana"},"application/exi":{"source":"iana","extensions":["exi"]},"application/expect-ct-report+json":{"source":"iana","compressible":true},"application/express":{"source":"iana","extensions":["exp"]},"application/fastinfoset":{"source":"iana"},"application/fastsoap":{"source":"iana"},"application/fdt+xml":{"source":"iana","compressible":true,"extensions":["fdt"]},"application/fhir+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/fhir+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/fido.trusted-apps+json":{"compressible":true},"application/fits":{"source":"iana"},"application/flexfec":{"source":"iana"},"application/font-sfnt":{"source":"iana"},"application/font-tdpfr":{"source":"iana","extensions":["pfr"]},"application/font-woff":{"source":"iana","compressible":false},"application/framework-attributes+xml":{"source":"iana","compressible":true},"application/geo+json":{"source":"iana","compressible":true,"extensions":["geojson"]},"application/geo+json-seq":{"source":"iana"},"application/geopackage+sqlite3":{"source":"iana"},"application/geoxacml+xml":{"source":"iana","compressible":true},"application/gltf-buffer":{"source":"iana"},"application/gml+xml":{"source":"iana","compressible":true,"extensions":["gml"]},"application/gpx+xml":{"source":"apache","compressible":true,"extensions":["gpx"]},"application/gxf":{"source":"apache","extensions":["gxf"]},"application/gzip":{"source":"iana","compressible":false,"extensions":["gz"]},"application/h224":{"source":"iana"},"application/held+xml":{"source":"iana","compressible":true},"application/hjson":{"extensions":["hjson"]},"application/http":{"source":"iana"},"application/hyperstudio":{"source":"iana","extensions":["stk"]},"application/ibe-key-request+xml":{"source":"iana","compressible":true},"application/ibe-pkg-reply+xml":{"source":"iana","compressible":true},"application/ibe-pp-data":{"source":"iana"},"application/iges":{"source":"iana"},"application/im-iscomposing+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/index":{"source":"iana"},"application/index.cmd":{"source":"iana"},"application/index.obj":{"source":"iana"},"application/index.response":{"source":"iana"},"application/index.vnd":{"source":"iana"},"application/inkml+xml":{"source":"iana","compressible":true,"extensions":["ink","inkml"]},"application/iotp":{"source":"iana"},"application/ipfix":{"source":"iana","extensions":["ipfix"]},"application/ipp":{"source":"iana"},"application/isup":{"source":"iana"},"application/its+xml":{"source":"iana","compressible":true,"extensions":["its"]},"application/java-archive":{"source":"apache","compressible":false,"extensions":["jar","war","ear"]},"application/java-serialized-object":{"source":"apache","compressible":false,"extensions":["ser"]},"application/java-vm":{"source":"apache","compressible":false,"extensions":["class"]},"application/javascript":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["js","mjs"]},"application/jf2feed+json":{"source":"iana","compressible":true},"application/jose":{"source":"iana"},"application/jose+json":{"source":"iana","compressible":true},"application/jrd+json":{"source":"iana","compressible":true},"application/jscalendar+json":{"source":"iana","compressible":true},"application/json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["json","map"]},"application/json-patch+json":{"source":"iana","compressible":true},"application/json-seq":{"source":"iana"},"application/json5":{"extensions":["json5"]},"application/jsonml+json":{"source":"apache","compressible":true,"extensions":["jsonml"]},"application/jwk+json":{"source":"iana","compressible":true},"application/jwk-set+json":{"source":"iana","compressible":true},"application/jwt":{"source":"iana"},"application/kpml-request+xml":{"source":"iana","compressible":true},"application/kpml-response+xml":{"source":"iana","compressible":true},"application/ld+json":{"source":"iana","compressible":true,"extensions":["jsonld"]},"application/lgr+xml":{"source":"iana","compressible":true,"extensions":["lgr"]},"application/link-format":{"source":"iana"},"application/load-control+xml":{"source":"iana","compressible":true},"application/lost+xml":{"source":"iana","compressible":true,"extensions":["lostxml"]},"application/lostsync+xml":{"source":"iana","compressible":true},"application/lpf+zip":{"source":"iana","compressible":false},"application/lxf":{"source":"iana"},"application/mac-binhex40":{"source":"iana","extensions":["hqx"]},"application/mac-compactpro":{"source":"apache","extensions":["cpt"]},"application/macwriteii":{"source":"iana"},"application/mads+xml":{"source":"iana","compressible":true,"extensions":["mads"]},"application/manifest+json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["webmanifest"]},"application/marc":{"source":"iana","extensions":["mrc"]},"application/marcxml+xml":{"source":"iana","compressible":true,"extensions":["mrcx"]},"application/mathematica":{"source":"iana","extensions":["ma","nb","mb"]},"application/mathml+xml":{"source":"iana","compressible":true,"extensions":["mathml"]},"application/mathml-content+xml":{"source":"iana","compressible":true},"application/mathml-presentation+xml":{"source":"iana","compressible":true},"application/mbms-associated-procedure-description+xml":{"source":"iana","compressible":true},"application/mbms-deregister+xml":{"source":"iana","compressible":true},"application/mbms-envelope+xml":{"source":"iana","compressible":true},"application/mbms-msk+xml":{"source":"iana","compressible":true},"application/mbms-msk-response+xml":{"source":"iana","compressible":true},"application/mbms-protection-description+xml":{"source":"iana","compressible":true},"application/mbms-reception-report+xml":{"source":"iana","compressible":true},"application/mbms-register+xml":{"source":"iana","compressible":true},"application/mbms-register-response+xml":{"source":"iana","compressible":true},"application/mbms-schedule+xml":{"source":"iana","compressible":true},"application/mbms-user-service-description+xml":{"source":"iana","compressible":true},"application/mbox":{"source":"iana","extensions":["mbox"]},"application/media-policy-dataset+xml":{"source":"iana","compressible":true,"extensions":["mpf"]},"application/media_control+xml":{"source":"iana","compressible":true},"application/mediaservercontrol+xml":{"source":"iana","compressible":true,"extensions":["mscml"]},"application/merge-patch+json":{"source":"iana","compressible":true},"application/metalink+xml":{"source":"apache","compressible":true,"extensions":["metalink"]},"application/metalink4+xml":{"source":"iana","compressible":true,"extensions":["meta4"]},"application/mets+xml":{"source":"iana","compressible":true,"extensions":["mets"]},"application/mf4":{"source":"iana"},"application/mikey":{"source":"iana"},"application/mipc":{"source":"iana"},"application/missing-blocks+cbor-seq":{"source":"iana"},"application/mmt-aei+xml":{"source":"iana","compressible":true,"extensions":["maei"]},"application/mmt-usd+xml":{"source":"iana","compressible":true,"extensions":["musd"]},"application/mods+xml":{"source":"iana","compressible":true,"extensions":["mods"]},"application/moss-keys":{"source":"iana"},"application/moss-signature":{"source":"iana"},"application/mosskey-data":{"source":"iana"},"application/mosskey-request":{"source":"iana"},"application/mp21":{"source":"iana","extensions":["m21","mp21"]},"application/mp4":{"source":"iana","extensions":["mp4s","m4p"]},"application/mpeg4-generic":{"source":"iana"},"application/mpeg4-iod":{"source":"iana"},"application/mpeg4-iod-xmt":{"source":"iana"},"application/mrb-consumer+xml":{"source":"iana","compressible":true},"application/mrb-publish+xml":{"source":"iana","compressible":true},"application/msc-ivr+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msc-mixer+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msword":{"source":"iana","compressible":false,"extensions":["doc","dot"]},"application/mud+json":{"source":"iana","compressible":true},"application/multipart-core":{"source":"iana"},"application/mxf":{"source":"iana","extensions":["mxf"]},"application/n-quads":{"source":"iana","extensions":["nq"]},"application/n-triples":{"source":"iana","extensions":["nt"]},"application/nasdata":{"source":"iana"},"application/news-checkgroups":{"source":"iana","charset":"US-ASCII"},"application/news-groupinfo":{"source":"iana","charset":"US-ASCII"},"application/news-transmission":{"source":"iana"},"application/nlsml+xml":{"source":"iana","compressible":true},"application/node":{"source":"iana","extensions":["cjs"]},"application/nss":{"source":"iana"},"application/oauth-authz-req+jwt":{"source":"iana"},"application/oblivious-dns-message":{"source":"iana"},"application/ocsp-request":{"source":"iana"},"application/ocsp-response":{"source":"iana"},"application/octet-stream":{"source":"iana","compressible":false,"extensions":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/oda":{"source":"iana","extensions":["oda"]},"application/odm+xml":{"source":"iana","compressible":true},"application/odx":{"source":"iana"},"application/oebps-package+xml":{"source":"iana","compressible":true,"extensions":["opf"]},"application/ogg":{"source":"iana","compressible":false,"extensions":["ogx"]},"application/omdoc+xml":{"source":"apache","compressible":true,"extensions":["omdoc"]},"application/onenote":{"source":"apache","extensions":["onetoc","onetoc2","onetmp","onepkg"]},"application/opc-nodeset+xml":{"source":"iana","compressible":true},"application/oscore":{"source":"iana"},"application/oxps":{"source":"iana","extensions":["oxps"]},"application/p21":{"source":"iana"},"application/p21+zip":{"source":"iana","compressible":false},"application/p2p-overlay+xml":{"source":"iana","compressible":true,"extensions":["relo"]},"application/parityfec":{"source":"iana"},"application/passport":{"source":"iana"},"application/patch-ops-error+xml":{"source":"iana","compressible":true,"extensions":["xer"]},"application/pdf":{"source":"iana","compressible":false,"extensions":["pdf"]},"application/pdx":{"source":"iana"},"application/pem-certificate-chain":{"source":"iana"},"application/pgp-encrypted":{"source":"iana","compressible":false,"extensions":["pgp"]},"application/pgp-keys":{"source":"iana","extensions":["asc"]},"application/pgp-signature":{"source":"iana","extensions":["asc","sig"]},"application/pics-rules":{"source":"apache","extensions":["prf"]},"application/pidf+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pidf-diff+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pkcs10":{"source":"iana","extensions":["p10"]},"application/pkcs12":{"source":"iana"},"application/pkcs7-mime":{"source":"iana","extensions":["p7m","p7c"]},"application/pkcs7-signature":{"source":"iana","extensions":["p7s"]},"application/pkcs8":{"source":"iana","extensions":["p8"]},"application/pkcs8-encrypted":{"source":"iana"},"application/pkix-attr-cert":{"source":"iana","extensions":["ac"]},"application/pkix-cert":{"source":"iana","extensions":["cer"]},"application/pkix-crl":{"source":"iana","extensions":["crl"]},"application/pkix-pkipath":{"source":"iana","extensions":["pkipath"]},"application/pkixcmp":{"source":"iana","extensions":["pki"]},"application/pls+xml":{"source":"iana","compressible":true,"extensions":["pls"]},"application/poc-settings+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/postscript":{"source":"iana","compressible":true,"extensions":["ai","eps","ps"]},"application/ppsp-tracker+json":{"source":"iana","compressible":true},"application/problem+json":{"source":"iana","compressible":true},"application/problem+xml":{"source":"iana","compressible":true},"application/provenance+xml":{"source":"iana","compressible":true,"extensions":["provx"]},"application/prs.alvestrand.titrax-sheet":{"source":"iana"},"application/prs.cww":{"source":"iana","extensions":["cww"]},"application/prs.cyn":{"source":"iana","charset":"7-BIT"},"application/prs.hpub+zip":{"source":"iana","compressible":false},"application/prs.nprend":{"source":"iana"},"application/prs.plucker":{"source":"iana"},"application/prs.rdf-xml-crypt":{"source":"iana"},"application/prs.xsf+xml":{"source":"iana","compressible":true},"application/pskc+xml":{"source":"iana","compressible":true,"extensions":["pskcxml"]},"application/pvd+json":{"source":"iana","compressible":true},"application/qsig":{"source":"iana"},"application/raml+yaml":{"compressible":true,"extensions":["raml"]},"application/raptorfec":{"source":"iana"},"application/rdap+json":{"source":"iana","compressible":true},"application/rdf+xml":{"source":"iana","compressible":true,"extensions":["rdf","owl"]},"application/reginfo+xml":{"source":"iana","compressible":true,"extensions":["rif"]},"application/relax-ng-compact-syntax":{"source":"iana","extensions":["rnc"]},"application/remote-printing":{"source":"iana"},"application/reputon+json":{"source":"iana","compressible":true},"application/resource-lists+xml":{"source":"iana","compressible":true,"extensions":["rl"]},"application/resource-lists-diff+xml":{"source":"iana","compressible":true,"extensions":["rld"]},"application/rfc+xml":{"source":"iana","compressible":true},"application/riscos":{"source":"iana"},"application/rlmi+xml":{"source":"iana","compressible":true},"application/rls-services+xml":{"source":"iana","compressible":true,"extensions":["rs"]},"application/route-apd+xml":{"source":"iana","compressible":true,"extensions":["rapd"]},"application/route-s-tsid+xml":{"source":"iana","compressible":true,"extensions":["sls"]},"application/route-usd+xml":{"source":"iana","compressible":true,"extensions":["rusd"]},"application/rpki-ghostbusters":{"source":"iana","extensions":["gbr"]},"application/rpki-manifest":{"source":"iana","extensions":["mft"]},"application/rpki-publication":{"source":"iana"},"application/rpki-roa":{"source":"iana","extensions":["roa"]},"application/rpki-updown":{"source":"iana"},"application/rsd+xml":{"source":"apache","compressible":true,"extensions":["rsd"]},"application/rss+xml":{"source":"apache","compressible":true,"extensions":["rss"]},"application/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"application/rtploopback":{"source":"iana"},"application/rtx":{"source":"iana"},"application/samlassertion+xml":{"source":"iana","compressible":true},"application/samlmetadata+xml":{"source":"iana","compressible":true},"application/sarif+json":{"source":"iana","compressible":true},"application/sarif-external-properties+json":{"source":"iana","compressible":true},"application/sbe":{"source":"iana"},"application/sbml+xml":{"source":"iana","compressible":true,"extensions":["sbml"]},"application/scaip+xml":{"source":"iana","compressible":true},"application/scim+json":{"source":"iana","compressible":true},"application/scvp-cv-request":{"source":"iana","extensions":["scq"]},"application/scvp-cv-response":{"source":"iana","extensions":["scs"]},"application/scvp-vp-request":{"source":"iana","extensions":["spq"]},"application/scvp-vp-response":{"source":"iana","extensions":["spp"]},"application/sdp":{"source":"iana","extensions":["sdp"]},"application/secevent+jwt":{"source":"iana"},"application/senml+cbor":{"source":"iana"},"application/senml+json":{"source":"iana","compressible":true},"application/senml+xml":{"source":"iana","compressible":true,"extensions":["senmlx"]},"application/senml-etch+cbor":{"source":"iana"},"application/senml-etch+json":{"source":"iana","compressible":true},"application/senml-exi":{"source":"iana"},"application/sensml+cbor":{"source":"iana"},"application/sensml+json":{"source":"iana","compressible":true},"application/sensml+xml":{"source":"iana","compressible":true,"extensions":["sensmlx"]},"application/sensml-exi":{"source":"iana"},"application/sep+xml":{"source":"iana","compressible":true},"application/sep-exi":{"source":"iana"},"application/session-info":{"source":"iana"},"application/set-payment":{"source":"iana"},"application/set-payment-initiation":{"source":"iana","extensions":["setpay"]},"application/set-registration":{"source":"iana"},"application/set-registration-initiation":{"source":"iana","extensions":["setreg"]},"application/sgml":{"source":"iana"},"application/sgml-open-catalog":{"source":"iana"},"application/shf+xml":{"source":"iana","compressible":true,"extensions":["shf"]},"application/sieve":{"source":"iana","extensions":["siv","sieve"]},"application/simple-filter+xml":{"source":"iana","compressible":true},"application/simple-message-summary":{"source":"iana"},"application/simplesymbolcontainer":{"source":"iana"},"application/sipc":{"source":"iana"},"application/slate":{"source":"iana"},"application/smil":{"source":"iana"},"application/smil+xml":{"source":"iana","compressible":true,"extensions":["smi","smil"]},"application/smpte336m":{"source":"iana"},"application/soap+fastinfoset":{"source":"iana"},"application/soap+xml":{"source":"iana","compressible":true},"application/sparql-query":{"source":"iana","extensions":["rq"]},"application/sparql-results+xml":{"source":"iana","compressible":true,"extensions":["srx"]},"application/spdx+json":{"source":"iana","compressible":true},"application/spirits-event+xml":{"source":"iana","compressible":true},"application/sql":{"source":"iana"},"application/srgs":{"source":"iana","extensions":["gram"]},"application/srgs+xml":{"source":"iana","compressible":true,"extensions":["grxml"]},"application/sru+xml":{"source":"iana","compressible":true,"extensions":["sru"]},"application/ssdl+xml":{"source":"apache","compressible":true,"extensions":["ssdl"]},"application/ssml+xml":{"source":"iana","compressible":true,"extensions":["ssml"]},"application/stix+json":{"source":"iana","compressible":true},"application/swid+xml":{"source":"iana","compressible":true,"extensions":["swidtag"]},"application/tamp-apex-update":{"source":"iana"},"application/tamp-apex-update-confirm":{"source":"iana"},"application/tamp-community-update":{"source":"iana"},"application/tamp-community-update-confirm":{"source":"iana"},"application/tamp-error":{"source":"iana"},"application/tamp-sequence-adjust":{"source":"iana"},"application/tamp-sequence-adjust-confirm":{"source":"iana"},"application/tamp-status-query":{"source":"iana"},"application/tamp-status-response":{"source":"iana"},"application/tamp-update":{"source":"iana"},"application/tamp-update-confirm":{"source":"iana"},"application/tar":{"compressible":true},"application/taxii+json":{"source":"iana","compressible":true},"application/td+json":{"source":"iana","compressible":true},"application/tei+xml":{"source":"iana","compressible":true,"extensions":["tei","teicorpus"]},"application/tetra_isi":{"source":"iana"},"application/thraud+xml":{"source":"iana","compressible":true,"extensions":["tfi"]},"application/timestamp-query":{"source":"iana"},"application/timestamp-reply":{"source":"iana"},"application/timestamped-data":{"source":"iana","extensions":["tsd"]},"application/tlsrpt+gzip":{"source":"iana"},"application/tlsrpt+json":{"source":"iana","compressible":true},"application/tnauthlist":{"source":"iana"},"application/token-introspection+jwt":{"source":"iana"},"application/toml":{"compressible":true,"extensions":["toml"]},"application/trickle-ice-sdpfrag":{"source":"iana"},"application/trig":{"source":"iana","extensions":["trig"]},"application/ttml+xml":{"source":"iana","compressible":true,"extensions":["ttml"]},"application/tve-trigger":{"source":"iana"},"application/tzif":{"source":"iana"},"application/tzif-leap":{"source":"iana"},"application/ubjson":{"compressible":false,"extensions":["ubj"]},"application/ulpfec":{"source":"iana"},"application/urc-grpsheet+xml":{"source":"iana","compressible":true},"application/urc-ressheet+xml":{"source":"iana","compressible":true,"extensions":["rsheet"]},"application/urc-targetdesc+xml":{"source":"iana","compressible":true,"extensions":["td"]},"application/urc-uisocketdesc+xml":{"source":"iana","compressible":true},"application/vcard+json":{"source":"iana","compressible":true},"application/vcard+xml":{"source":"iana","compressible":true},"application/vemmi":{"source":"iana"},"application/vividence.scriptfile":{"source":"apache"},"application/vnd.1000minds.decision-model+xml":{"source":"iana","compressible":true,"extensions":["1km"]},"application/vnd.3gpp-prose+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-prose-pc3ch+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-v2x-local-service-information":{"source":"iana"},"application/vnd.3gpp.5gnas":{"source":"iana"},"application/vnd.3gpp.access-transfer-events+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.bsf+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gmop+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gtpc":{"source":"iana"},"application/vnd.3gpp.interworking-data":{"source":"iana"},"application/vnd.3gpp.lpp":{"source":"iana"},"application/vnd.3gpp.mc-signalling-ear":{"source":"iana"},"application/vnd.3gpp.mcdata-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-payload":{"source":"iana"},"application/vnd.3gpp.mcdata-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-signalling":{"source":"iana"},"application/vnd.3gpp.mcdata-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-floor-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-signed+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-init-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-transmission-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mid-call+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ngap":{"source":"iana"},"application/vnd.3gpp.pfcp":{"source":"iana"},"application/vnd.3gpp.pic-bw-large":{"source":"iana","extensions":["plb"]},"application/vnd.3gpp.pic-bw-small":{"source":"iana","extensions":["psb"]},"application/vnd.3gpp.pic-bw-var":{"source":"iana","extensions":["pvb"]},"application/vnd.3gpp.s1ap":{"source":"iana"},"application/vnd.3gpp.sms":{"source":"iana"},"application/vnd.3gpp.sms+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-ext+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.state-and-event-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ussd+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.bcmcsinfo+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.sms":{"source":"iana"},"application/vnd.3gpp2.tcap":{"source":"iana","extensions":["tcap"]},"application/vnd.3lightssoftware.imagescal":{"source":"iana"},"application/vnd.3m.post-it-notes":{"source":"iana","extensions":["pwn"]},"application/vnd.accpac.simply.aso":{"source":"iana","extensions":["aso"]},"application/vnd.accpac.simply.imp":{"source":"iana","extensions":["imp"]},"application/vnd.acucobol":{"source":"iana","extensions":["acu"]},"application/vnd.acucorp":{"source":"iana","extensions":["atc","acutc"]},"application/vnd.adobe.air-application-installer-package+zip":{"source":"apache","compressible":false,"extensions":["air"]},"application/vnd.adobe.flash.movie":{"source":"iana"},"application/vnd.adobe.formscentral.fcdt":{"source":"iana","extensions":["fcdt"]},"application/vnd.adobe.fxp":{"source":"iana","extensions":["fxp","fxpl"]},"application/vnd.adobe.partial-upload":{"source":"iana"},"application/vnd.adobe.xdp+xml":{"source":"iana","compressible":true,"extensions":["xdp"]},"application/vnd.adobe.xfdf":{"source":"iana","extensions":["xfdf"]},"application/vnd.aether.imp":{"source":"iana"},"application/vnd.afpc.afplinedata":{"source":"iana"},"application/vnd.afpc.afplinedata-pagedef":{"source":"iana"},"application/vnd.afpc.cmoca-cmresource":{"source":"iana"},"application/vnd.afpc.foca-charset":{"source":"iana"},"application/vnd.afpc.foca-codedfont":{"source":"iana"},"application/vnd.afpc.foca-codepage":{"source":"iana"},"application/vnd.afpc.modca":{"source":"iana"},"application/vnd.afpc.modca-cmtable":{"source":"iana"},"application/vnd.afpc.modca-formdef":{"source":"iana"},"application/vnd.afpc.modca-mediummap":{"source":"iana"},"application/vnd.afpc.modca-objectcontainer":{"source":"iana"},"application/vnd.afpc.modca-overlay":{"source":"iana"},"application/vnd.afpc.modca-pagesegment":{"source":"iana"},"application/vnd.age":{"source":"iana","extensions":["age"]},"application/vnd.ah-barcode":{"source":"iana"},"application/vnd.ahead.space":{"source":"iana","extensions":["ahead"]},"application/vnd.airzip.filesecure.azf":{"source":"iana","extensions":["azf"]},"application/vnd.airzip.filesecure.azs":{"source":"iana","extensions":["azs"]},"application/vnd.amadeus+json":{"source":"iana","compressible":true},"application/vnd.amazon.ebook":{"source":"apache","extensions":["azw"]},"application/vnd.amazon.mobi8-ebook":{"source":"iana"},"application/vnd.americandynamics.acc":{"source":"iana","extensions":["acc"]},"application/vnd.amiga.ami":{"source":"iana","extensions":["ami"]},"application/vnd.amundsen.maze+xml":{"source":"iana","compressible":true},"application/vnd.android.ota":{"source":"iana"},"application/vnd.android.package-archive":{"source":"apache","compressible":false,"extensions":["apk"]},"application/vnd.anki":{"source":"iana"},"application/vnd.anser-web-certificate-issue-initiation":{"source":"iana","extensions":["cii"]},"application/vnd.anser-web-funds-transfer-initiation":{"source":"apache","extensions":["fti"]},"application/vnd.antix.game-component":{"source":"iana","extensions":["atx"]},"application/vnd.apache.arrow.file":{"source":"iana"},"application/vnd.apache.arrow.stream":{"source":"iana"},"application/vnd.apache.thrift.binary":{"source":"iana"},"application/vnd.apache.thrift.compact":{"source":"iana"},"application/vnd.apache.thrift.json":{"source":"iana"},"application/vnd.api+json":{"source":"iana","compressible":true},"application/vnd.aplextor.warrp+json":{"source":"iana","compressible":true},"application/vnd.apothekende.reservation+json":{"source":"iana","compressible":true},"application/vnd.apple.installer+xml":{"source":"iana","compressible":true,"extensions":["mpkg"]},"application/vnd.apple.keynote":{"source":"iana","extensions":["key"]},"application/vnd.apple.mpegurl":{"source":"iana","extensions":["m3u8"]},"application/vnd.apple.numbers":{"source":"iana","extensions":["numbers"]},"application/vnd.apple.pages":{"source":"iana","extensions":["pages"]},"application/vnd.apple.pkpass":{"compressible":false,"extensions":["pkpass"]},"application/vnd.arastra.swi":{"source":"iana"},"application/vnd.aristanetworks.swi":{"source":"iana","extensions":["swi"]},"application/vnd.artisan+json":{"source":"iana","compressible":true},"application/vnd.artsquare":{"source":"iana"},"application/vnd.astraea-software.iota":{"source":"iana","extensions":["iota"]},"application/vnd.audiograph":{"source":"iana","extensions":["aep"]},"application/vnd.autopackage":{"source":"iana"},"application/vnd.avalon+json":{"source":"iana","compressible":true},"application/vnd.avistar+xml":{"source":"iana","compressible":true},"application/vnd.balsamiq.bmml+xml":{"source":"iana","compressible":true,"extensions":["bmml"]},"application/vnd.balsamiq.bmpr":{"source":"iana"},"application/vnd.banana-accounting":{"source":"iana"},"application/vnd.bbf.usp.error":{"source":"iana"},"application/vnd.bbf.usp.msg":{"source":"iana"},"application/vnd.bbf.usp.msg+json":{"source":"iana","compressible":true},"application/vnd.bekitzur-stech+json":{"source":"iana","compressible":true},"application/vnd.bint.med-content":{"source":"iana"},"application/vnd.biopax.rdf+xml":{"source":"iana","compressible":true},"application/vnd.blink-idb-value-wrapper":{"source":"iana"},"application/vnd.blueice.multipass":{"source":"iana","extensions":["mpm"]},"application/vnd.bluetooth.ep.oob":{"source":"iana"},"application/vnd.bluetooth.le.oob":{"source":"iana"},"application/vnd.bmi":{"source":"iana","extensions":["bmi"]},"application/vnd.bpf":{"source":"iana"},"application/vnd.bpf3":{"source":"iana"},"application/vnd.businessobjects":{"source":"iana","extensions":["rep"]},"application/vnd.byu.uapi+json":{"source":"iana","compressible":true},"application/vnd.cab-jscript":{"source":"iana"},"application/vnd.canon-cpdl":{"source":"iana"},"application/vnd.canon-lips":{"source":"iana"},"application/vnd.capasystems-pg+json":{"source":"iana","compressible":true},"application/vnd.cendio.thinlinc.clientconf":{"source":"iana"},"application/vnd.century-systems.tcp_stream":{"source":"iana"},"application/vnd.chemdraw+xml":{"source":"iana","compressible":true,"extensions":["cdxml"]},"application/vnd.chess-pgn":{"source":"iana"},"application/vnd.chipnuts.karaoke-mmd":{"source":"iana","extensions":["mmd"]},"application/vnd.ciedi":{"source":"iana"},"application/vnd.cinderella":{"source":"iana","extensions":["cdy"]},"application/vnd.cirpack.isdn-ext":{"source":"iana"},"application/vnd.citationstyles.style+xml":{"source":"iana","compressible":true,"extensions":["csl"]},"application/vnd.claymore":{"source":"iana","extensions":["cla"]},"application/vnd.cloanto.rp9":{"source":"iana","extensions":["rp9"]},"application/vnd.clonk.c4group":{"source":"iana","extensions":["c4g","c4d","c4f","c4p","c4u"]},"application/vnd.cluetrust.cartomobile-config":{"source":"iana","extensions":["c11amc"]},"application/vnd.cluetrust.cartomobile-config-pkg":{"source":"iana","extensions":["c11amz"]},"application/vnd.coffeescript":{"source":"iana"},"application/vnd.collabio.xodocuments.document":{"source":"iana"},"application/vnd.collabio.xodocuments.document-template":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation-template":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet-template":{"source":"iana"},"application/vnd.collection+json":{"source":"iana","compressible":true},"application/vnd.collection.doc+json":{"source":"iana","compressible":true},"application/vnd.collection.next+json":{"source":"iana","compressible":true},"application/vnd.comicbook+zip":{"source":"iana","compressible":false},"application/vnd.comicbook-rar":{"source":"iana"},"application/vnd.commerce-battelle":{"source":"iana"},"application/vnd.commonspace":{"source":"iana","extensions":["csp"]},"application/vnd.contact.cmsg":{"source":"iana","extensions":["cdbcmsg"]},"application/vnd.coreos.ignition+json":{"source":"iana","compressible":true},"application/vnd.cosmocaller":{"source":"iana","extensions":["cmc"]},"application/vnd.crick.clicker":{"source":"iana","extensions":["clkx"]},"application/vnd.crick.clicker.keyboard":{"source":"iana","extensions":["clkk"]},"application/vnd.crick.clicker.palette":{"source":"iana","extensions":["clkp"]},"application/vnd.crick.clicker.template":{"source":"iana","extensions":["clkt"]},"application/vnd.crick.clicker.wordbank":{"source":"iana","extensions":["clkw"]},"application/vnd.criticaltools.wbs+xml":{"source":"iana","compressible":true,"extensions":["wbs"]},"application/vnd.cryptii.pipe+json":{"source":"iana","compressible":true},"application/vnd.crypto-shade-file":{"source":"iana"},"application/vnd.cryptomator.encrypted":{"source":"iana"},"application/vnd.cryptomator.vault":{"source":"iana"},"application/vnd.ctc-posml":{"source":"iana","extensions":["pml"]},"application/vnd.ctct.ws+xml":{"source":"iana","compressible":true},"application/vnd.cups-pdf":{"source":"iana"},"application/vnd.cups-postscript":{"source":"iana"},"application/vnd.cups-ppd":{"source":"iana","extensions":["ppd"]},"application/vnd.cups-raster":{"source":"iana"},"application/vnd.cups-raw":{"source":"iana"},"application/vnd.curl":{"source":"iana"},"application/vnd.curl.car":{"source":"apache","extensions":["car"]},"application/vnd.curl.pcurl":{"source":"apache","extensions":["pcurl"]},"application/vnd.cyan.dean.root+xml":{"source":"iana","compressible":true},"application/vnd.cybank":{"source":"iana"},"application/vnd.cyclonedx+json":{"source":"iana","compressible":true},"application/vnd.cyclonedx+xml":{"source":"iana","compressible":true},"application/vnd.d2l.coursepackage1p0+zip":{"source":"iana","compressible":false},"application/vnd.d3m-dataset":{"source":"iana"},"application/vnd.d3m-problem":{"source":"iana"},"application/vnd.dart":{"source":"iana","compressible":true,"extensions":["dart"]},"application/vnd.data-vision.rdz":{"source":"iana","extensions":["rdz"]},"application/vnd.datapackage+json":{"source":"iana","compressible":true},"application/vnd.dataresource+json":{"source":"iana","compressible":true},"application/vnd.dbf":{"source":"iana","extensions":["dbf"]},"application/vnd.debian.binary-package":{"source":"iana"},"application/vnd.dece.data":{"source":"iana","extensions":["uvf","uvvf","uvd","uvvd"]},"application/vnd.dece.ttml+xml":{"source":"iana","compressible":true,"extensions":["uvt","uvvt"]},"application/vnd.dece.unspecified":{"source":"iana","extensions":["uvx","uvvx"]},"application/vnd.dece.zip":{"source":"iana","extensions":["uvz","uvvz"]},"application/vnd.denovo.fcselayout-link":{"source":"iana","extensions":["fe_launch"]},"application/vnd.desmume.movie":{"source":"iana"},"application/vnd.dir-bi.plate-dl-nosuffix":{"source":"iana"},"application/vnd.dm.delegation+xml":{"source":"iana","compressible":true},"application/vnd.dna":{"source":"iana","extensions":["dna"]},"application/vnd.document+json":{"source":"iana","compressible":true},"application/vnd.dolby.mlp":{"source":"apache","extensions":["mlp"]},"application/vnd.dolby.mobile.1":{"source":"iana"},"application/vnd.dolby.mobile.2":{"source":"iana"},"application/vnd.doremir.scorecloud-binary-document":{"source":"iana"},"application/vnd.dpgraph":{"source":"iana","extensions":["dpg"]},"application/vnd.dreamfactory":{"source":"iana","extensions":["dfac"]},"application/vnd.drive+json":{"source":"iana","compressible":true},"application/vnd.ds-keypoint":{"source":"apache","extensions":["kpxx"]},"application/vnd.dtg.local":{"source":"iana"},"application/vnd.dtg.local.flash":{"source":"iana"},"application/vnd.dtg.local.html":{"source":"iana"},"application/vnd.dvb.ait":{"source":"iana","extensions":["ait"]},"application/vnd.dvb.dvbisl+xml":{"source":"iana","compressible":true},"application/vnd.dvb.dvbj":{"source":"iana"},"application/vnd.dvb.esgcontainer":{"source":"iana"},"application/vnd.dvb.ipdcdftnotifaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess2":{"source":"iana"},"application/vnd.dvb.ipdcesgpdd":{"source":"iana"},"application/vnd.dvb.ipdcroaming":{"source":"iana"},"application/vnd.dvb.iptv.alfec-base":{"source":"iana"},"application/vnd.dvb.iptv.alfec-enhancement":{"source":"iana"},"application/vnd.dvb.notif-aggregate-root+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-container+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-generic+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-msglist+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-request+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-response+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-init+xml":{"source":"iana","compressible":true},"application/vnd.dvb.pfr":{"source":"iana"},"application/vnd.dvb.service":{"source":"iana","extensions":["svc"]},"application/vnd.dxr":{"source":"iana"},"application/vnd.dynageo":{"source":"iana","extensions":["geo"]},"application/vnd.dzr":{"source":"iana"},"application/vnd.easykaraoke.cdgdownload":{"source":"iana"},"application/vnd.ecdis-update":{"source":"iana"},"application/vnd.ecip.rlp":{"source":"iana"},"application/vnd.eclipse.ditto+json":{"source":"iana","compressible":true},"application/vnd.ecowin.chart":{"source":"iana","extensions":["mag"]},"application/vnd.ecowin.filerequest":{"source":"iana"},"application/vnd.ecowin.fileupdate":{"source":"iana"},"application/vnd.ecowin.series":{"source":"iana"},"application/vnd.ecowin.seriesrequest":{"source":"iana"},"application/vnd.ecowin.seriesupdate":{"source":"iana"},"application/vnd.efi.img":{"source":"iana"},"application/vnd.efi.iso":{"source":"iana"},"application/vnd.emclient.accessrequest+xml":{"source":"iana","compressible":true},"application/vnd.enliven":{"source":"iana","extensions":["nml"]},"application/vnd.enphase.envoy":{"source":"iana"},"application/vnd.eprints.data+xml":{"source":"iana","compressible":true},"application/vnd.epson.esf":{"source":"iana","extensions":["esf"]},"application/vnd.epson.msf":{"source":"iana","extensions":["msf"]},"application/vnd.epson.quickanime":{"source":"iana","extensions":["qam"]},"application/vnd.epson.salt":{"source":"iana","extensions":["slt"]},"application/vnd.epson.ssf":{"source":"iana","extensions":["ssf"]},"application/vnd.ericsson.quickcall":{"source":"iana"},"application/vnd.espass-espass+zip":{"source":"iana","compressible":false},"application/vnd.eszigno3+xml":{"source":"iana","compressible":true,"extensions":["es3","et3"]},"application/vnd.etsi.aoc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.asic-e+zip":{"source":"iana","compressible":false},"application/vnd.etsi.asic-s+zip":{"source":"iana","compressible":false},"application/vnd.etsi.cug+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvcommand+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-bc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-cod+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-npvr+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvservice+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsync+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvueprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mcid+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mheg5":{"source":"iana"},"application/vnd.etsi.overload-control-policy-dataset+xml":{"source":"iana","compressible":true},"application/vnd.etsi.pstn+xml":{"source":"iana","compressible":true},"application/vnd.etsi.sci+xml":{"source":"iana","compressible":true},"application/vnd.etsi.simservs+xml":{"source":"iana","compressible":true},"application/vnd.etsi.timestamp-token":{"source":"iana"},"application/vnd.etsi.tsl+xml":{"source":"iana","compressible":true},"application/vnd.etsi.tsl.der":{"source":"iana"},"application/vnd.eu.kasparian.car+json":{"source":"iana","compressible":true},"application/vnd.eudora.data":{"source":"iana"},"application/vnd.evolv.ecig.profile":{"source":"iana"},"application/vnd.evolv.ecig.settings":{"source":"iana"},"application/vnd.evolv.ecig.theme":{"source":"iana"},"application/vnd.exstream-empower+zip":{"source":"iana","compressible":false},"application/vnd.exstream-package":{"source":"iana"},"application/vnd.ezpix-album":{"source":"iana","extensions":["ez2"]},"application/vnd.ezpix-package":{"source":"iana","extensions":["ez3"]},"application/vnd.f-secure.mobile":{"source":"iana"},"application/vnd.familysearch.gedcom+zip":{"source":"iana","compressible":false},"application/vnd.fastcopy-disk-image":{"source":"iana"},"application/vnd.fdf":{"source":"iana","extensions":["fdf"]},"application/vnd.fdsn.mseed":{"source":"iana","extensions":["mseed"]},"application/vnd.fdsn.seed":{"source":"iana","extensions":["seed","dataless"]},"application/vnd.ffsns":{"source":"iana"},"application/vnd.ficlab.flb+zip":{"source":"iana","compressible":false},"application/vnd.filmit.zfc":{"source":"iana"},"application/vnd.fints":{"source":"iana"},"application/vnd.firemonkeys.cloudcell":{"source":"iana"},"application/vnd.flographit":{"source":"iana","extensions":["gph"]},"application/vnd.fluxtime.clip":{"source":"iana","extensions":["ftc"]},"application/vnd.font-fontforge-sfd":{"source":"iana"},"application/vnd.framemaker":{"source":"iana","extensions":["fm","frame","maker","book"]},"application/vnd.frogans.fnc":{"source":"iana","extensions":["fnc"]},"application/vnd.frogans.ltf":{"source":"iana","extensions":["ltf"]},"application/vnd.fsc.weblaunch":{"source":"iana","extensions":["fsc"]},"application/vnd.fujifilm.fb.docuworks":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.binder":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.container":{"source":"iana"},"application/vnd.fujifilm.fb.jfi+xml":{"source":"iana","compressible":true},"application/vnd.fujitsu.oasys":{"source":"iana","extensions":["oas"]},"application/vnd.fujitsu.oasys2":{"source":"iana","extensions":["oa2"]},"application/vnd.fujitsu.oasys3":{"source":"iana","extensions":["oa3"]},"application/vnd.fujitsu.oasysgp":{"source":"iana","extensions":["fg5"]},"application/vnd.fujitsu.oasysprs":{"source":"iana","extensions":["bh2"]},"application/vnd.fujixerox.art-ex":{"source":"iana"},"application/vnd.fujixerox.art4":{"source":"iana"},"application/vnd.fujixerox.ddd":{"source":"iana","extensions":["ddd"]},"application/vnd.fujixerox.docuworks":{"source":"iana","extensions":["xdw"]},"application/vnd.fujixerox.docuworks.binder":{"source":"iana","extensions":["xbd"]},"application/vnd.fujixerox.docuworks.container":{"source":"iana"},"application/vnd.fujixerox.hbpl":{"source":"iana"},"application/vnd.fut-misnet":{"source":"iana"},"application/vnd.futoin+cbor":{"source":"iana"},"application/vnd.futoin+json":{"source":"iana","compressible":true},"application/vnd.fuzzysheet":{"source":"iana","extensions":["fzs"]},"application/vnd.genomatix.tuxedo":{"source":"iana","extensions":["txd"]},"application/vnd.gentics.grd+json":{"source":"iana","compressible":true},"application/vnd.geo+json":{"source":"iana","compressible":true},"application/vnd.geocube+xml":{"source":"iana","compressible":true},"application/vnd.geogebra.file":{"source":"iana","extensions":["ggb"]},"application/vnd.geogebra.slides":{"source":"iana"},"application/vnd.geogebra.tool":{"source":"iana","extensions":["ggt"]},"application/vnd.geometry-explorer":{"source":"iana","extensions":["gex","gre"]},"application/vnd.geonext":{"source":"iana","extensions":["gxt"]},"application/vnd.geoplan":{"source":"iana","extensions":["g2w"]},"application/vnd.geospace":{"source":"iana","extensions":["g3w"]},"application/vnd.gerber":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt-response":{"source":"iana"},"application/vnd.gmx":{"source":"iana","extensions":["gmx"]},"application/vnd.google-apps.document":{"compressible":false,"extensions":["gdoc"]},"application/vnd.google-apps.presentation":{"compressible":false,"extensions":["gslides"]},"application/vnd.google-apps.spreadsheet":{"compressible":false,"extensions":["gsheet"]},"application/vnd.google-earth.kml+xml":{"source":"iana","compressible":true,"extensions":["kml"]},"application/vnd.google-earth.kmz":{"source":"iana","compressible":false,"extensions":["kmz"]},"application/vnd.gov.sk.e-form+xml":{"source":"iana","compressible":true},"application/vnd.gov.sk.e-form+zip":{"source":"iana","compressible":false},"application/vnd.gov.sk.xmldatacontainer+xml":{"source":"iana","compressible":true},"application/vnd.grafeq":{"source":"iana","extensions":["gqf","gqs"]},"application/vnd.gridmp":{"source":"iana"},"application/vnd.groove-account":{"source":"iana","extensions":["gac"]},"application/vnd.groove-help":{"source":"iana","extensions":["ghf"]},"application/vnd.groove-identity-message":{"source":"iana","extensions":["gim"]},"application/vnd.groove-injector":{"source":"iana","extensions":["grv"]},"application/vnd.groove-tool-message":{"source":"iana","extensions":["gtm"]},"application/vnd.groove-tool-template":{"source":"iana","extensions":["tpl"]},"application/vnd.groove-vcard":{"source":"iana","extensions":["vcg"]},"application/vnd.hal+json":{"source":"iana","compressible":true},"application/vnd.hal+xml":{"source":"iana","compressible":true,"extensions":["hal"]},"application/vnd.handheld-entertainment+xml":{"source":"iana","compressible":true,"extensions":["zmm"]},"application/vnd.hbci":{"source":"iana","extensions":["hbci"]},"application/vnd.hc+json":{"source":"iana","compressible":true},"application/vnd.hcl-bireports":{"source":"iana"},"application/vnd.hdt":{"source":"iana"},"application/vnd.heroku+json":{"source":"iana","compressible":true},"application/vnd.hhe.lesson-player":{"source":"iana","extensions":["les"]},"application/vnd.hl7cda+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hl7v2+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hp-hpgl":{"source":"iana","extensions":["hpgl"]},"application/vnd.hp-hpid":{"source":"iana","extensions":["hpid"]},"application/vnd.hp-hps":{"source":"iana","extensions":["hps"]},"application/vnd.hp-jlyt":{"source":"iana","extensions":["jlt"]},"application/vnd.hp-pcl":{"source":"iana","extensions":["pcl"]},"application/vnd.hp-pclxl":{"source":"iana","extensions":["pclxl"]},"application/vnd.httphone":{"source":"iana"},"application/vnd.hydrostatix.sof-data":{"source":"iana","extensions":["sfd-hdstx"]},"application/vnd.hyper+json":{"source":"iana","compressible":true},"application/vnd.hyper-item+json":{"source":"iana","compressible":true},"application/vnd.hyperdrive+json":{"source":"iana","compressible":true},"application/vnd.hzn-3d-crossword":{"source":"iana"},"application/vnd.ibm.afplinedata":{"source":"iana"},"application/vnd.ibm.electronic-media":{"source":"iana"},"application/vnd.ibm.minipay":{"source":"iana","extensions":["mpy"]},"application/vnd.ibm.modcap":{"source":"iana","extensions":["afp","listafp","list3820"]},"application/vnd.ibm.rights-management":{"source":"iana","extensions":["irm"]},"application/vnd.ibm.secure-container":{"source":"iana","extensions":["sc"]},"application/vnd.iccprofile":{"source":"iana","extensions":["icc","icm"]},"application/vnd.ieee.1905":{"source":"iana"},"application/vnd.igloader":{"source":"iana","extensions":["igl"]},"application/vnd.imagemeter.folder+zip":{"source":"iana","compressible":false},"application/vnd.imagemeter.image+zip":{"source":"iana","compressible":false},"application/vnd.immervision-ivp":{"source":"iana","extensions":["ivp"]},"application/vnd.immervision-ivu":{"source":"iana","extensions":["ivu"]},"application/vnd.ims.imsccv1p1":{"source":"iana"},"application/vnd.ims.imsccv1p2":{"source":"iana"},"application/vnd.ims.imsccv1p3":{"source":"iana"},"application/vnd.ims.lis.v2.result+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolconsumerprofile+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy.id+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings.simple+json":{"source":"iana","compressible":true},"application/vnd.informedcontrol.rms+xml":{"source":"iana","compressible":true},"application/vnd.informix-visionary":{"source":"iana"},"application/vnd.infotech.project":{"source":"iana"},"application/vnd.infotech.project+xml":{"source":"iana","compressible":true},"application/vnd.innopath.wamp.notification":{"source":"iana"},"application/vnd.insors.igm":{"source":"iana","extensions":["igm"]},"application/vnd.intercon.formnet":{"source":"iana","extensions":["xpw","xpx"]},"application/vnd.intergeo":{"source":"iana","extensions":["i2g"]},"application/vnd.intertrust.digibox":{"source":"iana"},"application/vnd.intertrust.nncp":{"source":"iana"},"application/vnd.intu.qbo":{"source":"iana","extensions":["qbo"]},"application/vnd.intu.qfx":{"source":"iana","extensions":["qfx"]},"application/vnd.iptc.g2.catalogitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.conceptitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.knowledgeitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsmessage+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.packageitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.planningitem+xml":{"source":"iana","compressible":true},"application/vnd.ipunplugged.rcprofile":{"source":"iana","extensions":["rcprofile"]},"application/vnd.irepository.package+xml":{"source":"iana","compressible":true,"extensions":["irp"]},"application/vnd.is-xpr":{"source":"iana","extensions":["xpr"]},"application/vnd.isac.fcs":{"source":"iana","extensions":["fcs"]},"application/vnd.iso11783-10+zip":{"source":"iana","compressible":false},"application/vnd.jam":{"source":"iana","extensions":["jam"]},"application/vnd.japannet-directory-service":{"source":"iana"},"application/vnd.japannet-jpnstore-wakeup":{"source":"iana"},"application/vnd.japannet-payment-wakeup":{"source":"iana"},"application/vnd.japannet-registration":{"source":"iana"},"application/vnd.japannet-registration-wakeup":{"source":"iana"},"application/vnd.japannet-setstore-wakeup":{"source":"iana"},"application/vnd.japannet-verification":{"source":"iana"},"application/vnd.japannet-verification-wakeup":{"source":"iana"},"application/vnd.jcp.javame.midlet-rms":{"source":"iana","extensions":["rms"]},"application/vnd.jisp":{"source":"iana","extensions":["jisp"]},"application/vnd.joost.joda-archive":{"source":"iana","extensions":["joda"]},"application/vnd.jsk.isdn-ngn":{"source":"iana"},"application/vnd.kahootz":{"source":"iana","extensions":["ktz","ktr"]},"application/vnd.kde.karbon":{"source":"iana","extensions":["karbon"]},"application/vnd.kde.kchart":{"source":"iana","extensions":["chrt"]},"application/vnd.kde.kformula":{"source":"iana","extensions":["kfo"]},"application/vnd.kde.kivio":{"source":"iana","extensions":["flw"]},"application/vnd.kde.kontour":{"source":"iana","extensions":["kon"]},"application/vnd.kde.kpresenter":{"source":"iana","extensions":["kpr","kpt"]},"application/vnd.kde.kspread":{"source":"iana","extensions":["ksp"]},"application/vnd.kde.kword":{"source":"iana","extensions":["kwd","kwt"]},"application/vnd.kenameaapp":{"source":"iana","extensions":["htke"]},"application/vnd.kidspiration":{"source":"iana","extensions":["kia"]},"application/vnd.kinar":{"source":"iana","extensions":["kne","knp"]},"application/vnd.koan":{"source":"iana","extensions":["skp","skd","skt","skm"]},"application/vnd.kodak-descriptor":{"source":"iana","extensions":["sse"]},"application/vnd.las":{"source":"iana"},"application/vnd.las.las+json":{"source":"iana","compressible":true},"application/vnd.las.las+xml":{"source":"iana","compressible":true,"extensions":["lasxml"]},"application/vnd.laszip":{"source":"iana"},"application/vnd.leap+json":{"source":"iana","compressible":true},"application/vnd.liberty-request+xml":{"source":"iana","compressible":true},"application/vnd.llamagraphics.life-balance.desktop":{"source":"iana","extensions":["lbd"]},"application/vnd.llamagraphics.life-balance.exchange+xml":{"source":"iana","compressible":true,"extensions":["lbe"]},"application/vnd.logipipe.circuit+zip":{"source":"iana","compressible":false},"application/vnd.loom":{"source":"iana"},"application/vnd.lotus-1-2-3":{"source":"iana","extensions":["123"]},"application/vnd.lotus-approach":{"source":"iana","extensions":["apr"]},"application/vnd.lotus-freelance":{"source":"iana","extensions":["pre"]},"application/vnd.lotus-notes":{"source":"iana","extensions":["nsf"]},"application/vnd.lotus-organizer":{"source":"iana","extensions":["org"]},"application/vnd.lotus-screencam":{"source":"iana","extensions":["scm"]},"application/vnd.lotus-wordpro":{"source":"iana","extensions":["lwp"]},"application/vnd.macports.portpkg":{"source":"iana","extensions":["portpkg"]},"application/vnd.mapbox-vector-tile":{"source":"iana","extensions":["mvt"]},"application/vnd.marlin.drm.actiontoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.conftoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.license+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.mdcf":{"source":"iana"},"application/vnd.mason+json":{"source":"iana","compressible":true},"application/vnd.maxar.archive.3tz+zip":{"source":"iana","compressible":false},"application/vnd.maxmind.maxmind-db":{"source":"iana"},"application/vnd.mcd":{"source":"iana","extensions":["mcd"]},"application/vnd.medcalcdata":{"source":"iana","extensions":["mc1"]},"application/vnd.mediastation.cdkey":{"source":"iana","extensions":["cdkey"]},"application/vnd.meridian-slingshot":{"source":"iana"},"application/vnd.mfer":{"source":"iana","extensions":["mwf"]},"application/vnd.mfmp":{"source":"iana","extensions":["mfm"]},"application/vnd.micro+json":{"source":"iana","compressible":true},"application/vnd.micrografx.flo":{"source":"iana","extensions":["flo"]},"application/vnd.micrografx.igx":{"source":"iana","extensions":["igx"]},"application/vnd.microsoft.portable-executable":{"source":"iana"},"application/vnd.microsoft.windows.thumbnail-cache":{"source":"iana"},"application/vnd.miele+json":{"source":"iana","compressible":true},"application/vnd.mif":{"source":"iana","extensions":["mif"]},"application/vnd.minisoft-hp3000-save":{"source":"iana"},"application/vnd.mitsubishi.misty-guard.trustweb":{"source":"iana"},"application/vnd.mobius.daf":{"source":"iana","extensions":["daf"]},"application/vnd.mobius.dis":{"source":"iana","extensions":["dis"]},"application/vnd.mobius.mbk":{"source":"iana","extensions":["mbk"]},"application/vnd.mobius.mqy":{"source":"iana","extensions":["mqy"]},"application/vnd.mobius.msl":{"source":"iana","extensions":["msl"]},"application/vnd.mobius.plc":{"source":"iana","extensions":["plc"]},"application/vnd.mobius.txf":{"source":"iana","extensions":["txf"]},"application/vnd.mophun.application":{"source":"iana","extensions":["mpn"]},"application/vnd.mophun.certificate":{"source":"iana","extensions":["mpc"]},"application/vnd.motorola.flexsuite":{"source":"iana"},"application/vnd.motorola.flexsuite.adsi":{"source":"iana"},"application/vnd.motorola.flexsuite.fis":{"source":"iana"},"application/vnd.motorola.flexsuite.gotap":{"source":"iana"},"application/vnd.motorola.flexsuite.kmr":{"source":"iana"},"application/vnd.motorola.flexsuite.ttc":{"source":"iana"},"application/vnd.motorola.flexsuite.wem":{"source":"iana"},"application/vnd.motorola.iprm":{"source":"iana"},"application/vnd.mozilla.xul+xml":{"source":"iana","compressible":true,"extensions":["xul"]},"application/vnd.ms-3mfdocument":{"source":"iana"},"application/vnd.ms-artgalry":{"source":"iana","extensions":["cil"]},"application/vnd.ms-asf":{"source":"iana"},"application/vnd.ms-cab-compressed":{"source":"iana","extensions":["cab"]},"application/vnd.ms-color.iccprofile":{"source":"apache"},"application/vnd.ms-excel":{"source":"iana","compressible":false,"extensions":["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-excel.addin.macroenabled.12":{"source":"iana","extensions":["xlam"]},"application/vnd.ms-excel.sheet.binary.macroenabled.12":{"source":"iana","extensions":["xlsb"]},"application/vnd.ms-excel.sheet.macroenabled.12":{"source":"iana","extensions":["xlsm"]},"application/vnd.ms-excel.template.macroenabled.12":{"source":"iana","extensions":["xltm"]},"application/vnd.ms-fontobject":{"source":"iana","compressible":true,"extensions":["eot"]},"application/vnd.ms-htmlhelp":{"source":"iana","extensions":["chm"]},"application/vnd.ms-ims":{"source":"iana","extensions":["ims"]},"application/vnd.ms-lrm":{"source":"iana","extensions":["lrm"]},"application/vnd.ms-office.activex+xml":{"source":"iana","compressible":true},"application/vnd.ms-officetheme":{"source":"iana","extensions":["thmx"]},"application/vnd.ms-opentype":{"source":"apache","compressible":true},"application/vnd.ms-outlook":{"compressible":false,"extensions":["msg"]},"application/vnd.ms-package.obfuscated-opentype":{"source":"apache"},"application/vnd.ms-pki.seccat":{"source":"apache","extensions":["cat"]},"application/vnd.ms-pki.stl":{"source":"apache","extensions":["stl"]},"application/vnd.ms-playready.initiator+xml":{"source":"iana","compressible":true},"application/vnd.ms-powerpoint":{"source":"iana","compressible":false,"extensions":["ppt","pps","pot"]},"application/vnd.ms-powerpoint.addin.macroenabled.12":{"source":"iana","extensions":["ppam"]},"application/vnd.ms-powerpoint.presentation.macroenabled.12":{"source":"iana","extensions":["pptm"]},"application/vnd.ms-powerpoint.slide.macroenabled.12":{"source":"iana","extensions":["sldm"]},"application/vnd.ms-powerpoint.slideshow.macroenabled.12":{"source":"iana","extensions":["ppsm"]},"application/vnd.ms-powerpoint.template.macroenabled.12":{"source":"iana","extensions":["potm"]},"application/vnd.ms-printdevicecapabilities+xml":{"source":"iana","compressible":true},"application/vnd.ms-printing.printticket+xml":{"source":"apache","compressible":true},"application/vnd.ms-printschematicket+xml":{"source":"iana","compressible":true},"application/vnd.ms-project":{"source":"iana","extensions":["mpp","mpt"]},"application/vnd.ms-tnef":{"source":"iana"},"application/vnd.ms-windows.devicepairing":{"source":"iana"},"application/vnd.ms-windows.nwprinting.oob":{"source":"iana"},"application/vnd.ms-windows.printerpairing":{"source":"iana"},"application/vnd.ms-windows.wsd.oob":{"source":"iana"},"application/vnd.ms-wmdrm.lic-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.lic-resp":{"source":"iana"},"application/vnd.ms-wmdrm.meter-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.meter-resp":{"source":"iana"},"application/vnd.ms-word.document.macroenabled.12":{"source":"iana","extensions":["docm"]},"application/vnd.ms-word.template.macroenabled.12":{"source":"iana","extensions":["dotm"]},"application/vnd.ms-works":{"source":"iana","extensions":["wps","wks","wcm","wdb"]},"application/vnd.ms-wpl":{"source":"iana","extensions":["wpl"]},"application/vnd.ms-xpsdocument":{"source":"iana","compressible":false,"extensions":["xps"]},"application/vnd.msa-disk-image":{"source":"iana"},"application/vnd.mseq":{"source":"iana","extensions":["mseq"]},"application/vnd.msign":{"source":"iana"},"application/vnd.multiad.creator":{"source":"iana"},"application/vnd.multiad.creator.cif":{"source":"iana"},"application/vnd.music-niff":{"source":"iana"},"application/vnd.musician":{"source":"iana","extensions":["mus"]},"application/vnd.muvee.style":{"source":"iana","extensions":["msty"]},"application/vnd.mynfc":{"source":"iana","extensions":["taglet"]},"application/vnd.nacamar.ybrid+json":{"source":"iana","compressible":true},"application/vnd.ncd.control":{"source":"iana"},"application/vnd.ncd.reference":{"source":"iana"},"application/vnd.nearst.inv+json":{"source":"iana","compressible":true},"application/vnd.nebumind.line":{"source":"iana"},"application/vnd.nervana":{"source":"iana"},"application/vnd.netfpx":{"source":"iana"},"application/vnd.neurolanguage.nlu":{"source":"iana","extensions":["nlu"]},"application/vnd.nimn":{"source":"iana"},"application/vnd.nintendo.nitro.rom":{"source":"iana"},"application/vnd.nintendo.snes.rom":{"source":"iana"},"application/vnd.nitf":{"source":"iana","extensions":["ntf","nitf"]},"application/vnd.noblenet-directory":{"source":"iana","extensions":["nnd"]},"application/vnd.noblenet-sealer":{"source":"iana","extensions":["nns"]},"application/vnd.noblenet-web":{"source":"iana","extensions":["nnw"]},"application/vnd.nokia.catalogs":{"source":"iana"},"application/vnd.nokia.conml+wbxml":{"source":"iana"},"application/vnd.nokia.conml+xml":{"source":"iana","compressible":true},"application/vnd.nokia.iptv.config+xml":{"source":"iana","compressible":true},"application/vnd.nokia.isds-radio-presets":{"source":"iana"},"application/vnd.nokia.landmark+wbxml":{"source":"iana"},"application/vnd.nokia.landmark+xml":{"source":"iana","compressible":true},"application/vnd.nokia.landmarkcollection+xml":{"source":"iana","compressible":true},"application/vnd.nokia.n-gage.ac+xml":{"source":"iana","compressible":true,"extensions":["ac"]},"application/vnd.nokia.n-gage.data":{"source":"iana","extensions":["ngdat"]},"application/vnd.nokia.n-gage.symbian.install":{"source":"iana","extensions":["n-gage"]},"application/vnd.nokia.ncd":{"source":"iana"},"application/vnd.nokia.pcd+wbxml":{"source":"iana"},"application/vnd.nokia.pcd+xml":{"source":"iana","compressible":true},"application/vnd.nokia.radio-preset":{"source":"iana","extensions":["rpst"]},"application/vnd.nokia.radio-presets":{"source":"iana","extensions":["rpss"]},"application/vnd.novadigm.edm":{"source":"iana","extensions":["edm"]},"application/vnd.novadigm.edx":{"source":"iana","extensions":["edx"]},"application/vnd.novadigm.ext":{"source":"iana","extensions":["ext"]},"application/vnd.ntt-local.content-share":{"source":"iana"},"application/vnd.ntt-local.file-transfer":{"source":"iana"},"application/vnd.ntt-local.ogw_remote-access":{"source":"iana"},"application/vnd.ntt-local.sip-ta_remote":{"source":"iana"},"application/vnd.ntt-local.sip-ta_tcp_stream":{"source":"iana"},"application/vnd.oasis.opendocument.chart":{"source":"iana","extensions":["odc"]},"application/vnd.oasis.opendocument.chart-template":{"source":"iana","extensions":["otc"]},"application/vnd.oasis.opendocument.database":{"source":"iana","extensions":["odb"]},"application/vnd.oasis.opendocument.formula":{"source":"iana","extensions":["odf"]},"application/vnd.oasis.opendocument.formula-template":{"source":"iana","extensions":["odft"]},"application/vnd.oasis.opendocument.graphics":{"source":"iana","compressible":false,"extensions":["odg"]},"application/vnd.oasis.opendocument.graphics-template":{"source":"iana","extensions":["otg"]},"application/vnd.oasis.opendocument.image":{"source":"iana","extensions":["odi"]},"application/vnd.oasis.opendocument.image-template":{"source":"iana","extensions":["oti"]},"application/vnd.oasis.opendocument.presentation":{"source":"iana","compressible":false,"extensions":["odp"]},"application/vnd.oasis.opendocument.presentation-template":{"source":"iana","extensions":["otp"]},"application/vnd.oasis.opendocument.spreadsheet":{"source":"iana","compressible":false,"extensions":["ods"]},"application/vnd.oasis.opendocument.spreadsheet-template":{"source":"iana","extensions":["ots"]},"application/vnd.oasis.opendocument.text":{"source":"iana","compressible":false,"extensions":["odt"]},"application/vnd.oasis.opendocument.text-master":{"source":"iana","extensions":["odm"]},"application/vnd.oasis.opendocument.text-template":{"source":"iana","extensions":["ott"]},"application/vnd.oasis.opendocument.text-web":{"source":"iana","extensions":["oth"]},"application/vnd.obn":{"source":"iana"},"application/vnd.ocf+cbor":{"source":"iana"},"application/vnd.oci.image.manifest.v1+json":{"source":"iana","compressible":true},"application/vnd.oftn.l10n+json":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessdownload+xml":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessstreaming+xml":{"source":"iana","compressible":true},"application/vnd.oipf.cspg-hexbinary":{"source":"iana"},"application/vnd.oipf.dae.svg+xml":{"source":"iana","compressible":true},"application/vnd.oipf.dae.xhtml+xml":{"source":"iana","compressible":true},"application/vnd.oipf.mippvcontrolmessage+xml":{"source":"iana","compressible":true},"application/vnd.oipf.pae.gem":{"source":"iana"},"application/vnd.oipf.spdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.oipf.spdlist+xml":{"source":"iana","compressible":true},"application/vnd.oipf.ueprofile+xml":{"source":"iana","compressible":true},"application/vnd.oipf.userprofile+xml":{"source":"iana","compressible":true},"application/vnd.olpc-sugar":{"source":"iana","extensions":["xo"]},"application/vnd.oma-scws-config":{"source":"iana"},"application/vnd.oma-scws-http-request":{"source":"iana"},"application/vnd.oma-scws-http-response":{"source":"iana"},"application/vnd.oma.bcast.associated-procedure-parameter+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.drm-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.imd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.ltkm":{"source":"iana"},"application/vnd.oma.bcast.notification+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.provisioningtrigger":{"source":"iana"},"application/vnd.oma.bcast.sgboot":{"source":"iana"},"application/vnd.oma.bcast.sgdd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sgdu":{"source":"iana"},"application/vnd.oma.bcast.simple-symbol-container":{"source":"iana"},"application/vnd.oma.bcast.smartcard-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sprov+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.stkm":{"source":"iana"},"application/vnd.oma.cab-address-book+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-feature-handler+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-pcc+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-subs-invite+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-user-prefs+xml":{"source":"iana","compressible":true},"application/vnd.oma.dcd":{"source":"iana"},"application/vnd.oma.dcdc":{"source":"iana"},"application/vnd.oma.dd2+xml":{"source":"iana","compressible":true,"extensions":["dd2"]},"application/vnd.oma.drm.risd+xml":{"source":"iana","compressible":true},"application/vnd.oma.group-usage-list+xml":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+cbor":{"source":"iana"},"application/vnd.oma.lwm2m+json":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+tlv":{"source":"iana"},"application/vnd.oma.pal+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.detailed-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.final-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.groups+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.invocation-descriptor+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.optimized-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.push":{"source":"iana"},"application/vnd.oma.scidm.messages+xml":{"source":"iana","compressible":true},"application/vnd.oma.xcap-directory+xml":{"source":"iana","compressible":true},"application/vnd.omads-email+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-file+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-folder+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omaloc-supl-init":{"source":"iana"},"application/vnd.onepager":{"source":"iana"},"application/vnd.onepagertamp":{"source":"iana"},"application/vnd.onepagertamx":{"source":"iana"},"application/vnd.onepagertat":{"source":"iana"},"application/vnd.onepagertatp":{"source":"iana"},"application/vnd.onepagertatx":{"source":"iana"},"application/vnd.openblox.game+xml":{"source":"iana","compressible":true,"extensions":["obgx"]},"application/vnd.openblox.game-binary":{"source":"iana"},"application/vnd.openeye.oeb":{"source":"iana"},"application/vnd.openofficeorg.extension":{"source":"apache","extensions":["oxt"]},"application/vnd.openstreetmap.data+xml":{"source":"iana","compressible":true,"extensions":["osm"]},"application/vnd.opentimestamps.ots":{"source":"iana"},"application/vnd.openxmlformats-officedocument.custom-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.customxmlproperties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawing+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chart+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramcolors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramdata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramlayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramstyle+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.extended-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.commentauthors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.handoutmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesslide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{"source":"iana","compressible":false,"extensions":["pptx"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slide":{"source":"iana","extensions":["sldx"]},"application/vnd.openxmlformats-officedocument.presentationml.slide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidelayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidemaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideshow":{"source":"iana","extensions":["ppsx"]},"application/vnd.openxmlformats-officedocument.presentationml.slideshow.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideupdateinfo+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tablestyles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tags+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.template":{"source":"iana","extensions":["potx"]},"application/vnd.openxmlformats-officedocument.presentationml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.viewprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.calcchain+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.externallink+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcachedefinition+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcacherecords+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivottable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.querytable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionheaders+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionlog+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedstrings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{"source":"iana","compressible":false,"extensions":["xlsx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetmetadata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.tablesinglecells+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.template":{"source":"iana","extensions":["xltx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.usernames+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.volatiledependencies+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.theme+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.themeoverride+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.vmldrawing":{"source":"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{"source":"iana","compressible":false,"extensions":["docx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.glossary+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.fonttable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.template":{"source":"iana","extensions":["dotx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.websettings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.core-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.digital-signature-xmlsignature+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.relationships+xml":{"source":"iana","compressible":true},"application/vnd.oracle.resource+json":{"source":"iana","compressible":true},"application/vnd.orange.indata":{"source":"iana"},"application/vnd.osa.netdeploy":{"source":"iana"},"application/vnd.osgeo.mapguide.package":{"source":"iana","extensions":["mgp"]},"application/vnd.osgi.bundle":{"source":"iana"},"application/vnd.osgi.dp":{"source":"iana","extensions":["dp"]},"application/vnd.osgi.subsystem":{"source":"iana","extensions":["esa"]},"application/vnd.otps.ct-kip+xml":{"source":"iana","compressible":true},"application/vnd.oxli.countgraph":{"source":"iana"},"application/vnd.pagerduty+json":{"source":"iana","compressible":true},"application/vnd.palm":{"source":"iana","extensions":["pdb","pqa","oprc"]},"application/vnd.panoply":{"source":"iana"},"application/vnd.paos.xml":{"source":"iana"},"application/vnd.patentdive":{"source":"iana"},"application/vnd.patientecommsdoc":{"source":"iana"},"application/vnd.pawaafile":{"source":"iana","extensions":["paw"]},"application/vnd.pcos":{"source":"iana"},"application/vnd.pg.format":{"source":"iana","extensions":["str"]},"application/vnd.pg.osasli":{"source":"iana","extensions":["ei6"]},"application/vnd.piaccess.application-licence":{"source":"iana"},"application/vnd.picsel":{"source":"iana","extensions":["efif"]},"application/vnd.pmi.widget":{"source":"iana","extensions":["wg"]},"application/vnd.poc.group-advertisement+xml":{"source":"iana","compressible":true},"application/vnd.pocketlearn":{"source":"iana","extensions":["plf"]},"application/vnd.powerbuilder6":{"source":"iana","extensions":["pbd"]},"application/vnd.powerbuilder6-s":{"source":"iana"},"application/vnd.powerbuilder7":{"source":"iana"},"application/vnd.powerbuilder7-s":{"source":"iana"},"application/vnd.powerbuilder75":{"source":"iana"},"application/vnd.powerbuilder75-s":{"source":"iana"},"application/vnd.preminet":{"source":"iana"},"application/vnd.previewsystems.box":{"source":"iana","extensions":["box"]},"application/vnd.proteus.magazine":{"source":"iana","extensions":["mgz"]},"application/vnd.psfs":{"source":"iana"},"application/vnd.publishare-delta-tree":{"source":"iana","extensions":["qps"]},"application/vnd.pvi.ptid1":{"source":"iana","extensions":["ptid"]},"application/vnd.pwg-multiplexed":{"source":"iana"},"application/vnd.pwg-xhtml-print+xml":{"source":"iana","compressible":true},"application/vnd.qualcomm.brew-app-res":{"source":"iana"},"application/vnd.quarantainenet":{"source":"iana"},"application/vnd.quark.quarkxpress":{"source":"iana","extensions":["qxd","qxt","qwd","qwt","qxl","qxb"]},"application/vnd.quobject-quoxdocument":{"source":"iana"},"application/vnd.radisys.moml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conn+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-stream+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-base+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-detect+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-sendrecv+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-group+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-speech+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-transform+xml":{"source":"iana","compressible":true},"application/vnd.rainstor.data":{"source":"iana"},"application/vnd.rapid":{"source":"iana"},"application/vnd.rar":{"source":"iana","extensions":["rar"]},"application/vnd.realvnc.bed":{"source":"iana","extensions":["bed"]},"application/vnd.recordare.musicxml":{"source":"iana","extensions":["mxl"]},"application/vnd.recordare.musicxml+xml":{"source":"iana","compressible":true,"extensions":["musicxml"]},"application/vnd.renlearn.rlprint":{"source":"iana"},"application/vnd.resilient.logic":{"source":"iana"},"application/vnd.restful+json":{"source":"iana","compressible":true},"application/vnd.rig.cryptonote":{"source":"iana","extensions":["cryptonote"]},"application/vnd.rim.cod":{"source":"apache","extensions":["cod"]},"application/vnd.rn-realmedia":{"source":"apache","extensions":["rm"]},"application/vnd.rn-realmedia-vbr":{"source":"apache","extensions":["rmvb"]},"application/vnd.route66.link66+xml":{"source":"iana","compressible":true,"extensions":["link66"]},"application/vnd.rs-274x":{"source":"iana"},"application/vnd.ruckus.download":{"source":"iana"},"application/vnd.s3sms":{"source":"iana"},"application/vnd.sailingtracker.track":{"source":"iana","extensions":["st"]},"application/vnd.sar":{"source":"iana"},"application/vnd.sbm.cid":{"source":"iana"},"application/vnd.sbm.mid2":{"source":"iana"},"application/vnd.scribus":{"source":"iana"},"application/vnd.sealed.3df":{"source":"iana"},"application/vnd.sealed.csf":{"source":"iana"},"application/vnd.sealed.doc":{"source":"iana"},"application/vnd.sealed.eml":{"source":"iana"},"application/vnd.sealed.mht":{"source":"iana"},"application/vnd.sealed.net":{"source":"iana"},"application/vnd.sealed.ppt":{"source":"iana"},"application/vnd.sealed.tiff":{"source":"iana"},"application/vnd.sealed.xls":{"source":"iana"},"application/vnd.sealedmedia.softseal.html":{"source":"iana"},"application/vnd.sealedmedia.softseal.pdf":{"source":"iana"},"application/vnd.seemail":{"source":"iana","extensions":["see"]},"application/vnd.seis+json":{"source":"iana","compressible":true},"application/vnd.sema":{"source":"iana","extensions":["sema"]},"application/vnd.semd":{"source":"iana","extensions":["semd"]},"application/vnd.semf":{"source":"iana","extensions":["semf"]},"application/vnd.shade-save-file":{"source":"iana"},"application/vnd.shana.informed.formdata":{"source":"iana","extensions":["ifm"]},"application/vnd.shana.informed.formtemplate":{"source":"iana","extensions":["itp"]},"application/vnd.shana.informed.interchange":{"source":"iana","extensions":["iif"]},"application/vnd.shana.informed.package":{"source":"iana","extensions":["ipk"]},"application/vnd.shootproof+json":{"source":"iana","compressible":true},"application/vnd.shopkick+json":{"source":"iana","compressible":true},"application/vnd.shp":{"source":"iana"},"application/vnd.shx":{"source":"iana"},"application/vnd.sigrok.session":{"source":"iana"},"application/vnd.simtech-mindmapper":{"source":"iana","extensions":["twd","twds"]},"application/vnd.siren+json":{"source":"iana","compressible":true},"application/vnd.smaf":{"source":"iana","extensions":["mmf"]},"application/vnd.smart.notebook":{"source":"iana"},"application/vnd.smart.teacher":{"source":"iana","extensions":["teacher"]},"application/vnd.snesdev-page-table":{"source":"iana"},"application/vnd.software602.filler.form+xml":{"source":"iana","compressible":true,"extensions":["fo"]},"application/vnd.software602.filler.form-xml-zip":{"source":"iana"},"application/vnd.solent.sdkm+xml":{"source":"iana","compressible":true,"extensions":["sdkm","sdkd"]},"application/vnd.spotfire.dxp":{"source":"iana","extensions":["dxp"]},"application/vnd.spotfire.sfs":{"source":"iana","extensions":["sfs"]},"application/vnd.sqlite3":{"source":"iana"},"application/vnd.sss-cod":{"source":"iana"},"application/vnd.sss-dtf":{"source":"iana"},"application/vnd.sss-ntf":{"source":"iana"},"application/vnd.stardivision.calc":{"source":"apache","extensions":["sdc"]},"application/vnd.stardivision.draw":{"source":"apache","extensions":["sda"]},"application/vnd.stardivision.impress":{"source":"apache","extensions":["sdd"]},"application/vnd.stardivision.math":{"source":"apache","extensions":["smf"]},"application/vnd.stardivision.writer":{"source":"apache","extensions":["sdw","vor"]},"application/vnd.stardivision.writer-global":{"source":"apache","extensions":["sgl"]},"application/vnd.stepmania.package":{"source":"iana","extensions":["smzip"]},"application/vnd.stepmania.stepchart":{"source":"iana","extensions":["sm"]},"application/vnd.street-stream":{"source":"iana"},"application/vnd.sun.wadl+xml":{"source":"iana","compressible":true,"extensions":["wadl"]},"application/vnd.sun.xml.calc":{"source":"apache","extensions":["sxc"]},"application/vnd.sun.xml.calc.template":{"source":"apache","extensions":["stc"]},"application/vnd.sun.xml.draw":{"source":"apache","extensions":["sxd"]},"application/vnd.sun.xml.draw.template":{"source":"apache","extensions":["std"]},"application/vnd.sun.xml.impress":{"source":"apache","extensions":["sxi"]},"application/vnd.sun.xml.impress.template":{"source":"apache","extensions":["sti"]},"application/vnd.sun.xml.math":{"source":"apache","extensions":["sxm"]},"application/vnd.sun.xml.writer":{"source":"apache","extensions":["sxw"]},"application/vnd.sun.xml.writer.global":{"source":"apache","extensions":["sxg"]},"application/vnd.sun.xml.writer.template":{"source":"apache","extensions":["stw"]},"application/vnd.sus-calendar":{"source":"iana","extensions":["sus","susp"]},"application/vnd.svd":{"source":"iana","extensions":["svd"]},"application/vnd.swiftview-ics":{"source":"iana"},"application/vnd.sycle+xml":{"source":"iana","compressible":true},"application/vnd.syft+json":{"source":"iana","compressible":true},"application/vnd.symbian.install":{"source":"apache","extensions":["sis","sisx"]},"application/vnd.syncml+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xsm"]},"application/vnd.syncml.dm+wbxml":{"source":"iana","charset":"UTF-8","extensions":["bdm"]},"application/vnd.syncml.dm+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xdm"]},"application/vnd.syncml.dm.notification":{"source":"iana"},"application/vnd.syncml.dmddf+wbxml":{"source":"iana"},"application/vnd.syncml.dmddf+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["ddf"]},"application/vnd.syncml.dmtnds+wbxml":{"source":"iana"},"application/vnd.syncml.dmtnds+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.syncml.ds.notification":{"source":"iana"},"application/vnd.tableschema+json":{"source":"iana","compressible":true},"application/vnd.tao.intent-module-archive":{"source":"iana","extensions":["tao"]},"application/vnd.tcpdump.pcap":{"source":"iana","extensions":["pcap","cap","dmp"]},"application/vnd.think-cell.ppttc+json":{"source":"iana","compressible":true},"application/vnd.tmd.mediaflex.api+xml":{"source":"iana","compressible":true},"application/vnd.tml":{"source":"iana"},"application/vnd.tmobile-livetv":{"source":"iana","extensions":["tmo"]},"application/vnd.tri.onesource":{"source":"iana"},"application/vnd.trid.tpt":{"source":"iana","extensions":["tpt"]},"application/vnd.triscape.mxs":{"source":"iana","extensions":["mxs"]},"application/vnd.trueapp":{"source":"iana","extensions":["tra"]},"application/vnd.truedoc":{"source":"iana"},"application/vnd.ubisoft.webplayer":{"source":"iana"},"application/vnd.ufdl":{"source":"iana","extensions":["ufd","ufdl"]},"application/vnd.uiq.theme":{"source":"iana","extensions":["utz"]},"application/vnd.umajin":{"source":"iana","extensions":["umj"]},"application/vnd.unity":{"source":"iana","extensions":["unityweb"]},"application/vnd.uoml+xml":{"source":"iana","compressible":true,"extensions":["uoml"]},"application/vnd.uplanet.alert":{"source":"iana"},"application/vnd.uplanet.alert-wbxml":{"source":"iana"},"application/vnd.uplanet.bearer-choice":{"source":"iana"},"application/vnd.uplanet.bearer-choice-wbxml":{"source":"iana"},"application/vnd.uplanet.cacheop":{"source":"iana"},"application/vnd.uplanet.cacheop-wbxml":{"source":"iana"},"application/vnd.uplanet.channel":{"source":"iana"},"application/vnd.uplanet.channel-wbxml":{"source":"iana"},"application/vnd.uplanet.list":{"source":"iana"},"application/vnd.uplanet.list-wbxml":{"source":"iana"},"application/vnd.uplanet.listcmd":{"source":"iana"},"application/vnd.uplanet.listcmd-wbxml":{"source":"iana"},"application/vnd.uplanet.signal":{"source":"iana"},"application/vnd.uri-map":{"source":"iana"},"application/vnd.valve.source.material":{"source":"iana"},"application/vnd.vcx":{"source":"iana","extensions":["vcx"]},"application/vnd.vd-study":{"source":"iana"},"application/vnd.vectorworks":{"source":"iana"},"application/vnd.vel+json":{"source":"iana","compressible":true},"application/vnd.verimatrix.vcas":{"source":"iana"},"application/vnd.veritone.aion+json":{"source":"iana","compressible":true},"application/vnd.veryant.thin":{"source":"iana"},"application/vnd.ves.encrypted":{"source":"iana"},"application/vnd.vidsoft.vidconference":{"source":"iana"},"application/vnd.visio":{"source":"iana","extensions":["vsd","vst","vss","vsw"]},"application/vnd.visionary":{"source":"iana","extensions":["vis"]},"application/vnd.vividence.scriptfile":{"source":"iana"},"application/vnd.vsf":{"source":"iana","extensions":["vsf"]},"application/vnd.wap.sic":{"source":"iana"},"application/vnd.wap.slc":{"source":"iana"},"application/vnd.wap.wbxml":{"source":"iana","charset":"UTF-8","extensions":["wbxml"]},"application/vnd.wap.wmlc":{"source":"iana","extensions":["wmlc"]},"application/vnd.wap.wmlscriptc":{"source":"iana","extensions":["wmlsc"]},"application/vnd.webturbo":{"source":"iana","extensions":["wtb"]},"application/vnd.wfa.dpp":{"source":"iana"},"application/vnd.wfa.p2p":{"source":"iana"},"application/vnd.wfa.wsc":{"source":"iana"},"application/vnd.windows.devicepairing":{"source":"iana"},"application/vnd.wmc":{"source":"iana"},"application/vnd.wmf.bootstrap":{"source":"iana"},"application/vnd.wolfram.mathematica":{"source":"iana"},"application/vnd.wolfram.mathematica.package":{"source":"iana"},"application/vnd.wolfram.player":{"source":"iana","extensions":["nbp"]},"application/vnd.wordperfect":{"source":"iana","extensions":["wpd"]},"application/vnd.wqd":{"source":"iana","extensions":["wqd"]},"application/vnd.wrq-hp3000-labelled":{"source":"iana"},"application/vnd.wt.stf":{"source":"iana","extensions":["stf"]},"application/vnd.wv.csp+wbxml":{"source":"iana"},"application/vnd.wv.csp+xml":{"source":"iana","compressible":true},"application/vnd.wv.ssp+xml":{"source":"iana","compressible":true},"application/vnd.xacml+json":{"source":"iana","compressible":true},"application/vnd.xara":{"source":"iana","extensions":["xar"]},"application/vnd.xfdl":{"source":"iana","extensions":["xfdl"]},"application/vnd.xfdl.webform":{"source":"iana"},"application/vnd.xmi+xml":{"source":"iana","compressible":true},"application/vnd.xmpie.cpkg":{"source":"iana"},"application/vnd.xmpie.dpkg":{"source":"iana"},"application/vnd.xmpie.plan":{"source":"iana"},"application/vnd.xmpie.ppkg":{"source":"iana"},"application/vnd.xmpie.xlim":{"source":"iana"},"application/vnd.yamaha.hv-dic":{"source":"iana","extensions":["hvd"]},"application/vnd.yamaha.hv-script":{"source":"iana","extensions":["hvs"]},"application/vnd.yamaha.hv-voice":{"source":"iana","extensions":["hvp"]},"application/vnd.yamaha.openscoreformat":{"source":"iana","extensions":["osf"]},"application/vnd.yamaha.openscoreformat.osfpvg+xml":{"source":"iana","compressible":true,"extensions":["osfpvg"]},"application/vnd.yamaha.remote-setup":{"source":"iana"},"application/vnd.yamaha.smaf-audio":{"source":"iana","extensions":["saf"]},"application/vnd.yamaha.smaf-phrase":{"source":"iana","extensions":["spf"]},"application/vnd.yamaha.through-ngn":{"source":"iana"},"application/vnd.yamaha.tunnel-udpencap":{"source":"iana"},"application/vnd.yaoweme":{"source":"iana"},"application/vnd.yellowriver-custom-menu":{"source":"iana","extensions":["cmp"]},"application/vnd.youtube.yt":{"source":"iana"},"application/vnd.zul":{"source":"iana","extensions":["zir","zirz"]},"application/vnd.zzazz.deck+xml":{"source":"iana","compressible":true,"extensions":["zaz"]},"application/voicexml+xml":{"source":"iana","compressible":true,"extensions":["vxml"]},"application/voucher-cms+json":{"source":"iana","compressible":true},"application/vq-rtcpxr":{"source":"iana"},"application/wasm":{"source":"iana","compressible":true,"extensions":["wasm"]},"application/watcherinfo+xml":{"source":"iana","compressible":true,"extensions":["wif"]},"application/webpush-options+json":{"source":"iana","compressible":true},"application/whoispp-query":{"source":"iana"},"application/whoispp-response":{"source":"iana"},"application/widget":{"source":"iana","extensions":["wgt"]},"application/winhlp":{"source":"apache","extensions":["hlp"]},"application/wita":{"source":"iana"},"application/wordperfect5.1":{"source":"iana"},"application/wsdl+xml":{"source":"iana","compressible":true,"extensions":["wsdl"]},"application/wspolicy+xml":{"source":"iana","compressible":true,"extensions":["wspolicy"]},"application/x-7z-compressed":{"source":"apache","compressible":false,"extensions":["7z"]},"application/x-abiword":{"source":"apache","extensions":["abw"]},"application/x-ace-compressed":{"source":"apache","extensions":["ace"]},"application/x-amf":{"source":"apache"},"application/x-apple-diskimage":{"source":"apache","extensions":["dmg"]},"application/x-arj":{"compressible":false,"extensions":["arj"]},"application/x-authorware-bin":{"source":"apache","extensions":["aab","x32","u32","vox"]},"application/x-authorware-map":{"source":"apache","extensions":["aam"]},"application/x-authorware-seg":{"source":"apache","extensions":["aas"]},"application/x-bcpio":{"source":"apache","extensions":["bcpio"]},"application/x-bdoc":{"compressible":false,"extensions":["bdoc"]},"application/x-bittorrent":{"source":"apache","extensions":["torrent"]},"application/x-blorb":{"source":"apache","extensions":["blb","blorb"]},"application/x-bzip":{"source":"apache","compressible":false,"extensions":["bz"]},"application/x-bzip2":{"source":"apache","compressible":false,"extensions":["bz2","boz"]},"application/x-cbr":{"source":"apache","extensions":["cbr","cba","cbt","cbz","cb7"]},"application/x-cdlink":{"source":"apache","extensions":["vcd"]},"application/x-cfs-compressed":{"source":"apache","extensions":["cfs"]},"application/x-chat":{"source":"apache","extensions":["chat"]},"application/x-chess-pgn":{"source":"apache","extensions":["pgn"]},"application/x-chrome-extension":{"extensions":["crx"]},"application/x-cocoa":{"source":"nginx","extensions":["cco"]},"application/x-compress":{"source":"apache"},"application/x-conference":{"source":"apache","extensions":["nsc"]},"application/x-cpio":{"source":"apache","extensions":["cpio"]},"application/x-csh":{"source":"apache","extensions":["csh"]},"application/x-deb":{"compressible":false},"application/x-debian-package":{"source":"apache","extensions":["deb","udeb"]},"application/x-dgc-compressed":{"source":"apache","extensions":["dgc"]},"application/x-director":{"source":"apache","extensions":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"]},"application/x-doom":{"source":"apache","extensions":["wad"]},"application/x-dtbncx+xml":{"source":"apache","compressible":true,"extensions":["ncx"]},"application/x-dtbook+xml":{"source":"apache","compressible":true,"extensions":["dtb"]},"application/x-dtbresource+xml":{"source":"apache","compressible":true,"extensions":["res"]},"application/x-dvi":{"source":"apache","compressible":false,"extensions":["dvi"]},"application/x-envoy":{"source":"apache","extensions":["evy"]},"application/x-eva":{"source":"apache","extensions":["eva"]},"application/x-font-bdf":{"source":"apache","extensions":["bdf"]},"application/x-font-dos":{"source":"apache"},"application/x-font-framemaker":{"source":"apache"},"application/x-font-ghostscript":{"source":"apache","extensions":["gsf"]},"application/x-font-libgrx":{"source":"apache"},"application/x-font-linux-psf":{"source":"apache","extensions":["psf"]},"application/x-font-pcf":{"source":"apache","extensions":["pcf"]},"application/x-font-snf":{"source":"apache","extensions":["snf"]},"application/x-font-speedo":{"source":"apache"},"application/x-font-sunos-news":{"source":"apache"},"application/x-font-type1":{"source":"apache","extensions":["pfa","pfb","pfm","afm"]},"application/x-font-vfont":{"source":"apache"},"application/x-freearc":{"source":"apache","extensions":["arc"]},"application/x-futuresplash":{"source":"apache","extensions":["spl"]},"application/x-gca-compressed":{"source":"apache","extensions":["gca"]},"application/x-glulx":{"source":"apache","extensions":["ulx"]},"application/x-gnumeric":{"source":"apache","extensions":["gnumeric"]},"application/x-gramps-xml":{"source":"apache","extensions":["gramps"]},"application/x-gtar":{"source":"apache","extensions":["gtar"]},"application/x-gzip":{"source":"apache"},"application/x-hdf":{"source":"apache","extensions":["hdf"]},"application/x-httpd-php":{"compressible":true,"extensions":["php"]},"application/x-install-instructions":{"source":"apache","extensions":["install"]},"application/x-iso9660-image":{"source":"apache","extensions":["iso"]},"application/x-iwork-keynote-sffkey":{"extensions":["key"]},"application/x-iwork-numbers-sffnumbers":{"extensions":["numbers"]},"application/x-iwork-pages-sffpages":{"extensions":["pages"]},"application/x-java-archive-diff":{"source":"nginx","extensions":["jardiff"]},"application/x-java-jnlp-file":{"source":"apache","compressible":false,"extensions":["jnlp"]},"application/x-javascript":{"compressible":true},"application/x-keepass2":{"extensions":["kdbx"]},"application/x-latex":{"source":"apache","compressible":false,"extensions":["latex"]},"application/x-lua-bytecode":{"extensions":["luac"]},"application/x-lzh-compressed":{"source":"apache","extensions":["lzh","lha"]},"application/x-makeself":{"source":"nginx","extensions":["run"]},"application/x-mie":{"source":"apache","extensions":["mie"]},"application/x-mobipocket-ebook":{"source":"apache","extensions":["prc","mobi"]},"application/x-mpegurl":{"compressible":false},"application/x-ms-application":{"source":"apache","extensions":["application"]},"application/x-ms-shortcut":{"source":"apache","extensions":["lnk"]},"application/x-ms-wmd":{"source":"apache","extensions":["wmd"]},"application/x-ms-wmz":{"source":"apache","extensions":["wmz"]},"application/x-ms-xbap":{"source":"apache","extensions":["xbap"]},"application/x-msaccess":{"source":"apache","extensions":["mdb"]},"application/x-msbinder":{"source":"apache","extensions":["obd"]},"application/x-mscardfile":{"source":"apache","extensions":["crd"]},"application/x-msclip":{"source":"apache","extensions":["clp"]},"application/x-msdos-program":{"extensions":["exe"]},"application/x-msdownload":{"source":"apache","extensions":["exe","dll","com","bat","msi"]},"application/x-msmediaview":{"source":"apache","extensions":["mvb","m13","m14"]},"application/x-msmetafile":{"source":"apache","extensions":["wmf","wmz","emf","emz"]},"application/x-msmoney":{"source":"apache","extensions":["mny"]},"application/x-mspublisher":{"source":"apache","extensions":["pub"]},"application/x-msschedule":{"source":"apache","extensions":["scd"]},"application/x-msterminal":{"source":"apache","extensions":["trm"]},"application/x-mswrite":{"source":"apache","extensions":["wri"]},"application/x-netcdf":{"source":"apache","extensions":["nc","cdf"]},"application/x-ns-proxy-autoconfig":{"compressible":true,"extensions":["pac"]},"application/x-nzb":{"source":"apache","extensions":["nzb"]},"application/x-perl":{"source":"nginx","extensions":["pl","pm"]},"application/x-pilot":{"source":"nginx","extensions":["prc","pdb"]},"application/x-pkcs12":{"source":"apache","compressible":false,"extensions":["p12","pfx"]},"application/x-pkcs7-certificates":{"source":"apache","extensions":["p7b","spc"]},"application/x-pkcs7-certreqresp":{"source":"apache","extensions":["p7r"]},"application/x-pki-message":{"source":"iana"},"application/x-rar-compressed":{"source":"apache","compressible":false,"extensions":["rar"]},"application/x-redhat-package-manager":{"source":"nginx","extensions":["rpm"]},"application/x-research-info-systems":{"source":"apache","extensions":["ris"]},"application/x-sea":{"source":"nginx","extensions":["sea"]},"application/x-sh":{"source":"apache","compressible":true,"extensions":["sh"]},"application/x-shar":{"source":"apache","extensions":["shar"]},"application/x-shockwave-flash":{"source":"apache","compressible":false,"extensions":["swf"]},"application/x-silverlight-app":{"source":"apache","extensions":["xap"]},"application/x-sql":{"source":"apache","extensions":["sql"]},"application/x-stuffit":{"source":"apache","compressible":false,"extensions":["sit"]},"application/x-stuffitx":{"source":"apache","extensions":["sitx"]},"application/x-subrip":{"source":"apache","extensions":["srt"]},"application/x-sv4cpio":{"source":"apache","extensions":["sv4cpio"]},"application/x-sv4crc":{"source":"apache","extensions":["sv4crc"]},"application/x-t3vm-image":{"source":"apache","extensions":["t3"]},"application/x-tads":{"source":"apache","extensions":["gam"]},"application/x-tar":{"source":"apache","compressible":true,"extensions":["tar"]},"application/x-tcl":{"source":"apache","extensions":["tcl","tk"]},"application/x-tex":{"source":"apache","extensions":["tex"]},"application/x-tex-tfm":{"source":"apache","extensions":["tfm"]},"application/x-texinfo":{"source":"apache","extensions":["texinfo","texi"]},"application/x-tgif":{"source":"apache","extensions":["obj"]},"application/x-ustar":{"source":"apache","extensions":["ustar"]},"application/x-virtualbox-hdd":{"compressible":true,"extensions":["hdd"]},"application/x-virtualbox-ova":{"compressible":true,"extensions":["ova"]},"application/x-virtualbox-ovf":{"compressible":true,"extensions":["ovf"]},"application/x-virtualbox-vbox":{"compressible":true,"extensions":["vbox"]},"application/x-virtualbox-vbox-extpack":{"compressible":false,"extensions":["vbox-extpack"]},"application/x-virtualbox-vdi":{"compressible":true,"extensions":["vdi"]},"application/x-virtualbox-vhd":{"compressible":true,"extensions":["vhd"]},"application/x-virtualbox-vmdk":{"compressible":true,"extensions":["vmdk"]},"application/x-wais-source":{"source":"apache","extensions":["src"]},"application/x-web-app-manifest+json":{"compressible":true,"extensions":["webapp"]},"application/x-www-form-urlencoded":{"source":"iana","compressible":true},"application/x-x509-ca-cert":{"source":"iana","extensions":["der","crt","pem"]},"application/x-x509-ca-ra-cert":{"source":"iana"},"application/x-x509-next-ca-cert":{"source":"iana"},"application/x-xfig":{"source":"apache","extensions":["fig"]},"application/x-xliff+xml":{"source":"apache","compressible":true,"extensions":["xlf"]},"application/x-xpinstall":{"source":"apache","compressible":false,"extensions":["xpi"]},"application/x-xz":{"source":"apache","extensions":["xz"]},"application/x-zmachine":{"source":"apache","extensions":["z1","z2","z3","z4","z5","z6","z7","z8"]},"application/x400-bp":{"source":"iana"},"application/xacml+xml":{"source":"iana","compressible":true},"application/xaml+xml":{"source":"apache","compressible":true,"extensions":["xaml"]},"application/xcap-att+xml":{"source":"iana","compressible":true,"extensions":["xav"]},"application/xcap-caps+xml":{"source":"iana","compressible":true,"extensions":["xca"]},"application/xcap-diff+xml":{"source":"iana","compressible":true,"extensions":["xdf"]},"application/xcap-el+xml":{"source":"iana","compressible":true,"extensions":["xel"]},"application/xcap-error+xml":{"source":"iana","compressible":true},"application/xcap-ns+xml":{"source":"iana","compressible":true,"extensions":["xns"]},"application/xcon-conference-info+xml":{"source":"iana","compressible":true},"application/xcon-conference-info-diff+xml":{"source":"iana","compressible":true},"application/xenc+xml":{"source":"iana","compressible":true,"extensions":["xenc"]},"application/xhtml+xml":{"source":"iana","compressible":true,"extensions":["xhtml","xht"]},"application/xhtml-voice+xml":{"source":"apache","compressible":true},"application/xliff+xml":{"source":"iana","compressible":true,"extensions":["xlf"]},"application/xml":{"source":"iana","compressible":true,"extensions":["xml","xsl","xsd","rng"]},"application/xml-dtd":{"source":"iana","compressible":true,"extensions":["dtd"]},"application/xml-external-parsed-entity":{"source":"iana"},"application/xml-patch+xml":{"source":"iana","compressible":true},"application/xmpp+xml":{"source":"iana","compressible":true},"application/xop+xml":{"source":"iana","compressible":true,"extensions":["xop"]},"application/xproc+xml":{"source":"apache","compressible":true,"extensions":["xpl"]},"application/xslt+xml":{"source":"iana","compressible":true,"extensions":["xsl","xslt"]},"application/xspf+xml":{"source":"apache","compressible":true,"extensions":["xspf"]},"application/xv+xml":{"source":"iana","compressible":true,"extensions":["mxml","xhvml","xvml","xvm"]},"application/yang":{"source":"iana","extensions":["yang"]},"application/yang-data+json":{"source":"iana","compressible":true},"application/yang-data+xml":{"source":"iana","compressible":true},"application/yang-patch+json":{"source":"iana","compressible":true},"application/yang-patch+xml":{"source":"iana","compressible":true},"application/yin+xml":{"source":"iana","compressible":true,"extensions":["yin"]},"application/zip":{"source":"iana","compressible":false,"extensions":["zip"]},"application/zlib":{"source":"iana"},"application/zstd":{"source":"iana"},"audio/1d-interleaved-parityfec":{"source":"iana"},"audio/32kadpcm":{"source":"iana"},"audio/3gpp":{"source":"iana","compressible":false,"extensions":["3gpp"]},"audio/3gpp2":{"source":"iana"},"audio/aac":{"source":"iana"},"audio/ac3":{"source":"iana"},"audio/adpcm":{"source":"apache","extensions":["adp"]},"audio/amr":{"source":"iana","extensions":["amr"]},"audio/amr-wb":{"source":"iana"},"audio/amr-wb+":{"source":"iana"},"audio/aptx":{"source":"iana"},"audio/asc":{"source":"iana"},"audio/atrac-advanced-lossless":{"source":"iana"},"audio/atrac-x":{"source":"iana"},"audio/atrac3":{"source":"iana"},"audio/basic":{"source":"iana","compressible":false,"extensions":["au","snd"]},"audio/bv16":{"source":"iana"},"audio/bv32":{"source":"iana"},"audio/clearmode":{"source":"iana"},"audio/cn":{"source":"iana"},"audio/dat12":{"source":"iana"},"audio/dls":{"source":"iana"},"audio/dsr-es201108":{"source":"iana"},"audio/dsr-es202050":{"source":"iana"},"audio/dsr-es202211":{"source":"iana"},"audio/dsr-es202212":{"source":"iana"},"audio/dv":{"source":"iana"},"audio/dvi4":{"source":"iana"},"audio/eac3":{"source":"iana"},"audio/encaprtp":{"source":"iana"},"audio/evrc":{"source":"iana"},"audio/evrc-qcp":{"source":"iana"},"audio/evrc0":{"source":"iana"},"audio/evrc1":{"source":"iana"},"audio/evrcb":{"source":"iana"},"audio/evrcb0":{"source":"iana"},"audio/evrcb1":{"source":"iana"},"audio/evrcnw":{"source":"iana"},"audio/evrcnw0":{"source":"iana"},"audio/evrcnw1":{"source":"iana"},"audio/evrcwb":{"source":"iana"},"audio/evrcwb0":{"source":"iana"},"audio/evrcwb1":{"source":"iana"},"audio/evs":{"source":"iana"},"audio/flexfec":{"source":"iana"},"audio/fwdred":{"source":"iana"},"audio/g711-0":{"source":"iana"},"audio/g719":{"source":"iana"},"audio/g722":{"source":"iana"},"audio/g7221":{"source":"iana"},"audio/g723":{"source":"iana"},"audio/g726-16":{"source":"iana"},"audio/g726-24":{"source":"iana"},"audio/g726-32":{"source":"iana"},"audio/g726-40":{"source":"iana"},"audio/g728":{"source":"iana"},"audio/g729":{"source":"iana"},"audio/g7291":{"source":"iana"},"audio/g729d":{"source":"iana"},"audio/g729e":{"source":"iana"},"audio/gsm":{"source":"iana"},"audio/gsm-efr":{"source":"iana"},"audio/gsm-hr-08":{"source":"iana"},"audio/ilbc":{"source":"iana"},"audio/ip-mr_v2.5":{"source":"iana"},"audio/isac":{"source":"apache"},"audio/l16":{"source":"iana"},"audio/l20":{"source":"iana"},"audio/l24":{"source":"iana","compressible":false},"audio/l8":{"source":"iana"},"audio/lpc":{"source":"iana"},"audio/melp":{"source":"iana"},"audio/melp1200":{"source":"iana"},"audio/melp2400":{"source":"iana"},"audio/melp600":{"source":"iana"},"audio/mhas":{"source":"iana"},"audio/midi":{"source":"apache","extensions":["mid","midi","kar","rmi"]},"audio/mobile-xmf":{"source":"iana","extensions":["mxmf"]},"audio/mp3":{"compressible":false,"extensions":["mp3"]},"audio/mp4":{"source":"iana","compressible":false,"extensions":["m4a","mp4a"]},"audio/mp4a-latm":{"source":"iana"},"audio/mpa":{"source":"iana"},"audio/mpa-robust":{"source":"iana"},"audio/mpeg":{"source":"iana","compressible":false,"extensions":["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/mpeg4-generic":{"source":"iana"},"audio/musepack":{"source":"apache"},"audio/ogg":{"source":"iana","compressible":false,"extensions":["oga","ogg","spx","opus"]},"audio/opus":{"source":"iana"},"audio/parityfec":{"source":"iana"},"audio/pcma":{"source":"iana"},"audio/pcma-wb":{"source":"iana"},"audio/pcmu":{"source":"iana"},"audio/pcmu-wb":{"source":"iana"},"audio/prs.sid":{"source":"iana"},"audio/qcelp":{"source":"iana"},"audio/raptorfec":{"source":"iana"},"audio/red":{"source":"iana"},"audio/rtp-enc-aescm128":{"source":"iana"},"audio/rtp-midi":{"source":"iana"},"audio/rtploopback":{"source":"iana"},"audio/rtx":{"source":"iana"},"audio/s3m":{"source":"apache","extensions":["s3m"]},"audio/scip":{"source":"iana"},"audio/silk":{"source":"apache","extensions":["sil"]},"audio/smv":{"source":"iana"},"audio/smv-qcp":{"source":"iana"},"audio/smv0":{"source":"iana"},"audio/sofa":{"source":"iana"},"audio/sp-midi":{"source":"iana"},"audio/speex":{"source":"iana"},"audio/t140c":{"source":"iana"},"audio/t38":{"source":"iana"},"audio/telephone-event":{"source":"iana"},"audio/tetra_acelp":{"source":"iana"},"audio/tetra_acelp_bb":{"source":"iana"},"audio/tone":{"source":"iana"},"audio/tsvcis":{"source":"iana"},"audio/uemclip":{"source":"iana"},"audio/ulpfec":{"source":"iana"},"audio/usac":{"source":"iana"},"audio/vdvi":{"source":"iana"},"audio/vmr-wb":{"source":"iana"},"audio/vnd.3gpp.iufp":{"source":"iana"},"audio/vnd.4sb":{"source":"iana"},"audio/vnd.audiokoz":{"source":"iana"},"audio/vnd.celp":{"source":"iana"},"audio/vnd.cisco.nse":{"source":"iana"},"audio/vnd.cmles.radio-events":{"source":"iana"},"audio/vnd.cns.anp1":{"source":"iana"},"audio/vnd.cns.inf1":{"source":"iana"},"audio/vnd.dece.audio":{"source":"iana","extensions":["uva","uvva"]},"audio/vnd.digital-winds":{"source":"iana","extensions":["eol"]},"audio/vnd.dlna.adts":{"source":"iana"},"audio/vnd.dolby.heaac.1":{"source":"iana"},"audio/vnd.dolby.heaac.2":{"source":"iana"},"audio/vnd.dolby.mlp":{"source":"iana"},"audio/vnd.dolby.mps":{"source":"iana"},"audio/vnd.dolby.pl2":{"source":"iana"},"audio/vnd.dolby.pl2x":{"source":"iana"},"audio/vnd.dolby.pl2z":{"source":"iana"},"audio/vnd.dolby.pulse.1":{"source":"iana"},"audio/vnd.dra":{"source":"iana","extensions":["dra"]},"audio/vnd.dts":{"source":"iana","extensions":["dts"]},"audio/vnd.dts.hd":{"source":"iana","extensions":["dtshd"]},"audio/vnd.dts.uhd":{"source":"iana"},"audio/vnd.dvb.file":{"source":"iana"},"audio/vnd.everad.plj":{"source":"iana"},"audio/vnd.hns.audio":{"source":"iana"},"audio/vnd.lucent.voice":{"source":"iana","extensions":["lvp"]},"audio/vnd.ms-playready.media.pya":{"source":"iana","extensions":["pya"]},"audio/vnd.nokia.mobile-xmf":{"source":"iana"},"audio/vnd.nortel.vbk":{"source":"iana"},"audio/vnd.nuera.ecelp4800":{"source":"iana","extensions":["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{"source":"iana","extensions":["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{"source":"iana","extensions":["ecelp9600"]},"audio/vnd.octel.sbc":{"source":"iana"},"audio/vnd.presonus.multitrack":{"source":"iana"},"audio/vnd.qcelp":{"source":"iana"},"audio/vnd.rhetorex.32kadpcm":{"source":"iana"},"audio/vnd.rip":{"source":"iana","extensions":["rip"]},"audio/vnd.rn-realaudio":{"compressible":false},"audio/vnd.sealedmedia.softseal.mpeg":{"source":"iana"},"audio/vnd.vmx.cvsd":{"source":"iana"},"audio/vnd.wave":{"compressible":false},"audio/vorbis":{"source":"iana","compressible":false},"audio/vorbis-config":{"source":"iana"},"audio/wav":{"compressible":false,"extensions":["wav"]},"audio/wave":{"compressible":false,"extensions":["wav"]},"audio/webm":{"source":"apache","compressible":false,"extensions":["weba"]},"audio/x-aac":{"source":"apache","compressible":false,"extensions":["aac"]},"audio/x-aiff":{"source":"apache","extensions":["aif","aiff","aifc"]},"audio/x-caf":{"source":"apache","compressible":false,"extensions":["caf"]},"audio/x-flac":{"source":"apache","extensions":["flac"]},"audio/x-m4a":{"source":"nginx","extensions":["m4a"]},"audio/x-matroska":{"source":"apache","extensions":["mka"]},"audio/x-mpegurl":{"source":"apache","extensions":["m3u"]},"audio/x-ms-wax":{"source":"apache","extensions":["wax"]},"audio/x-ms-wma":{"source":"apache","extensions":["wma"]},"audio/x-pn-realaudio":{"source":"apache","extensions":["ram","ra"]},"audio/x-pn-realaudio-plugin":{"source":"apache","extensions":["rmp"]},"audio/x-realaudio":{"source":"nginx","extensions":["ra"]},"audio/x-tta":{"source":"apache"},"audio/x-wav":{"source":"apache","extensions":["wav"]},"audio/xm":{"source":"apache","extensions":["xm"]},"chemical/x-cdx":{"source":"apache","extensions":["cdx"]},"chemical/x-cif":{"source":"apache","extensions":["cif"]},"chemical/x-cmdf":{"source":"apache","extensions":["cmdf"]},"chemical/x-cml":{"source":"apache","extensions":["cml"]},"chemical/x-csml":{"source":"apache","extensions":["csml"]},"chemical/x-pdb":{"source":"apache"},"chemical/x-xyz":{"source":"apache","extensions":["xyz"]},"font/collection":{"source":"iana","extensions":["ttc"]},"font/otf":{"source":"iana","compressible":true,"extensions":["otf"]},"font/sfnt":{"source":"iana"},"font/ttf":{"source":"iana","compressible":true,"extensions":["ttf"]},"font/woff":{"source":"iana","extensions":["woff"]},"font/woff2":{"source":"iana","extensions":["woff2"]},"image/aces":{"source":"iana","extensions":["exr"]},"image/apng":{"compressible":false,"extensions":["apng"]},"image/avci":{"source":"iana","extensions":["avci"]},"image/avcs":{"source":"iana","extensions":["avcs"]},"image/avif":{"source":"iana","compressible":false,"extensions":["avif"]},"image/bmp":{"source":"iana","compressible":true,"extensions":["bmp"]},"image/cgm":{"source":"iana","extensions":["cgm"]},"image/dicom-rle":{"source":"iana","extensions":["drle"]},"image/emf":{"source":"iana","extensions":["emf"]},"image/fits":{"source":"iana","extensions":["fits"]},"image/g3fax":{"source":"iana","extensions":["g3"]},"image/gif":{"source":"iana","compressible":false,"extensions":["gif"]},"image/heic":{"source":"iana","extensions":["heic"]},"image/heic-sequence":{"source":"iana","extensions":["heics"]},"image/heif":{"source":"iana","extensions":["heif"]},"image/heif-sequence":{"source":"iana","extensions":["heifs"]},"image/hej2k":{"source":"iana","extensions":["hej2"]},"image/hsj2":{"source":"iana","extensions":["hsj2"]},"image/ief":{"source":"iana","extensions":["ief"]},"image/jls":{"source":"iana","extensions":["jls"]},"image/jp2":{"source":"iana","compressible":false,"extensions":["jp2","jpg2"]},"image/jpeg":{"source":"iana","compressible":false,"extensions":["jpeg","jpg","jpe"]},"image/jph":{"source":"iana","extensions":["jph"]},"image/jphc":{"source":"iana","extensions":["jhc"]},"image/jpm":{"source":"iana","compressible":false,"extensions":["jpm"]},"image/jpx":{"source":"iana","compressible":false,"extensions":["jpx","jpf"]},"image/jxr":{"source":"iana","extensions":["jxr"]},"image/jxra":{"source":"iana","extensions":["jxra"]},"image/jxrs":{"source":"iana","extensions":["jxrs"]},"image/jxs":{"source":"iana","extensions":["jxs"]},"image/jxsc":{"source":"iana","extensions":["jxsc"]},"image/jxsi":{"source":"iana","extensions":["jxsi"]},"image/jxss":{"source":"iana","extensions":["jxss"]},"image/ktx":{"source":"iana","extensions":["ktx"]},"image/ktx2":{"source":"iana","extensions":["ktx2"]},"image/naplps":{"source":"iana"},"image/pjpeg":{"compressible":false},"image/png":{"source":"iana","compressible":false,"extensions":["png"]},"image/prs.btif":{"source":"iana","extensions":["btif"]},"image/prs.pti":{"source":"iana","extensions":["pti"]},"image/pwg-raster":{"source":"iana"},"image/sgi":{"source":"apache","extensions":["sgi"]},"image/svg+xml":{"source":"iana","compressible":true,"extensions":["svg","svgz"]},"image/t38":{"source":"iana","extensions":["t38"]},"image/tiff":{"source":"iana","compressible":false,"extensions":["tif","tiff"]},"image/tiff-fx":{"source":"iana","extensions":["tfx"]},"image/vnd.adobe.photoshop":{"source":"iana","compressible":true,"extensions":["psd"]},"image/vnd.airzip.accelerator.azv":{"source":"iana","extensions":["azv"]},"image/vnd.cns.inf2":{"source":"iana"},"image/vnd.dece.graphic":{"source":"iana","extensions":["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{"source":"iana","extensions":["djvu","djv"]},"image/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"image/vnd.dwg":{"source":"iana","extensions":["dwg"]},"image/vnd.dxf":{"source":"iana","extensions":["dxf"]},"image/vnd.fastbidsheet":{"source":"iana","extensions":["fbs"]},"image/vnd.fpx":{"source":"iana","extensions":["fpx"]},"image/vnd.fst":{"source":"iana","extensions":["fst"]},"image/vnd.fujixerox.edmics-mmr":{"source":"iana","extensions":["mmr"]},"image/vnd.fujixerox.edmics-rlc":{"source":"iana","extensions":["rlc"]},"image/vnd.globalgraphics.pgb":{"source":"iana"},"image/vnd.microsoft.icon":{"source":"iana","compressible":true,"extensions":["ico"]},"image/vnd.mix":{"source":"iana"},"image/vnd.mozilla.apng":{"source":"iana"},"image/vnd.ms-dds":{"compressible":true,"extensions":["dds"]},"image/vnd.ms-modi":{"source":"iana","extensions":["mdi"]},"image/vnd.ms-photo":{"source":"apache","extensions":["wdp"]},"image/vnd.net-fpx":{"source":"iana","extensions":["npx"]},"image/vnd.pco.b16":{"source":"iana","extensions":["b16"]},"image/vnd.radiance":{"source":"iana"},"image/vnd.sealed.png":{"source":"iana"},"image/vnd.sealedmedia.softseal.gif":{"source":"iana"},"image/vnd.sealedmedia.softseal.jpg":{"source":"iana"},"image/vnd.svf":{"source":"iana"},"image/vnd.tencent.tap":{"source":"iana","extensions":["tap"]},"image/vnd.valve.source.texture":{"source":"iana","extensions":["vtf"]},"image/vnd.wap.wbmp":{"source":"iana","extensions":["wbmp"]},"image/vnd.xiff":{"source":"iana","extensions":["xif"]},"image/vnd.zbrush.pcx":{"source":"iana","extensions":["pcx"]},"image/webp":{"source":"apache","extensions":["webp"]},"image/wmf":{"source":"iana","extensions":["wmf"]},"image/x-3ds":{"source":"apache","extensions":["3ds"]},"image/x-cmu-raster":{"source":"apache","extensions":["ras"]},"image/x-cmx":{"source":"apache","extensions":["cmx"]},"image/x-freehand":{"source":"apache","extensions":["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{"source":"apache","compressible":true,"extensions":["ico"]},"image/x-jng":{"source":"nginx","extensions":["jng"]},"image/x-mrsid-image":{"source":"apache","extensions":["sid"]},"image/x-ms-bmp":{"source":"nginx","compressible":true,"extensions":["bmp"]},"image/x-pcx":{"source":"apache","extensions":["pcx"]},"image/x-pict":{"source":"apache","extensions":["pic","pct"]},"image/x-portable-anymap":{"source":"apache","extensions":["pnm"]},"image/x-portable-bitmap":{"source":"apache","extensions":["pbm"]},"image/x-portable-graymap":{"source":"apache","extensions":["pgm"]},"image/x-portable-pixmap":{"source":"apache","extensions":["ppm"]},"image/x-rgb":{"source":"apache","extensions":["rgb"]},"image/x-tga":{"source":"apache","extensions":["tga"]},"image/x-xbitmap":{"source":"apache","extensions":["xbm"]},"image/x-xcf":{"compressible":false},"image/x-xpixmap":{"source":"apache","extensions":["xpm"]},"image/x-xwindowdump":{"source":"apache","extensions":["xwd"]},"message/cpim":{"source":"iana"},"message/delivery-status":{"source":"iana"},"message/disposition-notification":{"source":"iana","extensions":["disposition-notification"]},"message/external-body":{"source":"iana"},"message/feedback-report":{"source":"iana"},"message/global":{"source":"iana","extensions":["u8msg"]},"message/global-delivery-status":{"source":"iana","extensions":["u8dsn"]},"message/global-disposition-notification":{"source":"iana","extensions":["u8mdn"]},"message/global-headers":{"source":"iana","extensions":["u8hdr"]},"message/http":{"source":"iana","compressible":false},"message/imdn+xml":{"source":"iana","compressible":true},"message/news":{"source":"iana"},"message/partial":{"source":"iana","compressible":false},"message/rfc822":{"source":"iana","compressible":true,"extensions":["eml","mime"]},"message/s-http":{"source":"iana"},"message/sip":{"source":"iana"},"message/sipfrag":{"source":"iana"},"message/tracking-status":{"source":"iana"},"message/vnd.si.simp":{"source":"iana"},"message/vnd.wfa.wsc":{"source":"iana","extensions":["wsc"]},"model/3mf":{"source":"iana","extensions":["3mf"]},"model/e57":{"source":"iana"},"model/gltf+json":{"source":"iana","compressible":true,"extensions":["gltf"]},"model/gltf-binary":{"source":"iana","compressible":true,"extensions":["glb"]},"model/iges":{"source":"iana","compressible":false,"extensions":["igs","iges"]},"model/mesh":{"source":"iana","compressible":false,"extensions":["msh","mesh","silo"]},"model/mtl":{"source":"iana","extensions":["mtl"]},"model/obj":{"source":"iana","extensions":["obj"]},"model/step":{"source":"iana"},"model/step+xml":{"source":"iana","compressible":true,"extensions":["stpx"]},"model/step+zip":{"source":"iana","compressible":false,"extensions":["stpz"]},"model/step-xml+zip":{"source":"iana","compressible":false,"extensions":["stpxz"]},"model/stl":{"source":"iana","extensions":["stl"]},"model/vnd.collada+xml":{"source":"iana","compressible":true,"extensions":["dae"]},"model/vnd.dwf":{"source":"iana","extensions":["dwf"]},"model/vnd.flatland.3dml":{"source":"iana"},"model/vnd.gdl":{"source":"iana","extensions":["gdl"]},"model/vnd.gs-gdl":{"source":"apache"},"model/vnd.gs.gdl":{"source":"iana"},"model/vnd.gtw":{"source":"iana","extensions":["gtw"]},"model/vnd.moml+xml":{"source":"iana","compressible":true},"model/vnd.mts":{"source":"iana","extensions":["mts"]},"model/vnd.opengex":{"source":"iana","extensions":["ogex"]},"model/vnd.parasolid.transmit.binary":{"source":"iana","extensions":["x_b"]},"model/vnd.parasolid.transmit.text":{"source":"iana","extensions":["x_t"]},"model/vnd.pytha.pyox":{"source":"iana"},"model/vnd.rosette.annotated-data-model":{"source":"iana"},"model/vnd.sap.vds":{"source":"iana","extensions":["vds"]},"model/vnd.usdz+zip":{"source":"iana","compressible":false,"extensions":["usdz"]},"model/vnd.valve.source.compiled-map":{"source":"iana","extensions":["bsp"]},"model/vnd.vtu":{"source":"iana","extensions":["vtu"]},"model/vrml":{"source":"iana","compressible":false,"extensions":["wrl","vrml"]},"model/x3d+binary":{"source":"apache","compressible":false,"extensions":["x3db","x3dbz"]},"model/x3d+fastinfoset":{"source":"iana","extensions":["x3db"]},"model/x3d+vrml":{"source":"apache","compressible":false,"extensions":["x3dv","x3dvz"]},"model/x3d+xml":{"source":"iana","compressible":true,"extensions":["x3d","x3dz"]},"model/x3d-vrml":{"source":"iana","extensions":["x3dv"]},"multipart/alternative":{"source":"iana","compressible":false},"multipart/appledouble":{"source":"iana"},"multipart/byteranges":{"source":"iana"},"multipart/digest":{"source":"iana"},"multipart/encrypted":{"source":"iana","compressible":false},"multipart/form-data":{"source":"iana","compressible":false},"multipart/header-set":{"source":"iana"},"multipart/mixed":{"source":"iana"},"multipart/multilingual":{"source":"iana"},"multipart/parallel":{"source":"iana"},"multipart/related":{"source":"iana","compressible":false},"multipart/report":{"source":"iana"},"multipart/signed":{"source":"iana","compressible":false},"multipart/vnd.bint.med-plus":{"source":"iana"},"multipart/voice-message":{"source":"iana"},"multipart/x-mixed-replace":{"source":"iana"},"text/1d-interleaved-parityfec":{"source":"iana"},"text/cache-manifest":{"source":"iana","compressible":true,"extensions":["appcache","manifest"]},"text/calendar":{"source":"iana","extensions":["ics","ifb"]},"text/calender":{"compressible":true},"text/cmd":{"compressible":true},"text/coffeescript":{"extensions":["coffee","litcoffee"]},"text/cql":{"source":"iana"},"text/cql-expression":{"source":"iana"},"text/cql-identifier":{"source":"iana"},"text/css":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["css"]},"text/csv":{"source":"iana","compressible":true,"extensions":["csv"]},"text/csv-schema":{"source":"iana"},"text/directory":{"source":"iana"},"text/dns":{"source":"iana"},"text/ecmascript":{"source":"iana"},"text/encaprtp":{"source":"iana"},"text/enriched":{"source":"iana"},"text/fhirpath":{"source":"iana"},"text/flexfec":{"source":"iana"},"text/fwdred":{"source":"iana"},"text/gff3":{"source":"iana"},"text/grammar-ref-list":{"source":"iana"},"text/html":{"source":"iana","compressible":true,"extensions":["html","htm","shtml"]},"text/jade":{"extensions":["jade"]},"text/javascript":{"source":"iana","compressible":true},"text/jcr-cnd":{"source":"iana"},"text/jsx":{"compressible":true,"extensions":["jsx"]},"text/less":{"compressible":true,"extensions":["less"]},"text/markdown":{"source":"iana","compressible":true,"extensions":["markdown","md"]},"text/mathml":{"source":"nginx","extensions":["mml"]},"text/mdx":{"compressible":true,"extensions":["mdx"]},"text/mizar":{"source":"iana"},"text/n3":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["n3"]},"text/parameters":{"source":"iana","charset":"UTF-8"},"text/parityfec":{"source":"iana"},"text/plain":{"source":"iana","compressible":true,"extensions":["txt","text","conf","def","list","log","in","ini"]},"text/provenance-notation":{"source":"iana","charset":"UTF-8"},"text/prs.fallenstein.rst":{"source":"iana"},"text/prs.lines.tag":{"source":"iana","extensions":["dsc"]},"text/prs.prop.logic":{"source":"iana"},"text/raptorfec":{"source":"iana"},"text/red":{"source":"iana"},"text/rfc822-headers":{"source":"iana"},"text/richtext":{"source":"iana","compressible":true,"extensions":["rtx"]},"text/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"text/rtp-enc-aescm128":{"source":"iana"},"text/rtploopback":{"source":"iana"},"text/rtx":{"source":"iana"},"text/sgml":{"source":"iana","extensions":["sgml","sgm"]},"text/shaclc":{"source":"iana"},"text/shex":{"source":"iana","extensions":["shex"]},"text/slim":{"extensions":["slim","slm"]},"text/spdx":{"source":"iana","extensions":["spdx"]},"text/strings":{"source":"iana"},"text/stylus":{"extensions":["stylus","styl"]},"text/t140":{"source":"iana"},"text/tab-separated-values":{"source":"iana","compressible":true,"extensions":["tsv"]},"text/troff":{"source":"iana","extensions":["t","tr","roff","man","me","ms"]},"text/turtle":{"source":"iana","charset":"UTF-8","extensions":["ttl"]},"text/ulpfec":{"source":"iana"},"text/uri-list":{"source":"iana","compressible":true,"extensions":["uri","uris","urls"]},"text/vcard":{"source":"iana","compressible":true,"extensions":["vcard"]},"text/vnd.a":{"source":"iana"},"text/vnd.abc":{"source":"iana"},"text/vnd.ascii-art":{"source":"iana"},"text/vnd.curl":{"source":"iana","extensions":["curl"]},"text/vnd.curl.dcurl":{"source":"apache","extensions":["dcurl"]},"text/vnd.curl.mcurl":{"source":"apache","extensions":["mcurl"]},"text/vnd.curl.scurl":{"source":"apache","extensions":["scurl"]},"text/vnd.debian.copyright":{"source":"iana","charset":"UTF-8"},"text/vnd.dmclientscript":{"source":"iana"},"text/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"text/vnd.esmertec.theme-descriptor":{"source":"iana","charset":"UTF-8"},"text/vnd.familysearch.gedcom":{"source":"iana","extensions":["ged"]},"text/vnd.ficlab.flt":{"source":"iana"},"text/vnd.fly":{"source":"iana","extensions":["fly"]},"text/vnd.fmi.flexstor":{"source":"iana","extensions":["flx"]},"text/vnd.gml":{"source":"iana"},"text/vnd.graphviz":{"source":"iana","extensions":["gv"]},"text/vnd.hans":{"source":"iana"},"text/vnd.hgl":{"source":"iana"},"text/vnd.in3d.3dml":{"source":"iana","extensions":["3dml"]},"text/vnd.in3d.spot":{"source":"iana","extensions":["spot"]},"text/vnd.iptc.newsml":{"source":"iana"},"text/vnd.iptc.nitf":{"source":"iana"},"text/vnd.latex-z":{"source":"iana"},"text/vnd.motorola.reflex":{"source":"iana"},"text/vnd.ms-mediapackage":{"source":"iana"},"text/vnd.net2phone.commcenter.command":{"source":"iana"},"text/vnd.radisys.msml-basic-layout":{"source":"iana"},"text/vnd.senx.warpscript":{"source":"iana"},"text/vnd.si.uricatalogue":{"source":"iana"},"text/vnd.sosi":{"source":"iana"},"text/vnd.sun.j2me.app-descriptor":{"source":"iana","charset":"UTF-8","extensions":["jad"]},"text/vnd.trolltech.linguist":{"source":"iana","charset":"UTF-8"},"text/vnd.wap.si":{"source":"iana"},"text/vnd.wap.sl":{"source":"iana"},"text/vnd.wap.wml":{"source":"iana","extensions":["wml"]},"text/vnd.wap.wmlscript":{"source":"iana","extensions":["wmls"]},"text/vtt":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["vtt"]},"text/x-asm":{"source":"apache","extensions":["s","asm"]},"text/x-c":{"source":"apache","extensions":["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{"source":"nginx","extensions":["htc"]},"text/x-fortran":{"source":"apache","extensions":["f","for","f77","f90"]},"text/x-gwt-rpc":{"compressible":true},"text/x-handlebars-template":{"extensions":["hbs"]},"text/x-java-source":{"source":"apache","extensions":["java"]},"text/x-jquery-tmpl":{"compressible":true},"text/x-lua":{"extensions":["lua"]},"text/x-markdown":{"compressible":true,"extensions":["mkd"]},"text/x-nfo":{"source":"apache","extensions":["nfo"]},"text/x-opml":{"source":"apache","extensions":["opml"]},"text/x-org":{"compressible":true,"extensions":["org"]},"text/x-pascal":{"source":"apache","extensions":["p","pas"]},"text/x-processing":{"compressible":true,"extensions":["pde"]},"text/x-sass":{"extensions":["sass"]},"text/x-scss":{"extensions":["scss"]},"text/x-setext":{"source":"apache","extensions":["etx"]},"text/x-sfv":{"source":"apache","extensions":["sfv"]},"text/x-suse-ymp":{"compressible":true,"extensions":["ymp"]},"text/x-uuencode":{"source":"apache","extensions":["uu"]},"text/x-vcalendar":{"source":"apache","extensions":["vcs"]},"text/x-vcard":{"source":"apache","extensions":["vcf"]},"text/xml":{"source":"iana","compressible":true,"extensions":["xml"]},"text/xml-external-parsed-entity":{"source":"iana"},"text/yaml":{"compressible":true,"extensions":["yaml","yml"]},"video/1d-interleaved-parityfec":{"source":"iana"},"video/3gpp":{"source":"iana","extensions":["3gp","3gpp"]},"video/3gpp-tt":{"source":"iana"},"video/3gpp2":{"source":"iana","extensions":["3g2"]},"video/av1":{"source":"iana"},"video/bmpeg":{"source":"iana"},"video/bt656":{"source":"iana"},"video/celb":{"source":"iana"},"video/dv":{"source":"iana"},"video/encaprtp":{"source":"iana"},"video/ffv1":{"source":"iana"},"video/flexfec":{"source":"iana"},"video/h261":{"source":"iana","extensions":["h261"]},"video/h263":{"source":"iana","extensions":["h263"]},"video/h263-1998":{"source":"iana"},"video/h263-2000":{"source":"iana"},"video/h264":{"source":"iana","extensions":["h264"]},"video/h264-rcdo":{"source":"iana"},"video/h264-svc":{"source":"iana"},"video/h265":{"source":"iana"},"video/iso.segment":{"source":"iana","extensions":["m4s"]},"video/jpeg":{"source":"iana","extensions":["jpgv"]},"video/jpeg2000":{"source":"iana"},"video/jpm":{"source":"apache","extensions":["jpm","jpgm"]},"video/jxsv":{"source":"iana"},"video/mj2":{"source":"iana","extensions":["mj2","mjp2"]},"video/mp1s":{"source":"iana"},"video/mp2p":{"source":"iana"},"video/mp2t":{"source":"iana","extensions":["ts"]},"video/mp4":{"source":"iana","compressible":false,"extensions":["mp4","mp4v","mpg4"]},"video/mp4v-es":{"source":"iana"},"video/mpeg":{"source":"iana","compressible":false,"extensions":["mpeg","mpg","mpe","m1v","m2v"]},"video/mpeg4-generic":{"source":"iana"},"video/mpv":{"source":"iana"},"video/nv":{"source":"iana"},"video/ogg":{"source":"iana","compressible":false,"extensions":["ogv"]},"video/parityfec":{"source":"iana"},"video/pointer":{"source":"iana"},"video/quicktime":{"source":"iana","compressible":false,"extensions":["qt","mov"]},"video/raptorfec":{"source":"iana"},"video/raw":{"source":"iana"},"video/rtp-enc-aescm128":{"source":"iana"},"video/rtploopback":{"source":"iana"},"video/rtx":{"source":"iana"},"video/scip":{"source":"iana"},"video/smpte291":{"source":"iana"},"video/smpte292m":{"source":"iana"},"video/ulpfec":{"source":"iana"},"video/vc1":{"source":"iana"},"video/vc2":{"source":"iana"},"video/vnd.cctv":{"source":"iana"},"video/vnd.dece.hd":{"source":"iana","extensions":["uvh","uvvh"]},"video/vnd.dece.mobile":{"source":"iana","extensions":["uvm","uvvm"]},"video/vnd.dece.mp4":{"source":"iana"},"video/vnd.dece.pd":{"source":"iana","extensions":["uvp","uvvp"]},"video/vnd.dece.sd":{"source":"iana","extensions":["uvs","uvvs"]},"video/vnd.dece.video":{"source":"iana","extensions":["uvv","uvvv"]},"video/vnd.directv.mpeg":{"source":"iana"},"video/vnd.directv.mpeg-tts":{"source":"iana"},"video/vnd.dlna.mpeg-tts":{"source":"iana"},"video/vnd.dvb.file":{"source":"iana","extensions":["dvb"]},"video/vnd.fvt":{"source":"iana","extensions":["fvt"]},"video/vnd.hns.video":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.ttsavc":{"source":"iana"},"video/vnd.iptvforum.ttsmpeg2":{"source":"iana"},"video/vnd.motorola.video":{"source":"iana"},"video/vnd.motorola.videop":{"source":"iana"},"video/vnd.mpegurl":{"source":"iana","extensions":["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{"source":"iana","extensions":["pyv"]},"video/vnd.nokia.interleaved-multimedia":{"source":"iana"},"video/vnd.nokia.mp4vr":{"source":"iana"},"video/vnd.nokia.videovoip":{"source":"iana"},"video/vnd.objectvideo":{"source":"iana"},"video/vnd.radgamettools.bink":{"source":"iana"},"video/vnd.radgamettools.smacker":{"source":"iana"},"video/vnd.sealed.mpeg1":{"source":"iana"},"video/vnd.sealed.mpeg4":{"source":"iana"},"video/vnd.sealed.swf":{"source":"iana"},"video/vnd.sealedmedia.softseal.mov":{"source":"iana"},"video/vnd.uvvu.mp4":{"source":"iana","extensions":["uvu","uvvu"]},"video/vnd.vivo":{"source":"iana","extensions":["viv"]},"video/vnd.youtube.yt":{"source":"iana"},"video/vp8":{"source":"iana"},"video/vp9":{"source":"iana"},"video/webm":{"source":"apache","compressible":false,"extensions":["webm"]},"video/x-f4v":{"source":"apache","extensions":["f4v"]},"video/x-fli":{"source":"apache","extensions":["fli"]},"video/x-flv":{"source":"apache","compressible":false,"extensions":["flv"]},"video/x-m4v":{"source":"apache","extensions":["m4v"]},"video/x-matroska":{"source":"apache","compressible":false,"extensions":["mkv","mk3d","mks"]},"video/x-mng":{"source":"apache","extensions":["mng"]},"video/x-ms-asf":{"source":"apache","extensions":["asf","asx"]},"video/x-ms-vob":{"source":"apache","extensions":["vob"]},"video/x-ms-wm":{"source":"apache","extensions":["wm"]},"video/x-ms-wmv":{"source":"apache","compressible":false,"extensions":["wmv"]},"video/x-ms-wmx":{"source":"apache","extensions":["wmx"]},"video/x-ms-wvx":{"source":"apache","extensions":["wvx"]},"video/x-msvideo":{"source":"apache","extensions":["avi"]},"video/x-sgi-movie":{"source":"apache","extensions":["movie"]},"video/x-smv":{"source":"apache","extensions":["smv"]},"x-conference/x-cooltalk":{"source":"apache","extensions":["ice"]},"x-shader/x-fragment":{"compressible":true},"x-shader/x-vertex":{"compressible":true}}')},25337:e=>{"use strict";e.exports=JSON.parse('{"name":"superagent","version":"4.1.0","description":"elegant & feature rich browser / node HTTP with a fluent API","scripts":{"prepare":"make all","test":"make test","test-http2":"make test-node-http2"},"keywords":["http","ajax","request","agent"],"license":"MIT","author":"TJ Holowaychuk <<EMAIL>>","contributors":["Kornel Lesiński <<EMAIL>>","Peter Lyons <<EMAIL>>","Hunter Loftis <<EMAIL>>"],"repository":{"type":"git","url":"git://github.com/visionmedia/superagent.git"},"dependencies":{"component-emitter":"^1.2.0","cookiejar":"^2.1.2","debug":"^4.1.0","form-data":"^2.3.3","formidable":"^1.2.0","methods":"^1.1.1","mime":"^2.4.0","qs":"^6.6.0","readable-stream":"^3.0.6"},"devDependencies":{"Base64":"^1.0.1","babel-core":"^6.26.3","babel-preset-es2015":"^6.24.1","babelify":"^8.0.0","basic-auth-connect":"^1.0.0","body-parser":"^1.18.2","browserify":"^16.2.3","cookie-parser":"^1.4.3","express":"^4.16.3","express-session":"^1.15.6","marked":"^0.5.2","mocha":"^3.5.3","multer":"^1.4.1","should":"^13.2.0","should-http":"^0.1.1","zuul":"^3.12.0"},"browser":{"./lib/node/index.js":"./lib/client.js","./test/support/server.js":"./test/support/blank.js"},"component":{"scripts":{"superagent":"lib/client.js"}},"main":"./lib/node/index.js","engines":{"node":">= 6.0"}}')},19112:e=>{"use strict";e.exports=JSON.parse('{"/":{"namespace":"","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"context":{}}}]},"/oembed/1.0":{"namespace":"oembed/1.0","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"namespace":{},"context":{}}}]},"/oembed/1.0/embed":{"namespace":"oembed/1.0","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"url":{},"format":{},"maxwidth":{}}}]},"/oembed/1.0/proxy":{"namespace":"oembed/1.0","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"url":{},"format":{},"maxwidth":{},"maxheight":{},"discover":{}}}]},"/wp/v2":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"namespace":{},"context":{}}}]},"/wp/v2/posts":{"namespace":"wp/v2","methods":["GET","POST"],"endpoints":[{"methods":["GET"],"args":{"context":{},"page":{},"per_page":{},"search":{},"after":{},"author":{},"author_exclude":{},"before":{},"exclude":{},"include":{},"offset":{},"order":{},"orderby":{},"slug":{},"status":{},"categories":{},"categories_exclude":{},"tags":{},"tags_exclude":{},"sticky":{}}},{"methods":["POST"],"args":{}}]},"/wp/v2/posts/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET","POST","PUT","PATCH","DELETE"],"endpoints":[{"methods":["GET"],"args":{"id":{},"context":{},"password":{}}},{"methods":["POST","PUT","PATCH"],"args":{}},{"methods":["DELETE"],"args":{}}]},"/wp/v2/posts/(?P<parent>[\\\\d]+)/revisions":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"parent":{},"context":{},"page":{},"per_page":{},"search":{},"exclude":{},"include":{},"offset":{},"order":{},"orderby":{}}}]},"/wp/v2/posts/(?P<parent>[\\\\d]+)/revisions/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET","DELETE"],"endpoints":[{"methods":["GET"],"args":{"parent":{},"id":{},"context":{}}},{"methods":["DELETE"],"args":{}}]},"/wp/v2/posts/(?P<id>[\\\\d]+)/autosaves":{"namespace":"wp/v2","methods":["GET","POST"],"endpoints":[{"methods":["GET"],"args":{"parent":{},"context":{}}},{"methods":["POST"],"args":{}}]},"/wp/v2/posts/(?P<parent>[\\\\d]+)/autosaves/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"parent":{},"id":{},"context":{}}}]},"/wp/v2/pages":{"namespace":"wp/v2","methods":["GET","POST"],"endpoints":[{"methods":["GET"],"args":{"context":{},"page":{},"per_page":{},"search":{},"after":{},"author":{},"author_exclude":{},"before":{},"exclude":{},"include":{},"menu_order":{},"offset":{},"order":{},"orderby":{},"parent":{},"parent_exclude":{},"slug":{},"status":{}}},{"methods":["POST"],"args":{}}]},"/wp/v2/pages/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET","POST","PUT","PATCH","DELETE"],"endpoints":[{"methods":["GET"],"args":{"id":{},"context":{},"password":{}}},{"methods":["POST","PUT","PATCH"],"args":{}},{"methods":["DELETE"],"args":{}}]},"/wp/v2/pages/(?P<parent>[\\\\d]+)/revisions":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"parent":{},"context":{},"page":{},"per_page":{},"search":{},"exclude":{},"include":{},"offset":{},"order":{},"orderby":{}}}]},"/wp/v2/pages/(?P<parent>[\\\\d]+)/revisions/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET","DELETE"],"endpoints":[{"methods":["GET"],"args":{"parent":{},"id":{},"context":{}}},{"methods":["DELETE"],"args":{}}]},"/wp/v2/pages/(?P<id>[\\\\d]+)/autosaves":{"namespace":"wp/v2","methods":["GET","POST"],"endpoints":[{"methods":["GET"],"args":{"parent":{},"context":{}}},{"methods":["POST"],"args":{}}]},"/wp/v2/pages/(?P<parent>[\\\\d]+)/autosaves/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"parent":{},"id":{},"context":{}}}]},"/wp/v2/media":{"namespace":"wp/v2","methods":["GET","POST"],"endpoints":[{"methods":["GET"],"args":{"context":{},"page":{},"per_page":{},"search":{},"after":{},"author":{},"author_exclude":{},"before":{},"exclude":{},"include":{},"offset":{},"order":{},"orderby":{},"parent":{},"parent_exclude":{},"slug":{},"status":{},"media_type":{},"mime_type":{}}},{"methods":["POST"],"args":{}}]},"/wp/v2/media/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET","POST","PUT","PATCH","DELETE"],"endpoints":[{"methods":["GET"],"args":{"id":{},"context":{}}},{"methods":["POST","PUT","PATCH"],"args":{}},{"methods":["DELETE"],"args":{}}]},"/wp/v2/blocks":{"namespace":"wp/v2","methods":["GET","POST"],"endpoints":[{"methods":["GET"],"args":{"context":{},"page":{},"per_page":{},"search":{},"after":{},"before":{},"exclude":{},"include":{},"offset":{},"order":{},"orderby":{},"slug":{},"status":{}}},{"methods":["POST"],"args":{}}]},"/wp/v2/blocks/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET","POST","PUT","PATCH","DELETE"],"endpoints":[{"methods":["GET"],"args":{"id":{},"context":{},"password":{}}},{"methods":["POST","PUT","PATCH"],"args":{}},{"methods":["DELETE"],"args":{}}]},"/wp/v2/blocks/(?P<id>[\\\\d]+)/autosaves":{"namespace":"wp/v2","methods":["GET","POST"],"endpoints":[{"methods":["GET"],"args":{"parent":{},"context":{}}},{"methods":["POST"],"args":{}}]},"/wp/v2/blocks/(?P<parent>[\\\\d]+)/autosaves/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"parent":{},"id":{},"context":{}}}]},"/wp/v2/types":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"context":{}}}]},"/wp/v2/types/(?P<type>[\\\\w-]+)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"type":{},"context":{}}}]},"/wp/v2/statuses":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"context":{}}}]},"/wp/v2/statuses/(?P<status>[\\\\w-]+)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"status":{},"context":{}}}]},"/wp/v2/taxonomies":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"context":{},"type":{}}}]},"/wp/v2/taxonomies/(?P<taxonomy>[\\\\w-]+)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"taxonomy":{},"context":{}}}]},"/wp/v2/categories":{"namespace":"wp/v2","methods":["GET","POST"],"endpoints":[{"methods":["GET"],"args":{"context":{},"page":{},"per_page":{},"search":{},"exclude":{},"include":{},"order":{},"orderby":{},"hide_empty":{},"parent":{},"post":{},"slug":{}}},{"methods":["POST"],"args":{}}]},"/wp/v2/categories/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET","POST","PUT","PATCH","DELETE"],"endpoints":[{"methods":["GET"],"args":{"id":{},"context":{}}},{"methods":["POST","PUT","PATCH"],"args":{}},{"methods":["DELETE"],"args":{}}]},"/wp/v2/tags":{"namespace":"wp/v2","methods":["GET","POST"],"endpoints":[{"methods":["GET"],"args":{"context":{},"page":{},"per_page":{},"search":{},"exclude":{},"include":{},"offset":{},"order":{},"orderby":{},"hide_empty":{},"post":{},"slug":{}}},{"methods":["POST"],"args":{}}]},"/wp/v2/tags/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET","POST","PUT","PATCH","DELETE"],"endpoints":[{"methods":["GET"],"args":{"id":{},"context":{}}},{"methods":["POST","PUT","PATCH"],"args":{}},{"methods":["DELETE"],"args":{}}]},"/wp/v2/users":{"namespace":"wp/v2","methods":["GET","POST"],"endpoints":[{"methods":["GET"],"args":{"context":{},"page":{},"per_page":{},"search":{},"exclude":{},"include":{},"offset":{},"order":{},"orderby":{},"slug":{},"roles":{},"who":{}}},{"methods":["POST"],"args":{}}]},"/wp/v2/users/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET","POST","PUT","PATCH","DELETE"],"endpoints":[{"methods":["GET"],"args":{"id":{},"context":{}}},{"methods":["POST","PUT","PATCH"],"args":{}},{"methods":["DELETE"],"args":{}}]},"/wp/v2/users/me":{"namespace":"wp/v2","methods":["GET","POST","PUT","PATCH","DELETE"],"endpoints":[{"methods":["GET"],"args":{"context":{}}},{"methods":["POST","PUT","PATCH"],"args":{}},{"methods":["DELETE"],"args":{}}]},"/wp/v2/comments":{"namespace":"wp/v2","methods":["GET","POST"],"endpoints":[{"methods":["GET"],"args":{"context":{},"page":{},"per_page":{},"search":{},"after":{},"author":{},"author_exclude":{},"author_email":{},"before":{},"exclude":{},"include":{},"offset":{},"order":{},"orderby":{},"parent":{},"parent_exclude":{},"post":{},"status":{},"type":{},"password":{}}},{"methods":["POST"],"args":{}}]},"/wp/v2/comments/(?P<id>[\\\\d]+)":{"namespace":"wp/v2","methods":["GET","POST","PUT","PATCH","DELETE"],"endpoints":[{"methods":["GET"],"args":{"id":{},"context":{},"password":{}}},{"methods":["POST","PUT","PATCH"],"args":{}},{"methods":["DELETE"],"args":{}}]},"/wp/v2/search":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"context":{},"page":{},"per_page":{},"search":{},"type":{},"subtype":{}}}]},"/wp/v2/block-renderer/(?P<name>core/block)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"name":{},"context":{},"attributes":{},"post_id":{}}}]},"/wp/v2/block-renderer/(?P<name>core/latest-comments)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"name":{},"context":{},"attributes":{},"post_id":{}}}]},"/wp/v2/block-renderer/(?P<name>core/archives)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"name":{},"context":{},"attributes":{},"post_id":{}}}]},"/wp/v2/block-renderer/(?P<name>core/categories)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"name":{},"context":{},"attributes":{},"post_id":{}}}]},"/wp/v2/block-renderer/(?P<name>core/latest-posts)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"name":{},"context":{},"attributes":{},"post_id":{}}}]},"/wp/v2/block-renderer/(?P<name>core/shortcode)":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"name":{},"context":{},"attributes":{},"post_id":{}}}]},"/wp/v2/settings":{"namespace":"wp/v2","methods":["GET","POST","PUT","PATCH"],"endpoints":[{"methods":["GET"],"args":{}},{"methods":["POST","PUT","PATCH"],"args":{}}]},"/wp/v2/themes":{"namespace":"wp/v2","methods":["GET"],"endpoints":[{"methods":["GET"],"args":{"context":{},"page":{},"per_page":{},"search":{},"status":{}}}]}}')}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),i=t.X(0,[948,518],()=>a(60577));module.exports=i})();