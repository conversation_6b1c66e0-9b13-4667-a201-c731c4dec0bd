(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[727],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},6195:e=>{"use strict";e.exports=require("node:buffer")},2480:()=>{},7784:(e,t,r)=>{"use strict";let n;r.r(t),r.d(t,{default:()=>iD});var i,a,s,o,l,c,u,d,h,p,f,g,m,y,v,b,w,_,S,k,x,T,E,C,O,P,I,R,N,A,M,U={};async function L(){let e="_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&(await _ENTRIES.middleware_instrumentation).register;if(e)try{await e()}catch(e){throw e.message=`An error occurred while loading instrumentation hook: ${e.message}`,e}}r.r(U),r.d(U,{config:()=>iA,default:()=>iN});let D=null;function j(){return D||(D=L()),D}function q(e){return`The edge runtime does not support Node.js '${e}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==r.g.process&&(process.env=r.g.process.env,r.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(e){let t=new Proxy(function(){},{get(t,r){if("then"===r)return{};throw Error(q(e))},construct(){throw Error(q(e))},apply(r,n,i){if("function"==typeof i[0])return i[0](t);throw Error(q(e))}});return new Proxy({},{get:()=>t})},enumerable:!1,configurable:!1}),j();class B extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class H extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class z extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}function $(e){var t,r,n,i,a,s=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,a=!1;l();)if(","===(r=e.charAt(o))){for(n=o,o+=1,l(),i=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(a=!0,o=i,s.push(e.substring(t,n)),t=o):o=n+1}else o+=1;(!a||o>=e.length)&&s.push(e.substring(t,e.length))}return s}function K(e){let t={},r=[];if(e)for(let[n,i]of e.entries())"set-cookie"===n.toLowerCase()?(r.push(...$(i)),t[n]=1===r.length?r[0]:r):t[n]=i;return t}function F(e){try{return String(new URL(String(e)))}catch(t){throw Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t})}}let V=Symbol("response"),J=Symbol("passThrough"),W=Symbol("waitUntil");class G{constructor(e){this[W]=[],this[J]=!1}respondWith(e){this[V]||(this[V]=Promise.resolve(e))}passThroughOnException(){this[J]=!0}waitUntil(e){this[W].push(e)}}class X extends G{constructor(e){super(e.request),this.sourcePage=e.page}get request(){throw new B({page:this.sourcePage})}respondWith(){throw new B({page:this.sourcePage})}}function Q(e){return e.replace(/\/$/,"")||"/"}function Y(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function Z(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=Y(e);return""+t+r+n+i}function ee(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=Y(e);return""+r+t+n+i}function et(e,t){if("string"!=typeof e)return!1;let{pathname:r}=Y(e);return r===t||r.startsWith(t+"/")}function er(e,t){let r;let n=e.split("/");return(t||[]).some(t=>!!n[1]&&n[1].toLowerCase()===t.toLowerCase()&&(r=t,n.splice(1,1),e=n.join("/")||"/",!0)),{pathname:e,detectedLocale:r}}let en=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function ei(e,t){return new URL(String(e).replace(en,"localhost"),t&&String(t).replace(en,"localhost"))}let ea=Symbol("NextURLInternal");class es{constructor(e,t,r){let n,i;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,i=r||{}):i=r||t||{},this[ea]={url:ei(e,n??i.base),options:i,basePath:""},this.analyze()}analyze(){var e,t,r,n,i;let a=function(e,t){var r,n;let{basePath:i,i18n:a,trailingSlash:s}=null!=(r=t.nextConfig)?r:{},o={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):s};i&&et(o.pathname,i)&&(o.pathname=function(e,t){if(!et(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(o.pathname,i),o.basePath=i);let l=o.pathname;if(o.pathname.startsWith("/_next/data/")&&o.pathname.endsWith(".json")){let e=o.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/"),r=e[0];o.buildId=r,l="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(o.pathname=l)}if(a){let e=t.i18nProvider?t.i18nProvider.analyze(o.pathname):er(o.pathname,a.locales);o.locale=e.detectedLocale,o.pathname=null!=(n=e.pathname)?n:o.pathname,!e.detectedLocale&&o.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(l):er(l,a.locales)).detectedLocale&&(o.locale=e.detectedLocale)}return o}(this[ea].url.pathname,{nextConfig:this[ea].options.nextConfig,parseData:!0,i18nProvider:this[ea].options.i18nProvider}),s=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[ea].url,this[ea].options.headers);this[ea].domainLocale=this[ea].options.i18nProvider?this[ea].options.i18nProvider.detectDomainLocale(s):function(e,t,r){if(e)for(let a of(r&&(r=r.toLowerCase()),e)){var n,i;if(t===(null==(n=a.domain)?void 0:n.split(":",1)[0].toLowerCase())||r===a.defaultLocale.toLowerCase()||(null==(i=a.locales)?void 0:i.some(e=>e.toLowerCase()===r)))return a}}(null==(t=this[ea].options.nextConfig)?void 0:null==(e=t.i18n)?void 0:e.domains,s);let o=(null==(r=this[ea].domainLocale)?void 0:r.defaultLocale)||(null==(i=this[ea].options.nextConfig)?void 0:null==(n=i.i18n)?void 0:n.defaultLocale);this[ea].url.pathname=a.pathname,this[ea].defaultLocale=o,this[ea].basePath=a.basePath??"",this[ea].buildId=a.buildId,this[ea].locale=a.locale??o,this[ea].trailingSlash=a.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,n){if(!t||t===r)return e;let i=e.toLowerCase();return!n&&(et(i,"/api")||et(i,"/"+t.toLowerCase()))?e:Z(e,"/"+t)}((e={basePath:this[ea].basePath,buildId:this[ea].buildId,defaultLocale:this[ea].options.forceLocale?void 0:this[ea].defaultLocale,locale:this[ea].locale,pathname:this[ea].url.pathname,trailingSlash:this[ea].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=Q(t)),e.buildId&&(t=ee(Z(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=Z(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:ee(t,"/"):Q(t)}formatSearch(){return this[ea].url.search}get buildId(){return this[ea].buildId}set buildId(e){this[ea].buildId=e}get locale(){return this[ea].locale??""}set locale(e){var t,r;if(!this[ea].locale||!(null==(r=this[ea].options.nextConfig)?void 0:null==(t=r.i18n)?void 0:t.locales.includes(e)))throw TypeError(`The NextURL configuration includes no locale "${e}"`);this[ea].locale=e}get defaultLocale(){return this[ea].defaultLocale}get domainLocale(){return this[ea].domainLocale}get searchParams(){return this[ea].url.searchParams}get host(){return this[ea].url.host}set host(e){this[ea].url.host=e}get hostname(){return this[ea].url.hostname}set hostname(e){this[ea].url.hostname=e}get port(){return this[ea].url.port}set port(e){this[ea].url.port=e}get protocol(){return this[ea].url.protocol}set protocol(e){this[ea].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[ea].url=ei(e),this.analyze()}get origin(){return this[ea].url.origin}get pathname(){return this[ea].url.pathname}set pathname(e){this[ea].url.pathname=e}get hash(){return this[ea].url.hash}set hash(e){this[ea].url.hash=e}get search(){return this[ea].url.search}set search(e){this[ea].url.search=e}get password(){return this[ea].url.password}set password(e){this[ea].url.password=e}get username(){return this[ea].url.username}set username(e){this[ea].url.username=e}get basePath(){return this[ea].basePath}set basePath(e){this[ea].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new es(String(this),this[ea].options)}}var eo=r(938);let el=Symbol("internal request");class ec extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);F(r),e instanceof Request?super(e,t):super(r,t);let n=new es(r,{headers:K(this.headers),nextConfig:t.nextConfig});this[el]={cookies:new eo.qC(this.headers),geo:t.geo||{},ip:t.ip,nextUrl:n,url:n.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,geo:this.geo,ip:this.ip,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[el].cookies}get geo(){return this[el].geo}get ip(){return this[el].ip}get nextUrl(){return this[el].nextUrl}get page(){throw new H}get ua(){throw new z}get url(){return this[el].url}}var eu=r(7217);let ed=Symbol("internal response"),eh=new Set([301,302,303,307,308]);function ep(e,t){var r;if(null==e?void 0:null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Error("request.headers must be an instance of Headers");let r=[];for(let[n,i]of e.request.headers)t.set("x-middleware-request-"+n,i),r.push(n);t.set("x-middleware-override-headers",r.join(","))}}class ef extends Response{constructor(e,t={}){super(e,t);let r=this.headers,n=new Proxy(new eo.nV(r),{get(e,n,i){switch(n){case"delete":case"set":return(...i)=>{let a=Reflect.apply(e[n],e,i),s=new Headers(r);return a instanceof eo.nV&&r.set("x-middleware-set-cookie",a.getAll().map(e=>(0,eo.Q7)(e)).join(",")),ep(t,s),a};default:return eu.g.get(e,n,i)}}});this[ed]={cookies:n,url:t.url?new es(t.url,{headers:K(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[ed].cookies}static json(e,t){let r=Response.json(e,t);return new ef(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!eh.has(r))throw RangeError('Failed to execute "redirect" on "response": Invalid status code');let n="object"==typeof t?t:{},i=new Headers(null==n?void 0:n.headers);return i.set("Location",F(e)),new ef(null,{...n,headers:i,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",F(e)),ep(t,r),new ef(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),ep(e,t),new ef(null,{...e,headers:t})}}function eg(e,t){let r="string"==typeof t?new URL(t):t,n=new URL(e,t),i=r.protocol+"//"+r.host;return n.protocol+"//"+n.host===i?n.toString().replace(i,""):n.toString()}let em=[["RSC"],["Next-Router-State-Tree"],["Next-Router-Prefetch"]],ey=["__nextFallback","__nextLocale","__nextInferredLocaleFromDefault","__nextDefaultLocale","__nextIsNotFound","_rsc"],ev=["__nextDataReq"],eb="nxtP",ew={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",appMetadataRoute:"app-metadata-route",appRouteHandler:"app-route-handler"};({...ew,GROUP:{serverOnly:[ew.reactServerComponents,ew.actionBrowser,ew.appMetadataRoute,ew.appRouteHandler,ew.instrument],clientOnly:[ew.serverSideRendering,ew.appPagesBrowser],nonClientServerTarget:[ew.middleware,ew.api],app:[ew.reactServerComponents,ew.actionBrowser,ew.appMetadataRoute,ew.appRouteHandler,ew.serverSideRendering,ew.appPagesBrowser,ew.shared,ew.instrument]}});var e_=r(7862),eS=r(8009);!function(e){e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404"}(i||(i={})),function(e){e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents"}(a||(a={})),function(e){e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer"}(s||(s={})),function(e){e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch"}(o||(o={})),(l||(l={})).startServer="startServer.startServer",function(e){e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult"}(c||(c={})),function(e){e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch"}(u||(u={})),(d||(d={})).executeRoute="Router.executeRoute",(h||(h={})).runHandler="Node.runHandler",(p||(p={})).runHandler="AppRouteRouteHandlers.runHandler",function(e){e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport"}(f||(f={})),(g||(g={})).execute="Middleware.execute";let ek=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],ex=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"],{context:eT,propagation:eE,trace:eC,SpanStatusCode:eO,SpanKind:eP,ROOT_CONTEXT:eI}=n=r(8439),eR=e=>null!==e&&"object"==typeof e&&"function"==typeof e.then,eN=(e,t)=>{(null==t?void 0:t.bubble)===!0?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:eO.ERROR,message:null==t?void 0:t.message})),e.end()},eA=new Map,eM=n.createContextKey("next.rootSpanId"),eU=0,eL=()=>eU++;class eD{getTracerInstance(){return eC.getTracer("next.js","0.0.1")}getContext(){return eT}getActiveScopeSpan(){return eC.getSpan(null==eT?void 0:eT.active())}withPropagatedContext(e,t,r){let n=eT.active();if(eC.getSpanContext(n))return t();let i=eE.extract(n,e,r);return eT.with(i,t)}trace(...e){var t;let[r,n,i]=e,{fn:a,options:s}="function"==typeof n?{fn:n,options:{}}:{fn:i,options:{...n}},o=s.spanName??r;if(!ek.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||s.hideSpan)return a();let l=this.getSpanContext((null==s?void 0:s.parentSpan)??this.getActiveScopeSpan()),c=!1;l?(null==(t=eC.getSpanContext(l))?void 0:t.isRemote)&&(c=!0):(l=(null==eT?void 0:eT.active())??eI,c=!0);let u=eL();return s.attributes={"next.span_name":o,"next.span_type":r,...s.attributes},eT.with(l.setValue(eM,u),()=>this.getTracerInstance().startActiveSpan(o,s,e=>{let t="performance"in globalThis?globalThis.performance.now():void 0,n=()=>{eA.delete(u),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&ex.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};c&&eA.set(u,new Map(Object.entries(s.attributes??{})));try{if(a.length>1)return a(e,t=>eN(e,t));let t=a(e);if(eR(t))return t.then(t=>(e.end(),t)).catch(t=>{throw eN(e,t),t}).finally(n);return e.end(),n(),t}catch(t){throw eN(e,t),n(),t}}))}wrap(...e){let t=this,[r,n,i]=3===e.length?e:[e[0],{},e[1]];return ek.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=n;"function"==typeof e&&"function"==typeof i&&(e=e.apply(this,arguments));let a=arguments.length-1,s=arguments[a];if("function"!=typeof s)return t.trace(r,e,()=>i.apply(this,arguments));{let n=t.getContext().bind(eT.active(),s);return t.trace(r,e,(e,t)=>(arguments[a]=function(e){return null==t||t(e),n.apply(this,arguments)},i.apply(this,arguments)))}}:i}startSpan(...e){let[t,r]=e,n=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,n)}getSpanContext(e){return e?eC.setSpan(eT.active(),e):void 0}getRootSpanAttributes(){let e=eT.active().getValue(eM);return eA.get(e)}}let ej=(()=>{let e=new eD;return()=>e})(),eq="__prerender_bypass";Symbol("__next_preview_data"),Symbol(eq);class eB{constructor(e,t,r,n){var i;let a=e&&function(e,t){let r=e_.h.from(e.headers);return{isOnDemandRevalidate:r.get("x-prerender-revalidate")===t.previewModeId,revalidateOnlyGenerated:r.has("x-prerender-revalidate-if-generated")}}(t,e).isOnDemandRevalidate,s=null==(i=r.get(eq))?void 0:i.value;this.isEnabled=!!(!a&&s&&e&&s===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=n}enable(){if(!this._previewModeId)throw Error("Invariant: previewProps missing previewModeId this should never happen");this._mutableCookies.set({name:eq,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"})}disable(){this._mutableCookies.set({name:eq,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)})}}function eH(e,t){if("x-middleware-set-cookie"in e.headers&&"string"==typeof e.headers["x-middleware-set-cookie"]){let r=e.headers["x-middleware-set-cookie"],n=new Headers;for(let e of $(r))n.append("set-cookie",e);for(let e of new eo.nV(n).getAll())t.set(e)}}let ez={wrap(e,{req:t,res:r,renderOpts:n},i){let a;function s(e){r&&r.setHeader("Set-Cookie",e)}n&&"previewProps"in n&&(a=n.previewProps);let o={},l={get headers(){return o.headers||(o.headers=function(e){let t=e_.h.from(e);for(let e of em)t.delete(e.toString().toLowerCase());return e_.h.seal(t)}(t.headers)),o.headers},get cookies(){if(!o.cookies){let e=new eo.qC(e_.h.from(t.headers));eH(t,e),o.cookies=eS.Qb.seal(e)}return o.cookies},get mutableCookies(){if(!o.mutableCookies){let e=function(e,t){let r=new eo.qC(e_.h.from(e));return eS.vr.wrap(r,t)}(t.headers,(null==n?void 0:n.onUpdateCookies)||(r?s:void 0));eH(t,e),o.mutableCookies=e}return o.mutableCookies},get draftMode(){return o.draftMode||(o.draftMode=new eB(a,t,this.cookies,this.mutableCookies)),o.draftMode},reactLoadableManifest:(null==n?void 0:n.reactLoadableManifest)||{},assetPrefix:(null==n?void 0:n.assetPrefix)||""};return e.run(l,i,l)}};var e$=r(6101);function eK(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID,previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}class eF extends ec{constructor(e){super(e.input,e.init),this.sourcePage=e.page}get request(){throw new B({page:this.sourcePage})}respondWith(){throw new B({page:this.sourcePage})}waitUntil(){throw new B({page:this.sourcePage})}}let eV={keys:e=>Array.from(e.keys()),get:(e,t)=>e.get(t)??void 0},eJ=(e,t)=>ej().withPropagatedContext(e.headers,t,eV),eW=!1;async function eG(e){let t,n;!function(){if(!eW&&(eW=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:e,wrapRequestHandler:t}=r(4177);e(),eJ=t(eJ)}}(),await j();let i=void 0!==self.__BUILD_MANIFEST;e.request.url=e.request.url.replace(/\.rsc($|\?)/,"$1");let a=new es(e.request.url,{headers:e.request.headers,nextConfig:e.request.nextConfig});for(let e of[...a.searchParams.keys()]){let t=a.searchParams.getAll(e);if(e!==eb&&e.startsWith(eb)){let r=e.substring(eb.length);for(let e of(a.searchParams.delete(r),t))a.searchParams.append(r,e);a.searchParams.delete(e)}}let s=a.buildId;a.buildId="";let o=e.request.headers["x-nextjs-data"];o&&"/index"===a.pathname&&(a.pathname="/");let l=function(e){let t=new Headers;for(let[r,n]of Object.entries(e))for(let e of Array.isArray(n)?n:[n])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}(e.request.headers),c=new Map;if(!i)for(let e of em){let t=e.toString().toLowerCase();l.get(t)&&(c.set(t,l.get(t)),l.delete(t))}let u=new eF({page:e.page,input:(function(e,t){let r="string"==typeof e,n=r?new URL(e):e;for(let e of ey)n.searchParams.delete(e);if(t)for(let e of ev)n.searchParams.delete(e);return r?n.toString():n})(a,!0).toString(),init:{body:e.request.body,geo:e.request.geo,headers:l,ip:e.request.ip,method:e.request.method,nextConfig:e.request.nextConfig,signal:e.request.signal}});o&&Object.defineProperty(u,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCache&&e.IncrementalCache&&(globalThis.__incrementalCache=new e.IncrementalCache({appDir:!0,fetchCache:!0,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:e.request.headers,requestProtocol:"https",getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:eK()})}));let d=new X({request:u,page:e.page});if((t=await eJ(u,()=>"/middleware"===e.page||"/src/middleware"===e.page?ej().trace(g.execute,{spanName:`middleware ${u.method} ${u.nextUrl.pathname}`,attributes:{"http.target":u.nextUrl.pathname,"http.method":u.method}},()=>ez.wrap(e$.O,{req:u,renderOpts:{onUpdateCookies:e=>{n=e},previewProps:eK()}},()=>e.handler(u,d))):e.handler(u,d)))&&!(t instanceof Response))throw TypeError("Expected an instance of Response to be returned");t&&n&&t.headers.set("set-cookie",n);let h=null==t?void 0:t.headers.get("x-middleware-rewrite");if(t&&h&&!i){let r=new es(h,{forceLocale:!0,headers:e.request.headers,nextConfig:e.request.nextConfig});r.host===u.nextUrl.host&&(r.buildId=s||r.buildId,t.headers.set("x-middleware-rewrite",String(r)));let n=eg(String(r),String(a));o&&t.headers.set("x-nextjs-rewrite",n)}let p=null==t?void 0:t.headers.get("Location");if(t&&p&&!i){let r=new es(p,{forceLocale:!1,headers:e.request.headers,nextConfig:e.request.nextConfig});t=new Response(t.body,t),r.host===u.nextUrl.host&&(r.buildId=s||r.buildId,t.headers.set("Location",String(r))),o&&(t.headers.delete("Location"),t.headers.set("x-nextjs-redirect",eg(String(r),String(a))))}let f=t||ef.next(),m=f.headers.get("x-middleware-override-headers"),y=[];if(m){for(let[e,t]of c)f.headers.set(`x-middleware-request-${e}`,t),y.push(e);y.length>0&&f.headers.set("x-middleware-override-headers",m+","+y.join(","))}return{response:f,waitUntil:Promise.all(d[W]),fetchMetrics:u.fetchMetrics}}Object.getOwnPropertyDescriptor,Object.getOwnPropertyNames,Object.prototype.hasOwnProperty;var eX=e=>{throw TypeError(e)},eQ=(e,t,r)=>t.has(e)||eX("Cannot "+r),eY=(e,t,r)=>(eQ(e,t,"read from private field"),r?r.call(e):t.get(e)),eZ=(e,t,r)=>t.has(e)?eX("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),e0=(e,t,r,n)=>(eQ(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),e1=(e,t,r)=>(eQ(e,t,"access private method"),r);function e2(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function e4(e){return e&&e.sensitive?"":"i"}function e5(e,t,r){var n;return e instanceof RegExp?function(e,t){if(!t)return e;for(var r=/\((?:\?<(.*?)>)?(?!\?)/g,n=0,i=r.exec(e.source);i;)t.push({name:i[1]||n++,prefix:"",suffix:"",modifier:"",pattern:""}),i=r.exec(e.source);return e}(e,t):Array.isArray(e)?(n=e.map(function(e){return e5(e,t,r).source}),new RegExp("(?:".concat(n.join("|"),")"),e4(r))):function(e,t,r){void 0===r&&(r={});for(var n=r.strict,i=void 0!==n&&n,a=r.start,s=r.end,o=r.encode,l=void 0===o?function(e){return e}:o,c=r.delimiter,u=r.endsWith,d="[".concat(e2(void 0===u?"":u),"]|$"),h="[".concat(e2(void 0===c?"/#?":c),"]"),p=void 0===a||a?"^":"",f=0;f<e.length;f++){var g=e[f];if("string"==typeof g)p+=e2(l(g));else{var m=e2(l(g.prefix)),y=e2(l(g.suffix));if(g.pattern){if(t&&t.push(g),m||y){if("+"===g.modifier||"*"===g.modifier){var v="*"===g.modifier?"?":"";p+="(?:".concat(m,"((?:").concat(g.pattern,")(?:").concat(y).concat(m,"(?:").concat(g.pattern,"))*)").concat(y,")").concat(v)}else p+="(?:".concat(m,"(").concat(g.pattern,")").concat(y,")").concat(g.modifier)}else{if("+"===g.modifier||"*"===g.modifier)throw TypeError('Can not repeat "'.concat(g.name,'" without a prefix and suffix'));p+="(".concat(g.pattern,")").concat(g.modifier)}}else p+="(?:".concat(m).concat(y,")").concat(g.modifier)}}if(void 0===s||s)i||(p+="".concat(h,"?")),p+=r.endsWith?"(?=".concat(d,")"):"$";else{var b=e[e.length-1],w="string"==typeof b?h.indexOf(b[b.length-1])>-1:void 0===b;i||(p+="(?:".concat(h,"(?=").concat(d,"))?")),w||(p+="(?=".concat(h,"|").concat(d,")"))}return new RegExp(p,e4(r))}(function(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var i="",a=r+1;a<e.length;){var s=e.charCodeAt(a);if(s>=48&&s<=57||s>=65&&s<=90||s>=97&&s<=122||95===s){i+=e[a++];continue}break}if(!i)throw TypeError("Missing parameter name at ".concat(r));t.push({type:"NAME",index:r,value:i}),r=a;continue}if("("===n){var o=1,l="",a=r+1;if("?"===e[a])throw TypeError('Pattern cannot start with "?" at '.concat(a));for(;a<e.length;){if("\\"===e[a]){l+=e[a++]+e[a++];continue}if(")"===e[a]){if(0==--o){a++;break}}else if("("===e[a]&&(o++,"?"!==e[a+1]))throw TypeError("Capturing groups are not allowed at ".concat(a));l+=e[a++]}if(o)throw TypeError("Unbalanced pattern at ".concat(r));if(!l)throw TypeError("Missing pattern at ".concat(r));t.push({type:"PATTERN",index:r,value:l}),r=a;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,i=void 0===n?"./":n,a=t.delimiter,s=void 0===a?"/#?":a,o=[],l=0,c=0,u="",d=function(e){if(c<r.length&&r[c].type===e)return r[c++].value},h=function(e){var t=d(e);if(void 0!==t)return t;var n=r[c],i=n.type,a=n.index;throw TypeError("Unexpected ".concat(i," at ").concat(a,", expected ").concat(e))},p=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t},f=function(e){for(var t=0;t<s.length;t++){var r=s[t];if(e.indexOf(r)>-1)return!0}return!1},g=function(e){var t=o[o.length-1],r=e||(t&&"string"==typeof t?t:"");if(t&&!r)throw TypeError('Must have text between two parameters, missing text after "'.concat(t.name,'"'));return!r||f(r)?"[^".concat(e2(s),"]+?"):"(?:(?!".concat(e2(r),")[^").concat(e2(s),"])+?")};c<r.length;){var m=d("CHAR"),y=d("NAME"),v=d("PATTERN");if(y||v){var b=m||"";-1===i.indexOf(b)&&(u+=b,b=""),u&&(o.push(u),u=""),o.push({name:y||l++,prefix:b,suffix:"",pattern:v||g(b),modifier:d("MODIFIER")||""});continue}var w=m||d("ESCAPED_CHAR");if(w){u+=w;continue}if(u&&(o.push(u),u=""),d("OPEN")){var b=p(),_=d("NAME")||"",S=d("PATTERN")||"",k=p();h("CLOSE"),o.push({name:_||(S?l++:""),pattern:_&&!S?g(b):S,prefix:b,suffix:k,modifier:d("MODIFIER")||""});continue}h("END")}return o}(e,r),t,r)}var e3=e=>{try{return e5(e)}catch(t){throw Error(`Invalid path: ${e}.
Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x
${t.message}`)}};function e6(e,t){try{var r,n,i,a,s;return r=[],n=e5(e,r,t),i=t,void 0===i&&(i={}),a=i.decode,s=void 0===a?function(e){return e}:a,function(e){var t=n.exec(e);if(!t)return!1;for(var i=t[0],a=t.index,o=Object.create(null),l=1;l<t.length;l++)!function(e){if(void 0!==t[e]){var n=r[e-1];"*"===n.modifier||"+"===n.modifier?o[n.name]=t[e].split(n.prefix+n.suffix).map(function(e){return s(e,n)}):o[n.name]=s(t[e],n)}}(l);return{path:i,index:a,params:o}}}catch(e){throw Error(`Invalid path and options: Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x
${e.message}`)}}let e8=e=>e.map(e=>e instanceof RegExp?e:e3(e));var e9=r(2067),e7={InvalidSecretKey:"clerk_key_invalid"},te={TokenExpired:"token-expired",TokenInvalid:"token-invalid",TokenInvalidAlgorithm:"token-invalid-algorithm",TokenInvalidAuthorizedParties:"token-invalid-authorized-parties",TokenInvalidSignature:"token-invalid-signature",TokenNotActiveYet:"token-not-active-yet",TokenIatInTheFuture:"token-iat-in-the-future",TokenVerificationFailed:"token-verification-failed",InvalidSecretKey:"secret-key-invalid",LocalJWKMissing:"jwk-local-missing",RemoteJWKFailedToLoad:"jwk-remote-failed-to-load",JWKFailedToResolve:"jwk-failed-to-resolve",JWKKidMismatch:"jwk-kid-mismatch"},tt={ContactSupport:"Contact <EMAIL>",EnsureClerkJWT:"Make sure that this is a valid Clerk generate JWT.",SetClerkJWTKey:"Set the CLERK_JWT_KEY environment variable.",SetClerkSecretKey:"Set the CLERK_SECRET_KEY environment variable."},tr=class e extends Error{constructor({action:t,message:r,reason:n}){super(r),Object.setPrototypeOf(this,e.prototype),this.reason=n,this.message=r,this.action=t}getFullMessage(){return`${[this.message,this.action].filter(e=>e).join(" ")} (reason=${this.reason}, token-carrier=${this.tokenCarrier})`}},tn=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e,ti={crypto:crypto,fetch:fetch.bind(globalThis),AbortController:globalThis.AbortController,Blob:globalThis.Blob,FormData:globalThis.FormData,Headers:globalThis.Headers,Request:globalThis.Request,Response:globalThis.Response},ta={parse:(e,t)=>(function(e,t,r={}){if(!t.codes){t.codes={};for(let e=0;e<t.chars.length;++e)t.codes[t.chars[e]]=e}if(!r.loose&&e.length*t.bits&7)throw SyntaxError("Invalid padding");let n=e.length;for(;"="===e[n-1];)if(--n,!r.loose&&!((e.length-n)*t.bits&7))throw SyntaxError("Invalid padding");let i=new(r.out??Uint8Array)(n*t.bits/8|0),a=0,s=0,o=0;for(let r=0;r<n;++r){let n=t.codes[e[r]];if(void 0===n)throw SyntaxError("Invalid character "+e[r]);s=s<<t.bits|n,(a+=t.bits)>=8&&(a-=8,i[o++]=255&s>>a)}if(a>=t.bits||255&s<<8-a)throw SyntaxError("Unexpected end of data");return i})(e,ts,t)},ts={chars:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bits:6},to={RS256:"SHA-256",RS384:"SHA-384",RS512:"SHA-512"},tl="RSASSA-PKCS1-v1_5",tc={RS256:tl,RS384:tl,RS512:tl},tu=Object.keys(to),td=e=>Array.isArray(e)&&e.length>0&&e.every(e=>"string"==typeof e),th=(e,t)=>{let r=[t].flat().filter(e=>!!e),n=[e].flat().filter(e=>!!e);if(r.length>0&&n.length>0){if("string"==typeof e){if(!r.includes(e))throw new tr({action:tt.EnsureClerkJWT,reason:te.TokenVerificationFailed,message:`Invalid JWT audience claim (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}else if(td(e)&&!e.some(e=>r.includes(e)))throw new tr({action:tt.EnsureClerkJWT,reason:te.TokenVerificationFailed,message:`Invalid JWT audience claim array (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}},tp=e=>{if(void 0!==e&&"JWT"!==e)throw new tr({action:tt.EnsureClerkJWT,reason:te.TokenInvalid,message:`Invalid JWT type ${JSON.stringify(e)}. Expected "JWT".`})},tf=e=>{if(!tu.includes(e))throw new tr({action:tt.EnsureClerkJWT,reason:te.TokenInvalidAlgorithm,message:`Invalid JWT algorithm ${JSON.stringify(e)}. Supported: ${tu}.`})},tg=e=>{if("string"!=typeof e)throw new tr({action:tt.EnsureClerkJWT,reason:te.TokenVerificationFailed,message:`Subject claim (sub) is required and must be a string. Received ${JSON.stringify(e)}.`})},tm=(e,t)=>{if(e&&t&&0!==t.length&&!t.includes(e))throw new tr({reason:te.TokenInvalidAuthorizedParties,message:`Invalid JWT Authorized party claim (azp) ${JSON.stringify(e)}. Expected "${t}".`})},ty=(e,t)=>{if("number"!=typeof e)throw new tr({action:tt.EnsureClerkJWT,reason:te.TokenVerificationFailed,message:`Invalid JWT expiry date claim (exp) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),n=new Date(0);if(n.setUTCSeconds(e),n.getTime()<=r.getTime()-t)throw new tr({reason:te.TokenExpired,message:`JWT is expired. Expiry date: ${n.toUTCString()}, Current date: ${r.toUTCString()}.`})},tv=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new tr({action:tt.EnsureClerkJWT,reason:te.TokenVerificationFailed,message:`Invalid JWT not before date claim (nbf) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),n=new Date(0);if(n.setUTCSeconds(e),n.getTime()>r.getTime()+t)throw new tr({reason:te.TokenNotActiveYet,message:`JWT cannot be used prior to not before date claim (nbf). Not before date: ${n.toUTCString()}; Current date: ${r.toUTCString()};`})},tb=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new tr({action:tt.EnsureClerkJWT,reason:te.TokenVerificationFailed,message:`Invalid JWT issued at date claim (iat) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),n=new Date(0);if(n.setUTCSeconds(e),n.getTime()>r.getTime()+t)throw new tr({reason:te.TokenIatInTheFuture,message:`JWT issued at date claim (iat) is in the future. Issued at date: ${n.toUTCString()}; Current date: ${r.toUTCString()};`})};async function tw(e,t){let{header:r,signature:n,raw:i}=e,a=new TextEncoder().encode([i.header,i.payload].join(".")),s=function(e){let t=to[e],r=tc[e];if(!t||!r)throw Error(`Unsupported algorithm ${e}, expected one of ${tu.join(",")}.`);return{hash:{name:to[e]},name:tc[e]}}(r.alg);try{let e=await function(e,t,r){if("object"==typeof e)return ti.crypto.subtle.importKey("jwk",e,t,!1,[r]);let n=function(e){let t=tn(e.replace(/-----BEGIN.*?-----/g,"").replace(/-----END.*?-----/g,"").replace(/\s/g,"")),r=new Uint8Array(new ArrayBuffer(t.length));for(let e=0,n=t.length;e<n;e++)r[e]=t.charCodeAt(e);return r}(e),i="sign"===r?"pkcs8":"spki";return ti.crypto.subtle.importKey(i,n,t,!1,[r])}(t,s,"verify");return{data:await ti.crypto.subtle.verify(s.name,e,n,a)}}catch(e){return{errors:[new tr({reason:te.TokenInvalidSignature,message:e?.message})]}}}function t_(e){let t=(e||"").toString().split(".");if(3!==t.length)return{errors:[new tr({reason:te.TokenInvalid,message:"Invalid JWT form. A JWT consists of three parts separated by dots."})]};let[r,n,i]=t,a=new TextDecoder,s=JSON.parse(a.decode(ta.parse(r,{loose:!0})));return{data:{header:s,payload:JSON.parse(a.decode(ta.parse(n,{loose:!0}))),signature:ta.parse(i,{loose:!0}),raw:{header:r,payload:n,signature:i,text:e}}}}async function tS(e,t){let{audience:r,authorizedParties:n,clockSkewInMs:i,key:a}=t,s=i||5e3,{data:o,errors:l}=t_(e);if(l)return{errors:l};let{header:c,payload:u}=o;try{let{typ:e,alg:t}=c;tp(e),tf(t);let{azp:i,sub:a,aud:o,iat:l,exp:d,nbf:h}=u;tg(a),th([o],[r]),tm(i,n),ty(d,s),tv(h,s),tb(l,s)}catch(e){return{errors:[e]}}let{data:d,errors:h}=await tw(o,a);return h?{errors:[new tr({action:tt.EnsureClerkJWT,reason:te.TokenVerificationFailed,message:`Error verifying JWT signature. ${h[0]}`})]}:d?{data:u}:{errors:[new tr({reason:te.TokenInvalidSignature,message:"JWT signature is invalid."})]}}function tk(e){var t,r,n,i,a;return{code:e.code,message:e.message,longMessage:e.long_message,meta:{paramName:null==(t=null==e?void 0:e.meta)?void 0:t.param_name,sessionId:null==(r=null==e?void 0:e.meta)?void 0:r.session_id,emailAddresses:null==(n=null==e?void 0:e.meta)?void 0:n.email_addresses,identifiers:null==(i=null==e?void 0:e.meta)?void 0:i.identifiers,zxcvbn:null==(a=null==e?void 0:e.meta)?void 0:a.zxcvbn}}}var tx=class e extends Error{constructor(t,{data:r,status:n,clerkTraceId:i}){super(t),this.toString=()=>{let e=`[${this.name}]
Message:${this.message}
Status:${this.status}
Serialized errors: ${this.errors.map(e=>JSON.stringify(e))}`;return this.clerkTraceId&&(e+=`
Clerk Trace ID: ${this.clerkTraceId}`),e},Object.setPrototypeOf(this,e.prototype),this.status=n,this.message=t,this.clerkTraceId=i,this.clerkError=!0,this.errors=function(e=[]){return e.length>0?e.map(tk):[]}(r)}},tT=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function tE({packageName:e,customMessages:t}){let r=e,n={...tT,...t};function i(e,t){if(!t)return`${r}: ${e}`;let n=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();n=n.replace(`{{${r[1]}}}`,e)}return`${r}: ${n}`}return{setPackageName({packageName:e}){return"string"==typeof e&&(r=e),this},setMessages({customMessages:e}){return Object.assign(n,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(i(n.InvalidPublishableKeyErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(i(n.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(i(n.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw Error(i(n.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(e){throw Error(i(n.MissingClerkProvider,e))},throw(e){throw Error(i(e))}}}var tC=r(4264);async function tO(e,t=1,r=5){try{return await e()}catch(i){var n;if(t>=r)throw i;return await (n=2**t*100,new Promise(e=>setTimeout(e,n))),tO(e,t+1,r)}}var tP=e=>"undefined"!=typeof btoa&&"function"==typeof btoa?btoa(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e).toString("base64"):e,tI=[".lcl.dev",".lclstage.dev",".lclclerk.com"],tR=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],tN=[".lcl.dev","lclstage.dev",".lclclerk.com",".accounts.lclclerk.com"],tA=[".accountsstage.dev"],tM="https://api.clerk.com",tU="pk_live_";function tL(e,t={}){if(!(e=e||"")||!function(e){let t=(e=e||"").startsWith(tU)||e.startsWith("pk_test_"),r=tn(e.split("_")[2]||"").endsWith("$");return t&&r}(e)){if(t.fatal)throw Error("Publishable key not valid.");return null}let r=e.startsWith(tU)?"production":"development",n=tn(e.split("_")[2]);return n=n.slice(0,-1),t.proxyUrl?n=t.proxyUrl:"development"!==r&&t.domain&&(n=`clerk.${t.domain}`),{instanceType:r,frontendApi:n}}function tD(e){return e.startsWith("test_")||e.startsWith("sk_test_")}async function tj(e,t=globalThis.crypto.subtle){let r=new TextEncoder().encode(e);return tP(String.fromCharCode(...new Uint8Array(await t.digest("sha-1",r)))).replace(/\+/gi,"-").replace(/\//gi,"_").substring(0,8)}var tq=(e,t)=>`${e}_${t}`,tB=()=>!1,tH=()=>{try{return!0}catch(e){}return!1},tz=new Set,t$=(e,t,r)=>{let n=tB()||tH(),i=null!=r?r:e;tz.has(i)||n||(tz.add(i),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))},tK={veryStrict:{afterMinutes:10,level:"multiFactor"},strict:{afterMinutes:10,level:"secondFactor"},moderate:{afterMinutes:60,level:"secondFactor"},lax:{afterMinutes:1440,level:"secondFactor"}},tF=new Set(["firstFactor","secondFactor","multiFactor"]),tV=new Set(["veryStrict","strict","moderate","lax"]),tJ=e=>"number"==typeof e&&e>0,tW=e=>tF.has(e),tG=e=>tV.has(e),tX=(e,t)=>{let{orgId:r,orgRole:n,orgPermissions:i}=t;return(e.role||e.permission)&&r&&n&&i?e.permission?i.includes(e.permission):e.role?n===e.role:null:null},tQ=e=>!!("string"==typeof e&&tG(e)||"object"==typeof e&&tW(e.level)&&tJ(e.afterMinutes))&&(e=>"string"==typeof e?tK[e]:e).bind(null,e),tY=(e,{__experimental_factorVerificationAge:t})=>{if(!e.__experimental_reverification||!t)return null;let r=tQ(e.__experimental_reverification);if(!r)return null;let{level:n,afterMinutes:i}=r(),[a,s]=t,o=-1!==a?i>a:null,l=-1!==s?i>s:null;switch(n){case"firstFactor":return o;case"secondFactor":return -1!==s?l:o;case"multiFactor":return -1===s?o:o&&l}},tZ=e=>t=>{if(!e.userId)return!1;let r=tX(t,e),n=tY(t,e);return[r,n].some(e=>null===e)?[r,n].some(e=>!0===e):[r,n].every(e=>!0===e)},t0=r(1250),t1="https://api.clerk.com",t2={Session:"__session",Refresh:"__refresh",ClientUat:"__client_uat",Handshake:"__clerk_handshake",DevBrowser:"__clerk_db_jwt",RedirectCount:"__clerk_redirect_count"},t4={ClerkSynced:"__clerk_synced",ClerkRedirectUrl:"__clerk_redirect_url",DevBrowser:t2.DevBrowser,Handshake:t2.Handshake,HandshakeHelp:"__clerk_help",LegacyDevBrowser:"__dev_session",HandshakeReason:"__clerk_hs_reason"},t5={Cookies:t2,Headers:{AuthToken:"x-clerk-auth-token",AuthSignature:"x-clerk-auth-signature",AuthStatus:"x-clerk-auth-status",AuthReason:"x-clerk-auth-reason",AuthMessage:"x-clerk-auth-message",ClerkUrl:"x-clerk-clerk-url",EnableDebug:"x-clerk-debug",ClerkRequestData:"x-clerk-request-data",ClerkRedirectTo:"x-clerk-redirect-to",CloudFrontForwardedProto:"cloudfront-forwarded-proto",Authorization:"authorization",ForwardedPort:"x-forwarded-port",ForwardedProto:"x-forwarded-proto",ForwardedHost:"x-forwarded-host",Accept:"accept",Referrer:"referer",UserAgent:"user-agent",Origin:"origin",Host:"host",ContentType:"content-type",SecFetchDest:"sec-fetch-dest",Location:"location",CacheControl:"cache-control"},ContentTypes:{Json:"application/json"},QueryParameters:t4},t3=class{constructor(e){this.request=e}requireId(e){if(!e)throw Error("A valid resource ID is required.")}},t6=RegExp("(?<!:)/{1,}","g");function t8(...e){return e.filter(e=>e).join("/").replace(t6,"/")}var t9="/allowlist_identifiers",t7=class extends t3{async getAllowlistIdentifierList(){return this.request({method:"GET",path:t9,queryParams:{paginated:!0}})}async createAllowlistIdentifier(e){return this.request({method:"POST",path:t9,bodyParams:e})}async deleteAllowlistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:t8(t9,e)})}},re="/clients",rt=class extends t3{async getClientList(e={}){return this.request({method:"GET",path:re,queryParams:{...e,paginated:!0}})}async getClient(e){return this.requireId(e),this.request({method:"GET",path:t8(re,e)})}verifyClient(e){return this.request({method:"POST",path:t8(re,"verify"),bodyParams:{token:e}})}},rr=class extends t3{async deleteDomain(e){return this.request({method:"DELETE",path:t8("/domains",e)})}},rn="/email_addresses",ri=class extends t3{async getEmailAddress(e){return this.requireId(e),this.request({method:"GET",path:t8(rn,e)})}async createEmailAddress(e){return this.request({method:"POST",path:rn,bodyParams:e})}async updateEmailAddress(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:t8(rn,e),bodyParams:t})}async deleteEmailAddress(e){return this.requireId(e),this.request({method:"DELETE",path:t8(rn,e)})}},ra="/invitations",rs=class extends t3{async getInvitationList(e={}){return this.request({method:"GET",path:ra,queryParams:{...e,paginated:!0}})}async createInvitation(e){return this.request({method:"POST",path:ra,bodyParams:e})}async revokeInvitation(e){return this.requireId(e),this.request({method:"POST",path:t8(ra,e,"revoke")})}},ro="/organizations",rl=class extends t3{async getOrganizationList(e){return this.request({method:"GET",path:ro,queryParams:e})}async createOrganization(e){return this.request({method:"POST",path:ro,bodyParams:e})}async getOrganization(e){let{includeMembersCount:t}=e,r="organizationId"in e?e.organizationId:e.slug;return this.requireId(r),this.request({method:"GET",path:t8(ro,r),queryParams:{includeMembersCount:t}})}async updateOrganization(e,t){return this.requireId(e),this.request({method:"PATCH",path:t8(ro,e),bodyParams:t})}async updateOrganizationLogo(e,t){this.requireId(e);let r=new ti.FormData;return r.append("file",t?.file),t?.uploaderUserId&&r.append("uploader_user_id",t?.uploaderUserId),this.request({method:"PUT",path:t8(ro,e,"logo"),formData:r})}async deleteOrganizationLogo(e){return this.requireId(e),this.request({method:"DELETE",path:t8(ro,e,"logo")})}async updateOrganizationMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:t8(ro,e,"metadata"),bodyParams:t})}async deleteOrganization(e){return this.request({method:"DELETE",path:t8(ro,e)})}async getOrganizationMembershipList(e){let{organizationId:t,limit:r,offset:n}=e;return this.requireId(t),this.request({method:"GET",path:t8(ro,t,"memberships"),queryParams:{limit:r,offset:n}})}async createOrganizationMembership(e){let{organizationId:t,userId:r,role:n}=e;return this.requireId(t),this.request({method:"POST",path:t8(ro,t,"memberships"),bodyParams:{userId:r,role:n}})}async updateOrganizationMembership(e){let{organizationId:t,userId:r,role:n}=e;return this.requireId(t),this.request({method:"PATCH",path:t8(ro,t,"memberships",r),bodyParams:{role:n}})}async updateOrganizationMembershipMetadata(e){let{organizationId:t,userId:r,publicMetadata:n,privateMetadata:i}=e;return this.request({method:"PATCH",path:t8(ro,t,"memberships",r,"metadata"),bodyParams:{publicMetadata:n,privateMetadata:i}})}async deleteOrganizationMembership(e){let{organizationId:t,userId:r}=e;return this.requireId(t),this.request({method:"DELETE",path:t8(ro,t,"memberships",r)})}async getOrganizationInvitationList(e){let{organizationId:t,status:r,limit:n,offset:i}=e;return this.requireId(t),this.request({method:"GET",path:t8(ro,t,"invitations"),queryParams:{status:r,limit:n,offset:i}})}async createOrganizationInvitation(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:t8(ro,t,"invitations"),bodyParams:{...r}})}async getOrganizationInvitation(e){let{organizationId:t,invitationId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"GET",path:t8(ro,t,"invitations",r)})}async revokeOrganizationInvitation(e){let{organizationId:t,invitationId:r,requestingUserId:n}=e;return this.requireId(t),this.request({method:"POST",path:t8(ro,t,"invitations",r,"revoke"),bodyParams:{requestingUserId:n}})}async getOrganizationDomainList(e){let{organizationId:t,limit:r,offset:n}=e;return this.requireId(t),this.request({method:"GET",path:t8(ro,t,"domains"),queryParams:{limit:r,offset:n}})}async createOrganizationDomain(e){let{organizationId:t,name:r,enrollmentMode:n,verified:i=!0}=e;return this.requireId(t),this.request({method:"POST",path:t8(ro,t,"domains"),bodyParams:{name:r,enrollmentMode:n,verified:i}})}async updateOrganizationDomain(e){let{organizationId:t,domainId:r,...n}=e;return this.requireId(t),this.requireId(r),this.request({method:"PATCH",path:t8(ro,t,"domains",r),bodyParams:n})}async deleteOrganizationDomain(e){let{organizationId:t,domainId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"DELETE",path:t8(ro,t,"domains",r)})}},rc="/phone_numbers",ru=class extends t3{async getPhoneNumber(e){return this.requireId(e),this.request({method:"GET",path:t8(rc,e)})}async createPhoneNumber(e){return this.request({method:"POST",path:rc,bodyParams:e})}async updatePhoneNumber(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:t8(rc,e),bodyParams:t})}async deletePhoneNumber(e){return this.requireId(e),this.request({method:"DELETE",path:t8(rc,e)})}},rd="/redirect_urls",rh=class extends t3{async getRedirectUrlList(){return this.request({method:"GET",path:rd,queryParams:{paginated:!0}})}async getRedirectUrl(e){return this.requireId(e),this.request({method:"GET",path:t8(rd,e)})}async createRedirectUrl(e){return this.request({method:"POST",path:rd,bodyParams:e})}async deleteRedirectUrl(e){return this.requireId(e),this.request({method:"DELETE",path:t8(rd,e)})}},rp="/sessions",rf=class extends t3{async getSessionList(e={}){return this.request({method:"GET",path:rp,queryParams:{...e,paginated:!0}})}async getSession(e){return this.requireId(e),this.request({method:"GET",path:t8(rp,e)})}async revokeSession(e){return this.requireId(e),this.request({method:"POST",path:t8(rp,e,"revoke")})}async verifySession(e,t){return this.requireId(e),this.request({method:"POST",path:t8(rp,e,"verify"),bodyParams:{token:t}})}async getToken(e,t){return this.requireId(e),this.request({method:"POST",path:t8(rp,e,"tokens",t||"")})}async refreshSession(e,t){return this.requireId(e),this.request({method:"POST",path:t8(rp,e,"refresh"),bodyParams:t})}},rg="/sign_in_tokens",rm=class extends t3{async createSignInToken(e){return this.request({method:"POST",path:rg,bodyParams:e})}async revokeSignInToken(e){return this.requireId(e),this.request({method:"POST",path:t8(rg,e,"revoke")})}},ry="/users",rv=class extends t3{async getUserList(e={}){let{limit:t,offset:r,orderBy:n,...i}=e,[a,s]=await Promise.all([this.request({method:"GET",path:ry,queryParams:e}),this.getCount(i)]);return{data:a,totalCount:s}}async getUser(e){return this.requireId(e),this.request({method:"GET",path:t8(ry,e)})}async createUser(e){return this.request({method:"POST",path:ry,bodyParams:e})}async updateUser(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:t8(ry,e),bodyParams:t})}async updateUserProfileImage(e,t){this.requireId(e);let r=new ti.FormData;return r.append("file",t?.file),this.request({method:"POST",path:t8(ry,e,"profile_image"),formData:r})}async updateUserMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:t8(ry,e,"metadata"),bodyParams:t})}async deleteUser(e){return this.requireId(e),this.request({method:"DELETE",path:t8(ry,e)})}async getCount(e={}){return this.request({method:"GET",path:t8(ry,"count"),queryParams:e})}async getUserOauthAccessToken(e,t){return this.requireId(e),this.request({method:"GET",path:t8(ry,e,"oauth_access_tokens",t),queryParams:{paginated:!0}})}async disableUserMFA(e){return this.requireId(e),this.request({method:"DELETE",path:t8(ry,e,"mfa")})}async getOrganizationMembershipList(e){let{userId:t,limit:r,offset:n}=e;return this.requireId(t),this.request({method:"GET",path:t8(ry,t,"organization_memberships"),queryParams:{limit:r,offset:n}})}async verifyPassword(e){let{userId:t,password:r}=e;return this.requireId(t),this.request({method:"POST",path:t8(ry,t,"verify_password"),bodyParams:{password:r}})}async verifyTOTP(e){let{userId:t,code:r}=e;return this.requireId(t),this.request({method:"POST",path:t8(ry,t,"verify_totp"),bodyParams:{code:r}})}async banUser(e){return this.requireId(e),this.request({method:"POST",path:t8(ry,e,"ban")})}async unbanUser(e){return this.requireId(e),this.request({method:"POST",path:t8(ry,e,"unban")})}async lockUser(e){return this.requireId(e),this.request({method:"POST",path:t8(ry,e,"lock")})}async unlockUser(e){return this.requireId(e),this.request({method:"POST",path:t8(ry,e,"unlock")})}async deleteUserProfileImage(e){return this.requireId(e),this.request({method:"DELETE",path:t8(ry,e,"profile_image")})}},rb="/saml_connections",rw=class extends t3{async getSamlConnectionList(e={}){return this.request({method:"GET",path:rb,queryParams:e})}async createSamlConnection(e){return this.request({method:"POST",path:rb,bodyParams:e})}async getSamlConnection(e){return this.requireId(e),this.request({method:"GET",path:t8(rb,e)})}async updateSamlConnection(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:t8(rb,e),bodyParams:t})}async deleteSamlConnection(e){return this.requireId(e),this.request({method:"DELETE",path:t8(rb,e)})}},r_=class extends t3{async createTestingToken(){return this.request({method:"POST",path:"/testing_tokens"})}},rS=tE({packageName:"@clerk/backend"}),{isDevOrStagingUrl:rk}=function(){let e=new Map;return{isDevOrStagingUrl:t=>{if(!t)return!1;let r="string"==typeof t?t:t.hostname,n=e.get(r);return void 0===n&&(n=tR.some(e=>r.endsWith(e)),e.set(r,n)),n}}}();function rx(e){if(!e||"string"!=typeof e)throw Error("Missing Clerk Secret Key. Go to https://dashboard.clerk.com and get your key for your instance.")}var rT=class e{constructor(e,t,r,n,i){this.id=e,this.identifier=t,this.createdAt=r,this.updatedAt=n,this.invitationId=i}static fromJSON(t){return new e(t.id,t.identifier,t.created_at,t.updated_at,t.invitation_id)}},rE=class e{constructor(e,t,r,n,i,a,s,o,l){this.id=e,this.clientId=t,this.userId=r,this.status=n,this.lastActiveAt=i,this.expireAt=a,this.abandonAt=s,this.createdAt=o,this.updatedAt=l}static fromJSON(t){return new e(t.id,t.client_id,t.user_id,t.status,t.last_active_at,t.expire_at,t.abandon_at,t.created_at,t.updated_at)}},rC=class e{constructor(e,t,r,n,i,a,s,o){this.id=e,this.sessionIds=t,this.sessions=r,this.signInId=n,this.signUpId=i,this.lastActiveSessionId=a,this.createdAt=s,this.updatedAt=o}static fromJSON(t){return new e(t.id,t.session_ids,t.sessions.map(e=>rE.fromJSON(e)),t.sign_in_id,t.sign_up_id,t.last_active_session_id,t.created_at,t.updated_at)}},rO=class e{constructor(e,t,r,n){this.object=e,this.id=t,this.slug=r,this.deleted=n}static fromJSON(t){return new e(t.object,t.id||null,t.slug||null,t.deleted)}},rP=class e{constructor(e,t,r,n,i,a,s,o,l,c,u){this.id=e,this.fromEmailName=t,this.emailAddressId=r,this.toEmailAddress=n,this.subject=i,this.body=a,this.bodyPlain=s,this.status=o,this.slug=l,this.data=c,this.deliveredByClerk=u}static fromJSON(t){return new e(t.id,t.from_email_name,t.email_address_id,t.to_email_address,t.subject,t.body,t.body_plain,t.status,t.slug,t.data,t.delivered_by_clerk)}},rI=class e{constructor(e,t){this.id=e,this.type=t}static fromJSON(t){return new e(t.id,t.type)}},rR=class e{constructor(e,t,r=null,n=null,i=null,a=null,s=null){this.status=e,this.strategy=t,this.externalVerificationRedirectURL=r,this.attempts=n,this.expireAt=i,this.nonce=a,this.message=s}static fromJSON(t){return new e(t.status,t.strategy,t.external_verification_redirect_url?new URL(t.external_verification_redirect_url):null,t.attempts,t.expire_at,t.nonce)}},rN=class e{constructor(e,t,r,n){this.id=e,this.emailAddress=t,this.verification=r,this.linkedTo=n}static fromJSON(t){return new e(t.id,t.email_address,t.verification&&rR.fromJSON(t.verification),t.linked_to.map(e=>rI.fromJSON(e)))}},rA=class e{constructor(e,t,r,n,i,a,s,o,l,c,u={},d,h){this.id=e,this.provider=t,this.identificationId=r,this.externalId=n,this.approvedScopes=i,this.emailAddress=a,this.firstName=s,this.lastName=o,this.imageUrl=l,this.username=c,this.publicMetadata=u,this.label=d,this.verification=h}static fromJSON(t){return new e(t.id,t.provider,t.identification_id,t.provider_user_id,t.approved_scopes,t.email_address,t.first_name,t.last_name,t.image_url||"",t.username,t.public_metadata,t.label,t.verification&&rR.fromJSON(t.verification))}},rM=class e{constructor(e,t,r,n,i,a,s,o){this.id=e,this.emailAddress=t,this.publicMetadata=r,this.createdAt=n,this.updatedAt=i,this.status=a,this.url=s,this.revoked=o}static fromJSON(t){return new e(t.id,t.email_address,t.public_metadata,t.created_at,t.updated_at,t.status,t.url,t.revoked)}},rU={AllowlistIdentifier:"allowlist_identifier",Client:"client",Email:"email",EmailAddress:"email_address",Invitation:"invitation",OauthAccessToken:"oauth_access_token",Organization:"organization",OrganizationInvitation:"organization_invitation",OrganizationMembership:"organization_membership",PhoneNumber:"phone_number",RedirectUrl:"redirect_url",Session:"session",SignInToken:"sign_in_token",SmsMessage:"sms_message",User:"user",Token:"token",TotalCount:"total_count"},rL=class e{constructor(e,t,r,n={},i,a,s){this.externalAccountId=e,this.provider=t,this.token=r,this.publicMetadata=n,this.label=i,this.scopes=a,this.tokenSecret=s}static fromJSON(t){return new e(t.external_account_id,t.provider,t.token,t.public_metadata,t.label||"",t.scopes,t.token_secret)}},rD=class e{constructor(e,t,r,n,i,a,s,o,l={},c={},u,d,h){this.id=e,this.name=t,this.slug=r,this.imageUrl=n,this.hasImage=i,this.createdBy=a,this.createdAt=s,this.updatedAt=o,this.publicMetadata=l,this.privateMetadata=c,this.maxAllowedMemberships=u,this.adminDeleteEnabled=d,this.membersCount=h}static fromJSON(t){return new e(t.id,t.name,t.slug,t.image_url||"",t.has_image,t.created_by,t.created_at,t.updated_at,t.public_metadata,t.private_metadata,t.max_allowed_memberships,t.admin_delete_enabled,t.members_count)}},rj=class e{constructor(e,t,r,n,i,a,s,o={},l={}){this.id=e,this.emailAddress=t,this.role=r,this.organizationId=n,this.createdAt=i,this.updatedAt=a,this.status=s,this.publicMetadata=o,this.privateMetadata=l}static fromJSON(t){return new e(t.id,t.email_address,t.role,t.organization_id,t.created_at,t.updated_at,t.status,t.public_metadata,t.private_metadata)}},rq=class e{constructor(e,t,r,n={},i={},a,s,o,l){this.id=e,this.role=t,this.permissions=r,this.publicMetadata=n,this.privateMetadata=i,this.createdAt=a,this.updatedAt=s,this.organization=o,this.publicUserData=l}static fromJSON(t){return new e(t.id,t.role,t.permissions,t.public_metadata,t.private_metadata,t.created_at,t.updated_at,rD.fromJSON(t.organization),rB.fromJSON(t.public_user_data))}},rB=class e{constructor(e,t,r,n,i,a){this.identifier=e,this.firstName=t,this.lastName=r,this.imageUrl=n,this.hasImage=i,this.userId=a}static fromJSON(t){return new e(t.identifier,t.first_name,t.last_name,t.image_url,t.has_image,t.user_id)}},rH=class e{constructor(e,t,r,n,i,a){this.id=e,this.phoneNumber=t,this.reservedForSecondFactor=r,this.defaultSecondFactor=n,this.verification=i,this.linkedTo=a}static fromJSON(t){return new e(t.id,t.phone_number,t.reserved_for_second_factor,t.default_second_factor,t.verification&&rR.fromJSON(t.verification),t.linked_to.map(e=>rI.fromJSON(e)))}},rz=class e{constructor(e,t,r,n){this.id=e,this.url=t,this.createdAt=r,this.updatedAt=n}static fromJSON(t){return new e(t.id,t.url,t.created_at,t.updated_at)}},r$=class e{constructor(e,t,r,n,i,a,s){this.id=e,this.userId=t,this.token=r,this.status=n,this.url=i,this.createdAt=a,this.updatedAt=s}static fromJSON(t){return new e(t.id,t.user_id,t.token,t.status,t.url,t.created_at,t.updated_at)}},rK=class e{constructor(e,t,r,n,i,a,s){this.id=e,this.fromPhoneNumber=t,this.toPhoneNumber=r,this.message=n,this.status=i,this.phoneNumberId=a,this.data=s}static fromJSON(t){return new e(t.id,t.from_phone_number,t.to_phone_number,t.message,t.status,t.phone_number_id,t.data)}},rF=class e{constructor(e){this.jwt=e}static fromJSON(t){return new e(t.jwt)}},rV=class e{constructor(e,t,r,n,i,a,s,o,l,c){this.id=e,this.name=t,this.domain=r,this.active=n,this.provider=i,this.syncUserAttributes=a,this.allowSubdomains=s,this.allowIdpInitiated=o,this.createdAt=l,this.updatedAt=c}static fromJSON(t){return new e(t.id,t.name,t.domain,t.active,t.provider,t.sync_user_attributes,t.allow_subdomains,t.allow_idp_initiated,t.created_at,t.updated_at)}},rJ=class e{constructor(e,t,r,n,i,a,s,o,l){this.id=e,this.provider=t,this.providerUserId=r,this.active=n,this.emailAddress=i,this.firstName=a,this.lastName=s,this.verification=o,this.samlConnection=l}static fromJSON(t){return new e(t.id,t.provider,t.provider_user_id,t.active,t.email_address,t.first_name,t.last_name,t.verification&&rR.fromJSON(t.verification),t.saml_connection&&rV.fromJSON(t.saml_connection))}},rW=class e{constructor(e,t,r){this.id=e,this.web3Wallet=t,this.verification=r}static fromJSON(t){return new e(t.id,t.web3_wallet,t.verification&&rR.fromJSON(t.verification))}},rG=class e{constructor(e,t,r,n,i,a,s,o,l,c,u,d,h,p,f,g,m,y,v,b={},w={},_={},S=[],k=[],x=[],T=[],E=[],C,O,P=null,I){this.id=e,this.passwordEnabled=t,this.totpEnabled=r,this.backupCodeEnabled=n,this.twoFactorEnabled=i,this.banned=a,this.locked=s,this.createdAt=o,this.updatedAt=l,this.imageUrl=c,this.hasImage=u,this.primaryEmailAddressId=d,this.primaryPhoneNumberId=h,this.primaryWeb3WalletId=p,this.lastSignInAt=f,this.externalId=g,this.username=m,this.firstName=y,this.lastName=v,this.publicMetadata=b,this.privateMetadata=w,this.unsafeMetadata=_,this.emailAddresses=S,this.phoneNumbers=k,this.web3Wallets=x,this.externalAccounts=T,this.samlAccounts=E,this.lastActiveAt=C,this.createOrganizationEnabled=O,this.createOrganizationsLimit=P,this.deleteSelfEnabled=I}static fromJSON(t){return new e(t.id,t.password_enabled,t.totp_enabled,t.backup_code_enabled,t.two_factor_enabled,t.banned,t.locked,t.created_at,t.updated_at,t.image_url,t.has_image,t.primary_email_address_id,t.primary_phone_number_id,t.primary_web3_wallet_id,t.last_sign_in_at,t.external_id,t.username,t.first_name,t.last_name,t.public_metadata,t.private_metadata,t.unsafe_metadata,(t.email_addresses||[]).map(e=>rN.fromJSON(e)),(t.phone_numbers||[]).map(e=>rH.fromJSON(e)),(t.web3_wallets||[]).map(e=>rW.fromJSON(e)),(t.external_accounts||[]).map(e=>rA.fromJSON(e)),(t.saml_accounts||[]).map(e=>rJ.fromJSON(e)),t.last_active_at,t.create_organization_enabled,t.create_organizations_limit,t.delete_self_enabled)}get primaryEmailAddress(){return this.emailAddresses.find(({id:e})=>e===this.primaryEmailAddressId)??null}get primaryPhoneNumber(){return this.phoneNumbers.find(({id:e})=>e===this.primaryPhoneNumberId)??null}get primaryWeb3Wallet(){return this.web3Wallets.find(({id:e})=>e===this.primaryWeb3WalletId)??null}get fullName(){return[this.firstName,this.lastName].join(" ").trim()||null}};function rX(e){if("string"!=typeof e&&"object"in e&&"deleted"in e)return rO.fromJSON(e);switch(e.object){case rU.AllowlistIdentifier:return rT.fromJSON(e);case rU.Client:return rC.fromJSON(e);case rU.EmailAddress:return rN.fromJSON(e);case rU.Email:return rP.fromJSON(e);case rU.Invitation:return rM.fromJSON(e);case rU.OauthAccessToken:return rL.fromJSON(e);case rU.Organization:return rD.fromJSON(e);case rU.OrganizationInvitation:return rj.fromJSON(e);case rU.OrganizationMembership:return rq.fromJSON(e);case rU.PhoneNumber:return rH.fromJSON(e);case rU.RedirectUrl:return rz.fromJSON(e);case rU.SignInToken:return r$.fromJSON(e);case rU.Session:return rE.fromJSON(e);case rU.SmsMessage:return rK.fromJSON(e);case rU.Token:return rF.fromJSON(e);case rU.TotalCount:return e.total_count;case rU.User:return rG.fromJSON(e);default:return e}}function rQ(e,t){return e&&"object"==typeof e&&"clerk_trace_id"in e&&"string"==typeof e.clerk_trace_id?e.clerk_trace_id:t?.get("cf-ray")||""}function rY(e){if(e&&"object"==typeof e&&"errors"in e){let t=e.errors;return t.length>0?t.map(tk):[]}return[]}function rZ(e){var t;let r=(t=async t=>{let r;let{secretKey:n,apiUrl:i=t1,apiVersion:a="v1",userAgent:s="@clerk/backend@1.14.1"}=e,{path:o,method:l,queryParams:c,headerParams:u,bodyParams:d,formData:h}=t;rx(n);let p=new URL(t8(i,a,o));if(c)for(let[e,t]of Object.entries(tC({...c})))t&&[t].flat().forEach(t=>p.searchParams.append(e,t));let f={Authorization:`Bearer ${n}`,"User-Agent":s,...u};try{if(h)r=await ti.fetch(p.href,{method:l,headers:f,body:h});else{f["Content-Type"]="application/json";let e="GET"!==l&&d&&Object.keys(d).length>0?{body:JSON.stringify(tC(d,{deep:!1}))}:null;r=await ti.fetch(p.href,{method:l,headers:f,...e})}let e=r?.headers&&r.headers?.get(t5.Headers.ContentType)===t5.ContentTypes.Json,t=await (e?r.json():r.text());if(!r.ok)return{data:null,errors:rY(t),status:r?.status,statusText:r?.statusText,clerkTraceId:rQ(t,r?.headers)};return{...Array.isArray(t)?{data:t.map(e=>rX(e))}:t&&"object"==typeof t&&"data"in t&&Array.isArray(t.data)&&void 0!==t.data?{data:t.data.map(e=>rX(e)),totalCount:t.total_count}:{data:rX(t)},errors:null}}catch(e){if(e instanceof Error)return{data:null,errors:[{code:"unexpected_error",message:e.message||"Unexpected error"}],clerkTraceId:rQ(e,r?.headers)};return{data:null,errors:rY(e),status:r?.status,statusText:r?.statusText,clerkTraceId:rQ(e,r?.headers)}}},async(...e)=>{let{data:r,errors:n,totalCount:i,status:a,statusText:s,clerkTraceId:o}=await t(...e);if(n){let e=new tx(s||"",{data:[],status:a,clerkTraceId:o});throw e.errors=n,e}return void 0!==i?{data:r,totalCount:i}:r});return{allowlistIdentifiers:new t7(r),clients:new rt(r),emailAddresses:new ri(r),invitations:new rs(r),organizations:new rl(r),phoneNumbers:new ru(r),redirectUrls:new rh(r),sessions:new rf(r),signInTokens:new rm(r),users:new rv(r),domains:new rr(r),samlConnections:new rw(r),testingTokens:new r_(r)}}var r0=e=>()=>{let t={...e};return t.secretKey=(t.secretKey||"").substring(0,7),t.jwtKey=(t.jwtKey||"").substring(0,7),{...t}},r1=e=>{let{fetcher:t,sessionToken:r,sessionId:n}=e||{};return async(e={})=>n?e.template?t(n,e.template):r:null},r2={SignedIn:"signed-in",SignedOut:"signed-out",Handshake:"handshake"},r4={ClientUATWithoutSessionToken:"client-uat-but-no-session-token",DevBrowserMissing:"dev-browser-missing",DevBrowserSync:"dev-browser-sync",PrimaryRespondsToSyncing:"primary-responds-to-syncing",SatelliteCookieNeedsSyncing:"satellite-needs-syncing",SessionTokenAndUATMissing:"session-token-and-uat-missing",SessionTokenMissing:"session-token-missing",SessionTokenExpired:"session-token-expired",SessionTokenIATBeforeClientUAT:"session-token-iat-before-client-uat",SessionTokenNBF:"session-token-nbf",SessionTokenIatInTheFuture:"session-token-iat-in-the-future",SessionTokenWithoutClientUAT:"session-token-but-no-client-uat",ActiveOrganizationMismatch:"active-organization-mismatch",UnexpectedError:"unexpected-error"};function r5(e,t,r=new Headers,n){let i=function(e,t,r){let{act:n,sid:i,org_id:a,org_role:s,org_slug:o,org_permissions:l,sub:c,fva:u}=r,d=rZ(e),h=r1({sessionId:i,sessionToken:t,fetcher:async(...e)=>(await d.sessions.getToken(...e)).jwt}),p=u??null;return{actor:n,sessionClaims:r,sessionId:i,userId:c,orgId:a,orgRole:s,orgSlug:o,orgPermissions:l,__experimental_factorVerificationAge:p,getToken:h,has:tZ({orgId:a,orgRole:s,orgPermissions:l,userId:c,__experimental_factorVerificationAge:p}),debug:r0({...e,sessionToken:t})}}(e,n,t);return{status:r2.SignedIn,reason:null,message:null,proxyUrl:e.proxyUrl||"",publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!0,toAuth:()=>i,headers:r,token:n}}function r3(e,t,r="",n=new Headers){return r6({status:r2.SignedOut,reason:t,message:r,proxyUrl:e.proxyUrl||"",publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!1,headers:n,toAuth:()=>({sessionClaims:null,sessionId:null,userId:null,actor:null,orgId:null,orgRole:null,orgSlug:null,orgPermissions:null,__experimental_factorVerificationAge:null,getToken:()=>Promise.resolve(null),has:()=>!1,debug:r0({...e,status:r2.SignedOut,reason:t,message:r})}),token:null})}var r6=e=>{let t=new Headers(e.headers||{});if(e.message)try{t.set(t5.Headers.AuthMessage,e.message)}catch(e){}if(e.reason)try{t.set(t5.Headers.AuthReason,e.reason)}catch(e){}if(e.status)try{t.set(t5.Headers.AuthStatus,e.status)}catch(e){}return e.headers=t,e},r8=class extends URL{isCrossOrigin(e){return this.origin!==new URL(e.toString()).origin}},r9=(...e)=>new r8(...e),r7=class extends Request{constructor(e,t){super("string"!=typeof e&&"url"in e?e.url:String(e),t||"string"==typeof e?void 0:e),this.clerkUrl=this.deriveUrlFromHeaders(this),this.cookies=this.parseCookies(this)}toJSON(){return{url:this.clerkUrl.href,method:this.method,headers:JSON.stringify(Object.fromEntries(this.headers)),clerkUrl:this.clerkUrl.toString(),cookies:JSON.stringify(Object.fromEntries(this.cookies))}}deriveUrlFromHeaders(e){let t=new URL(e.url),r=e.headers.get(t5.Headers.ForwardedProto),n=e.headers.get(t5.Headers.ForwardedHost),i=e.headers.get(t5.Headers.Host),a=t.protocol,s=this.getFirstValueFromHeader(n)??i,o=this.getFirstValueFromHeader(r)??a?.replace(/[:/]/,""),l=s&&o?`${o}://${s}`:t.origin;return l===t.origin?r9(t):r9(t.pathname+t.search,l)}getFirstValueFromHeader(e){return e?.split(",")[0]}parseCookies(e){return new Map(Object.entries((0,t0.Q)(this.decodeCookieValue(e.headers.get("cookie")||""))))}decodeCookieValue(e){return e?e.replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent):e}},ne=(...e)=>e[0]instanceof r7?e[0]:new r7(...e),nt={},nr=0;function nn(e,t=!0){nt[e.kid]=e,nr=t?Date.now():-1}var ni="local";function na(e){if(!nt[ni]){if(!e)throw new tr({action:tt.SetClerkJWTKey,message:"Missing local JWK.",reason:te.LocalJWKMissing});nn({kid:"local",kty:"RSA",alg:"RS256",n:e.replace(/(\r\n|\n|\r)/gm,"").replace("-----BEGIN PUBLIC KEY-----","").replace("-----END PUBLIC KEY-----","").replace("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA","").replace("IDAQAB","").replace(/\+/g,"-").replace(/\//g,"_"),e:"AQAB"},!1)}return nt[ni]}async function ns({secretKey:e,apiUrl:t=t1,apiVersion:r="v1",kid:n,skipJwksCache:i}){if(i||function(){if(-1===nr)return!1;let e=Date.now()-nr>=3e5;return e&&(nt={}),e}()||!nt[n]){if(!e)throw new tr({action:tt.ContactSupport,message:"Failed to load JWKS from Clerk Backend or Frontend API.",reason:te.RemoteJWKFailedToLoad});let{keys:n}=await tO(()=>no(t,e,r));if(!n||!n.length)throw new tr({action:tt.ContactSupport,message:"The JWKS endpoint did not contain any signing keys. Contact <EMAIL>.",reason:te.RemoteJWKFailedToLoad});n.forEach(e=>nn(e))}let a=nt[n];if(!a){let e=Object.values(nt).map(e=>e.kid).sort().join(", ");throw new tr({action:`Go to your Dashboard and validate your secret and public keys are correct. ${tt.ContactSupport} if the issue persists.`,message:`Unable to find a signing key in JWKS that matches the kid='${n}' of the provided session token. Please make sure that the __session cookie or the HTTP authorization header contain a Clerk-generated session JWT. The following kid is available: ${e}`,reason:te.JWKKidMismatch})}return a}async function no(e,t,r){if(!t)throw new tr({action:tt.SetClerkSecretKey,message:"Missing Clerk Secret Key or API Key. Go to https://dashboard.clerk.com and get your key for your instance.",reason:te.RemoteJWKFailedToLoad});let n=new URL(e);n.pathname=t8(n.pathname,r,"/jwks");let i=await ti.fetch(n.href,{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}});if(!i.ok){let e=await i.json(),t=nl(e?.errors,e7.InvalidSecretKey);if(t){let e=te.InvalidSecretKey;throw new tr({action:tt.ContactSupport,message:t.message,reason:e})}throw new tr({action:tt.ContactSupport,message:`Error loading Clerk JWKS from ${n.href} with code=${i.status}`,reason:te.RemoteJWKFailedToLoad})}return i.json()}var nl=(e,t)=>e?e.find(e=>e.code===t):null;async function nc(e,t){let{data:r,errors:n}=t_(e);if(n)return{errors:n};let{header:i}=r,{kid:a}=i;try{let r;if(t.jwtKey)r=na(t.jwtKey);else{if(!t.secretKey)return{errors:[new tr({action:tt.SetClerkJWTKey,message:"Failed to resolve JWK during verification.",reason:te.JWKFailedToResolve})]};r=await ns({...t,kid:a})}return await tS(e,{...t,key:r})}catch(e){return{errors:[e]}}}var nu=class{constructor(e,t,r){this.cookieSuffix=e,this.clerkRequest=t,this.initPublishableKeyValues(r),this.initHeaderValues(),this.initCookieValues(),this.initHandshakeValues(),Object.assign(this,r),this.clerkUrl=this.clerkRequest.clerkUrl}get sessionToken(){return this.sessionTokenInCookie||this.sessionTokenInHeader}initPublishableKeyValues(e){tL(e.publishableKey,{fatal:!0}),this.publishableKey=e.publishableKey;let t=tL(this.publishableKey,{fatal:!0,proxyUrl:e.proxyUrl,domain:e.domain});this.instanceType=t.instanceType,this.frontendApi=t.frontendApi}initHeaderValues(){this.sessionTokenInHeader=this.stripAuthorizationHeader(this.getHeader(t5.Headers.Authorization)),this.origin=this.getHeader(t5.Headers.Origin),this.host=this.getHeader(t5.Headers.Host),this.forwardedHost=this.getHeader(t5.Headers.ForwardedHost),this.forwardedProto=this.getHeader(t5.Headers.CloudFrontForwardedProto)||this.getHeader(t5.Headers.ForwardedProto),this.referrer=this.getHeader(t5.Headers.Referrer),this.userAgent=this.getHeader(t5.Headers.UserAgent),this.secFetchDest=this.getHeader(t5.Headers.SecFetchDest),this.accept=this.getHeader(t5.Headers.Accept)}initCookieValues(){this.suffixedCookies=this.shouldUseSuffixed(),this.sessionTokenInCookie=this.getSuffixedOrUnSuffixedCookie(t5.Cookies.Session),this.refreshTokenInCookie=this.getSuffixedCookie(t5.Cookies.Refresh),this.clientUat=Number.parseInt(this.getSuffixedOrUnSuffixedCookie(t5.Cookies.ClientUat)||"")||0}initHandshakeValues(){this.devBrowserToken=this.getQueryParam(t5.QueryParameters.DevBrowser)||this.getSuffixedOrUnSuffixedCookie(t5.Cookies.DevBrowser),this.handshakeToken=this.getQueryParam(t5.QueryParameters.Handshake)||this.getCookie(t5.Cookies.Handshake),this.handshakeRedirectLoopCounter=Number(this.getCookie(t5.Cookies.RedirectCount))||0}stripAuthorizationHeader(e){return e?.replace("Bearer ","")}getQueryParam(e){return this.clerkRequest.clerkUrl.searchParams.get(e)}getHeader(e){return this.clerkRequest.headers.get(e)||void 0}getCookie(e){return this.clerkRequest.cookies.get(e)||void 0}getSuffixedCookie(e){return this.getCookie(tq(e,this.cookieSuffix))||void 0}getSuffixedOrUnSuffixedCookie(e){return this.suffixedCookies?this.getSuffixedCookie(e):this.getCookie(e)}shouldUseSuffixed(){let e=this.getSuffixedCookie(t5.Cookies.ClientUat),t=this.getCookie(t5.Cookies.ClientUat),r=this.getSuffixedCookie(t5.Cookies.Session)||"",n=this.getCookie(t5.Cookies.Session)||"";if(n&&!this.tokenHasIssuer(n))return!1;if(n&&!this.tokenBelongsToInstance(n))return!0;if(!e&&!r)return!1;let{data:i}=t_(n),a=i?.payload.iat||0,{data:s}=t_(r),o=s?.payload.iat||0;if("0"!==e&&"0"!==t&&a>o||"0"===e&&"0"!==t)return!1;if("production"!==this.instanceType){let r=this.sessionExpired(s);if("0"!==e&&"0"===t&&r)return!1}return!!e||!r}tokenHasIssuer(e){let{data:t,errors:r}=t_(e);return!r&&!!t.payload.iss}tokenBelongsToInstance(e){if(!e)return!1;let{data:t,errors:r}=t_(e);if(r)return!1;let n=t.payload.iss.replace(/https?:\/\//gi,"");return this.frontendApi===n}sessionExpired(e){return!!e&&e?.payload.exp<=Date.now()/1e3>>0}},nd=async(e,t)=>new nu(t.publishableKey?await tj(t.publishableKey,ti.crypto.subtle):"",e,t),nh=e=>e.split(";")[0]?.split("=")[0],np=e=>e.split(";")[0]?.split("=")[1];async function nf(e,{key:t}){let{data:r,errors:n}=t_(e);if(n)throw n[0];let{header:i,payload:a}=r,{typ:s,alg:o}=i;tp(s),tf(o);let{data:l,errors:c}=await tw(r,t);if(c)throw new tr({reason:te.TokenVerificationFailed,message:`Error verifying handshake token. ${c[0]}`});if(!l)throw new tr({reason:te.TokenInvalidSignature,message:"Handshake signature is invalid."});return a}async function ng(e,t){let r;let{secretKey:n,apiUrl:i,apiVersion:a,jwksCacheTtlInMs:s,jwtKey:o,skipJwksCache:l}=t,{data:c,errors:u}=t_(e);if(u)throw u[0];let{kid:d}=c.header;if(o)r=na(o);else if(n)r=await ns({secretKey:n,apiUrl:i,apiVersion:a,kid:d,jwksCacheTtlInMs:s,skipJwksCache:l});else throw new tr({action:tt.SetClerkJWTKey,message:"Failed to resolve JWK during handshake verification.",reason:te.JWKFailedToResolve});return await nf(e,{key:r})}var nm={NonEligibleNoCookie:"non-eligible-no-refresh-cookie",NonEligibleNonGet:"non-eligible-non-get",InvalidSessionToken:"invalid-session-token",MissingApiClient:"missing-api-client",MissingSessionToken:"missing-session-token",MissingRefreshToken:"missing-refresh-token",ExpiredSessionTokenDecodeFailed:"expired-session-token-decode-failed",ExpiredSessionTokenMissingSidClaim:"expired-session-token-missing-sid-claim",FetchError:"fetch-error",UnexpectedSDKError:"unexpected-sdk-error"};async function ny(e,t){let r=await nd(ne(e),t);rx(r.secretKey),r.isSatellite&&(function(e,t){if(!e&&tD(t))throw Error("Missing signInUrl. Pass a signInUrl for dev instances if an app is satellite")}(r.signInUrl,r.secretKey),r.signInUrl&&r.origin&&function(e,t){let r;try{r=new URL(e)}catch{throw Error("The signInUrl needs to have a absolute url format.")}if(r.origin===t)throw Error("The signInUrl needs to be on a different origin than your satellite application.")}(r.signInUrl,r.origin),function(e){if(!e)throw Error("Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl")}(r.proxyUrl||r.domain));let n=function(e){let t=null;if(e?.personalAccountPatterns)try{t=e6(e.personalAccountPatterns)}catch(t){throw Error(`Invalid personal account pattern "${e.personalAccountPatterns}": "${t}"`)}let r=null;if(e?.organizationPatterns)try{r=e6(e.organizationPatterns)}catch(t){throw Error(`Clerk: Invalid organization pattern "${e.organizationPatterns}": "${t}"`)}return{OrganizationMatcher:r,PersonalAccountMatcher:t}}(t.organizationSyncOptions);async function i(){let e=new Headers({"Access-Control-Allow-Origin":"null","Access-Control-Allow-Credentials":"true"}),t=(await ng(r.handshakeToken,r)).handshake,n="";if(t.forEach(t=>{e.append("Set-Cookie",t),nh(t).startsWith(t5.Cookies.Session)&&(n=np(t))}),"development"===r.instanceType){let t=new URL(r.clerkUrl);t.searchParams.delete(t5.QueryParameters.Handshake),t.searchParams.delete(t5.QueryParameters.HandshakeHelp),e.append(t5.Headers.Location,t.toString()),e.set(t5.Headers.CacheControl,"no-store")}if(""===n)return r3(r,r4.SessionTokenMissing,"",e);let{data:i,errors:[a]=[]}=await nc(n,r);if(i)return r5(r,i,e,n);if("development"===r.instanceType&&(a?.reason===te.TokenExpired||a?.reason===te.TokenNotActiveYet||a?.reason===te.TokenIatInTheFuture)){a.tokenCarrier="cookie",console.error(`Clerk: Clock skew detected. This usually means that your system clock is inaccurate. Clerk will attempt to account for the clock skew in development.

To resolve this issue, make sure your system's clock is set to the correct time (e.g. turn off and on automatic time synchronization).

---

${a.getFullMessage()}`);let{data:t,errors:[i]=[]}=await nc(n,{...r,clockSkewInMs:864e5});if(t)return r5(r,t,e,n);throw i}throw a}async function a(r){if(!t.apiClient)return{data:null,error:{message:"An apiClient is needed to perform token refresh.",cause:{reason:nm.MissingApiClient}}};let{sessionToken:n,refreshTokenInCookie:i}=r;if(!n)return{data:null,error:{message:"Session token must be provided.",cause:{reason:nm.MissingSessionToken}}};if(!i)return{data:null,error:{message:"Refresh token must be provided.",cause:{reason:nm.MissingRefreshToken}}};let{data:a,errors:s}=t_(n);if(!a||s)return{data:null,error:{message:"Unable to decode the expired session token.",cause:{reason:nm.ExpiredSessionTokenDecodeFailed,errors:s}}};if(!a?.payload?.sid)return{data:null,error:{message:"Expired session token is missing the `sid` claim.",cause:{reason:nm.ExpiredSessionTokenMissingSidClaim}}};try{return{data:(await t.apiClient.sessions.refreshSession(a.payload.sid,{expired_token:n||"",refresh_token:i||"",request_origin:r.clerkUrl.origin,request_headers:Object.fromEntries(Array.from(e.headers.entries()).map(([e,t])=>[e,[t]]))})).jwt,error:null}}catch(e){if(!e?.errors?.length)return{data:null,error:e};if("unexpected_error"===e.errors[0].code)return{data:null,error:{message:"Fetch unexpected error",cause:{reason:nm.FetchError,errors:e.errors}}};return{data:null,error:{message:e.errors[0].code,cause:{reason:e.errors[0].code,errors:e.errors}}}}}async function s(e){let{data:t,error:r}=await a(e);if(!t)return{data:null,error:r};let{data:n,errors:i}=await nc(t,e);return i?{data:null,error:{message:"Clerk: unable to verify refreshed session token.",cause:{reason:nm.InvalidSessionToken,errors:i}}}:{data:{jwtPayload:n,sessionToken:t},error:null}}function o(e,i,a,s){if(function(e){let{accept:t,secFetchDest:r}=e;return!!("document"===r||"iframe"===r||!r&&t?.startsWith("text/html"))}(e)){let o=s??function({handshakeReason:e}){let i=function(e){let t=new URL(e);return t.searchParams.delete(t5.QueryParameters.DevBrowser),t.searchParams.delete(t5.QueryParameters.LegacyDevBrowser),t}(r.clerkUrl),a=r.frontendApi.replace(/http(s)?:\/\//,""),s=new URL(`https://${a}/v1/client/handshake`);s.searchParams.append("redirect_url",i?.href||""),s.searchParams.append("suffixed_cookies",r.suffixedCookies.toString()),s.searchParams.append(t5.QueryParameters.HandshakeReason,e),"development"===r.instanceType&&r.devBrowserToken&&s.searchParams.append(t5.QueryParameters.DevBrowser,r.devBrowserToken);let o=nb(r.clerkUrl,t.organizationSyncOptions,n);return o&&(function(e){let t=new Map;return"personalAccount"===e.type&&t.set("organization_id",""),"organization"===e.type&&(e.organizationId&&t.set("organization_id",e.organizationId),e.organizationSlug&&t.set("organization_id",e.organizationSlug)),t})(o).forEach((e,t)=>{s.searchParams.append(t,e)}),new Headers({[t5.Headers.Location]:s.href})}({handshakeReason:i});return(o.get(t5.Headers.Location)&&o.set(t5.Headers.CacheControl,"no-store"),function(e){if(3===r.handshakeRedirectLoopCounter)return!0;let t=r.handshakeRedirectLoopCounter+1,n=t5.Cookies.RedirectCount;return e.append("Set-Cookie",`${n}=${t}; SameSite=Lax; HttpOnly; Max-Age=3`),!1}(o))?(console.log("Clerk: Refreshing the session token resulted in an infinite redirect loop. This usually means that your Clerk instance keys do not match - make sure to copy the correct publishable and secret keys from the Clerk dashboard."),r3(e,i,a)):function(e,t,r="",n){return r6({status:r2.Handshake,reason:t,message:r,publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",proxyUrl:e.proxyUrl||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!1,headers:n,toAuth:()=>null,token:null})}(e,i,a,o)}return r3(e,i,a)}async function l(){let{sessionTokenInHeader:e}=r;try{let{data:t,errors:n}=await nc(e,r);if(n)throw n[0];return r5(r,t,void 0,e)}catch(e){return u(e,"header")}}async function c(){let e=r.clientUat,a=!!r.sessionTokenInCookie,s=!!r.devBrowserToken,l=r.isSatellite&&"document"===r.secFetchDest&&!r.clerkUrl.searchParams.has(t5.QueryParameters.ClerkSynced);if(r.handshakeToken)try{return await i()}catch(e){e instanceof tr&&"development"===r.instanceType?function(e){if(e.reason===te.TokenInvalidSignature)throw Error("Clerk: Handshake token verification failed due to an invalid signature. If you have switched Clerk keys locally, clear your cookies and try again.");throw Error(`Clerk: Handshake token verification failed: ${e.getFullMessage()}.`)}(e):console.error("Clerk: unable to resolve handshake:",e)}if("development"===r.instanceType&&r.clerkUrl.searchParams.has(t5.QueryParameters.DevBrowser))return o(r,r4.DevBrowserSync,"");if("production"===r.instanceType&&l)return o(r,r4.SatelliteCookieNeedsSyncing,"");if("development"===r.instanceType&&l){let e=new URL(r.signInUrl);e.searchParams.append(t5.QueryParameters.ClerkRedirectUrl,r.clerkUrl.toString());let t=r4.SatelliteCookieNeedsSyncing;return e.searchParams.append(t5.QueryParameters.HandshakeReason,t),o(r,t,"",new Headers({[t5.Headers.Location]:e.toString()}))}let c=new URL(r.clerkUrl).searchParams.get(t5.QueryParameters.ClerkRedirectUrl);if("development"===r.instanceType&&!r.isSatellite&&c){let e=new URL(c);r.devBrowserToken&&e.searchParams.append(t5.QueryParameters.DevBrowser,r.devBrowserToken),e.searchParams.append(t5.QueryParameters.ClerkSynced,"true");let t=r4.PrimaryRespondsToSyncing;return e.searchParams.append(t5.QueryParameters.HandshakeReason,t),o(r,t,"",new Headers({[t5.Headers.Location]:e.toString()}))}if("development"===r.instanceType&&!s)return o(r,r4.DevBrowserMissing,"");if(!e&&!a)return r3(r,r4.SessionTokenAndUATMissing,"");if(!e&&a)return o(r,r4.SessionTokenWithoutClientUAT,"");if(e&&!a)return o(r,r4.ClientUATWithoutSessionToken,"");let{data:d,errors:h}=t_(r.sessionTokenInCookie);if(h)return u(h[0],"cookie");if(d.payload.iat<r.clientUat)return o(r,r4.SessionTokenIATBeforeClientUAT,"");try{let{data:e,errors:i}=await nc(r.sessionTokenInCookie,r);if(i)throw i[0];let a=r5(r,e,void 0,r.sessionTokenInCookie),s=function(e,r){let i=nb(e.clerkUrl,t.organizationSyncOptions,n);if(!i)return null;let a=!1;if("organization"===i.type&&(i.organizationSlug&&i.organizationSlug!==r.orgSlug&&(a=!0),i.organizationId&&i.organizationId!==r.orgId&&(a=!0)),"personalAccount"===i.type&&r.orgId&&(a=!0),!a)return null;if(e.handshakeRedirectLoopCounter>0)return console.warn("Clerk: Organization activation handshake loop detected. This is likely due to an invalid organization ID or slug. Skipping organization activation."),null;let s=o(e,r4.ActiveOrganizationMismatch,"");return"handshake"!==s.status?null:s}(r,a.toAuth());if(s)return s;return a}catch(e){return u(e,"cookie")}return r3(r,r4.UnexpectedError)}async function u(t,n){let i;if(!(t instanceof tr))return r3(r,r4.UnexpectedError);if(t.reason===te.TokenExpired&&r.refreshTokenInCookie&&"GET"===e.method){let{data:e,error:t}=await s(r);if(e)return r5(r,e.jwtPayload,void 0,e.sessionToken);i=t?.cause?.reason?t.cause.reason:nm.UnexpectedSDKError}else i="GET"!==e.method?nm.NonEligibleNonGet:r.refreshTokenInCookie?null:nm.NonEligibleNoCookie;return(t.tokenCarrier=n,[te.TokenExpired,te.TokenNotActiveYet,te.TokenIatInTheFuture].includes(t.reason))?o(r,nw({tokenError:t.reason,refreshError:i}),t.getFullMessage()):r3(r,t.reason,t.getFullMessage())}return r.sessionTokenInHeader?l():c()}var nv=e=>{let{isSignedIn:t,proxyUrl:r,reason:n,message:i,publishableKey:a,isSatellite:s,domain:o}=e;return{isSignedIn:t,proxyUrl:r,reason:n,message:i,publishableKey:a,isSatellite:s,domain:o}};function nb(e,t,r){if(!t)return null;if(r.OrganizationMatcher){let n;try{n=r.OrganizationMatcher(e.pathname)}catch(e){return console.error(`Clerk: Failed to apply organization pattern "${t.organizationPatterns}" to a path`,e),null}if(n&&"params"in n){let e=n.params;if("id"in e&&"string"==typeof e.id)return{type:"organization",organizationId:e.id};if("slug"in e&&"string"==typeof e.slug)return{type:"organization",organizationSlug:e.slug};console.warn("Clerk: Detected an organization pattern match, but no organization ID or slug was found in the URL. Does the pattern include `:id` or `:slug`?")}}if(r.PersonalAccountMatcher){let n;try{n=r.PersonalAccountMatcher(e.pathname)}catch(e){return console.error(`Failed to apply personal account pattern "${t.personalAccountPatterns}" to a path`,e),null}if(n)return{type:"personalAccount"}}return null}var nw=({tokenError:e,refreshError:t})=>{switch(e){case te.TokenExpired:return`${r4.SessionTokenExpired}-refresh-${t}`;case te.TokenNotActiveYet:return r4.SessionTokenNBF;case te.TokenIatInTheFuture:return r4.SessionTokenIatInTheFuture;default:return r4.UnexpectedError}};function n_(e,t){return Object.keys(e).reduce((e,r)=>({...e,[r]:t[r]||e[r]}),{...e})}var nS={secretKey:"",jwtKey:"",apiUrl:void 0,apiVersion:void 0,proxyUrl:"",publishableKey:"",isSatellite:!1,domain:"",audience:""},nk=(e,t,r,n)=>{if(""===e)return nx(t.toString(),r?.toString());let i=new URL(e),a=r?new URL(r,i):void 0,s=new URL(t,i);return a&&s.searchParams.set("redirect_url",a.toString()),n&&i.hostname!==s.hostname&&s.searchParams.set(t5.QueryParameters.DevBrowser,n),s.toString()},nx=(e,t)=>{let r;if(e.startsWith("http"))r=new URL(e);else{if(!t||!t.startsWith("http"))throw Error("destination url or return back url should be an absolute path url!");let n=new URL(t);r=new URL(e,n.origin)}return t&&r.searchParams.set("redirect_url",t),r.toString()},nT=e=>{if(!e)return"";let t=e.replace(/(clerk\.accountsstage\.)/,"accountsstage.").replace(/(clerk\.accounts\.|clerk\.)/,"accounts.");return`https://${t}`},nE=e=>{let{publishableKey:t,redirectAdapter:r,signInUrl:n,signUpUrl:i,baseUrl:a}=e,s=tL(t),o=s?.frontendApi,l=s?.instanceType==="development",c=nT(o);return{redirectToSignUp:({returnBackUrl:t}={})=>{i||c||rS.throwMissingPublishableKeyError();let n=`${c}/sign-up`;return r(nk(a,i||n,t,l?e.devBrowserToken:null))},redirectToSignIn:({returnBackUrl:t}={})=>{n||c||rS.throwMissingPublishableKeyError();let i=`${c}/sign-in`;return r(nk(a,n||i,t,l?e.devBrowserToken:null))}}},nC=e=>{let t=r=>{if(!r)return r;if(Array.isArray(r))return r.map(e=>"object"==typeof e||Array.isArray(e)?t(e):e);let n={...r};for(let r of Object.keys(n)){let i=e(r.toString());i!==r&&(n[i]=n[r],delete n[r]),"object"==typeof n[i]&&(n[i]=t(n[i]))}return n};return t};function nO(e){if("boolean"==typeof e)return e;if(null==e)return!1;if("string"==typeof e){if("true"===e.toLowerCase())return!0;if("false"===e.toLowerCase())return!1}let t=parseInt(e,10);return!isNaN(t)&&t>0}nC(function(e){return e?e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`):""}),nC(function(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,"")):""});var nP=class{constructor(){eZ(this,v),eZ(this,m,"clerk_telemetry_throttler"),eZ(this,y,864e5)}isEventThrottled(e){var t;if(!eY(this,v,_))return!1;let r=Date.now(),n=e1(this,v,b).call(this,e),i=null==(t=eY(this,v,w))?void 0:t[n];if(!i){let e={...eY(this,v,w),[n]:r};localStorage.setItem(eY(this,m),JSON.stringify(e))}if(i&&r-i>eY(this,y)){let e=eY(this,v,w);delete e[n],localStorage.setItem(eY(this,m),JSON.stringify(e))}return!!i}};m=new WeakMap,y=new WeakMap,v=new WeakSet,b=function(e){let{sk:t,pk:r,payload:n,...i}=e,a={...n,...i};return JSON.stringify(Object.keys({...n,...i}).sort().map(e=>a[e]))},w=function(){let e=localStorage.getItem(eY(this,m));return e?JSON.parse(e):{}},_=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem(eY(this,m)),!1}};var nI={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},nR=class{constructor(e){var t,r,n,i,a,s;eZ(this,C),eZ(this,S),eZ(this,k),eZ(this,x,{}),eZ(this,T,[]),eZ(this,E),e0(this,S,{maxBufferSize:null!=(t=e.maxBufferSize)?t:nI.maxBufferSize,samplingRate:null!=(r=e.samplingRate)?r:nI.samplingRate,disabled:null!=(n=e.disabled)&&n,debug:null!=(i=e.debug)&&i,endpoint:nI.endpoint}),e.clerkVersion||"undefined"!=typeof window?eY(this,x).clerkVersion=null!=(a=e.clerkVersion)?a:"":eY(this,x).clerkVersion="",eY(this,x).sdk=e.sdk,eY(this,x).sdkVersion=e.sdkVersion,eY(this,x).publishableKey=null!=(s=e.publishableKey)?s:"";let o=tL(e.publishableKey);o&&(eY(this,x).instanceType=o.instanceType),e.secretKey&&(eY(this,x).secretKey=e.secretKey.substring(0,16)),e0(this,k,new nP)}get isEnabled(){var e;return!("development"!==eY(this,x).instanceType||eY(this,S).disabled||"undefined"!=typeof process&&nO(process.env.CLERK_TELEMETRY_DISABLED))&&("undefined"==typeof window||null==(e=null==window?void 0:window.navigator)||!e.webdriver)}get isDebug(){return eY(this,S).debug||"undefined"!=typeof process&&nO(process.env.CLERK_TELEMETRY_DEBUG)}record(e){let t=e1(this,C,M).call(this,e.event,e.payload);e1(this,C,N).call(this,t.event,t),e1(this,C,O).call(this,t,e.eventSamplingRate)&&(eY(this,T).push(t),e1(this,C,I).call(this))}};S=new WeakMap,k=new WeakMap,x=new WeakMap,T=new WeakMap,E=new WeakMap,C=new WeakSet,O=function(e,t){return this.isEnabled&&!this.isDebug&&e1(this,C,P).call(this,e,t)},P=function(e,t){let r=Math.random();return!eY(this,k).isEventThrottled(e)&&r<=eY(this,S).samplingRate&&(void 0===t||r<=t)},I=function(){if("undefined"==typeof window){e1(this,C,R).call(this);return}if(eY(this,T).length>=eY(this,S).maxBufferSize){eY(this,E)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)(eY(this,E)),e1(this,C,R).call(this);return}eY(this,E)||("requestIdleCallback"in window?e0(this,E,requestIdleCallback(()=>{e1(this,C,R).call(this)})):e0(this,E,setTimeout(()=>{e1(this,C,R).call(this)},0)))},R=function(){fetch(new URL("/v1/event",eY(this,S).endpoint),{method:"POST",body:JSON.stringify({events:eY(this,T)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{e0(this,T,[])}).catch(()=>void 0)},N=function(e,t){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",e),console.log(t),console.groupEnd()):console.log("[clerk/telemetry]",e,t))},A=function(){let e={name:eY(this,x).sdk,version:eY(this,x).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e},M=function(e,t){var r,n;let i=e1(this,C,A).call(this);return{event:e,cv:null!=(r=eY(this,x).clerkVersion)?r:"",it:null!=(n=eY(this,x).instanceType)?n:"",sdk:i.name,sdkv:i.version,...eY(this,x).publishableKey?{pk:eY(this,x).publishableKey}:{},...eY(this,x).secretKey?{sk:eY(this,x).secretKey}:{},payload:t}},r(340),"undefined"==typeof URLPattern||URLPattern;let nN={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},nA=e=>e.headers.get(nN.Headers.NextRedirect),nM=(e,t,r)=>(e.headers.set(t,r),e);var nU="__clerk_db_jwt";let nL=(e,t,r)=>{let n=t.headers.get("location");if("true"===t.headers.get(t5.Headers.ClerkRedirectTo)&&n&&tD(r.secretKey)&&e.clerkUrl.isCrossOrigin(n)){let r=e.cookies.get(nU)||"",i=function(e,t){let r=new URL(e),n=r.searchParams.get(nU);r.searchParams.delete(nU);let i=n||t;return i&&r.searchParams.set(nU,i),r}(new URL(n),r);return ef.redirect(i.href,t)}return t},nD={i8:"14.2.24"},nj=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch(e){return""}},nq=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?nj(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,nj(t)])),null,2)).join(", "),nB=(e,t)=>()=>{let r=[],n=!1;return{enable:()=>{n=!0},debug:(...e)=>{n&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(n){for(let n of(console.log(`[clerk debug start: ${e}]`),r)){let e=t(n);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,n=new TextDecoder("utf-8"),i=r.encode(e).slice(0,4096);return n.decode(i).replace(/\uFFFD/g,"")}(e,0)),console.log(e)}console.log(`[clerk debug end: ${e}] (@clerk/nextjs=5.7.5,next=${nD.i8})`)}}}},nH=(e,t)=>(...r)=>{let n=("string"==typeof e?nB(e,nq):e)(),i=t(n);try{let e=i(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(n.commit(),e)).catch(e=>{throw n.commit(),e});return n.commit(),e}catch(e){throw n.commit(),e}};function nz(e){let t={...e},r=rZ(t),n=function(e){let t=n_(nS,e.options),r=e.apiClient;return{authenticateRequest:(e,n={})=>{let{apiUrl:i,apiVersion:a}=t,s=n_(t,n);return ny(e,{...n,...s,apiUrl:i,apiVersion:a,apiClient:r})},debugRequestState:nv}}({options:t,apiClient:r}),i=new nR({...e.telemetry,publishableKey:t.publishableKey,secretKey:t.secretKey,...t.sdkMetadata?{sdk:t.sdkMetadata.name,sdkVersion:t.sdkMetadata.version}:{}});return{...r,...n,telemetry:i}}let n$=e=>{if(!(e instanceof Error)||!("message"in e))return!1;let{message:t}=e,r=t.toLowerCase(),n=r.includes("dynamic server usage"),i=r.includes("this page needs to bail out of prerendering");return/Route .*? needs to bail out of prerendering at this point because it used .*?./.test(t)||n||i},nK=()=>{try{let{headers:e}=r(4075);return new ec("https://placeholder.com",{headers:e()})}catch(e){if(e&&n$(e))throw e;throw Error(`Clerk: auth() and currentUser() are only supported in App Router (/app directory).
If you're using /pages, try getAuth() instead.
Original error: ${e}`)}};process.env.NEXT_PUBLIC_CLERK_JS_VERSION,process.env.NEXT_PUBLIC_CLERK_JS_URL;let nF=process.env.CLERK_API_VERSION||"v1",nV=process.env.CLERK_SECRET_KEY||"",nJ="pk_test_ZGlzY3JldGUtaGFyZS03My5jbGVyay5hY2NvdW50cy5kZXYk",nW=process.env.CLERK_ENCRYPTION_KEY||"",nG=process.env.CLERK_API_URL||(e=>{var t;let r=null==(t=tL(e))?void 0:t.frontendApi;return(null==r?void 0:r.startsWith("clerk."))&&tI.some(e=>null==r?void 0:r.endsWith(e))?tM:tN.some(e=>null==r?void 0:r.endsWith(e))?"https://api.lclclerk.com":tA.some(e=>null==r?void 0:r.endsWith(e))?"https://api.clerkstage.dev":tM})(nJ),nX=process.env.NEXT_PUBLIC_CLERK_DOMAIN||"",nQ=process.env.NEXT_PUBLIC_CLERK_PROXY_URL||"",nY=nO(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE)||!1,nZ="/sign-in",n0=nO(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED),n1=nO(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG);function n2(e,t,r){return"function"==typeof e?e(t):void 0!==e?e:void 0!==r?r:void 0}var n4=new Set,n5={warnOnce:e=>{n4.has(e)||(n4.add(e),console.warn(e))}};function n3(e){return/^http(s)?:\/\//.test(e||"")}var n6=r(2990),n8=r.n(n6),n9=r(6209),n7=r.n(n9),ie=r(6792),it=r.n(ie);let ir=`
Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl.

1) With middleware
   e.g. export default clerkMiddleware({domain:'YOUR_DOMAIN',isSatellite:true}); // or the deprecated authMiddleware()
2) With environment variables e.g.
   NEXT_PUBLIC_CLERK_DOMAIN='YOUR_DOMAIN'
   NEXT_PUBLIC_CLERK_IS_SATELLITE='true'
   `,ii=`
Invalid signInUrl. A satellite application requires a signInUrl for development instances.
Check if signInUrl is missing from your configuration or if it is not an absolute URL

1) With middleware
   e.g. export default clerkMiddleware({signInUrl:'SOME_URL', isSatellite:true}); // or the deprecated authMiddleware()
2) With environment variables e.g.
   NEXT_PUBLIC_CLERK_SIGN_IN_URL='SOME_URL'
   NEXT_PUBLIC_CLERK_IS_SATELLITE='true'`,ia=tE({packageName:"@clerk/nextjs"}),is="x-middleware-override-headers",io="x-middleware-request",il=(e,t,r)=>{e.headers.get(is)||(e.headers.set(is,[...t.headers.keys()]),t.headers.forEach((t,r)=>{e.headers.set(`${io}-${r}`,t)})),Object.entries(r).forEach(([t,r])=>{e.headers.set(is,`${e.headers.get(is)},${t}`),e.headers.set(`${io}-${t}`,r)})},ic=(e,t)=>{let r;let n=n2(null==t?void 0:t.proxyUrl,e.clerkUrl,nQ);r=n&&!n3(n)?new URL(n,e.clerkUrl).toString():n;let i=n2(t.isSatellite,new URL(e.url),nY),a=n2(t.domain,new URL(e.url),nX),s=(null==t?void 0:t.signInUrl)||nZ;if(i&&!r&&!a)throw Error(ir);if(i&&!n3(s)&&tD(t.secretKey||nV))throw Error(ii);return{proxyUrl:r,isSatellite:i,domain:a,signInUrl:s}},iu=e=>ef.redirect(e,{headers:{[t5.Headers.ClerkRedirectTo]:"true"}}),id={secretKey:nV,publishableKey:nJ,apiUrl:nG,apiVersion:nF,userAgent:"@clerk/nextjs@5.7.5",proxyUrl:nQ,domain:nX,isSatellite:nY,sdkMetadata:{name:"@clerk/nextjs",version:"5.7.5",environment:"production"},telemetry:{disabled:n0,debug:n1}},ih=e=>nz({...id,...e}),ip=nz(id),ig=new Proxy(Object.assign(()=>{var e,t;let r;try{let e=nK(),t=function(e,t){var r,n;return!function(e){try{let{headers:t,nextUrl:r,cookies:n}=e||{};return"function"==typeof(null==t?void 0:t.get)&&"function"==typeof(null==r?void 0:r.searchParams.get)&&"function"==typeof(null==n?void 0:n.get)}catch(e){return!1}}(e)?e.headers[t]||e.headers[t.toLowerCase()]||(null==(n=null==(r=e.socket)?void 0:r._httpMessage)?void 0:n.getHeader(t)):e.headers.get(t)}(e,t5.Headers.ClerkRequestData);r=function(e){if(!e)return{};try{let t=n8().decrypt(e,nW||nV).toString(n7());return JSON.parse(t)}catch(e){throw Error("Clerk: Unable to decrypt request data, this usually means the encryption key is invalid. Ensure the encryption key is properly set. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)")}}(t)}catch(e){if(e&&n$(e))throw e}let n=null!=(t=null==(e=ix.getStore())?void 0:e.get("requestData"))?t:r;return(null==n?void 0:n.secretKey)||(null==n?void 0:n.publishableKey)?ih(n):ip},ip),{get:(e,t,r)=>(t$("clerkClient singleton","Use `clerkClient()` as a function instead."),Reflect.get(e,t,r))}),im=e=>{let{redirectToSignIn:t,authObject:r,redirect:n,notFound:i,request:a}=e;return(...e)=>{var s,o,l,c,u,d;let h=(null==(s=e[0])?void 0:s.unauthenticatedUrl)||(null==(o=e[0])?void 0:o.unauthorizedUrl)?void 0:e[0],p=(null==(l=e[0])?void 0:l.unauthenticatedUrl)||(null==(c=e[1])?void 0:c.unauthenticatedUrl),f=(null==(u=e[0])?void 0:u.unauthorizedUrl)||(null==(d=e[1])?void 0:d.unauthorizedUrl),g=()=>f?n(f):i();return r.userId?h?"function"==typeof h?h(r.has)?r:g():r.has(h)?r:g():r:p?n(p):iv(a)?t():i()}},iy=e=>{var t,r;return!!e.headers.get(nN.Headers.NextUrl)&&((null==(t=e.headers.get(t5.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(t5.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(nN.Headers.NextAction))},iv=e=>{var t;return"document"===e.headers.get(t5.Headers.SecFetchDest)||"iframe"===e.headers.get(t5.Headers.SecFetchDest)||(null==(t=e.headers.get(t5.Headers.Accept))?void 0:t.includes("text/html"))||ib(e)||i_(e)},ib=e=>!!e.headers.get(nN.Headers.NextUrl)&&!iy(e)||iw(),iw=()=>{var e;let t=globalThis.fetch;return!!(function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(t)&&(null==(e=t.__nextGetStaticStore().getStore())?void 0:e.pagePath))},i_=e=>!!e.headers.get(nN.Headers.NextjsData),iS={FORCE_NOT_FOUND:"CLERK_PROTECT_REWRITE",REDIRECT_TO_URL:"CLERK_PROTECT_REDIRECT_TO_URL",REDIRECT_TO_SIGN_IN:"CLERK_PROTECT_REDIRECT_TO_SIGN_IN"},ik=new Map,ix=new e9.AsyncLocalStorage,iT=e=>[e[0]instanceof Request?e[0]:void 0,e[0]instanceof Request?e[1]:void 0],iE=e=>["function"==typeof e[0]?e[0]:void 0,(2===e.length?e[1]:"function"==typeof e[0]?{}:e[0])||{}],iC=(e,t)=>({...t,...ic(e,t)}),iO=e=>(t={})=>{let r=Error(iS.REDIRECT_TO_SIGN_IN);throw r.returnBackUrl=null===t.returnBackUrl?"":t.returnBackUrl||e.clerkUrl.toString(),r},iP=(e,t,r)=>(n,i)=>im({request:e,redirect:e=>{let t=Error(iS.REDIRECT_TO_URL);throw t.redirectUrl=e,t},notFound:()=>{throw Error(iS.FORCE_NOT_FOUND)},authObject:t,redirectToSignIn:r})(n,i),iI=(e,t,r)=>{switch(e.message){case iS.FORCE_NOT_FOUND:return nM(ef.rewrite(`${t.clerkUrl.origin}/clerk_${Date.now()}`),t5.Headers.AuthReason,"protect-rewrite");case iS.REDIRECT_TO_URL:return iu(e.redirectUrl);case iS.REDIRECT_TO_SIGN_IN:return nE({redirectAdapter:iu,baseUrl:t.clerkUrl,signInUrl:r.signInUrl,signUpUrl:r.signUpUrl,publishableKey:r.publishableKey}).redirectToSignIn({returnBackUrl:e.returnBackUrl});default:throw e}},iR=(e=>{if("function"==typeof e)return t=>e(t);let t=e8([e||""].flat().filter(Boolean));return e=>t.some(t=>t.test(e.nextUrl.pathname))})(["/","/sign-in(.*)","/sign-up(.*)","/api/create-checkout-session","/api/webhook/:path*","/api/test-email","/test-email","/dashboard","/api/waiting-list","/waiting-list","/blog","/blog(.*)","/sitemap.xml","/processing-page(.*)","/images/:path*","/ads.txt","/doc/:path*","/cv","/ad-demo"]),iN=((...e)=>{let[t,r]=iT(e),[n,i]=iE(e);return ix.run(ik,()=>{let e=nH("clerkMiddleware",e=>async(t,r)=>{let a="function"==typeof i?i(t):i,s=function(e,t){return e||t(),e}(a.publishableKey||nJ,()=>ia.throwMissingPublishableKeyError()),o={publishableKey:s,secretKey:function(e,t){return e||t(),e}(a.secretKey||nV,()=>ia.throwMissingSecretKeyError()),signInUrl:a.signInUrl||nZ,signUpUrl:a.signUpUrl||"/sign-up",...a};ik.set("requestData",o),ig().telemetry.record({event:"METHOD_CALLED",payload:{method:"clerkMiddleware",handler:!!n,satellite:!!o.isSatellite,proxy:!!o.proxyUrl}}),o.debug&&e.enable();let l=ne(t);e.debug("options",o),e.debug("url",()=>l.toJSON());let c=await ig().authenticateRequest(l,iC(l,o));if(e.debug("requestState",()=>({status:c.status,headers:JSON.stringify(Object.fromEntries(c.headers)),reason:c.reason})),c.headers.get(t5.Headers.Location))return new Response(null,{status:307,headers:c.headers});if(c.status===r2.Handshake)throw Error("Clerk: handshake status without redirect");let u=c.toAuth();e.debug("auth",()=>({auth:u,debug:u.debug()}));let d=iO(l),h=iP(l,u,d),p=Object.assign(u,{protect:h,redirectToSignIn:d}),f=ef.next();try{f=await ix.run(ik,async()=>null==n?void 0:n(()=>p,t,r))||f}catch(e){f=iI(e,l,c)}return(c.headers&&c.headers.forEach((e,t)=>{f.headers.append(t,e)}),nA(f))?(e.debug("handlerResult is redirect"),nL(l,f,o)):(o.debug&&il(f,l,{[t5.Headers.EnableDebug]:"true"}),!function(e,t,r,n){var i,a;let s;let{reason:o,message:l,status:c,token:u}=r;if(t||(t=ef.next()),t.headers.get(nN.Headers.NextRedirect))return;"1"===t.headers.get(nN.Headers.NextResume)&&(t.headers.delete(nN.Headers.NextResume),s=new URL(e.url));let d=t.headers.get(nN.Headers.NextRewrite);if(d){let t=new URL(e.url);if((s=new URL(d)).origin!==t.origin)return}if(s){let r=function(e){var t;if(e&&Object.values(e).length){if(e.secretKey&&!nW){n5.warnOnce("Clerk: Missing `CLERK_ENCRYPTION_KEY`. Required for propagating `secretKey` middleware option. See docs: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys");return}return n8().encrypt(JSON.stringify(e),nW||(t=()=>ia.throwMissingSecretKeyError(),nV||t(),nV)).toString()}}(n);il(t,e,{[t5.Headers.AuthStatus]:c,[t5.Headers.AuthToken]:u||"",[t5.Headers.AuthSignature]:u?(a=null!=(i=null==n?void 0:n.secretKey)?i:nV,it()(u,a).toString()):"",[t5.Headers.AuthMessage]:l||"",[t5.Headers.AuthReason]:o||"",[t5.Headers.ClerkUrl]:e.clerkUrl.toString(),...r?{[t5.Headers.ClerkRequestData]:r}:{}}),t.headers.set(nN.Headers.NextRewrite,s.href)}}(l,f,c,a),f)});return t&&r?e(t,r):e})})((e,t)=>{iR(t)||e().protect()}),iA={matcher:["/((?!_next/static|_next/image|favicon.ico|ads.txt|doc).*)","/(api|trpc)(.*)"]},iM={...U},iU=iM.middleware||iM.default,iL="/src/middleware";if("function"!=typeof iU)throw Error(`The Middleware "${iL}" must export a \`middleware\` or a \`default\` function`);function iD(e){return eG({...e,page:iL,handler:iU})}},1250:(e,t)=>{"use strict";function r(e,t,r){do{var n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<r);return r}function n(e,t,r){for(;t>r;){var n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return r}function i(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}t.Q=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");var a={},s=e.length,o=s-2;if(o<0)return a;var l=t&&t.decode||i,c=0,u=0,d=0;do{if(-1===(u=e.indexOf("=",c)))break;if(-1===(d=e.indexOf(";",c)))d=s;else if(u>d){c=e.lastIndexOf(";",u-1)+1;continue}var h=r(e,c,u),p=n(e,u,h),f=e.slice(h,p);if(void 0===a[f]){var g=r(e,u+1,d),m=n(e,d,g);34===e.charCodeAt(g)&&34===e.charCodeAt(m-1)&&(g++,m--);var y=e.slice(g,m);a[f]=function(e,t){try{return t(e)}catch(t){return e}}(y,l)}c=d+1}while(c<o);return a},Object.prototype.toString},2990:function(e,t,r){var n;n=function(e){var t,r,n,i,a,s,o,l,c,u,d,h,p,f;return t=e.lib.BlockCipher,r=e.algo,n=[],i=[],a=[],s=[],o=[],l=[],c=[],u=[],d=[],h=[],function(){for(var e=[],t=0;t<256;t++)t<128?e[t]=t<<1:e[t]=t<<1^283;for(var r=0,p=0,t=0;t<256;t++){var f=p^p<<1^p<<2^p<<3^p<<4;f=f>>>8^255&f^99,n[r]=f,i[f]=r;var g=e[r],m=e[g],y=e[m],v=257*e[f]^16843008*f;a[r]=v<<24|v>>>8,s[r]=v<<16|v>>>16,o[r]=v<<8|v>>>24,l[r]=v;var v=16843009*y^65537*m^257*g^16843008*r;c[f]=v<<24|v>>>8,u[f]=v<<16|v>>>16,d[f]=v<<8|v>>>24,h[f]=v,r?(r=g^e[e[e[y^g]]],p^=e[e[p]]):r=p=1}}(),p=[0,1,2,4,8,16,32,64,128,27,54],f=r.AES=t.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e,t=this._keyPriorReset=this._key,r=t.words,i=t.sigBytes/4,a=((this._nRounds=i+6)+1)*4,s=this._keySchedule=[],o=0;o<a;o++)o<i?s[o]=r[o]:(e=s[o-1],o%i?i>6&&o%i==4&&(e=n[e>>>24]<<24|n[e>>>16&255]<<16|n[e>>>8&255]<<8|n[255&e]):e=(n[(e=e<<8|e>>>24)>>>24]<<24|n[e>>>16&255]<<16|n[e>>>8&255]<<8|n[255&e])^p[o/i|0]<<24,s[o]=s[o-i]^e);for(var l=this._invKeySchedule=[],f=0;f<a;f++){var o=a-f;if(f%4)var e=s[o];else var e=s[o-4];f<4||o<=4?l[f]=e:l[f]=c[n[e>>>24]]^u[n[e>>>16&255]]^d[n[e>>>8&255]]^h[n[255&e]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,a,s,o,l,n)},decryptBlock:function(e,t){var r=e[t+1];e[t+1]=e[t+3],e[t+3]=r,this._doCryptBlock(e,t,this._invKeySchedule,c,u,d,h,i);var r=e[t+1];e[t+1]=e[t+3],e[t+3]=r},_doCryptBlock:function(e,t,r,n,i,a,s,o){for(var l=this._nRounds,c=e[t]^r[0],u=e[t+1]^r[1],d=e[t+2]^r[2],h=e[t+3]^r[3],p=4,f=1;f<l;f++){var g=n[c>>>24]^i[u>>>16&255]^a[d>>>8&255]^s[255&h]^r[p++],m=n[u>>>24]^i[d>>>16&255]^a[h>>>8&255]^s[255&c]^r[p++],y=n[d>>>24]^i[h>>>16&255]^a[c>>>8&255]^s[255&u]^r[p++],v=n[h>>>24]^i[c>>>16&255]^a[u>>>8&255]^s[255&d]^r[p++];c=g,u=m,d=y,h=v}var g=(o[c>>>24]<<24|o[u>>>16&255]<<16|o[d>>>8&255]<<8|o[255&h])^r[p++],m=(o[u>>>24]<<24|o[d>>>16&255]<<16|o[h>>>8&255]<<8|o[255&c])^r[p++],y=(o[d>>>24]<<24|o[h>>>16&255]<<16|o[c>>>8&255]<<8|o[255&u])^r[p++],v=(o[h>>>24]<<24|o[c>>>16&255]<<16|o[u>>>8&255]<<8|o[255&d])^r[p++];e[t]=g,e[t+1]=m,e[t+2]=y,e[t+3]=v},keySize:8}),e.AES=t._createHelper(f),e.AES},e.exports=n(r(1989),r(9570),r(5080),r(7869),r(606))},606:function(e,t,r){var n;n=function(e){var t,r,n,i,a,s,o,l,c,u,d,h,p,f,g,m,y;e.lib.Cipher||(r=(t=e.lib).Base,n=t.WordArray,i=t.BufferedBlockAlgorithm,(a=e.enc).Utf8,s=a.Base64,o=e.algo.EvpKDF,l=t.Cipher=i.extend({cfg:r.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,r){this.cfg=this.cfg.extend(r),this._xformMode=e,this._key=t,this.reset()},reset:function(){i.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function e(e){return"string"==typeof e?y:g}return function(t){return{encrypt:function(r,n,i){return e(n).encrypt(t,r,n,i)},decrypt:function(r,n,i){return e(n).decrypt(t,r,n,i)}}}}()}),t.StreamCipher=l.extend({_doFinalize:function(){return this._process(!0)},blockSize:1}),c=e.mode={},u=t.BlockCipherMode=r.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),d=c.CBC=function(){var e=u.extend();function t(e,t,r){var n,i=this._iv;i?(n=i,this._iv=void 0):n=this._prevBlock;for(var a=0;a<r;a++)e[t+a]^=n[a]}return e.Encryptor=e.extend({processBlock:function(e,r){var n=this._cipher,i=n.blockSize;t.call(this,e,r,i),n.encryptBlock(e,r),this._prevBlock=e.slice(r,r+i)}}),e.Decryptor=e.extend({processBlock:function(e,r){var n=this._cipher,i=n.blockSize,a=e.slice(r,r+i);n.decryptBlock(e,r),t.call(this,e,r,i),this._prevBlock=a}}),e}(),h=(e.pad={}).Pkcs7={pad:function(e,t){for(var r=4*t,i=r-e.sigBytes%r,a=i<<24|i<<16|i<<8|i,s=[],o=0;o<i;o+=4)s.push(a);var l=n.create(s,i);e.concat(l)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},t.BlockCipher=l.extend({cfg:l.cfg.extend({mode:d,padding:h}),reset:function(){l.reset.call(this);var e,t=this.cfg,r=t.iv,n=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=n.createEncryptor:(e=n.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==e?this._mode.init(this,r&&r.words):(this._mode=e.call(n,this,r&&r.words),this._mode.__creator=e)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4}),p=t.CipherParams=r.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}}),f=(e.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext,r=e.salt;return(r?n.create([1398893684,1701076831]).concat(r).concat(t):t).toString(s)},parse:function(e){var t,r=s.parse(e),i=r.words;return 1398893684==i[0]&&1701076831==i[1]&&(t=n.create(i.slice(2,4)),i.splice(0,4),r.sigBytes-=16),p.create({ciphertext:r,salt:t})}},g=t.SerializableCipher=r.extend({cfg:r.extend({format:f}),encrypt:function(e,t,r,n){n=this.cfg.extend(n);var i=e.createEncryptor(r,n),a=i.finalize(t),s=i.cfg;return p.create({ciphertext:a,key:r,iv:s.iv,algorithm:e,mode:s.mode,padding:s.padding,blockSize:e.blockSize,formatter:n.format})},decrypt:function(e,t,r,n){return n=this.cfg.extend(n),t=this._parse(t,n.format),e.createDecryptor(r,n).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),m=(e.kdf={}).OpenSSL={execute:function(e,t,r,i,a){if(i||(i=n.random(8)),a)var s=o.create({keySize:t+r,hasher:a}).compute(e,i);else var s=o.create({keySize:t+r}).compute(e,i);var l=n.create(s.words.slice(t),4*r);return s.sigBytes=4*t,p.create({key:s,iv:l,salt:i})}},y=t.PasswordBasedCipher=g.extend({cfg:g.cfg.extend({kdf:m}),encrypt:function(e,t,r,n){var i=(n=this.cfg.extend(n)).kdf.execute(r,e.keySize,e.ivSize,n.salt,n.hasher);n.iv=i.iv;var a=g.encrypt.call(this,e,t,i.key,n);return a.mixIn(i),a},decrypt:function(e,t,r,n){n=this.cfg.extend(n),t=this._parse(t,n.format);var i=n.kdf.execute(r,e.keySize,e.ivSize,t.salt,n.hasher);return n.iv=i.iv,g.decrypt.call(this,e,t,i.key,n)}}))},e.exports=n(r(1989),r(7869))},1989:function(e,t,r){var n;n=function(){var e=e||function(e,t){if("undefined"!=typeof window&&window.crypto&&(n=window.crypto),"undefined"!=typeof self&&self.crypto&&(n=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(n=globalThis.crypto),!n&&"undefined"!=typeof window&&window.msCrypto&&(n=window.msCrypto),!n&&void 0!==r.g&&r.g.crypto&&(n=r.g.crypto),!n)try{n=r(2480)}catch(e){}var n,i=function(){if(n){if("function"==typeof n.getRandomValues)try{return n.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof n.randomBytes)try{return n.randomBytes(4).readInt32LE()}catch(e){}}throw Error("Native crypto module could not be used to get secure random number.")},a=Object.create||function(){function e(){}return function(t){var r;return e.prototype=t,r=new e,e.prototype=null,r}}(),s={},o=s.lib={},l=o.Base={extend:function(e){var t=a(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},c=o.WordArray=l.extend({init:function(e,r){e=this.words=e||[],t!=r?this.sigBytes=r:this.sigBytes=4*e.length},toString:function(e){return(e||d).stringify(this)},concat:function(e){var t=this.words,r=e.words,n=this.sigBytes,i=e.sigBytes;if(this.clamp(),n%4)for(var a=0;a<i;a++){var s=r[a>>>2]>>>24-a%4*8&255;t[n+a>>>2]|=s<<24-(n+a)%4*8}else for(var o=0;o<i;o+=4)t[n+o>>>2]=r[o>>>2];return this.sigBytes+=i,this},clamp:function(){var t=this.words,r=this.sigBytes;t[r>>>2]&=4294967295<<32-r%4*8,t.length=e.ceil(r/4)},clone:function(){var e=l.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],r=0;r<e;r+=4)t.push(i());return new c.init(t,e)}}),u=s.enc={},d=u.Hex={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],i=0;i<r;i++){var a=t[i>>>2]>>>24-i%4*8&255;n.push((a>>>4).toString(16)),n.push((15&a).toString(16))}return n.join("")},parse:function(e){for(var t=e.length,r=[],n=0;n<t;n+=2)r[n>>>3]|=parseInt(e.substr(n,2),16)<<24-n%8*4;return new c.init(r,t/2)}},h=u.Latin1={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],i=0;i<r;i++){var a=t[i>>>2]>>>24-i%4*8&255;n.push(String.fromCharCode(a))}return n.join("")},parse:function(e){for(var t=e.length,r=[],n=0;n<t;n++)r[n>>>2]|=(255&e.charCodeAt(n))<<24-n%4*8;return new c.init(r,t)}},p=u.Utf8={stringify:function(e){try{return decodeURIComponent(escape(h.stringify(e)))}catch(e){throw Error("Malformed UTF-8 data")}},parse:function(e){return h.parse(unescape(encodeURIComponent(e)))}},f=o.BufferedBlockAlgorithm=l.extend({reset:function(){this._data=new c.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=p.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var r,n=this._data,i=n.words,a=n.sigBytes,s=this.blockSize,o=a/(4*s),l=(o=t?e.ceil(o):e.max((0|o)-this._minBufferSize,0))*s,u=e.min(4*l,a);if(l){for(var d=0;d<l;d+=s)this._doProcessBlock(i,d);r=i.splice(0,l),n.sigBytes-=u}return new c.init(r,u)},clone:function(){var e=l.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});o.Hasher=f.extend({cfg:l.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){f.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,r){return new e.init(r).finalize(t)}},_createHmacHelper:function(e){return function(t,r){return new g.HMAC.init(e,r).finalize(t)}}});var g=s.algo={};return s}(Math);return e},e.exports=n()},9570:function(e,t,r){var n;n=function(e){var t;return t=e.lib.WordArray,e.enc.Base64={stringify:function(e){var t=e.words,r=e.sigBytes,n=this._map;e.clamp();for(var i=[],a=0;a<r;a+=3)for(var s=(t[a>>>2]>>>24-a%4*8&255)<<16|(t[a+1>>>2]>>>24-(a+1)%4*8&255)<<8|t[a+2>>>2]>>>24-(a+2)%4*8&255,o=0;o<4&&a+.75*o<r;o++)i.push(n.charAt(s>>>6*(3-o)&63));var l=n.charAt(64);if(l)for(;i.length%4;)i.push(l);return i.join("")},parse:function(e){var r=e.length,n=this._map,i=this._reverseMap;if(!i){i=this._reverseMap=[];for(var a=0;a<n.length;a++)i[n.charCodeAt(a)]=a}var s=n.charAt(64);if(s){var o=e.indexOf(s);-1!==o&&(r=o)}return function(e,r,n){for(var i=[],a=0,s=0;s<r;s++)if(s%4){var o=n[e.charCodeAt(s-1)]<<s%4*2|n[e.charCodeAt(s)]>>>6-s%4*2;i[a>>>2]|=o<<24-a%4*8,a++}return t.create(i,a)}(e,r,i)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},e.enc.Base64},e.exports=n(r(1989))},6209:function(e,t,r){var n;n=function(e){return e.enc.Utf8},e.exports=n(r(1989))},7869:function(e,t,r){var n;n=function(e){var t,r,n,i,a,s;return r=(t=e.lib).Base,n=t.WordArray,a=(i=e.algo).MD5,s=i.EvpKDF=r.extend({cfg:r.extend({keySize:4,hasher:a,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r,i=this.cfg,a=i.hasher.create(),s=n.create(),o=s.words,l=i.keySize,c=i.iterations;o.length<l;){r&&a.update(r),r=a.update(e).finalize(t),a.reset();for(var u=1;u<c;u++)r=a.finalize(r),a.reset();s.concat(r)}return s.sigBytes=4*l,s}}),e.EvpKDF=function(e,t,r){return s.create(r).compute(e,t)},e.EvpKDF},e.exports=n(r(1989),r(8997),r(9269))},6792:function(e,t,r){var n;n=function(e){return e.HmacSHA1},e.exports=n(r(1989),r(8997),r(9269))},9269:function(e,t,r){var n;n=function(e){var t,r;t=e.lib.Base,r=e.enc.Utf8,e.algo.HMAC=t.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=r.parse(t));var n=e.blockSize,i=4*n;t.sigBytes>i&&(t=e.finalize(t)),t.clamp();for(var a=this._oKey=t.clone(),s=this._iKey=t.clone(),o=a.words,l=s.words,c=0;c<n;c++)o[c]^=1549556828,l[c]^=909522486;a.sigBytes=s.sigBytes=i,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}})},e.exports=n(r(1989))},5080:function(e,t,r){var n;n=function(e){return function(t){var r=e.lib,n=r.WordArray,i=r.Hasher,a=e.algo,s=[];!function(){for(var e=0;e<64;e++)s[e]=4294967296*t.abs(t.sin(e+1))|0}();var o=a.MD5=i.extend({_doReset:function(){this._hash=new n.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var r=0;r<16;r++){var n=t+r,i=e[n];e[n]=(i<<8|i>>>24)&16711935|(i<<24|i>>>8)&4278255360}var a=this._hash.words,o=e[t+0],h=e[t+1],p=e[t+2],f=e[t+3],g=e[t+4],m=e[t+5],y=e[t+6],v=e[t+7],b=e[t+8],w=e[t+9],_=e[t+10],S=e[t+11],k=e[t+12],x=e[t+13],T=e[t+14],E=e[t+15],C=a[0],O=a[1],P=a[2],I=a[3];C=l(C,O,P,I,o,7,s[0]),I=l(I,C,O,P,h,12,s[1]),P=l(P,I,C,O,p,17,s[2]),O=l(O,P,I,C,f,22,s[3]),C=l(C,O,P,I,g,7,s[4]),I=l(I,C,O,P,m,12,s[5]),P=l(P,I,C,O,y,17,s[6]),O=l(O,P,I,C,v,22,s[7]),C=l(C,O,P,I,b,7,s[8]),I=l(I,C,O,P,w,12,s[9]),P=l(P,I,C,O,_,17,s[10]),O=l(O,P,I,C,S,22,s[11]),C=l(C,O,P,I,k,7,s[12]),I=l(I,C,O,P,x,12,s[13]),P=l(P,I,C,O,T,17,s[14]),O=l(O,P,I,C,E,22,s[15]),C=c(C,O,P,I,h,5,s[16]),I=c(I,C,O,P,y,9,s[17]),P=c(P,I,C,O,S,14,s[18]),O=c(O,P,I,C,o,20,s[19]),C=c(C,O,P,I,m,5,s[20]),I=c(I,C,O,P,_,9,s[21]),P=c(P,I,C,O,E,14,s[22]),O=c(O,P,I,C,g,20,s[23]),C=c(C,O,P,I,w,5,s[24]),I=c(I,C,O,P,T,9,s[25]),P=c(P,I,C,O,f,14,s[26]),O=c(O,P,I,C,b,20,s[27]),C=c(C,O,P,I,x,5,s[28]),I=c(I,C,O,P,p,9,s[29]),P=c(P,I,C,O,v,14,s[30]),O=c(O,P,I,C,k,20,s[31]),C=u(C,O,P,I,m,4,s[32]),I=u(I,C,O,P,b,11,s[33]),P=u(P,I,C,O,S,16,s[34]),O=u(O,P,I,C,T,23,s[35]),C=u(C,O,P,I,h,4,s[36]),I=u(I,C,O,P,g,11,s[37]),P=u(P,I,C,O,v,16,s[38]),O=u(O,P,I,C,_,23,s[39]),C=u(C,O,P,I,x,4,s[40]),I=u(I,C,O,P,o,11,s[41]),P=u(P,I,C,O,f,16,s[42]),O=u(O,P,I,C,y,23,s[43]),C=u(C,O,P,I,w,4,s[44]),I=u(I,C,O,P,k,11,s[45]),P=u(P,I,C,O,E,16,s[46]),O=u(O,P,I,C,p,23,s[47]),C=d(C,O,P,I,o,6,s[48]),I=d(I,C,O,P,v,10,s[49]),P=d(P,I,C,O,T,15,s[50]),O=d(O,P,I,C,m,21,s[51]),C=d(C,O,P,I,k,6,s[52]),I=d(I,C,O,P,f,10,s[53]),P=d(P,I,C,O,_,15,s[54]),O=d(O,P,I,C,h,21,s[55]),C=d(C,O,P,I,b,6,s[56]),I=d(I,C,O,P,E,10,s[57]),P=d(P,I,C,O,y,15,s[58]),O=d(O,P,I,C,x,21,s[59]),C=d(C,O,P,I,g,6,s[60]),I=d(I,C,O,P,S,10,s[61]),P=d(P,I,C,O,p,15,s[62]),O=d(O,P,I,C,w,21,s[63]),a[0]=a[0]+C|0,a[1]=a[1]+O|0,a[2]=a[2]+P|0,a[3]=a[3]+I|0},_doFinalize:function(){var e=this._data,r=e.words,n=8*this._nDataBytes,i=8*e.sigBytes;r[i>>>5]|=128<<24-i%32;var a=t.floor(n/4294967296);r[(i+64>>>9<<4)+15]=(a<<8|a>>>24)&16711935|(a<<24|a>>>8)&4278255360,r[(i+64>>>9<<4)+14]=(n<<8|n>>>24)&16711935|(n<<24|n>>>8)&4278255360,e.sigBytes=(r.length+1)*4,this._process();for(var s=this._hash,o=s.words,l=0;l<4;l++){var c=o[l];o[l]=(c<<8|c>>>24)&16711935|(c<<24|c>>>8)&4278255360}return s},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});function l(e,t,r,n,i,a,s){var o=e+(t&r|~t&n)+i+s;return(o<<a|o>>>32-a)+t}function c(e,t,r,n,i,a,s){var o=e+(t&n|r&~n)+i+s;return(o<<a|o>>>32-a)+t}function u(e,t,r,n,i,a,s){var o=e+(t^r^n)+i+s;return(o<<a|o>>>32-a)+t}function d(e,t,r,n,i,a,s){var o=e+(r^(t|~n))+i+s;return(o<<a|o>>>32-a)+t}e.MD5=i._createHelper(o),e.HmacMD5=i._createHmacHelper(o)}(Math),e.MD5},e.exports=n(r(1989))},8997:function(e,t,r){var n;n=function(e){var t,r,n,i,a,s;return r=(t=e.lib).WordArray,n=t.Hasher,i=e.algo,a=[],s=i.SHA1=n.extend({_doReset:function(){this._hash=new r.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=this._hash.words,n=r[0],i=r[1],s=r[2],o=r[3],l=r[4],c=0;c<80;c++){if(c<16)a[c]=0|e[t+c];else{var u=a[c-3]^a[c-8]^a[c-14]^a[c-16];a[c]=u<<1|u>>>31}var d=(n<<5|n>>>27)+l+a[c];c<20?d+=(i&s|~i&o)+1518500249:c<40?d+=(i^s^o)+1859775393:c<60?d+=(i&s|i&o|s&o)-1894007588:d+=(i^s^o)-899497514,l=o,o=s,s=i<<30|i>>>2,i=n,n=d}r[0]=r[0]+n|0,r[1]=r[1]+i|0,r[2]=r[2]+s|0,r[3]=r[3]+o|0,r[4]=r[4]+l|0},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;return t[n>>>5]|=128<<24-n%32,t[(n+64>>>9<<4)+14]=Math.floor(r/4294967296),t[(n+64>>>9<<4)+15]=r,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}}),e.SHA1=n._createHelper(s),e.HmacSHA1=n._createHmacHelper(s),e.SHA1},e.exports=n(r(1989))},106:e=>{"use strict";let t=e=>"object"==typeof e&&null!==e,r=Symbol("skip"),n=e=>t(e)&&!(e instanceof RegExp)&&!(e instanceof Error)&&!(e instanceof Date),i=(e,t,a,s=new WeakMap)=>{if(a={deep:!1,target:{},...a},s.has(e))return s.get(e);s.set(e,a.target);let{target:o}=a;delete a.target;let l=e=>e.map(e=>n(e)?i(e,t,a,s):e);if(Array.isArray(e))return l(e);for(let[c,u]of Object.entries(e)){let d=t(c,u,e);if(d===r)continue;let[h,p,{shouldRecurse:f=!0}={}]=d;"__proto__"!==h&&(a.deep&&f&&n(p)&&(p=Array.isArray(p)?l(p):i(p,t,a,s)),o[h]=p)}return o};e.exports=(e,r,n)=>{if(!t(e))throw TypeError(`Expected an object, got \`${e}\` (${typeof e})`);return i(e,r,n)},e.exports.mapObjectSkip=r},5945:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a={};function s(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function o(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,i]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=i?i:"true"))}catch{}}return t}function l(e){var t,r;if(!e)return;let[[n,i],...a]=o(e),{domain:s,expires:l,httponly:d,maxage:h,path:p,samesite:f,secure:g,partitioned:m,priority:y}=Object.fromEntries(a.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:n,value:decodeURIComponent(i),domain:s,...l&&{expires:new Date(l)},...d&&{httpOnly:!0},..."string"==typeof h&&{maxAge:Number(h)},path:p,...f&&{sameSite:c.includes(t=(t=f).toLowerCase())?t:void 0},...g&&{secure:!0},...y&&{priority:u.includes(r=(r=y).toLowerCase())?r:void 0},...m&&{partitioned:!0}})}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(a,{RequestCookies:()=>d,ResponseCookies:()=>h,parseCookie:()=>o,parseSetCookie:()=>l,stringifyCookie:()=>s}),e.exports=((e,a,s,o)=>{if(a&&"object"==typeof a||"function"==typeof a)for(let l of n(a))i.call(e,l)||l===s||t(e,l,{get:()=>a[l],enumerable:!(o=r(a,l))||o.enumerable});return e})(t({},"__esModule",{value:!0}),a);var c=["strict","lax","none"],u=["low","medium","high"],d=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of o(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>s(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>s(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},h=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let i=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(i)?i:function(e){if(!e)return[];var t,r,n,i,a,s=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,a=!1;l();)if(","===(r=e.charAt(o))){for(n=o,o+=1,l(),i=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(a=!0,o=i,s.push(e.substring(t,n)),t=o):o=n+1}else o+=1;(!a||o>=e.length)&&s.push(e.substring(t,e.length))}return s}(i)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,i=this._parsed;return i.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=s(r);t.append("set-cookie",e)}}(i,this._headers),this}delete(...e){let[t,r,n]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:n,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(s).join("; ")}}},8439:(e,t,r)=>{(()=>{"use strict";var t={491:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let n=r(223),i=r(172),a=r(930),s="context",o=new n.NoopContextManager;class l{constructor(){}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalContextManager(e){return(0,i.registerGlobal)(s,e,a.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,r,...n){return this._getContextManager().with(e,t,r,...n)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,i.getGlobal)(s)||o}disable(){this._getContextManager().disable(),(0,i.unregisterGlobal)(s,a.DiagAPI.instance())}}t.ContextAPI=l},930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let n=r(56),i=r(912),a=r(957),s=r(172);class o{constructor(){function e(e){return function(...t){let r=(0,s.getGlobal)("diag");if(r)return r[e](...t)}}let t=this;t.setLogger=(e,r={logLevel:a.DiagLogLevel.INFO})=>{var n,o,l;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!==(n=e.stack)&&void 0!==n?n:e.message),!1}"number"==typeof r&&(r={logLevel:r});let c=(0,s.getGlobal)("diag"),u=(0,i.createLogLevelDiagLogger)(null!==(o=r.logLevel)&&void 0!==o?o:a.DiagLogLevel.INFO,e);if(c&&!r.suppressOverrideMessage){let e=null!==(l=Error().stack)&&void 0!==l?l:"<failed to generate stacktrace>";c.warn(`Current logger will be overwritten from ${e}`),u.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,s.registerGlobal)("diag",u,t,!0)},t.disable=()=>{(0,s.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new n.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new o),this._instance}}t.DiagAPI=o},653:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let n=r(660),i=r(172),a=r(930),s="metrics";class o{constructor(){}static getInstance(){return this._instance||(this._instance=new o),this._instance}setGlobalMeterProvider(e){return(0,i.registerGlobal)(s,e,a.DiagAPI.instance())}getMeterProvider(){return(0,i.getGlobal)(s)||n.NOOP_METER_PROVIDER}getMeter(e,t,r){return this.getMeterProvider().getMeter(e,t,r)}disable(){(0,i.unregisterGlobal)(s,a.DiagAPI.instance())}}t.MetricsAPI=o},181:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let n=r(172),i=r(874),a=r(194),s=r(277),o=r(369),l=r(930),c="propagation",u=new i.NoopTextMapPropagator;class d{constructor(){this.createBaggage=o.createBaggage,this.getBaggage=s.getBaggage,this.getActiveBaggage=s.getActiveBaggage,this.setBaggage=s.setBaggage,this.deleteBaggage=s.deleteBaggage}static getInstance(){return this._instance||(this._instance=new d),this._instance}setGlobalPropagator(e){return(0,n.registerGlobal)(c,e,l.DiagAPI.instance())}inject(e,t,r=a.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,r)}extract(e,t,r=a.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,r)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,n.unregisterGlobal)(c,l.DiagAPI.instance())}_getGlobalPropagator(){return(0,n.getGlobal)(c)||u}}t.PropagationAPI=d},997:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let n=r(172),i=r(846),a=r(139),s=r(607),o=r(930),l="trace";class c{constructor(){this._proxyTracerProvider=new i.ProxyTracerProvider,this.wrapSpanContext=a.wrapSpanContext,this.isSpanContextValid=a.isSpanContextValid,this.deleteSpan=s.deleteSpan,this.getSpan=s.getSpan,this.getActiveSpan=s.getActiveSpan,this.getSpanContext=s.getSpanContext,this.setSpan=s.setSpan,this.setSpanContext=s.setSpanContext}static getInstance(){return this._instance||(this._instance=new c),this._instance}setGlobalTracerProvider(e){let t=(0,n.registerGlobal)(l,this._proxyTracerProvider,o.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,n.getGlobal)(l)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,n.unregisterGlobal)(l,o.DiagAPI.instance()),this._proxyTracerProvider=new i.ProxyTracerProvider}}t.TraceAPI=c},277:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let n=r(491),i=(0,r(780).createContextKey)("OpenTelemetry Baggage Key");function a(e){return e.getValue(i)||void 0}t.getBaggage=a,t.getActiveBaggage=function(){return a(n.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(i,t)},t.deleteBaggage=function(e){return e.deleteValue(i)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class r{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let n=new r(this._entries);return n._entries.set(e,t),n}removeEntry(e){let t=new r(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new r(this._entries);for(let r of e)t._entries.delete(r);return t}clear(){return new r}}t.BaggageImpl=r},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let n=r(930),i=r(993),a=r(830),s=n.DiagAPI.instance();t.createBaggage=function(e={}){return new i.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(s.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:a.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0;let n=r(491);t.context=n.ContextAPI.getInstance()},223:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let n=r(780);class i{active(){return n.ROOT_CONTEXT}with(e,t,r,...n){return t.call(r,...n)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=i},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class r{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,n)=>{let i=new r(t._currentContext);return i._currentContext.set(e,n),i},t.deleteValue=e=>{let n=new r(t._currentContext);return n._currentContext.delete(e),n}}}t.ROOT_CONTEXT=new r},506:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0;let n=r(930);t.diag=n.DiagAPI.instance()},56:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let n=r(172);class i{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return a("debug",this._namespace,e)}error(...e){return a("error",this._namespace,e)}info(...e){return a("info",this._namespace,e)}warn(...e){return a("warn",this._namespace,e)}verbose(...e){return a("verbose",this._namespace,e)}}function a(e,t,r){let i=(0,n.getGlobal)("diag");if(i)return r.unshift(t),i[e](...r)}t.DiagComponentLogger=i},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let r=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class n{constructor(){for(let e=0;e<r.length;e++)this[r[e].n]=function(e){return function(...t){if(console){let r=console[e];if("function"!=typeof r&&(r=console.log),"function"==typeof r)return r.apply(console,t)}}}(r[e].c)}}t.DiagConsoleLogger=n},912:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let n=r(957);t.createLogLevelDiagLogger=function(e,t){function r(r,n){let i=t[r];return"function"==typeof i&&e>=n?i.bind(t):function(){}}return e<n.DiagLogLevel.NONE?e=n.DiagLogLevel.NONE:e>n.DiagLogLevel.ALL&&(e=n.DiagLogLevel.ALL),t=t||{},{error:r("error",n.DiagLogLevel.ERROR),warn:r("warn",n.DiagLogLevel.WARN),info:r("info",n.DiagLogLevel.INFO),debug:r("debug",n.DiagLogLevel.DEBUG),verbose:r("verbose",n.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let n=r(200),i=r(521),a=r(130),s=i.VERSION.split(".")[0],o=Symbol.for(`opentelemetry.js.api.${s}`),l=n._globalThis;t.registerGlobal=function(e,t,r,n=!1){var a;let s=l[o]=null!==(a=l[o])&&void 0!==a?a:{version:i.VERSION};if(!n&&s[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return r.error(t.stack||t.message),!1}if(s.version!==i.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${s.version} for ${e} does not match previously registered API v${i.VERSION}`);return r.error(t.stack||t.message),!1}return s[e]=t,r.debug(`@opentelemetry/api: Registered a global for ${e} v${i.VERSION}.`),!0},t.getGlobal=function(e){var t,r;let n=null===(t=l[o])||void 0===t?void 0:t.version;if(n&&(0,a.isCompatible)(n))return null===(r=l[o])||void 0===r?void 0:r[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${i.VERSION}.`);let r=l[o];r&&delete r[e]}},130:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let n=r(521),i=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function a(e){let t=new Set([e]),r=new Set,n=e.match(i);if(!n)return()=>!1;let a={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=a.prerelease)return function(t){return t===e};function s(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;let n=e.match(i);if(!n)return s(e);let o={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};return null!=o.prerelease||a.major!==o.major?s(e):0===a.major?a.minor===o.minor&&a.patch<=o.patch?(t.add(e),!0):s(e):a.minor<=o.minor?(t.add(e),!0):s(e)}}t._makeCompatibilityCheck=a,t.isCompatible=a(n.VERSION)},886:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0;let n=r(653);t.metrics=n.MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class r{constructor(){}createHistogram(e,r){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,r){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,r){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,r){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,r){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,r){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=r;class n{}t.NoopMetric=n;class i extends n{add(e,t){}}t.NoopCounterMetric=i;class a extends n{add(e,t){}}t.NoopUpDownCounterMetric=a;class s extends n{record(e,t){}}t.NoopHistogramMetric=s;class o{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=o;class l extends o{}t.NoopObservableCounterMetric=l;class c extends o{}t.NoopObservableGaugeMetric=c;class u extends o{}t.NoopObservableUpDownCounterMetric=u,t.NOOP_METER=new r,t.NOOP_COUNTER_METRIC=new i,t.NOOP_HISTOGRAM_METRIC=new s,t.NOOP_UP_DOWN_COUNTER_METRIC=new a,t.NOOP_OBSERVABLE_COUNTER_METRIC=new l,t.NOOP_OBSERVABLE_GAUGE_METRIC=new c,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new u,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let n=r(102);class i{getMeter(e,t,r){return n.NOOP_METER}}t.NoopMeterProvider=i,t.NOOP_METER_PROVIDER=new i},200:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(46),t)},651:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t._globalThis=void 0,t._globalThis="object"==typeof globalThis?globalThis:r.g},46:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(651),t)},939:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0;let n=r(181);t.propagation=n.PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0;class r{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=r},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,r){null!=e&&(e[t]=r)}}},845:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0;let n=r(997);t.trace=n.TraceAPI.getInstance()},403:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let n=r(476);class i{constructor(e=n.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}t.NonRecordingSpan=i},614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let n=r(491),i=r(607),a=r(403),s=r(139),o=n.ContextAPI.getInstance();class l{startSpan(e,t,r=o.active()){if(null==t?void 0:t.root)return new a.NonRecordingSpan;let n=r&&(0,i.getSpanContext)(r);return"object"==typeof n&&"string"==typeof n.spanId&&"string"==typeof n.traceId&&"number"==typeof n.traceFlags&&(0,s.isSpanContextValid)(n)?new a.NonRecordingSpan(n):new a.NonRecordingSpan}startActiveSpan(e,t,r,n){let a,s,l;if(arguments.length<2)return;2==arguments.length?l=t:3==arguments.length?(a=t,l=r):(a=t,s=r,l=n);let c=null!=s?s:o.active(),u=this.startSpan(e,a,c),d=(0,i.setSpan)(c,u);return o.with(d,l,void 0,u)}}t.NoopTracer=l},124:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let n=r(614);class i{getTracer(e,t,r){return new n.NoopTracer}}t.NoopTracerProvider=i},125:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let n=new(r(614)).NoopTracer;class i{constructor(e,t,r,n){this._provider=e,this.name=t,this.version=r,this.options=n}startSpan(e,t,r){return this._getTracer().startSpan(e,t,r)}startActiveSpan(e,t,r,n){let i=this._getTracer();return Reflect.apply(i.startActiveSpan,i,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):n}}t.ProxyTracer=i},846:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let n=r(125),i=new(r(124)).NoopTracerProvider;class a{getTracer(e,t,r){var i;return null!==(i=this.getDelegateTracer(e,t,r))&&void 0!==i?i:new n.ProxyTracer(this,e,t,r)}getDelegate(){var e;return null!==(e=this._delegate)&&void 0!==e?e:i}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,r){var n;return null===(n=this._delegate)||void 0===n?void 0:n.getTracer(e,t,r)}}t.ProxyTracerProvider=a},996:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let n=r(780),i=r(403),a=r(491),s=(0,n.createContextKey)("OpenTelemetry Context Key SPAN");function o(e){return e.getValue(s)||void 0}function l(e,t){return e.setValue(s,t)}t.getSpan=o,t.getActiveSpan=function(){return o(a.ContextAPI.getInstance().active())},t.setSpan=l,t.deleteSpan=function(e){return e.deleteValue(s)},t.setSpanContext=function(e,t){return l(e,new i.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null===(t=o(e))||void 0===t?void 0:t.spanContext()}},325:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let n=r(564);class i{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let r=t.trim(),i=r.indexOf("=");if(-1!==i){let a=r.slice(0,i),s=r.slice(i+1,t.length);(0,n.validateKey)(a)&&(0,n.validateValue)(s)&&e.set(a,s)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new i;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=i},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let r="[_0-9a-z-*/]",n=`[a-z]${r}{0,255}`,i=`[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`,a=RegExp(`^(?:${n}|${i})$`),s=/^[ -~]{0,255}[!-~]$/,o=/,|=/;t.validateKey=function(e){return a.test(e)},t.validateValue=function(e){return s.test(e)&&!o.test(e)}},98:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let n=r(325);t.createTraceState=function(e){return new n.TraceStateImpl(e)}},476:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let n=r(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:n.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(t.SpanKind||(t.SpanKind={}))},139:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let n=r(476),i=r(403),a=/^([0-9a-f]{32})$/i,s=/^[0-9a-f]{16}$/i;function o(e){return a.test(e)&&e!==n.INVALID_TRACEID}function l(e){return s.test(e)&&e!==n.INVALID_SPANID}t.isValidTraceId=o,t.isValidSpanId=l,t.isSpanContextValid=function(e){return o(e.traceId)&&l(e.spanId)},t.wrapSpanContext=function(e){return new i.NonRecordingSpan(e)}},847:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},n={};function i(e){var r=n[e];if(void 0!==r)return r.exports;var a=n[e]={exports:{}},s=!0;try{t[e].call(a.exports,a,a.exports,i),s=!1}finally{s&&delete n[e]}return a.exports}i.ab="//";var a={};(()=>{Object.defineProperty(a,"__esModule",{value:!0}),a.trace=a.propagation=a.metrics=a.diag=a.context=a.INVALID_SPAN_CONTEXT=a.INVALID_TRACEID=a.INVALID_SPANID=a.isValidSpanId=a.isValidTraceId=a.isSpanContextValid=a.createTraceState=a.TraceFlags=a.SpanStatusCode=a.SpanKind=a.SamplingDecision=a.ProxyTracerProvider=a.ProxyTracer=a.defaultTextMapSetter=a.defaultTextMapGetter=a.ValueType=a.createNoopMeter=a.DiagLogLevel=a.DiagConsoleLogger=a.ROOT_CONTEXT=a.createContextKey=a.baggageEntryMetadataFromString=void 0;var e=i(369);Object.defineProperty(a,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=i(780);Object.defineProperty(a,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(a,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var r=i(972);Object.defineProperty(a,"DiagConsoleLogger",{enumerable:!0,get:function(){return r.DiagConsoleLogger}});var n=i(957);Object.defineProperty(a,"DiagLogLevel",{enumerable:!0,get:function(){return n.DiagLogLevel}});var s=i(102);Object.defineProperty(a,"createNoopMeter",{enumerable:!0,get:function(){return s.createNoopMeter}});var o=i(901);Object.defineProperty(a,"ValueType",{enumerable:!0,get:function(){return o.ValueType}});var l=i(194);Object.defineProperty(a,"defaultTextMapGetter",{enumerable:!0,get:function(){return l.defaultTextMapGetter}}),Object.defineProperty(a,"defaultTextMapSetter",{enumerable:!0,get:function(){return l.defaultTextMapSetter}});var c=i(125);Object.defineProperty(a,"ProxyTracer",{enumerable:!0,get:function(){return c.ProxyTracer}});var u=i(846);Object.defineProperty(a,"ProxyTracerProvider",{enumerable:!0,get:function(){return u.ProxyTracerProvider}});var d=i(996);Object.defineProperty(a,"SamplingDecision",{enumerable:!0,get:function(){return d.SamplingDecision}});var h=i(357);Object.defineProperty(a,"SpanKind",{enumerable:!0,get:function(){return h.SpanKind}});var p=i(847);Object.defineProperty(a,"SpanStatusCode",{enumerable:!0,get:function(){return p.SpanStatusCode}});var f=i(475);Object.defineProperty(a,"TraceFlags",{enumerable:!0,get:function(){return f.TraceFlags}});var g=i(98);Object.defineProperty(a,"createTraceState",{enumerable:!0,get:function(){return g.createTraceState}});var m=i(139);Object.defineProperty(a,"isSpanContextValid",{enumerable:!0,get:function(){return m.isSpanContextValid}}),Object.defineProperty(a,"isValidTraceId",{enumerable:!0,get:function(){return m.isValidTraceId}}),Object.defineProperty(a,"isValidSpanId",{enumerable:!0,get:function(){return m.isValidSpanId}});var y=i(476);Object.defineProperty(a,"INVALID_SPANID",{enumerable:!0,get:function(){return y.INVALID_SPANID}}),Object.defineProperty(a,"INVALID_TRACEID",{enumerable:!0,get:function(){return y.INVALID_TRACEID}}),Object.defineProperty(a,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return y.INVALID_SPAN_CONTEXT}});let v=i(67);Object.defineProperty(a,"context",{enumerable:!0,get:function(){return v.context}});let b=i(506);Object.defineProperty(a,"diag",{enumerable:!0,get:function(){return b.diag}});let w=i(886);Object.defineProperty(a,"metrics",{enumerable:!0,get:function(){return w.metrics}});let _=i(939);Object.defineProperty(a,"propagation",{enumerable:!0,get:function(){return _.propagation}});let S=i(845);Object.defineProperty(a,"trace",{enumerable:!0,get:function(){return S.trace}}),a.default={context:v.context,diag:b.diag,metrics:w.metrics,propagation:_.propagation,trace:S.trace}})(),e.exports=a})()},1133:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},a=t.split(n),s=(r||{}).decode||e,o=0;o<a.length;o++){var l=a[o],c=l.indexOf("=");if(!(c<0)){var u=l.substr(0,c).trim(),d=l.substr(++c,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==i[u]&&(i[u]=function(e,t){try{return t(e)}catch(t){return e}}(d,s))}}return i},t.serialize=function(e,t,n){var a=n||{},s=a.encode||r;if("function"!=typeof s)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var o=s(t);if(o&&!i.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=a.maxAge){var c=a.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(c)}if(a.domain){if(!i.test(a.domain))throw TypeError("option domain is invalid");l+="; Domain="+a.domain}if(a.path){if(!i.test(a.path))throw TypeError("option path is invalid");l+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(l+="; HttpOnly"),a.secure&&(l+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},340:(e,t,r)=>{var n;(()=>{var i={226:function(i,a){!function(s,o){"use strict";var l="function",c="undefined",u="object",d="string",h="major",p="model",f="name",g="type",m="vendor",y="version",v="architecture",b="console",w="mobile",_="tablet",S="smarttv",k="wearable",x="embedded",T="Amazon",E="Apple",C="ASUS",O="BlackBerry",P="Browser",I="Chrome",R="Firefox",N="Google",A="Huawei",M="Microsoft",U="Motorola",L="Opera",D="Samsung",j="Sharp",q="Sony",B="Xiaomi",H="Zebra",z="Facebook",$="Chromium OS",K="Mac OS",F=function(e,t){var r={};for(var n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},V=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},J=function(e,t){return typeof e===d&&-1!==W(t).indexOf(W(e))},W=function(e){return e.toLowerCase()},G=function(e,t){if(typeof e===d)return e=e.replace(/^\s\s*/,""),typeof t===c?e:e.substring(0,350)},X=function(e,t){for(var r,n,i,a,s,c,d=0;d<t.length&&!s;){var h=t[d],p=t[d+1];for(r=n=0;r<h.length&&!s&&h[r];)if(s=h[r++].exec(e))for(i=0;i<p.length;i++)c=s[++n],typeof(a=p[i])===u&&a.length>0?2===a.length?typeof a[1]==l?this[a[0]]=a[1].call(this,c):this[a[0]]=a[1]:3===a.length?typeof a[1]!==l||a[1].exec&&a[1].test?this[a[0]]=c?c.replace(a[1],a[2]):void 0:this[a[0]]=c?a[1].call(this,c,a[2]):void 0:4===a.length&&(this[a[0]]=c?a[3].call(this,c.replace(a[1],a[2])):void 0):this[a]=c||o;d+=2}},Q=function(e,t){for(var r in t)if(typeof t[r]===u&&t[r].length>0){for(var n=0;n<t[r].length;n++)if(J(t[r][n],e))return"?"===r?o:r}else if(J(t[r],e))return"?"===r?o:r;return e},Y={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Z={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[y,[f,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[y,[f,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[f,y],[/opios[\/ ]+([\w\.]+)/i],[y,[f,L+" Mini"]],[/\bopr\/([\w\.]+)/i],[y,[f,L]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[f,y],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[y,[f,"UC"+P]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[y,[f,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[y,[f,"WeChat"]],[/konqueror\/([\w\.]+)/i],[y,[f,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[y,[f,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[y,[f,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[f,/(.+)/,"$1 Secure "+P],y],[/\bfocus\/([\w\.]+)/i],[y,[f,R+" Focus"]],[/\bopt\/([\w\.]+)/i],[y,[f,L+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[y,[f,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[y,[f,"Dolphin"]],[/coast\/([\w\.]+)/i],[y,[f,L+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[y,[f,"MIUI "+P]],[/fxios\/([-\w\.]+)/i],[y,[f,R]],[/\bqihu|(qi?ho?o?|360)browser/i],[[f,"360 "+P]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[f,/(.+)/,"$1 "+P],y],[/(comodo_dragon)\/([\w\.]+)/i],[[f,/_/g," "],y],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[f,y],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[f],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[f,z],y],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[f,y],[/\bgsa\/([\w\.]+) .*safari\//i],[y,[f,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[y,[f,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[y,[f,I+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[f,I+" WebView"],y],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[y,[f,"Android "+P]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[f,y],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[y,[f,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[y,f],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[f,[y,Q,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[f,y],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[f,"Netscape"],y],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[y,[f,R+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[f,y],[/(cobalt)\/([\w\.]+)/i],[f,[y,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[v,"amd64"]],[/(ia32(?=;))/i],[[v,W]],[/((?:i[346]|x)86)[;\)]/i],[[v,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[v,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[v,"armhf"]],[/windows (ce|mobile); ppc;/i],[[v,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[v,/ower/,"",W]],[/(sun4\w)[;\)]/i],[[v,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[v,W]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[p,[m,D],[g,_]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[p,[m,D],[g,w]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[p,[m,E],[g,w]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[p,[m,E],[g,_]],[/(macintosh);/i],[p,[m,E]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[p,[m,j],[g,w]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[p,[m,A],[g,_]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[p,[m,A],[g,w]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[p,/_/g," "],[m,B],[g,w]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[p,/_/g," "],[m,B],[g,_]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[p,[m,"OPPO"],[g,w]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[p,[m,"Vivo"],[g,w]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[p,[m,"Realme"],[g,w]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[p,[m,U],[g,w]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[p,[m,U],[g,_]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[p,[m,"LG"],[g,_]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[p,[m,"LG"],[g,w]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[p,[m,"Lenovo"],[g,_]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[p,/_/g," "],[m,"Nokia"],[g,w]],[/(pixel c)\b/i],[p,[m,N],[g,_]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[p,[m,N],[g,w]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[p,[m,q],[g,w]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[p,"Xperia Tablet"],[m,q],[g,_]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[p,[m,"OnePlus"],[g,w]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[p,[m,T],[g,_]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[p,/(.+)/g,"Fire Phone $1"],[m,T],[g,w]],[/(playbook);[-\w\),; ]+(rim)/i],[p,m,[g,_]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[p,[m,O],[g,w]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[p,[m,C],[g,_]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[p,[m,C],[g,w]],[/(nexus 9)/i],[p,[m,"HTC"],[g,_]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[m,[p,/_/g," "],[g,w]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[p,[m,"Acer"],[g,_]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[p,[m,"Meizu"],[g,w]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[m,p,[g,w]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[m,p,[g,_]],[/(surface duo)/i],[p,[m,M],[g,_]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[p,[m,"Fairphone"],[g,w]],[/(u304aa)/i],[p,[m,"AT&T"],[g,w]],[/\bsie-(\w*)/i],[p,[m,"Siemens"],[g,w]],[/\b(rct\w+) b/i],[p,[m,"RCA"],[g,_]],[/\b(venue[\d ]{2,7}) b/i],[p,[m,"Dell"],[g,_]],[/\b(q(?:mv|ta)\w+) b/i],[p,[m,"Verizon"],[g,_]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[p,[m,"Barnes & Noble"],[g,_]],[/\b(tm\d{3}\w+) b/i],[p,[m,"NuVision"],[g,_]],[/\b(k88) b/i],[p,[m,"ZTE"],[g,_]],[/\b(nx\d{3}j) b/i],[p,[m,"ZTE"],[g,w]],[/\b(gen\d{3}) b.+49h/i],[p,[m,"Swiss"],[g,w]],[/\b(zur\d{3}) b/i],[p,[m,"Swiss"],[g,_]],[/\b((zeki)?tb.*\b) b/i],[p,[m,"Zeki"],[g,_]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[m,"Dragon Touch"],p,[g,_]],[/\b(ns-?\w{0,9}) b/i],[p,[m,"Insignia"],[g,_]],[/\b((nxa|next)-?\w{0,9}) b/i],[p,[m,"NextBook"],[g,_]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[m,"Voice"],p,[g,w]],[/\b(lvtel\-)?(v1[12]) b/i],[[m,"LvTel"],p,[g,w]],[/\b(ph-1) /i],[p,[m,"Essential"],[g,w]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[p,[m,"Envizen"],[g,_]],[/\b(trio[-\w\. ]+) b/i],[p,[m,"MachSpeed"],[g,_]],[/\btu_(1491) b/i],[p,[m,"Rotor"],[g,_]],[/(shield[\w ]+) b/i],[p,[m,"Nvidia"],[g,_]],[/(sprint) (\w+)/i],[m,p,[g,w]],[/(kin\.[onetw]{3})/i],[[p,/\./g," "],[m,M],[g,w]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[p,[m,H],[g,_]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[p,[m,H],[g,w]],[/smart-tv.+(samsung)/i],[m,[g,S]],[/hbbtv.+maple;(\d+)/i],[[p,/^/,"SmartTV"],[m,D],[g,S]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[m,"LG"],[g,S]],[/(apple) ?tv/i],[m,[p,E+" TV"],[g,S]],[/crkey/i],[[p,I+"cast"],[m,N],[g,S]],[/droid.+aft(\w)( bui|\))/i],[p,[m,T],[g,S]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[p,[m,j],[g,S]],[/(bravia[\w ]+)( bui|\))/i],[p,[m,q],[g,S]],[/(mitv-\w{5}) bui/i],[p,[m,B],[g,S]],[/Hbbtv.*(technisat) (.*);/i],[m,p,[g,S]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[m,G],[p,G],[g,S]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[g,S]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[m,p,[g,b]],[/droid.+; (shield) bui/i],[p,[m,"Nvidia"],[g,b]],[/(playstation [345portablevi]+)/i],[p,[m,q],[g,b]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[p,[m,M],[g,b]],[/((pebble))app/i],[m,p,[g,k]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[p,[m,E],[g,k]],[/droid.+; (glass) \d/i],[p,[m,N],[g,k]],[/droid.+; (wt63?0{2,3})\)/i],[p,[m,H],[g,k]],[/(quest( 2| pro)?)/i],[p,[m,z],[g,k]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[m,[g,x]],[/(aeobc)\b/i],[p,[m,T],[g,x]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[p,[g,w]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[p,[g,_]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[g,_]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[g,w]],[/(android[-\w\. ]{0,9});.+buil/i],[p,[m,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[y,[f,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[y,[f,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[f,y],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[y,f]],os:[[/microsoft (windows) (vista|xp)/i],[f,y],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[f,[y,Q,Y]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[f,"Windows"],[y,Q,Y]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[y,/_/g,"."],[f,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[f,K],[y,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[y,f],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[f,y],[/\(bb(10);/i],[y,[f,O]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[y,[f,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[y,[f,R+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[y,[f,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[y,[f,"watchOS"]],[/crkey\/([\d\.]+)/i],[y,[f,I+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[f,$],y],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[f,y],[/(sunos) ?([\w\.\d]*)/i],[[f,"Solaris"],y],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[f,y]]},ee=function(e,t){if(typeof e===u&&(t=e,e=o),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof s!==c&&s.navigator?s.navigator:o,n=e||(r&&r.userAgent?r.userAgent:""),i=r&&r.userAgentData?r.userAgentData:o,a=t?F(Z,t):Z,b=r&&r.userAgent==n;return this.getBrowser=function(){var e,t={};return t[f]=o,t[y]=o,X.call(t,n,a.browser),t[h]=typeof(e=t[y])===d?e.replace(/[^\d\.]/g,"").split(".")[0]:o,b&&r&&r.brave&&typeof r.brave.isBrave==l&&(t[f]="Brave"),t},this.getCPU=function(){var e={};return e[v]=o,X.call(e,n,a.cpu),e},this.getDevice=function(){var e={};return e[m]=o,e[p]=o,e[g]=o,X.call(e,n,a.device),b&&!e[g]&&i&&i.mobile&&(e[g]=w),b&&"Macintosh"==e[p]&&r&&typeof r.standalone!==c&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[p]="iPad",e[g]=_),e},this.getEngine=function(){var e={};return e[f]=o,e[y]=o,X.call(e,n,a.engine),e},this.getOS=function(){var e={};return e[f]=o,e[y]=o,X.call(e,n,a.os),b&&!e[f]&&i&&"Unknown"!=i.platform&&(e[f]=i.platform.replace(/chrome os/i,$).replace(/macos/i,K)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(e){return n=typeof e===d&&e.length>350?G(e,350):e,this},this.setUA(n),this};ee.VERSION="1.0.35",ee.BROWSER=V([f,y,h]),ee.CPU=V([v]),ee.DEVICE=V([p,m,g,b,w,S,_,k,x]),ee.ENGINE=ee.OS=V([f,y]),typeof a!==c?(i.exports&&(a=i.exports=ee),a.UAParser=ee):r.amdO?void 0!==(n=(function(){return ee}).call(t,r,t,e))&&(e.exports=n):typeof s!==c&&(s.UAParser=ee);var et=typeof s!==c&&(s.jQuery||s.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},a={};function s(e){var t=a[e];if(void 0!==t)return t.exports;var r=a[e]={exports:{}},n=!0;try{i[e].call(r.exports,r,r.exports,s),n=!1}finally{n&&delete a[e]}return r.exports}s.ab="//";var o=s(226);e.exports=o})()},4075:(e,t,r)=>{"use strict";r.r(t),r.d(t,{cookies:()=>m,draftMode:()=>y,headers:()=>g});var n=r(8009),i=r(7862),a=r(938),s=r(1959),o=r(6865),l=r(5023);class c extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest="DYNAMIC_SERVER_USAGE"}}class u extends Error{constructor(...e){super(...e),this.code="NEXT_STATIC_GEN_BAILOUT"}}let d="function"==typeof l.unstable_postpone;function h(e,t){let r=new URL(e.urlPathname,"http://n").pathname;if(e.isUnstableCacheCallback)throw Error(`Route ${r} used "${t}" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "${t}" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`);if(e.dynamicShouldError)throw new u(`Route ${r} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${t}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);if(e.prerenderState)!function(e,t,r){!function(){if(!d)throw Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js")}();let n=`Route ${r} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`;e.dynamicAccesses.push({stack:e.isDebugSkeleton?Error().stack:void 0,expression:t}),l.unstable_postpone(n)}(e.prerenderState,t,r);else if(e.revalidate=0,e.isStaticGeneration){let n=new c(`Route ${r} couldn't be rendered statically because it used \`${t}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`);throw e.dynamicUsageDescription=t,e.dynamicUsageStack=n.stack,n}}class p{get isEnabled(){return this._provider.isEnabled}enable(){let e=o.A.getStore();return e&&h(e,"draftMode().enable()"),this._provider.enable()}disable(){let e=o.A.getStore();return e&&h(e,"draftMode().disable()"),this._provider.disable()}constructor(e){this._provider=e}}var f=r(6101);function g(){let e="headers",t=o.A.getStore();if(t){if(t.forceStatic)return i.h.seal(new Headers({}));h(t,e)}return(0,f.F)(e).headers}function m(){let e="cookies",t=o.A.getStore();if(t){if(t.forceStatic)return n.Qb.seal(new a.qC(new Headers({})));h(t,e)}let r=(0,f.F)(e),i=s.W.getStore();return(null==i?void 0:i.isAction)||(null==i?void 0:i.isAppRoute)?r.mutableCookies:r.cookies}function y(){return new p((0,f.F)("draftMode").draftMode)}},6101:(e,t,r)=>{"use strict";r.d(t,{F:()=>i,O:()=>n.F});var n=r(5303);function i(e){let t=n.F.getStore();if(t)return t;throw Error("`"+e+"` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context")}},6865:(e,t,r)=>{"use strict";r.d(t,{A:()=>n.A});var n=r(5558)},7862:(e,t,r)=>{"use strict";r.d(t,{h:()=>a});var n=r(7217);class i extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new i}}class a extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,i){if("symbol"==typeof r)return n.g.get(t,r,i);let a=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===a);if(void 0!==s)return n.g.get(t,s,i)},set(t,r,i,a){if("symbol"==typeof r)return n.g.set(t,r,i,a);let s=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===s);return n.g.set(t,o??r,i,a)},has(t,r){if("symbol"==typeof r)return n.g.has(t,r);let i=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==a&&n.g.has(t,a)},deleteProperty(t,r){if("symbol"==typeof r)return n.g.deleteProperty(t,r);let i=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===a||n.g.deleteProperty(t,a)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return i.callable;default:return n.g.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new a(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},7217:(e,t,r)=>{"use strict";r.d(t,{g:()=>n});class n{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},8009:(e,t,r)=>{"use strict";r.d(t,{Qb:()=>o,vr:()=>c});var n=r(938),i=r(7217),a=r(6865);class s extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new s}}class o{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return s.callable;default:return i.g.get(e,t,r)}}})}}let l=Symbol.for("next.mutated.cookies");class c{static wrap(e,t){let r=new n.nV(new Headers);for(let t of e.getAll())r.set(t);let s=[],o=new Set,c=()=>{let e=a.A.getStore();if(e&&(e.pathWasRevalidated=!0),s=r.getAll().filter(e=>o.has(e.name)),t){let e=[];for(let t of s){let r=new n.nV(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case l:return s;case"delete":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{c()}};case"set":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{c()}};default:return i.g.get(e,t,r)}}})}}},938:(e,t,r)=>{"use strict";r.d(t,{Q7:()=>n.stringifyCookie,nV:()=>n.ResponseCookies,qC:()=>n.RequestCookies});var n=r(5945)},8488:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getTestReqInfo:function(){return s},withRequest:function(){return a}});let n=new(r(2067)).AsyncLocalStorage;function i(e,t){let r=t.header(e,"next-test-proxy-port");if(r)return{url:t.url(e),proxyPort:Number(r),testData:t.header(e,"next-test-data")||""}}function a(e,t,r){let a=i(e,t);return a?n.run(a,r):r()}function s(e,t){return n.getStore()||(e&&t?i(e,t):void 0)}},375:(e,t,r)=>{"use strict";var n=r(6195).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleFetch:function(){return o},interceptFetch:function(){return l},reader:function(){return a}});let i=r(8488),a={url:e=>e.url,header:(e,t)=>e.headers.get(t)};async function s(e,t){let{url:r,method:i,headers:a,body:s,cache:o,credentials:l,integrity:c,mode:u,redirect:d,referrer:h,referrerPolicy:p}=t;return{testData:e,api:"fetch",request:{url:r,method:i,headers:[...Array.from(a),["next-test-stack",function(){let e=(Error().stack??"").split("\n");for(let t=1;t<e.length;t++)if(e[t].length>0){e=e.slice(t);break}return(e=(e=(e=e.filter(e=>!e.includes("/next/dist/"))).slice(0,5)).map(e=>e.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:s?n.from(await t.arrayBuffer()).toString("base64"):null,cache:o,credentials:l,integrity:c,mode:u,redirect:d,referrer:h,referrerPolicy:p}}}async function o(e,t){let r=(0,i.getTestReqInfo)(t,a);if(!r)return e(t);let{testData:o,proxyPort:l}=r,c=await s(o,t),u=await e(`http://localhost:${l}`,{method:"POST",body:JSON.stringify(c),next:{internal:!0}});if(!u.ok)throw Error(`Proxy request failed: ${u.status}`);let d=await u.json(),{api:h}=d;switch(h){case"continue":return e(t);case"abort":case"unhandled":throw Error(`Proxy request aborted [${t.method} ${t.url}]`)}return function(e){let{status:t,headers:r,body:i}=e.response;return new Response(i?n.from(i,"base64"):null,{status:t,headers:new Headers(r)})}(d)}function l(e){return r.g.fetch=function(t,r){var n;return(null==r?void 0:null==(n=r.next)?void 0:n.internal)?e(t,r):o(e,new Request(t,r))},()=>{r.g.fetch=e}}},4177:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{interceptTestApis:function(){return a},wrapRequestHandler:function(){return s}});let n=r(8488),i=r(375);function a(){return(0,i.interceptFetch)(r.g.fetch)}function s(e){return(t,r)=>(0,n.withRequest)(t,i.reader,()=>e(t,r))}},4835:(e,t)=>{"use strict";var r=Symbol.for("react.element"),n=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),o=Symbol.for("react.provider"),l=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),p=Symbol.iterator,f={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},g=Object.assign,m={};function y(e,t,r){this.props=e,this.context=t,this.refs=m,this.updater=r||f}function v(){}function b(e,t,r){this.props=e,this.context=t,this.refs=m,this.updater=r||f}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var w=b.prototype=new v;w.constructor=b,g(w,y.prototype),w.isPureReactComponent=!0;var _=Array.isArray,S=Object.prototype.hasOwnProperty,k={current:null},x={key:!0,ref:!0,__self:!0,__source:!0};function T(e,t,n){var i,a={},s=null,o=null;if(null!=t)for(i in void 0!==t.ref&&(o=t.ref),void 0!==t.key&&(s=""+t.key),t)S.call(t,i)&&!x.hasOwnProperty(i)&&(a[i]=t[i]);var l=arguments.length-2;if(1===l)a.children=n;else if(1<l){for(var c=Array(l),u=0;u<l;u++)c[u]=arguments[u+2];a.children=c}if(e&&e.defaultProps)for(i in l=e.defaultProps)void 0===a[i]&&(a[i]=l[i]);return{$$typeof:r,type:e,key:s,ref:o,props:a,_owner:k.current}}function E(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}var C=/\/+/g;function O(e,t){var r,n;return"object"==typeof e&&null!==e&&null!=e.key?(r=""+e.key,n={"=":"=0",":":"=2"},"$"+r.replace(/[=:]/g,function(e){return n[e]})):t.toString(36)}function P(e,t,i){if(null==e)return e;var a=[],s=0;return!function e(t,i,a,s,o){var l,c,u,d=typeof t;("undefined"===d||"boolean"===d)&&(t=null);var h=!1;if(null===t)h=!0;else switch(d){case"string":case"number":h=!0;break;case"object":switch(t.$$typeof){case r:case n:h=!0}}if(h)return o=o(h=t),t=""===s?"."+O(h,0):s,_(o)?(a="",null!=t&&(a=t.replace(C,"$&/")+"/"),e(o,i,a,"",function(e){return e})):null!=o&&(E(o)&&(l=o,c=a+(!o.key||h&&h.key===o.key?"":(""+o.key).replace(C,"$&/")+"/")+t,o={$$typeof:r,type:l.type,key:c,ref:l.ref,props:l.props,_owner:l._owner}),i.push(o)),1;if(h=0,s=""===s?".":s+":",_(t))for(var f=0;f<t.length;f++){var g=s+O(d=t[f],f);h+=e(d,i,a,g,o)}else if("function"==typeof(g=null===(u=t)||"object"!=typeof u?null:"function"==typeof(u=p&&u[p]||u["@@iterator"])?u:null))for(t=g.call(t),f=0;!(d=t.next()).done;)g=s+O(d=d.value,f++),h+=e(d,i,a,g,o);else if("object"===d)throw Error("Objects are not valid as a React child (found: "+("[object Object]"===(i=String(t))?"object with keys {"+Object.keys(t).join(", ")+"}":i)+"). If you meant to render a collection of children, use an array instead.");return h}(e,a,"","",function(e){return t.call(i,e,s++)}),a}function I(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){(0===e._status||-1===e._status)&&(e._status=1,e._result=t)},function(t){(0===e._status||-1===e._status)&&(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var R={current:null},N={transition:null};function A(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:P,forEach:function(e,t,r){P(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return P(e,function(){t++}),t},toArray:function(e){return P(e,function(e){return e})||[]},only:function(e){if(!E(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=i,t.Profiler=s,t.PureComponent=b,t.StrictMode=a,t.Suspense=u,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED={ReactCurrentDispatcher:R,ReactCurrentBatchConfig:N,ReactCurrentOwner:k},t.act=A,t.cloneElement=function(e,t,n){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var i=g({},e.props),a=e.key,s=e.ref,o=e._owner;if(null!=t){if(void 0!==t.ref&&(s=t.ref,o=k.current),void 0!==t.key&&(a=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(c in t)S.call(t,c)&&!x.hasOwnProperty(c)&&(i[c]=void 0===t[c]&&void 0!==l?l[c]:t[c])}var c=arguments.length-2;if(1===c)i.children=n;else if(1<c){l=Array(c);for(var u=0;u<c;u++)l[u]=arguments[u+2];i.children=l}return{$$typeof:r,type:e.type,key:a,ref:s,props:i,_owner:o}},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:o,_context:e},e.Consumer=e},t.createElement=T,t.createFactory=function(e){var t=T.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=E,t.lazy=function(e){return{$$typeof:h,_payload:{_status:-1,_result:e},_init:I}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=N.transition;N.transition={};try{e()}finally{N.transition=t}},t.unstable_act=A,t.useCallback=function(e,t){return R.current.useCallback(e,t)},t.useContext=function(e){return R.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return R.current.useDeferredValue(e)},t.useEffect=function(e,t){return R.current.useEffect(e,t)},t.useId=function(){return R.current.useId()},t.useImperativeHandle=function(e,t,r){return R.current.useImperativeHandle(e,t,r)},t.useInsertionEffect=function(e,t){return R.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return R.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return R.current.useMemo(e,t)},t.useReducer=function(e,t,r){return R.current.useReducer(e,t,r)},t.useRef=function(e){return R.current.useRef(e)},t.useState=function(e){return R.current.useState(e)},t.useSyncExternalStore=function(e,t,r){return R.current.useSyncExternalStore(e,t,r)},t.useTransition=function(){return R.current.useTransition()},t.version="18.3.1"},5023:(e,t,r)=>{"use strict";e.exports=r(4835)},2514:(e,t,r)=>{"use strict";r.r(t),r.d(t,{snakeCase:()=>l});var n=function(){return(n=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function i(e){return e.toLowerCase()}Object.create,Object.create;var a=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],s=/[^A-Z0-9]+/gi;function o(e,t,r){return t instanceof RegExp?e.replace(t,r):t.reduce(function(e,t){return e.replace(t,r)},e)}function l(e,t){var r;return void 0===t&&(t={}),void 0===(r=n({delimiter:"_"},t))&&(r={}),function(e,t){void 0===t&&(t={});for(var r=t.splitRegexp,n=t.stripRegexp,l=t.transform,c=t.delimiter,u=o(o(e,void 0===r?a:r,"$1\0$2"),void 0===n?s:n,"\0"),d=0,h=u.length;"\0"===u.charAt(d);)d++;for(;"\0"===u.charAt(h-1);)h--;return u.slice(d,h).split("\0").map(void 0===l?i:l).join(void 0===c?" ":c)}(e,n({delimiter:"."},r))}},4264:(e,t,r)=>{"use strict";let n=r(106),{snakeCase:i}=r(2514);e.exports=function(e,t){return n(e,function(e,r){return[t.exclude.some(function(t){return"string"==typeof t?t===e:t.test(e)})?e:i(e,t.parsingOptions),r]},t=Object.assign({deep:!0,exclude:[],parsingOptions:{}},t))}},1959:(e,t,r)=>{"use strict";r.d(t,{W:()=>n});let n=(0,r(5228).P)()},5228:(e,t,r)=>{"use strict";r.d(t,{P:()=>s});let n=Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available");class i{disable(){throw n}getStore(){}run(){throw n}exit(){throw n}enterWith(){throw n}}let a=globalThis.AsyncLocalStorage;function s(){return a?new a:new i}},5303:(e,t,r)=>{"use strict";r.d(t,{F:()=>n});let n=(0,r(5228).P)()},5558:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(5228).P)()}},e=>{var t=e(e.s=7784);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_src/middleware"]=t}]);
//# sourceMappingURL=middleware.js.map