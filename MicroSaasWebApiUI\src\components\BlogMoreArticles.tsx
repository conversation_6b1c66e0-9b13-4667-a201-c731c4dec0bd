import Image from 'next/image'
import Link from 'next/link'

import { convertToReadableDate } from '@/utils/functions'
import AdManager from './AdManager'

const BlogMoreArticles = ({ currentBlog, AllPosts }: any) => {
	const filteredPosts = AllPosts.filter(
		(post: any) => post.title.rendered !== currentBlog
	)
	return (
		<div className='w-full max-w-[90rem] mx-auto px-4 sm:px-6 lg:px-8 mb-6'>
			<div className='flex justify-between'>
				<div className='flex-1 mx-auto max-w-5xl pr-8'>
					<h2 className='text-4xl font-bold mb-6 text-left'>Other Articles</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						{filteredPosts.slice(0, 4).map((post: any, index: any) => (
							<Link
								href={`/blog/${post.slug}`}
								key={index}
								className='bg-white dark:bg-[#1E232C] rounded-xl overflow-hidden shadow-lg dark:border border-solid border-[#373C53] hover:border-[#5b6285] transition-all duration-500 transform hover:scale-105 ease-in-out'
							>
								<Image
									src={post.featured_img}
									alt={post.title.rendered}
									className='object-cover'
									width={500}
									height={156}
								/>
								<div className='p-4'>
									<h2 className='font-normal text-[16px] dark:text-white mb-2'>
										{post.title.rendered}
									</h2>
									<p className='text-gray-400'>
										{convertToReadableDate(post.date)}
									</p>
								</div>
							</Link>
						))}
					</div>
				</div>

				{/* Right sidebar space for alignment */}
				<div className='hidden lg:block w-[320px]'>
					{/* Empty space to match the layout in BlogDetails */}
				</div>
			</div>

			{/* Horizontal ad unit after blog posts */}
			<div className="mt-6 max-w-5xl mx-auto">
				<AdManager type="display-horizontal" className="px-0" />
			</div>
		</div>
	)
}

export default BlogMoreArticles
