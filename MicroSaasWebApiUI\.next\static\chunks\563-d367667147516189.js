(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[563],{99376:function(e,t,r){"use strict";var n=r(35475);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},40257:function(e,t,r){"use strict";var n,i;e.exports=(null==(n=r.g.process)?void 0:n.env)&&"object"==typeof(null==(i=r.g.process)?void 0:i.env)?r.g.process:r(44227)},44227:function(e){!function(){var t={229:function(e){var t,r,n,i=e.exports={};function o(){throw Error("setTimeout has not been defined")}function s(){throw Error("clearTimeout has not been defined")}function a(e){if(t===setTimeout)return setTimeout(e,0);if((t===o||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(r){try{return t.call(null,e,0)}catch(r){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:o}catch(e){t=o}try{r="function"==typeof clearTimeout?clearTimeout:s}catch(e){r=s}}();var l=[],u=!1,c=-1;function d(){u&&n&&(u=!1,n.length?l=n.concat(l):c=-1,l.length&&h())}function h(){if(!u){var e=a(d);u=!0;for(var t=l.length;t;){for(n=l,l=[];++c<t;)n&&n[c].run();c=-1,t=l.length}n=null,u=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===s||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function p(e,t){this.fun=e,this.array=t}function f(){}i.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];l.push(new p(e,t)),1!==l.length||u||a(h)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=f,i.addListener=f,i.once=f,i.off=f,i.removeListener=f,i.removeAllListeners=f,i.emit=f,i.prependListener=f,i.prependOnceListener=f,i.listeners=function(e){return[]},i.binding=function(e){throw Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw Error("process.chdir is not supported")},i.umask=function(){return 0}}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var o=r[e]={exports:{}},s=!0;try{t[e](o,o.exports,n),s=!1}finally{s&&delete r[e]}return o.exports}n.ab="//";var i=n(229);e.exports=i}()},24369:function(e,t,r){"use strict";var n=r(2265),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=n.useState,s=n.useEffect,a=n.useLayoutEffect,l=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=o({inst:{value:r,getSnapshot:t}}),i=n[0].inst,c=n[1];return a(function(){i.value=r,i.getSnapshot=t,u(i)&&c({inst:i})},[e,r,t]),s(function(){return u(i)&&c({inst:i}),e(function(){u(i)&&c({inst:i})})},[e]),l(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:c},82558:function(e,t,r){"use strict";e.exports=r(24369)},76548:function(e,t,r){"use strict";r.d(t,{Vo:function(){return P},vn:function(){return el},a7:function(){return et},qI:function(){return er},iz:function(){return C},gM:function(){return ea},yB:function(){return es},N1:function(){return en},C2:function(){return ei},sO:function(){return eo},Gl:function(){return A},tL:function(){return W},oG:function(){return R},E7:function(){return T},RM:function(){return w},RE:function(){return N},tF:function(){return U},KQ:function(){return O},Gv:function(){return x},Rl:function(){return I},ej:function(){return L},JM:function(){return S},qq:function(){return j},aC:function(){return H},cL:function(){return b.cL},E2:function(){return Q},o8:function(){return b.o8},eW:function(){return b.eW},kP:function(){return b.kP},xo:function(){return b.xo},zq:function(){return X},QS:function(){return Z},aF:function(){return b.aF},Kr:function(){return V},B$:function(){return D},HZ:function(){return $},Lu:function(){return J},X:function(){return F},qH:function(){return B},Mp:function(){return z},ge:function(){return M},r0:function(){return ee}});var n,i,o,s,a=r(22605),l=(r(84301),{veryStrict:{afterMinutes:10,level:"multiFactor"},strict:{afterMinutes:10,level:"secondFactor"},moderate:{afterMinutes:60,level:"secondFactor"},lax:{afterMinutes:1440,level:"secondFactor"}}),u=new Set(["firstFactor","secondFactor","multiFactor"]),c=new Set(["veryStrict","strict","moderate","lax"]),d=e=>"number"==typeof e&&e>0,h=e=>u.has(e),p=e=>c.has(e),f=(e,t)=>{let{orgId:r,orgRole:n,orgPermissions:i}=t;return(e.role||e.permission)&&r&&n&&i?e.permission?i.includes(e.permission):e.role?n===e.role:null:null},g=e=>!!("string"==typeof e&&p(e)||"object"==typeof e&&h(e.level)&&d(e.afterMinutes))&&(e=>"string"==typeof e?l[e]:e).bind(null,e),m=(e,{__experimental_factorVerificationAge:t})=>{if(!e.__experimental_reverification||!t)return null;let r=g(e.__experimental_reverification);if(!r)return null;let{level:n,afterMinutes:i}=r(),[o,s]=t,a=-1!==o?i>o:null,l=-1!==s?i>s:null;switch(n){case"firstFactor":return a;case"secondFactor":return -1!==s?l:a;case"multiFactor":return -1===s?a:a&&l}},v=e=>t=>{if(!e.userId)return!1;let r=f(t,e),n=m(t,e);return[r,n].some(e=>null===e)?[r,n].some(e=>!0===e):[r,n].every(e=>!0===e)},k=r(2265),b=r(6627),y=r(10684);r(70040),r(40257),new WeakMap,new WeakMap,new WeakSet,new WeakMap,new WeakMap,new WeakMap,new WeakMap,new WeakMap,new WeakSet;var w=(0,a.t5)({packageName:"@clerk/clerk-react"});function S(e){w.setMessages(e).setPackageName(e)}var[P,E]=(0,b.uH)("AuthContext"),C=b.b5,_=b.rI,O="You've added multiple <ClerkProvider> components in your React component tree. Wrap your components in a single <ClerkProvider>.",U=e=>`You've passed multiple children components to <${e}/>. You can only pass a single child component or text.`,j="Unsupported usage of isSatellite, domain or proxyUrl. The usage of isSatellite, domain or proxyUrl as function is not supported in non-browser environments.",M="<UserProfile.Page /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",z="<UserProfile.Link /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",L="<OrganizationProfile.Page /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",I="<OrganizationProfile.Link /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",T=e=>`<${e} /> can only accept <${e}.Page /> and <${e}.Link /> as its children. Any other provided component will be ignored.`,R=e=>`Missing props. <${e}.Page /> component requires the following props: url, label, labelIcon, alongside with children to be rendered inside the page.`,A=e=>`Missing props. <${e}.Link /> component requires the following props: url, label and labelIcon.`,x=e=>`The <${e}/> component uses path-based routing by default unless a different routing strategy is provided using the \`routing\` prop. When path-based routing is used, you need to provide the path where the component is mounted on by using the \`path\` prop. Example: <${e} path={'/my-path'} />`,N=e=>`The \`path\` prop will only be respected when the Clerk component uses path-based routing. To resolve this error, pass \`routing='path'\` to the <${e}/> component, or drop the \`path\` prop to switch to hash-based routing. For more details please refer to our docs: https://clerk.com/docs`,V="<UserButton /> can only accept <UserButton.UserProfilePage />, <UserButton.UserProfileLink /> and <UserButton.MenuItems /> as its children. Any other provided component will be ignored.",W="<UserButton.MenuItems /> component can only accept <UserButton.Action /> and <UserButton.Link /> as its children. Any other provided component will be ignored.",F="<UserButton.MenuItems /> component needs to be a direct child of `<UserButton />`.",D="<UserButton.Action /> component needs to be a direct child of `<UserButton.MenuItems />`.",B="<UserButton.Link /> component needs to be a direct child of `<UserButton.MenuItems />`.",$="Missing props. <UserButton.Link /> component requires the following props: href, label and labelIcon.",J="Missing props. <UserButton.Action /> component requires the following props: label.",q=e=>{(0,b.Rm)(()=>{w.throwMissingClerkProviderError({source:e})})},G=e=>new Promise(t=>{e.loaded&&t(),e.addOnLoaded(t)}),K=e=>async t=>(await G(e),e.session)?e.session.getToken(t):null,Y=e=>async(...t)=>(await G(e),e.signOut(...t)),H=()=>{q("useAuth");let{sessionId:e,userId:t,actor:r,orgId:n,orgRole:i,orgSlug:o,orgPermissions:s,__experimental_factorVerificationAge:a}=E(),l=_(),u=(0,k.useCallback)(K(l),[l]),c=(0,k.useCallback)(Y(l),[l]),d=(0,k.useCallback)(e=>v({userId:t,orgId:n,orgRole:i,orgPermissions:s,__experimental_factorVerificationAge:a})(e),[t,a,n,i,s]);return void 0===e&&void 0===t?{isLoaded:!1,isSignedIn:void 0,sessionId:e,userId:t,actor:void 0,orgId:void 0,orgRole:void 0,orgSlug:void 0,has:void 0,signOut:c,getToken:u}:null===e&&null===t?{isLoaded:!0,isSignedIn:!1,sessionId:e,userId:t,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:c,getToken:u}:e&&t&&n&&i?{isLoaded:!0,isSignedIn:!0,sessionId:e,userId:t,actor:r||null,orgId:n,orgRole:i,orgSlug:o||null,has:d,signOut:c,getToken:u}:e&&t&&!n?{isLoaded:!0,isSignedIn:!0,sessionId:e,userId:t,actor:r||null,orgId:null,orgRole:null,orgSlug:null,has:d,signOut:c,getToken:u}:w.throw("Invalid state. Feel free to submit a bug or reach out to support here: https://clerk.com/support")};function Q(e){let{startEmailLinkFlow:t,cancelEmailLinkFlow:r}=k.useMemo(()=>e.createEmailLinkFlow(),[e]);return k.useEffect(()=>r,[]),{startEmailLinkFlow:t,cancelEmailLinkFlow:r}}var X=()=>{var e;q("useSignIn");let t=_(),r=(0,b.sX)();return(null==(e=t.telemetry)||e.record((0,y.J)("useSignIn")),r)?{isLoaded:!0,signIn:r.signIn,setActive:t.setActive}:{isLoaded:!1,signIn:void 0,setActive:void 0}},Z=()=>{var e;q("useSignUp");let t=_(),r=(0,b.sX)();return(null==(e=t.telemetry)||e.record((0,y.J)("useSignUp")),r)?{isLoaded:!0,signUp:r.signUp,setActive:t.setActive}:{isLoaded:!1,signUp:void 0,setActive:void 0}},ee=(e,t)=>{t=t||e.displayName||e.name||"Component",e.displayName=t;let r=r=>{q(t||"withClerk");let n=_();return n.loaded?k.createElement(e,{...r,clerk:n}):null};return r.displayName=`withClerk(${t})`,r},et=({children:e})=>(q("ClerkLoaded"),_().loaded)?k.createElement(k.Fragment,null,e):null,er=({children:e})=>(q("ClerkLoading"),_().loaded)?null:k.createElement(k.Fragment,null,e),en=ee(({clerk:e,...t})=>{let{client:r,session:n}=e,i=r.activeSessions&&r.activeSessions.length>0;return k.useEffect(()=>{null===n&&i?e.redirectToAfterSignOut():e.redirectToSignIn(t)},[]),null},"RedirectToSignIn"),ei=ee(({clerk:e,...t})=>(k.useEffect(()=>{e.redirectToSignUp(t)},[]),null),"RedirectToSignUp"),eo=ee(({clerk:e})=>(k.useEffect(()=>{e.redirectToUserProfile()},[]),null),"RedirectToUserProfile"),es=ee(({clerk:e})=>(k.useEffect(()=>{e.redirectToOrganizationProfile()},[]),null),"RedirectToOrganizationProfile"),ea=ee(({clerk:e})=>(k.useEffect(()=>{e.redirectToCreateOrganization()},[]),null),"RedirectToCreateOrganization"),el=ee(({clerk:e,...t})=>(k.useEffect(()=>{e.handleRedirectCallback(t)},[]),null),"AuthenticateWithRedirectCallback")},87770:function(e,t,r){"use strict";r.d(t,{vn:function(){return c.vn},a7:function(){return c.a7},qI:function(){return c.qI},El:function(){return eS},Gp:function(){return es},Kb:function(){return ec},Bg:function(){return eu},A:function(){return eo},Li:function(){return el},gM:function(){return c.gM},yB:function(){return c.yB},N1:function(){return c.N1},C2:function(){return c.C2},sO:function(){return c.sO},cL:function(){return G},$d:function(){return ed},qu:function(){return ef},AM:function(){return ep},Mo:function(){return K},gX:function(){return eh},l8:function(){return er},Iw:function(){return Q},aC:function(){return c.aC},ll:function(){return c.cL},E2:function(){return c.E2},o8:function(){return c.o8},eW:function(){return c.eW},kP:function(){return c.kP},xo:function(){return c.xo},zq:function(){return c.zq},QS:function(){return c.QS},aF:function(){return c.aF}});var n,i,o,s,a,l,u,c=r(76548),d=e=>{throw TypeError(e)},h=(e,t,r)=>t.has(e)||d("Cannot "+r),p=(e,t,r)=>(h(e,t,"read from private field"),r?r.call(e):t.get(e)),f=(e,t,r)=>t.has(e)?d("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),g=(e,t,r,n)=>(h(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),m=(e,t,r)=>(h(e,t,"access private method"),r),v=r(23054),k=r(59827),b=r(6627),y=r(2265),w=r(54887),S=r(77126);r(84301);var P=r(8996);function E(e,t,r){return"function"==typeof e?e(t):void 0!==e?e:void 0!==r?r:void 0}"undefined"==typeof window||window.global||(window.global="undefined"==typeof global?window:global);var C=e=>t=>{try{return y.Children.only(e)}catch(e){return c.RM.throw((0,c.tF)(t))}},_=(e,t)=>(e||(e=t),"string"==typeof e&&(e=y.createElement("button",null,e)),e),O=e=>(...t)=>{if(e&&"function"==typeof e)return e(...t)},U=new Map,j=e=>{let t=Array(e.length).fill(null),[r,n]=(0,y.useState)(t);return e.map((e,t)=>({id:e.id,mount:e=>n(r=>r.map((r,n)=>n===t?e:r)),unmount:()=>n(e=>e.map((e,r)=>r===t?null:e)),portal:()=>y.createElement(y.Fragment,null,r[t]?(0,w.createPortal)(e.component,r[t]):null)}))},M=(e,t)=>!!e&&y.isValidElement(e)&&(null==e?void 0:e.type)===t,z=(e,t)=>T({children:e,reorderItemsLabels:["account","security"],LinkComponent:H,PageComponent:Y,MenuItemsComponent:Z,componentName:"UserProfile"},t),L=(e,t)=>T({children:e,reorderItemsLabels:["general","members"],LinkComponent:ei,PageComponent:en,componentName:"OrganizationProfile"},t),I=e=>{let t=[],r=[ei,en,Z,Y,H];return y.Children.forEach(e,e=>{r.some(t=>M(e,t))||t.push(e)}),t},T=(e,t)=>{let{children:r,LinkComponent:n,PageComponent:i,MenuItemsComponent:o,reorderItemsLabels:s,componentName:a}=e,{allowForAnyChildren:l=!1}=t||{},u=[];y.Children.forEach(r,e=>{if(!M(e,i)&&!M(e,n)&&!M(e,o)){e&&!l&&(0,k.yJ)((0,c.E7)(a));return}let{props:t}=e,{children:r,label:d,url:h,labelIcon:p}=t;if(M(e,i)){if(R(t,s))u.push({label:d});else if(A(t))u.push({label:d,labelIcon:p,children:r,url:h});else{(0,k.yJ)((0,c.oG)(a));return}}if(M(e,n)){if(x(t))u.push({label:d,labelIcon:p,url:h});else{(0,k.yJ)((0,c.Gl)(a));return}}});let d=[],h=[],p=[];u.forEach((e,t)=>{if(A(e)){d.push({component:e.children,id:t}),h.push({component:e.labelIcon,id:t});return}x(e)&&p.push({component:e.labelIcon,id:t})});let f=j(d),g=j(h),m=j(p),v=[],b=[];return u.forEach((e,t)=>{if(R(e,s)){v.push({label:e.label});return}if(A(e)){let{portal:r,mount:n,unmount:i}=f.find(e=>e.id===t),{portal:o,mount:s,unmount:a}=g.find(e=>e.id===t);v.push({label:e.label,url:e.url,mount:n,unmount:i,mountIcon:s,unmountIcon:a}),b.push(r),b.push(o);return}if(x(e)){let{portal:r,mount:n,unmount:i}=m.find(e=>e.id===t);v.push({label:e.label,url:e.url,mountIcon:n,unmountIcon:i}),b.push(r);return}}),{customPages:v,customPagesPortals:b}},R=(e,t)=>{let{children:r,label:n,url:i,labelIcon:o}=e;return!r&&!i&&!o&&t.some(e=>e===n)},A=e=>{let{children:t,label:r,url:n,labelIcon:i}=e;return!!t&&!!n&&!!i&&!!r},x=e=>{let{children:t,label:r,url:n,labelIcon:i}=e;return!t&&!!n&&!!i&&!!r},N=e=>V({children:e,reorderItemsLabels:["manageAccount","signOut"],MenuItemsComponent:Z,MenuActionComponent:ee,MenuLinkComponent:et,UserProfileLinkComponent:H,UserProfilePageComponent:Y}),V=({children:e,MenuItemsComponent:t,MenuActionComponent:r,MenuLinkComponent:n,UserProfileLinkComponent:i,UserProfilePageComponent:o,reorderItemsLabels:s})=>{let a=[],l=[],u=[];y.Children.forEach(e,e=>{if(!M(e,t)&&!M(e,i)&&!M(e,o)){e&&(0,k.yJ)(c.Kr);return}if(M(e,i)||M(e,o))return;let{props:l}=e;y.Children.forEach(l.children,e=>{if(!M(e,r)&&!M(e,n)){e&&(0,k.yJ)(c.tL);return}let{props:t}=e,{label:i,labelIcon:o,href:l,onClick:u,open:d}=t;if(M(e,r)){if(W(t,s))a.push({label:i});else if(F(t)){let e={label:i,labelIcon:o};if(void 0!==u)a.push({...e,onClick:u});else if(void 0!==d)a.push({...e,open:d.startsWith("/")?d:`/${d}`});else{(0,k.yJ)("Custom menu item must have either onClick or open property");return}}else{(0,k.yJ)(c.Lu);return}}if(M(e,n)){if(D(t))a.push({label:i,labelIcon:o,href:l});else{(0,k.yJ)(c.HZ);return}}})});let d=[],h=[];a.forEach((e,t)=>{F(e)&&d.push({component:e.labelIcon,id:t}),D(e)&&h.push({component:e.labelIcon,id:t})});let p=j(d),f=j(h);return a.forEach((e,t)=>{if(W(e,s)&&l.push({label:e.label}),F(e)){let{portal:r,mount:n,unmount:i}=p.find(e=>e.id===t),o={label:e.label,mountIcon:n,unmountIcon:i};"onClick"in e?o.onClick=e.onClick:"open"in e&&(o.open=e.open),l.push(o),u.push(r)}if(D(e)){let{portal:r,mount:n,unmount:i}=f.find(e=>e.id===t);l.push({label:e.label,href:e.href,mountIcon:n,unmountIcon:i}),u.push(r)}}),{customMenuItems:l,customMenuItemsPortals:u}},W=(e,t)=>{let{children:r,label:n,onClick:i,labelIcon:o}=e;return!r&&!i&&!o&&t.some(e=>e===n)},F=e=>{let{label:t,labelIcon:r,onClick:n,open:i}=e;return!!r&&!!t&&("function"==typeof n||"string"==typeof i)},D=e=>{let{label:t,href:r,labelIcon:n}=e;return!!r&&!!n&&!!t},B=e=>"mount"in e,$=e=>"open"in e,J=class extends y.PureComponent{constructor(){super(...arguments),this.portalRef=y.createRef()}componentDidUpdate(e){var t,r,n,i;if(!B(e)||!B(this.props))return;let o=(0,k.zu)(e.props,"customPages","customMenuItems","children"),s=(0,k.zu)(this.props.props,"customPages","customMenuItems","children"),a=(null==(t=o.customPages)?void 0:t.length)!==(null==(r=s.customPages)?void 0:r.length),l=(null==(n=o.customMenuItems)?void 0:n.length)!==(null==(i=s.customMenuItems)?void 0:i.length);(!(0,b.c1)(o,s)||a||l)&&this.portalRef.current&&this.props.updateProps({node:this.portalRef.current,props:this.props.props})}componentDidMount(){this.portalRef.current&&(B(this.props)&&this.props.mount(this.portalRef.current,this.props.props),$(this.props)&&this.props.open(this.props.props))}componentWillUnmount(){this.portalRef.current&&(B(this.props)&&this.props.unmount(this.portalRef.current),$(this.props)&&this.props.close())}render(){let{hideRootHtmlElement:e=!1}=this.props;return y.createElement(y.Fragment,null,!e&&y.createElement("div",{ref:this.portalRef}),this.props.children)}},q=e=>{var t,r;return y.createElement(y.Fragment,null,null==(t=null==e?void 0:e.customPagesPortals)?void 0:t.map((e,t)=>(0,y.createElement)(e,{key:t})),null==(r=null==e?void 0:e.customMenuItemsPortals)?void 0:r.map((e,t)=>(0,y.createElement)(e,{key:t})))},G=(0,c.r0)(({clerk:e,...t})=>y.createElement(J,{mount:e.mountSignIn,unmount:e.unmountSignIn,updateProps:e.__unstable__updateProps,props:t}),"SignIn"),K=(0,c.r0)(({clerk:e,...t})=>y.createElement(J,{mount:e.mountSignUp,unmount:e.unmountSignUp,updateProps:e.__unstable__updateProps,props:t}),"SignUp");function Y({children:e}){return(0,k.yJ)(c.ge),y.createElement(y.Fragment,null,e)}function H({children:e}){return(0,k.yJ)(c.Mp),y.createElement(y.Fragment,null,e)}var Q=Object.assign((0,c.r0)(({clerk:e,...t})=>{let{customPages:r,customPagesPortals:n}=z(t.children);return y.createElement(J,{mount:e.mountUserProfile,unmount:e.unmountUserProfile,updateProps:e.__unstable__updateProps,props:{...t,customPages:r}},y.createElement(q,{customPagesPortals:n}))},"UserProfile"),{Page:Y,Link:H}),X=(0,y.createContext)({mount:()=>{},unmount:()=>{},updateProps:()=>{}});function Z({children:e}){return(0,k.yJ)(c.X),y.createElement(y.Fragment,null,e)}function ee({children:e}){return(0,k.yJ)(c.B$),y.createElement(y.Fragment,null,e)}function et({children:e}){return(0,k.yJ)(c.qH),y.createElement(y.Fragment,null,e)}var er=Object.assign((0,c.r0)(({clerk:e,...t})=>{let{customPages:r,customPagesPortals:n}=z(t.children,{allowForAnyChildren:!!t.__experimental_asProvider}),i=Object.assign(t.userProfileProps||{},{customPages:r}),{customMenuItems:o,customMenuItemsPortals:s}=N(t.children),a=I(t.children),l={mount:e.mountUserButton,unmount:e.unmountUserButton,updateProps:e.__unstable__updateProps,props:{...t,userProfileProps:i,customMenuItems:o}};return y.createElement(X.Provider,{value:l},y.createElement(J,{...l,hideRootHtmlElement:!!t.__experimental_asProvider},t.__experimental_asProvider?a:null,y.createElement(q,{customPagesPortals:n,customMenuItemsPortals:s})))},"UserButton"),{UserProfilePage:Y,UserProfileLink:H,MenuItems:Z,Action:ee,Link:et,__experimental_Outlet:function(e){let t=(0,y.useContext)(X),r={...t,props:{...t.props,...e}};return y.createElement(J,{...r})}});function en({children:e}){return(0,k.yJ)(c.ej),y.createElement(y.Fragment,null,e)}function ei({children:e}){return(0,k.yJ)(c.Rl),y.createElement(y.Fragment,null,e)}var eo=Object.assign((0,c.r0)(({clerk:e,...t})=>{let{customPages:r,customPagesPortals:n}=L(t.children);return y.createElement(J,{mount:e.mountOrganizationProfile,unmount:e.unmountOrganizationProfile,updateProps:e.__unstable__updateProps,props:{...t,customPages:r}},y.createElement(q,{customPagesPortals:n}))},"OrganizationProfile"),{Page:en,Link:ei}),es=(0,c.r0)(({clerk:e,...t})=>y.createElement(J,{mount:e.mountCreateOrganization,unmount:e.unmountCreateOrganization,updateProps:e.__unstable__updateProps,props:t}),"CreateOrganization"),ea=(0,y.createContext)({mount:()=>{},unmount:()=>{},updateProps:()=>{}}),el=Object.assign((0,c.r0)(({clerk:e,...t})=>{let{customPages:r,customPagesPortals:n}=L(t.children,{allowForAnyChildren:!!t.__experimental_asProvider}),i=Object.assign(t.organizationProfileProps||{},{customPages:r}),o=I(t.children),s={mount:e.mountOrganizationSwitcher,unmount:e.unmountOrganizationSwitcher,updateProps:e.__unstable__updateProps,props:{...t,organizationProfileProps:i}};return e.__experimental_prefetchOrganizationSwitcher(),y.createElement(ea.Provider,{value:s},y.createElement(J,{...s,hideRootHtmlElement:!!t.__experimental_asProvider},t.__experimental_asProvider?o:null,y.createElement(q,{customPagesPortals:n})))},"OrganizationSwitcher"),{OrganizationProfilePage:en,OrganizationProfileLink:ei,__experimental_Outlet:function(e){let t=(0,y.useContext)(ea),r={...t,props:{...t.props,...e}};return y.createElement(J,{...r})}}),eu=(0,c.r0)(({clerk:e,...t})=>y.createElement(J,{mount:e.mountOrganizationList,unmount:e.unmountOrganizationList,updateProps:e.__unstable__updateProps,props:t}),"OrganizationList"),ec=(0,c.r0)(({clerk:e,...t})=>y.createElement(J,{open:e.openGoogleOneTap,close:e.closeGoogleOneTap,props:t}),"GoogleOneTap"),ed=(0,c.r0)(({clerk:e,children:t,...r})=>{let{signUpFallbackRedirectUrl:n,forceRedirectUrl:i,fallbackRedirectUrl:o,signUpForceRedirectUrl:s,mode:a,...l}=r,u=C(t=_(t,"Sign in"))("SignInButton"),c=()=>{let t={forceRedirectUrl:i,fallbackRedirectUrl:o,signUpFallbackRedirectUrl:n,signUpForceRedirectUrl:s};return"modal"===a?e.openSignIn(t):e.redirectToSignIn({...t,signInFallbackRedirectUrl:o,signInForceRedirectUrl:i})},d=async e=>(u&&"object"==typeof u&&"props"in u&&await O(u.props.onClick)(e),c()),h={...l,onClick:d};return y.cloneElement(u,h)},"SignInButton"),eh=(0,c.r0)(({clerk:e,children:t,...r})=>{let{fallbackRedirectUrl:n,forceRedirectUrl:i,signInFallbackRedirectUrl:o,signInForceRedirectUrl:s,mode:a,unsafeMetadata:l,...u}=r,c=C(t=_(t,"Sign up"))("SignUpButton"),d=()=>{let t={fallbackRedirectUrl:n,forceRedirectUrl:i,signInFallbackRedirectUrl:o,signInForceRedirectUrl:s,unsafeMetadata:l};return"modal"===a?e.openSignUp(t):e.redirectToSignUp({...t,signUpFallbackRedirectUrl:n,signUpForceRedirectUrl:i})},h=async e=>(c&&"object"==typeof c&&"props"in c&&await O(c.props.onClick)(e),d()),p={...u,onClick:h};return y.cloneElement(c,p)},"SignUpButton"),ep=(0,c.r0)(({clerk:e,children:t,...r})=>{let{redirectUrl:n="/",signOutOptions:i,...o}=r,s=C(t=_(t,"Sign out"))("SignOutButton"),a=()=>e.signOut({redirectUrl:n}),l=async e=>(await O(s.props.onClick)(e),a()),u={...o,onClick:l};return y.cloneElement(s,u)},"SignOutButton"),ef=(0,c.r0)(({clerk:e,children:t,...r})=>{let{redirectUrl:n,...i}=r,o=C(t=_(t,"Sign in with Metamask"))("SignInWithMetamaskButton"),s=async()=>{!async function(){await e.authenticateWithMetamask({redirectUrl:n||void 0})}()},a=async e=>(await O(o.props.onClick)(e),s()),l={...i,onClick:a};return y.cloneElement(o,l)},"SignInWithMetamask"),eg={name:"@clerk/clerk-react",version:"5.12.0",environment:"production"},em=class e{constructor(e){f(this,l),this.clerkjs=null,this.preopenOneTap=null,this.preopenUserVerification=null,this.preopenSignIn=null,this.preopenSignUp=null,this.preopenUserProfile=null,this.preopenOrganizationProfile=null,this.preopenCreateOrganization=null,this.premountSignInNodes=new Map,this.premountSignUpNodes=new Map,this.premountUserProfileNodes=new Map,this.premountUserButtonNodes=new Map,this.premountOrganizationProfileNodes=new Map,this.premountCreateOrganizationNodes=new Map,this.premountOrganizationSwitcherNodes=new Map,this.premountOrganizationListNodes=new Map,this.premountMethodCalls=new Map,this.premountAddListenerCalls=new Map,this.loadedListeners=[],f(this,n,!1),f(this,i),f(this,o),f(this,s),this.buildSignInUrl=e=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildSignInUrl(e))||""};if(this.clerkjs&&p(this,n))return t();this.premountMethodCalls.set("buildSignInUrl",t)},this.buildSignUpUrl=e=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildSignUpUrl(e))||""};if(this.clerkjs&&p(this,n))return t();this.premountMethodCalls.set("buildSignUpUrl",t)},this.buildAfterSignInUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildAfterSignInUrl())||""};if(this.clerkjs&&p(this,n))return e();this.premountMethodCalls.set("buildAfterSignInUrl",e)},this.buildAfterSignUpUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildAfterSignUpUrl())||""};if(this.clerkjs&&p(this,n))return e();this.premountMethodCalls.set("buildAfterSignUpUrl",e)},this.buildAfterSignOutUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildAfterSignOutUrl())||""};if(this.clerkjs&&p(this,n))return e();this.premountMethodCalls.set("buildAfterSignOutUrl",e)},this.buildAfterMultiSessionSingleSignOutUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildAfterMultiSessionSingleSignOutUrl())||""};if(this.clerkjs&&p(this,n))return e();this.premountMethodCalls.set("buildAfterMultiSessionSingleSignOutUrl",e)},this.buildUserProfileUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildUserProfileUrl())||""};if(this.clerkjs&&p(this,n))return e();this.premountMethodCalls.set("buildUserProfileUrl",e)},this.buildCreateOrganizationUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildCreateOrganizationUrl())||""};if(this.clerkjs&&p(this,n))return e();this.premountMethodCalls.set("buildCreateOrganizationUrl",e)},this.buildOrganizationProfileUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildOrganizationProfileUrl())||""};if(this.clerkjs&&p(this,n))return e();this.premountMethodCalls.set("buildOrganizationProfileUrl",e)},this.buildUrlWithAuth=e=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildUrlWithAuth(e))||""};if(this.clerkjs&&p(this,n))return t();this.premountMethodCalls.set("buildUrlWithAuth",t)},this.handleUnauthenticated=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.handleUnauthenticated()};this.clerkjs&&p(this,n)?e():this.premountMethodCalls.set("handleUnauthenticated",e)},this.addOnLoaded=e=>{this.loadedListeners.push(e),this.loaded&&this.emitLoaded()},this.emitLoaded=()=>{this.loadedListeners.forEach(e=>e()),this.loadedListeners=[]},this.hydrateClerkJS=e=>{if(!e)throw Error("Failed to hydrate latest Clerk JS");return this.clerkjs=e,this.premountMethodCalls.forEach(e=>e()),this.premountAddListenerCalls.forEach((t,r)=>{t.nativeUnsubscribe=e.addListener(r)}),null!==this.preopenSignIn&&e.openSignIn(this.preopenSignIn),null!==this.preopenSignUp&&e.openSignUp(this.preopenSignUp),null!==this.preopenUserProfile&&e.openUserProfile(this.preopenUserProfile),null!==this.preopenUserVerification&&e.__experimental_openUserVerification(this.preopenUserVerification),null!==this.preopenOneTap&&e.openGoogleOneTap(this.preopenOneTap),null!==this.preopenOrganizationProfile&&e.openOrganizationProfile(this.preopenOrganizationProfile),null!==this.preopenCreateOrganization&&e.openCreateOrganization(this.preopenCreateOrganization),this.premountSignInNodes.forEach((t,r)=>{e.mountSignIn(r,t)}),this.premountSignUpNodes.forEach((t,r)=>{e.mountSignUp(r,t)}),this.premountUserProfileNodes.forEach((t,r)=>{e.mountUserProfile(r,t)}),this.premountUserButtonNodes.forEach((t,r)=>{e.mountUserButton(r,t)}),this.premountOrganizationListNodes.forEach((t,r)=>{e.mountOrganizationList(r,t)}),g(this,n,!0),this.emitLoaded(),this.clerkjs},this.__unstable__updateProps=async e=>{let t=await m(this,l,u).call(this);if(t&&"__unstable__updateProps"in t)return t.__unstable__updateProps(e)},this.setActive=({session:e,organization:t,beforeEmit:r})=>this.clerkjs?this.clerkjs.setActive({session:e,organization:t,beforeEmit:r}):Promise.reject(),this.openSignIn=e=>{this.clerkjs&&p(this,n)?this.clerkjs.openSignIn(e):this.preopenSignIn=e},this.closeSignIn=()=>{this.clerkjs&&p(this,n)?this.clerkjs.closeSignIn():this.preopenSignIn=null},this.__experimental_openUserVerification=e=>{this.clerkjs&&p(this,n)?this.clerkjs.__experimental_openUserVerification(e):this.preopenUserVerification=e},this.__experimental_closeUserVerification=()=>{this.clerkjs&&p(this,n)?this.clerkjs.__experimental_closeUserVerification():this.preopenUserVerification=null},this.openGoogleOneTap=e=>{this.clerkjs&&p(this,n)?this.clerkjs.openGoogleOneTap(e):this.preopenOneTap=e},this.closeGoogleOneTap=()=>{this.clerkjs&&p(this,n)?this.clerkjs.closeGoogleOneTap():this.preopenOneTap=null},this.openUserProfile=e=>{this.clerkjs&&p(this,n)?this.clerkjs.openUserProfile(e):this.preopenUserProfile=e},this.closeUserProfile=()=>{this.clerkjs&&p(this,n)?this.clerkjs.closeUserProfile():this.preopenUserProfile=null},this.openOrganizationProfile=e=>{this.clerkjs&&p(this,n)?this.clerkjs.openOrganizationProfile(e):this.preopenOrganizationProfile=e},this.closeOrganizationProfile=()=>{this.clerkjs&&p(this,n)?this.clerkjs.closeOrganizationProfile():this.preopenOrganizationProfile=null},this.openCreateOrganization=e=>{this.clerkjs&&p(this,n)?this.clerkjs.openCreateOrganization(e):this.preopenCreateOrganization=e},this.closeCreateOrganization=()=>{this.clerkjs&&p(this,n)?this.clerkjs.closeCreateOrganization():this.preopenCreateOrganization=null},this.openSignUp=e=>{this.clerkjs&&p(this,n)?this.clerkjs.openSignUp(e):this.preopenSignUp=e},this.closeSignUp=()=>{this.clerkjs&&p(this,n)?this.clerkjs.closeSignUp():this.preopenSignUp=null},this.mountSignIn=(e,t)=>{this.clerkjs&&p(this,n)?this.clerkjs.mountSignIn(e,t):this.premountSignInNodes.set(e,t)},this.unmountSignIn=e=>{this.clerkjs&&p(this,n)?this.clerkjs.unmountSignIn(e):this.premountSignInNodes.delete(e)},this.mountSignUp=(e,t)=>{this.clerkjs&&p(this,n)?this.clerkjs.mountSignUp(e,t):this.premountSignUpNodes.set(e,t)},this.unmountSignUp=e=>{this.clerkjs&&p(this,n)?this.clerkjs.unmountSignUp(e):this.premountSignUpNodes.delete(e)},this.mountUserProfile=(e,t)=>{this.clerkjs&&p(this,n)?this.clerkjs.mountUserProfile(e,t):this.premountUserProfileNodes.set(e,t)},this.unmountUserProfile=e=>{this.clerkjs&&p(this,n)?this.clerkjs.unmountUserProfile(e):this.premountUserProfileNodes.delete(e)},this.mountOrganizationProfile=(e,t)=>{this.clerkjs&&p(this,n)?this.clerkjs.mountOrganizationProfile(e,t):this.premountOrganizationProfileNodes.set(e,t)},this.unmountOrganizationProfile=e=>{this.clerkjs&&p(this,n)?this.clerkjs.unmountOrganizationProfile(e):this.premountOrganizationProfileNodes.delete(e)},this.mountCreateOrganization=(e,t)=>{this.clerkjs&&p(this,n)?this.clerkjs.mountCreateOrganization(e,t):this.premountCreateOrganizationNodes.set(e,t)},this.unmountCreateOrganization=e=>{this.clerkjs&&p(this,n)?this.clerkjs.unmountCreateOrganization(e):this.premountCreateOrganizationNodes.delete(e)},this.mountOrganizationSwitcher=(e,t)=>{this.clerkjs&&p(this,n)?this.clerkjs.mountOrganizationSwitcher(e,t):this.premountOrganizationSwitcherNodes.set(e,t)},this.unmountOrganizationSwitcher=e=>{this.clerkjs&&p(this,n)?this.clerkjs.unmountOrganizationSwitcher(e):this.premountOrganizationSwitcherNodes.delete(e)},this.__experimental_prefetchOrganizationSwitcher=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.__experimental_prefetchOrganizationSwitcher()};this.clerkjs&&p(this,n)?e():this.premountMethodCalls.set("__experimental_prefetchOrganizationSwitcher",e)},this.mountOrganizationList=(e,t)=>{this.clerkjs&&p(this,n)?this.clerkjs.mountOrganizationList(e,t):this.premountOrganizationListNodes.set(e,t)},this.unmountOrganizationList=e=>{this.clerkjs&&p(this,n)?this.clerkjs.unmountOrganizationList(e):this.premountOrganizationListNodes.delete(e)},this.mountUserButton=(e,t)=>{this.clerkjs&&p(this,n)?this.clerkjs.mountUserButton(e,t):this.premountUserButtonNodes.set(e,t)},this.unmountUserButton=e=>{this.clerkjs&&p(this,n)?this.clerkjs.unmountUserButton(e):this.premountUserButtonNodes.delete(e)},this.addListener=e=>{if(this.clerkjs)return this.clerkjs.addListener(e);{let t=()=>{var t;let r=this.premountAddListenerCalls.get(e);r&&(null==(t=r.nativeUnsubscribe)||t.call(r),this.premountAddListenerCalls.delete(e))};return this.premountAddListenerCalls.set(e,{unsubscribe:t,nativeUnsubscribe:void 0}),t}},this.navigate=e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.navigate(e)};this.clerkjs&&p(this,n)?t():this.premountMethodCalls.set("navigate",t)},this.redirectWithAuth=async(...e)=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.redirectWithAuth(...e)};if(this.clerkjs&&p(this,n))return t();this.premountMethodCalls.set("redirectWithAuth",t)},this.redirectToSignIn=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.redirectToSignIn(e)};if(this.clerkjs&&p(this,n))return t();this.premountMethodCalls.set("redirectToSignIn",t)},this.redirectToSignUp=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.redirectToSignUp(e)};if(this.clerkjs&&p(this,n))return t();this.premountMethodCalls.set("redirectToSignUp",t)},this.redirectToUserProfile=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToUserProfile()};if(this.clerkjs&&p(this,n))return e();this.premountMethodCalls.set("redirectToUserProfile",e)},this.redirectToAfterSignUp=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToAfterSignUp()};if(this.clerkjs&&p(this,n))return e();this.premountMethodCalls.set("redirectToAfterSignUp",e)},this.redirectToAfterSignIn=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToAfterSignIn()};this.clerkjs&&p(this,n)?e():this.premountMethodCalls.set("redirectToAfterSignIn",e)},this.redirectToAfterSignOut=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToAfterSignOut()};this.clerkjs&&p(this,n)?e():this.premountMethodCalls.set("redirectToAfterSignOut",e)},this.redirectToOrganizationProfile=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToOrganizationProfile()};if(this.clerkjs&&p(this,n))return e();this.premountMethodCalls.set("redirectToOrganizationProfile",e)},this.redirectToCreateOrganization=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToCreateOrganization()};if(this.clerkjs&&p(this,n))return e();this.premountMethodCalls.set("redirectToCreateOrganization",e)},this.handleRedirectCallback=e=>{var t;let r=()=>{var t;return null==(t=this.clerkjs)?void 0:t.handleRedirectCallback(e)};this.clerkjs&&p(this,n)?null==(t=r())||t.catch(()=>{}):this.premountMethodCalls.set("handleRedirectCallback",r)},this.handleGoogleOneTapCallback=(e,t)=>{var r;let i=()=>{var r;return null==(r=this.clerkjs)?void 0:r.handleGoogleOneTapCallback(e,t)};this.clerkjs&&p(this,n)?null==(r=i())||r.catch(()=>{}):this.premountMethodCalls.set("handleGoogleOneTapCallback",i)},this.handleEmailLinkVerification=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.handleEmailLinkVerification(e)};if(this.clerkjs&&p(this,n))return t();this.premountMethodCalls.set("handleEmailLinkVerification",t)},this.authenticateWithMetamask=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithMetamask(e)};if(this.clerkjs&&p(this,n))return t();this.premountMethodCalls.set("authenticateWithMetamask",t)},this.authenticateWithCoinbaseWallet=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithCoinbaseWallet(e)};if(this.clerkjs&&p(this,n))return t();this.premountMethodCalls.set("authenticateWithCoinbaseWallet",t)},this.authenticateWithWeb3=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithWeb3(e)};if(this.clerkjs&&p(this,n))return t();this.premountMethodCalls.set("authenticateWithWeb3",t)},this.authenticateWithGoogleOneTap=async e=>(await m(this,l,u).call(this)).authenticateWithGoogleOneTap(e),this.createOrganization=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.createOrganization(e)};if(this.clerkjs&&p(this,n))return t();this.premountMethodCalls.set("createOrganization",t)},this.getOrganization=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.getOrganization(e)};if(this.clerkjs&&p(this,n))return t();this.premountMethodCalls.set("getOrganization",t)},this.signOut=async(e,t)=>{let r=()=>{var r;return null==(r=this.clerkjs)?void 0:r.signOut(e,t)};if(this.clerkjs&&p(this,n))return r();this.premountMethodCalls.set("signOut",r)};let{Clerk:t=null,publishableKey:r}=e||{};g(this,s,r),g(this,o,null==e?void 0:e.proxyUrl),g(this,i,null==e?void 0:e.domain),this.options=e,this.Clerk=t,this.mode=(0,P._f)()?"browser":"server",this.options.sdkMetadata||(this.options.sdkMetadata=eg),this.loadClerkJS()}get publishableKey(){return p(this,s)}get loaded(){return p(this,n)}static getOrCreateInstance(t){return(0,P._f)()&&p(this,a)&&(!t.Clerk||p(this,a).Clerk===t.Clerk)||g(this,a,new e(t)),p(this,a)}static clearInstance(){g(this,a,null)}get domain(){return"undefined"!=typeof window&&window.location?E(p(this,i),new URL(window.location.href),""):"function"==typeof p(this,i)?c.RM.throw(c.qq):p(this,i)||""}get proxyUrl(){return"undefined"!=typeof window&&window.location?E(p(this,o),new URL(window.location.href),""):"function"==typeof p(this,o)?c.RM.throw(c.qq):p(this,o)||""}get sdkMetadata(){var e;return(null==(e=this.clerkjs)?void 0:e.sdkMetadata)||this.options.sdkMetadata||void 0}get instanceType(){var e;return null==(e=this.clerkjs)?void 0:e.instanceType}get frontendApi(){var e;return(null==(e=this.clerkjs)?void 0:e.frontendApi)||""}get isStandardBrowser(){var e;return(null==(e=this.clerkjs)?void 0:e.isStandardBrowser)||this.options.standardBrowser||!1}get isSatellite(){return"undefined"!=typeof window&&window.location?E(this.options.isSatellite,new URL(window.location.href),!1):"function"==typeof this.options.isSatellite&&c.RM.throw(c.qq)}async loadClerkJS(){var e,t;if(!("browser"!==this.mode||p(this,n))){"undefined"!=typeof window&&(window.__clerk_publishable_key=p(this,s),window.__clerk_proxy_url=this.proxyUrl,window.__clerk_domain=this.domain);try{if(this.Clerk){let e;(t=this.Clerk,"function"==typeof t)?(e=new this.Clerk(p(this,s),{proxyUrl:this.proxyUrl,domain:this.domain}),await e.load(this.options)):(e=this.Clerk).loaded||await e.load(this.options),global.Clerk=e}else{if(global.Clerk||await (0,v.YJ)({...this.options,publishableKey:p(this,s),proxyUrl:this.proxyUrl,domain:this.domain,nonce:this.options.nonce}),!global.Clerk)throw Error("Failed to download latest ClerkJS. Contact <EMAIL>.");await global.Clerk.load(this.options)}if(null==(e=global.Clerk)?void 0:e.loaded)return this.hydrateClerkJS(global.Clerk);return}catch(e){console.error(e.stack||e.message||e);return}}}get version(){var e;return null==(e=this.clerkjs)?void 0:e.version}get client(){return this.clerkjs?this.clerkjs.client:void 0}get session(){return this.clerkjs?this.clerkjs.session:void 0}get user(){return this.clerkjs?this.clerkjs.user:void 0}get organization(){return this.clerkjs?this.clerkjs.organization:void 0}get telemetry(){return this.clerkjs?this.clerkjs.telemetry:void 0}get __unstable__environment(){return this.clerkjs?this.clerkjs.__unstable__environment:void 0}__unstable__setEnvironment(...e){this.clerkjs&&"__unstable__setEnvironment"in this.clerkjs&&this.clerkjs.__unstable__setEnvironment(e)}};n=new WeakMap,i=new WeakMap,o=new WeakMap,s=new WeakMap,a=new WeakMap,l=new WeakSet,u=function(){return new Promise(e=>{this.addOnLoaded(()=>e(this.clerkjs))})},f(em,a);var ev=(e,t,r)=>!e&&r?ek(r):eb(t),ek=e=>{let t=e.userId,r=e.user,n=e.sessionId,i=e.session,o=e.organization,s=e.orgId,a=e.orgRole,l=e.orgPermissions;return{userId:t,user:r,sessionId:n,session:i,organization:o,orgId:s,orgRole:a,orgPermissions:l,orgSlug:e.orgSlug,actor:e.actor,__experimental_factorVerificationAge:e.__experimental_factorVerificationAge}},eb=e=>{var t;let r=e.user?e.user.id:e.user,n=e.user,i=e.session?e.session.id:e.session,o=e.session,s=e.session?e.session.__experimental_factorVerificationAge:null,a=null==o?void 0:o.actor,l=e.organization,u=e.organization?e.organization.id:e.organization,c=null==l?void 0:l.slug,d=l?null==(t=null==n?void 0:n.organizationMemberships)?void 0:t.find(e=>e.organization.id===u):l,h=d?d.permissions:d;return{userId:r,user:n,sessionId:i,session:o,organization:l,orgId:u,orgRole:d?d.role:d,orgSlug:c,orgPermissions:h,actor:a,__experimental_factorVerificationAge:s}};function ey(e){let{isomorphicClerkOptions:t,initialState:r,children:n}=e,{isomorphicClerk:i,loaded:o}=ew(t),[s,a]=y.useState({client:i.client,session:i.session,user:i.user,organization:i.organization});y.useEffect(()=>i.addListener(e=>a({...e})),[]);let l=ev(o,s,r),u=y.useMemo(()=>({value:i}),[o]),d=y.useMemo(()=>({value:s.client}),[s.client]),{sessionId:h,session:p,userId:f,user:g,orgId:m,actor:v,organization:k,orgRole:w,orgSlug:S,orgPermissions:P,__experimental_factorVerificationAge:E}=l,C=y.useMemo(()=>({value:{sessionId:h,userId:f,actor:v,orgId:m,orgRole:w,orgSlug:S,orgPermissions:P,__experimental_factorVerificationAge:E}}),[h,f,v,m,w,S,E]),_=y.useMemo(()=>({value:p}),[h,p]),O=y.useMemo(()=>({value:g}),[f,g]),U=y.useMemo(()=>({value:{organization:k}}),[m,k]);return y.createElement(c.iz.Provider,{value:u},y.createElement(b.RY.Provider,{value:d},y.createElement(b.B3.Provider,{value:_},y.createElement(b.f0,{...U.value},y.createElement(c.Vo.Provider,{value:C},y.createElement(b.St.Provider,{value:O},n))))))}var ew=e=>{let[t,r]=y.useState(!1),n=y.useMemo(()=>em.getOrCreateInstance(e),[]);return y.useEffect(()=>{n.__unstable__updateProps({appearance:e.appearance})},[e.appearance]),y.useEffect(()=>{n.__unstable__updateProps({options:e})},[e.localization]),y.useEffect(()=>{n.addOnLoaded(()=>r(!0))},[]),y.useEffect(()=>()=>{em.clearInstance()},[]),{isomorphicClerk:n,loaded:t}},eS=function(e,t,r){let n=e.displayName||e.name||t||"Component",i=n=>(!function(e,t,r=1){y.useEffect(()=>{let n=U.get(e)||0;return n==r?c.RM.throw(t):(U.set(e,n+1),()=>{U.set(e,(U.get(e)||1)-1)})},[])}(t,r),y.createElement(e,{...n}));return i.displayName=`withMaxAllowedInstancesGuard(${n})`,i}(function(e){let{initialState:t,children:r,...n}=e,{publishableKey:i="",Clerk:o}=n;return o||(i?i&&!(0,S.x9)(i)&&c.RM.throwInvalidPublishableKeyError({key:i}):c.RM.throwMissingPublishableKeyError()),y.createElement(ey,{initialState:t,isomorphicClerkOptions:n},r)},"ClerkProvider",c.KQ);eS.displayName="ClerkProvider",(0,c.JM)({packageName:"@clerk/clerk-react"}),(0,v.Aw)("@clerk/clerk-react")},84301:function(e,t,r){"use strict";r.d(t,{U9:function(){return f},ac:function(){return h},qx:function(){return p},r2:function(){return l},yA:function(){return c}});var n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,a=e=>{throw TypeError(e)},l=(e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})},u=(e,t,r,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let l of o(t))s.call(e,l)||l===r||n(e,l,{get:()=>t[l],enumerable:!(a=i(t,l))||a.enumerable});return e},c=(e,t,r)=>(u(e,t,"default"),r&&u(r,t,"default")),d=(e,t,r)=>t.has(e)||a("Cannot "+r),h=(e,t,r)=>(d(e,t,"read from private field"),r?r.call(e):t.get(e)),p=(e,t,r,n)=>(d(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),f=(e,t,r)=>(d(e,t,"access private method"),r)},48784:function(e,t,r){"use strict";r.d(t,{ve:function(){return u},yJ:function(){return i}});var n=r(6880),i=e=>{(0,n.vf)()&&console.error(`Clerk: ${e}`)},o={firstDelay:125,maxDelay:0,timeMultiple:2,shouldRetry:()=>!0},s=async e=>new Promise(t=>setTimeout(t,e)),a=e=>{let t=0,r=()=>{let r=e.firstDelay*Math.pow(e.timeMultiple,t);return Math.min(e.maxDelay||r,r)};return async()=>{await s(r()),t++}},l=async(e,t={})=>{let r=0,{shouldRetry:n,firstDelay:i,maxDelay:s,timeMultiple:l}={...o,...t},u=a({firstDelay:i,maxDelay:s,timeMultiple:l});for(;;)try{return await e()}catch(e){if(!n(e,++r))throw e;await u()}};async function u(e="",t){let{async:r,defer:n,beforeLoad:i,crossOrigin:o,nonce:s}=t||{};return l(()=>new Promise((t,a)=>{e||a("loadScript cannot be called without a src"),document&&document.body||a("loadScript cannot be called when document does not exist");let l=document.createElement("script");o&&l.setAttribute("crossorigin",o),l.async=r||!1,l.defer=n||!1,l.addEventListener("load",()=>{l.remove(),t(l)}),l.addEventListener("error",()=>{l.remove(),a()}),l.src=e,l.nonce=s,null==i||i(l),document.body.appendChild(l)}),{shouldRetry:(e,t)=>t<5})}},79775:function(e,t,r){"use strict";r.d(t,{Fo:function(){return a},Iq:function(){return l},Xv:function(){return u},cM:function(){return s},iF:function(){return o},mv:function(){return n},vO:function(){return i}});var n=[".lcl.dev",".lclstage.dev",".lclclerk.com"],i=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],o=[".lcl.dev","lclstage.dev",".lclclerk.com",".accounts.lclclerk.com"],s=[".accountsstage.dev"],a="https://api.lclclerk.com",l="https://api.clerkstage.dev",u="https://api.clerk.com"},77126:function(e,t,r){"use strict";r.d(t,{MY:function(){return l},x9:function(){return a},nQ:function(){return s}});var n=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e,i=r(79775),o="pk_live_";function s(e,t={}){if(!(e=e||"")||!a(e)){if(t.fatal)throw Error("Publishable key not valid.");return null}let r=e.startsWith(o)?"production":"development",i=n(e.split("_")[2]);return i=i.slice(0,-1),t.proxyUrl?i=t.proxyUrl:"development"!==r&&t.domain&&(i=`clerk.${t.domain}`),{instanceType:r,frontendApi:i}}function a(e){let t=(e=e||"").startsWith(o)||e.startsWith("pk_test_"),r=n(e.split("_")[2]||"").endsWith("$");return t&&r}function l(){let e=new Map;return{isDevOrStagingUrl:t=>{if(!t)return!1;let r="string"==typeof t?t:t.hostname,n=e.get(r);return void 0===n&&(n=i.vO.some(e=>r.endsWith(e)),e.set(r,n)),n}}}},8996:function(e,t,r){"use strict";function n(){return"undefined"!=typeof window}r.d(t,{_f:function(){return n}}),RegExp("bot|spider|crawl|APIs-Google|AdsBot|Googlebot|mediapartners|Google Favicon|FeedFetcher|Google-Read-Aloud|DuplexWeb-Google|googleweblight|bing|yandex|baidu|duckduck|yahoo|ecosia|ia_archiver|facebook|instagram|pinterest|reddit|slack|twitter|whatsapp|youtube|semrush","i")},35831:function(e,t,r){"use strict";r.d(t,{iv:function(){return m},wE:function(){return g},YJ:function(){return f},Aw:function(){return p}});var n=(e,t="5.27.0")=>{if(e)return e;let r=i(t);return r?"snapshot"===r?"5.27.0":r:o(t)},i=e=>{var t;return null==(t=e.trim().replace(/^v/,"").match(/-(.+?)(\.|$)/))?void 0:t[1]},o=e=>e.trim().replace(/^v/,"").split(".")[0];function s(e){return e.startsWith("/")}var a=r(48784),l=r(2310),u=r(77126),c="Clerk: Failed to load Clerk",{isDevOrStagingUrl:d}=(0,u.MY)(),h=(0,l.t5)({packageName:"@clerk/shared"});function p(e){h.setPackageName({packageName:e})}var f=async e=>{let t=document.querySelector("script[data-clerk-js-script]");if(t)return new Promise((e,r)=>{t.addEventListener("load",()=>{e(t)}),t.addEventListener("error",()=>{r(c)})});if(!(null==e?void 0:e.publishableKey)){h.throwMissingPublishableKeyError();return}return(0,a.ve)(g(e),{async:!0,crossOrigin:"anonymous",nonce:e.nonce,beforeLoad:v(e)}).catch(()=>{throw Error(c)})},g=e=>{var t,r;let{clerkJSUrl:i,clerkJSVariant:o,clerkJSVersion:a,proxyUrl:l,domain:c,publishableKey:h}=e;if(i)return i;let p="";p=l&&(!l||/^http(s)?:\/\//.test(l||"")||s(l))?(l?s(l)?new URL(l,window.location.origin).toString():l:"").replace(/http(s)?:\/\//,""):c&&!d((null==(t=(0,u.nQ)(h))?void 0:t.frontendApi)||"")?function(e){let t;if(!e)return"";if(e.match(/^(clerk\.)+\w*$/))t=/(clerk\.)*(?=clerk\.)/;else{if(e.match(/\.clerk.accounts/))return e;t=/^(clerk\.)*/gi}let r=e.replace(t,"");return`clerk.${r}`}(c):(null==(r=(0,u.nQ)(h))?void 0:r.frontendApi)||"";let f=o?`${o.replace(/\.+$/,"")}.`:"",g=n(a);return`https://${p}/npm/@clerk/clerk-js@${g}/dist/clerk.${f}browser.js`},m=e=>{let t={};return e.publishableKey&&(t["data-clerk-publishable-key"]=e.publishableKey),e.proxyUrl&&(t["data-clerk-proxy-url"]=e.proxyUrl),e.domain&&(t["data-clerk-domain"]=e.domain),e.nonce&&(t.nonce=e.nonce),t},v=e=>t=>{let r=m(e);for(let e in r)t.setAttribute(e,r[e])}},70040:function(e,t,r){"use strict";r.d(t,{fQ:function(){return i}});var n=e=>{let t=r=>{if(!r)return r;if(Array.isArray(r))return r.map(e=>"object"==typeof e||Array.isArray(e)?t(e):e);let n={...r};for(let r of Object.keys(n)){let i=e(r.toString());i!==r&&(n[i]=n[r],delete n[r]),"object"==typeof n[i]&&(n[i]=t(n[i]))}return n};return t};function i(e){if("boolean"==typeof e)return e;if(null==e)return!1;if("string"==typeof e){if("true"===e.toLowerCase())return!0;if("false"===e.toLowerCase())return!1}let t=parseInt(e,10);return!isNaN(t)&&t>0}n(function(e){return e?e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`):""}),n(function(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,"")):""})},6880:function(e,t,r){"use strict";r.d(t,{rx:function(){return i},vf:function(){return n}});var n=()=>!1,i=()=>{try{return!0}catch(e){}return!1}},2310:function(e,t,r){"use strict";r.d(t,{t5:function(){return i}});var n=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function i({packageName:e,customMessages:t}){let r=e,i={...n,...t};function o(e,t){if(!t)return`${r}: ${e}`;let n=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();n=n.replace(`{{${r[1]}}}`,e)}return`${r}: ${n}`}return{setPackageName({packageName:e}){return"string"==typeof e&&(r=e),this},setMessages({customMessages:e}){return Object.assign(i,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(o(i.InvalidPublishableKeyErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(o(i.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(o(i.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw Error(o(i.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(e){throw Error(o(i.MissingClerkProvider,e))},throw(e){throw Error(o(e))}}}},10684:function(e,t,r){"use strict";function n(e,t){return{event:"METHOD_CALLED",payload:{method:e,...t}}}r.d(t,{J:function(){return n}})},22605:function(e,t,r){"use strict";r.d(t,{t5:function(){return n.t5}});var n=r(2310);r(84301)},59827:function(e,t,r){"use strict";r.d(t,{rx:function(){return o.rx},yJ:function(){return i.yJ},zu:function(){return n}});var n=(e,...t)=>{let r={...e};for(let e of t)delete r[e];return r};r(70040),r(35831);var i=r(48784),o=r(6880);r(2310),Object.freeze({"image/png":"png","image/jpeg":"jpg","image/gif":"gif","image/webp":"webp","image/x-icon":"ico","image/vnd.microsoft.icon":"ico"}),r(8996),r(84301)},23054:function(e,t,r){"use strict";r.d(t,{Aw:function(){return n.Aw},YJ:function(){return n.YJ},iv:function(){return n.iv},wE:function(){return n.wE}});var n=r(35831);r(48784),r(2310),r(84301)},6627:function(e,t,r){"use strict";let n;r.d(t,{b5:function(){return eb},RY:function(){return eP},f0:function(){return ej},B3:function(){return eC},St:function(){return ew},uH:function(){return ev},c1:function(){return eq},Rm:function(){return eM},cL:function(){return eD},rI:function(){return ey},sX:function(){return eE},o8:function(){return eA},eW:function(){return eN},kP:function(){return eV},xo:function(){return eW},aF:function(){return eF}});var i={};r.r(i),r.d(i,{SWRConfig:function(){return el},default:function(){return eu},mutate:function(){return J},preload:function(){return et},unstable_serialize:function(){return eo},useSWRConfig:function(){return ee}});var o=r(10684),s=r(84301),a=r(2265),l=r(82558);let u=()=>{},c=u(),d=Object,h=e=>e===c,p=e=>"function"==typeof e,f=(e,t)=>({...e,...t}),g=e=>p(e.then),m=new WeakMap,v=0,k=e=>{let t,r;let n=typeof e,i=e&&e.constructor,o=i==Date;if(d(e)!==e||o||i==RegExp)t=o?e.toJSON():"symbol"==n?e.toString():"string"==n?JSON.stringify(e):""+e;else{if(t=m.get(e))return t;if(t=++v+"~",m.set(e,t),i==Array){for(r=0,t="@";r<e.length;r++)t+=k(e[r])+",";m.set(e,t)}if(i==d){t="#";let n=d.keys(e).sort();for(;!h(r=n.pop());)h(e[r])||(t+=r+":"+k(e[r])+",");m.set(e,t)}}return t},b=new WeakMap,y={},w={},S="undefined",P=typeof window!=S,E=typeof document!=S,C=()=>P&&typeof window.requestAnimationFrame!=S,_=(e,t)=>{let r=b.get(e);return[()=>!h(t)&&e.get(t)||y,n=>{if(!h(t)){let i=e.get(t);t in w||(w[t]=i),r[5](t,f(i,n),i||y)}},r[6],()=>!h(t)&&t in w?w[t]:!h(t)&&e.get(t)||y]},O=!0,[U,j]=P&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[u,u],M={initFocus:e=>(E&&document.addEventListener("visibilitychange",e),U("focus",e),()=>{E&&document.removeEventListener("visibilitychange",e),j("focus",e)}),initReconnect:e=>{let t=()=>{O=!0,e()},r=()=>{O=!1};return U("online",t),U("offline",r),()=>{j("online",t),j("offline",r)}}},z=!a.useId,L=!P||"Deno"in window,I=e=>C()?window.requestAnimationFrame(e):setTimeout(e,1),T=L?a.useEffect:a.useLayoutEffect,R="undefined"!=typeof navigator&&navigator.connection,A=!L&&R&&(["slow-2g","2g"].includes(R.effectiveType)||R.saveData),x=e=>{if(p(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?k(e):"",t]},N=0,V=()=>++N;var W={ERROR_REVALIDATE_EVENT:3,FOCUS_EVENT:0,MUTATE_EVENT:2,RECONNECT_EVENT:1};async function F(...e){let[t,r,n,i]=e,o=f({populateCache:!0,throwOnError:!0},"boolean"==typeof i?{revalidate:i}:i||{}),s=o.populateCache,a=o.rollbackOnError,l=o.optimisticData,u=e=>"function"==typeof a?a(e):!1!==a,d=o.throwOnError;if(p(r)){let e=[];for(let n of t.keys())!/^\$(inf|sub)\$/.test(n)&&r(t.get(n)._k)&&e.push(n);return Promise.all(e.map(m))}return m(r);async function m(r){let i;let[a]=x(r);if(!a)return;let[f,m]=_(t,a),[v,k,y,w]=b.get(t),S=()=>{let e=v[a];return(p(o.revalidate)?o.revalidate(f().data,r):!1!==o.revalidate)&&(delete y[a],delete w[a],e&&e[0])?e[0](2).then(()=>f().data):f().data};if(e.length<3)return S();let P=n,E=V();k[a]=[E,0];let C=!h(l),O=f(),U=O.data,j=O._c,M=h(j)?U:j;if(C&&m({data:l=p(l)?l(M,U):l,_c:M}),p(P))try{P=P(M)}catch(e){i=e}if(P&&g(P)){if(P=await P.catch(e=>{i=e}),E!==k[a][0]){if(i)throw i;return P}i&&C&&u(i)&&(s=!0,m({data:M,_c:c}))}if(s&&!i&&(p(s)?m({data:s(P,M),error:c,_c:c}):m({data:P,error:c,_c:c})),k[a][1]=V(),Promise.resolve(S()).then(()=>{m({_c:c})}),i){if(d)throw i;return}return P}}let D=(e,t)=>{for(let r in e)e[r][0]&&e[r][0](t)},B=(e,t)=>{if(!b.has(e)){let r=f(M,t),n={},i=F.bind(c,e),o=u,s={},a=(e,t)=>{let r=s[e]||[];return s[e]=r,r.push(t),()=>r.splice(r.indexOf(t),1)},l=(t,r,n)=>{e.set(t,r);let i=s[t];if(i)for(let e of i)e(r,n)},d=()=>{if(!b.has(e)&&(b.set(e,[n,{},{},{},i,l,a]),!L)){let t=r.initFocus(setTimeout.bind(c,D.bind(c,n,0))),i=r.initReconnect(setTimeout.bind(c,D.bind(c,n,1)));o=()=>{t&&t(),i&&i(),b.delete(e)}}};return d(),[e,i,d,o]}return[e,b.get(e)[4]]},[$,J]=B(new Map),q=f({onLoadingSlow:u,onSuccess:u,onError:u,onErrorRetry:(e,t,r,n,i)=>{let o=r.errorRetryCount,s=i.retryCount,a=~~((Math.random()+.5)*(1<<(s<8?s:8)))*r.errorRetryInterval;(h(o)||!(s>o))&&setTimeout(n,a,i)},onDiscarded:u,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:A?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:A?5e3:3e3,compare:(e,t)=>k(e)==k(t),isPaused:()=>!1,cache:$,mutate:J,fallback:{}},{isOnline:()=>O,isVisible:()=>{let e=E&&document.visibilityState;return h(e)||"hidden"!==e}}),G=(e,t)=>{let r=f(e,t);if(t){let{use:n,fallback:i}=e,{use:o,fallback:s}=t;n&&o&&(r.use=n.concat(o)),i&&s&&(r.fallback=f(i,s))}return r},K=(0,a.createContext)({}),Y=e=>{let{value:t}=e,r=(0,a.useContext)(K),n=p(t),i=(0,a.useMemo)(()=>n?t(r):t,[n,r,t]),o=(0,a.useMemo)(()=>n?i:G(r,i),[n,r,i]),s=i&&i.provider,l=(0,a.useRef)(c);s&&!l.current&&(l.current=B(s(o.cache||$),i));let u=l.current;return u&&(o.cache=u[0],o.mutate=u[1]),T(()=>{if(u)return u[2]&&u[2](),u[3]},[]),(0,a.createElement)(K.Provider,f(e,{value:o}))},H="$inf$",Q=P&&window.__SWR_DEVTOOLS_USE__,X=Q?window.__SWR_DEVTOOLS_USE__:[],Z=e=>p(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}],ee=()=>f(q,(0,a.useContext)(K)),et=(e,t)=>{let[r,n]=x(e),[,,,i]=b.get($);if(i[r])return i[r];let o=t(n);return i[r]=o,o},er=X.concat(e=>(t,r,n)=>{let i=r&&((...e)=>{let[n]=x(t),[,,,i]=b.get($);if(n.startsWith(H))return r(...e);let o=i[n];return h(o)?r(...e):(delete i[n],o)});return e(t,i,n)}),en=e=>function(...t){let r=ee(),[n,i,o]=Z(t),s=G(r,o),a=e,{use:l}=s,u=(l||[]).concat(er);for(let e=u.length;e--;)a=u[e](a);return a(n,i||s.fetcher||null,s)},ei=(e,t,r)=>{let n=t[e]||(t[e]=[]);return n.push(r),()=>{let e=n.indexOf(r);e>=0&&(n[e]=n[n.length-1],n.pop())}};Q&&(window.__SWR_DEVTOOLS_REACT__=a);let eo=e=>x(e)[0],es=a.use||(e=>{if("pending"===e.status)throw e;if("fulfilled"===e.status)return e.value;if("rejected"===e.status)throw e.reason;throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}),ea={dedupe:!0},el=d.defineProperty(Y,"defaultValue",{value:q}),eu=en((e,t,r)=>{let{cache:n,compare:i,suspense:o,fallbackData:s,revalidateOnMount:u,revalidateIfStale:d,refreshInterval:g,refreshWhenHidden:m,refreshWhenOffline:v,keepPreviousData:k}=r,[y,w,S,P]=b.get(n),[E,C]=x(e),O=(0,a.useRef)(!1),U=(0,a.useRef)(!1),j=(0,a.useRef)(E),M=(0,a.useRef)(t),R=(0,a.useRef)(r),A=()=>R.current,N=()=>A().isVisible()&&A().isOnline(),[D,B,$,J]=_(n,E),q=(0,a.useRef)({}).current,G=h(s)?r.fallback[E]:s,K=(e,t)=>{for(let r in q)if("data"===r){if(!i(e[r],t[r])&&(!h(e[r])||!i(en,t[r])))return!1}else if(t[r]!==e[r])return!1;return!0},Y=(0,a.useMemo)(()=>{let e=!!E&&!!t&&(h(u)?!A().isPaused()&&!o&&(!!h(d)||d):u),r=t=>{let r=f(t);return(delete r._k,e)?{isValidating:!0,isLoading:!0,...r}:r},n=D(),i=J(),s=r(n),a=n===i?s:r(i),l=s;return[()=>{let e=r(D());return K(e,l)?(l.data=e.data,l.isLoading=e.isLoading,l.isValidating=e.isValidating,l.error=e.error,l):(l=e,e)},()=>a]},[n,E]),H=(0,l.useSyncExternalStore)((0,a.useCallback)(e=>$(E,(t,r)=>{K(r,t)||e()}),[n,E]),Y[0],Y[1]),Q=!O.current,X=y[E]&&y[E].length>0,Z=H.data,ee=h(Z)?G:Z,et=H.error,er=(0,a.useRef)(ee),en=k?h(Z)?er.current:Z:ee,eo=(!X||!!h(et))&&(Q&&!h(u)?u:!A().isPaused()&&(o?!h(ee)&&d:h(ee)||d)),el=!!(E&&t&&Q&&eo),eu=h(H.isValidating)?el:H.isValidating,ec=h(H.isLoading)?el:H.isLoading,ed=(0,a.useCallback)(async e=>{let t,n;let o=M.current;if(!E||!o||U.current||A().isPaused())return!1;let s=!0,a=e||{},l=!S[E]||!a.dedupe,u=()=>z?!U.current&&E===j.current&&O.current:E===j.current,d={isValidating:!1,isLoading:!1},f=()=>{B(d)},g=()=>{let e=S[E];e&&e[1]===n&&delete S[E]},m={isValidating:!0};h(D().data)&&(m.isLoading=!0);try{if(l&&(B(m),r.loadingTimeout&&h(D().data)&&setTimeout(()=>{s&&u()&&A().onLoadingSlow(E,r)},r.loadingTimeout),S[E]=[o(C),V()]),[t,n]=S[E],t=await t,l&&setTimeout(g,r.dedupingInterval),!S[E]||S[E][1]!==n)return l&&u()&&A().onDiscarded(E),!1;d.error=c;let e=w[E];if(!h(e)&&(n<=e[0]||n<=e[1]||0===e[1]))return f(),l&&u()&&A().onDiscarded(E),!1;let a=D().data;d.data=i(a,t)?a:t,l&&u()&&A().onSuccess(t,E,r)}catch(r){g();let e=A(),{shouldRetryOnError:t}=e;!e.isPaused()&&(d.error=r,l&&u()&&(e.onError(r,E,e),(!0===t||p(t)&&t(r))&&(!A().revalidateOnFocus||!A().revalidateOnReconnect||N())&&e.onErrorRetry(r,E,e,e=>{let t=y[E];t&&t[0]&&t[0](W.ERROR_REVALIDATE_EVENT,e)},{retryCount:(a.retryCount||0)+1,dedupe:!0})))}return s=!1,f(),!0},[E,n]),eh=(0,a.useCallback)((...e)=>F(n,j.current,...e),[]);if(T(()=>{M.current=t,R.current=r,h(Z)||(er.current=Z)}),T(()=>{if(!E)return;let e=ed.bind(c,ea),t=0,r=ei(E,y,(r,n={})=>{if(r==W.FOCUS_EVENT){let r=Date.now();A().revalidateOnFocus&&r>t&&N()&&(t=r+A().focusThrottleInterval,e())}else if(r==W.RECONNECT_EVENT)A().revalidateOnReconnect&&N()&&e();else if(r==W.MUTATE_EVENT)return ed();else if(r==W.ERROR_REVALIDATE_EVENT)return ed(n)});return U.current=!1,j.current=E,O.current=!0,B({_k:C}),eo&&(h(ee)||L?e():I(e)),()=>{U.current=!0,r()}},[E]),T(()=>{let e;function t(){let t=p(g)?g(D().data):g;t&&-1!==e&&(e=setTimeout(r,t))}function r(){!D().error&&(m||A().isVisible())&&(v||A().isOnline())?ed(ea).then(t):t()}return t(),()=>{e&&(clearTimeout(e),e=-1)}},[g,m,v,E]),(0,a.useDebugValue)(en),o&&h(ee)&&E){if(!z&&L)throw Error("Fallback data is required when using suspense in SSR.");M.current=t,R.current=r,U.current=!1;let e=P[E];if(h(e)||es(eh(e)),h(et)){let e=ed(ea);h(en)||(e.status="fulfilled",e.value=!0),es(e)}else throw et}return{mutate:eh,get data(){return q.data=!0,en},get error(){return q.error=!0,et},get isValidating(){return q.isValidating=!0,eu},get isLoading(){return q.isLoading=!0,ec}}}),ec=a.use||(e=>{if("pending"===e.status)throw e;if("fulfilled"===e.status)return e.value;if("rejected"===e.status)throw e.reason;throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}),ed={dedupe:!0};d.defineProperty(Y,"defaultValue",{value:q});let eh=en((e,t,r)=>{let{cache:n,compare:i,suspense:o,fallbackData:s,revalidateOnMount:u,revalidateIfStale:d,refreshInterval:g,refreshWhenHidden:m,refreshWhenOffline:v,keepPreviousData:k}=r,[y,w,S,P]=b.get(n),[E,C]=x(e),O=(0,a.useRef)(!1),U=(0,a.useRef)(!1),j=(0,a.useRef)(E),M=(0,a.useRef)(t),R=(0,a.useRef)(r),A=()=>R.current,N=()=>A().isVisible()&&A().isOnline(),[D,B,$,J]=_(n,E),q=(0,a.useRef)({}).current,G=h(s)?r.fallback[E]:s,K=(e,t)=>{for(let r in q)if("data"===r){if(!i(e[r],t[r])&&(!h(e[r])||!i(en,t[r])))return!1}else if(t[r]!==e[r])return!1;return!0},Y=(0,a.useMemo)(()=>{let e=!!E&&!!t&&(h(u)?!A().isPaused()&&!o&&(!!h(d)||d):u),r=t=>{let r=f(t);return(delete r._k,e)?{isValidating:!0,isLoading:!0,...r}:r},n=D(),i=J(),s=r(n),a=n===i?s:r(i),l=s;return[()=>{let e=r(D());return K(e,l)?(l.data=e.data,l.isLoading=e.isLoading,l.isValidating=e.isValidating,l.error=e.error,l):(l=e,e)},()=>a]},[n,E]),H=(0,l.useSyncExternalStore)((0,a.useCallback)(e=>$(E,(t,r)=>{K(r,t)||e()}),[n,E]),Y[0],Y[1]),Q=!O.current,X=y[E]&&y[E].length>0,Z=H.data,ee=h(Z)?G:Z,et=H.error,er=(0,a.useRef)(ee),en=k?h(Z)?er.current:Z:ee,eo=(!X||!!h(et))&&(Q&&!h(u)?u:!A().isPaused()&&(o?!h(ee)&&d:h(ee)||d)),es=!!(E&&t&&Q&&eo),ea=h(H.isValidating)?es:H.isValidating,el=h(H.isLoading)?es:H.isLoading,eu=(0,a.useCallback)(async e=>{let t,n;let o=M.current;if(!E||!o||U.current||A().isPaused())return!1;let s=!0,a=e||{},l=!S[E]||!a.dedupe,u=()=>z?!U.current&&E===j.current&&O.current:E===j.current,d={isValidating:!1,isLoading:!1},f=()=>{B(d)},g=()=>{let e=S[E];e&&e[1]===n&&delete S[E]},m={isValidating:!0};h(D().data)&&(m.isLoading=!0);try{if(l&&(B(m),r.loadingTimeout&&h(D().data)&&setTimeout(()=>{s&&u()&&A().onLoadingSlow(E,r)},r.loadingTimeout),S[E]=[o(C),V()]),[t,n]=S[E],t=await t,l&&setTimeout(g,r.dedupingInterval),!S[E]||S[E][1]!==n)return l&&u()&&A().onDiscarded(E),!1;d.error=c;let e=w[E];if(!h(e)&&(n<=e[0]||n<=e[1]||0===e[1]))return f(),l&&u()&&A().onDiscarded(E),!1;let a=D().data;d.data=i(a,t)?a:t,l&&u()&&A().onSuccess(t,E,r)}catch(r){g();let e=A(),{shouldRetryOnError:t}=e;!e.isPaused()&&(d.error=r,l&&u()&&(e.onError(r,E,e),(!0===t||p(t)&&t(r))&&(!A().revalidateOnFocus||!A().revalidateOnReconnect||N())&&e.onErrorRetry(r,E,e,e=>{let t=y[E];t&&t[0]&&t[0](W.ERROR_REVALIDATE_EVENT,e)},{retryCount:(a.retryCount||0)+1,dedupe:!0})))}return s=!1,f(),!0},[E,n]),eh=(0,a.useCallback)((...e)=>F(n,j.current,...e),[]);if(T(()=>{M.current=t,R.current=r,h(Z)||(er.current=Z)}),T(()=>{if(!E)return;let e=eu.bind(c,ed),t=0,r=ei(E,y,(r,n={})=>{if(r==W.FOCUS_EVENT){let r=Date.now();A().revalidateOnFocus&&r>t&&N()&&(t=r+A().focusThrottleInterval,e())}else if(r==W.RECONNECT_EVENT)A().revalidateOnReconnect&&N()&&e();else if(r==W.MUTATE_EVENT)return eu();else if(r==W.ERROR_REVALIDATE_EVENT)return eu(n)});return U.current=!1,j.current=E,O.current=!0,B({_k:C}),eo&&(h(ee)||L?e():I(e)),()=>{U.current=!0,r()}},[E]),T(()=>{let e;function t(){let t=p(g)?g(D().data):g;t&&-1!==e&&(e=setTimeout(r,t))}function r(){!D().error&&(m||A().isVisible())&&(v||A().isOnline())?eu(ed).then(t):t()}return t(),()=>{e&&(clearTimeout(e),e=-1)}},[g,m,v,E]),(0,a.useDebugValue)(en),o&&h(ee)&&E){if(!z&&L)throw Error("Fallback data is required when using suspense in SSR.");M.current=t,R.current=r,U.current=!1;let e=P[E];if(h(e)||ec(eh(e)),h(et)){let e=eu(ed);h(en)||(e.status="fulfilled",e.value=!0),ec(e)}else throw et}return{mutate:eh,get data(){return q.data=!0,en},get error(){return q.error=!0,et},get isValidating(){return q.isValidating=!0,ea},get isLoading(){return q.isLoading=!0,el}}}),ep=e=>x(e?e(0,null):null)[0],ef=Promise.resolve(),eg=(n=e=>(t,r,n)=>{let i;let o=(0,a.useRef)(!1),{cache:s,initialSize:u=1,revalidateAll:d=!1,persistSize:f=!1,revalidateFirstPage:g=!0,revalidateOnMount:m=!1,parallel:v=!1}=n,[,,,k]=b.get($);try{(i=ep(t))&&(i=H+i)}catch(e){}let[y,w,S]=_(s,i),P=(0,a.useCallback)(()=>h(y()._l)?u:y()._l,[s,i,u]);(0,l.useSyncExternalStore)((0,a.useCallback)(e=>i?S(i,()=>{e()}):()=>{},[s,i]),P,P);let E=(0,a.useCallback)(()=>{let e=y()._l;return h(e)?u:e},[i,u]),C=(0,a.useRef)(E());T(()=>{if(!o.current){o.current=!0;return}i&&w({_l:f?C.current:E()})},[i,s]);let O=m&&!o.current,U=e(i,async e=>{let i=y()._i,o=y()._r;w({_r:c});let a=[],l=E(),[u]=_(s,e),p=u().data,f=[],m=null;for(let e=0;e<l;++e){let[l,u]=x(t(e,v?null:m));if(!l)break;let[c,b]=_(s,l),y=c().data,w=d||i||h(y)||g&&!e&&!h(p)||O||p&&!h(p[e])&&!n.compare(p[e],y);if(r&&("function"==typeof o?o(y,u):w)){let t=async()=>{if(l in k){let e=k[l];delete k[l],y=await e}else y=await r(u);b({data:y,_k:u}),a[e]=y};v?f.push(t):await t()}else a[e]=y;v||(m=y)}return v&&await Promise.all(f.map(e=>e())),w({_i:c}),a},n),j=(0,a.useCallback)(function(e,t){let r="boolean"==typeof t?{revalidate:t}:t||{},n=!1!==r.revalidate;return i?(n&&(h(e)?w({_i:!0,_r:r.revalidate}):w({_i:!1,_r:r.revalidate})),arguments.length?U.mutate(e,{...r,revalidate:n}):U.mutate()):ef},[i,s]),M=(0,a.useCallback)(e=>{let r;if(!i)return ef;let[,n]=_(s,i);if(p(e)?r=e(E()):"number"==typeof e&&(r=e),"number"!=typeof r)return ef;n({_l:r}),C.current=r;let o=[],[a]=_(s,i),l=null;for(let e=0;e<r;++e){let[r]=x(t(e,l)),[n]=_(s,r),i=r?n().data:c;if(h(i))return j(a().data);o.push(i),l=i}return j(o)},[i,s,j,E]);return{size:E(),setSize:M,mutate:j,get data(){return U.data},get error(){return U.error},get isValidating(){return U.isValidating},get isLoading(){return U.isLoading}}},(...e)=>{let[t,r,i]=Z(e),o=(i.use||[]).concat(n);return eh(t,r,{...i,use:o})});function em(e,t){if(!e)throw"string"==typeof t?Error(t):Error(`${t.displayName} not found`)}var ev=(e,t)=>{let{assertCtxFn:r=em}=t||{},n=a.createContext(void 0);return n.displayName=e,[n,()=>{let t=a.useContext(n);return r(t,`${e} not found`),t.value},()=>{let e=a.useContext(n);return e?e.value:{}}]},ek={};(0,s.r2)(ek,{SWRConfig:()=>el,useSWR:()=>eu,useSWRInfinite:()=>eg}),(0,s.yA)(ek,i);var[eb,ey]=ev("ClerkInstanceContext"),[ew,eS]=ev("UserContext"),[eP,eE]=ev("ClientContext"),[eC,e_]=ev("SessionContext");a.createContext({});var[eO,eU]=ev("OrganizationContext"),ej=({children:e,organization:t,swrConfig:r})=>a.createElement(el,{value:r},a.createElement(eO.Provider,{value:{value:{organization:t}}},e));function eM(e){if(!a.useContext(eb)){if("function"==typeof e){e();return}throw Error(`${e} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider`)}}function ez(e,t){let r=new Set(Object.keys(t)),n={};for(let t of Object.keys(e))r.has(t)||(n[t]=e[t]);return n}var eL=(e,t)=>{var r,n,i;let o="boolean"==typeof e&&e,s=(0,a.useRef)(o?t.initialPage:null!=(r=null==e?void 0:e.initialPage)?r:t.initialPage),l=(0,a.useRef)(o?t.pageSize:null!=(n=null==e?void 0:e.pageSize)?n:t.pageSize),u={};for(let r of Object.keys(t))u[r]=o?t[r]:null!=(i=null==e?void 0:e[r])?i:t[r];return{...u,initialPage:s.current,pageSize:l.current}},eI={dedupingInterval:6e4,focusThrottleInterval:12e4},eT=(e,t,r,n)=>{var i,o,s,l,u,c,d;let[h,p]=(0,a.useState)(null!=(i=e.initialPage)?i:1),f=(0,a.useRef)(null!=(o=e.initialPage)?o:1),g=(0,a.useRef)(null!=(s=e.pageSize)?s:10),m=null==(l=r.enabled)||l,v=null!=(u=r.infinite)&&u,k=null!=(c=r.keepPreviousData)&&c,b={...n,...e,initialPage:h,pageSize:g.current},{data:y,isValidating:w,isLoading:S,error:P,mutate:E}=eu(!v&&t&&m?b:null,e=>{let r=ez(e,n);return null==t?void 0:t(r)},{keepPreviousData:k,...eI}),{data:C,isLoading:_,isValidating:O,error:U,size:j,setSize:M,mutate:z}=eg(t=>v&&m?{...e,...n,initialPage:f.current+t,pageSize:g.current}:null,e=>{let r=ez(e,n);return null==t?void 0:t(r)},eI),L=(0,a.useMemo)(()=>v?j:h,[v,j,h]),I=(0,a.useCallback)(e=>{if(v){M(e);return}return p(e)},[M]),T=(0,a.useMemo)(()=>{var e,t;return v?null!=(e=null==C?void 0:C.map(e=>null==e?void 0:e.data).flat())?e:[]:null!=(t=null==y?void 0:y.data)?t:[]},[v,y,C]),R=(0,a.useMemo)(()=>{var e,t;return v?(null==(e=null==C?void 0:C[(null==C?void 0:C.length)-1])?void 0:e.total_count)||0:null!=(t=null==y?void 0:y.total_count)?t:0},[v,y,C]),A=v?_:S,x=v?O:w,N=null!=(d=v?U:P)?d:null,V=(0,a.useCallback)(()=>{I(e=>Math.max(0,e+1))},[I]),W=(0,a.useCallback)(()=>{I(e=>Math.max(0,e-1))},[I]),F=(f.current-1)*g.current,D=Math.ceil((R-F)/g.current),B=R-F*g.current>L*g.current,$=(L-1)*g.current>F*g.current,J=v?e=>z(e,{revalidate:!1}):e=>E(e,{revalidate:!1});return{data:T,count:R,error:N,isLoading:A,isFetching:x,isError:!!N,page:L,pageCount:D,fetchPage:I,fetchNext:V,fetchPrevious:W,hasNextPage:B,hasPreviousPage:$,revalidate:v?()=>z():()=>E(),setData:J}},eR={data:void 0,count:void 0,error:void 0,isLoading:!1,isFetching:!1,isError:!1,page:void 0,pageCount:void 0,fetchPage:void 0,fetchNext:void 0,fetchPrevious:void 0,hasNextPage:!1,hasPreviousPage:!1,revalidate:void 0,setData:void 0},eA=e=>{var t,r,n;let{domains:i,membershipRequests:s,memberships:a,invitations:l}=e||{};eM("useOrganization");let{organization:u}=eU(),c=e_(),d=eL(i,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1,enrollmentMode:void 0}),h=eL(s,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),p=eL(a,{initialPage:1,pageSize:10,role:void 0,keepPreviousData:!1,infinite:!1}),f=eL(l,{initialPage:1,pageSize:10,status:["pending"],keepPreviousData:!1,infinite:!1}),g=ey();null==(t=g.telemetry)||t.record((0,o.J)("useOrganization"));let m=void 0===i?void 0:{initialPage:d.initialPage,pageSize:d.pageSize,enrollmentMode:d.enrollmentMode},v=void 0===s?void 0:{initialPage:h.initialPage,pageSize:h.pageSize,status:h.status},k=void 0===a?void 0:{initialPage:p.initialPage,pageSize:p.pageSize,role:p.role},b=void 0===l?void 0:{initialPage:f.initialPage,pageSize:f.pageSize,status:f.status},y=eT({...m},null==u?void 0:u.getDomains,{keepPreviousData:d.keepPreviousData,infinite:d.infinite,enabled:!!m},{type:"domains",organizationId:null==u?void 0:u.id}),w=eT({...v},null==u?void 0:u.getMembershipRequests,{keepPreviousData:h.keepPreviousData,infinite:h.infinite,enabled:!!v},{type:"membershipRequests",organizationId:null==u?void 0:u.id}),S=eT(k||{},null==u?void 0:u.getMemberships,{keepPreviousData:p.keepPreviousData,infinite:p.infinite,enabled:!!k},{type:"members",organizationId:null==u?void 0:u.id}),P=eT({...b},null==u?void 0:u.getInvitations,{keepPreviousData:f.keepPreviousData,infinite:f.infinite,enabled:!!b},{type:"invitations",organizationId:null==u?void 0:u.id});return void 0===u?{isLoaded:!1,organization:void 0,membership:void 0,domains:eR,membershipRequests:eR,memberships:eR,invitations:eR}:null===u?{isLoaded:!0,organization:null,membership:null,domains:null,membershipRequests:null,memberships:null,invitations:null}:!g.loaded&&u?{isLoaded:!0,organization:u,membership:void 0,domains:eR,membershipRequests:eR,memberships:eR,invitations:eR}:{isLoaded:g.loaded,organization:u,membership:(r=c.user.organizationMemberships,n=u.id,r.find(e=>e.organization.id===n)),domains:y,membershipRequests:w,memberships:S,invitations:P}},ex={data:void 0,count:void 0,error:void 0,isLoading:!1,isFetching:!1,isError:!1,page:void 0,pageCount:void 0,fetchPage:void 0,fetchNext:void 0,fetchPrevious:void 0,hasNextPage:!1,hasPreviousPage:!1,revalidate:void 0,setData:void 0},eN=e=>{var t;let{userMemberships:r,userInvitations:n,userSuggestions:i}=e||{};eM("useOrganizationList");let s=eL(r,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1}),a=eL(n,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),l=eL(i,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),u=ey(),c=eS();null==(t=u.telemetry)||t.record((0,o.J)("useOrganizationList"));let d=void 0===r?void 0:{initialPage:s.initialPage,pageSize:s.pageSize},h=void 0===n?void 0:{initialPage:a.initialPage,pageSize:a.pageSize,status:a.status},p=void 0===i?void 0:{initialPage:l.initialPage,pageSize:l.pageSize,status:l.status},f=!!(u.loaded&&c),g=eT(d||{},null==c?void 0:c.getOrganizationMemberships,{keepPreviousData:s.keepPreviousData,infinite:s.infinite,enabled:!!d},{type:"userMemberships",userId:null==c?void 0:c.id}),m=eT({...h},null==c?void 0:c.getOrganizationInvitations,{keepPreviousData:a.keepPreviousData,infinite:a.infinite,enabled:!!h},{type:"userInvitations",userId:null==c?void 0:c.id}),v=eT({...p},null==c?void 0:c.getOrganizationSuggestions,{keepPreviousData:l.keepPreviousData,infinite:l.infinite,enabled:!!p},{type:"userSuggestions",userId:null==c?void 0:c.id});return f?{isLoaded:f,setActive:u.setActive,createOrganization:u.createOrganization,userMemberships:g,userInvitations:m,userSuggestions:v}:{isLoaded:!1,createOrganization:void 0,setActive:void 0,userMemberships:ex,userInvitations:ex,userSuggestions:ex}};"undefined"!=typeof window?a.useLayoutEffect:a.useEffect;var eV=()=>{eM("useSession");let e=e_();return void 0===e?{isLoaded:!1,isSignedIn:void 0,session:void 0}:null===e?{isLoaded:!0,isSignedIn:!1,session:null}:{isLoaded:!0,isSignedIn:!0,session:e}},eW=()=>{eM("useSessionList");let e=ey(),t=eE();return t?{isLoaded:!0,sessions:t.sessions,setActive:e.setActive}:{isLoaded:!1,sessions:void 0,setActive:void 0}};function eF(){eM("useUser");let e=eS();return void 0===e?{isLoaded:!1,isSignedIn:void 0,user:void 0}:null===e?{isLoaded:!0,isSignedIn:!1,user:null}:{isLoaded:!0,isSignedIn:!0,user:e}}var eD=()=>(eM("useClerk"),ey()),eB=Object.prototype.hasOwnProperty;function e$(e,t,r){for(r of e.keys())if(eJ(r,t))return r}function eJ(e,t){var r,n,i;if(e===t)return!0;if(e&&t&&(r=e.constructor)===t.constructor){if(r===Date)return e.getTime()===t.getTime();if(r===RegExp)return e.toString()===t.toString();if(r===Array){if((n=e.length)===t.length)for(;n--&&eJ(e[n],t[n]););return -1===n}if(r===Set){if(e.size!==t.size)return!1;for(n of e)if((i=n)&&"object"==typeof i&&!(i=e$(t,i))||!t.has(i))return!1;return!0}if(r===Map){if(e.size!==t.size)return!1;for(n of e)if((i=n[0])&&"object"==typeof i&&!(i=e$(t,i))||!eJ(n[1],t.get(i)))return!1;return!0}if(r===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(r===DataView){if((n=e.byteLength)===t.byteLength)for(;n--&&e.getInt8(n)===t.getInt8(n););return -1===n}if(ArrayBuffer.isView(e)){if((n=e.byteLength)===t.byteLength)for(;n--&&e[n]===t[n];);return -1===n}if(!r||"object"==typeof e){for(r in n=0,e)if(eB.call(e,r)&&++n&&!eB.call(t,r)||!(r in t)||!eJ(e[r],t[r]))return!1;return Object.keys(t).length===n}}return e!=e&&t!=t}var eq=eJ}}]);