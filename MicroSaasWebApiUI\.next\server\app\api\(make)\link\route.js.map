{"version": 3, "file": "app/api/(make)/link/route.js", "mappings": "oFAAAA,CAAAA,EAAAC,OAAA,CAAAC,QAAA,0CCAAF,CAAAA,EAAAC,OAAA,CAAAC,QAAA,kZEKO,IAAMC,EAAU,gBACVC,EAAU,OAEhB,eAAeC,EAAIC,CAAgB,EACzC,GAAI,CACH,IAAMC,EAAO,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,IAEbC,EAAYH,EAAII,OAAO,CAACC,YAAY,CAACC,GAAG,CAAC,aAE/C,GAAI,CAACL,EACJ,OAAOM,EAAAA,EAAYA,CAACC,IAAI,CACvB,CAAEC,MAAO,cAAe,EACxB,CAAEC,OAAQ,GAAI,GAIhB,GAAI,CAACP,EACJ,OAAOI,EAAAA,EAAYA,CAACC,IAAI,CACvB,CAAEC,MAAO,wBAAyB,EAClC,CAAEC,OAAQ,GAAI,GAIhB,IAAMC,EAAU,MAAMC,EAAAA,CAAMA,CAACD,OAAO,CAACE,UAAU,CAAC,CAC/CC,MAAO,CACNC,GAAIZ,CACL,CACD,GAEA,GAAI,CAACQ,GAASK,YACb,OAAOT,EAAAA,EAAYA,CAACC,IAAI,CACvB,CAAEC,MAAO,iDAAkD,EAC3D,CAAEC,OAAQ,GAAI,GAIhB,OAAOH,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACxBS,QAAS,GACTD,YAAaL,EAAQK,WAAW,EAElC,CAAE,MAAOE,EAAK,CAEb,OADAC,QAAQV,KAAK,CAAC,oBAAqBS,GAC5BX,EAAAA,EAAYA,CAACC,IAAI,CACvB,CAAEC,MAAO,8BAA+B,EACxC,CAAEC,OAAQ,GAAI,EAEhB,CACD,CC7CA,IAAAU,EAAA,IAAwBC,EAAAC,mBAAmB,EAC3CC,WAAA,CACAC,KAAcC,EAAAC,CAAS,CAAAC,SAAA,CACvBC,KAAA,yBACAC,SAAA,YACAC,SAAA,QACAC,WAAA,2BACA,EACAC,iBAAA,sEACAC,iBAVA,GAWAC,SAAYC,CACZ,GAIA,CAAQC,oBAAAA,CAAA,CAAAC,6BAAAA,CAAA,CAAAC,YAAAA,CAAA,EAAiElB,EACzEmB,EAAA,yBACA,SAAAC,IACA,MAAW,GAAAC,EAAAC,EAAA,EAAW,CACtBJ,YAAAA,EACAD,6BAAAA,CACA,EACA,CC1BO,IAAAM,EAAqBC,EAC5BC,EAAeC,EAAAC,CAAsB,CAAAC,IAAA,CAAM5B,wECF3C,eAAAlB,IACE+C,EAAQ,MACV,IAAUC,OAAAA,CAAA,EAAW,GAAAC,EAAAC,CAAA,WACrB,EAGS,GAAAC,EAAAC,CAAA,IAAWC,KAAA,CAAAC,OAAA,CAAAN,GAFpB,IAGA,gDCLA,IAAAO,EAFe,GAAIC,UAAAA,YAAYA", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/ignored|C:\\Projects\\PersonalPortal\\node_modules\\crypto-js|crypto", "webpack://_N_E/./src/app/api/(make)/link/route.ts", "webpack://_N_E/./src/app/api/(make)/link/route.ts?de80", "webpack://_N_E/?3cc9", "webpack://_N_E/./node_modules/@clerk/nextjs/dist/esm/app-router/server/currentUser.js", "webpack://_N_E/./src/libs/prisma.ts"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "/* (ignored) */", "import prisma from '@/libs/prisma'\r\nimport { currentUser } from '@clerk/nextjs/server'\r\nimport { NextRequest, NextResponse } from 'next/server'\r\n\r\n// Mark this route as dynamic\r\nexport const dynamic = 'force-dynamic'\r\nexport const runtime = 'edge'\r\n\r\nexport async function GET(req: NextRequest) {\r\n\ttry {\r\n\t\tconst user = await currentUser()\r\n\t\t// Take projectID from the query params\r\n\t\tconst projectID = req.nextUrl.searchParams.get('projectID')\r\n\r\n\t\tif (!user) {\r\n\t\t\treturn NextResponse.json(\r\n\t\t\t\t{ error: 'Unauthorized' },\r\n\t\t\t\t{ status: 401 }\r\n\t\t\t)\r\n\t\t}\r\n\r\n\t\tif (!projectID) {\r\n\t\t\treturn NextResponse.json(\r\n\t\t\t\t{ error: 'Project ID is required' },\r\n\t\t\t\t{ status: 400 }\r\n\t\t\t)\r\n\t\t}\r\n\r\n\t\tconst project = await prisma.project.findUnique({\r\n\t\t\twhere: {\r\n\t\t\t\tid: projectID,\r\n\t\t\t},\r\n\t\t})\r\n\r\n\t\tif (!project?.webhookLink) {\r\n\t\t\treturn NextResponse.json(\r\n\t\t\t\t{ error: 'Project not found or webhook link not available' },\r\n\t\t\t\t{ status: 404 }\r\n\t\t\t)\r\n\t\t}\r\n\r\n\t\treturn NextResponse.json({ \r\n\t\t\tsuccess: true, \r\n\t\t\twebhookLink: project.webhookLink \r\n\t\t})\r\n\t} catch (err) {\r\n\t\tconsole.error('Connection error:', err)\r\n\t\treturn NextResponse.json(\r\n\t\t\t{ error: 'An unexpected error occurred' },\r\n\t\t\t{ status: 500 }\r\n\t\t)\r\n\t}\r\n}\r\n", "import { AppRouteRouteModule } from \"next/dist/server/future/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/future/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Projects\\\\PersonalPortal\\\\src\\\\app\\\\api\\\\(make)\\\\link\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/(make)/link/route\",\n        pathname: \"/api/link\",\n        filename: \"route\",\n        bundlePath: \"app/api/(make)/link/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\PersonalPortal\\\\src\\\\app\\\\api\\\\(make)\\\\link\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/(make)/link/route\";\nfunction patchFetch() {\n    return _patchFetch({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\nexport { routeModule, requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, originalPathname, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "import { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2F(make)%2Flink%2Froute&page=%2Fapi%2F(make)%2Flink%2Froute&pagePath=private-next-app-dir%2Fapi%2F(make)%2Flink%2Froute.ts&appDir=C%3A%5CProjects%5CPersonalPortal%5Csrc%5Capp&appPaths=%2Fapi%2F(make)%2Flink%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/(make)/link/route.ts?__next_edge_ssr_entry__\";\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule);\n\n//# sourceMappingURL=edge-app-route.js.map", "import { clerkClient } from \"../../server/clerkClient\";\nimport { auth } from \"./auth\";\nasync function currentUser() {\n  require(\"server-only\");\n  const { userId } = auth();\n  if (!userId) {\n    return null;\n  }\n  return clerkClient().users.getUser(userId);\n}\nexport {\n  currentUser\n};\n//# sourceMappingURL=currentUser.js.map", "import { PrismaClient } from \"@prisma/client\";\r\n\r\nconst prisma = new PrismaClient();\r\n\r\nexport default prisma;\r\n"], "names": ["module", "exports", "require", "dynamic", "runtime", "GET", "req", "user", "currentUser", "projectID", "nextUrl", "searchParams", "get", "NextResponse", "json", "error", "status", "project", "prisma", "findUnique", "where", "id", "webhookLink", "success", "err", "console", "routeModule", "module_compiled", "AppRouteRouteModule", "definition", "kind", "route_kind", "x", "APP_ROUTE", "page", "pathname", "filename", "bundlePath", "resolvedPagePath", "nextConfigOutput", "userland", "route_namespaceObject", "requestAsyncStorage", "staticGenerationAsyncStorage", "serverHooks", "originalPathname", "patchFetch", "patch_fetch", "XH", "ComponentMod", "route_next_edge_ssr_entry_namespaceObject", "next_edge_app_route_loaderabsolutePagePath_private_next_app_dir_2Fapi_2F_make_2Flink_2Froute_ts_page_2Fapi_2F_make_2Flink_2Froute_appDirLoader_bmV4dC1hcHAtbG9hZGVyP25hbWU9YXBwJTJGYXBpJTJGKG1ha2UpJTJGbGluayUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGKG1ha2UpJTJGbGluayUyRnJvdXRlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGKG1ha2UpJTJGbGluayUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDUHJvamVjdHMlNUNQZXJzb25hbFBvcnRhbCU1Q3NyYyU1Q2FwcCZhcHBQYXRocz0lMkZhcGklMkYobWFrZSklMkZsaW5rJTJGcm91dGUmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0Qh_nextConfigOutput_preferredRegion_middlewareConfig_e30_3D_", "edge_route_module_wrapper", "a", "wrap", "__webpack_require__", "userId", "_auth__WEBPACK_IMPORTED_MODULE_0__", "I", "_server_clerkClient__WEBPACK_IMPORTED_MODULE_1__", "N", "users", "getUser", "__WEBPACK_DEFAULT_EXPORT__", "PrismaClient"], "sourceRoot": ""}