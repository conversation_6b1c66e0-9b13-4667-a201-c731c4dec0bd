﻿using Swashbuckle.AspNetCore.Filters;
using System.Text.Json;

namespace MicroSaasWebApi.Models.PXW
{
    public class RequestBodyExample : IExamplesProvider<RequestBody>
    {
        public RequestBody GetExamples()
        {
            return new RequestBody(
                component: "ProfilesList",
                tenantGuid: "{{TenantGuid}}", // replace with an actual tenant guid for the example
                controller: "PXW/{{ControllerName}}",
                action: "{{MethodName}}",
                language: "English", //(Optional) replace with an actual language code 01: English
                pathParameters: new Dictionary<string, JsonElement>(),
                queryParameters: new Dictionary<string, JsonElement>(),
                payloadParameters: new Dictionary<string, JsonElement>()
            );
        }
    }

}
