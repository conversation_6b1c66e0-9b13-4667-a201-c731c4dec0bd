"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[832],{11297:function(e,t,a){a.d(t,{default:function(){return b}});var s=a(57437),l=a(33145),r=a(24462),i=a(27648),n=a(61002),o=a(75135),d=a(50598),c=a(89345),m=a(42351),u=a(74767);let x={github:o.Z,linkedin:d.Z,email:c.Z,x:m.Z,youtube:u.Z};var h={src:"/_next/static/media/footer-bg1.96b003a2.svg",height:350,width:1440,blurWidth:0,blurHeight:0},f={src:"/_next/static/media/footer-bg2.0853b915.svg",height:548,width:1288,blurWidth:0,blurHeight:0};let g=[{title:"Terms of services",link:"https://elegant-nightshade-79e.notion.site/Terms-Services-4335df481a50486ca26c81e699a2fbf1"},{title:"Privacy Policy",link:"https://elegant-nightshade-79e.notion.site/Privacy-policy-f2a3142bfb8c4b16b80369c19263c23a"},{title:"Licences",link:"#"}],p=[{name:"LinkedIn",link:"https://linkedin.com/in/javianpicardo",icon:x.linkedin},{name:"YouTube",link:"https://www.youtube.com/@javianpicardo",icon:x.youtube},{name:"X",link:"https://x.com/javian_picardo",icon:x.x}];var b=()=>(0,s.jsxs)("div",{className:"relative flex justify-center items-center w-full",children:[(0,s.jsx)(l.default,{src:h,alt:"background",fill:!0,objectFit:"cover",className:"z-0 block dark:hidden"}),(0,s.jsx)(l.default,{src:f,alt:"background",fill:!0,objectFit:"cover",className:"z-0 hidden dark:block"}),(0,s.jsx)("div",{className:"relative z-10 max-w-[1440px] w-full h-full px-4 sm:px-12 py-12",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 h-full gap-x-4 gap-y-12",children:[(0,s.jsx)("div",{className:"flex flex-col !min-h-[inherit] justify-between gap-8",children:(0,s.jsx)(i.default,{href:"/",children:(0,s.jsx)(r.TR,{})})}),(0,s.jsxs)("div",{className:"sm:order-3 xl:order-2",children:[(0,s.jsx)("p",{className:"uppercase text-[#86898E] dark:text-[#808389] font-semibold text-base mb-8",children:"Legal"}),(0,s.jsx)(n.Z,{nav_links:g,isFooter:!0})]}),(0,s.jsxs)("div",{className:"sm:order-2 xl:order-last",children:[(0,s.jsx)("p",{className:"uppercase text-[#86898E] dark:text-[#808389] font-semibold text-base mb-8",children:"Connect With Me"}),(0,s.jsx)("div",{className:"flex items-center gap-4",children:p.map(e=>(0,s.jsx)(i.default,{href:e.link,target:"_blank",rel:"noopener noreferrer",children:(0,s.jsx)("div",{className:"shadow-lg bg-white h-[32px] w-[32px] flex items-center justify-center rounded-[8px] hover:bg-gray-100 transition-colors",children:(0,s.jsx)(e.icon,{className:"h-5 w-5 text-zinc-600"})})},e.name))})]})]})})]})},83938:function(e,t,a){a.d(t,{default:function(){return E}});var s=a(57437),l=a(24462),r=a(12381),i=a(87770),n=a(33145),o=a(27648),d=a(99376),c=e=>{let{text:t="Login/SignUp",extraStyle:a}=e,l=(0,d.useRouter)(),{isSignedIn:c,user:m}=(0,i.aF)(),{openSignIn:u,signOut:x}=(0,i.ll)();if(c&&m){var h,f;return(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 cursor-pointer rounded-full bg-[#006fee] text-white px-4 py-1.5 text-sm font-medium hover:bg-blue-700 transition-all duration-300 ".concat(a||""),children:[m.hasImage?(0,s.jsx)(n.default,{src:m.imageUrl,alt:m.firstName||"Account",className:"w-6 h-6 rounded-full shrink-0",referrerPolicy:"no-referrer",width:24,height:24}):(0,s.jsx)("span",{className:"w-6 h-6 bg-base-300 flex justify-center items-center rounded-full shrink-0",children:m.firstName?m.firstName.charAt(0):(null===(h=m.primaryEmailAddress)||void 0===h?void 0:h.emailAddress)||"A"}),m.firstName||(null===(f=m.primaryEmailAddress)||void 0===f?void 0:f.emailAddress)||"Account"]}),(0,s.jsx)("div",{className:"absolute right-0 mt-2 w-48 bg-white dark:bg-[#1E232C] border border-gray-200 dark:border-[#373C53] rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50",children:(0,s.jsxs)("div",{className:"py-1",children:[(0,s.jsx)(o.default,{href:"/",className:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-[#373C53]",children:"Home"}),(0,s.jsx)("div",{className:"border-t border-gray-200 dark:border-[#373C53] my-1"}),(0,s.jsx)("button",{className:"block w-full text-left px-4 py-2 text-sm text-red-500 hover:bg-gray-100 dark:hover:bg-[#373C53]",onClick:()=>x(()=>l.push("/")),children:"Logout"})]})})]})}return(0,s.jsx)(r.z,{className:"bg-[#006fee] border-none transition-all duration-300 rounded-full px-5 py-1.5 h-8 text-sm font-medium hover:bg-blue-700 ".concat(a||""),onClick:()=>{c?l.push("/"):u({redirectUrl:"/"})},children:t})},m=a(61002),u=a(2265),x=a(92685),h=a(90535),f=a(10090);let g=x.fC,p=x.xz;x.x8;let b=x.h_,v=u.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(x.aV,{className:(0,f.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...l,ref:t})});v.displayName=x.aV.displayName;let k=(0,h.j)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),j=u.forwardRef((e,t)=>{let{side:a="right",className:l,children:r,...i}=e;return(0,s.jsxs)(b,{children:[(0,s.jsx)(v,{}),(0,s.jsxs)(x.VY,{ref:t,className:(0,f.cn)(k({side:a}),l),...i,children:[r,(0,s.jsxs)(x.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,s.jsx)("p",{className:"outline-none",children:"✕"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});j.displayName=x.VY.displayName;let y=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,f.cn)("flex flex-col space-y-2 text-center sm:text-left",t),...a})};y.displayName="SheetHeader";let w=u.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(x.Dx,{ref:t,className:(0,f.cn)("text-lg font-semibold text-foreground",a),...l})});w.displayName=x.Dx.displayName,u.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(x.dk,{ref:t,className:(0,f.cn)("text-sm text-muted-foreground",a),...l})}).displayName=x.dk.displayName;var N=a(7150),C=a(25922);let z=[{icon:(0,s.jsx)(N.l3,{width:18,height:18}),title:"Blog",link:"/blog"}],A=()=>{let{setTheme:e,theme:t}=(0,C.F)();return(0,u.useEffect)(()=>{"light"!==t&&"dark"!==t&&e("light"),"true"!==sessionStorage.getItem("themeInitialized")&&sessionStorage.setItem("themeInitialized","true"),"dark"===t?(document.documentElement.classList.add("dark"),document.documentElement.classList.remove("light"),document.body.style.backgroundColor="#010814",document.body.style.color="white"):(document.documentElement.classList.add("light"),document.documentElement.classList.remove("dark"),document.body.style.backgroundColor="white",document.body.style.color="black")},[t,e]),(0,s.jsx)("div",{className:"flex items-center gap-2",children:(0,s.jsx)("button",{type:"button",onClick:()=>{let a="dark"===t?"light":"dark";console.log("Theme toggled to:",a),e(a),window.dispatchEvent(new CustomEvent("themeChange",{detail:{theme:a}}))},className:"flex items-center justify-center w-8 h-8 rounded-md transition-colors ".concat("light"===t?"bg-blue-100 text-blue-800":"bg-gray-700 text-yellow-300"),"aria-label":"Currently in ".concat("light"===t?"light":"dark"," mode. Click to switch to ").concat("light"===t?"dark":"light"," mode"),title:"Currently in ".concat("light"===t?"light":"dark"," mode. Click to switch to ").concat("light"===t?"dark":"light"," mode"),children:"light"===t?(0,s.jsx)(N.kO,{className:"w-5 h-5"}):(0,s.jsx)(N.JF,{className:"w-5 h-5"})})})},_=()=>{var e,t;let{isSignedIn:a,user:r}=(0,i.aF)(),{signOut:u}=(0,i.ll)(),x=(0,d.useRouter)();return(0,s.jsxs)(g,{children:[(0,s.jsx)(p,{children:(0,s.jsx)("div",{className:"text-black1 dark:text-white",children:(0,s.jsx)(N.qR,{})})}),(0,s.jsxs)(j,{className:"bg-white dark:bg-black1 px-0 pt-4 border-l-0 min-w-[320px]",children:[(0,s.jsx)(y,{children:(0,s.jsx)(w,{className:"text-black1 dark:text-white text-xl font-bold border-b border-[#b3b3b3] text-left pb-4 pl-4",children:"Menu"})}),(0,s.jsx)(o.default,{href:"/",className:"flex items-center gap-2 mt-8 mx-auto w-fit",children:(0,s.jsx)(l.TR,{})}),(0,s.jsx)("div",{className:"my-8 mx-auto w-fit",children:(0,s.jsx)(m.Z,{nav_links:z})}),a&&r?(0,s.jsxs)("div",{className:"flex flex-col gap-4 mb-8 mx-auto w-fit",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-4 text-black1 dark:text-white",children:[r.hasImage?(0,s.jsx)(n.default,{src:r.imageUrl,alt:r.firstName||"Account",className:"w-6 h-6 rounded-full shrink-0",referrerPolicy:"no-referrer",width:24,height:24}):(0,s.jsx)("span",{className:"w-6 h-6 bg-base-300 flex justify-center items-center rounded-full shrink-0",children:r.firstName?r.firstName.charAt(0):(null===(e=r.primaryEmailAddress)||void 0===e?void 0:e.emailAddress)||"A"}),(0,s.jsx)("span",{className:"font-medium",children:r.firstName||(null===(t=r.primaryEmailAddress)||void 0===t?void 0:t.emailAddress)||"Account"})]}),(0,s.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-4",children:[(0,s.jsx)(o.default,{href:"/",className:"block text-black1 dark:text-white hover:underline py-2",children:"Home"}),(0,s.jsx)(o.default,{href:"https://accounts.clerk.com/account",target:"_blank",rel:"noopener noreferrer",className:"block text-black1 dark:text-white hover:underline py-2",children:"Manage Profile"}),(0,s.jsx)("button",{className:"text-red-500 hover:underline text-left block w-full py-2",onClick:()=>u(()=>x.push("/")),children:"Logout"})]})]}):(0,s.jsx)("div",{className:"mb-8 mx-auto w-fit block",children:(0,s.jsx)(c,{text:"Login/SignUp"})})]})]})};var E=()=>(0,s.jsx)("div",{className:"w-full fixed top-0 z-50 bg-white dark:bg-[#010814] border-b border-gray-200 dark:border-gray-800 shadow-sm",children:(0,s.jsxs)("div",{className:"container mx-auto max-w-[1440px] w-full flex justify-between items-center gap-4 px-4 sm:px-12 py-1.5",children:[(0,s.jsx)(o.default,{href:"/",className:"flex items-center",children:(0,s.jsx)(l.TR,{})}),(0,s.jsxs)("div",{className:"flex items-center gap-6",children:[(0,s.jsx)("div",{className:"hidden lg:flex items-center justify-end",children:(0,s.jsx)(m.Z,{nav_links:z})}),(0,s.jsxs)("div",{className:"hidden lg:flex items-center gap-4",children:[(0,s.jsx)("div",{dangerouslySetInnerHTML:{__html:'<a href="https://www.buymeacoffee.com/javian" target="_blank"><img src="https://cdn.buymeacoffee.com/buttons/v2/default-yellow.png" alt="Buy Me A Coffee" style="height: 32px !important;width: auto !important;" ></a>'}}),(0,s.jsx)(A,{}),(0,s.jsx)(c,{})]}),(0,s.jsxs)("div",{className:"lg:hidden flex gap-2",children:[(0,s.jsx)(A,{}),(0,s.jsx)(_,{})]})]})]})})},51133:function(e,t,a){var s=a(57437);t.Z=e=>{let{text:t,isLeft:a,icon:l,isDownload:r,isDisable:i,isSubmit:n=!0,isLoading:o}=e;return(0,s.jsx)("button",{type:n?"submit":"button",className:"text-white font-medium whitespace-nowrap text-sm px-5 py-1.5 h-8 rounded-full w-full transition-all duration-300 bg-[#006fee] hover:bg-blue-700",disabled:o,children:o?(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"animate-spin",children:(0,s.jsx)("path",{d:"M21 12a9 9 0 1 1-6.219-8.56"})})}):(0,s.jsxs)("span",{className:"flex items-center justify-center ".concat(l?"gap-2":""),children:[a&&(0,s.jsx)("span",{className:"".concat(r?"rotate-90":""),children:l}),(0,s.jsx)("span",{children:t}),!a&&!i&&(0,s.jsx)("span",{className:"".concat(r?"rotate-90":""),children:l})]})})}},24462:function(e,t,a){a.d(t,{TR:function(){return u}}),a(51133);var s=a(57437),l=a(7150);a(69064),a(99376),a(24457),a(53375);var r=a(2265),i=a(92685),n=a(10090);i.fC,i.xz;let o=i.h_;i.x8;let d=r.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(i.aV,{ref:t,className:(0,n.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...l})});d.displayName=i.aV.displayName,r.forwardRef((e,t)=>{let{className:a,children:l,...r}=e;return(0,s.jsxs)(o,{children:[(0,s.jsx)(d,{}),(0,s.jsxs)(i.VY,{ref:t,className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border-none bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...r,children:[l,(0,s.jsx)(i.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"})]})]})}).displayName=i.VY.displayName,r.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(i.Dx,{ref:t,className:(0,n.cn)("text-lg font-semibold leading-none tracking-tight",a),...l})}).displayName=i.Dx.displayName,r.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(i.dk,{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",a),...l})}).displayName=i.dk.displayName;var c={src:"/_next/static/media/logo.9487aadc.svg",height:1200,width:1200,blurWidth:0,blurHeight:0},m=a(33145);l.eM,l.BB;var u=e=>{let{isLarge:t=!1}=e;return(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(m.default,{src:c,width:24,height:24,layout:"lazy",alt:"logo",className:"".concat(t?"hidden":"")}),(0,s.jsxs)("div",{className:"flex gap-1 items-center",children:[(0,s.jsx)("p",{className:"text-black1 dark:text-white ".concat(t?"text-[20px] sm:text-[32px] leading-[20px] sm:leading-[32px] font-bold":"text-[20px] leading-[20px] font-semibold"),children:"Javian"}),(0,s.jsx)("p",{className:"text-white dark:text-black1 bg-black dark:bg-white font-semibold px-2 rounded-[3px] pb-[3px] pt-[2px] ".concat(t?"text-[20px] sm:text-[32px] leading-[20px] sm:leading-[32px]":"text-[20px] leading-[20px]"),children:"Picardo"})]})]})}},61002:function(e,t,a){a.d(t,{Z:function(){return n}});var s=a(57437);let l=e=>{let t=document.getElementById(e);t&&setTimeout(()=>{t.scrollIntoView({behavior:"smooth",block:"start"})},200)};var r=a(27648),i=a(99376),n=e=>{let{nav_links:t,isFooter:a=!1}=e,n=(0,i.useRouter)(),o=(0,i.usePathname)(),d=(e,t)=>{e.download||("/"===o&&(null==e?void 0:e.link)==="/"?l(t.toString()):(n.push(null==e?void 0:e.link),setTimeout(()=>{l(t.toString())},1500)))};return(0,s.jsx)("div",{className:"flex ".concat(a?"flex-col":"flex-col lg:flex-row lg:items-center justify-end"," gap-x-6 gap-y-4"),children:null==t?void 0:t.map((e,t)=>"/"===o&&(null==e?void 0:e.link)==="/"?(0,s.jsxs)("p",{className:"text-black1 dark:text-white cursor-pointer hover:text-[#006fee] dark:hover:text-[#006fee] transition-colors duration-300 ease-in-out ".concat(a?"font-medium":"text-center flex items-center font-medium text-base gap-2"),style:{lineHeight:"normal"},onClick:()=>d(e,t),children:[(0,s.jsx)("span",{children:null==e?void 0:e.icon})," ",null==e?void 0:e.title]},t):(0,s.jsxs)(r.default,{href:null==e?void 0:e.link,download:null==e?void 0:e.download,className:"text-black1 dark:text-white cursor-pointer hover:text-[#006fee] dark:hover:text-[#006fee] transition-colors duration-300 ease-in-out ".concat(a?"font-medium":"text-center flex items-center font-medium text-base gap-2"),style:{lineHeight:"normal"},onClick:()=>d(e,t),children:[(0,s.jsx)("span",{children:null==e?void 0:e.icon})," ",null==e?void 0:e.title]},t))})}},12381:function(e,t,a){a.d(t,{z:function(){return d}});var s=a(57437),l=a(2265),r=a(37053),i=a(90535),n=a(10090);let o=(0,i.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 dark:ring-offset-slate-950 dark:focus-visible:ring-slate-300",{variants:{variant:{default:"bg-slate-900 text-slate-50 hover:bg-slate-900/90 dark:bg-slate-50 dark:text-slate-900 dark:hover:bg-slate-50/90",destructive:"bg-red-500 text-slate-50 hover:bg-red-500/90 dark:bg-red-900 dark:text-slate-50 dark:hover:bg-red-900/90",outline:"border border-slate-200 bg-white hover:bg-slate-100 hover:text-slate-900 dark:border-slate-800 dark:bg-slate-950 dark:hover:bg-slate-800 dark:hover:text-slate-50",secondary:"bg-slate-100 text-slate-900 hover:bg-slate-100/80 dark:bg-slate-800 dark:text-slate-50 dark:hover:bg-slate-800/80",ghost:"hover:bg-slate-100 hover:text-slate-900 dark:hover:bg-slate-800 dark:hover:text-slate-50",link:"text-slate-900 underline-offset-4 hover:underline dark:text-slate-50"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=l.forwardRef((e,t)=>{let{className:a,variant:l,size:i,asChild:d=!1,...c}=e,m=d?r.g7:"button";return(0,s.jsx)(m,{className:(0,n.cn)(o({variant:l,size:i,className:a})),ref:t,...c})});d.displayName="Button"},10090:function(e,t,a){a.d(t,{cn:function(){return r}});var s=a(61994),l=a(53335);function r(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,l.m6)((0,s.W)(t))}}}]);