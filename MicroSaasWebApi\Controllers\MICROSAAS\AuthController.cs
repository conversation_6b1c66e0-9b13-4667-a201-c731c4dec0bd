using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Models.Auth;
using MicroSaasWebApi.Services.MICROSAAS.Interface;
using Swashbuckle.AspNetCore.Annotations;

namespace MicroSaasWebApi.Controllers.MICROSAAS
{
    /// <summary>
    /// Authentication controller for MicroSaaS template with Supabase integration
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;
        private readonly ILogger<AuthController> _logger;

        public AuthController(
            IAuthService authService,
            ILogger<AuthController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        /// <summary>
        /// Register a new user
        /// </summary>
        /// <param name="request">Registration details</param>
        /// <returns>Authentication response with tokens</returns>
        [HttpPost("register")]
        [SwaggerOperation(Summary = "Register new user", Description = "Creates a new user account with Supabase authentication")]
        [ProducesResponseType(typeof(AuthResponse), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status409Conflict)]
        public async Task<IActionResult> Register([FromBody] RegisterRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _authService.RegisterAsync(request);
                
                if (!result.Success)
                {
                    if (result.Message?.Contains("already exists") == true)
                    {
                        return Conflict(result);
                    }
                    return BadRequest(result);
                }

                return CreatedAtAction(nameof(Register), result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during user registration");
                return StatusCode(500, new AuthResponse 
                { 
                    Success = false, 
                    Message = "Internal server error during registration" 
                });
            }
        }

        /// <summary>
        /// Login user
        /// </summary>
        /// <param name="request">Login credentials</param>
        /// <returns>Authentication response with tokens</returns>
        [HttpPost("login")]
        [SwaggerOperation(Summary = "User login", Description = "Authenticates user with Supabase and returns JWT tokens")]
        [ProducesResponseType(typeof(AuthResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> Login([FromBody] LoginRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _authService.LoginAsync(request);
                
                if (!result.Success)
                {
                    return Unauthorized(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during user login");
                return StatusCode(500, new AuthResponse 
                { 
                    Success = false, 
                    Message = "Internal server error during login" 
                });
            }
        }

        /// <summary>
        /// Refresh access token
        /// </summary>
        /// <param name="refreshToken">Refresh token</param>
        /// <returns>New access token</returns>
        [HttpPost("refresh")]
        [SwaggerOperation(Summary = "Refresh token", Description = "Refreshes the access token using a valid refresh token")]
        [ProducesResponseType(typeof(AuthResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> RefreshToken([FromBody] string refreshToken)
        {
            try
            {
                if (string.IsNullOrEmpty(refreshToken))
                {
                    return BadRequest("Refresh token is required");
                }

                var result = await _authService.RefreshTokenAsync(refreshToken);
                
                if (!result.Success)
                {
                    return Unauthorized(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during token refresh");
                return StatusCode(500, new AuthResponse 
                { 
                    Success = false, 
                    Message = "Internal server error during token refresh" 
                });
            }
        }

        /// <summary>
        /// Logout user
        /// </summary>
        /// <returns>Success confirmation</returns>
        [HttpPost("logout")]
        [Authorize]
        [SwaggerOperation(Summary = "User logout", Description = "Logs out the current user and invalidates tokens")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> Logout()
        {
            try
            {
                var userId = User.FindFirst("sub")?.Value ?? User.FindFirst("user_id")?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("Invalid token");
                }

                await _authService.LogoutAsync(userId);
                return Ok(new { message = "Logged out successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout");
                return StatusCode(500, "Internal server error during logout");
            }
        }

        /// <summary>
        /// Request password reset
        /// </summary>
        /// <param name="request">Password reset request</param>
        /// <returns>Success confirmation</returns>
        [HttpPost("forgot-password")]
        [SwaggerOperation(Summary = "Request password reset", Description = "Sends a password reset email to the user")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> ForgotPassword([FromBody] PasswordResetRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                await _authService.RequestPasswordResetAsync(request.Email);
                
                // Always return success for security reasons (don't reveal if email exists)
                return Ok(new { message = "If the email exists, a password reset link has been sent." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during password reset request");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Verify email address
        /// </summary>
        /// <param name="request">Email verification request</param>
        /// <returns>Success confirmation</returns>
        [HttpPost("verify-email")]
        [SwaggerOperation(Summary = "Verify email", Description = "Verifies user email address using verification token")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> VerifyEmail([FromBody] EmailVerificationRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _authService.VerifyEmailAsync(request.Token);
                
                if (!result)
                {
                    return BadRequest("Invalid or expired verification token");
                }

                return Ok(new { message = "Email verified successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during email verification");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Update password
        /// </summary>
        /// <param name="request">Password update request</param>
        /// <returns>Success confirmation</returns>
        [HttpPost("update-password")]
        [Authorize]
        [SwaggerOperation(Summary = "Update password", Description = "Updates the current user's password")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> UpdatePassword([FromBody] PasswordUpdateRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var userId = User.FindFirst("sub")?.Value ?? User.FindFirst("user_id")?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("Invalid token");
                }

                var result = await _authService.UpdatePasswordAsync(userId, request.CurrentPassword, request.NewPassword);
                
                if (!result)
                {
                    return BadRequest("Current password is incorrect");
                }

                return Ok(new { message = "Password updated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during password update");
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
