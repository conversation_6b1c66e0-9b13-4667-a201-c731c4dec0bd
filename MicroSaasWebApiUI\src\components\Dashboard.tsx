'use client'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { useFormik } from 'formik'
import toast from 'react-hot-toast'
import * as Yup from 'yup'
import { Button } from './ui/button'

const validationSchema = Yup.object({
	apiKey: Yup.string().required('API Key is required'),
	organizationId: Yup.string().required('Organization ID is required'),
})

const Dashboard = () => {
	const formik = useFormik({
		initialValues: {
			apiKey: '',
			organizationId: '',
		},
		validationSchema,
		onSubmit: values => {
			console.log('Form submitted', values)
			toast.success('Settings saved')
		},
	})

	return (
		<div className='flex justify-center items-center w-full bg-white dark:bg-[#010814] my-16'>
			<div className='max-w-[1440px] w-full rounded-[16px] mx-4 sm:mx-12 dark:border dark:border-[#5f708b] bg-white dark:bg-gradient-to-r from-[#1E242D] to-[#0B111B] min-h-[300px] pb-6'>
				<div className='flex justify-between items-center w-full p-4 sm:p-8 dark:border-b dark:border-[#4D525A]'>
					<p className='text-black1 dark:text-white font-semibold text-xl'>
						.NET API Template Dashboard
					</p>
					<div className='flex gap-2 items-center'>
						<Avatar className='w-[32px] h-[32px]'>
							<AvatarImage src='/assets/avatar.svg' />
							<AvatarFallback>CN</AvatarFallback>
						</Avatar>
						<p className='text-black1 dark:text-white font-medium text-lg'>
							Developer
						</p>
					</div>
				</div>
				<div className='flex flex-col gap-6'>
					<h1 className='text-black1 dark:text-white font-semibold text-[32px] px-4 sm:px-8 py-4'>
						Your .NET API Template Access
					</h1>

					<div className='px-4 sm:px-8 grid md:grid-cols-2 gap-6'>
						<div className='bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white'>
							<h3 className='text-xl font-bold mb-4'>Download Template</h3>
							<p className='mb-4'>Get the complete .NET Web API template source code</p>
							<Button className='bg-white text-blue-600 hover:bg-gray-100'>
								Download ZIP
							</Button>
						</div>

						<div className='bg-gradient-to-r from-green-500 to-teal-600 rounded-lg p-6 text-white'>
							<h3 className='text-xl font-bold mb-4'>GitHub Repository</h3>
							<p className='mb-4'>Access the private GitHub repository with latest updates</p>
							<Button className='bg-white text-green-600 hover:bg-gray-100'>
								View Repository
							</Button>
						</div>
					</div>

					<div className='px-4 sm:px-8'>
						<h2 className='text-black1 dark:text-white font-medium text-xl mb-4'>
							Documentation & Resources
						</h2>
						<div className='grid md:grid-cols-3 gap-4'>
							<div className='border border-gray-300 dark:border-gray-700 rounded-lg p-4'>
								<h4 className='font-semibold text-black1 dark:text-white mb-2'>Setup Guide</h4>
								<p className='text-gray-600 dark:text-gray-400 text-sm mb-3'>
									Step-by-step instructions to get your API running
								</p>
								<Button variant="outline" size="sm">View Guide</Button>
							</div>

							<div className='border border-gray-300 dark:border-gray-700 rounded-lg p-4'>
								<h4 className='font-semibold text-black1 dark:text-white mb-2'>API Documentation</h4>
								<p className='text-gray-600 dark:text-gray-400 text-sm mb-3'>
									Complete API reference and examples
								</p>
								<Button variant="outline" size="sm">View Docs</Button>
							</div>

							<div className='border border-gray-300 dark:border-gray-700 rounded-lg p-4'>
								<h4 className='font-semibold text-black1 dark:text-white mb-2'>Video Tutorials</h4>
								<p className='text-gray-600 dark:text-gray-400 text-sm mb-3'>
									Watch detailed setup and customization videos
								</p>
								<Button variant="outline" size="sm">Watch Videos</Button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	)
}

export default Dashboard
