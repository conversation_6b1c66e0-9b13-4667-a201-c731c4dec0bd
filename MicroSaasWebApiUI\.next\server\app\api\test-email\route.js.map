{"version": 3, "file": "app/api/test-email/route.js", "mappings": "oFAAAA,CAAAA,EAAAC,OAAA,CAAAC,QAAA,0CCAAF,CAAAA,EAAAC,OAAA,CAAAC,QAAA,8XCGO,IAAMC,EAAU,gBAEVC,EAAU,OAEhB,eAAeC,EAAKC,CAAgB,EACzC,GAAI,CACF,GAAM,CAAEC,MAAAA,CAAK,CAAE,CAAG,MAAMD,EAAIE,IAAI,GAEhC,GAAI,CAACD,EACH,OAAOE,EAAAA,EAAYA,CAACD,IAAI,CAAC,CAAEE,MAAO,mBAAoB,EAAG,CAAEC,OAAQ,GAAI,GAGzEC,QAAQC,GAAG,CAAC,yBAA0BN,GAGtC,IAAMO,EAAS,MAAMC,EAAAA,CAAaA,CAACC,kBAAkB,CAACT,GAItD,OAFAK,QAAQC,GAAG,CAAC,2BAA4BC,GAEjCL,EAAAA,EAAYA,CAACD,IAAI,CAAC,CACvBS,QAAS,+BACTC,GAAIJ,EAAOI,EAAE,EAEjB,CAAE,MAAOR,EAAO,CAEd,OADAE,QAAQF,KAAK,CAAC,4BAA6BA,GACpCD,EAAAA,EAAYA,CAACD,IAAI,CAAC,CACvBE,MAAO,4BACPS,QAAST,EAAMO,OAAO,EACrB,CAAEN,OAAQ,GAAI,EACnB,CACF,CC1BA,IAAAS,EAAA,IAAwBC,EAAAC,mBAAmB,EAC3CC,WAAA,CACAC,KAAcC,EAAAC,CAAS,CAAAC,SAAA,CACvBC,KAAA,wBACAC,SAAA,kBACAC,SAAA,QACAC,WAAA,0BACA,EACAC,iBAAA,oEACAC,iBAVA,GAWAC,SAAYC,CACZ,GAIA,CAAQC,oBAAAA,CAAA,CAAAC,6BAAAA,CAAA,CAAAC,YAAAA,CAAA,EAAiElB,EACzEmB,EAAA,wBACA,SAAAC,IACA,MAAW,GAAAC,EAAAC,EAAA,EAAW,CACtBJ,YAAAA,EACAD,6BAAAA,CACA,EACA,CC1BO,IAAAM,EAAqBC,EAC5BC,EAAeC,EAAAC,CAAsB,CAAAC,IAAA,CAAM5B,+DC+G3C,IAAA6B,EAhH4B,CAE3BC,QAAS,iBAETC,eACC,sDAEDC,WAAY,wBAEZC,OAAQ,CACPC,MAAO,GACPC,OAAQ,GACR,oBAAqB,QACrB,cAAe,GACfC,UAAW,CACVF,MAAO,GACPC,OAAQ,GACR,oBAAqB,QACrB,cAAe,EAChB,CACD,EACAE,OAAQ,CAEPC,SAAU,CACT,CACCC,KAAM,WACNC,MAAO,eACPC,UAAW,sBACXC,SAAU,mCACVC,MAAO,IACPC,OAAQ,GACRC,UAAW,oBACXC,cAAe,mBACfC,QAAS,iCACTC,SAAU,CACT,CACCR,MAAO,2BACPS,SAAU,EACX,EACA,CACCT,MAAO,mBACPS,SAAU,EACX,EACA,CACCT,MAAO,mBACPS,SAAU,EACX,EACA,CACCT,MAAO,6BACPS,SAAU,EACX,EACA,CACCT,MAAO,mCACPS,SAAU,EACX,EACA,EAEF,CACCV,KAAM,WACNC,MAAO,cACPC,UAAW,sBACXC,SAAU,qCACVC,MAAO,IACPC,OAAQ,GACRC,UAAW,mBACXC,cAAe,mBACfC,QAAS,iCACTC,SAAU,CACT,CACCR,MAAO,6BACPS,SAAU,EACX,EACA,CACCT,MAAO,mBACPS,SAAU,EACX,EACA,CACCT,MAAO,mBACPS,SAAU,EACX,EACA,CACCT,MAAO,6BACPS,SAAU,EACX,EACA,CACCT,MAAO,mCACPS,SAAU,EACX,EACA,EAEF,EAEFC,OAAQ,CAEPC,MAAO,QAGPC,KAAMC,SAAAA,KAAAA,CAAgB,OAAU,EAEjCC,OAAQ,CAEPC,UAAW,6CAEXC,aAAc,4BAEdC,iBAAkB,4BAClBC,SAAU,CACTC,SAAU,2BACX,CACD,CACD,gDC7GA,IAAA9B,EAFe,GAAI+B,UAAAA,YAAYA,qECuF/B,IAAAC,EAvFwB,GAEhB,GAAAC,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,2CACX,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,iBACX,GAAAH,EAAAI,GAAA,EAACC,KAAAA,CAAGF,UAAU,iDAAwC,YAEtD,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,kCACX,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sBACX,GAAAH,EAAAC,IAAA,EAACK,IAAAA,CAAEH,UAAU,kCACT,GAAAH,EAAAI,GAAA,EAACG,OAAAA,CAAKJ,UAAU,uBAAc,qBAC7BK,EAAMxE,EAAE,IAEb,GAAAgE,EAAAC,IAAA,EAACK,IAAAA,CAAEH,UAAU,kCACT,GAAAH,EAAAI,GAAA,EAACG,OAAAA,CAAKJ,UAAU,uBAAc,gBAC7BK,EAAMC,SAAS,YAOhC,GAAAT,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,wCACX,GAAAH,EAAAC,IAAA,EAACC,MAAAA,WACG,GAAAF,EAAAI,GAAA,EAACM,KAAAA,CAAGP,UAAU,sCAA6B,UAC3C,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,4CACX,GAAAH,EAAAI,GAAA,EAACE,IAAAA,CAAEH,UAAU,uBAAc,qBAC3B,GAAAH,EAAAI,GAAA,EAACE,IAAAA,UAAE,mBACH,GAAAN,EAAAI,GAAA,EAACE,IAAAA,UAAE,yBACH,GAAAN,EAAAI,GAAA,EAACE,IAAAA,UAAE,cACH,GAAAN,EAAAI,GAAA,EAACE,IAAAA,UAAE,mBACH,GAAAN,EAAAI,GAAA,EAACE,IAAAA,UAAE,kBAIX,GAAAN,EAAAC,IAAA,EAACC,MAAAA,WACG,GAAAF,EAAAI,GAAA,EAACM,KAAAA,CAAGP,UAAU,sCAA6B,QAC3C,GAAAH,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,0FACX,GAAAH,EAAAI,GAAA,EAACG,OAAAA,UACIC,EAAMG,IAAI,WAO3B,GAAAX,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,gBACX,GAAAH,EAAAC,IAAA,EAACW,QAAAA,CAAMT,UAAU,mBACb,GAAAH,EAAAI,GAAA,EAACS,QAAAA,UACD,GAAAb,EAAAC,IAAA,EAACa,KAAAA,CAAGX,UAAU,qCACV,GAAAH,EAAAI,GAAA,EAACW,KAAAA,CAAGZ,UAAU,sDAA6C,gBAC3D,GAAAH,EAAAI,GAAA,EAACW,KAAAA,CAAGZ,UAAU,wDAA+C,QAC7D,GAAAH,EAAAI,GAAA,EAACW,KAAAA,CAAGZ,UAAU,uDAA8C,eAC5D,GAAAH,EAAAI,GAAA,EAACW,KAAAA,CAAGZ,UAAU,uDAA8C,gBAGhE,GAAAH,EAAAI,GAAA,EAACY,QAAAA,UACD,GAAAhB,EAAAC,IAAA,EAACa,KAAAA,CAAGX,UAAU,qCACV,GAAAH,EAAAI,GAAA,EAACa,KAAAA,CAAGd,UAAU,sCAA6B,4BAC3C,GAAAH,EAAAI,GAAA,EAACa,KAAAA,CAAGd,UAAU,kDAAyC,MACvD,GAAAH,EAAAI,GAAA,EAACa,KAAAA,CAAGd,UAAU,iDAAyCK,EAAM3B,KAAK,GAClE,GAAAmB,EAAAI,GAAA,EAACa,KAAAA,CAAGd,UAAU,iDAAyCK,EAAM3B,KAAK,aAO9E,GAAAmB,EAAAI,GAAA,EAACF,MAAAA,CAAIC,UAAU,yCACX,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,sBACX,GAAAH,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,uDACX,GAAAH,EAAAI,GAAA,EAACG,OAAAA,UAAK,cACN,GAAAP,EAAAI,GAAA,EAACG,OAAAA,CAAAA,MAEL,GAAAP,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,mEACX,GAAAH,EAAAI,GAAA,EAACG,OAAAA,UAAK,WACN,GAAAP,EAAAI,GAAA,EAACG,OAAAA,CAAAA,MAEL,GAAAP,EAAAC,IAAA,EAACC,MAAAA,CAAIC,UAAU,oEACX,GAAAH,EAAAI,GAAA,EAACG,OAAAA,UAAK,uBACN,GAAAP,EAAAI,GAAA,EAACG,OAAAA,CAAAA,gGC1EzB,IAAMW,EAAS,CACbC,KAAM,CACJC,gBAAiB,UACjBC,WAAY,mBACd,EACAC,UAAW,CACTC,OAAQ,SACRC,QAAS,cACTC,MAAO,OACT,EACAC,QAAS,CACPN,gBAAiB,UACjBO,aAAc,MACdH,QAAS,MACX,EACAnB,GAAI,CACFuB,MAAO,UACPC,SAAU,OACVC,WAAY,OACZC,UAAW,SACXR,OAAQ,UACV,EACAS,KAAM,CACJJ,MAAO,UACPC,SAAU,OACVI,WAAY,OACZF,UAAW,QACb,EACAG,OAAQ,CACNd,gBAAiB,UACjBO,aAAc,MACdC,MAAO,UACPO,QAAS,eACTN,SAAU,OACVC,WAAY,OACZN,QAAS,YACTY,eAAgB,OAChBL,UAAW,SACXM,UAAW,MACb,CACF,EAgCAC,EA9BiE,CAAC,CAChEjH,MAAAA,CAAK,CACN,GACC,GAAA2E,EAAAC,IAAA,EAACsC,EAAAA,CAAIA,CAAAA,WACH,GAAAvC,EAAAI,GAAA,EAACoC,EAAAA,CAAIA,CAAAA,CAAAA,GACL,GAAAxC,EAAAI,GAAA,EAACqC,EAAAA,CAAOA,CAAAA,UAAC,8BACT,GAAAzC,EAAAI,GAAA,EAACsC,EAAAA,CAAIA,CAAAA,CAACC,MAAOzB,EAAOC,IAAI,UACtB,GAAAnB,EAAAI,GAAA,EAACwC,EAAAA,CAASA,CAAAA,CAACD,MAAOzB,EAAOI,SAAS,UAChC,GAAAtB,EAAAC,IAAA,EAAC4C,EAAAA,CAAOA,CAAAA,CAACF,MAAOzB,EAAOQ,OAAO,WAC5B,GAAA1B,EAAAI,GAAA,EAAC0C,EAAAA,CAAIA,CAAAA,CAACH,MAAOzB,EAAOb,EAAE,UAAE,oBACxB,GAAAL,EAAAC,IAAA,EAAC6C,EAAAA,CAAIA,CAAAA,CAACH,MAAOzB,EAAOc,IAAI,WAAE,uCACkB3G,EAAM,OAElD,GAAA2E,EAAAI,GAAA,EAAC0C,EAAAA,CAAIA,CAAAA,CAACH,MAAOzB,EAAOc,IAAI,UAAE,wFAG1B,GAAAhC,EAAAI,GAAA,EAACyC,EAAAA,CAAOA,CAAAA,CAACF,MAAO,CAAEZ,UAAW,QAAS,WACpC,GAAA/B,EAAAI,GAAA,EAAC2C,EAAAA,CAAIA,CAAAA,CACHC,KAAK,kCACLL,MAAOzB,EAAOgB,MAAM,UACrB,6DC/Db,OAAMe,EAGL,MAAanH,mBAAmBoH,CAAc,CAAE,CAC/C,GAAM,CAAEvC,KAAAA,CAAI,CAAEnF,MAAAA,CAAK,CAAE,CAAG,MAAM,IAAI,CAACgE,MAAM,CAAC2D,MAAM,CAACC,IAAI,CAAC,CACrDC,KAAMC,EAAAA,CAAMA,CAAC9D,MAAM,CAACC,SAAS,CAC7B8D,GAAI,CAACL,EAAO,CACZM,QAASF,EAAAA,CAAMA,CAAC9D,MAAM,CAACG,gBAAgB,CACvC8D,QAASH,EAAAA,CAAMA,CAAC9D,MAAM,CAACI,QAAQ,CAACC,QAAQ,CACxC6D,MAAOC,EAAiB,CAAEtI,MAAO6H,CAAO,EACzC,GAEA,GAAI1H,EACH,MAAMA,EAGP,OAAOmF,CACR,CAEA,MAAaiD,YAAYV,CAAc,CAAEW,CAAe,CAAE,CACzD,GAAM,CAAElD,KAAAA,CAAI,CAAEnF,MAAAA,CAAK,CAAE,CAAG,MAAM,IAAI,CAACgE,MAAM,CAAC2D,MAAM,CAACC,IAAI,CAAC,CACrDC,KAAMC,EAAAA,CAAMA,CAAC9D,MAAM,CAACC,SAAS,CAC7B8D,GAAI,CAACL,EAAO,CACZM,QAASF,EAAAA,CAAMA,CAAC9D,MAAM,CAACG,gBAAgB,CACvC8D,QAAS,YAAcI,EAAW7H,EAAE,CACpC0H,MAAOI,EAAgBD,EACxB,GAEA,GAAIrI,EACH,MAAMA,EAGP,OAAOmF,CACR,CAEA,MAAaoD,mBAAmB1I,CAAa,CAAE,CAC9C,IAAM2I,EAAW,MAAM,IAAI,CAACC,cAAc,GAC1C,OAAO,IAAI,CAACzE,MAAM,CAAC0E,QAAQ,CAACC,MAAM,CAAC,CAClC9I,MAAAA,EACA+I,aAAc,GACdC,WAAYL,EAASM,SAAS,EAEhC,CAEA,MAAcL,gBAAiB,CAC9B,IAAMD,EAAW,MAAMO,EAAAA,CAAMA,CAACC,SAAS,CAACC,SAAS,GAEjD,GAAIT,EACH,OAAOA,EAMR,GAAM,CACLrD,KAAM,CAAE3E,GAAAA,CAAE,CAAE0I,KAAAA,CAAI,CAAE,CAClB,CALsB,MAAM,IAAI,CAAClF,MAAM,CAACgF,SAAS,CAACL,MAAM,CAAC,CACzDO,KAAM,cACP,GAIA,OAAOH,EAAAA,CAAMA,CAACC,SAAS,CAACL,MAAM,CAAC,CAC9BxD,KAAM,CACL2D,UAAWtI,EACX0I,KAAAA,CACD,CACD,EACD,oBA9DQlF,MAAAA,CAAS,IAAImF,EAAAA,CAAMA,CAACC,QAAQC,GAAG,CAACC,cAAc,EA+DvD,CAEO,IAAMjJ,EAAgB,IAAIoH", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/./src/app/api/test-email/route.ts", "webpack://_N_E/./src/app/api/test-email/route.ts?9622", "webpack://_N_E/?75b7", "webpack://_N_E/./src/config.ts", "webpack://_N_E/./src/libs/prisma.ts", "webpack://_N_E/./src/components/email-templates/Invoice.tsx", "webpack://_N_E/./src/components/email-templates/ThanksYouTemplate.tsx", "webpack://_N_E/./src/libs/resend.ts"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "import { resendService } from '@/libs/resend';\nimport { NextRequest, NextResponse } from 'next/server';\n\nexport const dynamic = 'force-dynamic';\n// Make this route public and not require authentication\nexport const runtime = 'edge';\n\nexport async function POST(req: NextRequest) {\n  try {\n    const { email } = await req.json();\n\n    if (!email) {\n      return NextResponse.json({ error: 'Email is required' }, { status: 400 });\n    }\n\n    console.log('Sending test email to:', email);\n\n    // Send a test email\n    const result = await resendService.sendThanksYouEmail(email);\n\n    console.log('Email sent successfully:', result);\n\n    return NextResponse.json({\n      message: 'Test email sent successfully',\n      id: result.id\n    });\n  } catch (error) {\n    console.error('Error sending test email:', error);\n    return NextResponse.json({\n      error: 'Failed to send test email',\n      details: error.message\n    }, { status: 500 });\n  }\n}\n", "import { AppRouteRouteModule } from \"next/dist/server/future/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/future/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Projects\\\\PersonalPortal\\\\src\\\\app\\\\api\\\\test-email\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/test-email/route\",\n        pathname: \"/api/test-email\",\n        filename: \"route\",\n        bundlePath: \"app/api/test-email/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\PersonalPortal\\\\src\\\\app\\\\api\\\\test-email\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/test-email/route\";\nfunction patchFetch() {\n    return _patchFetch({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\nexport { routeModule, requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, originalPathname, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "import { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Ftest-email%2Froute&page=%2Fapi%2Ftest-email%2Froute&pagePath=private-next-app-dir%2Fapi%2Ftest-email%2Froute.ts&appDir=C%3A%5CProjects%5CPersonalPortal%5Csrc%5Capp&appPaths=%2Fapi%2Ftest-email%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/test-email/route.ts?__next_edge_ssr_entry__\";\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule);\n\n//# sourceMappingURL=edge-app-route.js.map", "import { ConfigProps } from '@/types'\r\nimport themes from 'daisyui/src/theming/themes'\r\n\r\nconst config: ConfigProps = {\r\n\t// REQUIRED\r\n\tappName: 'Javian Picardo',\r\n\t// REQUIRED: a short description of your app for SEO tags (can be overwritten)\r\n\tappDescription:\r\n\t\t'Portal for the services provided by <PERSON><PERSON><PERSON>.',\r\n\t// REQUIRED (no https://, not trialing slash at the end, just the naked domain)\r\n\tdomainName: 'www.javianpicardo.com',\r\n\t// Add required robots configuration\r\n\trobots: {\r\n\t\tindex: true,\r\n\t\tfollow: true,\r\n\t\t'max-image-preview': 'large',\r\n\t\t'max-snippet': -1,\r\n\t\tgoogleBot: {\r\n\t\t\tindex: true,\r\n\t\t\tfollow: true,\r\n\t\t\t'max-image-preview': 'large',\r\n\t\t\t'max-snippet': -1,\r\n\t\t},\r\n\t},\r\n\tstripe: {\r\n\t\t// Create multiple products in your Stripe dashboard, then add them here. You can add as many plans as you want, just make sure to add the priceId\r\n\t\tproducts: [\r\n\t\t\t{\r\n\t\t\t\ttype: 'one-time',\r\n\t\t\t\ttitle: 'Quick Shoots',\r\n\t\t\t\tproductId: 'prod_S0c6NHMe0vINXA',\r\n\t\t\t\tsubtitle: 'Professional photography session',\r\n\t\t\t\tprice: 300,\r\n\t\t\t\tisBest: false,\r\n\t\t\t\tlinkTitle: 'Book Quick Shoots',\r\n\t\t\t\tfeaturesTitle: 'Package Features',\r\n\t\t\t\tpriceId: 'price_1R6aslP82YH9JfOlJZcweC4d',\r\n\t\t\t\tfeatures: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: 'One hour of footage time',\r\n\t\t\t\t\t\tdisabled: false,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '50 edited photos',\r\n\t\t\t\t\t\tdisabled: false,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: 'Help with posing',\r\n\t\t\t\t\t\tdisabled: false,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: 'Location list to pick from',\r\n\t\t\t\t\t\tdisabled: false,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '7 business days turn around time',\r\n\t\t\t\t\t\tdisabled: false,\r\n\t\t\t\t\t},\r\n\t\t\t\t],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\ttype: 'one-time',\r\n\t\t\t\ttitle: 'Mini Shoots',\r\n\t\t\t\tproductId: 'prod_S0c9zDLLtK9rnS',\r\n\t\t\t\tsubtitle: 'Perfect for professional portraits',\r\n\t\t\t\tprice: 150,\r\n\t\t\t\tisBest: true,\r\n\t\t\t\tlinkTitle: 'Book Mini Shoots',\r\n\t\t\t\tfeaturesTitle: 'Package Features',\r\n\t\t\t\tpriceId: 'price_1R6avXP82YH9JfOlIGpAfwjU',\r\n\t\t\t\tfeatures: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '30 minutes of footage time',\r\n\t\t\t\t\t\tdisabled: false,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '25 edited photos',\r\n\t\t\t\t\t\tdisabled: false,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: 'Help with posing',\r\n\t\t\t\t\t\tdisabled: false,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: 'Location list to pick from',\r\n\t\t\t\t\t\tdisabled: false,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '7 business days turn around time',\r\n\t\t\t\t\t\tdisabled: false,\r\n\t\t\t\t\t},\r\n\t\t\t\t],\r\n\t\t\t},\r\n\t\t],\r\n\t},\r\n\tcolors: {\r\n\t\t// REQUIRED — The DaisyUI theme to use (added to the main layout.js). Leave blank for default (light & dark mode). If you any other theme than light/dark, you need to add it in config.tailwind.js in daisyui.themes.\r\n\t\ttheme: 'light',\r\n\t\t// REQUIRED — This color will be reflected on the whole app outside of the document (loading bar, Chrome tabs, etc..). By default it takes the primary color from your DaisyUI theme (make sure to update your the theme name after \"data-theme=\")\r\n\t\t// OR you can just do this to use a custom color: main: \"#f37055\". HEX only.\r\n\t\tmain: themes['light']['primary'],\r\n\t},\r\n\tresend: {\r\n\t\t// REQUIRED — Email 'From' field to be used when sending other emails, like abandoned carts, updates etc..\r\n\t\tfromAdmin: `Javian Picardo <<EMAIL>>`,\r\n\t\t// Email shown to customer if need support. Leave empty if not needed => if empty, set up Crisp above, otherwise you won't be able to offer customer support.\"\r\n\t\tsupportEmail: '<EMAIL>',\r\n\t\t// When someone replies to supportEmail sent by the app, forward it to the email below (otherwise it's lost). If you set supportEmail to empty, this will be ignored.\r\n\t\tforwardRepliesTo: '<EMAIL>',\r\n\t\tsubjects: {\r\n\t\t\tthankYou: 'Welcome to Javian Picardo',\r\n\t\t},\r\n\t},\r\n}\r\n\r\nexport default config\r\n", "import { PrismaClient } from \"@prisma/client\";\r\n\r\nconst prisma = new PrismaClient();\r\n\r\nexport default prisma;\r\n", "import React from 'react';\r\n\r\nconst InvoiceTemplate = (props: any) => {\r\n    return (\r\n        <div className=\"max-w-3xl mx-auto p-8 bg-white\">\r\n            <div className=\"mb-8\">\r\n                <h1 className=\"text-3xl font-bold text-gray-900 mb-6\">Invoice</h1>\r\n\r\n                <div className=\"grid grid-cols-2 gap-4\">\r\n                    <div className=\"space-y-2\">\r\n                        <p className=\"text-sm text-gray-600\">\r\n                            <span className=\"font-medium\">Invoice number: </span>\r\n                            {props.id}\r\n                        </p>\r\n                        <p className=\"text-sm text-gray-600\">\r\n                            <span className=\"font-medium\">Date paid: </span>\r\n                            {props.paid_date}\r\n                        </p>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Address Section */}\r\n            <div className=\"grid grid-cols-2 gap-8 mb-8\">\r\n                <div>\r\n                    <h2 className=\"text-lg font-semibold mb-3\">From:</h2>\r\n                    <div className=\"text-sm text-gray-600 space-y-1\">\r\n                        <p className=\"font-medium\">DB2 Software Ltd</p>\r\n                        <p>38 Fawkner Way</p>\r\n                        <p>Stanford In The Vale</p>\r\n                        <p>Faringdon</p>\r\n                        <p>United Kingdom</p>\r\n                        <p>SN7 8FF</p>\r\n                    </div>\r\n                </div>\r\n\r\n                <div>\r\n                    <h2 className=\"text-lg font-semibold mb-3\">To:</h2>\r\n                    <div className=\"text-sm text-gray-600 h-24 border border-dashed border-gray-300 rounded-md p-3\">\r\n                        <span>\r\n                            {props.data}\r\n                        </span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Invoice Table */}\r\n            <div className=\"mb-8\">\r\n                <table className=\"w-full\">\r\n                    <thead>\r\n                    <tr className=\"border-b border-gray-200\">\r\n                        <th className=\"py-3 text-left font-semibold text-gray-700\">Description</th>\r\n                        <th className=\"py-3 text-center font-semibold text-gray-700\">Qty</th>\r\n                        <th className=\"py-3 text-right font-semibold text-gray-700\">Unit price</th>\r\n                        <th className=\"py-3 text-right font-semibold text-gray-700\">Amount</th>\r\n                    </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                    <tr className=\"border-b border-gray-200\">\r\n                        <td className=\"py-4 text-sm text-gray-600\">Nextjs code boilerplate</td>\r\n                        <td className=\"py-4 text-center text-sm text-gray-600\">1</td>\r\n                        <td className=\"py-4 text-right text-sm text-gray-600\">{props.price}</td>\r\n                        <td className=\"py-4 text-right text-sm text-gray-600\">{props.price}</td>\r\n                    </tr>\r\n                    </tbody>\r\n                </table>\r\n            </div>\r\n\r\n            {/* Totals */}\r\n            <div className=\"border-t border-gray-200 pt-4\">\r\n                <div className=\"space-y-2\">\r\n                    <div className=\"flex justify-between text-sm text-gray-600\">\r\n                        <span>Subtotal:</span>\r\n                        <span></span>\r\n                    </div>\r\n                    <div className=\"flex justify-between text-sm font-medium text-gray-800\">\r\n                        <span>Total:</span>\r\n                        <span></span>\r\n                    </div>\r\n                    <div className=\"flex justify-between text-sm font-medium text-green-600\">\r\n                        <span>Total amount paid:</span>\r\n                        <span></span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default InvoiceTemplate;", "import React from 'react';\r\nimport { Html, Head, Body, Container, Section, Text, Link, Preview } from '@react-email/components';\r\n\r\ninterface EmailTemplateProps {\r\n  email: string;\r\n}\r\n\r\nconst styles = {\r\n  body: {\r\n    backgroundColor: '#f3f4f6',\r\n    fontFamily: 'Arial, sans-serif',\r\n  },\r\n  container: {\r\n    margin: '0 auto',\r\n    padding: '20px 0 48px',\r\n    width: '580px',\r\n  },\r\n  section: {\r\n    backgroundColor: '#ffffff',\r\n    borderRadius: '5px',\r\n    padding: '40px',\r\n  },\r\n  h1: {\r\n    color: '#3b82f6',\r\n    fontSize: '32px',\r\n    fontWeight: 'bold',\r\n    textAlign: 'center' as const,\r\n    margin: '0 0 20px',\r\n  },\r\n  text: {\r\n    color: '#374151',\r\n    fontSize: '16px',\r\n    lineHeight: '24px',\r\n    textAlign: 'center' as const,\r\n  },\r\n  button: {\r\n    backgroundColor: '#3b82f6',\r\n    borderRadius: '5px',\r\n    color: '#ffffff',\r\n    display: 'inline-block',\r\n    fontSize: '16px',\r\n    fontWeight: 'bold',\r\n    padding: '12px 24px',\r\n    textDecoration: 'none',\r\n    textAlign: 'center' as const,\r\n    marginTop: '32px',\r\n  },\r\n};\r\n\r\nconst ThankYouTemplate: React.FC<Readonly<EmailTemplateProps>> = ({\r\n  email,\r\n}) => (\r\n  <Html>\r\n    <Head />\r\n    <Preview>Welcome to our community!</Preview>\r\n    <Body style={styles.body}>\r\n      <Container style={styles.container}>\r\n        <Section style={styles.section}>\r\n          <Text style={styles.h1}>Welcome aboard!</Text>\r\n          <Text style={styles.text}>\r\n            We&apos;re thrilled to have you join us, {email}!\r\n          </Text>\r\n          <Text style={styles.text}>\r\n            Get ready for an amazing journey. We can&apos;t wait to see what you&apos;ll achieve with us.\r\n          </Text>\r\n          <Section style={{ textAlign: 'center' }}>\r\n            <Link\r\n              href=\"http://localhost:3000/dashboard\"\r\n              style={styles.button}\r\n            >\r\n              Get Started\r\n            </Link>\r\n          </Section>\r\n        </Section>\r\n      </Container>\r\n    </Body>\r\n  </Html>\r\n);\r\n\r\nexport default ThankYouTemplate;", "import InvoiceTemplate from '@/components/email-templates/Invoice'\r\nimport ThankYouTemplate from '@/components/email-templates/ThanksYouTemplate'\r\nimport config from '@/config'\r\nimport prisma from '@/libs/prisma'\r\nimport { Resend } from 'resend'\r\n\r\nclass ResendService {\r\n\tprivate resend = new Resend(process.env.RESEND_API_KEY)\r\n\r\n\tpublic async sendThanksYouEmail(toMail: string) {\r\n\t\tconst { data, error } = await this.resend.emails.send({\r\n\t\t\tfrom: config.resend.fromAdmin,\r\n\t\t\tto: [toMail],\r\n\t\t\treplyTo: config.resend.forwardRepliesTo,\r\n\t\t\tsubject: config.resend.subjects.thankYou,\r\n\t\t\treact: ThankYouTemplate({ email: toMail }),\r\n\t\t})\r\n\r\n\t\tif (error) {\r\n\t\t\tthrow error\r\n\t\t}\r\n\r\n\t\treturn data\r\n\t}\r\n\r\n\tpublic async sendInvoice(toMail: string, renderData: any) {\r\n\t\tconst { data, error } = await this.resend.emails.send({\r\n\t\t\tfrom: config.resend.fromAdmin,\r\n\t\t\tto: [toMail],\r\n\t\t\treplyTo: config.resend.forwardRepliesTo,\r\n\t\t\tsubject: 'Invoice: ' + renderData.id,\r\n\t\t\treact: InvoiceTemplate(renderData),\r\n\t\t})\r\n\r\n\t\tif (error) {\r\n\t\t\tthrow error\r\n\t\t}\r\n\r\n\t\treturn data\r\n\t}\r\n\r\n\tpublic async addNewEmailAddress(email: string) {\r\n\t\tconst audience = await this.upsertAudience()\r\n\t\treturn this.resend.contacts.create({\r\n\t\t\temail,\r\n\t\t\tunsubscribed: false,\r\n\t\t\taudienceId: audience.resend_id,\r\n\t\t})\r\n\t}\r\n\r\n\tprivate async upsertAudience() {\r\n\t\tconst audience = await prisma.audiences.findFirst()\r\n\r\n\t\tif (audience) {\r\n\t\t\treturn audience\r\n\t\t}\r\n\r\n\t\tconst resendAudience = await this.resend.audiences.create({\r\n\t\t\tname: 'Waiting List',\r\n\t\t})\r\n\t\tconst {\r\n\t\t\tdata: { id, name },\r\n\t\t} = resendAudience\r\n\t\treturn prisma.audiences.create({\r\n\t\t\tdata: {\r\n\t\t\t\tresend_id: id,\r\n\t\t\t\tname,\r\n\t\t\t},\r\n\t\t})\r\n\t}\r\n}\r\n\r\nexport const resendService = new ResendService()\r\n"], "names": ["module", "exports", "require", "dynamic", "runtime", "POST", "req", "email", "json", "NextResponse", "error", "status", "console", "log", "result", "resendService", "sendThanksYouEmail", "message", "id", "details", "routeModule", "module_compiled", "AppRouteRouteModule", "definition", "kind", "route_kind", "x", "APP_ROUTE", "page", "pathname", "filename", "bundlePath", "resolvedPagePath", "nextConfigOutput", "userland", "route_namespaceObject", "requestAsyncStorage", "staticGenerationAsyncStorage", "serverHooks", "originalPathname", "patchFetch", "patch_fetch", "XH", "ComponentMod", "route_next_edge_ssr_entry_namespaceObject", "next_edge_app_route_loaderabsolutePagePath_private_next_app_dir_2Fapi_2Ftest_email_2Froute_ts_page_2Fapi_2Ftest_email_2Froute_appDirLoader_bmV4dC1hcHAtbG9hZGVyP25hbWU9YXBwJTJGYXBpJTJGdGVzdC1lbWFpbCUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGdGVzdC1lbWFpbCUyRnJvdXRlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGdGVzdC1lbWFpbCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDUHJvamVjdHMlNUNQZXJzb25hbFBvcnRhbCU1Q3NyYyU1Q2FwcCZhcHBQYXRocz0lMkZhcGklMkZ0ZXN0LWVtYWlsJTJGcm91dGUmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0Qh_nextConfigOutput_preferredRegion_middlewareConfig_e30_3D_", "edge_route_module_wrapper", "a", "wrap", "__WEBPACK_DEFAULT_EXPORT__", "appName", "appDescription", "domainName", "robots", "index", "follow", "googleBot", "stripe", "products", "type", "title", "productId", "subtitle", "price", "isBest", "linkTitle", "featuresTitle", "priceId", "features", "disabled", "colors", "theme", "main", "themes", "resend", "fromAdmin", "supportEmail", "forwardRepliesTo", "subjects", "thankYou", "PrismaClient", "Invoice", "jsx_runtime", "jsxs", "div", "className", "jsx", "h1", "p", "span", "props", "paid_date", "h2", "data", "table", "thead", "tr", "th", "tbody", "td", "styles", "body", "backgroundColor", "fontFamily", "container", "margin", "padding", "width", "section", "borderRadius", "color", "fontSize", "fontWeight", "textAlign", "text", "lineHeight", "button", "display", "textDecoration", "marginTop", "ThanksYouTemplate", "Html", "Head", "Preview", "Body", "style", "Container", "Section", "Text", "Link", "href", "ResendService", "toMail", "emails", "send", "from", "config", "to", "replyTo", "subject", "react", "ThankYouTemplate", "sendInvoice", "renderData", "InvoiceTemplate", "addNewEmailAddress", "audience", "upsertAudience", "contacts", "create", "unsubscribed", "audienceId", "resend_id", "prisma", "audiences", "<PERSON><PERSON><PERSON><PERSON>", "name", "Resend", "process", "env", "RESEND_API_KEY"], "sourceRoot": ""}