globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/assets/images/auth.svg":{"*":{"id":"(ssr)/./src/assets/images/auth.svg","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/assets/images/blog.svg":{"*":{"id":"(ssr)/./src/assets/images/blog.svg","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/assets/images/database.svg":{"*":{"id":"(ssr)/./src/assets/images/database.svg","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/assets/images/email.svg":{"*":{"id":"(ssr)/./src/assets/images/email.svg","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/assets/images/landing.svg":{"*":{"id":"(ssr)/./src/assets/images/landing.svg","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/assets/images/licence.svg":{"*":{"id":"(ssr)/./src/assets/images/licence.svg","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/assets/images/logo.svg":{"*":{"id":"(ssr)/./src/assets/images/logo.svg","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/assets/images/payment.svg":{"*":{"id":"(ssr)/./src/assets/images/payment.svg","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/assets/images/pricing-bg1.svg":{"*":{"id":"(ssr)/./src/assets/images/pricing-bg1.svg","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/assets/images/pricing-bg2.svg":{"*":{"id":"(ssr)/./src/assets/images/pricing-bg2.svg","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/assets/images/seo.svg":{"*":{"id":"(ssr)/./src/assets/images/seo.svg","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/assets/images/stop-coding-bg1.svg":{"*":{"id":"(ssr)/./src/assets/images/stop-coding-bg1.svg","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/assets/images/stop-coding-bg2.svg":{"*":{"id":"(ssr)/./src/assets/images/stop-coding-bg2.svg","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/assets/images/ui.svg":{"*":{"id":"(ssr)/./src/assets/images/ui.svg","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/assets/images/zero-risk-bg1.svg":{"*":{"id":"(ssr)/./src/assets/images/zero-risk-bg1.svg","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/assets/images/zero-risk-bg2.svg":{"*":{"id":"(ssr)/./src/assets/images/zero-risk-bg2.svg","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/assets/images/zero-risk1.svg":{"*":{"id":"(ssr)/./src/assets/images/zero-risk1.svg","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/assets/images/zero-risk2.svg":{"*":{"id":"(ssr)/./src/assets/images/zero-risk2.svg","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/AboutUs.tsx":{"*":{"id":"(ssr)/./src/components/AboutUs.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/FAQ.tsx":{"*":{"id":"(ssr)/./src/components/FAQ.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Footer.tsx":{"*":{"id":"(ssr)/./src/components/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Header.tsx":{"*":{"id":"(ssr)/./src/components/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Hero.tsx":{"*":{"id":"(ssr)/./src/components/Hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/lazy-load-iframe.tsx":{"*":{"id":"(ssr)/./src/components/lazy-load-iframe.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/login-payment.tsx":{"*":{"id":"(ssr)/./src/components/login-payment.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Testimonials.jsx":{"*":{"id":"(ssr)/./src/components/Testimonials.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@vercel/analytics/dist/react/index.mjs":{"*":{"id":"(ssr)/./node_modules/@vercel/analytics/dist/react/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@vercel/speed-insights/dist/next/index.mjs":{"*":{"id":"(ssr)/./node_modules/@vercel/speed-insights/dist/next/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers.tsx":{"*":{"id":"(ssr)/./src/components/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/error.tsx":{"*":{"id":"(ssr)/./src/app/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\assets\\images\\auth.svg":{"id":"(app-pages-browser)/./src/assets/images/auth.svg","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\assets\\images\\blog.svg":{"id":"(app-pages-browser)/./src/assets/images/blog.svg","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\assets\\images\\database.svg":{"id":"(app-pages-browser)/./src/assets/images/database.svg","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\assets\\images\\email.svg":{"id":"(app-pages-browser)/./src/assets/images/email.svg","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\assets\\images\\landing.svg":{"id":"(app-pages-browser)/./src/assets/images/landing.svg","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\assets\\images\\licence.svg":{"id":"(app-pages-browser)/./src/assets/images/licence.svg","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\assets\\images\\logo.svg":{"id":"(app-pages-browser)/./src/assets/images/logo.svg","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\assets\\images\\payment.svg":{"id":"(app-pages-browser)/./src/assets/images/payment.svg","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\assets\\images\\pricing-bg1.svg":{"id":"(app-pages-browser)/./src/assets/images/pricing-bg1.svg","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\assets\\images\\pricing-bg2.svg":{"id":"(app-pages-browser)/./src/assets/images/pricing-bg2.svg","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\assets\\images\\seo.svg":{"id":"(app-pages-browser)/./src/assets/images/seo.svg","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\assets\\images\\stop-coding-bg1.svg":{"id":"(app-pages-browser)/./src/assets/images/stop-coding-bg1.svg","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\assets\\images\\stop-coding-bg2.svg":{"id":"(app-pages-browser)/./src/assets/images/stop-coding-bg2.svg","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\assets\\images\\ui.svg":{"id":"(app-pages-browser)/./src/assets/images/ui.svg","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\assets\\images\\zero-risk-bg1.svg":{"id":"(app-pages-browser)/./src/assets/images/zero-risk-bg1.svg","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\assets\\images\\zero-risk-bg2.svg":{"id":"(app-pages-browser)/./src/assets/images/zero-risk-bg2.svg","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\assets\\images\\zero-risk1.svg":{"id":"(app-pages-browser)/./src/assets/images/zero-risk1.svg","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\assets\\images\\zero-risk2.svg":{"id":"(app-pages-browser)/./src/assets/images/zero-risk2.svg","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\components\\AboutUs.tsx":{"id":"(app-pages-browser)/./src/components/AboutUs.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\components\\FAQ.tsx":{"id":"(app-pages-browser)/./src/components/FAQ.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\components\\Footer.tsx":{"id":"(app-pages-browser)/./src/components/Footer.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\components\\Header.tsx":{"id":"(app-pages-browser)/./src/components/Header.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\components\\Hero.tsx":{"id":"(app-pages-browser)/./src/components/Hero.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\components\\lazy-load-iframe.tsx":{"id":"(app-pages-browser)/./src/components/lazy-load-iframe.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\components\\login-payment.tsx":{"id":"(app-pages-browser)/./src/components/login-payment.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\components\\Testimonials.jsx":{"id":"(app-pages-browser)/./src/components/Testimonials.jsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\node_modules\\@clerk\\nextjs\\dist\\esm\\app-router\\client\\ClerkProvider.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\node_modules\\@vercel\\analytics\\dist\\react\\index.mjs":{"id":"(app-pages-browser)/./node_modules/@vercel/analytics/dist/react/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\node_modules\\@vercel\\speed-insights\\dist\\next\\index.mjs":{"id":"(app-pages-browser)/./node_modules/@vercel/speed-insights/dist/next/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"font\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"font\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\assets\\styles\\globals.scss":{"id":"(app-pages-browser)/./src/assets/styles/globals.scss","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\components\\providers.tsx":{"id":"(app-pages-browser)/./src/components/providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\app\\error.tsx":{"id":"(app-pages-browser)/./src/app/error.tsx","name":"*","chunks":["app/error","static/chunks/app/error.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\node_modules\\next\\dist\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\node_modules\\next\\dist\\esm\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false}},"entryCSSFiles":{"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\":[],"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\app\\page":[],"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\app\\layout":["static/css/app/layout.css"],"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\app\\error":[],"C:\\Projects\\MicroSaasWebApiRoot\\MicroSaasWebApiUI\\src\\app\\not-found":[]}}