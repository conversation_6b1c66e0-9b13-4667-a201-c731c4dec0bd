import { currentUser } from '@clerk/nextjs/server'
import axios from 'axios'
import { NextResponse } from 'next/server'

// Mark this route as dynamic
export const dynamic = 'force-dynamic'
export const runtime = 'edge'

export async function GET() {
	try {
		const user = await currentUser()

		if (!user) {
			return NextResponse.json(
				{ error: 'Unauthorized' },
				{ status: 401 }
			)
		}

		const scenarios = await axios.get(
			`${process.env.MAKE_API_URL}/scenarios?teamId=${process.env.MAKE_TEAM_ID}`,
			{ headers: { Authorization: `Token ${process.env.MAKE_API_KEY}` } }
		)

		if (!scenarios.data?.scenarios) {
			return NextResponse.json(
				{ error: 'No scenarios found' },
				{ status: 404 }
			)
		}

		return NextResponse.json({
			success: true,
			scenarios: scenarios.data.scenarios,
		})
	} catch (err) {
		console.error('Connection error:', err)
		return NextResponse.json(
			{ error: err?.message || 'An unexpected error occurred' },
			{ status: 500 }
		)
	}
}
