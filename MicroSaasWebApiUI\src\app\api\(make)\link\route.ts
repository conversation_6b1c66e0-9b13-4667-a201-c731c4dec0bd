import prisma from '@/libs/prisma'
import { currentUser } from '@clerk/nextjs/server'
import { NextRequest, NextResponse } from 'next/server'

// Mark this route as dynamic
export const dynamic = 'force-dynamic'
export const runtime = 'edge'

export async function GET(req: NextRequest) {
	try {
		const user = await currentUser()
		// Take projectID from the query params
		const projectID = req.nextUrl.searchParams.get('projectID')

		if (!user) {
			return NextResponse.json(
				{ error: 'Unauthorized' },
				{ status: 401 }
			)
		}

		if (!projectID) {
			return NextResponse.json(
				{ error: 'Project ID is required' },
				{ status: 400 }
			)
		}

		const project = await prisma.project.findUnique({
			where: {
				id: projectID,
			},
		})

		if (!project?.webhookLink) {
			return NextResponse.json(
				{ error: 'Project not found or webhook link not available' },
				{ status: 404 }
			)
		}

		return NextResponse.json({ 
			success: true, 
			webhookLink: project.webhookLink 
		})
	} catch (err) {
		console.error('Connection error:', err)
		return NextResponse.json(
			{ error: 'An unexpected error occurred' },
			{ status: 500 }
		)
	}
}
