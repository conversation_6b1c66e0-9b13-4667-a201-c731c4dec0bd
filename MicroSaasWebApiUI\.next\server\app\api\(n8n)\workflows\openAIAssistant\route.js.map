{"version": 3, "file": "app/api/(n8n)/workflows/openAIAssistant/route.js", "mappings": "oFAAAA,CAAAA,EAAAC,OAAA,CAAAC,QAAA,0CCAAF,CAAAA,EAAAC,OAAA,CAAAC,QAAA,6ZEqBO,IAAMC,EAAU,gBACVC,EAAU,OAEhB,eAAeC,EAAKC,CAAY,EACtC,GAAI,CACH,GAAM,CAAEC,GAAAA,CAAE,CAAEC,OAAAA,CAAM,CAAEC,YAAAA,CAAW,CAAE,CAAG,MAAMH,EAAII,IAAI,GAC5CC,EAAO,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,IAEnB,GAAI,CAACD,EACJ,MAAM,MAAU,0BAGjB,GAAI,CAACJ,EACJ,MAAM,MAAU,2BAGjB,GAAI,CAACC,EACJ,MAAM,MAAU,uBAGjB,GAAI,CAACC,EACJ,MAAM,MAAU,4BAIjB,IAAMI,EAAcC,QAAQC,GAAG,CAACF,WAAW,CACrCG,EAAeF,QAAQC,GAAG,CAACE,WAAW,CACtCC,EAAkBJ,QAAQC,GAAG,CAACG,eAAe,CAEnD,GAAI,CAACL,GAAe,CAACG,EACpB,MAAM,MAAU,gCAGjB,IAAMG,EAAO,IAAIC,OAAOC,WAAW,GAC7BC,EAAU,CAAE,gBAAiBT,CAAY,EAGzCU,EAAkC,CACvCC,KAAM,CAAC,OAAO,EAAEL,EAAK,CAAC,CACtBM,KAAM,YACNC,KAAM,CAAElB,OAAQA,CAAO,CACxB,EAQMmB,EAAgBC,CANM,MAAMC,EAAAA,CAAKA,CAACC,IAAI,CAC3C,CAAC,EAAEd,EAAa,YAAY,CAAC,CAC7BO,EACA,CAAED,QAAAA,CAAQ,IAG+BI,IAAI,CAACnB,EAAE,CACjD,GAAI,CAACoB,EACJ,MAAM,MAAU,gCASjB,IAAMI,EAAWC,CALQ,MAAMH,EAAAA,CAAKA,CAACI,GAAG,CACvC,CAAC,EAAEjB,EAAa,WAAW,EAAET,EAAG,CAAC,CACjC,CAAEe,QAAAA,CAAQ,IAGuBI,IAAI,CAChCQ,EAAcf,EAAKgB,OAAO,CAAC,gBAAiB,IAG5CC,EAA+BL,EAASM,KAAK,CAACC,GAAG,CACtD,GACC,2BAAIC,EAAKd,IAAI,CACL,CACN,GAAGc,CAAI,CACPC,WAAY,CACX,GAAGD,EAAKC,UAAU,CAClBC,KAAMP,CACP,CACD,EAGGK,0BAAAA,EAAKd,IAAI,CACL,CACN,GAAGc,CAAI,CACPG,YAAa,CACZC,UAAWhB,CACZ,EACAa,WAAY,CACX,GAAGD,EAAKC,UAAU,CAClB/B,YAAaA,CACd,CACD,EAGM8B,GAgBHK,EAAQC,CAXc,MAAMhB,EAAAA,CAAKA,CAACC,IAAI,CAC3C,CAAC,EAAEd,EAAa,UAAU,CAAC,CAC3B,CACCQ,KAAM,CAAC,gBAAgB,EAAEL,EAAK,CAAC,CAC/BkB,MAAOD,EACPU,YAAaf,EAASe,WAAW,CACjCC,SAAUhB,EAASgB,QAAQ,EAE5B,CAAEzB,QAAAA,CAAQ,IAGuBI,IAAI,CAACnB,EAAE,CACzC,GAAI,CAACqC,EACJ,MAAM,MAAU,iCAEjB,IAAMI,EAAc,CAAC,EAAE9B,EAAgB,CAAC,EAAEgB,EAAY,CAAC,CASvD,GAAI,CANqB,MAAML,EAAAA,CAAKA,CAACC,IAAI,CACxC,CAAC,EAAEd,EAAa,WAAW,EAAE4B,EAAM,SAAS,CAAC,CAC7C,CAAC,EACD,CAAEtB,QAAAA,CAAQ,GAIV,MAAM,MAAU,+BAkBjB,GAAI,CAbc,MAAM2B,EAAAA,CAAMA,CAACC,OAAO,CAACC,MAAM,CAAC,CAC7CzB,KAAM,CACL0B,cAAezB,EACf0B,WAAYT,EACZU,YAAaV,EACbW,cAAe5C,EAAKJ,EAAE,CACtByC,YAAaA,EACbQ,aAAc/C,EACdgB,KAAM,MACNgC,OAAQ,QACT,CACD,GAGC,MAAM,MAAU,wCAGjB,OAAOC,EAAAA,EAAYA,CAAChD,IAAI,CAAC,CACxBiD,QAAS,GACTpD,GAAIqC,EACJI,YAAAA,EACArB,cAAAA,CACD,EACD,CAAE,MAAOiC,EAAY,CAGpB,OAFAC,QAAQD,KAAK,CAAC,qCAAsCA,GAE7CF,EAAAA,EAAYA,CAAChD,IAAI,CACvB,CACCiD,QAAS,GACTC,MACCA,EAAME,QAAQ,EAAEpC,MAAMqC,SACtB,sCACDC,QAASJ,EAAME,QAAQ,EAAEpC,MAAQkC,EAAMG,OAAO,EAE/C,CAAEN,OAAQG,EAAME,QAAQ,EAAEL,QAAU,GAAI,EAE1C,CACD,CC/KA,IAAAQ,EAAA,IAAwBC,EAAAC,mBAAmB,EAC3CC,WAAA,CACAC,KAAcC,EAAAC,CAAS,CAAAC,SAAA,CACvBC,KAAA,6CACAC,SAAA,iCACAC,SAAA,QACAC,WAAA,+CACA,EACAC,iBAAA,2FACAC,iBAVA,GAWAC,SAAYC,CACZ,GAIA,CAAQC,oBAAAA,CAAA,CAAAC,6BAAAA,CAAA,CAAAC,YAAAA,CAAA,EAAiElB,EACzEmB,EAAA,6CACA,SAAAC,IACA,MAAW,GAAAC,EAAAC,EAAA,EAAW,CACtBJ,YAAAA,EACAD,6BAAAA,CACA,EACA,CC1BO,IAAAM,EAAqBC,EAC5BC,EAAeC,EAAAC,CAAsB,CAAAC,IAAA,CAAM5B,iDCA3C,IAAA6B,EAFe,GAAIC,UAAAA,YAAYA", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/ignored|C:\\Projects\\PersonalPortal\\node_modules\\crypto-js|crypto", "webpack://_N_E/./src/app/api/(n8n)/workflows/openAIAssistant/route.ts", "webpack://_N_E/./src/app/api/(n8n)/workflows/openAIAssistant/route.ts?13b8", "webpack://_N_E/?ff43", "webpack://_N_E/./src/libs/prisma.ts"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "/* (ignored) */", "import prisma from '@/libs/prisma'\r\nimport { currentUser } from '@clerk/nextjs/server'\r\nimport axios from 'axios'\r\nimport { NextResponse } from 'next/server'\r\n\r\ninterface WorkflowNode {\r\n\tid: string\r\n\tparameters: Record<string, any>\r\n\tname: string\r\n\ttype: string\r\n\ttypeVersion: number\r\n\tposition: [number, number]\r\n\tcredentials?: Record<string, any>\r\n}\r\n\r\ninterface N8nCredentials {\r\n\tname: string\r\n\ttype: string\r\n\tdata: Record<string, string>\r\n}\r\n\r\nexport const dynamic = 'force-dynamic'\r\nexport const runtime = 'edge'\r\n\r\nexport async function POST(req: Request) {\r\n\ttry {\r\n\t\tconst { id, apiKey, assistantId } = await req.json()\r\n\t\tconst user = await currentUser()\r\n\r\n\t\tif (!user) {\r\n\t\t\tthrow new Error('User not authenticated')\r\n\t\t}\r\n\r\n\t\tif (!id) {\r\n\t\t\tthrow new Error('Workflow ID is required')\r\n\t\t}\r\n\r\n\t\tif (!apiKey) {\r\n\t\t\tthrow new Error('API Key is required')\r\n\t\t}\r\n\r\n\t\tif (!assistantId) {\r\n\t\t\tthrow new Error('Assistant ID is required')\r\n\t\t}\r\n\r\n\t\t// Safe retrieval of environment variables\r\n\t\tconst N8N_API_KEY = process.env.N8N_API_KEY\r\n\t\tconst N8N_BASE_URL = process.env.N8N_API_URL\r\n\t\tconst N8N_WEBHOOK_URL = process.env.N8N_WEBHOOK_URL\r\n\r\n\t\tif (!N8N_API_KEY || !N8N_BASE_URL) {\r\n\t\t\tthrow new Error('N8N configuration is missing')\r\n\t\t}\r\n\r\n\t\tconst date = new Date().toISOString()\r\n\t\tconst headers = { 'X-N8N-API-KEY': N8N_API_KEY }\r\n\r\n\t\t// 1. Creating OpenAI credentials\r\n\t\tconst credentialsData: N8nCredentials = {\r\n\t\t\tname: `OpenAI ${date}`,\r\n\t\t\ttype: 'openAiApi',\r\n\t\t\tdata: { apiKey: apiKey },\r\n\t\t}\r\n\r\n\t\tconst credentialsResponse = await axios.post(\r\n\t\t\t`${N8N_BASE_URL}/credentials`,\r\n\t\t\tcredentialsData,\r\n\t\t\t{ headers }\r\n\t\t)\r\n\r\n\t\tconst credentialsId = credentialsResponse.data.id\r\n\t\tif (!credentialsId) {\r\n\t\t\tthrow new Error('Failed to create credentials')\r\n\t\t}\r\n\r\n\t\t// 3. Getting the source workflow\r\n\t\tconst workflowResponse = await axios.get(\r\n\t\t\t`${N8N_BASE_URL}/workflows/${id}`,\r\n\t\t\t{ headers }\r\n\t\t)\r\n\r\n\t\tconst workflow = workflowResponse.data\r\n\t\tconst webhookPath = date.replace(/[^a-zA-Z0-9]/g, '')\r\n\r\n\t\t// 4. Updating nodes configuration\r\n\t\tconst updatedNodes: WorkflowNode[] = workflow.nodes.map(\r\n\t\t\t(node: WorkflowNode) => {\r\n\t\t\t\tif (node.type === 'n8n-nodes-base.webhook') {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\t...node,\r\n\t\t\t\t\t\tparameters: {\r\n\t\t\t\t\t\t\t...node.parameters,\r\n\t\t\t\t\t\t\tpath: webhookPath,\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (node.type === 'n8n-nodes-base.openAi') {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\t...node,\r\n\t\t\t\t\t\tcredentials: {\r\n\t\t\t\t\t\t\topenAiApi: credentialsId,\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tparameters: {\r\n\t\t\t\t\t\t\t...node.parameters,\r\n\t\t\t\t\t\t\tassistantId: assistantId,\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn node\r\n\t\t\t}\r\n\t\t)\r\n\r\n\t\t// 5. Creating new workflow\r\n\t\tconst newWorkflowResponse = await axios.post(\r\n\t\t\t`${N8N_BASE_URL}/workflows`,\r\n\t\t\t{\r\n\t\t\t\tname: `Cloned Workflow ${date}`,\r\n\t\t\t\tnodes: updatedNodes,\r\n\t\t\t\tconnections: workflow.connections,\r\n\t\t\t\tsettings: workflow.settings,\r\n\t\t\t},\r\n\t\t\t{ headers }\r\n\t\t)\r\n\r\n\t\tconst newid = newWorkflowResponse.data.id\r\n\t\tif (!newid) {\r\n\t\t\tthrow new Error('Failed to create new workflow')\r\n\t\t}\r\n\t\tconst webhookLink = `${N8N_WEBHOOK_URL}/${webhookPath}`\r\n\r\n\t\t// Activating the new workflow\r\n\t\tconst activateWorkflow = await axios.post(\r\n\t\t\t`${N8N_BASE_URL}/workflows/${newid}/activate`,\r\n\t\t\t{},\r\n\t\t\t{ headers }\r\n\t\t)\r\n\r\n\t\tif (!activateWorkflow) {\r\n\t\t\tthrow new Error('Failed to activate workflow')\r\n\t\t}\r\n\r\n\t\t// 6. Saving project to database\r\n\r\n\t\tconst dbProject = await prisma.project.create({\r\n\t\t\tdata: {\r\n\t\t\t\tconnection_id: credentialsId,\r\n\t\t\t\twebhook_id: newid, // Using workflow ID instead of separate webhook ID\r\n\t\t\t\tscenario_id: newid,\r\n\t\t\t\tuser_clerk_id: user.id,\r\n\t\t\t\twebhookLink: webhookLink,\r\n\t\t\t\tassistant_id: assistantId,\r\n\t\t\t\ttype: 'n8n',\r\n\t\t\t\tstatus: 'active',\r\n\t\t\t},\r\n\t\t})\r\n\r\n\t\tif (!dbProject) {\r\n\t\t\tthrow new Error('Failed to create project in database')\r\n\t\t}\r\n\r\n\t\treturn NextResponse.json({\r\n\t\t\tsuccess: true,\r\n\t\t\tid: newid,\r\n\t\t\twebhookLink,\r\n\t\t\tcredentialsId,\r\n\t\t})\r\n\t} catch (error: any) {\r\n\t\tconsole.error('Error in workflow cloning process:', error)\r\n\r\n\t\treturn NextResponse.json(\r\n\t\t\t{\r\n\t\t\t\tsuccess: false,\r\n\t\t\t\terror:\r\n\t\t\t\t\terror.response?.data?.message ||\r\n\t\t\t\t\t'Failed to clone and update workflow',\r\n\t\t\t\tdetails: error.response?.data || error.message,\r\n\t\t\t},\r\n\t\t\t{ status: error.response?.status || 500 }\r\n\t\t)\r\n\t}\r\n}\r\n", "import { AppRouteRouteModule } from \"next/dist/server/future/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/future/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Projects\\\\PersonalPortal\\\\src\\\\app\\\\api\\\\(n8n)\\\\workflows\\\\openAIAssistant\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/(n8n)/workflows/openAIAssistant/route\",\n        pathname: \"/api/workflows/openAIAssistant\",\n        filename: \"route\",\n        bundlePath: \"app/api/(n8n)/workflows/openAIAssistant/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\PersonalPortal\\\\src\\\\app\\\\api\\\\(n8n)\\\\workflows\\\\openAIAssistant\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/(n8n)/workflows/openAIAssistant/route\";\nfunction patchFetch() {\n    return _patchFetch({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\nexport { routeModule, requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, originalPathname, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "import { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2F(n8n)%2Fworkflows%2FopenAIAssistant%2Froute&page=%2Fapi%2F(n8n)%2Fworkflows%2FopenAIAssistant%2Froute&pagePath=private-next-app-dir%2Fapi%2F(n8n)%2Fworkflows%2FopenAIAssistant%2Froute.ts&appDir=C%3A%5CProjects%5CPersonalPortal%5Csrc%5Capp&appPaths=%2Fapi%2F(n8n)%2Fworkflows%2FopenAIAssistant%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/(n8n)/workflows/openAIAssistant/route.ts?__next_edge_ssr_entry__\";\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule);\n\n//# sourceMappingURL=edge-app-route.js.map", "import { PrismaClient } from \"@prisma/client\";\r\n\r\nconst prisma = new PrismaClient();\r\n\r\nexport default prisma;\r\n"], "names": ["module", "exports", "require", "dynamic", "runtime", "POST", "req", "id", "<PERSON><PERSON><PERSON><PERSON>", "assistantId", "json", "user", "currentUser", "N8N_API_KEY", "process", "env", "N8N_BASE_URL", "N8N_API_URL", "N8N_WEBHOOK_URL", "date", "Date", "toISOString", "headers", "credentialsData", "name", "type", "data", "credentialsId", "credentialsResponse", "axios", "post", "workflow", "workflowResponse", "get", "webhookPath", "replace", "updatedNodes", "nodes", "map", "node", "parameters", "path", "credentials", "openAiApi", "newid", "newWorkflowResponse", "connections", "settings", "webhookLink", "prisma", "project", "create", "connection_id", "webhook_id", "scenario_id", "user_clerk_id", "assistant_id", "status", "NextResponse", "success", "error", "console", "response", "message", "details", "routeModule", "module_compiled", "AppRouteRouteModule", "definition", "kind", "route_kind", "x", "APP_ROUTE", "page", "pathname", "filename", "bundlePath", "resolvedPagePath", "nextConfigOutput", "userland", "route_namespaceObject", "requestAsyncStorage", "staticGenerationAsyncStorage", "serverHooks", "originalPathname", "patchFetch", "patch_fetch", "XH", "ComponentMod", "route_next_edge_ssr_entry_namespaceObject", "next_edge_app_route_loaderabsolutePagePath_private_next_app_dir_2Fapi_2F_n8n_2Fworkflows_2FopenAIAssistant_2Froute_ts_page_2Fapi_2F_n8n_2Fworkflows_2FopenAIAssistant_2Froute_appDirLoader_bmV4dC1hcHAtbG9hZGVyP25hbWU9YXBwJTJGYXBpJTJGKG44biklMkZ3b3JrZmxvd3MlMkZvcGVuQUlBc3Npc3RhbnQlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRihuOG4pJTJGd29ya2Zsb3dzJTJGb3BlbkFJQXNzaXN0YW50JTJGcm91dGUmcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkYobjhuKSUyRndvcmtmbG93cyUyRm9wZW5BSUFzc2lzdGFudCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDUHJvamVjdHMlNUNQZXJzb25hbFBvcnRhbCU1Q3NyYyU1Q2FwcCZhcHBQYXRocz0lMkZhcGklMkYobjhuKSUyRndvcmtmbG93cyUyRm9wZW5BSUFzc2lzdGFudCUyRnJvdXRlJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEIQ_3D_3D_nextConfigOutput_preferredRegion_middlewareConfig_e30_3D_", "edge_route_module_wrapper", "a", "wrap", "__WEBPACK_DEFAULT_EXPORT__", "PrismaClient"], "sourceRoot": ""}