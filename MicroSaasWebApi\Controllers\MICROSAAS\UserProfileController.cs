using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MicroSaasWebApi.Models.Profile;
using MicroSaasWebApi.Services.MICROSAAS.Interface;
using Swashbuckle.AspNetCore.Annotations;
using System.Security.Claims;

namespace MicroSaasWebApi.Controllers.MICROSAAS
{
    /// <summary>
    /// User Profile management controller for MicroSaaS template
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class UserProfileController : ControllerBase
    {
        private readonly IUserProfileService _userProfileService;
        private readonly ILogger<UserProfileController> _logger;

        public UserProfileController(
            IUserProfileService userProfileService,
            ILogger<UserProfileController> logger)
        {
            _userProfileService = userProfileService;
            _logger = logger;
        }

        /// <summary>
        /// Get current user's profile
        /// </summary>
        /// <returns>User profile information</returns>
        [HttpGet("me")]
        [SwaggerOperation(Summary = "Get current user profile", Description = "Retrieves the profile of the currently authenticated user")]
        [ProducesResponseType(typeof(UserProfile), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetMyProfile()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found in token");
                }

                var profile = await _userProfileService.GetUserProfileAsync(userId);
                if (profile == null)
                {
                    return NotFound("User profile not found");
                }

                return Ok(profile);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user profile");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Update current user's profile
        /// </summary>
        /// <param name="updateRequest">Profile update data</param>
        /// <returns>Updated user profile</returns>
        [HttpPut("me")]
        [SwaggerOperation(Summary = "Update user profile", Description = "Updates the profile of the currently authenticated user")]
        [ProducesResponseType(typeof(UserProfile), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateMyProfile([FromBody] UpdateUserProfileRequest updateRequest)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var userId = GetCurrentUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found in token");
                }

                var updatedProfile = await _userProfileService.UpdateUserProfileAsync(userId, updateRequest);
                if (updatedProfile == null)
                {
                    return NotFound("User profile not found");
                }

                return Ok(updatedProfile);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user profile");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get user profile by ID (Admin only)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>User profile information</returns>
        [HttpGet("{userId}")]
        [Authorize(Roles = "Admin")]
        [SwaggerOperation(Summary = "Get user profile by ID", Description = "Retrieves a user profile by ID (Admin only)")]
        [ProducesResponseType(typeof(UserProfile), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetUserProfile(string userId)
        {
            try
            {
                var profile = await _userProfileService.GetUserProfileAsync(userId);
                if (profile == null)
                {
                    return NotFound("User profile not found");
                }

                return Ok(profile);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user profile for user {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Delete current user's profile
        /// </summary>
        /// <returns>Success confirmation</returns>
        [HttpDelete("me")]
        [SwaggerOperation(Summary = "Delete user profile", Description = "Deletes the profile of the currently authenticated user")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteMyProfile()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found in token");
                }

                var success = await _userProfileService.DeleteUserProfileAsync(userId);
                if (!success)
                {
                    return NotFound("User profile not found");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user profile");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Upload profile image
        /// </summary>
        /// <param name="file">Image file</param>
        /// <returns>Updated profile with new image URL</returns>
        [HttpPost("me/upload-image")]
        [SwaggerOperation(Summary = "Upload profile image", Description = "Uploads a profile image for the current user")]
        [ProducesResponseType(typeof(UserProfile), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> UploadProfileImage(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest("No file uploaded");
                }

                // Validate file type and size
                var allowedTypes = new[] { "image/jpeg", "image/png", "image/gif" };
                if (!allowedTypes.Contains(file.ContentType))
                {
                    return BadRequest("Invalid file type. Only JPEG, PNG, and GIF are allowed.");
                }

                if (file.Length > 5 * 1024 * 1024) // 5MB limit
                {
                    return BadRequest("File size exceeds 5MB limit");
                }

                var userId = GetCurrentUserId();
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized("User ID not found in token");
                }

                var updatedProfile = await _userProfileService.UploadProfileImageAsync(userId, file);
                return Ok(updatedProfile);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading profile image");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get current user ID from JWT token
        /// </summary>
        /// <returns>User ID</returns>
        private string? GetCurrentUserId()
        {
            return User.FindFirst(ClaimTypes.NameIdentifier)?.Value ??
                   User.FindFirst("sub")?.Value ??
                   User.FindFirst("user_id")?.Value;
        }
    }

    /// <summary>
    /// Update user profile request model
    /// </summary>
    public class UpdateUserProfileRequest
    {
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Bio { get; set; }
        public string? Company { get; set; }
        public string? JobTitle { get; set; }
        public string? Website { get; set; }
        public string? Country { get; set; }
        public string? City { get; set; }
        public string? TimeZone { get; set; }
    }
}
