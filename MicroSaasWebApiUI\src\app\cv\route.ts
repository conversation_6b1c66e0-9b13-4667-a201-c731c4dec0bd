import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
    try {
        const filePath = path.join(process.cwd(), 'public', 'doc', 'CVJavianPicardo2025.pdf')
        
        // Check if file exists
        if (!fs.existsSync(filePath)) {
            return new NextResponse('File not found', { status: 404 })
        }

        const fileBuffer = await fs.promises.readFile(filePath)

        return new NextResponse(fileBuffer, {
            headers: {
                'Content-Type': 'application/pdf',
                'Content-Disposition': 'attachment; filename=CVJavianPicardo2025.pdf',
                'Cache-Control': 'no-cache'
            }
        })
    } catch (error) {
        console.error('Error serving PDF:', error)
        return new NextResponse('Error serving file', { status: 500 })
    }
}