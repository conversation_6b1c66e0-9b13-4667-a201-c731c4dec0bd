import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

/**
 * This endpoint handles incoming emails forwarded by Resend.
 * When someone sends an <NAME_EMAIL>, <PERSON>send will forward it to this endpoint.
 * We can then process the email and take appropriate actions.
 */
export async function POST(req: NextRequest) {
  try {
    // Parse the incoming email data from Resend
    const emailData = await req.json();
    
    console.log('Received email from Resend webhook:', emailData);
    
    // Here you can process the email data
    // For example, you could:
    // 1. Store it in your database
    // 2. Send a notification
    // 3. Create a support ticket
    // 4. Forward it to another email address
    
    // For now, we'll just log it and return a success response
    
    return NextResponse.json({ 
      success: true, 
      message: 'Email received and processed successfully' 
    });
  } catch (error) {
    console.error('Error processing incoming email:', error);
    
    return NextResponse.json({ 
      success: false, 
      error: 'Failed to process incoming email' 
    }, { status: 500 });
  }
}
