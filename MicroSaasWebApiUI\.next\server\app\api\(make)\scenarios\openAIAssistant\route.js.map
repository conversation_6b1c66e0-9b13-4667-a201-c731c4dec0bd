{"version": 3, "file": "app/api/(make)/scenarios/openAIAssistant/route.js", "mappings": "oFAAAA,CAAAA,EAAAC,OAAA,CAAAC,QAAA,0CCAAF,CAAAA,EAAAC,OAAA,CAAAC,QAAA,6ZEcO,IAAMC,EAAU,gBACVC,EAAU,OAEhB,eAAeC,EAAKC,CAAY,EACtC,GAAI,CACH,GAAM,CAAEC,GAAAA,CAAE,CAAEC,OAAAA,CAAM,CAAEC,OAAAA,CAAM,CAAEC,YAAAA,CAAW,CAAE,CAAG,MAAMJ,EAAIK,IAAI,GACpDC,EAAO,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,IAEnB,GAAI,CAACD,EACJ,MAAM,MAAU,kBAGjB,IAAME,EAAeC,QAAQC,GAAG,CAACF,YAAY,CACvCG,EAAeF,QAAQC,GAAG,CAACC,YAAY,CACvCC,EAAWH,QAAQC,GAAG,CAACG,YAAY,CACnCC,EAAO,IAAIC,OAAOC,WAAW,GAc7BC,EAAeC,CAXM,MAAMC,EAAAA,CAAKA,CAACC,IAAI,CAC1C,CAAC,EAAER,EAAS,oBAAoB,EAAED,EAAa,CAAC,CAChD,CACCU,YAAa,CAAC,OAAO,EAAEP,EAAK,CAAC,CAC7BQ,YAAa,eACbpB,OAAQA,EACRC,OAAQA,CACT,EACA,CAAEoB,QAAS,CAAEC,cAAe,CAAC,MAAM,EAAEhB,EAAa,CAAC,CAAG,IAGfiB,IAAI,CAACC,UAAU,CAACzB,EAAE,CAE1D,GAAI,CAACgB,EACJ,MAAM,MAAU,wBAIjB,IAAMU,EAAkB,MAAMR,EAAAA,CAAKA,CAACC,IAAI,CACvC,CAAC,EAAER,EAAS,cAAc,EAAED,EAAa,CAAC,CAC1C,CACCiB,KAAM,CAAC,eAAe,EAAEd,EAAK,CAAC,CAC9Be,QAASlB,EACTmB,OAAQnB,EACRoB,SAAU,kBACVC,OAAQ,GACRT,QAAS,GACTU,UAAW,GACXC,UAAW,CAAC,CAAEN,KAAM,UAAWO,KAAM,MAAO,EAAE,EAE/C,CAAEZ,QAAS,CAAEC,cAAe,CAAC,MAAM,EAAEhB,EAAa,CAAC,CAAG,GAGjD4B,EAAYT,EAAgBF,IAAI,CAACY,IAAI,CAACpC,EAAE,CACxCqC,EAAcX,EAAgBF,IAAI,CAACY,IAAI,CAACE,GAAG,CAEjD,GAAI,CAACH,GAAa,CAACE,EAClB,MAAME,MAAM,uBAWb,IAAMC,EAAYC,CAPQ,MAAMvB,EAAAA,CAAKA,CAACwB,GAAG,CACxC,CAAC,EAAE/B,EAAS,WAAW,EAAEX,EAAG,UAAU,CAAC,CACvC,CACCsB,QAAS,CAAEC,cAAe,CAAC,MAAM,EAAEhB,EAAa,CAAC,CAClD,IAGmCiB,IAAI,CAACmB,QAAQ,CAACH,SAAS,CAIrDI,EAAcC,EAHGA,IAAI,CAGFC,GAAG,CAACrD,GAE5B,0BAAIA,EAAOA,MAAM,CACT,CACN,GAAGA,CAAM,CACTsD,WAAY,CACX,GAAGtD,EAAOsD,UAAU,CACpBX,KAAMD,CACP,CACD,EAIG1C,0CAAAA,EAAOA,MAAM,CACT,CACN,GAAGA,CAAM,CACTsD,WAAY,CACX,GAAGtD,EAAOsD,UAAU,CACpBC,YAAahC,CACd,EACAiC,OAAQ,CACP,GAAGxD,EAAOwD,MAAM,CAChB9C,YAAaA,CACd,CACD,EAGMV,GAsBFyD,EAAQC,CAlBS,MAAMjC,EAAAA,CAAKA,CAACC,IAAI,CACtC,CAAC,EAAER,EAAS,UAAU,CAAC,CACvB,CACCgB,KAAM,CAAC,gBAAgB,EAAEd,EAAK,CAAC,CAC/BgB,OAAQnB,EACR8B,UAAWY,KAAKpB,SAAS,CAAC,CACzB,GAAGQ,CAAS,CACZb,KAAM,CAAC,gBAAgB,EAAEd,EAAK,CAAC,CAC/BgC,KAAMD,CACP,GACAS,WAAYD,KAAKpB,SAAS,CAAC,CAAEE,KAAM,eAAgBoB,SAAU,GAAI,GACjEC,QAAS,EACV,EACA,CACCjC,QAAS,CAAEC,cAAe,CAAC,MAAM,EAAEhB,EAAa,CAAC,CAClD,IAG4BiB,IAAI,CAACgC,QAAQ,CAACxD,EAAE,CAE7C,GAAI,CAACkD,EACJ,MAAM,MAAU,iCA0BjB,GAtBA,MAAMhC,EAAAA,CAAKA,CAACC,IAAI,CACf,CAAC,EAAER,EAAS,WAAW,EAAEuC,EAAM,MAAM,CAAC,CACtC,CAAC,EACD,CACC5B,QAAS,CAAEC,cAAe,CAAC,MAAM,EAAEhB,EAAa,CAAC,CAClD,GAiBG,CAbc,MAAMkD,EAAAA,CAAMA,CAACC,OAAO,CAACC,MAAM,CAAC,CAC7CnC,KAAM,CACLoC,cAAe5C,EAAa6C,QAAQ,GACpCC,WAAY3B,EAAU0B,QAAQ,GAC9BE,YAAab,EAAMW,QAAQ,GAC3BG,cAAe3D,EAAKL,EAAE,CACtBqC,YAAaA,EACb4B,aAAc9D,EACd+B,KAAM,OACNgC,OAAQ,QACT,CACD,GAGC,MAAM,MAAU,4BAIjB,OAAOC,EAAAA,EAAYA,CAAC/D,IAAI,CAAC,CACxBgE,QAAS,GACTpE,GAAIkD,EACJb,YAAAA,EACArB,aAAAA,CACD,EACD,CAAE,MAAOqD,EAAO,CAGf,OADAC,QAAQD,KAAK,CAAC,qCAAsCA,GAC7CF,EAAAA,EAAYA,CAAC/D,IAAI,CACvB,CACCgE,QAAS,GACTC,MACCA,EAAM1B,QAAQ,EAAEnB,MAAM+C,SACtB,sCACDC,QAASH,EAAM1B,QAAQ,EAAEnB,MAAQ6C,EAAME,OAAO,EAE/C,CAAEL,OAAQG,EAAM1B,QAAQ,EAAEuB,QAAU,GAAI,EAE1C,CACD,CCpLA,IAAAO,EAAA,IAAwBC,EAAAC,mBAAmB,EAC3CC,WAAA,CACAC,KAAcC,EAAAC,CAAS,CAAAC,SAAA,CACvBC,KAAA,8CACAC,SAAA,iCACAC,SAAA,QACAC,WAAA,gDACA,EACAC,iBAAA,4FACAC,iBAVA,GAWAC,SAAYC,CACZ,GAIA,CAAQC,oBAAAA,CAAA,CAAAC,6BAAAA,CAAA,CAAAC,YAAAA,CAAA,EAAiElB,EACzEmB,EAAA,8CACA,SAAAC,IACA,MAAW,GAAAC,EAAAC,EAAA,EAAW,CACtBJ,YAAAA,EACAD,6BAAAA,CACA,EACA,CC1BO,IAAAM,EAAqBC,EAC5BC,EAAeC,EAAAC,CAAsB,CAAAC,IAAA,CAAM5B,iDCA3C,IAAA6B,EAFe,GAAIC,UAAAA,YAAYA", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/ignored|C:\\Projects\\PersonalPortal\\node_modules\\crypto-js|crypto", "webpack://_N_E/./src/app/api/(make)/scenarios/openAIAssistant/route.ts", "webpack://_N_E/./src/app/api/(make)/scenarios/openAIAssistant/route.ts?e68e", "webpack://_N_E/?752b", "webpack://_N_E/./src/libs/prisma.ts"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "/* (ignored) */", "import prisma from '@/libs/prisma'\r\nimport { currentUser } from '@clerk/nextjs/server'\r\nimport axios from 'axios'\r\nimport { NextResponse } from 'next/server'\r\n\r\ninterface FlowModule {\r\n\tid: number\r\n\tmapper: Record<string, any>\r\n\tparameters: Record<string, any>\r\n\tmodule: string\r\n\tversion: number\r\n\tmetadata: Record<string, any>\r\n}\r\n\r\nexport const dynamic = 'force-dynamic'\r\nexport const runtime = 'edge'\r\n\r\nexport async function POST(req: Request) {\r\n\ttry {\r\n\t\tconst { id, apiKey, apiOrg, assistantId } = await req.json()\r\n\t\tconst user = await currentUser()\r\n\r\n\t\tif (!user) {\r\n\t\t\tthrow new Error('User not found')\r\n\t\t}\r\n\r\n\t\tconst MAKE_API_KEY = process.env.MAKE_API_KEY\r\n\t\tconst MAKE_TEAM_ID = process.env.MAKE_TEAM_ID\r\n\t\tconst BASE_URL = process.env.MAKE_API_URL\r\n\t\tconst date = new Date().toISOString()\r\n\r\n\t\t// 1. Create OpenAI connection\r\n\t\tconst connectionResponse = await axios.post(\r\n\t\t\t`${BASE_URL}/connections?teamId=${MAKE_TEAM_ID}`,\r\n\t\t\t{\r\n\t\t\t\taccountName: `OpenAI ${date}`,\r\n\t\t\t\taccountType: 'openai-gpt-3',\r\n\t\t\t\tapiKey: apiKey,\r\n\t\t\t\tapiOrg: apiOrg,\r\n\t\t\t},\r\n\t\t\t{ headers: { Authorization: `Token ${MAKE_API_KEY}` } }\r\n\t\t)\r\n\r\n\t\tconst connectionId = connectionResponse.data.connection.id\r\n\r\n\t\tif (!connectionId) {\r\n\t\t\tthrow new Error('Connection ID missed')\r\n\t\t}\r\n\r\n\t\t// 2. Create webhook for receiving messages\r\n\t\tconst webhookResponse = await axios.post(\r\n\t\t\t`${BASE_URL}/hooks?teamId=${MAKE_TEAM_ID}`,\r\n\t\t\t{\r\n\t\t\t\tname: `webhook openAI ${date}`,\r\n\t\t\t\tteam_id: MAKE_TEAM_ID,\r\n\t\t\t\tteamId: MAKE_TEAM_ID,\r\n\t\t\t\ttypeName: 'gateway-webhook',\r\n\t\t\t\tmethod: true,\r\n\t\t\t\theaders: true,\r\n\t\t\t\tstringify: false,\r\n\t\t\t\tinterface: [{ name: 'message', type: 'text' }],\r\n\t\t\t},\r\n\t\t\t{ headers: { Authorization: `Token ${MAKE_API_KEY}` } }\r\n\t\t)\r\n\r\n\t\tconst webhookId = webhookResponse.data.hook.id\r\n\t\tconst webhookLink = webhookResponse.data.hook.url\r\n\r\n\t\tif (!webhookId || !webhookLink) {\r\n\t\t\tthrow Error('hook creating error')\r\n\t\t}\r\n\r\n\t\t// 3. Get the blueprint of the source scenario\r\n\t\tconst blueprintResponse = await axios.get(\r\n\t\t\t`${BASE_URL}/scenarios/${id}/blueprint`,\r\n\t\t\t{\r\n\t\t\t\theaders: { Authorization: `Token ${MAKE_API_KEY}` },\r\n\t\t\t}\r\n\t\t)\r\n\r\n\t\tconst blueprint = blueprintResponse.data.response.blueprint\r\n\t\tconst flow = blueprint.flow as FlowModule[]\r\n\r\n\t\t// 4. Update module configurations in the flow\r\n\t\tconst updatedFlow = flow.map(module => {\r\n\t\t\t// Update webhook module with new webhook ID\r\n\t\t\tif (module.module === 'gateway:CustomWebHook') {\r\n\t\t\t\treturn {\r\n\t\t\t\t\t...module,\r\n\t\t\t\t\tparameters: {\r\n\t\t\t\t\t\t...module.parameters,\r\n\t\t\t\t\t\thook: webhookId,\r\n\t\t\t\t\t},\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// Update OpenAI module with new connection and assistant ID\r\n\t\t\tif (module.module === 'openai-gpt-3:messageAssistantAdvanced') {\r\n\t\t\t\treturn {\r\n\t\t\t\t\t...module,\r\n\t\t\t\t\tparameters: {\r\n\t\t\t\t\t\t...module.parameters,\r\n\t\t\t\t\t\t__IMTCONN__: connectionId,\r\n\t\t\t\t\t},\r\n\t\t\t\t\tmapper: {\r\n\t\t\t\t\t\t...module.mapper,\r\n\t\t\t\t\t\tassistantId: assistantId,\r\n\t\t\t\t\t},\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn module\r\n\t\t})\r\n\r\n\t\t// 5. Create new scenario with updated configuration\r\n\t\tconst createResponse = await axios.post(\r\n\t\t\t`${BASE_URL}/scenarios`,\r\n\t\t\t{\r\n\t\t\t\tname: `Cloned Scenario ${date}`,\r\n\t\t\t\tteamId: MAKE_TEAM_ID,\r\n\t\t\t\tblueprint: JSON.stringify({\r\n\t\t\t\t\t...blueprint,\r\n\t\t\t\t\tname: `Cloned Scenario ${date}`,\r\n\t\t\t\t\tflow: updatedFlow,\r\n\t\t\t\t}),\r\n\t\t\t\tscheduling: JSON.stringify({ type: 'indefinitely', interval: 900 }),\r\n\t\t\t\tbasedon: 20,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\theaders: { Authorization: `Token ${MAKE_API_KEY}` },\r\n\t\t\t}\r\n\t\t)\r\n\r\n\t\tconst newid = createResponse.data.scenario.id\r\n\r\n\t\tif (!newid) {\r\n\t\t\tthrow new Error('Failed to create new scenario')\r\n\t\t}\r\n\r\n\t\t// 6. Activate the newly created scenario\r\n\t\tawait axios.post(\r\n\t\t\t`${BASE_URL}/scenarios/${newid}/start`,\r\n\t\t\t{},\r\n\t\t\t{\r\n\t\t\t\theaders: { Authorization: `Token ${MAKE_API_KEY}` },\r\n\t\t\t}\r\n\t\t)\r\n\r\n\t\t// 7. Store project details in database\r\n\t\tconst dbProject = await prisma.project.create({\r\n\t\t\tdata: {\r\n\t\t\t\tconnection_id: connectionId.toString(),\r\n\t\t\t\twebhook_id: webhookId.toString(),\r\n\t\t\t\tscenario_id: newid.toString(),\r\n\t\t\t\tuser_clerk_id: user.id,\r\n\t\t\t\twebhookLink: webhookLink,\r\n\t\t\t\tassistant_id: assistantId,\r\n\t\t\t\ttype: 'make',\r\n\t\t\t\tstatus: 'active',\r\n\t\t\t},\r\n\t\t})\r\n\r\n\t\tif (!dbProject) {\r\n\t\t\tthrow new Error('Failed to create project')\r\n\t\t}\r\n\r\n\t\t// Return success response with necessary IDs\r\n\t\treturn NextResponse.json({\r\n\t\t\tsuccess: true,\r\n\t\t\tid: newid,\r\n\t\t\twebhookLink,\r\n\t\t\tconnectionId,\r\n\t\t})\r\n\t} catch (error) {\r\n\t\t// Handle and log any errors during the process\r\n\t\tconsole.error('Error in scenario cloning process:', error)\r\n\t\treturn NextResponse.json(\r\n\t\t\t{\r\n\t\t\t\tsuccess: false,\r\n\t\t\t\terror:\r\n\t\t\t\t\terror.response?.data?.message ||\r\n\t\t\t\t\t'Failed to clone and update scenario',\r\n\t\t\t\tdetails: error.response?.data || error.message,\r\n\t\t\t},\r\n\t\t\t{ status: error.response?.status || 500 }\r\n\t\t)\r\n\t}\r\n}\r\n", "import { AppRouteRouteModule } from \"next/dist/server/future/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/future/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Projects\\\\PersonalPortal\\\\src\\\\app\\\\api\\\\(make)\\\\scenarios\\\\openAIAssistant\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/(make)/scenarios/openAIAssistant/route\",\n        pathname: \"/api/scenarios/openAIAssistant\",\n        filename: \"route\",\n        bundlePath: \"app/api/(make)/scenarios/openAIAssistant/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\PersonalPortal\\\\src\\\\app\\\\api\\\\(make)\\\\scenarios\\\\openAIAssistant\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/(make)/scenarios/openAIAssistant/route\";\nfunction patchFetch() {\n    return _patchFetch({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\nexport { routeModule, requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, originalPathname, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "import { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2F(make)%2Fscenarios%2FopenAIAssistant%2Froute&page=%2Fapi%2F(make)%2Fscenarios%2FopenAIAssistant%2Froute&pagePath=private-next-app-dir%2Fapi%2F(make)%2Fscenarios%2FopenAIAssistant%2Froute.ts&appDir=C%3A%5CProjects%5CPersonalPortal%5Csrc%5Capp&appPaths=%2Fapi%2F(make)%2Fscenarios%2FopenAIAssistant%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/(make)/scenarios/openAIAssistant/route.ts?__next_edge_ssr_entry__\";\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule);\n\n//# sourceMappingURL=edge-app-route.js.map", "import { PrismaClient } from \"@prisma/client\";\r\n\r\nconst prisma = new PrismaClient();\r\n\r\nexport default prisma;\r\n"], "names": ["module", "exports", "require", "dynamic", "runtime", "POST", "req", "id", "<PERSON><PERSON><PERSON><PERSON>", "apiOrg", "assistantId", "json", "user", "currentUser", "MAKE_API_KEY", "process", "env", "MAKE_TEAM_ID", "BASE_URL", "MAKE_API_URL", "date", "Date", "toISOString", "connectionId", "connectionResponse", "axios", "post", "accountName", "accountType", "headers", "Authorization", "data", "connection", "webhookResponse", "name", "team_id", "teamId", "typeName", "method", "stringify", "interface", "type", "webhookId", "hook", "webhookLink", "url", "Error", "blueprint", "blueprintResponse", "get", "response", "updatedFlow", "flow", "map", "parameters", "__IMTCONN__", "mapper", "newid", "createResponse", "JSON", "scheduling", "interval", "<PERSON>on", "scenario", "prisma", "project", "create", "connection_id", "toString", "webhook_id", "scenario_id", "user_clerk_id", "assistant_id", "status", "NextResponse", "success", "error", "console", "message", "details", "routeModule", "module_compiled", "AppRouteRouteModule", "definition", "kind", "route_kind", "x", "APP_ROUTE", "page", "pathname", "filename", "bundlePath", "resolvedPagePath", "nextConfigOutput", "userland", "route_namespaceObject", "requestAsyncStorage", "staticGenerationAsyncStorage", "serverHooks", "originalPathname", "patchFetch", "patch_fetch", "XH", "ComponentMod", "route_next_edge_ssr_entry_namespaceObject", "next_edge_app_route_loaderabsolutePagePath_private_next_app_dir_2Fapi_2F_make_2Fscenarios_2FopenAIAssistant_2Froute_ts_page_2Fapi_2F_make_2Fscenarios_2FopenAIAssistant_2Froute_appDirLoader_bmV4dC1hcHAtbG9hZGVyP25hbWU9YXBwJTJGYXBpJTJGKG1ha2UpJTJGc2NlbmFyaW9zJTJGb3BlbkFJQXNzaXN0YW50JTJGcm91dGUmcGFnZT0lMkZhcGklMkYobWFrZSklMkZzY2VuYXJpb3MlMkZvcGVuQUlBc3Npc3RhbnQlMkZyb3V0ZSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRihtYWtlKSUyRnNjZW5hcmlvcyUyRm9wZW5BSUFzc2lzdGFudCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDUHJvamVjdHMlNUNQZXJzb25hbFBvcnRhbCU1Q3NyYyU1Q2FwcCZhcHBQYXRocz0lMkZhcGklMkYobWFrZSklMkZzY2VuYXJpb3MlMkZvcGVuQUlBc3Npc3RhbnQlMkZyb3V0ZSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCE_3D_nextConfigOutput_preferredRegion_middlewareConfig_e30_3D_", "edge_route_module_wrapper", "a", "wrap", "__WEBPACK_DEFAULT_EXPORT__", "PrismaClient"], "sourceRoot": ""}