{"version": 3, "file": "app/api/stripe/create-portal/route.js", "mappings": "oFAAAA,CAAAA,EAAAC,OAAA,CAAAC,QAAA,0CCAAF,CAAAA,EAAAC,OAAA,CAAAC,QAAA,0aGMO,IAAMC,EAAU,gBACVC,EAAU,OAEhB,eAAeC,EAAKC,CAAgB,EACzC,GAAM,CAAEC,OAAAA,CAAM,CAAE,CAA8BC,CAAAA,EAAAA,EAAAA,CAAAA,IAE9C,GAAG,CAACD,EACF,OAAOE,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAO,eACT,EACA,CACEC,OAAQ,GACV,GAIJ,GAAI,CAEF,IAAMC,EAAe,MAAMC,EAAAA,CAAMA,CAACD,YAAY,CAACE,SAAS,CAAC,CACvDC,MAAO,CACLC,cAAeV,CACjB,CACF,GAEA,GAAI,CAACM,GAAcK,mBACjB,OAAOT,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MACE,8DACJ,EACA,CACEC,OAAQ,GACV,GAIJ,IAAMO,EAAY,CAAC,EAAEb,EAAIc,OAAO,CAACC,GAAG,CAAC,UAAU,UAAU,CAAC,CAEpDC,EAAkB,MAAMC,EAAAA,CAAaA,CAACC,oBAAoB,CAACX,EAAaK,kBAAkB,CAAEC,GAElG,OAAOV,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACvBe,IAAKH,CACP,EACF,CAAE,MAAOI,EAAG,CAGV,OAFAC,QAAQhB,KAAK,CAACe,GAEPjB,EAAAA,EAAYA,CAACC,IAAI,CACtB,CACEC,MAAOe,GAAGE,OACZ,EACA,CACEhB,OAAQ,GACV,EAEJ,CAEF,CCxDA,IAAAiB,EAAA,IAAwBC,EAAAC,mBAAmB,EAC3CC,WAAA,CACAC,KAAcC,EAAAC,CAAS,CAAAC,SAAA,CACvBC,KAAA,kCACAC,SAAA,4BACAC,SAAA,QACAC,WAAA,oCACA,EACAC,iBAAA,+EACAC,iBAVA,GAWAC,SAAYC,CACZ,GAIA,CAAQC,oBAAAA,CAAA,CAAAC,6BAAAA,CAAA,CAAAC,YAAAA,CAAA,EAAiElB,EACzEmB,EAAA,kCACA,SAAAC,IACA,MAAW,GAAAC,EAAAC,EAAA,EAAW,CACtBJ,YAAAA,EACAD,6BAAAA,CACA,EACA,CC1BO,IAAAM,EAAqBC,EAC5BC,EAAeC,EAAAC,CAAsB,CAAAC,IAAA,CAAM5B,iDCA3C,IAAA6B,EAFe,GAAIC,UAAAA,YAAYA,4DCA/B,OAAMC,EAMJ,MAAapC,qBAAqBqC,CAAkB,CAAE1C,CAAiB,CAAmB,CAKxF,MAAO2C,CAJe,MAAM,IAAI,CAACC,MAAM,CAACC,aAAa,CAACC,QAAQ,CAACC,MAAM,CAAC,CACpEC,SAAUN,EACVO,WAAYjD,CACd,IACqBM,GAAG,CAI1B,MAAa4C,oBAAoBC,CAAiB,CAAE,CACpD,GAAI,CAIJ,OAHgB,MAAM,IAAI,CAACP,MAAM,CAACQ,QAAQ,CAACN,QAAQ,CAACO,QAAQ,CAACF,EAAW,CACtEG,OAAQ,CAAC,aAAa,EAG1B,CAAE,MAAO/C,EAAG,CAEV,OADAC,QAAQhB,KAAK,CAACe,GACP,IACN,CACD,CAEA,MAAagD,gBAAgBC,CAAa,CAAE,CAC1C,OAAO,IAAI,CAACZ,MAAM,CAACa,aAAa,CAACJ,QAAQ,CAACG,EAC5C,CAEA,MAAaE,mBAAmBC,CAAY,CAAE,CAC5C,OAAO,IAAI,CAACf,MAAM,CAACQ,QAAQ,CAACN,QAAQ,CAACO,QAAQ,CAACM,EAChD,oBAhCQf,MAAAA,CAAS,IAAIgB,EAAAA,CAAMA,CAACC,QAAQC,GAAG,CAACC,iBAAiB,CAAE,CACzDC,WAAY,EACd,GAgCF,CAEO,IAAM5D,EAAgB,IAAIqC", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/ignored|C:\\Projects\\PersonalPortal\\node_modules\\crypto-js|crypto", "webpack://_N_E/ignored|C:\\Projects\\PersonalPortal\\node_modules\\object-inspect|./util.inspect", "webpack://_N_E/./src/app/api/stripe/create-portal/route.ts", "webpack://_N_E/./src/app/api/stripe/create-portal/route.ts?eede", "webpack://_N_E/?978c", "webpack://_N_E/./src/libs/prisma.ts", "webpack://_N_E/./src/libs/stripe.ts"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "/* (ignored) */", "/* (ignored) */", "import { NextResponse, NextRequest } from \"next/server\";\r\nimport { stripeService } from \"@/libs/stripe\";\r\nimport prisma from \"@/libs/prisma\";\r\nimport { auth } from '@clerk/nextjs/server'\r\n\r\n// Mark this route as dynamic\r\nexport const dynamic = 'force-dynamic'\r\nexport const runtime = 'edge'\r\n\r\nexport async function POST(req: NextRequest) {\r\n  const { userId }: { userId: string | null } = auth()\r\n\r\n  if(!userId) {\r\n    return NextResponse.json(\r\n      {\r\n        error: \"Not signed in\",\r\n      },\r\n      {\r\n        status: 401,\r\n      }\r\n    );\r\n  }\r\n  \r\n  try {\r\n\r\n    const subscription = await prisma.subscription.findFirst({\r\n      where: {\r\n        user_clerk_id: userId,\r\n      },\r\n    });\r\n\r\n    if (!subscription?.stripe_customer_id) {\r\n      return NextResponse.json(\r\n        {\r\n          error:\r\n            \"You don't have a billing account yet. Make a purchase first.\",\r\n        },\r\n        {\r\n          status: 400,\r\n        }\r\n      );\r\n    } \r\n\r\n    const returnUrl = `${req.headers.get('origin')}/dashboard`\r\n\r\n    const stripePortalUrl = await stripeService.createCustomerPortal(subscription.stripe_customer_id, returnUrl);\r\n\r\n    return NextResponse.json({\r\n      url: stripePortalUrl,\r\n    });\r\n  } catch (e) {\r\n    console.error(e);\r\n\r\n    return NextResponse.json(\r\n      {\r\n        error: e?.message,\r\n      },\r\n      {\r\n        status: 500,\r\n      }\r\n    );\r\n  }\r\n\r\n}\r\n", "import { AppRouteRouteModule } from \"next/dist/server/future/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/future/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Projects\\\\PersonalPortal\\\\src\\\\app\\\\api\\\\stripe\\\\create-portal\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/stripe/create-portal/route\",\n        pathname: \"/api/stripe/create-portal\",\n        filename: \"route\",\n        bundlePath: \"app/api/stripe/create-portal/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\PersonalPortal\\\\src\\\\app\\\\api\\\\stripe\\\\create-portal\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/stripe/create-portal/route\";\nfunction patchFetch() {\n    return _patchFetch({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\nexport { routeModule, requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, originalPathname, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "import { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fstripe%2Fcreate-portal%2Froute&page=%2Fapi%2Fstripe%2Fcreate-portal%2Froute&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcreate-portal%2Froute.ts&appDir=C%3A%5CProjects%5CPersonalPortal%5Csrc%5Capp&appPaths=%2Fapi%2Fstripe%2Fcreate-portal%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/stripe/create-portal/route.ts?__next_edge_ssr_entry__\";\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule);\n\n//# sourceMappingURL=edge-app-route.js.map", "import { PrismaClient } from \"@prisma/client\";\r\n\r\nconst prisma = new PrismaClient();\r\n\r\nexport default prisma;\r\n", "import Stripe from \"stripe\";\r\n\r\nclass StripeService {\r\n  private stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {\r\n    typescript: true,\r\n  });\r\n\r\n  // This is used to create Customer Portal sessions, so users can manage their subscriptions (payment methods, cancel, etc..)\r\n  public async createCustomerPortal(customerId: string, returnUrl: string): Promise<string> {\r\n    const portalSession = await this.stripe.billingPortal.sessions.create({\r\n      customer: customerId,\r\n      return_url: returnUrl,\r\n    });\r\n    return portalSession.url;\r\n  }\r\n\r\n// This is used to get the uesr checkout session and populate the data so we get the planId the user subscribed to\r\n  public async findCheckoutSession(sessionId: string) {\r\n  try {\r\n  const session = await this.stripe.checkout.sessions.retrieve(sessionId, {\r\n    expand: [\"line_items\"],\r\n  });\r\n  return session;\r\n} catch (e) {\r\n  console.error(e);\r\n  return null;\r\n   }\r\n  }\r\n\r\n  public async getSubscription(subId: string) {\r\n    return this.stripe.subscriptions.retrieve(subId)\r\n  }\r\n\r\n  public async getCheckoutSession(csId: string) {\r\n    return this.stripe.checkout.sessions.retrieve(csId)\r\n  }\r\n\r\n}\r\n\r\nexport const stripeService = new StripeService()"], "names": ["module", "exports", "require", "dynamic", "runtime", "POST", "req", "userId", "auth", "NextResponse", "json", "error", "status", "subscription", "prisma", "<PERSON><PERSON><PERSON><PERSON>", "where", "user_clerk_id", "stripe_customer_id", "returnUrl", "headers", "get", "stripePortalUrl", "stripeService", "createCustomerPortal", "url", "e", "console", "message", "routeModule", "module_compiled", "AppRouteRouteModule", "definition", "kind", "route_kind", "x", "APP_ROUTE", "page", "pathname", "filename", "bundlePath", "resolvedPagePath", "nextConfigOutput", "userland", "route_namespaceObject", "requestAsyncStorage", "staticGenerationAsyncStorage", "serverHooks", "originalPathname", "patchFetch", "patch_fetch", "XH", "ComponentMod", "route_next_edge_ssr_entry_namespaceObject", "next_edge_app_route_loaderabsolutePagePath_private_next_app_dir_2Fapi_2Fstripe_2Fcreate_portal_2Froute_ts_page_2Fapi_2Fstripe_2Fcreate_portal_2Froute_appDirLoader_bmV4dC1hcHAtbG9hZGVyP25hbWU9YXBwJTJGYXBpJTJGc3RyaXBlJTJGY3JlYXRlLXBvcnRhbCUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGc3RyaXBlJTJGY3JlYXRlLXBvcnRhbCUyRnJvdXRlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGc3RyaXBlJTJGY3JlYXRlLXBvcnRhbCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDUHJvamVjdHMlNUNQZXJzb25hbFBvcnRhbCU1Q3NyYyU1Q2FwcCZhcHBQYXRocz0lMkZhcGklMkZzdHJpcGUlMkZjcmVhdGUtcG9ydGFsJTJGcm91dGUmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0Qh_nextConfigOutput_preferredRegion_middlewareConfig_e30_3D_", "edge_route_module_wrapper", "a", "wrap", "__WEBPACK_DEFAULT_EXPORT__", "PrismaClient", "StripeService", "customerId", "portalSession", "stripe", "billingPortal", "sessions", "create", "customer", "return_url", "findCheckoutSession", "sessionId", "checkout", "retrieve", "expand", "getSubscription", "subId", "subscriptions", "getCheckoutSession", "csId", "Stripe", "process", "env", "STRIPE_SECRET_KEY", "typescript"], "sourceRoot": ""}