﻿using MicroSaasWebApi.Properties.PXW;
using MicroSaasWebApi.Services.PXW.Interface;
using System.Text.Json;

namespace MicroSaasWebApi.Services.PXW
{
    public class LoggerService : ILoggerService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<LoggerService> _logger;

        public LoggerService(IHttpContextAccessor httpContextAccessor, ILogger<LoggerService> logger)
        {
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
        }

        public async Task SetResponseAndLogAsync(int statusCode, string message, Exception? e = null)
        {
            try
            {
                var responseMessage = "An error occurred.";
                if (e != null)
                {
                    // Log the detailed error internally
                    _logger.LogError("Error occurred: {e}", e);

                    // Do not send detailed error information to the client
                    responseMessage += " Please contact support if the problem persists.";
                }
                else
                {
                    // Log the information
                    _logger.LogInformation("Response: {message}, StatusCode = {statusCode}", message, statusCode);

                    responseMessage = message;
                }

                var responseObj = new
                {
                    Message = responseMessage,
                    Type = e?.GetType().Name,
                    Detail = e?.StackTrace,
                    StatusCode = statusCode
                };
                var responseJson = JsonSerializer.Serialize(responseObj);

                if (_httpContextAccessor?.HttpContext?.Response != null)
                {
                    _httpContextAccessor.HttpContext.Response.StatusCode = statusCode;
                    _httpContextAccessor.HttpContext.Response.ContentType = "application/json";
                    await _httpContextAccessor.HttpContext.Response.WriteAsync(responseJson);
                }
            }
            catch (Exception ex)
            {
                // Log the exception
                _logger.LogError("Error in SetResponseAndLogAsync: {ex}", ex);

                // Create a generic error message
                var errorMessage = "An internal error occurred.";
                var errorObj = new { Message = errorMessage, StatusCode = StatusCodes.Status500InternalServerError };
                var errorJson = JsonSerializer.Serialize(errorObj);

                // Write the error message to the response
                if (_httpContextAccessor?.HttpContext?.Response != null)
                {
                    _httpContextAccessor.HttpContext.Response.StatusCode = StatusCodes.Status500InternalServerError;
                    _httpContextAccessor.HttpContext.Response.ContentType = "application/json";
                    await _httpContextAccessor.HttpContext.Response.WriteAsync(errorJson);
                }
            }
        }

        public async Task LogInformationAndRequestAsync(string message)
        {
            var requestBody = _httpContextAccessor.HttpContext?.Items[Constants.RequestBodyText];
            var logObject = new { Message = message, RequestBody = requestBody };

            await Task.Run(() =>
            {
                var logJson = JsonSerializer.Serialize(logObject);
                _logger.LogInformation(logJson);
            });
        }


        public async Task LogInformationAsync(string message)
        {
            var logObject = new { Message = message };

            await Task.Run(() =>
            {
                var logJson = JsonSerializer.Serialize(logObject);
                _logger.LogInformation(logJson);
            });
        }

        public async Task LogRequestInformationAsync()
        {
            var logObject = new
            {
                RequestMethod = _httpContextAccessor?.HttpContext?.Request.Method,
                RequestUrl = _httpContextAccessor?.HttpContext?.Request.Path
            };
            await Task.Run(() =>
            {
                var logJson = JsonSerializer.Serialize(logObject);
                _logger.LogInformation(logJson);
            });
        }

        public async Task LogErrorAsync(Exception exception, string message)
        {
            await Task.Run(() =>
            {
                _logger.LogError(exception, "An error occurred: {message}. Exception: {exception}", message, exception);
            });
        }

        public async Task LogTraceAsync(string message)
        {
            await Task.Run(() =>
            {
                _logger.LogError("Log Trace: {message} : ", message);
            });
        }

        // Send the log data to DataDog using the StatsD client
        //public async Task LogInformationToDataDogAsync(string logJson)
        //{            
        //    await Task.Run(() =>
        //    {                
        //        // Send the log data to DataDog using the StatsD client
        //        var statsdConfig = new StatsdConfig
        //        {
        //            StatsdServerName = "your-statsd-server",
        //            StatsdPort = 8125
        //        };

        //        var statsdClient = new StatsdClient(statsdConfig);
        //        statsdClient.Log(logJson);
        //    });
        //}

    }

}
