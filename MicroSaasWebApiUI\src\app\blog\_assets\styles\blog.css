/* Blog styles */
.blog-content {
  max-width: 100%;
  margin: 0 auto;
  background-color: white;
  color: #333;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  /* Add border for dark mode */
  border: 1px solid transparent;
}

.blog-content h1,
.blog-content h2,
.blog-content h3,
.blog-content h4,
.blog-content h5,
.blog-content h6 {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
  color: #333;
}

.blog-content p {
  margin-bottom: 1.25rem;
  line-height: 1.7;
}

.blog-content a {
  color: #3182ce;
  text-decoration: none;
}

.blog-content a:hover {
  text-decoration: underline;
}

.blog-content ul,
.blog-content ol {
  margin-left: 1.5rem;
  margin-bottom: 1.25rem;
}

.blog-content li {
  margin-bottom: 0.5rem;
}

.blog-content img {
  max-width: 100%;
  height: auto;
  margin: 1.5rem 0;
  border-radius: 4px;
}

.blog-content blockquote {
  border-left: 4px solid #e2e8f0;
  padding-left: 1rem;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
  color: #4a5568;
}

/* Code block styling */
.blog-content pre {
  border-radius: 4px;
  padding: 1rem;
  overflow-x: auto;
  margin: 1.5rem 0;
  position: relative;
}

.blog-content code {
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  font-size: 0.9rem;
  padding: 0;
  border-radius: 0;
  border: none;
  margin: 0 0.1rem;
  display: inline;
  line-height: inherit;
  vertical-align: baseline;
}

/* Special styling for inline code in paragraphs */
.blog-content p code,
p code {
  padding: 0 !important;
  margin: 0 0.1rem !important;
  border: none !important;
  font-size: 0.85em !important;
  line-height: inherit !important;
  white-space: normal !important;
  background-color: transparent !important;
  display: inline !important;
  vertical-align: baseline !important;
}

/* Ensure all code blocks have proper positioning */
.blog-content pre,
.wp-block-code,
pre.wp-block-code,
.blog-content .wp-block-code,
pre {
  position: relative !important;
  margin: 1rem 0 !important;
  padding: 1rem !important;
  font-family: monospace !important;
  background-color: transparent !important;
  border: none !important;
}

/* Remove syntax highlighting */
.blog-content pre,
.wp-block-code,
.code-block,
.code-snippet {
  background-color: transparent !important;
  color: #2d3748 !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 0.375rem !important;
}

/* Remove syntax highlighting for all code elements */
code span,
pre span,
.token,
.hljs-keyword,
.hljs-string,
.hljs-number,
.hljs-comment,
.hljs-function,
.hljs-operator,
.hljs-punctuation,
[class*="language-"] *,
.prism *,
.highlight *,
.syntax-highlighter * {
  color: inherit !important;
  background-color: transparent !important;
  font-style: normal !important;
  font-weight: normal !important;
}

.blog-content pre code {
  padding: 0;
  border-radius: 0;
  display: block;
}

/* Style for detected code blocks */
.detected-code-block {
  display: block !important;
  padding: 1rem !important;
  background-color: #f1f5f9 !important;
  color: #2d3748 !important;
  border-radius: 0.375rem !important;
  border: 1px solid #e2e8f0 !important;
  white-space: pre-wrap !important;
  word-break: break-word !important;
  overflow-x: auto !important;
}

html.dark .detected-code-block {
  background-color: transparent !important;
  color: #e2e8f0 !important;
  border: none !important;
}

/* Copy button styles removed as requested */

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  .blog-content {
    background-color: #1E232C;
    color: #e2e8f0;
    border-color: #373C53;
  }

  /* Dark mode code blocks */
  .blog-content pre,
  pre,
  .wp-block-code,
  .code-block,
  .code-snippet {
    background-color: transparent !important;
    color: #e2e8f0 !important;
    border-color: #373C53 !important;
    padding: 1rem !important;
  }

  /* Dark mode inline code */
  .blog-content code,
  code {
    background-color: transparent !important;
    color: #e2e8f0 !important;
    border: none !important;
    padding: 0.1rem 0.2rem !important;
    margin: 0 0.1rem !important;
    display: inline-block !important;
    line-height: normal !important;
    vertical-align: middle !important;
  }

  /* Dark mode inline code in paragraphs */
  .blog-content p code,
  p code {
    background-color: transparent !important;
    color: #e2e8f0 !important;
    border: none !important;
    padding: 0 !important;
    margin: 0 0.1rem !important;
    font-size: 0.85em !important;
    line-height: 1.2 !important;
    white-space: nowrap !important;
  }

  .blog-content h1,
  .blog-content h2,
  .blog-content h3,
  .blog-content h4,
  .blog-content h5,
  .blog-content h6 {
    color: #f7fafc;
  }

  .blog-content a {
    color: #63b3ed;
  }

  .blog-content blockquote {
    border-left-color: #4a5568;
  }

  /* Code block styles handled by JavaScript */

  /* Dark mode copy button styles removed as requested */
}

/* AdSense container styling */
.adsense-container,
.adsense-container pre,
.adsense-container code,
pre.adsense-code,
code.adsense-code {
  background-color: transparent !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 auto !important;
  font-family: inherit !important;
  color: inherit !important;
  width: 100% !important;
  max-width: 300px !important;
  text-align: left !important;
  display: block !important;
  overflow: visible !important;
}

/* Horizontal AdSense container styling */
.adsense-container-horizontal,
.adsense-container-horizontal pre,
.adsense-container-horizontal code {
  background-color: transparent !important;
  border: none !important;
  padding: 0 !important;
  margin: 2rem auto !important;
  font-family: inherit !important;
  color: inherit !important;
  width: 100% !important;
  max-width: 100% !important;
  text-align: left !important;
  display: block !important;
  overflow: visible !important;
  align-self: flex-start !important;
}

/* Sidebar ad container */
.sidebar-ad {
  margin-top: 2rem !important;
  position: sticky !important;
  top: 2rem !important;
  z-index: 10 !important;
  width: 100% !important;
  max-width: 300px !important;
  padding: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  overflow: hidden !important;
}

/* Sidebar AdSense container specific styling */
.adsense-container-sidebar {
  width: 300px !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
  position: relative !important;
}

/* Category tree styling */
.category-tree {
  max-height: 600px;
  overflow-y: auto;
}

.category-item {
  margin-bottom: 0.25rem;
}

/* Code block styles */
pre {
    border: none;
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 1rem 0;
    position: relative;
    background: #f6f8fa; /* GitHub light theme background */
    overflow-x: auto;
}

.dark pre {
    background: #0d1117; /* GitHub dark theme background */
}

pre code {
    border: none !important;
    background: none !important;
    padding: 0 !important;
    margin: 0 !important;
    font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
    color: #24292f; /* GitHub light theme text color */
    display: block;
    line-height: 1.5;
    font-size: 85%;
}

.dark pre code {
    color: #c9d1d9; /* GitHub dark theme text color */
}

pre code *,
pre code span {
    border: none !important;
    background: none !important;
}

/* Simple copy button */
.copy-code-button {
    position: absolute !important;
    top: 0.5rem !important;
    right: 0.5rem !important;
    padding: 0.25rem !important;
    background: transparent !important;
    border: none !important;
    cursor: pointer !important;
    z-index: 10 !important;
    opacity: 0.7 !important;
    transition: opacity 0.2s !important;
    color: inherit !important;
}

.copy-code-button:hover {
    opacity: 1 !important;
    background: rgba(175, 184, 193, 0.2) !important;
}

.dark .copy-code-button:hover {
    background: rgba(110, 118, 129, 0.4) !important;
}

.copy-code-button svg {
    width: 16px !important;
    height: 16px !important;
    stroke: currentColor !important;
    fill: none !important;
    display: block !important;
}

