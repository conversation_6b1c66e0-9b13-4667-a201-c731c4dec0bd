using MicroSaasWebApi.Models.Payment;

namespace MicroSaasWebApi.Services.MICROSAAS.Interface
{
    /// <summary>
    /// Payment service interface for MicroSaaS template with Clerk integration
    /// </summary>
    public interface IPaymentService
    {
        /// <summary>
        /// Get all available subscription plans
        /// </summary>
        /// <returns>List of subscription plans</returns>
        Task<IEnumerable<SubscriptionPlan>> GetSubscriptionPlansAsync();

        /// <summary>
        /// Create a new subscription for user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="request">Subscription creation request</param>
        /// <returns>Payment response</returns>
        Task<PaymentResponse> CreateSubscriptionAsync(string userId, CreateSubscriptionRequest request);

        /// <summary>
        /// Get user's subscription status
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Subscription status or null if no subscription</returns>
        Task<SubscriptionStatus?> GetSubscriptionStatusAsync(string userId);

        /// <summary>
        /// Cancel user's subscription
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if cancellation successful</returns>
        Task<bool> CancelSubscriptionAsync(string userId);

        /// <summary>
        /// Create a one-time payment
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="request">One-time payment request</param>
        /// <returns>Payment response</returns>
        Task<PaymentResponse> CreateOneTimePaymentAsync(string userId, OneTimePaymentRequest request);

        /// <summary>
        /// Get user's payment history
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>List of invoices/payments</returns>
        Task<IEnumerable<Invoice>> GetPaymentHistoryAsync(string userId);

        /// <summary>
        /// Process webhook events from Clerk
        /// </summary>
        /// <param name="webhookEvent">Webhook event data</param>
        /// <returns>Task</returns>
        Task ProcessWebhookAsync(ClerkWebhookEvent webhookEvent);

        /// <summary>
        /// Update user's default payment method
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="paymentMethodId">Payment method ID</param>
        /// <returns>True if update successful</returns>
        Task<bool> UpdatePaymentMethodAsync(string userId, string paymentMethodId);

        /// <summary>
        /// Get subscription plan by ID
        /// </summary>
        /// <param name="planId">Plan ID</param>
        /// <returns>Subscription plan or null if not found</returns>
        Task<SubscriptionPlan?> GetSubscriptionPlanAsync(string planId);

        /// <summary>
        /// Update subscription plan
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="newPlanId">New plan ID</param>
        /// <returns>Payment response</returns>
        Task<PaymentResponse> UpdateSubscriptionPlanAsync(string userId, string newPlanId);

        /// <summary>
        /// Check if user has active subscription
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if user has active subscription</returns>
        Task<bool> HasActiveSubscriptionAsync(string userId);

        /// <summary>
        /// Get users with expiring trials
        /// </summary>
        /// <param name="daysBeforeExpiry">Days before trial expiry</param>
        /// <returns>List of users with expiring trials</returns>
        Task<IEnumerable<string>> GetUsersWithExpiringTrialsAsync(int daysBeforeExpiry = 3);

        /// <summary>
        /// Get failed payments that need retry
        /// </summary>
        /// <returns>List of failed payment user IDs</returns>
        Task<IEnumerable<string>> GetFailedPaymentsAsync();

        /// <summary>
        /// Retry failed payment
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Payment response</returns>
        Task<PaymentResponse> RetryFailedPaymentAsync(string userId);

        /// <summary>
        /// Generate invoice for subscription
        /// </summary>
        /// <param name="subscriptionId">Subscription ID</param>
        /// <returns>Invoice</returns>
        Task<Invoice> GenerateInvoiceAsync(string subscriptionId);

        /// <summary>
        /// Send payment receipt email
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="paymentId">Payment ID</param>
        /// <returns>Task</returns>
        Task SendPaymentReceiptAsync(string userId, string paymentId);
    }
}
