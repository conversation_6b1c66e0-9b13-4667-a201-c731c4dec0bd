parameters:
- name: environment
  type: string
- name: client
  type: string
- name: dockerRegistryServiceConnection
  type: string
- name: imageRepository
  type: string
- name: containerRegistry
  type: string
- name: dockerfilePath
  type: string
- name: tag
  type: string
- name: poolName
  type: string
- name: azureSubscription
  type: string
- name: webAppName
  type: string
- name: appSettingsEnvironment
  type: string
- name: deployToAppServiceEnvOrSlot
  type: boolean
  default: true
  displayName: 'Deploy to App Service Environment'

variables:
  appName: "${{ parameters.client }}-pxwreact-${{ parameters.environment }}"
  appSettingsFileName: "appsettings_${{ parameters.client }}_${{ parameters.environment }}.json"
  #clientConfigTemplate: "azure_pipelines/${{ parameters.client }}/azure_pipeline_${{ parameters.client }}_${{ parameters.environment }}.yml"
  dynamicTag: $(Build.BuildId)
  dynamicDescription: "Pipeline run for Client- ${{ parameters.client }}, Environment- ${{ parameters.environment }}, Tag- $(dynamicTag)"

stages:
- stage: Initialize
  jobs:
  - job: SetupDescription
    pool:
      name: ${{ parameters.poolName }}
    steps:
    - script: |
        echo "Selected Client: ${{ parameters.client }}"
        echo "Selected Environment: ${{ parameters.environment }}"
        echo "Current Project Path: $(System.DefaultWorkingDirectory)"
        echo "$(dynamicDescription)"
      displayName: "Print Parameters and Project Path"
#   displayName: "SonarQube Scan"
#   jobs:
#   - job: SonarQubeScan
#     displayName: "SonarQube Scan"
#     pool:
#       vmImage: ubuntu-latest
#     steps:
#     - task: JavaToolInstaller@0
#       inputs:
#         versionSpec: '17' # Specify the desired Java version here
#         jdkArchitectureOption: 'x64'
#         jdkSourceOption: 'PreInstalled'
#     - task: SonarQubePrepare@5
#       inputs:
#         SonarQube: 'SonarQubeSC'
#         scannerMode: 'MSBuild'
#         projectKey: 'PXW-Cloud-Apps_MicroSaasWebApi_AY0Tsci28T8_V5xlCNmk'
#         projectName: 'MicroSaasWebApi'
#     - task: DotNetCoreCLI@2
#       inputs:
#         command: 'restore'
#         projects: '**/*MicroSaasWebApi/*.csproj'
#     - task: DotNetCoreCLI@2
#       inputs:
#         command: 'build'
#         projects: '**/*MicroSaasWebApi/*.csproj'
#     - task: SonarQubeAnalyze@5
#       inputs:
#         jdkVersion: 'JAVA_HOME'
#     - task: SonarQubePublish@5
#       inputs:
#         pollingTimeoutSec: "300"
#     - task: DotNetCoreCLI@2
#       inputs:
#         command: 'test'
#         projects: '**/*MicroSaasWebApi.Tests.Unit/*.csproj'
#         arguments: '--configuration Release --collect "Code coverage"'

- stage: Build
  # - stage: SonarQubeScan
  displayName: Build and push stage
  jobs:
  - job: Build
    displayName: Build
    pool:
      name: ${{ parameters.poolName }}
    steps:
    - bash: |
        echo "Application Name Environment: ${{ parameters.client }}-webapi-${{ parameters.environment }}"
        echo "Application Settings FileName to be Copied: appsettings_${{ parameters.client }}_${{ parameters.environment }}.json"
        echo "Current Project Path: $(System.DefaultWorkingDirectory)"
      displayName: "Print Variables."

    - script: cp MicroSaasWebApi/Assets/${{ parameters.client }}/AppSettings/${{ variables.appSettingsFileName }} MicroSaasWebApi/appsettings.json
      displayName: "Setup Client-Specific App Settings"
    - task: Docker@2
      displayName: Build and push an image to container registry
      inputs:
        containerRegistry: "${{ parameters.dockerRegistryServiceConnection }}"
        repository: "${{ parameters.imageRepository }}"
        command: "buildAndPush"
        Dockerfile: "${{ parameters.dockerfilePath }}"
        tags: "${{ parameters.tag }}"
####PX-5317          
  - job: approveDeploy 
    displayName: Approve_prodDeploy
    dependsOn: Build
    pool: server
    steps:
    - task: ManualValidation@1
      timeoutInMinutes: 1440 # task times out in 1 day
      inputs:
        notifyUsers: ''   
        instructions: 'Please validate the prod build and approve the prod deploy '
        onTimeout: 'reject'       

  - job: Deploy
    displayName: Deploy
    dependsOn: 
    - Build
    - approveDeploy
    pool:
      name: ${{ parameters.poolName }}
    steps:
####PX-5317         
    - task: AzureWebAppContainer@1
      displayName: "Deploy to ${{ parameters.environment }}"
      inputs:
        azureSubscription: ${{ parameters.azureSubscription }}
        appName: ${{ parameters.webAppName }}
        containers: "${{ parameters.containerRegistry }}/${{ parameters.imageRepository }}:${{ parameters.tag }}"
        appSettings: "${{ parameters.appSettingsEnvironment }}"
        # Conditional parameters based on deployToAppServiceEnvOrSlot
        ${{ if eq(parameters.deployToAppServiceEnvOrSlot, true) }}:
          appServiceEnvironmentName: ${{ parameters.webAppName }}
        ${{ if eq(parameters.deployToAppServiceEnvOrSlot, false) }}:
          slotName: 'YourSlotName' # Specify your slot name here
# - script: |
#     cd MicroSaasWebApi.Test.Unit
#     dotnet test /p:CollectCoverage=true /p:CoverletOutputFormat=opencover /p:CoverletOutput=./coverage-reports/
#   displayName: "Run Tests and Collect Coverage"
# - task: PublishCodeCoverageResults@1
#   displayName: "Publish Code Coverage"
#   inputs:
#     codeCoverageTool: "Cobertura"
#     summaryFileLocation: "$(System.DefaultWorkingDirectory)/MicroSaasWebApi.Test.Unit/coverage-reports/coverage.opencover.xml"
