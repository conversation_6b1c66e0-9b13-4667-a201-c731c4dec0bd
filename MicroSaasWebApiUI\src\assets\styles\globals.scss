@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
}

/* Force light theme background color */
:root {
  background-color: white;
  color: black;
}

/* Force dark theme background color */
.dark {
  background-color: #010814;
  color: white;
}

/* Ensure consistent background colors */
body {
  background-color: white !important;
  color: black;
}

.dark body {
  background-color: #010814 !important;
  color: white;
}

/* Ensure consistent background for AboutMeJP component */
.bg-background {
  background-color: white !important;
}

.dark .bg-background {
  background-color: #010814 !important;
}

// .lucide {
//     stroke: white;
//     fill: white;
//     width: 24px;
//     height: 24px;
// }

::-webkit-scrollbar {
	width: 0px;
}

.blocks-class a {
	color: #0000ee !important;
	text-decoration: underline;
}

.blocks-class a:hover {
	color: darkblue !important;
}

.video-class iframe {
	max-width: 1050px;
	width: 100%;
	height: 550px;
	border-radius: 16px;
	margin: 0 auto;
}

@media (max-width: 1023px) {
	.video-class iframe {
		height: 450px;
		max-width: 100%;
	}
}

@media (max-width: 767px) {
	.video-class iframe {
		height: 400px;
		max-width: 100%;
	}
}

@media (max-width: 600px) {
	.video-class iframe {
		height: 330px;
		max-width: 100%;
	}
}

#theme-toggle:checked {
	background-color: #373c53;
}

#theme-toggle:checked ~ span:last-child {
	--tw-translate-x: 1.75rem;
}

.close-div button {
	color: #fff;
	display: none;
}

.glass {
	background: #121a32;
	background: linear-gradient(
		155deg,
		rgba(5, 0, 107, 0) 0%,
		rgba(0, 50, 69, 0.18) 57%,
		rgba(0, 30, 54, 0.24) 100%
	);
}
