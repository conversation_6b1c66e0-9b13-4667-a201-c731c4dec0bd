{"version": 3, "file": "edge-chunks/436.js", "mappings": "qLEkBA,SAAAA,EAAAC,CAAA,EACA,IAAAC,EAAAC,KAAAC,SAAA,CAAAH,GAEAI,EAAAC,IADAC,cACAC,MAAA,CAAAN,GACA,OAASO,EAAAC,EAAS,CAAAN,SAAA,CAAAC,EAAA,CAAsBM,IAAA,IACxC,CACA,eAAAC,EAAAC,CAAA,CAAAC,CAAA,CAAAC,CAAA,EACA,IAAAA,EAAAC,SAAA,CACA,sCAEA,IAAAV,EAAA,IAAAC,YACAS,EAAoB,GAAAP,EAAAQ,EAAA,EAAkBF,EAAAC,SAAA,EACtC,IAAAA,EACA,OACAE,OAAA,KAAmBC,EAAAC,EAAY,0BAA0BL,EAAAC,SAAA,CAAkB,KAG3E,IAAAK,EAAA,MAA0B,GAAAZ,EAAAa,EAAA,EAASR,EAAAE,EAAA,QACnCO,EAAAR,EAAAQ,MAAA,GAAqCC,IAAA,MACrCD,CAAAA,EAAAE,GAAA,CAAAV,EAAAC,SAAA,CACAH,EAAAa,GAAA,CAAAC,KAAAC,KAAA,CAAAC,KAAAC,GAAA,QACA,IAAAC,EAAA/B,EAAAuB,GACAS,EAAAhC,EAAAa,GACAoB,EAAA,GAAuBF,EAAc,GAAGC,EAAe,EACvD,IACA,IAAAE,EAAA,MAA4BzB,EAAA0B,EAAe,CAAAC,MAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAtB,EAAAK,EAAAf,EAAAE,MAAA,CAAAyB,IAE3C,OAAaM,KADb,GAAgCN,EAAU,GAAGxB,EAAAC,EAAS,CAAAN,SAAA,KAAAoC,WAAAN,GAAA,CAAwCvB,IAAA,KAAc,EAE5G,CAAI,MAAA8B,EAAA,CACJ,OAAavB,OAAA,KAAaC,EAAAC,EAAY,CAAAqB,GAAAC,SAAA,CACtC,CACA,CAGiB,GAAAC,EAAAC,CAAA,EAAiBnC,EAAAoC,EAAS,EAC3C,IAAAC,EAAiB,GAAAH,EAAAI,CAAA,EAAqBtC,EAAAuC,EAAS,EAChC,GAAAL,EAAAC,CAAA,EAAgBhC,GACN,GAAA+B,EAAAC,CAAA,EAAiBnC,EAAAwC,EAAiB,uBCvD3DC,EAAA,IACA,IAAAC,GAAA,iBAAAA,EACA,OAAAA,EAEA,IACA,OAAAA,GAAA,IAAAC,OAAA,kCAA4D,gBAC5D,CAAI,MAAAC,EAAA,CACJ,QACA,CACA,EACAC,EAAA,GACA,CAAAC,MAAAC,OAAA,CAAAC,GAAAA,EAAA,CAAAA,EAAA,EAAAC,GAAA,IACA,iBAAAC,EACAT,EAAAS,GAGAxD,KAAAC,SAAA,CADAwD,OAAAC,WAAA,CAAAD,OAAAE,OAAA,CAAAH,GAAAD,GAAA,GAAAK,EAAAC,EAAA,IAAAD,EAAAb,EAAAc,GAAA,GACA,SACGC,IAAA,OCfHC,EAAA,CAAAC,EAAAC,IAAA,KACA,IAAAN,EAAA,GACAO,EAAA,GACA,OACAC,OAAA,KACAD,EAAA,EACA,EACAE,MAAA,IAAAC,KACAH,GACAP,EAAAW,IAAA,CAAAD,EAAAd,GAAA,uBAAAgB,EAAAA,IAAAA,GAEA,EACAC,OAAA,KACA,GAAAN,EAAA,CAEA,QAAAO,KADAC,QAAAD,GAAA,CAuCA,uBAvCAT,EAuCqC,IAtCrCL,GAAA,CACA,IAAAgB,EAAAV,EAAAQ,GACAE,EAAAA,EAAAC,KAAA,OAAArB,GAAA,SAAsDsB,EAAE,GAAAf,IAAA,OACxDgB,QAAAC,GAAA,CAAAC,MAAA,EACAL,CAAAA,EAAAM,SAuCAjC,CAAA,CAAAkC,CAAA,EACA,IAAA/E,EAAA,IAAAC,YACA+E,EAAA,IAAAC,YAAA,SAEAC,EAAAC,EADAjF,MAAA,CAAA2C,GACAuC,KAAA,GA3CA,MA4CA,OAAAJ,EAAAK,MAAA,CAAAH,GAAApC,OAAA,cACA,EA7CA0B,EAAA,IAEAD,QAAAD,GAAA,CAAAE,EACA,CACAD,QAAAD,GAAA,CAiCA,qBAjCAT,gCAiCsEyB,EAAAC,EAAe,GAAC,CAhCtF,CACA,CACA,CACA,EACAC,EAAA,CAAAC,EAAAC,IACA,IAAAxB,KAEA,IAAAyB,EAAAC,CADA,iBAAAH,EAAA7B,EAAA6B,EAAqGzC,GAAYyC,CAAA,IAEjHI,EAAAH,EAAAC,GACA,IACA,IAAAG,EAAAD,KAAA3B,GACA,oBAAA4B,GAAA,SAAAA,GAAA,mBAAAA,EAAAC,IAAA,CACA,OAAAD,EAAAC,IAAA,KACAJ,EAAAtB,MAAA,GACA2B,IACSC,KAAA,KAET,MADAN,EAAAtB,MAAA,GACA6B,CACA,GAGA,OADAP,EAAAtB,MAAA,GACAyB,CACA,CAAM,MAAAI,EAAA,CAEN,MADAP,EAAAtB,MAAA,GACA6B,CACA,CACA,0BC9CA,IAAAC,EAAA,EACAC,oBAAAA,CAAA,CACAC,gBAAAA,CAAA,CACC,GAAKb,EAAUa,EAAA,GAChB,CAAAC,EAAAC,KACgC,SAAxB,GAAAC,EAAAC,EAAA,EAASH,EAAMI,EAAAC,EAAS,CAAAC,OAAA,CAAAC,WAAA,GAChClB,EAAA3B,MAAA,GAEA,IAAA8C,EAAsB,GAAAN,EAAAO,EAAA,EAAqBT,EAAA,aAC3CU,EAA0B,GAAAR,EAAAO,EAAA,EAAqBT,EAAA,iBAC/CW,EAAwB,GAAAT,EAAAO,EAAA,EAAqBT,EAAA,eAC7CY,EAAuB,GAAAV,EAAAO,EAAA,EAAqBT,EAAA,cAC5Ca,EAAuB,GAAAX,EAAAO,EAAA,EAAqBT,EAAA,cAE5C,GADAX,EAAA1B,KAAA,kBAAoCkD,WAAAA,EAAAF,YAAAA,EAAAC,WAAAA,CAAA,GACpC,CAAAC,EACA,YAAAf,GAEA,IAAAgB,EAAiC,GAAAZ,EAAAC,EAAA,EAASH,EAAMI,EAAAC,EAAS,CAAAC,OAAA,CAAAS,gBAAA,EACzDC,EAAiC,GAAAd,EAAAe,EAAA,EAAuBH,GACxD3G,EAAA,CACA0G,WAAAA,EACAK,OAAcC,EAAAC,EAAO,CACrBC,WAAkBF,EAAAG,EAAW,CAC7BX,YAAAA,EACAY,UAAA,CAAAtB,MAAAA,EAAA,OAAAA,EAAAsB,SAAA,GAAAP,EAAAO,SAAA,EAA+FJ,EAAAK,EAAU,CACzGZ,WAAAA,CACA,EAEA,GADAvB,EAAA1B,KAAA,iBAAAxD,GACA0G,IAAuBT,EAAAqB,EAAU,CAAAC,QAAA,EAC3B,GAAAxB,EAAAyB,EAAA,EAAoBnB,EAAArG,EAAAoH,SAAA,CAAAb,GAC1B,IAAAkB,EAAkB1F,EAASsE,GAE3B,OADAnB,EAAA1B,KAAA,aAAAiE,EAAAC,GAAA,CAAAC,IAAA,EACa,GAAA1B,EAAA2B,EAAA,EAAkB5H,EAAAyH,EAAAC,GAAA,CAAAC,IAAA,CAAAF,EAAA3H,OAAA,CAC/B,CACA,MAAW,GAAAmG,EAAA4B,EAAA,EAAmB7H,EAC9B,GAEA0F,EAAA,CACAE,gBAAA,YACAD,oBAAuB,GAAAxF,EAAA2H,EAAA,GACvB,GCpCA,IAAMC,EAAS,CACf5B,QAXA,CACA6B,YAAA,uBACAC,WAAA,oBACAC,aAAA,WAEAC,QAAA,WACAC,WAAA,cAEAC,WAAA,eACA,CAGA,EETAC,EAAA,IACA,IAAUC,iBAAAA,CAAA,CAAAC,WAAAA,CAAA,CAAAC,SAAAA,CAAA,CAAAC,SAAAA,CAAA,CAAAC,QAAAA,CAAA,EAA4D7C,EACtE,UAAArC,KACA,IAAAmF,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAEA,IAAAC,EAAAC,CADA,MAAAP,CAAAA,EAAAnF,CAAA,YAAAmF,EAAAQ,kBAAA,UAAAP,CAAAA,EAAApF,CAAA,YAAAoF,EAAAQ,eAAA,EACA,OAAA5F,CAAA,IACA2F,EAAA,OAAAN,CAAAA,EAAArF,CAAA,YAAAqF,EAAAM,kBAAA,UAAAL,CAAAA,EAAAtF,CAAA,YAAAsF,EAAAK,kBAAA,EACAC,EAAA,OAAAL,CAAAA,EAAAvF,CAAA,YAAAuF,EAAAK,eAAA,UAAAJ,CAAAA,EAAAxF,CAAA,YAAAwF,EAAAI,eAAA,EAUAC,EAAA,IACA,EACAb,EAAAY,GAEAX,WAEA,EAAAa,MAAA,CAGAL,EAGA,mBAAAA,EACA,EAAAV,EAAAgB,GAAA,EACAhB,EAEAc,IAEA,EAAAE,GAAA,CAAAN,GACAV,EAEAc,IAXAd,EAlBA,EACAC,EAAAW,GAEAK,EAAAd,GACAJ,IAEAG,GAwBA,CACA,EACAgB,EAAA,IACA,IAAAd,EAAAC,EACA,QAAAhD,EAAA8D,OAAA,CAAAC,GAAA,CAA2B7B,EAAa5B,OAAA,CAAAgC,OAAA,WAAAS,CAAAA,EAAA/C,EAAA8D,OAAA,CAAAC,GAAA,CAA6C3D,EAAAC,EAAS,CAAAC,OAAA,CAAA0D,MAAA,UAAAjB,EAAAkB,QAAA,8BAAAjB,CAAAA,EAAAhD,EAAA8D,OAAA,CAAAC,GAAA,CAAgG3D,EAAAC,EAAS,CAAAC,OAAA,CAAA4D,WAAA,UAAAlB,EAAAiB,QAAA,4BAAAjE,EAAA8D,OAAA,CAAAC,GAAA,CAAmG7B,EAAa5B,OAAA,CAAAiC,UAAA,EACvT,EACAqB,EAAA,IACA,IAAAb,EACA,MAAA/C,aAAAA,EAAA8D,OAAA,CAAAC,GAAA,CAAyB3D,EAAAC,EAAS,CAAAC,OAAA,CAAA6D,YAAA,GAAAnE,WAAAA,EAAA8D,OAAA,CAAAC,GAAA,CAAyD3D,EAAAC,EAAS,CAAAC,OAAA,CAAA6D,YAAA,UAAApB,CAAAA,EAAA/C,EAAA8D,OAAA,CAAAC,GAAA,CAA8D3D,EAAAC,EAAS,CAAAC,OAAA,CAAA0D,MAAA,UAAAjB,EAAAkB,QAAA,gBAAAG,EAAApE,IAAAqE,EAAArE,EAC3K,EACAoE,EAAA,KAAApE,EAAA8D,OAAA,CAAAC,GAAA,CAAiE7B,EAAa5B,OAAA,CAAAgC,OAAA,IAAAuB,EAAA7D,IAAAsE,IAC9EA,EAAA,KACA,IAAAvB,EACA,IAAAwB,EAAAC,WAAAC,KAAA,CACA,MAAAC,CAAAA,CAAiBC,CAAAA,SDxDjBF,CAAA,EACA,wBAAAA,GAAAA,CAAA,IAAAA,EAAAG,aAAA,ECuD8BL,IAAA,OAAAxB,CAAAA,EAAAwB,EAAAM,oBAAA,GAAAC,QAAA,WAAA/B,EAAAgC,QAAA,EAC9B,EACAV,EAAA,KAAArE,EAAA8D,OAAA,CAAAC,GAAA,CAAmE7B,EAAa5B,OAAA,CAAAkC,UAAA,gBCjDhF,IAAAwC,EAAA,KACEC,EAAQ,MACV,IAAAnC,EAAkB,GAAAoC,EAAAC,EAAA,IAClBxC,EAAqB9C,EAAa,CAClCE,gBAAA,SACAD,oBAAyB,GAAAxF,EAAA8K,EAAA,GACzB,GAAGtC,GACHuC,EAAmB,GAAAnF,EAAAO,EAAA,EAAqBqC,EAAA,YACxCJ,EAAA,CAAAzC,EAAA,EAAqC,IACrC,IAAAqF,EAAyB,GAAAlF,EAAAmF,EAAA,EAAkBzC,GAC3C0C,EAAAF,EAAAD,QAAA,CAAAI,YAAA,CAAA1B,GAAA,CAAmE3D,EAAAC,EAAS,CAAAqF,eAAA,CAAAC,UAAA,GAAAL,EAAAM,OAAA,CAAA7B,GAAA,CAAyD3D,EAAAC,EAAS,CAAAwF,OAAA,CAAAF,UAAA,EAC9I7E,EAAiC,GAAAZ,EAAAC,EAAA,EAAS2C,EAAU1C,EAAAC,EAAS,CAAAC,OAAA,CAAAS,gBAAA,EAC7DC,EAAiC,GAAAd,EAAAe,EAAA,EAAuBH,GACxD,MAAW,GAAAV,EAAA0F,EAAA,EAAc,CACzBC,gBAAuBnD,EAAAoD,EAAQ,CAC/BR,gBAAAA,EACAS,QAAAX,EAAAD,QAAA,CAAAa,QAAA,GACAC,eAAAnF,EAAAmF,cAAA,EAA6DhF,EAAAiF,EAAe,CAC5EC,UAAArF,EAAAqF,SAAA,EAAmDlF,EAAAmF,EAAW,CAC9DC,UAAAvF,EAAAuF,SAAA,EAAmDpF,EAAAqF,EAAW,GACzD9D,gBAAA,EACL+D,cAAAxG,OAAAA,EAAAwG,aAAA,IAAAxG,EAAAwG,aAAA,EAAApB,CAAAA,MAAAA,EAAA,OAAAA,EAAAa,QAAA,GACA,EACA,EACAQ,EAAkBjE,EAAa,CAAGK,QAAAA,EAAAH,WAAAA,EAAAD,iBAAAA,EAAAG,SAA+C8D,EAAAvJ,CAAA,CAAAwF,SAAUA,EAAAoD,EAAA,GAC3F,OAAAhJ,OAAA4J,MAAA,CAAAjE,EAAA,CAAqC+D,QAAAA,EAAAhE,iBAAAA,CAAA,EACrC", "sources": ["webpack://_N_E/../../src/client/components/navigation.react-server.ts", "webpack://_N_E/./node_modules/next/dist/esm/api/navigation.react-server.js", "webpack://_N_E/./node_modules/@clerk/backend/dist/jwt/index.mjs", "webpack://_N_E/./node_modules/@clerk/nextjs/dist/esm/utils/logFormatter.js?d04a", "webpack://_N_E/./node_modules/@clerk/nextjs/dist/esm/utils/debugLogger.js?b5bc", "webpack://_N_E/./node_modules/@clerk/nextjs/dist/esm/server/createGetAuth.js", "webpack://_N_E/./node_modules/@clerk/nextjs/dist/esm/constants.js?891e", "webpack://_N_E/./node_modules/@clerk/nextjs/dist/esm/server/nextFetcher.js?3cc2", "webpack://_N_E/./node_modules/@clerk/nextjs/dist/esm/server/protect.js?fcad", "webpack://_N_E/./node_modules/@clerk/nextjs/dist/esm/app-router/server/auth.js", "webpack://_N_E/./node_modules/next/dist/compiled/server-only/empty.js"], "sourcesContent": [null, "export * from \"../client/components/navigation.react-server\";\n\n//# sourceMappingURL=navigation.react-server.js.map", "import {\n  withLegacyReturn,\n  withLegacySyncReturn\n} from \"../chunk-P263NW7Z.mjs\";\nimport {\n  base64url,\n  decodeJwt,\n  getCryptoAlgorithm,\n  hasValidSignature,\n  importKey,\n  runtime_default,\n  verifyJwt\n} from \"../chunk-PVHPEMF5.mjs\";\nimport {\n  SignJWTError\n} from \"../chunk-5JS2VYLU.mjs\";\n\n// src/jwt/signJwt.ts\nfunction encodeJwtData(value) {\n  const stringified = JSON.stringify(value);\n  const encoder = new TextEncoder();\n  const encoded = encoder.encode(stringified);\n  return base64url.stringify(encoded, { pad: false });\n}\nasync function signJwt(payload, key, options) {\n  if (!options.algorithm) {\n    throw new Error(\"No algorithm specified\");\n  }\n  const encoder = new TextEncoder();\n  const algorithm = getCryptoAlgorithm(options.algorithm);\n  if (!algorithm) {\n    return {\n      errors: [new SignJWTError(`Unsupported algorithm ${options.algorithm}`)]\n    };\n  }\n  const cryptoKey = await import<PERSON><PERSON>(key, algorithm, \"sign\");\n  const header = options.header || { typ: \"JWT\" };\n  header.alg = options.algorithm;\n  payload.iat = Math.floor(Date.now() / 1e3);\n  const encodedHeader = encodeJwtData(header);\n  const encodedPayload = encodeJwtData(payload);\n  const firstPart = `${encodedHeader}.${encodedPayload}`;\n  try {\n    const signature = await runtime_default.crypto.subtle.sign(algorithm, cryptoKey, encoder.encode(firstPart));\n    const encodedSignature = `${firstPart}.${base64url.stringify(new Uint8Array(signature), { pad: false })}`;\n    return { data: encodedSignature };\n  } catch (error) {\n    return { errors: [new SignJWTError(error?.message)] };\n  }\n}\n\n// src/jwt/index.ts\nvar verifyJwt2 = withLegacyReturn(verifyJwt);\nvar decodeJwt2 = withLegacySyncReturn(decodeJwt);\nvar signJwt2 = withLegacyReturn(signJwt);\nvar hasValidSignature2 = withLegacyReturn(hasValidSignature);\nexport {\n  decodeJwt2 as decodeJwt,\n  hasValidSignature2 as hasValidSignature,\n  signJwt2 as signJwt,\n  verifyJwt2 as verifyJwt\n};\n//# sourceMappingURL=index.mjs.map", "const maskSecretKey = (str) => {\n  if (!str || typeof str !== \"string\") {\n    return str;\n  }\n  try {\n    return (str || \"\").replace(/^(sk_(live|test)_)(.+?)(.{3})$/, \"$1*********$4\");\n  } catch (e) {\n    return \"\";\n  }\n};\nconst logFormatter = (entry) => {\n  return (Array.isArray(entry) ? entry : [entry]).map((entry2) => {\n    if (typeof entry2 === \"string\") {\n      return maskSecretKey(entry2);\n    }\n    const masked = Object.fromEntries(Object.entries(entry2).map(([k, v]) => [k, maskSecretKey(v)]));\n    return JSON.stringify(masked, null, 2);\n  }).join(\", \");\n};\nexport {\n  logFormatter\n};\n//# sourceMappingURL=logFormatter.js.map", "import nextPkg from \"next/package.json\";\nimport { logFormatter } from \"./logFormatter\";\nconst createDebugLogger = (name, formatter) => () => {\n  const entries = [];\n  let isEnabled = false;\n  return {\n    enable: () => {\n      isEnabled = true;\n    },\n    debug: (...args) => {\n      if (isEnabled) {\n        entries.push(args.map((arg) => typeof arg === \"function\" ? arg() : arg));\n      }\n    },\n    commit: () => {\n      if (isEnabled) {\n        console.log(debugLogHeader(name));\n        for (const log of entries) {\n          let output = formatter(log);\n          output = output.split(\"\\n\").map((l) => `  ${l}`).join(\"\\n\");\n          if (process.env.VERCEL) {\n            output = truncate(output, 4096);\n          }\n          console.log(output);\n        }\n        console.log(debugLogFooter(name));\n      }\n    }\n  };\n};\nconst withLogger = (loggerFactoryOrName, handlerCtor) => {\n  return (...args) => {\n    const factory = typeof loggerFactoryOrName === \"string\" ? createDebugLogger(loggerFactoryOrName, logFormatter) : loggerFactoryOrName;\n    const logger = factory();\n    const handler = handlerCtor(logger);\n    try {\n      const res = handler(...args);\n      if (typeof res === \"object\" && \"then\" in res && typeof res.then === \"function\") {\n        return res.then((val) => {\n          logger.commit();\n          return val;\n        }).catch((err) => {\n          logger.commit();\n          throw err;\n        });\n      }\n      logger.commit();\n      return res;\n    } catch (err) {\n      logger.commit();\n      throw err;\n    }\n  };\n};\nfunction debugLogHeader(name) {\n  return `[clerk debug start: ${name}]`;\n}\nfunction debugLogFooter(name) {\n  return `[clerk debug end: ${name}] (@clerk/nextjs=${\"5.7.5\"},next=${nextPkg.version})`;\n}\nfunction truncate(str, maxLength) {\n  const encoder = new TextEncoder();\n  const decoder = new TextDecoder(\"utf-8\");\n  const encodedString = encoder.encode(str);\n  const truncatedString = encodedString.slice(0, maxLength);\n  return decoder.decode(truncatedString).replace(/\\uFFFD/g, \"\");\n}\nexport {\n  createDebugLogger,\n  withLogger\n};\n//# sourceMappingURL=debugLogger.js.map", "import { AuthStatus, constants, signedInAuthObject, signedOutAuthObject } from \"@clerk/backend/internal\";\nimport { decodeJwt } from \"@clerk/backend/jwt\";\nimport { withLogger } from \"../utils/debugLogger\";\nimport { API_URL, API_VERSION, SECRET_KEY } from \"./constants\";\nimport { getAuthAuthHeaderMissing } from \"./errors\";\nimport { assertTokenSignature, decryptClerkRequestData, getAuthKeyFromRequest, getCookie, getHeader } from \"./utils\";\nconst createGetAuth = ({\n  noAuthStatusMessage,\n  debugLoggerName\n}) => withLogger(debugLoggerName, (logger) => {\n  return (req, opts) => {\n    if (getHeader(req, constants.Headers.EnableDebug) === \"true\") {\n      logger.enable();\n    }\n    const authToken = getAuthKeyFromRequest(req, \"AuthToken\");\n    const authSignature = getAuthKeyFromRequest(req, \"AuthSignature\");\n    const authMessage = getAuthKeyFromRequest(req, \"AuthMessage\");\n    const authReason = getAuthKeyFromRequest(req, \"AuthReason\");\n    const authStatus = getAuthKeyFromRequest(req, \"AuthStatus\");\n    logger.debug(\"Headers debug\", { authStatus, authMessage, authReason });\n    if (!authStatus) {\n      throw new Error(noAuthStatusMessage);\n    }\n    const encryptedRequestData = getHeader(req, constants.Headers.ClerkRequestData);\n    const decryptedRequestData = decryptClerkRequestData(encryptedRequestData);\n    const options = {\n      authStatus,\n      apiUrl: API_URL,\n      apiVersion: API_VERSION,\n      authMessage,\n      secretKey: (opts == null ? void 0 : opts.secretKey) || decryptedRequestData.secretKey || SECRET_KEY,\n      authReason\n    };\n    logger.debug(\"Options debug\", options);\n    if (authStatus === AuthStatus.SignedIn) {\n      assertTokenSignature(authToken, options.secretKey, authSignature);\n      const jwt = decodeJwt(authToken);\n      logger.debug(\"JWT debug\", jwt.raw.text);\n      return signedInAuthObject(options, jwt.raw.text, jwt.payload);\n    }\n    return signedOutAuthObject(options);\n  };\n});\nconst getAuth = createGetAuth({\n  debugLoggerName: \"getAuth()\",\n  noAuthStatusMessage: getAuthAuthHeaderMissing()\n});\nconst parseJwt = (req) => {\n  var _a;\n  const cookieToken = getCookie(req, constants.Cookies.Session);\n  const headerToken = (_a = getHeader(req, \"authorization\")) == null ? void 0 : _a.replace(\"Bearer \", \"\");\n  return decodeJwt(cookieToken || headerToken || \"\");\n};\nexport {\n  createGetAuth,\n  getAuth,\n  parseJwt\n};\n//# sourceMappingURL=createGetAuth.js.map", "const Headers = {\n  NextRewrite: \"x-middleware-rewrite\",\n  NextResume: \"x-middleware-next\",\n  NextRedirect: \"Location\",\n  // Used by next to identify internal navigation for app router\n  NextUrl: \"next-url\",\n  NextAction: \"next-action\",\n  // Used by next to identify internal navigation for pages router\n  NextjsData: \"x-nextjs-data\"\n};\nconst constants = {\n  Headers\n};\nexport {\n  constants\n};\n//# sourceMappingURL=constants.js.map", "function isNextFetcher(fetch) {\n  return \"__nextPatched\" in fetch && fetch.__nextPatched === true;\n}\nexport {\n  isNextFetcher\n};\n//# sourceMappingURL=nextFetcher.js.map", "import { constants } from \"@clerk/backend/internal\";\nimport { constants as nextConstants } from \"../constants\";\nimport { isNextFetcher } from \"./nextFetcher\";\nconst createProtect = (opts) => {\n  const { redirectToSignIn, authObject, redirect, notFound, request } = opts;\n  return (...args) => {\n    var _a, _b, _c, _d, _e, _f;\n    const optionValuesAsParam = ((_a = args[0]) == null ? void 0 : _a.unauthenticatedUrl) || ((_b = args[0]) == null ? void 0 : _b.unauthorizedUrl);\n    const paramsOrFunction = optionValuesAsParam ? void 0 : args[0];\n    const unauthenticatedUrl = ((_c = args[0]) == null ? void 0 : _c.unauthenticatedUrl) || ((_d = args[1]) == null ? void 0 : _d.unauthenticatedUrl);\n    const unauthorizedUrl = ((_e = args[0]) == null ? void 0 : _e.unauthorizedUrl) || ((_f = args[1]) == null ? void 0 : _f.unauthorizedUrl);\n    const handleUnauthenticated = () => {\n      if (unauthenticatedUrl) {\n        return redirect(unauthenticatedUrl);\n      }\n      if (isPageRequest(request)) {\n        return redirectToSignIn();\n      }\n      return notFound();\n    };\n    const handleUnauthorized = () => {\n      if (unauthorizedUrl) {\n        return redirect(unauthorizedUrl);\n      }\n      return notFound();\n    };\n    if (!authObject.userId) {\n      return handleUnauthenticated();\n    }\n    if (!paramsOrFunction) {\n      return authObject;\n    }\n    if (typeof paramsOrFunction === \"function\") {\n      if (paramsOrFunction(authObject.has)) {\n        return authObject;\n      }\n      return handleUnauthorized();\n    }\n    if (authObject.has(paramsOrFunction)) {\n      return authObject;\n    }\n    return handleUnauthorized();\n  };\n};\nconst isServerActionRequest = (req) => {\n  var _a, _b;\n  return !!req.headers.get(nextConstants.Headers.NextUrl) && (((_a = req.headers.get(constants.Headers.Accept)) == null ? void 0 : _a.includes(\"text/x-component\")) || ((_b = req.headers.get(constants.Headers.ContentType)) == null ? void 0 : _b.includes(\"multipart/form-data\")) || !!req.headers.get(nextConstants.Headers.NextAction));\n};\nconst isPageRequest = (req) => {\n  var _a;\n  return req.headers.get(constants.Headers.SecFetchDest) === \"document\" || req.headers.get(constants.Headers.SecFetchDest) === \"iframe\" || ((_a = req.headers.get(constants.Headers.Accept)) == null ? void 0 : _a.includes(\"text/html\")) || isAppRouterInternalNavigation(req) || isPagesRouterInternalNavigation(req);\n};\nconst isAppRouterInternalNavigation = (req) => !!req.headers.get(nextConstants.Headers.NextUrl) && !isServerActionRequest(req) || isPagePathAvailable();\nconst isPagePathAvailable = () => {\n  var _a;\n  const __fetch = globalThis.fetch;\n  return Boolean(isNextFetcher(__fetch) ? (_a = __fetch.__nextGetStaticStore().getStore()) == null ? void 0 : _a.pagePath : false);\n};\nconst isPagesRouterInternalNavigation = (req) => !!req.headers.get(nextConstants.Headers.NextjsData);\nexport {\n  createProtect\n};\n//# sourceMappingURL=protect.js.map", "import { constants, createClerkRequest, createRedirect } from \"@clerk/backend/internal\";\nimport { notFound, redirect } from \"next/navigation\";\nimport { buildClerkProps } from \"../../server/buildClerkProps\";\nimport { PUBLISHABLE_KEY, SIGN_IN_URL, SIGN_UP_URL } from \"../../server/constants\";\nimport { createGetAuth } from \"../../server/createGetAuth\";\nimport { authAuthHeaderMissing } from \"../../server/errors\";\nimport { createProtect } from \"../../server/protect\";\nimport { decryptClerkRequestData, getAuthKeyFromRequest, getHeader } from \"../../server/utils\";\nimport { buildRequestLike } from \"./utils\";\nconst auth = () => {\n  require(\"server-only\");\n  const request = buildRequestLike();\n  const authObject = createGetAuth({\n    debugLoggerName: \"auth()\",\n    noAuthStatusMessage: authAuthHeaderMissing()\n  })(request);\n  const clerkUrl = getAuthKeyFromRequest(request, \"ClerkUrl\");\n  const redirectToSignIn = (opts = {}) => {\n    const clerkRequest = createClerkRequest(request);\n    const devBrowserToken = clerkRequest.clerkUrl.searchParams.get(constants.QueryParameters.DevBrowser) || clerkRequest.cookies.get(constants.Cookies.DevBrowser);\n    const encryptedRequestData = getHeader(request, constants.Headers.ClerkRequestData);\n    const decryptedRequestData = decryptClerkRequestData(encryptedRequestData);\n    return createRedirect({\n      redirectAdapter: redirect,\n      devBrowserToken,\n      baseUrl: clerkRequest.clerkUrl.toString(),\n      publishableKey: decryptedRequestData.publishableKey || PUBLISHABLE_KEY,\n      signInUrl: decryptedRequestData.signInUrl || SIGN_IN_URL,\n      signUpUrl: decryptedRequestData.signUpUrl || SIGN_UP_URL\n    }).redirectToSignIn({\n      returnBackUrl: opts.returnBackUrl === null ? \"\" : opts.returnBackUrl || (clerkUrl == null ? void 0 : clerkUrl.toString())\n    });\n  };\n  const protect = createProtect({ request, authObject, redirectToSignIn, notFound, redirect });\n  return Object.assign(authObject, { protect, redirectToSignIn });\n};\nconst initialState = () => {\n  return buildClerkProps(buildRequestLike());\n};\nexport {\n  auth,\n  initialState\n};\n//# sourceMappingURL=auth.js.map"], "names": ["encodeJwtData", "value", "stringified", "JSON", "stringify", "encoded", "encoder", "TextEncoder", "encode", "chunk_PVHPEMF5", "tH", "pad", "signJwt", "payload", "key", "options", "algorithm", "DO", "errors", "chunk_5JS2VYLU", "fi", "cryptoKey", "uI", "header", "typ", "alg", "iat", "Math", "floor", "Date", "now", "encodedHeader", "encodedPayload", "firstPart", "signature", "ZE", "crypto", "subtle", "sign", "data", "Uint8Array", "error", "message", "chunk_P263NW7Z", "A", "$v", "decodeJwt2", "t", "t5", "N7", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "str", "replace", "e", "log<PERSON><PERSON><PERSON>er", "Array", "isArray", "entry", "map", "entry2", "Object", "fromEntries", "entries", "k", "v", "join", "createDebugLogger", "name", "formatter", "isEnabled", "enable", "debug", "args", "push", "arg", "commit", "log", "console", "output", "split", "l", "process", "env", "VERCEL", "truncate", "max<PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "truncatedString", "encodedString", "slice", "decode", "package_namespaceObject", "i8", "<PERSON><PERSON><PERSON><PERSON>", "loggerFactoryOrName", "handler<PERSON>tor", "logger", "factory", "handler", "res", "then", "val", "catch", "err", "createGetAuth", "noAuthStatusMessage", "debugLoggerName", "req", "opts", "utils", "Pg", "internal", "_G", "Headers", "EnableDebug", "authToken", "to", "authSignature", "authMessage", "authReason", "authStatus", "encryptedRequestData", "ClerkRequestData", "decryptedRequestData", "om", "apiUrl", "server_constants", "T5", "apiVersion", "Gn", "secret<PERSON>ey", "Cn", "tD", "SignedIn", "ii", "jwt", "raw", "text", "B9", "JH", "PL", "constants_constants", "NextRewrite", "NextResume", "NextRedirect", "NextUrl", "NextAction", "NextjsData", "createProtect", "redirectToSignIn", "authObject", "redirect", "notFound", "request", "_a", "_b", "_c", "_d", "_e", "_f", "paramsOrFunction", "optionValuesAsParam", "unauthenticatedUrl", "unauthorizedUrl", "handleUnauthorized", "userId", "has", "isPageRequest", "isServerActionRequest", "headers", "get", "Accept", "includes", "ContentType", "SecFetchDest", "isAppRouterInternalNavigation", "isPagesRouterInternalNavigation", "isPagePathAvailable", "__fetch", "globalThis", "fetch", "Boolean", "isNextFetcher", "__nextPatched", "__nextGetStaticStore", "getStore", "pagePath", "auth", "__webpack_require__", "server_utils", "vX", "It", "clerkUrl", "clerkRequest", "WL", "devBrowserToken", "searchParams", "QueryParameters", "<PERSON><PERSON><PERSON><PERSON>", "cookies", "Cookies", "bI", "redirectAdapter", "uX", "baseUrl", "toString", "publishableKey", "Am", "signInUrl", "O0", "signUpUrl", "Dt", "returnBackUrl", "protect", "not_found", "assign"], "sourceRoot": ""}