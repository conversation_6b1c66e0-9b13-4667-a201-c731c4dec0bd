{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|ads.txt|doc).*))(.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|ads.txt|doc).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Gi1S19DOJ9aO_1S-1OZtT", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "BwQYLPtyqSBsoHoglCEO7+dsxikXjyRzTMeXw25qEpE=", "__NEXT_PREVIEW_MODE_ID": "9bcdc5e3486add297f925ec41b1a24c1", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f792bb45a091158d104a1b0e9af6057b66d4a42e3d9c9da16f34b520d970fce7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "73a4a734d58e88382c289b72b77a1253323cfca1c192a47f30a05343b524c99e"}}}, "functions": {"/api/(make)/active/route": {"files": ["server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/520.js", "server/edge-chunks/288.js", "server/edge-chunks/225.js", "server/edge-chunks/436.js", "server/edge-chunks/657.js", "server/edge-chunks/551.js", "server/app/api/(make)/active/route.js"], "name": "app/api/(make)/active/route", "page": "/api/(make)/active/route", "matchers": [{"regexp": "^/api/active$", "originalSource": "/api/active"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Gi1S19DOJ9aO_1S-1OZtT", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "BwQYLPtyqSBsoHoglCEO7+dsxikXjyRzTMeXw25qEpE=", "__NEXT_PREVIEW_MODE_ID": "9bcdc5e3486add297f925ec41b1a24c1", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f792bb45a091158d104a1b0e9af6057b66d4a42e3d9c9da16f34b520d970fce7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "73a4a734d58e88382c289b72b77a1253323cfca1c192a47f30a05343b524c99e"}}, "/api/(make)/link/route": {"files": ["server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/520.js", "server/edge-chunks/288.js", "server/edge-chunks/225.js", "server/edge-chunks/436.js", "server/edge-chunks/657.js", "server/app/api/(make)/link/route.js"], "name": "app/api/(make)/link/route", "page": "/api/(make)/link/route", "matchers": [{"regexp": "^/api/link$", "originalSource": "/api/link"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Gi1S19DOJ9aO_1S-1OZtT", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "BwQYLPtyqSBsoHoglCEO7+dsxikXjyRzTMeXw25qEpE=", "__NEXT_PREVIEW_MODE_ID": "9bcdc5e3486add297f925ec41b1a24c1", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f792bb45a091158d104a1b0e9af6057b66d4a42e3d9c9da16f34b520d970fce7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "73a4a734d58e88382c289b72b77a1253323cfca1c192a47f30a05343b524c99e"}}, "/api/(n8n)/workflows/openAIAssistant/route": {"files": ["server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/520.js", "server/edge-chunks/288.js", "server/edge-chunks/225.js", "server/edge-chunks/436.js", "server/edge-chunks/657.js", "server/edge-chunks/551.js", "server/app/api/(n8n)/workflows/openAIAssistant/route.js"], "name": "app/api/(n8n)/workflows/openAIAssistant/route", "page": "/api/(n8n)/workflows/openAIAssistant/route", "matchers": [{"regexp": "^/api/workflows/openAIAssistant$", "originalSource": "/api/workflows/openAIAssistant"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Gi1S19DOJ9aO_1S-1OZtT", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "BwQYLPtyqSBsoHoglCEO7+dsxikXjyRzTMeXw25qEpE=", "__NEXT_PREVIEW_MODE_ID": "9bcdc5e3486add297f925ec41b1a24c1", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f792bb45a091158d104a1b0e9af6057b66d4a42e3d9c9da16f34b520d970fce7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "73a4a734d58e88382c289b72b77a1253323cfca1c192a47f30a05343b524c99e"}}, "/api/projects/route": {"files": ["server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/520.js", "server/edge-chunks/288.js", "server/edge-chunks/225.js", "server/edge-chunks/436.js", "server/edge-chunks/657.js", "server/app/api/projects/route.js"], "name": "app/api/projects/route", "page": "/api/projects/route", "matchers": [{"regexp": "^/api/projects$", "originalSource": "/api/projects"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Gi1S19DOJ9aO_1S-1OZtT", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "BwQYLPtyqSBsoHoglCEO7+dsxikXjyRzTMeXw25qEpE=", "__NEXT_PREVIEW_MODE_ID": "9bcdc5e3486add297f925ec41b1a24c1", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f792bb45a091158d104a1b0e9af6057b66d4a42e3d9c9da16f34b520d970fce7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "73a4a734d58e88382c289b72b77a1253323cfca1c192a47f30a05343b524c99e"}}, "/api/stripe/create-checkout/route": {"files": ["server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/520.js", "server/edge-chunks/735.js", "server/app/api/stripe/create-checkout/route.js"], "name": "app/api/stripe/create-checkout/route", "page": "/api/stripe/create-checkout/route", "matchers": [{"regexp": "^/api/stripe/create\\-checkout$", "originalSource": "/api/stripe/create-checkout"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Gi1S19DOJ9aO_1S-1OZtT", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "BwQYLPtyqSBsoHoglCEO7+dsxikXjyRzTMeXw25qEpE=", "__NEXT_PREVIEW_MODE_ID": "9bcdc5e3486add297f925ec41b1a24c1", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f792bb45a091158d104a1b0e9af6057b66d4a42e3d9c9da16f34b520d970fce7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "73a4a734d58e88382c289b72b77a1253323cfca1c192a47f30a05343b524c99e"}}, "/api/(make)/scenarios/route": {"files": ["server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/520.js", "server/edge-chunks/288.js", "server/edge-chunks/436.js", "server/edge-chunks/657.js", "server/edge-chunks/551.js", "server/app/api/(make)/scenarios/route.js"], "name": "app/api/(make)/scenarios/route", "page": "/api/(make)/scenarios/route", "matchers": [{"regexp": "^/api/scenarios$", "originalSource": "/api/scenarios"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Gi1S19DOJ9aO_1S-1OZtT", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "BwQYLPtyqSBsoHoglCEO7+dsxikXjyRzTMeXw25qEpE=", "__NEXT_PREVIEW_MODE_ID": "9bcdc5e3486add297f925ec41b1a24c1", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f792bb45a091158d104a1b0e9af6057b66d4a42e3d9c9da16f34b520d970fce7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "73a4a734d58e88382c289b72b77a1253323cfca1c192a47f30a05343b524c99e"}}, "/api/(make)/scenarios/openAIAssistant/route": {"files": ["server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/520.js", "server/edge-chunks/288.js", "server/edge-chunks/225.js", "server/edge-chunks/436.js", "server/edge-chunks/657.js", "server/edge-chunks/551.js", "server/app/api/(make)/scenarios/openAIAssistant/route.js"], "name": "app/api/(make)/scenarios/openAIAssistant/route", "page": "/api/(make)/scenarios/openAIAssistant/route", "matchers": [{"regexp": "^/api/scenarios/openAIAssistant$", "originalSource": "/api/scenarios/openAIAssistant"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Gi1S19DOJ9aO_1S-1OZtT", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "BwQYLPtyqSBsoHoglCEO7+dsxikXjyRzTMeXw25qEpE=", "__NEXT_PREVIEW_MODE_ID": "9bcdc5e3486add297f925ec41b1a24c1", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f792bb45a091158d104a1b0e9af6057b66d4a42e3d9c9da16f34b520d970fce7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "73a4a734d58e88382c289b72b77a1253323cfca1c192a47f30a05343b524c99e"}}, "/api/stripe/create-portal/route": {"files": ["server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/520.js", "server/edge-chunks/288.js", "server/edge-chunks/225.js", "server/edge-chunks/436.js", "server/edge-chunks/735.js", "server/app/api/stripe/create-portal/route.js"], "name": "app/api/stripe/create-portal/route", "page": "/api/stripe/create-portal/route", "matchers": [{"regexp": "^/api/stripe/create\\-portal$", "originalSource": "/api/stripe/create-portal"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Gi1S19DOJ9aO_1S-1OZtT", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "BwQYLPtyqSBsoHoglCEO7+dsxikXjyRzTMeXw25qEpE=", "__NEXT_PREVIEW_MODE_ID": "9bcdc5e3486add297f925ec41b1a24c1", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f792bb45a091158d104a1b0e9af6057b66d4a42e3d9c9da16f34b520d970fce7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "73a4a734d58e88382c289b72b77a1253323cfca1c192a47f30a05343b524c99e"}}, "/api/test-email/route": {"files": ["server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/520.js", "server/edge-chunks/225.js", "server/edge-chunks/19.js", "server/app/api/test-email/route.js"], "name": "app/api/test-email/route", "page": "/api/test-email/route", "matchers": [{"regexp": "^/api/test\\-email$", "originalSource": "/api/test-email"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Gi1S19DOJ9aO_1S-1OZtT", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "BwQYLPtyqSBsoHoglCEO7+dsxikXjyRzTMeXw25qEpE=", "__NEXT_PREVIEW_MODE_ID": "9bcdc5e3486add297f925ec41b1a24c1", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f792bb45a091158d104a1b0e9af6057b66d4a42e3d9c9da16f34b520d970fce7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "73a4a734d58e88382c289b72b77a1253323cfca1c192a47f30a05343b524c99e"}}, "/api/webhook/stripe/route": {"files": ["server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/520.js", "server/edge-chunks/288.js", "server/edge-chunks/225.js", "server/edge-chunks/657.js", "server/edge-chunks/735.js", "server/edge-chunks/19.js", "server/app/api/webhook/stripe/route.js"], "name": "app/api/webhook/stripe/route", "page": "/api/webhook/stripe/route", "matchers": [{"regexp": "^/api/webhook/stripe$", "originalSource": "/api/webhook/stripe"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Gi1S19DOJ9aO_1S-1OZtT", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "BwQYLPtyqSBsoHoglCEO7+dsxikXjyRzTMeXw25qEpE=", "__NEXT_PREVIEW_MODE_ID": "9bcdc5e3486add297f925ec41b1a24c1", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f792bb45a091158d104a1b0e9af6057b66d4a42e3d9c9da16f34b520d970fce7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "73a4a734d58e88382c289b72b77a1253323cfca1c192a47f30a05343b524c99e"}}}, "sortedMiddleware": ["/"]}