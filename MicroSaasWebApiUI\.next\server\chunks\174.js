"use strict";exports.id=174,exports.ids=[174],exports.modules={45128:(e,r,t)=>{t.d(r,{u:()=>y});var n=t(71159),i=t(19510),s=Object.defineProperty,a=Object.defineProperties,o=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable,u=(e,r,t)=>r in e?s(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,f=(e,r)=>{for(var t in r||(r={}))c.call(r,t)&&u(e,t,r[t]);if(l)for(var t of l(r))d.call(r,t)&&u(e,t,r[t]);return e},h=(e,r)=>a(e,o(r)),p=(e,r)=>{var t={};for(var n in e)c.call(e,n)&&0>r.indexOf(n)&&(t[n]=e[n]);if(null!=e&&l)for(var n of l(e))0>r.indexOf(n)&&d.call(e,n)&&(t[n]=e[n]);return t},y=n.forwardRef((e,r)=>{var{children:t,style:n}=e,s=p(e,["children","style"]);return(0,i.jsx)("body",h(f({},s),{ref:r,style:n,children:t}))});y.displayName="Body"},35565:(e,r,t)=>{t.d(r,{W:()=>y});var n=t(71159),i=t(19510),s=Object.defineProperty,a=Object.defineProperties,o=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable,u=(e,r,t)=>r in e?s(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,f=(e,r)=>{for(var t in r||(r={}))c.call(r,t)&&u(e,t,r[t]);if(l)for(var t of l(r))d.call(r,t)&&u(e,t,r[t]);return e},h=(e,r)=>a(e,o(r)),p=(e,r)=>{var t={};for(var n in e)c.call(e,n)&&0>r.indexOf(n)&&(t[n]=e[n]);if(null!=e&&l)for(var n of l(e))0>r.indexOf(n)&&d.call(e,n)&&(t[n]=e[n]);return t},y=n.forwardRef((e,r)=>{var{children:t,style:n}=e,s=p(e,["children","style"]);return(0,i.jsx)("table",h(f({align:"center",width:"100%"},s),{border:0,cellPadding:"0",cellSpacing:"0",ref:r,role:"presentation",style:f({maxWidth:"37.5em"},n),children:(0,i.jsx)("tbody",{children:(0,i.jsx)("tr",{style:{width:"100%"},children:(0,i.jsx)("td",{children:t})})})}))});y.displayName="Container"},15904:(e,r,t)=>{t.d(r,{F:()=>y});var n=t(71159),i=t(19510),s=Object.defineProperty,a=Object.defineProperties,o=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable,u=(e,r,t)=>r in e?s(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,f=(e,r)=>{for(var t in r||(r={}))c.call(r,t)&&u(e,t,r[t]);if(l)for(var t of l(r))d.call(r,t)&&u(e,t,r[t]);return e},h=(e,r)=>a(e,o(r)),p=(e,r)=>{var t={};for(var n in e)c.call(e,n)&&0>r.indexOf(n)&&(t[n]=e[n]);if(null!=e&&l)for(var n of l(e))0>r.indexOf(n)&&d.call(e,n)&&(t[n]=e[n]);return t},y=n.forwardRef((e,r)=>{var{children:t}=e,n=p(e,["children"]);return(0,i.jsxs)("head",h(f({},n),{ref:r,children:[(0,i.jsx)("meta",{content:"text/html; charset=UTF-8",httpEquiv:"Content-Type"}),(0,i.jsx)("meta",{name:"x-apple-disable-message-reformatting"}),t]}))});y.displayName="Head"},52801:(e,r,t)=>{t.d(r,{V:()=>y});var n=t(71159),i=t(19510),s=Object.defineProperty,a=Object.defineProperties,o=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable,u=(e,r,t)=>r in e?s(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,f=(e,r)=>{for(var t in r||(r={}))c.call(r,t)&&u(e,t,r[t]);if(l)for(var t of l(r))d.call(r,t)&&u(e,t,r[t]);return e},h=(e,r)=>a(e,o(r)),p=(e,r)=>{var t={};for(var n in e)c.call(e,n)&&0>r.indexOf(n)&&(t[n]=e[n]);if(null!=e&&l)for(var n of l(e))0>r.indexOf(n)&&d.call(e,n)&&(t[n]=e[n]);return t},y=n.forwardRef((e,r)=>{var{children:t,lang:n="en",dir:s="ltr"}=e,a=p(e,["children","lang","dir"]);return(0,i.jsx)("html",h(f({},a),{dir:s,lang:n,ref:r,children:t}))});y.displayName="Html"},86901:(e,r,t)=>{t.d(r,{r:()=>y});var n=t(71159),i=t(19510),s=Object.defineProperty,a=Object.defineProperties,o=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable,u=(e,r,t)=>r in e?s(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,f=(e,r)=>{for(var t in r||(r={}))c.call(r,t)&&u(e,t,r[t]);if(l)for(var t of l(r))d.call(r,t)&&u(e,t,r[t]);return e},h=(e,r)=>a(e,o(r)),p=(e,r)=>{var t={};for(var n in e)c.call(e,n)&&0>r.indexOf(n)&&(t[n]=e[n]);if(null!=e&&l)for(var n of l(e))0>r.indexOf(n)&&d.call(e,n)&&(t[n]=e[n]);return t},y=n.forwardRef((e,r)=>{var{target:t="_blank",style:n}=e,s=p(e,["target","style"]);return(0,i.jsx)("a",h(f({},s),{ref:r,style:f({color:"#067df7",textDecoration:"none"},n),target:t,children:s.children}))});y.displayName="Link"},70168:(e,r,t)=>{t.d(r,{M:()=>y});var n=t(71159),i=t(19510),s=Object.defineProperty,a=Object.defineProperties,o=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable,u=(e,r,t)=>r in e?s(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,f=(e,r)=>{for(var t in r||(r={}))c.call(r,t)&&u(e,t,r[t]);if(l)for(var t of l(r))d.call(r,t)&&u(e,t,r[t]);return e},h=(e,r)=>a(e,o(r)),p=(e,r)=>{var t={};for(var n in e)c.call(e,n)&&0>r.indexOf(n)&&(t[n]=e[n]);if(null!=e&&l)for(var n of l(e))0>r.indexOf(n)&&d.call(e,n)&&(t[n]=e[n]);return t},y=n.forwardRef((e,r)=>{var{children:t=""}=e,n=p(e,["children"]);let s=(Array.isArray(t)?t.join(""):t).substring(0,150);return(0,i.jsxs)("div",h(f({style:{display:"none",overflow:"hidden",lineHeight:"1px",opacity:0,maxHeight:0,maxWidth:0}},n),{ref:r,children:[s,b(s)]}))});y.displayName="Preview";var b=e=>e.length>=150?null:(0,i.jsx)("div",{children:"\xa0‌​‍‎‏\uFEFF".repeat(150-e.length)})},15087:(e,r,t)=>{t.d(r,{$:()=>y});var n=t(71159),i=t(19510),s=Object.defineProperty,a=Object.defineProperties,o=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable,u=(e,r,t)=>r in e?s(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,f=(e,r)=>{for(var t in r||(r={}))c.call(r,t)&&u(e,t,r[t]);if(l)for(var t of l(r))d.call(r,t)&&u(e,t,r[t]);return e},h=(e,r)=>a(e,o(r)),p=(e,r)=>{var t={};for(var n in e)c.call(e,n)&&0>r.indexOf(n)&&(t[n]=e[n]);if(null!=e&&l)for(var n of l(e))0>r.indexOf(n)&&d.call(e,n)&&(t[n]=e[n]);return t},y=n.forwardRef((e,r)=>{var{children:t,style:n}=e,s=p(e,["children","style"]);return(0,i.jsx)("table",h(f({align:"center",width:"100%"},s),{border:0,cellPadding:"0",cellSpacing:"0",ref:r,role:"presentation",style:n,children:(0,i.jsx)("tbody",{children:(0,i.jsx)("tr",{children:(0,i.jsx)("td",{children:t})})})}))});y.displayName="Section"},56777:(e,r,t)=>{t.d(r,{x:()=>y});var n=t(71159),i=t(19510),s=Object.defineProperty,a=Object.defineProperties,o=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable,u=(e,r,t)=>r in e?s(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,f=(e,r)=>{for(var t in r||(r={}))c.call(r,t)&&u(e,t,r[t]);if(l)for(var t of l(r))d.call(r,t)&&u(e,t,r[t]);return e},h=(e,r)=>a(e,o(r)),p=(e,r)=>{var t={};for(var n in e)c.call(e,n)&&0>r.indexOf(n)&&(t[n]=e[n]);if(null!=e&&l)for(var n of l(e))0>r.indexOf(n)&&d.call(e,n)&&(t[n]=e[n]);return t},y=n.forwardRef((e,r)=>{var{style:t}=e,n=p(e,["style"]);return(0,i.jsx)("p",h(f({},n),{ref:r,style:f({fontSize:"14px",lineHeight:"24px",margin:"16px 0"},t)}))});y.displayName="Text"},2723:(e,r,t)=>{t.d(r,{R:()=>j});var n=Object.defineProperty,i=Object.defineProperties,s=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,c=(e,r,t)=>r in e?n(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,d=(e,r)=>{for(var t in r||(r={}))o.call(r,t)&&c(e,t,r[t]);if(a)for(var t of a(r))l.call(r,t)&&c(e,t,r[t]);return e},u=(e,r)=>i(e,s(r)),f=(e,r,t)=>new Promise((n,i)=>{var s=e=>{try{o(t.next(e))}catch(e){i(e)}},a=e=>{try{o(t.throw(e))}catch(e){i(e)}},o=e=>e.done?n(e.value):Promise.resolve(e.value).then(s,a);o((t=t.apply(e,r)).next())}),h=class{constructor(e){this.resend=e}create(e){return f(this,arguments,function*(e,r={}){return yield this.resend.post("/api-keys",e,r)})}list(){return f(this,null,function*(){return yield this.resend.get("/api-keys")})}remove(e){return f(this,null,function*(){return yield this.resend.delete(`/api-keys/${e}`)})}},p=class{constructor(e){this.resend=e}create(e){return f(this,arguments,function*(e,r={}){return yield this.resend.post("/audiences",e,r)})}list(){return f(this,null,function*(){return yield this.resend.get("/audiences")})}get(e){return f(this,null,function*(){return yield this.resend.get(`/audiences/${e}`)})}remove(e){return f(this,null,function*(){return yield this.resend.delete(`/audiences/${e}`)})}},y=class{constructor(e){this.resend=e}send(e){return f(this,arguments,function*(e,r={}){return this.create(e,r)})}create(e){return f(this,arguments,function*(e,r={}){for(let r of e)if(r.react){if(!this.renderAsync)try{let{renderAsync:e}=yield t.e(650).then(t.bind(t,62650));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}r.html=yield this.renderAsync(r.react),r.react=void 0}return yield this.resend.post("/emails/batch",e,r)})}},b=class{constructor(e){this.resend=e}create(e){return f(this,arguments,function*(e,r={}){return yield this.resend.post(`/audiences/${e.audienceId}/contacts`,{unsubscribed:e.unsubscribed,email:e.email,first_name:e.firstName,last_name:e.lastName},r)})}list(e){return f(this,null,function*(){return yield this.resend.get(`/audiences/${e.audienceId}/contacts`)})}get(e){return f(this,null,function*(){return yield this.resend.get(`/audiences/${e.audienceId}/contacts/${e.id}`)})}update(e){return f(this,null,function*(){return yield this.resend.patch(`/audiences/${e.audienceId}/contacts/${e.id}`,{unsubscribed:e.unsubscribed,first_name:e.firstName,last_name:e.lastName})})}remove(e){return f(this,null,function*(){return yield this.resend.delete(`/audiences/${e.audienceId}/contacts/${(null==e?void 0:e.email)?null==e?void 0:e.email:null==e?void 0:e.id}`)})}},m=class{constructor(e){this.resend=e}create(e){return f(this,arguments,function*(e,r={}){return yield this.resend.post("/domains",e,r)})}list(){return f(this,null,function*(){return yield this.resend.get("/domains")})}get(e){return f(this,null,function*(){return yield this.resend.get(`/domains/${e}`)})}update(e){return f(this,null,function*(){return yield this.resend.patch(`/domains/${e.id}`,{click_tracking:e.clickTracking,open_tracking:e.openTracking,tls:e.tls})})}remove(e){return f(this,null,function*(){return yield this.resend.delete(`/domains/${e}`)})}verify(e){return f(this,null,function*(){return yield this.resend.post(`/domains/${e}/verify`)})}},O=class{constructor(e){this.resend=e}send(e){return f(this,arguments,function*(e,r={}){return this.create(e,r)})}create(e){return f(this,arguments,function*(e,r={}){if(e.react){if(!this.renderAsync)try{let{renderAsync:e}=yield t.e(650).then(t.bind(t,62650));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}e.html=yield this.renderAsync(e.react)}return yield this.resend.post("/emails",{attachments:e.attachments,bcc:e.bcc,cc:e.cc,from:e.from,headers:e.headers,html:e.html,reply_to:e.replyTo,scheduled_at:e.scheduledAt,subject:e.subject,tags:e.tags,text:e.text,to:e.to},r)})}get(e){return f(this,null,function*(){return yield this.resend.get(`/emails/${e}`)})}update(e){return f(this,null,function*(){return yield this.resend.patch(`/emails/${e.id}`,{scheduled_at:e.scheduledAt})})}cancel(e){return f(this,null,function*(){return yield this.resend.post(`/emails/${e}/cancel`)})}},v="undefined"!=typeof process&&process.env&&process.env.RESEND_BASE_URL||"https://api.resend.com",g="undefined"!=typeof process&&process.env&&process.env.RESEND_USER_AGENT||"resend-node:4.0.1",j=class{constructor(e){if(this.key=e,this.apiKeys=new h(this),this.audiences=new p(this),this.batch=new y(this),this.contacts=new b(this),this.domains=new m(this),this.emails=new O(this),!e&&("undefined"!=typeof process&&process.env&&(this.key=process.env.RESEND_API_KEY),!this.key))throw Error('Missing API key. Pass it to the constructor `new Resend("re_123")`');this.headers=new Headers({Authorization:`Bearer ${this.key}`,"User-Agent":g,"Content-Type":"application/json"})}fetchRequest(e){return f(this,arguments,function*(e,r={}){try{let t=yield fetch(`${v}${e}`,r);if(!t.ok)try{let e=yield t.text();return{data:null,error:JSON.parse(e)}}catch(r){if(r instanceof SyntaxError)return{data:null,error:{name:"application_error",message:"Internal server error. We are unable to process your request right now, please try again later."}};let e={message:t.statusText,name:"application_error"};if(r instanceof Error)return{data:null,error:u(d({},e),{message:r.message})};return{data:null,error:e}}return{data:yield t.json(),error:null}}catch(e){return{data:null,error:{name:"application_error",message:"Unable to fetch data. The request could not be resolved."}}}})}post(e,r){return f(this,arguments,function*(e,r,t={}){let n=d({method:"POST",headers:this.headers,body:JSON.stringify(r)},t);return this.fetchRequest(e,n)})}get(e){return f(this,arguments,function*(e,r={}){let t=d({method:"GET",headers:this.headers},r);return this.fetchRequest(e,t)})}put(e,r){return f(this,arguments,function*(e,r,t={}){let n=d({method:"PUT",headers:this.headers,body:JSON.stringify(r)},t);return this.fetchRequest(e,n)})}patch(e,r){return f(this,arguments,function*(e,r,t={}){let n=d({method:"PATCH",headers:this.headers,body:JSON.stringify(r)},t);return this.fetchRequest(e,n)})}delete(e,r){return f(this,null,function*(){let t={method:"DELETE",headers:this.headers,body:JSON.stringify(r)};return this.fetchRequest(e,t)})}}}};