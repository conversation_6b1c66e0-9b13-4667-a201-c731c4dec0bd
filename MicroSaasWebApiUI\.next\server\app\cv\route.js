"use strict";(()=>{var e={};e.id=143,e.ids=[143],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},92048:e=>{e.exports=require("fs")},55315:e=>{e.exports=require("path")},49649:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>P,patchFetch:()=>g,requestAsyncStorage:()=>m,routeModule:()=>x,serverHooks:()=>h,staticGenerationAsyncStorage:()=>f});var n={};t.r(n),t.d(n,{GET:()=>v,dynamic:()=>l});var o=t(49303),a=t(88716),s=t(60670),i=t(87070),p=t(92048),c=t.n(p),u=t(55315),d=t.n(u);let l="force-dynamic";async function v(e){try{let e=d().join(process.cwd(),"public","doc","CVJavianPicardo2025.pdf");if(!c().existsSync(e))return new i.NextResponse("File not found",{status:404});let r=await c().promises.readFile(e);return new i.NextResponse(r,{headers:{"Content-Type":"application/pdf","Content-Disposition":"attachment; filename=CVJavianPicardo2025.pdf","Cache-Control":"no-cache"}})}catch(e){return console.error("Error serving PDF:",e),new i.NextResponse("Error serving file",{status:500})}}let x=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/cv/route",pathname:"/cv",filename:"route",bundlePath:"app/cv/route"},resolvedPagePath:"C:\\Projects\\PersonalPortal\\src\\app\\cv\\route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:m,staticGenerationAsyncStorage:f,serverHooks:h}=x,P="/cv/route";function g(){return(0,s.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:f})}},49303:(e,r,t)=>{e.exports=t(30517)}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[948,70],()=>t(49649));module.exports=n})();