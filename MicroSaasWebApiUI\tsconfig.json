{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "typeRoots": ["./node_modules/@types", "./types"], "allowJs": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "incremental": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noImplicitAny": true, "jsx": "preserve", "paths": {"@/*": ["./src/*"]}, "plugins": [{"name": "next"}], "strictNullChecks": false}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}