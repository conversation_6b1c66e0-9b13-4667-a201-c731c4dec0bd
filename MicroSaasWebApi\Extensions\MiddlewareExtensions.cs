﻿using Microsoft.Extensions.FileProviders;
using MicroSaasWebApi.Middlewares;

namespace MicroSaasWebApi.Extensions
{
    // Extension method used to add the middleware to the HTTP request pipeline.
    public static class MiddlewareExtensions
    {
        //public static IApplicationBuilder UseMiddlewareExtensions(this IApplicationBuilder builder)
        //{
        //    return builder.UseMiddleware<ApiRedirectMiddleware>();
        //}
    }

    public static class MarkdownMiddlewareExtensions
    {
        public static IApplicationBuilder UseMarkdownMiddleware(this IApplicationBuilder builder, string rootPath = "Documentation")
        {
            var fileProvider = new PhysicalFileProvider(System.IO.Path.Combine(System.IO.Directory.GetCurrentDirectory(), rootPath));
            return builder.UseMiddleware<DocumentationMiddleware>(fileProvider);
        }
    }
}
