﻿
using Newtonsoft.Json;
using MicroSaasWebApi.Models.AlphaCCO;
using MicroSaasWebApi.Models.Profile;
using MicroSaasWebApi.Models.PXW.AppSettings;
using MicroSaasWebApi.Properties.Enums;
using MicroSaasWebApi.Properties.PXW;
using MicroSaasWebApi.Services.AlphaCCO.PXW;
using MicroSaasWebApi.Services.PXW;
using MicroSaasWebApi.Services.PXW.Interface;
using RestSharp;
//using System.Text.Json;

namespace MicroSaasWebApi.Services.Profile.PXW
{
    /// <summary>
    /// Summary description for ProfileEngine
    /// </summary>
    public class ProfileEngine : EngineBase
    {
        protected ProfileResponse ProfileResult;
        protected AccountResponse AccountResult;
        protected MembersResponse MembersResult;
        protected MemberResponse MemberResult;
        protected MemberDetailResponse MembersDetailResult;
        protected BearerTokenResponse BearerTokenResponse;
        #region Initialize
        public ProfileEngine(int domainId, int languageId, IApplicationSettingsService applicationSettingsService, IHttpContextAccessor httpContextAccessor) : base(domainId, languageId, applicationSettingsService, httpContextAccessor) { }

        public void SetActiveAccountProfileId(string activeAccountProfileId)
        {
            _httpContextAccessor.HttpContext.Items["ActiveAccountProfileId"] = activeAccountProfileId;
        }

        public string GetActiveAccountProfileId()
        {
            try
            {
                return _httpContextAccessor?.HttpContext?.Items["ActiveAccountProfileId"] as string;
            }
            catch
            {
                return "";
            }
        }
        #endregion Initialize

        public List<AccountMember> GetAccountMembers()
        {
            GetBearerTokenTask().Wait();
            if (BearerTokenResponse != null)
            {
                var languagePreferred = "en";
                var isAcive = "true";
                if (!string.IsNullOrEmpty(BearerTokenResponse.access_token))
                {
                    GetMembersResponse(BearerTokenResponse.access_token);
                    if (MembersResult != null && MembersResult?.users?.Count > 0)
                    {
                        List<AccountMember> members = new List<AccountMember>();
                        AccountMember member;
                        foreach (Member mem in MembersResult.users)
                        {
                            member = new AccountMember();

                            member.FirstName = mem.firstname;
                            member.LastName = mem.lastname;
                            member.Email = mem.email;
                            member.MasterID = new Guid(mem?.id);
                            member.isActive = mem?.isActive?.ToLower() == "true" ? true : false;
                            member.LanguagePreference = mem?.languagePreference;
                            member.Account = new AccountInformation();

                            members.Add(member);
                        }
                        return members;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }
            }
            else
            {
                return null;
            }
        }

        public List<AccountMember> GetAccountMemberListWithProfile()
        {
            GetBearerTokenTask().Wait();
            if (BearerTokenResponse != null)
            {
                var languagePreferred = "en";
                var isAcive = "true";
                if (!string.IsNullOrEmpty(BearerTokenResponse.access_token))
                {
                    GetMembersResponse(BearerTokenResponse.access_token);
                    if (MembersResult != null && MembersResult.users.Count > 0)
                    {
                        List<AccountMember> members = new List<AccountMember>();
                        AccountMember member;
                        foreach (Member mem in MembersResult.users)
                        {
                            member = new AccountMember();

                            member.FirstName = mem.firstname;
                            member.LastName = mem.lastname;
                            member.Email = mem.email;
                            member.MasterID = new Guid(mem.id);
                            member.isActive = mem.isActive.ToLower() == "true" ? true : false;
                            member.isInviteSent = mem.isInviteSent == null ? false : mem.isInviteSent.ToLower() == "true" ? true : false;
                            member.LanguagePreference = mem.languagePreference;
                            member.Account = new AccountInformation();

                            AccountProfile profile;
                            if (BearerTokenResponse == null)
                            {
                                GetBearerToken();
                            }
                            GetProfiles(member.MasterID.ToString(), BearerTokenResponse.access_token, true).Wait();

                            if (ProfileResult.success)
                            {
                                int profileIndex = 1;
                                member.Account.Profiles = new List<AccountProfile>();
                                if (ProfileResult.profiles != null && ProfileResult.profiles.Count > 0)
                                {
                                    foreach (APIProfile ap in ProfileResult.profiles)
                                    {
                                        profile = ConvertProfile(profileIndex, ap, BearerTokenResponse.access_token, member.FirstName, member.LastName, member.Email, languagePreferred, true);
                                        member.Account.Profiles.Add(profile);
                                        profileIndex++;
                                    }

                                }
                            }

                            members.Add(member);
                        }
                        return members;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }
            }
            else
            {
                return null;
            }
        }

        public List<AccountMember> GetAccountMemberListWithoutWorkflows()
        {
            GetBearerTokenTask().Wait();
            if (BearerTokenResponse != null)
            {
                var languagePreferred = "en";
                var isAcive = "true";
                if (!string.IsNullOrEmpty(BearerTokenResponse.access_token))
                {
                    GetMembersResponse(BearerTokenResponse.access_token);
                    if (MembersResult != null && MembersResult.users.Count > 0)
                    {
                        List<AccountMember> members = new List<AccountMember>();
                        AccountMember member;
                        foreach (Member mem in MembersResult.users)
                        {
                            member = new AccountMember();

                            member.FirstName = mem.firstname;
                            member.LastName = mem.lastname;
                            member.Email = mem.email;
                            member.MasterID = new Guid(mem.id);
                            member.isActive = mem.isActive.ToLower() == "true" ? true : false;
                            member.isInviteSent = mem.isInviteSent == null ? false : mem.isInviteSent.ToLower() == "true" ? true : false;
                            member.LanguagePreference = mem.languagePreference;
                            member.Account = new AccountInformation();

                            AccountProfile profile;
                            if (BearerTokenResponse == null)
                            {
                                GetBearerToken();
                            }
                            GetProfiles(member.MasterID.ToString(), BearerTokenResponse.access_token, true).Wait();

                            if (ProfileResult.success)
                            {
                                int profileIndex = 1;
                                member.Account.Profiles = new List<AccountProfile>();
                                if (ProfileResult.profiles != null && ProfileResult.profiles.Count > 0)
                                {
                                    foreach (APIProfile ap in ProfileResult.profiles)
                                    {
                                        profile = ConvertProfileWithAccounts(profileIndex, ap, BearerTokenResponse.access_token, member.FirstName, member.LastName, member.Email, languagePreferred, true);
                                        member.Account.Profiles.Add(profile);
                                        profileIndex++;
                                    }

                                }
                            }

                            members.Add(member);
                        }
                        return members;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }
            }
            else
            {
                return null;
            }
        }

        public List<AccountMember> GetMembersBrief()
        {
            GetBearerTokenTask().Wait();
            if (BearerTokenResponse != null)
            {
                if (!string.IsNullOrEmpty(BearerTokenResponse.access_token))
                {
                    GetMembersBriefResponse(BearerTokenResponse.access_token);
                    if (MembersDetailResult != null && MembersDetailResult.data.users.Count > 0)
                    {
                        List<AccountMember> members = new List<AccountMember>();
                        AccountMember member;
                        AccountProfile profile;
                        Account account;
                        foreach (MemberBriefData mem in MembersDetailResult.data.users)
                        {
                            member = new AccountMember();

                            member.Email = mem.email;
                            member.MasterID = new Guid(mem.id);
                            member.Account = new AccountInformation();
                            member.Account.Profiles = new List<AccountProfile>();
                            member.isActive = mem.isActive;
                            member.isInviteSent = mem.isInviteSent == "true" ? true : false;
                            if (mem.profiles != null && mem.profiles.Count > 0)
                            {
                                foreach (ProfileBrief ap in mem.profiles)
                                {
                                    profile = new AccountProfile();
                                    profile.ProfileID = ap.profileId;
                                    profile.Name = ap.profileName;
                                    if (ap.accounts != null && ap.accounts.Count > 0)
                                    {
                                        profile.Accounts = new List<Account>();
                                        foreach (AccountBrief ab in ap.accounts)
                                        {
                                            account = new Account();
                                            account.AccountCode = ab.accountCode;

                                            profile.Accounts.Add(account);
                                        }
                                    }
                                    member.Account.Profiles.Add(profile);

                                }

                            }


                            members.Add(member);
                        }
                        return members;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }
            }
            else
            {
                return null;
            }
        }
        public AccountMember GetAccountMemberDetail(AccountMember member, string languagePreferred)
        {
            AccountProfile profile;
            if (BearerTokenResponse == null)
            {
                GetBearerToken();
            }
            GetProfiles(member.MasterID.ToString(), BearerTokenResponse.access_token, true).Wait();

            if (ProfileResult.success)
            {
                int profileIndex = 1;
                member.Account.Profiles = new List<AccountProfile>();
                if (ProfileResult.profiles != null && ProfileResult.profiles.Count > 0)
                {
                    foreach (APIProfile ap in ProfileResult.profiles)
                    {
                        profile = ConvertProfile(profileIndex, ap, BearerTokenResponse.access_token, member.FirstName, member.LastName, member.Email, languagePreferred, true);
                        member.Account.Profiles.Add(profile);
                        profileIndex++;
                    }
                }
            }
            return member;
        }

        public MemberResponse GetMemberResponse(string userid, string token)
        {
            GetMember(userid, token).Wait();
            return MemberResult;
        }

        public MembersResponse GetMembersResponse(string token)
        {
            GetMemberListTask(token).Wait();
            return MembersResult;

        }

        public MemberDetailResponse GetMembersBriefResponse(string token)
        {
            GetMemberDetailListTask(token).Wait();
            return MembersDetailResult;

        }


        public MemberResponse UpdateMemberPreferredLanguage(string token, AccountMember ac, string langaguePrefer)
        {
            UpdateMember(token, ac, langaguePrefer).Wait();
            return MemberResult;
        }

        /// <summary>
        /// this is the code for front-end to get member list from B2C
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="token"></param>
        /// <returns></returns>

        public AccountMember GetAccountMember(string userId, string token)
        {
            //WorkflowEngine we = new WorkflowEngine(Context, CurrentSql, DomainId, LanguageId);
            var languagePreferred = "en";
            var isAcive = "true";
            //MemberResponse mr = GetMemberResponse(userId, token);

            GetMember(userId, token).Wait();
            MemberResponse mr = MemberResult;
            AccountMember accountMember = new AccountMember();
            if (mr != null && mr.user != null)
            {
                languagePreferred = mr.user.languagePreference;
                accountMember.FirstName = mr.user.firstname;
                accountMember.LastName = mr.user.lastname;
                accountMember.Email = mr.user.email;
                accountMember.MasterID = new Guid(userId);
                accountMember.isActive = mr.user?.isActive?.ToLower() == "true" ? true : false;
                accountMember.Account = new AccountInformation();

                AccountProfile profile;
                GetProfiles(userId, token).Wait();
                if (ProfileResult.success)
                {
                    int profileIndex = 1;
                    accountMember.Account.Profiles = new List<AccountProfile>();
                    if (ProfileResult.profiles != null && ProfileResult.profiles.Count > 0)
                    {
                        ///load the profile for only the id stored in cookie, if no cookie, load only the first one
                        ///
                        string selectedProfileID = string.Empty;
                        try
                        {
                            selectedProfileID = _httpContextAccessor.HttpContext.Items["ActiveAccountProfileId"] as string;
                            //CookieHelper.GetFromCookie(Context, "ActiveAccountProfileId");

                            if (!string.IsNullOrEmpty(selectedProfileID))
                            {
                                selectedProfileID = ApiHelper.DecryptString(selectedProfileID, accountMember.MasterID.ToString());
                            }
                        }
                        catch
                        {

                        }
                        APIProfile apf = new APIProfile();
                        APIProfile apA = ProfileResult.profiles.FirstOrDefault(p => p.id.Equals(selectedProfileID));
                        if (apA != null)
                        {
                            apf = apA;
                        }
                        else
                        {
                            apf = ProfileResult.profiles.FirstOrDefault();
                            selectedProfileID = apf.id;
                            //CookieHelper.StoreInCookie(Context, "ActiveAccountProfileId", Encryption.RijndaelEncryption.Encrypt(apf.id, accountMember.MasterID.ToString()), null, null, DateTime.Now.AddHours(2), false, SameSiteMode.None, true);
                            _httpContextAccessor.HttpContext.Items["ActiveAccountProfileId"] = ApiHelper.EncryptString(apf.id, accountMember.MasterID.ToString());
                        }


                        foreach (APIProfile ap in ProfileResult.profiles)
                        {
                            if (ap.id == selectedProfileID)
                                profile = ConvertProfile(profileIndex, ap, token, accountMember.FirstName, accountMember.LastName, accountMember.Email, languagePreferred);
                            else
                                profile = ConvertProfileSimple(profileIndex, ap, token, accountMember.FirstName, accountMember.LastName, accountMember.Email, languagePreferred);


                            accountMember.Account.Profiles.Add(profile);
                            profileIndex++;
                        }
                    }

                    //if (ProfileResult.administers != null && ProfileResult.administers.Count > 0)
                    //{
                    //    foreach (APIProfile ap in ProfileResult.administers)
                    //    {
                    //        profile = ConvertProfile(profileIndex, eAccountProfileType.Manager, ap, token, firstName, lastName, email, languagePreferred);
                    //        accountMember.Account.Profiles.Add(profile);
                    //        profileIndex++;
                    //    }
                    //}

                }
            }

            return accountMember;
        }

        public AccountMember GetAccountMember(string userId, string token, string selectedProfileId)
        {
            //WorkflowEngine we = new WorkflowEngine(Context, CurrentSql, DomainId, LanguageId);
            var languagePreferred = "en";
            var isActive = "true";
            //MemberResponse mr = GetMemberResponse(userId, token);

            GetMember(userId, token).Wait();
            MemberResponse mr = MemberResult;
            AccountMember accountMember = new AccountMember();
            if (mr != null && mr.user != null)
            {
                languagePreferred = mr.user.languagePreference;
                accountMember.FirstName = mr.user.firstname;
                accountMember.LastName = mr.user.lastname;
                accountMember.Email = mr.user.email;
                accountMember.MasterID = new Guid(userId);
                accountMember.isActive = mr.user?.isActive?.ToLower() == "true" ? true : false;
                accountMember.Account = new AccountInformation();

                AccountProfile profile;
                GetProfiles(userId, token).Wait();
                if (ProfileResult.success)
                {
                    int profileIndex = 1;
                    accountMember.Account.Profiles = new List<AccountProfile>();
                    if (ProfileResult.profiles != null && ProfileResult.profiles.Count > 0)
                    {
                        //load the profile for only the id stored in cookie, if no cookie, load only the first one
                        APIProfile apf = new APIProfile();
                        APIProfile apA = ProfileResult.profiles.FirstOrDefault(p => p.id.Equals(selectedProfileId));
                        if (apA != null)
                        {
                            apf = apA;
                        }
                        else
                        {
                            apf = ProfileResult.profiles.FirstOrDefault();
                            selectedProfileId = apf.id;
                            //CookieHelper.StoreInCookie(Context, "ActiveAccountProfileId", Encryption.RijndaelEncryption.Encrypt(apf.id, accountMember.MasterID.ToString()), null, null, DateTime.Now.AddHours(2), false, SameSiteMode.None, true);
                            _httpContextAccessor.HttpContext.Items["ActiveAccountProfileId"] = ApiHelper.EncryptString(apf.id, accountMember.MasterID.ToString());
                        }

                        foreach (APIProfile ap in ProfileResult.profiles)
                        {
                            if (ap.id == selectedProfileId)
                                profile = ConvertProfile(profileIndex, ap, token, accountMember.FirstName, accountMember.LastName, accountMember.Email, languagePreferred);
                            else
                                profile = ConvertProfileSimple(profileIndex, ap, token, accountMember.FirstName, accountMember.LastName, accountMember.Email, languagePreferred);


                            accountMember.Account.Profiles.Add(profile);
                            profileIndex++;
                        }
                    }

                    //if (ProfileResult.administers != null && ProfileResult.administers.Count > 0)
                    //{
                    //    foreach (APIProfile ap in ProfileResult.administers)
                    //    {
                    //        profile = ConvertProfile(profileIndex, eAccountProfileType.Manager, ap, token, firstName, lastName, email, languagePreferred);
                    //        accountMember.Account.Profiles.Add(profile);
                    //        profileIndex++;
                    //    }
                    //}

                }
            }

            return accountMember;
        }

        public AccountProfile GetActiveProfile(AccountMember member)
        {
            string accid = string.Empty;
            return GetActiveProfile(member, out accid);
        }

        public AccountProfile GetActiveProfile(AccountMember member, out string accid)
        {
            //AccountProfile acc = (AccountProfile)Context.Items["ActiveAccountProfile" + member.Email];

            //if (acc != null)
            //    return acc;
            AccountProfile acc = null;

            accid = string.Empty;
            string selectedProfileId = _httpContextAccessor.HttpContext.Items["ActiveAccountProfileId"] as string;
            //CookieHelper.GetFromCookie(Context, "ActiveAccountProfileId");
            if (member != null)
            {

                if (!string.IsNullOrEmpty(selectedProfileId))
                {
                    try
                    {
                        selectedProfileId = ApiHelper.DecryptString(selectedProfileId, member.MasterID.ToString());
                        accid = selectedProfileId;
                    }
                    catch
                    {
                    }
                }

                if (!string.IsNullOrEmpty(accid))
                {
                    try
                    {
                        acc = member.Account.Profiles.FirstOrDefault(ee => ee.ExternalId.Equals(selectedProfileId));
                        //if (acc.Workflows != null && acc.Workflows.Count > 0 && acc.InitialWorkflowsCompleted)
                        //    acc = InitialWorkflowsAllCompleted(acc);
                    }
                    catch
                    {
                        acc = null;
                    }
                }

            }
            if (member == null || member.Account == null || member.Account.Profiles == null || member.Account.Profiles.Count == 0)
                return acc;

            if (acc == null)
            {
                acc = member.Account.Profiles[0];
                selectedProfileId = acc.ExternalId.ToString();
            }



            //CookieHelper.StoreInCookie(Context, "ActiveAccountProfileId",
            //    Encryption.RijndaelEncryption.Encrypt(acc.ExternalId, member.MasterID.ToString())
            //    , null, null, DateTime.Now.AddHours(2), false, SameSiteMode.None, true);
            _httpContextAccessor.HttpContext.Items["ActiveAccountProfileId"] = ApiHelper.EncryptString(acc.ExternalId, member.MasterID.ToString());
            //Context.Items["ActiveAccountProfile" + member.Email] = acc;
            return acc;
        }

        public void SetActiveProfile(AccountMember member, string accountProfileId)
        {
            AccountProfile acc = null;

            if (member == null || member.Account == null || member.Account.Profiles == null || member.Account.Profiles.Count == 0)
            {
                //CookieHelper.StoreInCookie(Context, "ActiveAccountProfileId", string.Empty, null, null, DateTime.Now.AddHours(2), false, SameSiteMode.None, true);
                _httpContextAccessor.HttpContext.Items["ActiveAccountProfileId"] = string.Empty;
                return;
            }
            if (!string.IsNullOrEmpty(accountProfileId))
            {
                Account account;
                acc = member.Account.Profiles.FirstOrDefault(ee => ee.ExternalId.Equals(accountProfileId));
                member.Account.Profiles.Remove(acc);
                if (string.IsNullOrEmpty(acc.AlphaCCOKey))
                {
                    acc.Accounts = new List<Account>();

                    if (BearerTokenResponse == null || BearerTokenResponse.access_token == null)
                    {
                        GetBearerToken();
                    }
                    GetAccounts(acc.ExternalId, BearerTokenResponse.access_token, true).Wait();
                    if (AccountResult.success)
                    {
                        int accIndex = 0;
                        if (AccountResult.accounts != null && AccountResult.accounts.Count > 0)
                        {

                            foreach (APIAccount aa in AccountResult.accounts)
                            {
                                account = new Account();

                                account.AccountCode = aa.accountCode;
                                account.AccountName = aa.name;
                                account.AccountVisualizationID = aa.accountVisualizationKey;
                                account.FriendlyName = aa.friendlyName;
                                account.AccountType = aa.type;
                                account.Currency = aa.accountCurrency;
                                account.ID = accIndex;
                                acc.Accounts.Add(account);

                                accIndex++;
                            }
                        }
                    }
                }
                else
                {
                    if (BearerTokenResponse == null || BearerTokenResponse.access_token == null)
                    {
                        GetBearerToken();
                    }
                    APIProfile apf = new APIProfile();

                    apf.canBypassInitialWorkflow = acc.CanBypassInitialWorkflows ? "true" : "false";
                    apf.name = acc.Name;
                    apf.alphaccoKey = acc.AlphaCCOKey;
                    apf.initialWorkflowCompleted = acc.InitialWorkflowsCompleted ? "true" : "false";
                    apf.type = acc.ProfileType.ToString();
                    apf.isActive = acc.IsActive ? "true" : "false";
                    apf.pk = acc.Organization;
                    apf.id = acc.ExternalId.ToString();


                    acc = ConvertProfile(acc.SortOrder, apf, BearerTokenResponse.access_token, member.FirstName, member.LastName, member.Email, acc.PreferredLanguage, true);
                }

                //add the updated profile data back to AccountMember record,

                member.Account.Profiles.Add(acc);
                //IdentityExtension.ResetCurrentSignedInAccountMember(member);
                this.CurrentSignedInAccountMember = member;
            }

            if (acc == null)
                acc = member.Account.Profiles[0];

            //CookieHelper.StoreInCookie(Context, "ActiveAccountProfileId", Encryption.RijndaelEncryption.Encrypt(acc.ExternalId, member.MasterID.ToString()), null, null, DateTime.Now.AddDays(2), false, SameSiteMode.None, true);
            _httpContextAccessor.HttpContext.Items["ActiveAccountProfileId"] = ApiHelper.EncryptString(acc.ExternalId, member.MasterID.ToString());
            // Context.Items["ActiveAccountProfile" + member.Email] = acc;
            return;
        }

        private AccountProfile ConvertProfile(int profileIndex, APIProfile ap, string token, string firstName, string lastName, string email, string language, bool isBearerToken = false)
        {
            WorkflowEngine we = new WorkflowEngine(DomainId, LanguageId, _applicationSettingsService, _httpContextAccessor);
            Account account;
            AccountProfile profile = new AccountProfile();
            AlphaCCOExternalPartyResponse memberInfo = null;
            profile.Name = ap.name;
            if (string.IsNullOrEmpty(ap.alphaccoKey))
            {
                profile.CanBypassInitialWorkflows = true;  //this is a workaround for now to make sure we are not populate AlphaCCO Key for the Profile

                //Todo: need call profileUpdate to set this change;
            }
            else
            {
                memberInfo = we.GetAlphaCCOAccountByWorkflowKey(ap.alphaccoKey);
                profile.CanBypassInitialWorkflows = ap.canBypassInitialWorkflow.ToLower() == "true" ? true : false;
            }

            if (memberInfo != null)
            {
                profile.FirstName = memberInfo.firstName;
                profile.LastName = memberInfo.lastName;
                profile.Email = memberInfo.email;
                profile.PreferredLanguage = memberInfo.language;
            }
            else
            {
                profile.FirstName = firstName;
                profile.LastName = lastName;
                profile.Email = email;
                profile.PreferredLanguage = language;
            }


            profile.ProfileID = ap.id;
            profile.ProfileType = ap.type.ToLower() == "owner" ? eAccountProfileType.Owner : ap.type.ToLower() == "admin" ? eAccountProfileType.Manager : eAccountProfileType.Viewer;
            profile.Organization = ap.pk;
            profile.AlphaCCOKey = ap.alphaccoKey;
            profile.ExternalId = ap.id;
            profile.ID = profileIndex;
            profile.InitialWorkflowsCompleted = ap.initialWorkflowCompleted.ToLower() == "true" ? true : false;
            profile.CanBypassInitialWorkflows = ap.canBypassInitialWorkflow.ToLower() == "true" ? true : false;
            profile.SortOrder = profileIndex;

            profile.IsActive = ap.isActive.ToLower() == "true" ? true : false;
            profile.MasterID = Guid.NewGuid();

            profile.Accounts = new List<Account>();
            if (profile.AlphaCCOKey != null)
            {
                profile.Workflows = we.GetAlphaCCOWorkflows(profile, null, null, null);
                profile = InitialWorkflowsAllCompleted(profile);
            }
            GetAccounts(profile.ExternalId, token, isBearerToken).Wait();
            if (AccountResult.success)
            {
                int accIndex = 0;
                if (AccountResult.accounts != null && AccountResult.accounts.Count > 0)
                {
                    foreach (APIAccount aa in AccountResult.accounts)
                    {
                        account = new Account();

                        account.AccountCode = aa.accountCode;
                        account.AccountName = aa.name;
                        account.AccountVisualizationID = aa.accountVisualizationKey;
                        account.FriendlyName = aa.friendlyName;
                        account.AccountType = aa.type;
                        account.Currency = aa.accountCurrency;
                        account.ID = accIndex;
                        profile.Accounts.Add(account);

                        accIndex++;
                    }
                }
            }

            return profile;
        }

        private AccountProfile ConvertProfileSimple(int profileIndex, APIProfile ap, string token, string firstName, string lastName, string email, string language, bool isBearerToken = false)
        {
            WorkflowEngine we = new WorkflowEngine(DomainId, LanguageId, _applicationSettingsService, _httpContextAccessor);
            Account account;
            AccountProfile profile = new AccountProfile();
            AlphaCCOExternalPartyResponse memberInfo = null;
            profile.Name = ap.name;
            if (string.IsNullOrEmpty(ap.alphaccoKey))
            {
                profile.CanBypassInitialWorkflows = true;  //this is a workaround for now to make sure we are not populate AlphaCCO Key for the Profile

                //Todo: need call profileUpdate to set this change;
            }
            else
            {
                //memberInfo = we.GetAlphaCCOAccountByAlphaCCOKey(ap.alphaccoKey);
                profile.CanBypassInitialWorkflows = ap.canBypassInitialWorkflow.ToLower() == "true" ? true : false;
            }

            if (memberInfo != null)
            {
                profile.FirstName = memberInfo.firstName;
                profile.LastName = memberInfo.lastName;
                profile.Email = memberInfo.email;
                profile.PreferredLanguage = memberInfo.language;
            }
            else
            {
                profile.FirstName = firstName;
                profile.LastName = lastName;
                profile.Email = email;
                profile.PreferredLanguage = language;
            }


            profile.ProfileID = ap.id;
            profile.ProfileType = ap.type.ToLower() == "owner" ? eAccountProfileType.Owner : ap.type.ToLower() == "admin" ? eAccountProfileType.Manager : eAccountProfileType.Viewer;
            profile.Organization = ap.pk;
            profile.AlphaCCOKey = ap.alphaccoKey;
            profile.ExternalId = ap.id;
            profile.ID = profileIndex;
            profile.InitialWorkflowsCompleted = ap.initialWorkflowCompleted.ToLower() == "true" ? true : false;
            profile.CanBypassInitialWorkflows = ap.canBypassInitialWorkflow.ToLower() == "true" ? true : false;
            profile.SortOrder = profileIndex;

            profile.IsActive = ap.isActive.ToLower() == "true" ? true : false;
            profile.MasterID = Guid.NewGuid();

            profile.Accounts = new List<Account>();
            //if (profile.AlphaCCOKey != null)
            //{
            //    profile.Workflows = we.GetAlphaCCOWorkflows(profile, null, null, null);
            //}
            //GetAccounts(profile.ExternalId, token, isBearerToken);
            //if (AccountResult.success)
            //{
            //    int accIndex = 0;
            //    if (AccountResult.accounts != null && AccountResult.accounts.Count > 0)
            //    {
            //        foreach (APIAccount aa in AccountResult.accounts)
            //        {
            //            account = new Account();

            //            account.AccountCode = aa.accountCode;
            //            account.AccountName = aa.name;
            //            account.AccountVisualizationID = aa.accountVisualizationKey;
            //            account.FriendlyName = aa.friendlyName;
            //            account.AccountType = aa.type;
            //            account.Currency = aa.accountCurrency;
            //            account.ID = accIndex;
            //            profile.Accounts.Add(account);

            //            accIndex++;
            //        }
            //    }
            //}

            return profile;
        }

        private AccountProfile ConvertProfileWithAccounts(int profileIndex, APIProfile ap, string token, string firstName, string lastName, string email, string language, bool isBearerToken = false)
        {
            WorkflowEngine we = new WorkflowEngine(DomainId, LanguageId, _applicationSettingsService, _httpContextAccessor);
            Account account;
            AccountProfile profile = new AccountProfile();
            AlphaCCOExternalPartyResponse memberInfo = null;
            profile.Name = ap.name;
            if (string.IsNullOrEmpty(ap.alphaccoKey))
            {
                profile.CanBypassInitialWorkflows = true;  //this is a workaround for now to make sure we are not populate AlphaCCO Key for the Profile

                //Todo: need call profileUpdate to set this change;
            }
            else
            {
                //memberInfo = we.GetAlphaCCOAccountByAlphaCCOKey(ap.alphaccoKey);
                profile.CanBypassInitialWorkflows = ap.canBypassInitialWorkflow.ToLower() == "true" ? true : false;
            }

            if (memberInfo != null)
            {
                profile.FirstName = memberInfo.firstName;
                profile.LastName = memberInfo.lastName;
                profile.Email = memberInfo.email;
                profile.PreferredLanguage = memberInfo.language;
            }
            else
            {
                profile.FirstName = firstName;
                profile.LastName = lastName;
                profile.Email = email;
                profile.PreferredLanguage = language;
            }


            profile.ProfileID = ap.id;
            profile.ProfileType = ap.type.ToLower() == "owner" ? eAccountProfileType.Owner : ap.type.ToLower() == "admin" ? eAccountProfileType.Manager : eAccountProfileType.Viewer;
            profile.Organization = ap.pk;
            profile.AlphaCCOKey = ap.alphaccoKey;
            profile.ExternalId = ap.id;
            profile.ID = profileIndex;
            profile.InitialWorkflowsCompleted = ap.initialWorkflowCompleted.ToLower() == "true" ? true : false;
            profile.CanBypassInitialWorkflows = ap.canBypassInitialWorkflow.ToLower() == "true" ? true : false;
            profile.SortOrder = profileIndex;

            profile.IsActive = ap.isActive.ToLower() == "true" ? true : false;
            profile.MasterID = Guid.NewGuid();

            profile.Accounts = new List<Account>();
            //if (profile.AlphaCCOKey != null)
            //{
            //    profile.Workflows = we.GetAlphaCCOWorkflows(profile, null, null, null);
            //}
            GetAccounts(profile.ExternalId, token, isBearerToken);
            if (AccountResult.success)
            {
                int accIndex = 0;
                if (AccountResult.accounts != null && AccountResult.accounts.Count > 0)
                {
                    foreach (APIAccount aa in AccountResult.accounts)
                    {
                        account = new Account();

                        account.AccountCode = aa.accountCode;
                        account.AccountName = aa.name;
                        account.AccountVisualizationID = aa.accountVisualizationKey;
                        account.FriendlyName = aa.friendlyName;
                        account.AccountType = aa.type;
                        account.Currency = aa.accountCurrency;
                        account.ID = accIndex;
                        profile.Accounts.Add(account);

                        accIndex++;
                    }
                }
            }

            return profile;
        }

        private AlphaCCOWorkflows _mandatoryworkflows;
        private AlphaCCOWorkflows Mandatoryworkflows
        {
            get
            {
                if (_mandatoryworkflows == null)
                {
                    _applicationSettingsService.SetAlphaCCOWorkflowsInHttpContext();
                    _mandatoryworkflows = (AlphaCCOWorkflows)_applicationSettingsService.GetAlphaCCOWorkflows();
                    //.Where(w => w.IsMandatory).ToList();
                    if (_mandatoryworkflows.Count > 0)
                    {
                        _mandatoryworkflows.Where(w => w.IsMandatory).ToList();
                    }
                }
                return _mandatoryworkflows;
            }
        }

        public AccountProfile InitialWorkflowsAllCompleted(AccountProfile ap)
        {
            bool rtn = true;
            if (ap.ProfileType == eAccountProfileType.Owner)
            {
                if (ap.Workflows != null && ap.Workflows.Count > 0)
                {
                    foreach (AlphaCCOWorkflow w in Mandatoryworkflows)
                    {
                        if (ap.Workflows.FirstOrDefault(f => f.workflowKey.Equals(w.WorkflowKey) && f.statusCode == "C") != null)
                        {
                            rtn = rtn && true;
                        }
                        else
                        {
                            rtn = false;
                        }
                    }
                }
                else
                {
                    rtn = false;
                }
            }
            if (rtn && !ap.InitialWorkflowsCompleted)
            {
                ap.InitialWorkflowsCompleted = true;

                // no need to check changes anymore
                //
                //string message = string.Empty;
                //var status = am.ValidateLockStatus(am, base.CurrentSql, base.DomainId, base.LanguageId, out message);

                //if (status.Status == AdvantageCMS.Core.Admin.Event.eCMSEngineEventStatus.Success)
                //    ModuleEngine.BusinessHelper.PublishObject(ClientConstants.ServiceAccount, am);
                //else
                //{
                //    ///do something to reload the object and update
                //    ///
                //    IdentityExtension.ResetCurrentSignedInAccountMember();
                //    am = IdentityExtension.CurrentSignedInAccountMember(this.CurrentPage);
                //    profile = am.Account.Profiles.FirstOrDefault(a => a.Name.Equals(ap.Name));
                //    profile.InitialWorkflowsCompleted = true;
                //    status = am.ValidateLockStatus(am, base.CurrentSql, base.DomainId, base.LanguageId, out message);
                //}
            }

            return ap;
        }


        public BearerTokenResponse GetBearerToken()
        {
            GetBearerTokenTask().Wait();
            return BearerTokenResponse;
        }

        #region service calls
        public async Task<ProfileResponse> GetProfilesApi(
          string userId,
          string token,
          bool isBearerToken = false,
          bool withOrganization = true,
          bool withAccounts = true)
        {
            await GetProfiles(userId, token, isBearerToken, withOrganization, withAccounts).ConfigureAwait(false);
            return ProfileResult;
        }
        public async Task<AccountResponse> GetAccountsApi(string profile_id, string token, bool isBearerToken = false)
        {
            await GetAccounts(profile_id, token, isBearerToken).ConfigureAwait(false);
            return AccountResult;
        }
        private async Task GetProfiles(
          string userId,
          string token,
          bool isBearerToken = false,
          bool withOrganization = true,
          bool withAccounts = true)
        {

            string path = string.Format("{0}/{1}", FilteredTenant?.Settings?.AzureAdB2C?.BaseURL, FilteredTenant?.Settings?.AzureAdB2C?.GETProfilesForUserURL);
            path = path.Replace("{userId}", userId);
            var client = new RestClient(path);

            var request = new RestRequest();
            request.Method = Method.Get;// Method.GET

            request.AddHeader("Ocp-Apim-Subscription-Key", FilteredTenant?.Settings?.AzureAdB2C?.APIMKey);
            request.AddHeader("Authorization", string.Format("Bearer {0}", token));
            request.AddQueryParameter("withOrganization", withOrganization.ToString().ToLower());
            request.AddQueryParameter("withAccounts", withAccounts.ToString().ToLower());
            if (isBearerToken)
                request.AddHeader("Application-Id", FilteredTenant?.Settings?.AzureAdB2C?.BearerTokenApplicationName);

            try
            {
                RestResponse response = client.Execute(request);
                if (!response.IsSuccessful)
                {
                    string er = FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOBaseURL + " - " + response.StatusCode + " - " + response.ErrorMessage;
                    ProfileResult = new ProfileResponse();
                    ProfileResult.success = false;

                }

                ProfileResult = DeserializeProfileResponse(response.Content);
            }
            catch (Exception ex)
            {
                ProfileResult = new ProfileResponse();
                ProfileResult.success = false;
            }
        }
        private async Task UpdateMember(string token, AccountMember ac, string langaguePrefer)
        {
            string path = string.Format("{0}{1}", FilteredTenant?.Settings?.AzureAdB2C?.BaseURL, FilteredTenant?.Settings?.AzureAdB2C?.UPDATEMemberURL);
            path = path.Replace("{userId}", ac.MasterID.ToString());
            var client = new RestClient(path);

            var request = new RestRequest();
            request.Method = Method.Put;// Method.Post

            request.AddHeader("Ocp-Apim-Subscription-Key", FilteredTenant?.Settings?.AzureAdB2C?.APIMKey);
            request.AddHeader("Authorization", string.Format("Bearer {0}", token));

            Member body = new Member();
            body.isActive = ac.isActive ? "true" : "false";
            body.languagePreference = langaguePrefer;

            string requeststr = JsonConvert.SerializeObject(body, Formatting.None,
                            new JsonSerializerSettings
                            {
                                NullValueHandling = NullValueHandling.Ignore
                            });
            //var body = string.Format("{{ workflowkey: null,workflowInstancekey: null,externalPartyKey: \"{1}\" }}", string.IsNullOrEmpty(workflowKey) ? null : workflowKey, alphaccokey);
            request.AddParameter("application/json", requeststr, ParameterType.RequestBody);
            try
            {
                RestResponse response = client.Execute(request);
                if (!response.IsSuccessful)
                {
                    string er = path + " - " + response.StatusCode + " - " + response.ErrorMessage;
                    MemberResult = new MemberResponse();
                    MemberResult.success = false;

                }

                MemberResult = DeserializeMemberResponse(response.Content);
            }
            catch (Exception ex)
            {
                MemberResult = new MemberResponse();
                MemberResult.success = false;
            }
        }
        private async Task GetAccounts(string profileid, string token, bool isBearerToken = false)
        {
            string path = string.Format("{0}/{1}", FilteredTenant?.Settings?.AzureAdB2C?.BaseURL, FilteredTenant?.Settings?.AzureAdB2C?.GETAccountsForProfileURL);
            path = path.Replace("{profileId}", profileid);
            var client = new RestClient(path);

            var request = new RestRequest();
            request.Method = Method.Get;// Method.Post

            request.AddHeader("Ocp-Apim-Subscription-Key", FilteredTenant?.Settings?.AzureAdB2C?.APIMKey);
            request.AddHeader("Authorization", string.Format("Bearer {0}", token));
            if (isBearerToken)
                request.AddHeader("Application-Id", FilteredTenant?.Settings?.AzureAdB2C?.BearerTokenApplicationName);
            try
            {
                RestResponse response = client.Execute(request);
                if (!response.IsSuccessful)
                {
                    string er = FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOBaseURL + " - " + response.StatusCode + " - " + response.ErrorMessage;
                    AccountResult = new AccountResponse();
                    AccountResult.success = false;

                }

                AccountResult = DeserializeAccountResponse(response.Content);
            }
            catch (Exception ex)
            {
                AccountResult = new AccountResponse();
                AccountResult.success = false;
            }
        }

        private async Task GetMembers(string token)
        {
            string path = string.Format("{0}/{1}", FilteredTenant?.Settings?.AzureAdB2C?.BaseURL, FilteredTenant?.Settings?.AzureAdB2C?.GETMembersBriefURL);//Check the URL

            var client = new RestClient(path);

            var request = new RestRequest();
            request.Method = Method.Get;// Method.GET

            request.AddHeader("Ocp-Apim-Subscription-Key", FilteredTenant?.Settings?.AzureAdB2C?.APIMKey);
            request.AddHeader("Authorization", string.Format("Bearer {0}", token));

            try
            {
                RestResponse response = client.Execute(request);
                if (!response.IsSuccessful)
                {
                    string er = FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOBaseURL + " - " + response.StatusCode + " - " + response.ErrorMessage;
                    MembersResult = new MembersResponse();
                    MembersResult.success = false;

                }

                MembersResult = DeserializeMembersResponse(response.Content);
            }
            catch (Exception ex)
            {
                MembersResult = new MembersResponse();
                MembersResult.success = false;
            }
        }
        private async Task GetMember(string userId, string token, bool isBearerToken = false)
        {
            string path = string.Format("{0}/{1}", FilteredTenant?.Settings?.AzureAdB2C?.BaseURL, FilteredTenant?.Settings?.AzureAdB2C?.GETMemberURL);
            path = path.Replace("{userid}", userId);
            var client = new RestClient(path);
            var request = new RestRequest();
            request.Method = Method.Get;// Method.GET

            request.AddHeader("Ocp-Apim-Subscription-Key", FilteredTenant?.Settings?.AzureAdB2C?.APIMKey);
            request.AddHeader("Authorization", string.Format("Bearer {0}", token));

            if (isBearerToken)
                request.AddHeader("Application-Id", FilteredTenant?.Settings?.AzureAdB2C?.BearerTokenApplicationName);
            try
            {
                RestResponse response = client.Get(request);
                if (!response.IsSuccessful)
                {
                    string er = FilteredTenant?.Settings?.AlphaCC0?.AlphaCCOBaseURL + " - " + response.StatusCode + " - " + response.ErrorMessage;
                    MembersResult = new MembersResponse();
                    MembersResult.success = false;

                }

                MemberResult = DeserializeMemberResponse(response.Content);
            }
            catch (Exception ex)
            {
                MemberResult = new MemberResponse();
                MemberResult.success = false;
            }
        }

        private async Task GetBearerTokenTask()
        {
            var AzureB2CSettings = FilteredTenant.Settings?.AzureAdB2C;
            string path = string.Format(AzureB2CSettings.BearerTokenBaseURL, AzureB2CSettings.BearerTokenTenant);
            try
            {
                var client = new RestClient(path);

                var request = new RestRequest();
                request.Method = Method.Post;//Method.Post
                request.AddHeader("APP", "");
                request.AddHeader("Content-Type", "application/x-www-form-urlencoded");
                request.AddParameter("client_id", AzureB2CSettings.BearerTokenClientID);
                request.AddParameter("client_secret", AzureB2CSettings.BearerTokenClientSecret);
                request.AddParameter("grant_type", AzureB2CSettings.BearerTokenGrantType);
                request.AddParameter("scope", AzureB2CSettings.BearerTokenScope);


                RestResponse response = client.Execute(request);

                if (!response.IsSuccessful)
                {
                    BearerTokenResponse = new BearerTokenResponse();
                    BearerTokenResponse.access_token = string.Empty;

                }

                BearerTokenResponse = DeserializeBearerTokenResponse(response.Content);
            }
            catch (Exception ex)
            {
                BearerTokenResponse = new BearerTokenResponse();
                BearerTokenResponse.access_token = string.Empty;
            }
        }

        private async Task GetMemberListTask(string token)
        {
            var AzureB2CSettings = FilteredTenant?.Settings?.AzureAdB2C;
            var AlphaCCOSettings = FilteredTenant?.Settings?.AlphaCC0;
            string path = string.Format("{0}/organization/{1}/{2}", AzureB2CSettings?.BaseURL, AzureB2CSettings?.OrganizationId, AzureB2CSettings.GETMemberListURL);
            var client = new RestClient(path);

            var request = new RestRequest();
            request.Method = Method.Get;//Method.GET

            request.AddHeader("Ocp-Apim-Subscription-Key", AzureB2CSettings?.APIMKey);
            request.AddHeader("Application-Id", AzureB2CSettings?.BearerTokenApplicationName);
            request.AddHeader("Authorization", string.Format("Bearer {0}", token));

            try
            {
                RestResponse response = client.Execute(request);
                if (!response.IsSuccessful)
                {
                    string er = AlphaCCOSettings?.AlphaCCOBaseURL + " - " + response.StatusCode + " - " + response.ErrorMessage;
                    MembersResult = new MembersResponse();
                    MembersResult.success = false;

                }

                MembersResult = DeserializeMembersResponse(response.Content);
            }
            catch (Exception ex)
            {
                MembersResult = new MembersResponse();
                MembersResult.success = false;
            }
        }

        private async Task GetMemberDetailListTask(string token)
        {
            var AzureB2CSettings = FilteredTenant?.Settings?.AzureAdB2C;
            var AlphaCCOSettings = FilteredTenant?.Settings?.AlphaCC0;
            string path = string.Format("{0}/organization/{1}/{2}", AzureB2CSettings?.BaseURL, AzureB2CSettings?.OrganizationId, AzureB2CSettings?.GETMembersBriefURL);
            var client = new RestClient(path);

            var request = new RestRequest();
            request.Method = Method.Get;//Method.GET

            request.AddHeader("Ocp-Apim-Subscription-Key", AzureB2CSettings?.APIMKey);
            request.AddHeader("Application-Id", AzureB2CSettings?.BearerTokenApplicationName);
            request.AddHeader("Authorization", string.Format("Bearer {0}", token));

            try
            {
                RestResponse response = client.Execute(request);
                if (!response.IsSuccessful)
                {
                    string er = AlphaCCOSettings?.AlphaCCOBaseURL + " - " + response.StatusCode + " - " + response.ErrorMessage;
                    MembersDetailResult = new MemberDetailResponse();
                    MembersDetailResult.success = false;
                }

                MembersDetailResult = DeserializeMemberDetailResponse(response.Content);
            }
            catch (Exception ex)
            {
                MembersDetailResult = new MemberDetailResponse();
                MembersDetailResult.success = false;
            }
        }

        private ProfileResponse DeserializeProfileResponse(string json)
        {
            ProfileResponse obj;
            //JavaScriptSerializer oJS = new JavaScriptSerializer();
            obj = JsonConvert.DeserializeObject<ProfileResponse>(json);
            return obj;
        }
        private AccountResponse DeserializeAccountResponse(string json)
        {
            AccountResponse obj;
            //JavaScriptSerializer oJS = new JavaScriptSerializer();
            obj = JsonConvert.DeserializeObject<AccountResponse>(json);
            return obj;
        }
        private MembersResponse DeserializeMembersResponse(string json)
        {
            MembersResponse obj;
            //JavaScriptSerializer oJS = new JavaScriptSerializer();
            obj = JsonConvert.DeserializeObject<MembersResponse>(json);
            return obj;
        }
        private MemberResponse DeserializeMemberResponse(string json)
        {
            MemberResponse obj;
            //JavaScriptSerializer oJS = new JavaScriptSerializer();
            obj = JsonConvert.DeserializeObject<MemberResponse>(json);
            return obj;
        }
        private BearerTokenResponse DeserializeBearerTokenResponse(string json)
        {
            BearerTokenResponse obj;
            //JavaScriptSerializer oJS = new JavaScriptSerializer();
            obj = JsonConvert.DeserializeObject<BearerTokenResponse>(json);
            return obj;
        }

        private MemberDetailResponse DeserializeMemberDetailResponse(string json)
        {
            MemberDetailResponse obj = new MemberDetailResponse();
            try
            {
                //JavaScriptSerializer oJS = new JavaScriptSerializer();
                obj = JsonConvert.DeserializeObject<MemberDetailResponse>(json);
            }
            catch (Exception ex)
            {

            }
            return obj;
        }
        #endregion


    }
    public class TokenObject
    {
        public string? Token { get; set; }
        public DateTime Expire { get; set; }
    }
    [Serializable]
    public class APIProfile
    {
        public string? canBypassInitialWorkflow { get; set; }
        public string? name { get; set; }
        public string? alphaccoKey { get; set; }
        public string? initialWorkflowCompleted { get; set; }
        public string? type { get; set; }
        public string? isActive { get; set; }
        public string? pk { get; set; }
        public string? id { get; set; }
        public string? pydioCellPath { get; set; }
        public string? pydioCellId { get; set; }
        public bool? isLinkedToPydio { get; set; }
        public int? accountCount { get; set; }
    }

    [Serializable]
    public class APIProfileGroup
    {
        public string? id { get; set; }
        public string? name { get; set; }
        public string? pk { get; set; }
        public string? type { get; set; }
        public string? createdAt { get; set; }
        public string? updatedAt { get; set; }
        public string? isActive { get; set; }
        public int? accountCount { get; set; }
    }

    public class ProfileResponse
    {
        public bool success { get; set; }
        public List<APIProfile>? profiles { get; set; }
        public List<APIProfileGroup>? profileGroups { get; set; }
        //public List<APIProfile> administers { get; set; }
    }


    public class APIAccount
    {
        public string? name { get; set; }
        public string? type { get; set; }
        public string? accountCode { get; set; }
        public string? friendlyName { get; set; }
        public string? accountVisualizationKey { get; set; }
        public string? accountCurrency { get; set; }
        public string? isActive { get; set; }
        public string? pk { get; set; }
        public string? id { get; set; }
    }

    public class AccountResponse
    {
        public bool success { get; set; }
        public List<APIAccount>? accounts { get; set; }
    }

    public class MembersResponse
    {
        public bool success { get; set; }
        public List<Member>? users { get; set; }
    }

    public class MemberResponse
    {
        public bool success { get; set; }
        public Member? user { get; set; }
    }

    public class MemberDetailResponse
    {
        [JsonProperty("success")]
        public bool success { get; set; }

        [JsonProperty("data")]
        public DataBrief? data { get; set; }
    }

    public class DataBrief
    {
        [JsonProperty("users")]
        public List<MemberBriefData>? users { get; set; }
    }

    [Serializable]
    public class AccountBrief
    {
        [JsonProperty("accountId")]
        public string? accountId { get; set; }

        [JsonProperty("accountCode")]
        public string? accountCode { get; set; }
    }


    [Serializable]
    public class ProfileBrief
    {
        [JsonProperty("profileId")]
        public string? profileId { get; set; }

        [JsonProperty("profileName")]
        public string? profileName { get; set; }

        [JsonProperty("pk")]
        public string? pk { get; set; }

        [JsonProperty("initialWorkflowCompleted")]
        public string? initialWorkflowCompleted { get; set; }

        [JsonProperty("canBypassInitialWorkflow")]
        public string? canBypassInitialWorkflow { get; set; }

        [JsonProperty("type")]
        public string? type { get; set; }

        [JsonProperty("isActive")]
        public string? isActive { get; set; }

        [JsonProperty("accounts")]
        public List<AccountBrief>? accounts { get; set; }
    }


    [Serializable]
    public class MemberBriefData
    {
        [JsonProperty("isActive")]
        public bool isActive { get; set; }

        [JsonProperty("email")]
        public string? email { get; set; }

        [JsonProperty("isInviteSent")]
        public string? isInviteSent { get; set; }

        [JsonProperty("inviteSentDate")]
        public string? inviteSentDate { get; set; }

        [JsonProperty("firstName")]
        public string? firstName { get; set; }

        [JsonProperty("lastName")]
        public string? lastName { get; set; }

        [JsonProperty("languagePreference")]
        public string? languagePreference { get; set; }

        [JsonProperty("pk")]
        public string? pk { get; set; }

        [JsonProperty("id")]
        public string? id { get; set; }

        [JsonProperty("profiles")]
        public List<ProfileBrief>? profiles { get; set; }
    }


    [Serializable]
    public class Member
    {
        public string? email { get; set; }
        public string? firstname { get; set; }
        public string? lastname { get; set; }
        public string? isInviteSent { get; set; }
        public string? inviteSentDate { get; set; }
        public string? languagePreference { get; set; }
        public string? isActive { get; set; }
        public string? pk { get; set; }
        public string? id { get; set; }
        public string? pydioUsername { get; set; }
    }

    [Serializable]
    public class BearerTokenResponse
    {
        public string? token_type { get; set; }
        public string? expires_in { get; set; }
        public string? ext_expires_in { get; set; }
        public string? access_token { get; set; }
    }
}
