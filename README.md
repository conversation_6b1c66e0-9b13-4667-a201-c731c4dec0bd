﻿# 🚀 MicroSaaS .NET Core WebAPI Template

A comprehensive, production-ready .NET Core WebAPI template for building MicroSaaS applications with modern authentication, payment processing, and user management features.

## ✨ Features

### 🔐 Authentication & Authorization

- **Supabase Integration** - Complete authentication flow with JWT tokens
- **Role-based Access Control** - Admin and user roles with permission management
- **Email Verification** - Automated email verification workflow
- **Password Management** - Secure password reset and update functionality
- **Social Login Ready** - Extensible for Google, GitHub, and other providers

### 💳 Payment Processing

- **Clerk Integration** - One-time payments and subscription management
- **Multiple Plan Support** - Free, Basic, Premium, Enterprise tiers
- **Trial Periods** - Configurable trial periods for new users
- **Webhook Handling** - Automated payment event processing
- **Invoice Generation** - Automated billing and receipt management

### 👤 User Management

- **User Profiles** - Complete CRUD operations with Dapper ORM
- **Profile Images** - File upload and image management
- **Subscription Tracking** - Real-time subscription status monitoring
- **User Search** - Advanced user search and filtering

### 🛠 Technical Features

- **Clean Architecture** - Well-structured, maintainable codebase
- **Swagger Documentation** - Complete API documentation
- **Error Handling** - Global error handling and logging
- **Validation** - Comprehensive input validation
- **CORS Support** - Cross-origin resource sharing configuration
- **Health Checks** - Application health monitoring endpoints

## 🏗 Architecture

```
MicroSaasWebApi/
├── Controllers/MICROSAAS/          # API Controllers
│   ├── AuthController.cs           # Authentication endpoints
│   ├── PaymentController.cs        # Payment & subscription endpoints
│   ├── UserProfileController.cs    # User profile management
│   └── BaseController.cs           # Base controller with common functionality
├── Models/                         # Data models
│   ├── Auth/                       # Authentication models
│   ├── Payment/                    # Payment & subscription models
│   └── Profile/                    # User profile models
├── Services/MICROSAAS/             # Business logic services
│   └── Interface/                  # Service interfaces
└── Properties/Enums/               # Application enumerations
```

## 🚀 Quick Start

### Prerequisites

- .NET 8.0 SDK
- PostgreSQL database (for Supabase)
- Supabase account
- Clerk account (for payments)

### 1. Clone and Setup

```bash
git clone https://github.com/JavianDev/MicroSaasWebApi.git
cd MicroSaasWebApi
dotnet restore
```

### 2. Configuration

Update `appsettings.json` with your credentials:

```json
{
  "Supabase": {
    "Url": "your-supabase-url",
    "Key": "your-supabase-anon-key"
  },
  "Clerk": {
    "SecretKey": "your-clerk-secret-key",
    "WebhookSecret": "your-clerk-webhook-secret"
  },
  "ConnectionStrings": {
    "DefaultConnection": "your-database-connection-string"
  }
}
```

### 3. Database Setup

```bash
# Run database migrations (implement your preferred migration strategy)
dotnet ef database update
```

### 4. Run the Application

```bash
dotnet run
```

Visit `https://localhost:7000/swagger` to explore the API documentation.

## 📚 API Endpoints

### Authentication

- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Refresh access token
- `POST /api/auth/logout` - User logout
- `POST /api/auth/forgot-password` - Request password reset
- `POST /api/auth/verify-email` - Verify email address

### User Profile

- `GET /api/userprofile/me` - Get current user profile
- `PUT /api/userprofile/me` - Update current user profile
- `DELETE /api/userprofile/me` - Delete current user profile
- `POST /api/userprofile/me/upload-image` - Upload profile image

### Payments

- `GET /api/payment/plans` - Get subscription plans
- `POST /api/payment/subscribe` - Create subscription
- `GET /api/payment/subscription` - Get subscription status
- `POST /api/payment/subscription/cancel` - Cancel subscription
- `POST /api/payment/one-time` - Create one-time payment
- `GET /api/payment/history` - Get payment history

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Local Setup](#local-setup)
   - [Windows](#windows)
   - [macOS](#macos)
3. [Understanding program.cs](#understanding-programcs)
4. [Contributing](#contributing)
5. [References](#references)

## Prerequisites

Before you begin, make sure you have the following installed on your machine:

- .NET 7 SDK
- Docker Desktop

## Local Setup

Detailed steps with screenshots are added in the confluence documentation.

### Windows

1. Clone the repository to your local machine.
   git clone https://dev.azure.com/PortfolioXpressWay/PXW-Cloud-Apps/_git/MicroSaasWebApi
2. Navigate to the project folder in the command prompt.
3. Open the `launchsettings.json` file located in the `Properties` folder.Local ENV variables can be set here.
   For every environment get the .env.{client}.{environment}.launchsettings.json from the development team. If your target is PXW-UAT then use file
   .env.PXW.UAT.launchsettings.json and rename it to launchsettings.json.
   Copy the appsettingsjson file for the respective environment from Assets/folder.
4. Under the "Docker" profile, ensure that the `useSSL` property is set to `true`.
5. Open a command prompt window and navigate to the project directory.
6. Run the following command to build the Docker image:
   docker build -t project-name .
7. Once the build is complete, run the following command to start the Docker container:
   docker run -d -p 5000:80 project-name
8. The project should now be accessible at [http://localhost:5000/swagger](http://localhost:5000/swagger) or [https://localhost:5000/health](https://localhost:5000/health) in your web browser.

### macOS

1. Clone the repository to your local machine.
   git clone https://dev.azure.com/PortfolioXpressWay/PXW-Cloud-Apps/_git/MicroSaasWebApi
2. Navigate to the project folder in the command prompt.
3. For every environment get the .env.{client}.{environment}.launchsettings.json from the development team. If your target is PXW-UAT then use file
   .env.PXW.UAT.launchsettings.json and rename it to launchsettings.json.
   Copy the appsettingsjson file for the respective environment from Assets/folder.

4. Under the "Docker" profile, ensure that the `useSSL` property is set to `true`.
5. Open a command prompt window and navigate to the project directory.
6. Run the following command to build the Docker image:
   docker build -t project-name .
7. Once the build is complete, run the following command to start the Docker container:
   docker run -d -p 5000:80 project-name
8. The project should now be accessible at [http://localhost:5000/swagger](http://localhost:5000/swagger) or [https://localhost:5000/health](https://localhost:5000/health) in your web browser.

## Troubleshooting Local.

- Scenario 1:if you call the web api endpoint. ProfilesList & if you get response a
  {
  "success": true,
  "profiles": []
  } please check web api logs.
- Scenario 2:if you call the web api endpoint. ProfilesList & if you get response a
  {
  Tenant Url not found.
  } please check web api logs and see if you are able to retrieve values from Key Vault.
  Make sure the service principal i.e. AZURE_CLIENT_ID added in launch settings has access in the KeyVault & if Keyvault is restricted under private IP to access the web api.

## Understanding program.cs

The `program.cs` file is the entry point of the ASP.NET project. It contains the main logic for configuring services, middleware, and endpoints. Here's a brief overview of the methods and logic used in `program.cs`.
Let's break it down step by step:

### Initial Setup:

- Imports necessary namespaces and sets up initial configurations.

### Configuration Retrieval:

- It retrieves various configuration parameters from the app's configuration settings and environment variables. These include paths to certificates, target URLs, Azure App Configuration settings, Key Vault settings, etc.

### Service Configuration (ConfigureServices method):

- Sets up services required by the application using services.Add[ServiceName] methods.
- Configures HTTP client settings, such as timeouts, SSL protocols, and custom certificate validation.
- Adds controllers, JSON serialization settings, application cookie settings, response compression, health checks, HTTP context accessor, logging, directory browser, CORS policy, etc.
- Registers custom services and sets up Azure App Configuration and Key Vault integration.

### Azure App Configuration and Key Vault Integration (ConfigureAzureAppConfigurationAndKeyVault method):

- Checks for Azure App Configuration and Key Vault settings and configures the application to use them if available.
- Retrieves secrets from Azure Key Vault and populates configuration settings.
- If Azure App Configuration settings are provided, it configures the app to use managed identity or connection string. If Key Vault settings are provided, it fetches values directly from Key Vault.

The method `SetAppConfigurationWithKeyVault` is designed to retrieve configuration values from both environment variables and Azure Key Vault, and then use these values to populate a `filteredTenant` object. Here's a breakdown of how the method accomplishes this:

- **Retrieving Environment Variables:** The method begins by retrieving several environment variables set in App Services
  or Open the `launchsettings.json` file located in the `Properties` folder to execute code locally.Local ENV variables can be set here.in :

  - APPCONFIGURATION_ENDPOINT
  - APPCONFIGURATION_CONNECTIONSTRING
  - APPCONFIGURATION_ENVIRONMENT_LABEL
  - KEYVAULT_URI
  - AZURE_TENANT_ID
  - AZURE_CLIENT_ID
  - AZURE_CLIENT_SECRET
    These environment variables are used to determine whether the application should attempt to retrieve configuration values from Azure Key Vault.

- **Checking Azure Key Vault Availability:** If the required environment variables for connecting to Azure Key Vault are present (`kvUri`, `tenantId`, `clientId`, `clientSecret`), and if Azure App Configuration endpoint variables are not provided, the method proceeds to retrieve secrets from Azure Key Vault.

- **Retrieving Secrets from Azure Key Vault:** The method uses the `SecretClient` class to connect to Azure Key Vault using the provided credentials. It then attempts to retrieve several secrets from the Key Vault:

  - APIM-BASE-URL
  - APIM-KEY
  - APIM-SECRET-KEY
  - BEARER-TOKEN-CLIENTID
  - BEARER-TOKEN-CLIENT-SECRET
  - PORTFOLIO-DATA-API-DOMAIN
  - PORTFOLIO-DOCUMENT-API-DOMAIN
  - PORTFOLIO-PERMISSION-API-DOMAIN
  - PORTFOLIO-DATAINTERCHANGE-API-DOMAIN
    These secrets are critical configuration values used by the application.

- **Populating `filteredTenant` Object:** If all secrets are successfully retrieved and none of them are null or empty, the method assigns these values to corresponding properties of the `filteredTenant` object in method: SetAppSettingsFilteredTenantInHttpContext. If any secret is found to be null or empty, it throws an `InvalidOperationException` with an appropriate error message.

- **Error Handling:** The method handles potential errors that may occur during the retrieval of secrets from Azure Key Vault. It logs the error messages and provides appropriate feedback, such as when secrets are not found or access is denied. Overall, this method ensures that the application's configuration is securely managed, retrieving sensitive information from Azure Key Vault while providing proper error handling in case of any issues.

### Health Checks (AddHealthChecks method):

- Adds health checks to monitor the application's health.
- Registers custom health checks and sets up health checks UI endpoint.

### Response Compression (AddResponseCompression method):

- Configures response compression options to compress HTTP responses.

### Manual Service Registration (RegisterManualServices method):

- Registers manual services required by the application.

### App Settings Configuration (RegisterAppSettings method):

- Configures application settings using strongly typed objects.

### Kestrel Configuration (ConfigureKestrel method):

- Configures Kestrel server options like max request body size, max concurrent connections, etc.

### CORS Policy Configuration (AddCorsPolicy method):

- Configures Cross-Origin Resource Sharing (CORS) policy.

### Swagger Generation (AddSwaggerGen method):

- Configures Swagger/OpenAPI documentation generation for the API endpoints.
- Adds security definitions and requirements for JWT authentication.
- Enables XML comments and annotations for API documentation.

### Middleware Configuration (ConfigureMiddleware method):

- Configures middleware components for request processing.
- Sets up security headers, HTTPS redirection, static files serving, routing, authentication, etc.
- Configures middleware components for request processing.
- Registers custom middleware like token authentication middleware, redirects, etc.
- Sets up environment-specific configurations for development and production.
- Configures security headers and features.
- Configures HTTPS redirection, static files serving, routing, authentication, authorization, response compression, etc.
- Enables HSTS (HTTP Strict Transport Security) and Content Security Policy.

### Swagger UI Configuration (ConfigureSwaggerUI method):

- Configures Swagger UI for visualizing API documentation.
- Enables OAuth authentication for API testing.

### Health Checks Configuration (ConfigureHealthChecks method):

- Maps endpoints for health checks and health checks UI.
- Configures response statuses based on health check results.

## Contributing

Feel free to contribute to this project. Please fork the repository, make changes as you see fit, and submit a pull request.

## References

- [Enhance Swagger Documentation with Annotations in ASP.NET Core](https://medium.com/@niteshsinghal85/enhance-swagger-documentation-with-annotations-in-asp-net-core-d2981803e299)
- [Testing file upload with Swagger in ASP.Net core](https://medium.com/@niteshsinghal85/testing-file-upload-with-swagger-in-asp-net-core-90269bc24fe8)
- [azure-key-vault-configuration-and-implementation-using-net-core-7-web-api](https://www.c-sharpcorner.com/article/azure-key-vault-configuration-and-implementation-using-net-core-7-web-api/)
