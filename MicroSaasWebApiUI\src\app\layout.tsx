import { Providers } from '@/components/providers'
import { getSEOTags } from '@/libs/seo'
import { Clerk<PERSON>rovider } from '@clerk/nextjs'
import { Viewport } from 'next'
import { Inter } from 'next/font/google'
import { ReactNode } from 'react'
import { Analytics } from '@vercel/analytics/react'
import { SpeedInsights } from '@vercel/speed-insights/next'

import '@/assets/styles/globals.scss'
import { Header } from '@/layout'

const font = Inter({ subsets: ['latin'] })

export const viewport: Viewport = {
	themeColor: '#000000',
	width: 'device-width',
	initialScale: 1,
}

export const metadata = getSEOTags()

export default function RootLayout({ children }: { children: ReactNode }) {
	return (
		<html lang='en' suppressHydrationWarning>
			<head>
				{/* Google AdSense Script - Standard implementation */}
				<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-8317058180798912" crossOrigin="anonymous"></script>

				{/* Basic security meta tags */}
				<meta name="referrer" content="strict-origin-when-cross-origin" />

				{/* Content Security Policy to upgrade insecure requests */}
				<meta httpEquiv="Content-Security-Policy" content="upgrade-insecure-requests" />
			</head>
			<body className={`${font.className} bg-white text-black`}>
				<Providers>
					<ClerkProvider>
						<Header />
						<main className='min-h-screen pt-12 bg-white dark:bg-[#010814]'>{children}</main>
						<Analytics />
						<SpeedInsights />
					</ClerkProvider>
				</Providers>
			</body>
		</html>
	)
}
