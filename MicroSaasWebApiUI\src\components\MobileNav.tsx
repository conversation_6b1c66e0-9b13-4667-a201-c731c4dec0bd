'use client'

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@/components'
import NavLinks from '@/components/nav-links'
import { ScrollToSection } from '@/utils/scroll-to-section'
import {
  Sheet,
  She<PERSON><PERSON>ontent,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from '@/components/ui/sheet'
import { Blog, Demo, OpenNav, Pricing, RightArrow } from '@/icons'
import Link from 'next/link'

const nav_links = [
  // {
  //   icon: <Demo />,
  //   title: 'Demo',
  //   link: '/',
  // },
  // {
  //   icon: <Pricing />,
  //   title: 'Pricing',
  //   link: '/',
  // },
  {
    icon: <Blog width={18} height={18} />,
    title: 'Blog',
    link: '/blog',
  },
  {
    icon: (
      <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M18.5 8c.276 0 .5.224.5.5v1c0 .276-.224.5-.5.5h-13c-.276 0-.5-.224-.5-.5v-1c0-.276.224-.5.5-.5h13zm0 4c.276 0 .5.224.5.5v1c0 .276-.224.5-.5.5h-13c-.276 0-.5-.224-.5-.5v-1c0-.276.224-.5.5-.5h13zm0 4c.276 0 .5.224.5.5v1c0 .276-.224.5-.5.5h-13c-.276 0-.5-.224-.5-.5v-1c0-.276.224-.5.5-.5h13zm4.5-12v16c0 1.105-.895 2-2 2h-18c-1.105 0-2-.895-2-2v-16c0-1.105.895-2 2-2h18c1.105 0 2 .895 2 2zm-2 0h-18v16h18v-16z" fill="currentColor"/>
      </svg>
    ),
    title: 'Buy Me A Coffee',
    link: 'https://www.buymeacoffee.com/javian',
    external: true
  },
]

export const MobileNav = () => {
  return (
    <Sheet>
      <SheetTrigger>
        <div className='text-black1 dark:text-white'>
          <OpenNav />
        </div>
      </SheetTrigger>
      <SheetContent className='bg-white dark:bg-black1 px-0 pt-4 border-l-0 min-w-[320px]'>
        <SheetHeader>
          <SheetTitle className='text-black1 dark:text-white text-xl font-bold border-b border-[#b3b3b3] text-left pb-4 pl-4'>
            Menu
          </SheetTitle>
        </SheetHeader>
        <Link href='/' className='flex items-center gap-2 mt-8 mx-auto w-fit'>
          <Logo />
        </Link>
        <div className='my-8 mx-auto w-fit'>
          <NavLinks nav_links={nav_links} />
        </div>
        <div className='mb-8 mx-auto w-fit block'>
          <div
            dangerouslySetInnerHTML={{
              __html: `<a href="https://www.buymeacoffee.com/javian" target="_blank"><img src="https://cdn.buymeacoffee.com/buttons/v2/default-yellow.png" alt="Buy Me A Coffee" style="height: 40px !important;width: auto !important;" ></a>`
            }}
          />
        </div>
      </SheetContent>
    </Sheet>
  )
}

export default MobileNav