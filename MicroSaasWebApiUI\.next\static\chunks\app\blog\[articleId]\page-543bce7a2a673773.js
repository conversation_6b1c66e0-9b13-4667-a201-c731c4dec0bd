(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[433],{35952:function(e,t,r){Promise.resolve().then(r.bind(r,2817))},2817:function(e,t,r){"use strict";r.d(t,{default:function(){return f}});var l=r(57437);r(61957);var s=r(33145),a=r(27648),n=r(4023),i=r(16008),d=e=>{let{currentBlog:t,AllPosts:r}=e,d=r.filter(e=>e.title.rendered!==t);return(0,l.jsxs)("div",{className:"w-full max-w-[90rem] mx-auto px-4 sm:px-6 lg:px-8 mb-6",children:[(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsxs)("div",{className:"flex-1 mx-auto max-w-5xl pr-8",children:[(0,l.jsx)("h2",{className:"text-4xl font-bold mb-6 text-left",children:"Other Articles"}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:d.slice(0,4).map((e,t)=>(0,l.jsxs)(a.default,{href:"/blog/".concat(e.slug),className:"bg-white dark:bg-[#1E232C] rounded-xl overflow-hidden shadow-lg dark:border border-solid border-[#373C53] hover:border-[#5b6285] transition-all duration-500 transform hover:scale-105 ease-in-out",children:[(0,l.jsx)(s.default,{src:e.featured_img,alt:e.title.rendered,className:"object-cover",width:500,height:156}),(0,l.jsxs)("div",{className:"p-4",children:[(0,l.jsx)("h2",{className:"font-normal text-[16px] dark:text-white mb-2",children:e.title.rendered}),(0,l.jsx)("p",{className:"text-gray-400",children:(0,n.a)(e.date)})]})]},t))})]}),(0,l.jsx)("div",{className:"hidden lg:block w-[320px]"})]}),(0,l.jsx)("div",{className:"mt-6 max-w-5xl mx-auto",children:(0,l.jsx)(i.default,{type:"display-horizontal",className:"px-0"})})]})},o={src:"/_next/static/media/back-arrow.1d46893e.svg",height:16,width:20,blurWidth:0,blurHeight:0},c=e=>{var t,r,i,d;let{post:c}=e,{yoast_head_json:m}=c,u=null==m?void 0:null===(r=m.schema)||void 0===r?void 0:null===(t=r["@graph"])||void 0===t?void 0:t.find(e=>"Person"===e["@type"]),h=(null==u?void 0:u.name)||"Javian Picardo",x=(null==u?void 0:null===(i=u.image)||void 0===i?void 0:i.contentUrl)||"";return(0,l.jsxs)("div",{children:[(0,l.jsxs)(a.default,{href:"/blog",className:"flex items-center dark:text-gray-500 text-black1/70 gap-2 mb-4",children:[(0,l.jsx)(s.default,{alt:"Back Arrow",src:o,height:10,width:15}),(0,l.jsx)("span",{children:"Back to blog"})]}),(0,l.jsx)("h1",{className:"text-[42px] !leading-[1.19] font-bold mb-2",children:null==c?void 0:null===(d=c.title)||void 0===d?void 0:d.rendered}),(0,l.jsxs)("div",{className:"flex items-center mb-4",children:[x?(0,l.jsx)(s.default,{src:x,width:50,height:50,alt:"Author's profile picture",className:"w-10 h-10 rounded-full mr-2",onError:e=>{let t=e.target;t.src.startsWith("https://")&&(t.src=t.src.replace("https://","http://"))}}):"",(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"font-bold",children:h}),(0,l.jsx)("div",{className:"text-gray-500",children:(0,n.a)(c.date)})]})]})]})},m=r(2265),u=r(25922),h=r(65381),x=e=>{let{content:t}=e,{theme:r}=(0,u.F)();return(0,m.useEffect)(()=>{(0,h.$)(r);let e=e=>{var t;let l=(null===(t=e.detail)||void 0===t?void 0:t.theme)||r;(0,h.$)(l)};return window.addEventListener("themeChange",e),()=>{window.removeEventListener("themeChange",e)}},[t,r]),(0,l.jsx)("div",{className:"dark"===r?"blog-content bg-[#1E232C] text-white border border-[#373C53] dark-theme":"blog-content bg-white text-gray-800 border border-gray-200 light-theme",dangerouslySetInnerHTML:{__html:t}})},f=e=>{var t,r;let{postDetails:s,allPosts:a}=e;return(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"w-full max-w-[90rem] mx-auto px-4 sm:px-6 lg:px-8 mt-16",children:[(0,l.jsx)(c,{post:s}),(0,l.jsxs)("div",{className:"mt-4 flex justify-between",children:[(0,l.jsxs)("div",{className:"flex-1 mx-auto max-w-5xl pr-8",children:[(0,l.jsx)(x,{content:(null==s?void 0:null===(t=s.content)||void 0===t?void 0:t.rendered)||""}),(0,l.jsx)("div",{className:"mt-6 mb-6",children:(0,l.jsx)(i.default,{type:"in-article"})})]}),(0,l.jsx)("div",{className:"hidden lg:block w-[320px] sticky top-32 h-screen",children:(0,l.jsx)(i.default,{type:"multiplex-vertical"})})]})]}),(0,l.jsx)(d,{currentBlog:null==s?void 0:null===(r=s.title)||void 0===r?void 0:r.rendered,AllPosts:a}),(0,l.jsx)("div",{className:"mt-12",children:(0,l.jsx)(i.default,{type:"display-horizontal"})})]})}},61957:function(){},25922:function(e,t,r){"use strict";r.d(t,{F:function(){return c},f:function(){return m}});var l=r(2265),s=(e,t,r,l,s,a,n,i)=>{let d=document.documentElement,o=["light","dark"];function c(t){(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,l=r&&a?s.map(e=>a[e]||e):s;r?(d.classList.remove(...l),d.classList.add(t)):d.setAttribute(e,t)}),i&&o.includes(t)&&(d.style.colorScheme=t)}if(l)c(l);else try{let e=localStorage.getItem(t)||r,l=n&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(l)}catch(e){}},a=["light","dark"],n="(prefers-color-scheme: dark)",i="undefined"==typeof window,d=l.createContext(void 0),o={setTheme:e=>{},themes:[]},c=()=>{var e;return null!=(e=l.useContext(d))?e:o},m=e=>l.useContext(d)?l.createElement(l.Fragment,null,e.children):l.createElement(h,{...e}),u=["light","dark"],h=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:s=!0,enableColorScheme:i=!0,storageKey:o="theme",themes:c=u,defaultTheme:m=s?"system":"light",attribute:h="data-theme",value:p,children:b,nonce:w,scriptProps:y}=e,[j,N]=l.useState(()=>f(o,m)),[k,E]=l.useState(()=>f(o)),C=p?Object.values(p):c,S=l.useCallback(e=>{let t=e;if(!t)return;"system"===e&&s&&(t=g());let l=p?p[t]:t,n=r?v(w):null,d=document.documentElement,o=e=>{"class"===e?(d.classList.remove(...C),l&&d.classList.add(l)):e.startsWith("data-")&&(l?d.setAttribute(e,l):d.removeAttribute(e))};if(Array.isArray(h)?h.forEach(o):o(h),i){let e=a.includes(m)?m:null,r=a.includes(t)?t:e;d.style.colorScheme=r}null==n||n()},[w]),T=l.useCallback(e=>{let t="function"==typeof e?e(j):e;N(t);try{localStorage.setItem(o,t)}catch(e){}},[j]),A=l.useCallback(e=>{E(g(e)),"system"===j&&s&&!t&&S("system")},[j,t]);l.useEffect(()=>{let e=window.matchMedia(n);return e.addListener(A),A(e),()=>e.removeListener(A)},[A]),l.useEffect(()=>{let e=e=>{e.key===o&&(e.newValue?N(e.newValue):T(m))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[T]),l.useEffect(()=>{S(null!=t?t:j)},[t,j]);let L=l.useMemo(()=>({theme:j,setTheme:T,forcedTheme:t,resolvedTheme:"system"===j?k:j,themes:s?[...c,"system"]:c,systemTheme:s?k:void 0}),[j,T,t,k,s,c]);return l.createElement(d.Provider,{value:L},l.createElement(x,{forcedTheme:t,storageKey:o,attribute:h,enableSystem:s,enableColorScheme:i,defaultTheme:m,value:p,themes:c,nonce:w,scriptProps:y}),b)},x=l.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:a,enableSystem:n,enableColorScheme:i,defaultTheme:d,value:o,themes:c,nonce:m,scriptProps:u}=e,h=JSON.stringify([a,r,d,t,c,o,n,i]).slice(1,-1);return l.createElement("script",{...u,suppressHydrationWarning:!0,nonce:"undefined"==typeof window?m:"",dangerouslySetInnerHTML:{__html:"(".concat(s.toString(),")(").concat(h,")")}})}),f=(e,t)=>{let r;if(!i){try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t}},v=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},g=e=>(e||(e=window.matchMedia(n)),e.matches?"dark":"light")}},function(e){e.O(0,[613,972,145,824,971,117,744],function(){return e(e.s=35952)}),_N_E=e.O()}]);