{
  "Properties": {
    // Other configurations...
    "AppName": "PXW App Settings",
    "Version": "01.00.00"
  },
  "Configurations": {
    "Endpoints": {
      "PortfolioDataApiEndpoints": {
        "CurrenciesList": {
          "URL": "/data-api/v1/currencies"
        },
        "ProfilesAccountsSummaryRetrieve": {
          "URL": "/data-api/v1/profiles/{id}/accounts-summary"
        },
        "ProfilesAsOfDatesRetrieve": {
          "URL": "/data-api/v1/profiles/{id}/as-of-dates"
        },
        "ProfilesCustodianAccountCodesRetrieve": {
          "URL": "/data-api/v1/profiles/{id}/custodian-account-codes"
        },
        "ProfilesDashboardLineGraphRetrieve": {
          "URL": "/data-api/v1/profiles/{id}/dashboard-line-graph"
        },
        "ProfilesDashboardSummaryRetrieve": {
          "URL": "/data-api/v1/profiles/{id}/dashboard-summary"
        },
        "ProfilesPortfolioSummaryRetrieve": {
          "URL": "/data-api/v1/profiles/{id}/portfolio-summary"
        },
        "ProfilesFundingsRetrieve": {
          "URL": "/data-api/v1/profiles/{id}/fundings"
        },
        "ProfilesHoldingsRetrieve": {
          "URL": "/data-api/v1/profiles/{id}/holdings"
        },
        "ProfilesPieChartByAccountRetrieve": {
          "URL": "/data-api/v1/profiles/{id}/pie-chart-by-account"
        },
        "ProfilesPieChartByAssetClassRetrieve": {
          "URL": "/data-api/v1/profiles/{id}/pie-chart-by-asset-class"
        },
        "ProfilesPieChartsRetrieve": {
          "URL": "/data-api/v1/profiles/{id}/pie-charts"
        },
        "ProfilesTopHoldingsRetrieve": {
          "URL": "/data-api/v1/profiles/{id}/top-holdings"
        },
        "ProfilesTransactionsRetrieve": {
          "URL": "/data-api/v1/profiles/{id}/transactions"
        },
        "ProfilesAccountsList": {
          "URL": "/data-api/v1/profiles/{profile_pk}/accounts"
        },
        "ProfilesAccountsHoldingsRetrieve": {
          "URL": "/data-api/v1/profiles/{profile_pk}/accounts/{id}/holdings"
        },
        "ProfilesAccountsTransactionsRetrieve": {
          "URL": "/data-api/v1/profiles/{profile_pk}/accounts/{id}/transactions"
        },
        "LogUserLogin": {
          "URL": "/permissions/user-activities/log/login"
        },
        "LogUserLogout": {
          "URL": "/permissions/user-activities/log/logout"
        }
      }
    }
  },
  "Tenants": [
    {
      "TenantId": "001",
      "TenantName": "PortfolioHiWay",
      "TenantGuid": "ddb47824-e626-4d26-97da-97eb6b23e16d",
      "Settings": {
        "AzureAdB2C": {
          "Instance": "https://pxwuat.b2clogin.com",
          "Domain": "pxwuat.onmicrosoft.com",
          "TenantId": "ea02d495-6137-400a-9c6c-43ca3eab6ec3",
          "ClientId": "98d7682c-822c-4819-bb22-c9aea80a49b2", //"245bfee9-7eee-4402-b281-0991a5737b48",
          "CallbackPath": "/signin-oidc",
          "Scopes": "access_as_user access_as_admin Scope.PHW",
          "SignUpSignInPolicyId": "B2C_1A_SIGNIN_TOTP",
          "SignedOutCallbackPath": "/signout/B2C_1A_SIGNIN_TOTP",
          "ResetPasswordPolicyId": "B2C_1_signup-reset",
          "EditProfilePolicyId": "B2C_1_change-password",
          "EnablePiiLogging": true,
          "ApplicationIdUri": "https://pxwuat.onmicrosoft.com/98d7682c-822c-4819-bb22-c9aea80a49b2",
          "JwksUri": "https://pxwuat.b2clogin.com/pxwuat.onmicrosoft.com/B2C_1A_SIGNIN_TOTP/discovery/v2.0/keys",
          "MetadataUrl": "https://pxwuat.b2clogin.com/pxwuat.onmicrosoft.com/B2C_1A_SIGNIN_TOTP/v2.0/.well-known/openid-configuration",
          "Issuer": "https://pxwuat.b2clogin.com/ea02d495-6137-400a-9c6c-43ca3eab6ec3/v2.0/",
          "OrganizationId": "pxw-01",
          "BaseURL": "*************************************/v1.0",
          "APIMKey": "db03e1278327479096e75e278952a751",
          "APIMSecretKey": "****************************************",
          "GETMemberListURL": "/users",
          "GETMemberURL": "/users/{userid}",
          "GETProfilesForUserURL": "/users/{userId}/profiles",
          "GETAccountsForProfileURL": "/profiles/{profileId}/accounts",
          "GETMembersBriefURL": "/users-details",
          "UPDATEMemberURL": "/users/{userId}",
          "BearerTokenBaseURL": "https://login.microsoftonline.com/{0}/oauth2/v2.0/token",
          "BearerTokenTenant": "ea02d495-6137-400a-9c6c-43ca3eab6ec3",
          "BearerTokenClientID": "f9281b9c-c986-4dfb-a74a-29d67069243d",
          "BearerTokenClientSecret": "****************************************",
          "BearerTokenGrantType": "client_credentials",
          "BearerTokenScope": "https://pxwuat.onmicrosoft.com/98d7682c-822c-4819-bb22-c9aea80a49b2/.default",
          "BearerTokenApplicationName": "PXW",
          "RedirectBaseURL": "pxwuat.b2clogin.com/pxwuat.onmicrosoft.com",
          "RedirectParam": "B2C_1A_CHANGEPASSWORD",
          "RedirectClientID": "98d7682c-822c-4819-bb22-c9aea80a49b2",
          "RedirectMemberURI": "https://uat-pxw-react-appservice.azurewebsites.net/"
        },
        "AlphaCCOWorkflows": [
          {
            "AlphaCCOKey": "7707c77c-a142-4e8a-acbf-2f8864dd5624",
            "AlphaCCOStatusCode": "A",
            "Name": "Opening a Cash Account",
            "ShortDescription": "Opening a Cash Account",
            "PublishDate": "9/21/21 0:00",
            "AlphaCCOVersionNumber": "1",
            "IsInMenu": "TRUE",
            "IsMandatory": "FALSE",
            "IsAutoApproval": "TRUE",
            "IsSingleInstance": "FALSE",
            "IsNeedPrevoiusWorkflowInstanceID": "TRUE",
            "IsNeedParentWorkflowInstanceID": "TRUE",
            "RequiredParentWorkflowID": "********-0cc9-4ef6-845d-8f3e4548ca0a",
            "WorkflowIDtoSearchOnCompletedStatus": "63db9c46-8cce-47ab-bf28-48782c5443f0",
            "IsPOACWorkflow": "TRUE",
            "ActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "InActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "PendingIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "CompletedIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "GetDerivedObject": "PXW.AlphaCCOWorkflow",
            "ObjectTypeName": "PXW.AlphaCCOWorkflow",
            "MasterID": "7ebe8464-7f6c-44ce-8994-007b39637587",
            "Version": "15",
            "VersionName": "5/4/22 15:42",
            "PublishStatus": "Live",
            "LanguageID": "1",
            "MasterRecord": "FALSE",
            "CreatedDate": "4/4/23 18:51",
            "CreatedBy": "frank",
            "LastModifiedDate": "4/4/23 18:51",
            "LastModifiedBy": "frank",
            "RestrictToCurrentDomain": "FALSE"
          },
          {
            "AlphaCCOKey": "a1ae54c1-b5ce-4cc7-b71c-e12e18b8b3af",
            "AlphaCCOStatusCode": "A",
            "Name": "04.00 NBIN Review",
            "PublishDate": "9/21/21 0:00",
            "AlphaCCOVersionNumber": "0",
            "IsInMenu": "FALSE",
            "IsMandatory": "FALSE",
            "IsAutoApproval": "TRUE",
            "IsSingleInstance": "FALSE",
            "IsNeedPrevoiusWorkflowInstanceID": "TRUE",
            "IsNeedParentWorkflowInstanceID": "TRUE",
            "RequiredParentWorkflowID": "********-0cc9-4ef6-845d-8f3e4548ca0a",
            "WorkflowIDtoSearchOnCompletedStatus": "63db9c46-8cce-47ab-bf28-48782c5443f0",
            "IsPOACWorkflow": "FALSE",
            "ActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "InActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "PendingIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "CompletedIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "GetDerivedObject": "PXW.AlphaCCOWorkflow",
            "ObjectTypeName": "PXW.AlphaCCOWorkflow",
            "MasterID": "63db9c46-8cce-47ab-bf28-48782c5443f0",
            "Version": "3",
            "VersionName": "1/17/23 21:51",
            "PublishStatus": "Live",
            "LanguageID": "1",
            "MasterRecord": "FALSE",
            "CreatedDate": "3/21/23 14:03",
            "CreatedBy": "frank",
            "LastModifiedDate": "3/21/23 14:03",
            "LastModifiedBy": "frank",
            "RestrictToCurrentDomain": "FALSE"
          },
          {
            "AlphaCCOKey": "af4e23ef-ecd9-45b8-9c0b-e709b00003ca",
            "AlphaCCOStatusCode": "A",
            "Name": "Transfer Funds",
            "ShortDescription": "You need money to make more money.",
            "Description": "You need money to make more money.",
            "PublishDate": "6/30/21 0:00",
            "AlphaCCOVersionNumber": "1",
            "IsInMenu": "FALSE",
            "IsMandatory": "FALSE",
            "IsAutoApproval": "FALSE",
            "IsSingleInstance": "FALSE",
            "IsNeedPrevoiusWorkflowInstanceID": "FALSE",
            "IsNeedParentWorkflowInstanceID": "FALSE",
            "RequiredParentWorkflowID": "********-0000-0000-0000-************",
            "WorkflowIDtoSearchOnCompletedStatus": "********-0000-0000-0000-************",
            "IsPOACWorkflow": "FALSE",
            "ActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "InActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "PendingIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "CompletedIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "WorkflowButtonText": "Fund Accounts",
            "GetDerivedObject": "PXW.AlphaCCOWorkflow",
            "ObjectTypeName": "PXW.AlphaCCOWorkflow",
            "MasterID": "a3ee43c6-6399-48cf-80ba-676fcc0c1a71",
            "Version": "33",
            "VersionName": "Copied from Version->23",
            "PublishStatus": "Live",
            "LanguageID": "1",
            "MasterRecord": "FALSE",
            "CreatedDate": "1/17/23 21:51",
            "CreatedBy": "System",
            "LastModifiedDate": "1/17/23 21:51",
            "LastModifiedBy": "System",
            "RestrictToCurrentDomain": "FALSE"
          },
          {
            "AlphaCCOKey": "5645d594-1622-4f9c-9f15-a3b74a5a3d82",
            "AlphaCCOStatusCode": "A",
            "Name": "Regulatory Questions",
            "ShortDescription": "A few more general regulatory questions.",
            "Description": "A few more general regulatory questions.",
            "PublishDate": "5/31/22 0:00",
            "AlphaCCOVersionNumber": "1",
            "IsInMenu": "TRUE",
            "IsMandatory": "TRUE",
            "IsAutoApproval": "FALSE",
            "IsSingleInstance": "FALSE",
            "IsNeedPrevoiusWorkflowInstanceID": "FALSE",
            "IsNeedParentWorkflowInstanceID": "TRUE",
            "RequiredParentWorkflowID": "f28b1f92-e0a8-4dc5-947a-9cc7ff7bf260",
            "WorkflowIDtoSearchOnCompletedStatus": "********-0000-0000-0000-************",
            "IsPOACWorkflow": "FALSE",
            "ActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "InActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "PendingIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "CompletedIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "TitleForDisabled": "Not available",
            "TitleForCompleted": "Completed",
            "TitleForContinueNext": "Continue",
            "TitleForPending": "Submission under review",
            "TitleForNotStarted": "Please Start",
            "WorkflowButtonText": "Start",
            "WorkflowInProcessingButtonText": "Continue",
            "GetDerivedObject": "PXW.AlphaCCOWorkflow",
            "ObjectTypeName": "PXW.AlphaCCOWorkflow",
            "MasterID": "********-0cc9-4ef6-845d-8f3e4548ca0a",
            "Version": "2",
            "PublishStatus": "Live",
            "LanguageID": "1",
            "MasterRecord": "FALSE",
            "CreatedDate": "1/17/23 21:51",
            "CreatedBy": "System",
            "LastModifiedDate": "1/17/23 21:51",
            "LastModifiedBy": "System",
            "RestrictToCurrentDomain": "FALSE"
          },
          {
            "AlphaCCOKey": "2fc0610a-dd5a-44d2-badf-20c26c284465",
            "AlphaCCOStatusCode": "A",
            "Name": "Account Opening Details",
            "ShortDescription": "Tell us how you want to invest.",
            "Description": "Tell us how you want to invest.",
            "PublishDate": "6/22/21 0:00",
            "AlphaCCOVersionNumber": "1",
            "IsInMenu": "TRUE",
            "IsMandatory": "TRUE",
            "IsAutoApproval": "TRUE",
            "IsSingleInstance": "FALSE",
            "IsNeedPrevoiusWorkflowInstanceID": "FALSE",
            "IsNeedParentWorkflowInstanceID": "TRUE",
            "RequiredParentWorkflowID": "792902c9-0ed7-4ded-850a-aa7484916fe8",
            "WorkflowIDtoSearchOnCompletedStatus": "********-0000-0000-0000-************",
            "IsPOACWorkflow": "FALSE",
            "ActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "InActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "PendingIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "CompletedIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "TitleForDisabled": "Not available",
            "TitleForCompleted": "Completed",
            "TitleForContinueNext": "Continue",
            "TitleForPending": "Submission under review",
            "TitleForNotStarted": "Please Start",
            "WorkflowButtonText": "Start",
            "WorkflowInProcessingButtonText": "Continue",
            "GetDerivedObject": "PXW.AlphaCCOWorkflow",
            "ObjectTypeName": "PXW.AlphaCCOWorkflow",
            "MasterID": "f28b1f92-e0a8-4dc5-947a-9cc7ff7bf260",
            "Version": "2",
            "PublishStatus": "Live",
            "LanguageID": "1",
            "MasterRecord": "FALSE",
            "CreatedDate": "1/17/23 21:51",
            "CreatedBy": "System",
            "LastModifiedDate": "1/17/23 21:51",
            "LastModifiedBy": "System",
            "RestrictToCurrentDomain": "FALSE"
          },
          {
            "AlphaCCOKey": "7707c77c-a142-4e8a-acbf-2f8864dd5624",
            "AlphaCCOStatusCode": "A",
            "Name": "Opening a TFSA Account",
            "ShortDescription": "Opening a TFSA Account",
            "PublishDate": "6/1/21 0:00",
            "AlphaCCOVersionNumber": "0",
            "IsInMenu": "TRUE",
            "IsMandatory": "FALSE",
            "IsAutoApproval": "TRUE",
            "IsSingleInstance": "FALSE",
            "IsNeedPrevoiusWorkflowInstanceID": "TRUE",
            "IsNeedParentWorkflowInstanceID": "TRUE",
            "RequiredParentWorkflowID": "********-0cc9-4ef6-845d-8f3e4548ca0a",
            "WorkflowIDtoSearchOnCompletedStatus": "63db9c46-8cce-47ab-bf28-48782c5443f0",
            "IsPOACWorkflow": "TRUE",
            "ActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "InActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "PendingIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "CompletedIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "WorkflowButtonText": "Enter",
            "GetDerivedObject": "PXW.AlphaCCOWorkflow",
            "ObjectTypeName": "PXW.AlphaCCOWorkflow",
            "MasterID": "a9284adb-b702-48e6-bfaf-a10cfb5820b0",
            "Version": "22",
            "VersionName": "Copied from Version->12",
            "PublishStatus": "Live",
            "LanguageID": "1",
            "MasterRecord": "FALSE",
            "CreatedDate": "1/17/23 21:51",
            "CreatedBy": "System",
            "LastModifiedDate": "4/4/23 18:51",
            "LastModifiedBy": "frank",
            "RestrictToCurrentDomain": "FALSE"
          },
          {
            "AlphaCCOKey": "c2c59d67-b4f2-443a-bb38-f0e52335fc1f",
            "AlphaCCOStatusCode": "A",
            "Name": "Proof of Bank",
            "ShortDescription": "We need some proof that you can bank. ",
            "Description": "We need some proof that you can bank. ",
            "PublishDate": "5/31/22 0:00",
            "AlphaCCOVersionNumber": "1",
            "IsInMenu": "TRUE",
            "IsMandatory": "TRUE",
            "IsAutoApproval": "TRUE",
            "IsSingleInstance": "FALSE",
            "IsNeedPrevoiusWorkflowInstanceID": "FALSE",
            "IsNeedParentWorkflowInstanceID": "TRUE",
            "RequiredParentWorkflowID": "81cfaac1-05c6-406c-822d-dce05c6726bd",
            "WorkflowIDtoSearchOnCompletedStatus": "********-0000-0000-0000-************",
            "IsPOACWorkflow": "FALSE",
            "ActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "InActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "PendingIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "CompletedIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "TitleForDisabled": "Not available",
            "TitleForCompleted": "Completed",
            "TitleForContinueNext": "Continue",
            "TitleForPending": "Submission under review",
            "TitleForNotStarted": "Please Start",
            "WorkflowButtonText": "Start",
            "WorkflowInProcessingButtonText": "Continue",
            "GetDerivedObject": "PXW.AlphaCCOWorkflow",
            "ObjectTypeName": "PXW.AlphaCCOWorkflow",
            "MasterID": "792902c9-0ed7-4ded-850a-aa7484916fe8",
            "Version": "2",
            "PublishStatus": "Live",
            "LanguageID": "1",
            "MasterRecord": "FALSE",
            "CreatedDate": "1/17/23 21:51",
            "CreatedBy": "System",
            "LastModifiedDate": "1/17/23 21:51",
            "LastModifiedBy": "System",
            "RestrictToCurrentDomain": "FALSE"
          },
          {
            "AlphaCCOKey": "34b3eb4b-0d3d-4010-ab47-87f625c8e4cb",
            "AlphaCCOStatusCode": "A",
            "Name": "Electronic Fund Transfer (EFT)",
            "ShortDescription": "We transfer your money from your bank account to us.",
            "Description": "We transfer your money from your bank account to us.",
            "PublishDate": "10/24/21 0:00",
            "AlphaCCOVersionNumber": "1",
            "IsInMenu": "TRUE",
            "IsMandatory": "FALSE",
            "IsAutoApproval": "TRUE",
            "IsSingleInstance": "FALSE",
            "IsNeedPrevoiusWorkflowInstanceID": "FALSE",
            "IsNeedParentWorkflowInstanceID": "FALSE",
            "RequiredParentWorkflowID": "********-0000-0000-0000-************",
            "WorkflowIDtoSearchOnCompletedStatus": "********-0000-0000-0000-************",
            "IsPOACWorkflow": "FALSE",
            "ActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "InActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "PendingIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "CompletedIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "WorkflowButtonText": "Get Started",
            "WorkflowInProcessingButtonText": "Get Started",
            "GetDerivedObject": "PXW.AlphaCCOWorkflow",
            "ObjectTypeName": "PXW.AlphaCCOWorkflow",
            "MasterID": "4172a1c0-3787-421c-9859-d9a6cb718c66",
            "Version": "13",
            "PublishStatus": "Live",
            "LanguageID": "1",
            "MasterRecord": "FALSE",
            "CreatedDate": "1/17/23 21:51",
            "CreatedBy": "System",
            "LastModifiedDate": "1/17/23 21:51",
            "LastModifiedBy": "System",
            "RestrictToCurrentDomain": "FALSE"
          },
          {
            "AlphaCCOKey": "741fa19b-14ca-4e97-9043-ae95d31af49b",
            "AlphaCCOStatusCode": "A",
            "Name": "Personal Information",
            "ShortDescription": "Let's get to know each other.",
            "Description": "Let's get to know each other.",
            "PublishDate": "5/31/22 0:00",
            "AlphaCCOVersionNumber": "3",
            "IsInMenu": "TRUE",
            "IsMandatory": "TRUE",
            "IsAutoApproval": "TRUE",
            "IsSingleInstance": "FALSE",
            "IsNeedPrevoiusWorkflowInstanceID": "FALSE",
            "IsNeedParentWorkflowInstanceID": "TRUE",
            "RequiredParentWorkflowID": "********-0000-0000-0000-************",
            "WorkflowIDtoSearchOnCompletedStatus": "********-0000-0000-0000-************",
            "IsPOACWorkflow": "FALSE",
            "ActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "InActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "PendingIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "CompletedIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "TitleForDisabled": "Not available",
            "TitleForCompleted": "Completed",
            "TitleForContinueNext": "Continue",
            "TitleForPending": "Submission under review",
            "TitleForNotStarted": "Please Start",
            "WorkflowButtonText": "Start",
            "WorkflowInProcessingButtonText": "Continue",
            "GetDerivedObject": "PXW.AlphaCCOWorkflow",
            "ObjectTypeName": "PXW.AlphaCCOWorkflow",
            "MasterID": "81cfaac1-05c6-406c-822d-dce05c6726bd",
            "Version": "2",
            "PublishStatus": "Live",
            "LanguageID": "1",
            "MasterRecord": "FALSE",
            "CreatedDate": "1/17/23 21:51",
            "CreatedBy": "System",
            "LastModifiedDate": "1/17/23 21:51",
            "LastModifiedBy": "System",
            "RestrictToCurrentDomain": "FALSE"
          },
          {
            "AlphaCCOKey": "7707c77c-a142-4e8a-acbf-2f8864dd5624",
            "AlphaCCOStatusCode": "A",
            "Name": "Opening an RRSP Account",
            "ShortDescription": "Opening an RRSP Account",
            "Description": "description",
            "PublishDate": "6/1/21 0:00",
            "AlphaCCOVersionNumber": "2",
            "IsInMenu": "TRUE",
            "IsMandatory": "FALSE",
            "IsAutoApproval": "TRUE",
            "IsSingleInstance": "FALSE",
            "IsNeedPrevoiusWorkflowInstanceID": "TRUE",
            "IsNeedParentWorkflowInstanceID": "TRUE",
            "RequiredParentWorkflowID": "********-0cc9-4ef6-845d-8f3e4548ca0a",
            "WorkflowIDtoSearchOnCompletedStatus": "63db9c46-8cce-47ab-bf28-48782c5443f0",
            "IsPOACWorkflow": "TRUE",
            "ActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "InActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "PendingIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "CompletedIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "WorkflowButtonText": "Start",
            "GetDerivedObject": "PXW.AlphaCCOWorkflow",
            "ObjectTypeName": "PXW.AlphaCCOWorkflow",
            "MasterID": "5d6153ce-679f-48dc-8a25-dfc2ca4b5730",
            "Version": "26",
            "VersionName": "Copied from Version->14",
            "PublishStatus": "Live",
            "LanguageID": "1",
            "MasterRecord": "FALSE",
            "CreatedDate": "4/4/23 18:53",
            "CreatedBy": "frank",
            "LastModifiedDate": "4/4/23 18:53",
            "LastModifiedBy": "frank",
            "RestrictToCurrentDomain": "FALSE"
          },
          {
            "AlphaCCOKey": "a926dd8d-c160-4cb9-bfd9-6df49308bdeb",
            "AlphaCCOStatusCode": "A",
            "Name": "Orders",
            "ShortDescription": "Order a variety of investment products and assets.",
            "PublishDate": "9/1/21 0:00",
            "AlphaCCOVersionNumber": "1",
            "IsInMenu": "TRUE",
            "IsMandatory": "FALSE",
            "IsAutoApproval": "TRUE",
            "IsSingleInstance": "FALSE",
            "IsNeedPrevoiusWorkflowInstanceID": "FALSE",
            "IsNeedParentWorkflowInstanceID": "TRUE",
            "RequiredParentWorkflowID": "********-0000-0000-0000-************",
            "WorkflowIDtoSearchOnCompletedStatus": "********-0000-0000-0000-************",
            "IsPOACWorkflow": "FALSE",
            "ActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "InActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "PendingIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "CompletedIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "WorkflowButtonText": "Order",
            "GetDerivedObject": "PXW.AlphaCCOWorkflow",
            "ObjectTypeName": "PXW.AlphaCCOWorkflow",
            "MasterID": "b7c07f13-c837-4924-8d61-f67ad9adf9ae",
            "Version": "14",
            "PublishStatus": "Live",
            "LanguageID": "1",
            "MasterRecord": "FALSE",
            "CreatedDate": "1/17/23 21:51",
            "CreatedBy": "System",
            "LastModifiedDate": "1/17/23 21:51",
            "LastModifiedBy": "System",
            "RestrictToCurrentDomain": "FALSE"
          },
          {
            "AlphaCCOKey": "1efed681-4c0f-40ab-a6b5-09d5d28f5076",
            "AlphaCCOStatusCode": "A",
            "Name": "Client Confirmation",
            "PublishDate": "10/21/21 0:00",
            "AlphaCCOVersionNumber": "0",
            "IsInMenu": "FALSE",
            "IsMandatory": "FALSE",
            "IsAutoApproval": "TRUE",
            "IsSingleInstance": "FALSE",
            "IsNeedPrevoiusWorkflowInstanceID": "FALSE",
            "IsNeedParentWorkflowInstanceID": "FALSE",
            "RequiredParentWorkflowID": "********-0000-0000-0000-************",
            "WorkflowIDtoSearchOnCompletedStatus": "********-0000-0000-0000-************",
            "IsPOACWorkflow": "FALSE",
            "ActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "InActiveIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "PendingIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "CompletedIcon": "AdvantageCMS.Core.Common.BaseClasses.AdvantageImage",
            "GetDerivedObject": "PXW.AlphaCCOWorkflow",
            "ObjectTypeName": "PXW.AlphaCCOWorkflow",
            "MasterID": "c5562233-50b5-4d6a-9477-f757b19cc4cb",
            "Version": "2",
            "PublishStatus": "Live",
            "LanguageID": "1",
            "MasterRecord": "FALSE",
            "CreatedDate": "1/17/23 21:51",
            "CreatedBy": "System",
            "LastModifiedDate": "1/17/23 21:51",
            "LastModifiedBy": "System",
            "RestrictToCurrentDomain": "FALSE"
          }
        ],
        "PortfolioDataApi": {
          "Domain": "https://pxw-datainterchange-uat.azurewebsites.net"
        },
        "AlphaCC0": {
          "AlphaCCOBaseURL": "https://pxw-api.AlphaCCO.com",
          //"AlphaCCOTenantID": "9689F247FBE846DDA2B8D3143E6190E5",
          "AlphaCCOTenantID": "",
          "AlphaCCOHost": "<EMAIL>",
          "AlphaCCOHostkey": "F5KsCt^QW#^HVw6c83Q^zn$VvH",
          "AlphaCCOComponentHost": "<EMAIL>",
          "AlphaCCOComponentHostKey": "67ytp8CnGQbLG&w0qc$d0R9Rd4",
          "AlphaCCOAuthenticationURL": "/account/authenticate"
        },
        "Emails": {
          "EmailAddressForDocumentImportErrorMessage": "<EMAIL>",
          "DocumentImportErrorMessageEmailTemplate": "",
          "DocumentNotificationEmailTemplate": "",
          "DocumentNotificationSubject": "PortfolioHiWay - New documents available (UAT)",
          "DocumentNotificationDocumentCountPhase": "new document(s) for profile ",
          "SendGridEmailFrom": "<EMAIL>",
          "SendGridEmailFromName": "UAT - Do Not Reply"
        },
        "Pages": {
          "DashboardURL": "/dashboard",
          "SurveyPageURL": "/survey"
        },
        "Documents": {
          "SharePointDocumentSiteUrl": "https://portfolioxpressway.sharepoint.com",
          "SharePointDocumentClientName": "PHW",
          "SharePointDocumentClientId": "bbb1ff27-e413-4ecd-b769-4e931d113850",
          "SharePointDocumentClientSecret": "8lEDgJp4YCJ7dTMcZY05d1FUW8RbZSY2CChrU2hxfBo=",
          "SharePointDocumentLandingDocumentLibrary": "PXWStaging",
          "SharePointDocumentLandingFolder": "LandingZone",
          "SharePointDocumentFinalDocumentLibrary": "PXWStaging",
          "SharePointDocumentFinalFolder": "FinalZone",
          "DocumentTypes": [
            {
              "Name": "AccountStatement",
              "DisplayName": "Account Statements",
              "NoDocumentMessage": "No document",
              "Description": "",
              "IsActive": true
            },
            {
              "Name": "TradeConfirmation",
              "DisplayName": "Trade Confirmations",
              "NoDocumentMessage": "No document",
              "Description": "",
              "IsActive": true
            },
            {
              "Name": "InvestmentInformation",
              "DisplayName": "Investment Information",
              "NoDocumentMessage": "No document",
              "Description": "",
              "IsActive": true
            },
            {
              "Name": "AccountApplication",
              "DisplayName": "Account Applications",
              "NoDocumentMessage": "No document",
              "Description": "",
              "IsActive": true
            },
            {
              "Name": "TaxDocument",
              "DisplayName": "Tax Documents",
              "NoDocumentMessage": "No document",
              "Description": "",
              "IsActive": true
            },
            {
              "Name": "Others",
              "DisplayName": "Others",
              "NoDocumentMessage": "No document",
              "Description": "Others",
              "IsActive": true
            },
            {
              "Name": "RegulatoryDisclosure",
              "DisplayName": "Regulatory Disclosures",
              "NoDocumentMessage": "No document",
              "Description": "",
              "IsActive": true
            }
          ]
        }
      }
    }
  ],
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.IdentityModel": "Trace",
      "System": "Error"
    }
  },
  "Microsoft.IdentityModel.Logging": {
    "LogLevel": {
      "Default": "Trace"
    },
    "IncludeScopes": false,
    "LogToConsole": true,
    "LogToTrace": true,
    "DisplayPII": true
  },
  "AllowedHosts": "*"
}
