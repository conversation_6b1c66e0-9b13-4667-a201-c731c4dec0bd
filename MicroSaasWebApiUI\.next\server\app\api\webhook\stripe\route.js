(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[310],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},6195:e=>{"use strict";e.exports=require("node:buffer")},2480:()=>{},4654:()=>{},2848:(e,t,s)=>{"use strict";s.r(t),s.d(t,{ComponentMod:()=>U,default:()=>W});var r={};s.r(r),s.d(r,{POST:()=>P,dynamic:()=>T,runtime:()=>R});var i={};s.r(i),s.d(i,{originalPathname:()=>Z,patchFetch:()=>F,requestAsyncStorage:()=>I,routeModule:()=>A,serverHooks:()=>C,staticGenerationAsyncStorage:()=>O});var a=s(932),n=s(2561),o=s(4828),c=s(6631),l=s(9985),d=s(473),u=s(1735);let p=new u.Z(process.env.STRIPE_SECRET_KEY,{typescript:!0}),h=process.env.STRIPE_WEBHOOK_SECRET;var f=s(796);function m(e,t){return(0,f.registerServerReference)(t,e,null)}s(4630),s(6991),s(8816),new Uint8Array([60,104,116,109,108]),new Uint8Array([60,98,111,100,121]),new Uint8Array([60,47,104,101,97,100,62]),new Uint8Array([60,47,98,111,100,121,62]),new Uint8Array([60,47,104,116,109,108,62]),new Uint8Array([60,47,98,111,100,121,62,60,47,104,116,109,108,62]),new TextEncoder,Symbol.for("next.server.action-manifests"),new TextEncoder,new TextDecoder;var x=s(6333),b=s(5657),y=s(8753),v=s(8552);let g=require("node:events");var w=s(2112);let j=new g.EventEmitter;j.on("sendThanksYouEmail",async e=>{w.g.sendThanksYouEmail(e)});let _=new u.Z(process.env.STRIPE_SECRET_KEY,{apiVersion:"2024-06-20"});async function k(e){return await x.Z.subscription.findFirst({where:{user_clerk_id:e}})}async function N(e,t){let s=t.data.object,r=await v.R.findCheckoutSession(s.id),i=r?.customer,a=r?.line_items?.data[0]?.price.id;if(!y.Z.stripe.products.find(e=>e.priceId===a))return l.xk.json({error:"Plan not found"},{status:400});let n=await _.customers.retrieve(i);if(!n||!n.email)return console.error("No customer email found"),l.xk.json({error:"No customer email found for subscription"},{status:400});let o=await _.prices.retrieve(a);if(!o)return new Response("Price not found",{status:500});let c=await _.products.retrieve("string"==typeof o.product?o.product:o.product.id);if(!c)return new Response("Product not found",{status:500});let d=c.metadata.subscription_type||"default",u=n.email,p=await b.N.users.getUserList({emailAddress:[u]});if(!p.data.length)return console.error("Clerc user not found"),l.xk.json({error:"No customer email found for subscription"},{status:400});let h=p.data[0],f=e?.data?.object?.subscription;return await x.Z.subscription.upsert({where:{user_clerk_id:h.id},create:{last_stripe_cs_id:s.id,user_clerk_id:h.id,...f?{sub_stripe_id:f}:{},sub_status:"active",user_email:u,sub_type:d,stripe_customer_id:n.id},update:{last_stripe_cs_id:s.id,...f?{sub_stripe_id:f}:{},sub_status:"active"}}),j.emit("sendThanksYouEmail",u),l.xk.json({},{status:200})}async function S(e){let t=e.data.object,s=await _.subscriptions.retrieve(t.id),r=await x.Z.subscription.findFirst({where:{sub_stripe_id:s.id}});await x.Z.subscription.update({where:{id:r.id},data:{sub_status:"inactive"}})}async function E(e,t){let s=t.data.object,r=await x.Z.subscription.findFirst({where:{stripe_customer_id:s.customer.toString()}}),i=e?.data?.object?.subscription;return i?r?.sub_stripe_id!==i?new Response("Subscription is invalid",{status:403}):void await x.Z.subscription.update({where:{id:r.id},data:{sub_status:"active"}}):new Response("Subscription is invalid",{status:400})}!function(e){for(let t=0;t<e.length;t++){let s=e[t];if("function"!=typeof s)throw Error(`A "use server" file can only export async functions, found ${typeof s}.
Read more: https://nextjs.org/docs/messages/invalid-use-server-value`)}}([k,N,S,E]),m("1c548c926b5e17db6a6e381a3f15e16b07a70dca",k),m("78d93be0f3171de0df85ce5e42f8418a9c8f37e4",N),m("f377f06404a2b61735b4a2be973e74b304c9cb4d",S),m("cca4f3aba61e31f11eb6718d2992c5e458bb5109",E);let T="force-dynamic",R="edge";async function P(e){let t;let s=await e.text(),r=(0,d.headers)().get("stripe-signature");console.log("1 POST",s),console.log("2 signature",r);try{t=p.webhooks.constructEvent(s,r,h)}catch(e){return console.error(`Webhook signature verification failed. ${e.message}`),l.xk.json({error:e.message},{status:400})}try{switch(t.type){case"checkout.session.completed":{let e=JSON.parse(s);return N(e,t)}case"checkout.session.expired":case"customer.subscription.updated":case"invoice.payment_failed":break;case"customer.subscription.deleted":S(t);break;case"invoice.paid":{let e=JSON.parse(s);E(e,t)}}}catch(e){console.error("stripe error: ",e.message)}return l.xk.json({})}let A=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/webhook/stripe/route",pathname:"/api/webhook/stripe",filename:"route",bundlePath:"app/api/webhook/stripe/route"},resolvedPagePath:"C:\\Projects\\PersonalPortal\\src\\app\\api\\webhook\\stripe\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:I,staticGenerationAsyncStorage:O,serverHooks:C}=A,Z="/api/webhook/stripe/route";function F(){return(0,c.XH)({serverHooks:C,staticGenerationAsyncStorage:O})}let U=i,W=a.a.wrap(A)},8105:(e,t,s)=>{"use strict";var r=s(5105);r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,Symbol.for("react.element"),Symbol.for("react.lazy");var i=Symbol.iterator,a=Array.isArray,n=Object.getPrototypeOf,o=Object.prototype,c=new WeakMap,l=new WeakMap;function d(e,t){var s=c.get(this);if(!s)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");if(s.id!==e)return!1;var r=s.bound;if(null===r)return 0===t;switch(r.status){case"fulfilled":return r.value.length===t;case"pending":throw r;case"rejected":throw r.reason;default:throw"string"!=typeof r.status&&(r.status="pending",r.then(function(e){r.status="fulfilled",r.value=e},function(e){r.status="rejected",r.reason=e})),r}}var u=Function.prototype.bind,p=Array.prototype.slice;function h(){var e=u.apply(this,arguments),t=c.get(this);if(t){var s=p.call(arguments,1),r=null;r=null!==t.bound?Promise.resolve(t.bound).then(function(e){return e.concat(s)}):Promise.resolve(s),Object.defineProperties(e,{$$FORM_ACTION:{value:this.$$FORM_ACTION},$$IS_SIGNATURE_EQUAL:{value:d},bind:{value:h}}),c.set(e,{id:t.id,bound:r})}return e}function f(e,t,s,r){this.status=e,this.value=t,this.reason=s,this._response=r}function m(e,t){for(var s=0;s<e.length;s++)(0,e[s])(t)}f.prototype=Object.create(Promise.prototype),f.prototype.then=function(e,t){switch(this.status){case"resolved_model":y(this);break;case"resolved_module":v(this)}switch(this.status){case"fulfilled":e(this.value);break;case"pending":case"blocked":case"cyclic":e&&(null===this.value&&(this.value=[]),this.value.push(e)),t&&(null===this.reason&&(this.reason=[]),this.reason.push(t));break;default:t(this.reason)}};var x=null,b=null;function y(e){var t=x,s=b;x=e,b=null;var r=e.value;e.status="cyclic",e.value=null,e.reason=null;try{var i=JSON.parse(r,e._response._fromJSON);if(null!==b&&0<b.deps)b.value=i,e.status="blocked",e.value=null,e.reason=null;else{var a=e.value;e.status="fulfilled",e.value=i,null!==a&&m(a,i)}}catch(t){e.status="rejected",e.reason=t}finally{x=t,b=s}}function v(e){try{var t=e.value,s=globalThis.__next_require__(t[0]);if(4===t.length&&"function"==typeof s.then){if("fulfilled"===s.status)s=s.value;else throw s.reason}var r="*"===t[2]?s:""===t[2]?s.__esModule?s.default:s:s[t[2]];e.status="fulfilled",e.value=r}catch(t){e.status="rejected",e.reason=t}}},4630:(e,t,s)=>{"use strict";s(8105)},8753:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var r=s(3155);let i={appName:"Javian Picardo",appDescription:"Portal for the services provided by Javian Picardo.",domainName:"www.javianpicardo.com",robots:{index:!0,follow:!0,"max-image-preview":"large","max-snippet":-1,googleBot:{index:!0,follow:!0,"max-image-preview":"large","max-snippet":-1}},stripe:{products:[{type:"one-time",title:"Quick Shoots",productId:"prod_S0c6NHMe0vINXA",subtitle:"Professional photography session",price:300,isBest:!1,linkTitle:"Book Quick Shoots",featuresTitle:"Package Features",priceId:"price_1R6aslP82YH9JfOlJZcweC4d",features:[{title:"One hour of footage time",disabled:!1},{title:"50 edited photos",disabled:!1},{title:"Help with posing",disabled:!1},{title:"Location list to pick from",disabled:!1},{title:"7 business days turn around time",disabled:!1}]},{type:"one-time",title:"Mini Shoots",productId:"prod_S0c9zDLLtK9rnS",subtitle:"Perfect for professional portraits",price:150,isBest:!0,linkTitle:"Book Mini Shoots",featuresTitle:"Package Features",priceId:"price_1R6avXP82YH9JfOlIGpAfwjU",features:[{title:"30 minutes of footage time",disabled:!1},{title:"25 edited photos",disabled:!1},{title:"Help with posing",disabled:!1},{title:"Location list to pick from",disabled:!1},{title:"7 business days turn around time",disabled:!1}]}]},colors:{theme:"light",main:s.n(r)().light.primary},resend:{fromAdmin:"Javian Picardo <<EMAIL>>",supportEmail:"<EMAIL>",forwardRepliesTo:"<EMAIL>",subjects:{thankYou:"Welcome to Javian Picardo"}}}},6333:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=new(s(7225)).PrismaClient},2112:(e,t,s)=>{"use strict";s.d(t,{g:()=>v});var r=s(2416);s(7908);let i=e=>(0,r.jsxs)("div",{className:"max-w-3xl mx-auto p-8 bg-white",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Invoice"}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-4",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,r.jsx)("span",{className:"font-medium",children:"Invoice number: "}),e.id]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,r.jsx)("span",{className:"font-medium",children:"Date paid: "}),e.paid_date]})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-8 mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-lg font-semibold mb-3",children:"From:"}),(0,r.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,r.jsx)("p",{className:"font-medium",children:"DB2 Software Ltd"}),(0,r.jsx)("p",{children:"38 Fawkner Way"}),(0,r.jsx)("p",{children:"Stanford In The Vale"}),(0,r.jsx)("p",{children:"Faringdon"}),(0,r.jsx)("p",{children:"United Kingdom"}),(0,r.jsx)("p",{children:"SN7 8FF"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-lg font-semibold mb-3",children:"To:"}),(0,r.jsx)("div",{className:"text-sm text-gray-600 h-24 border border-dashed border-gray-300 rounded-md p-3",children:(0,r.jsx)("span",{children:e.data})})]})]}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,r.jsx)("th",{className:"py-3 text-left font-semibold text-gray-700",children:"Description"}),(0,r.jsx)("th",{className:"py-3 text-center font-semibold text-gray-700",children:"Qty"}),(0,r.jsx)("th",{className:"py-3 text-right font-semibold text-gray-700",children:"Unit price"}),(0,r.jsx)("th",{className:"py-3 text-right font-semibold text-gray-700",children:"Amount"})]})}),(0,r.jsx)("tbody",{children:(0,r.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,r.jsx)("td",{className:"py-4 text-sm text-gray-600",children:"Nextjs code boilerplate"}),(0,r.jsx)("td",{className:"py-4 text-center text-sm text-gray-600",children:"1"}),(0,r.jsx)("td",{className:"py-4 text-right text-sm text-gray-600",children:e.price}),(0,r.jsx)("td",{className:"py-4 text-right text-sm text-gray-600",children:e.price})]})})]})}),(0,r.jsx)("div",{className:"border-t border-gray-200 pt-4",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm text-gray-600",children:[(0,r.jsx)("span",{children:"Subtotal:"}),(0,r.jsx)("span",{})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm font-medium text-gray-800",children:[(0,r.jsx)("span",{children:"Total:"}),(0,r.jsx)("span",{})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm font-medium text-green-600",children:[(0,r.jsx)("span",{children:"Total amount paid:"}),(0,r.jsx)("span",{})]})]})})]});var a=s(4568),n=s(5631),o=s(7619),c=s(8150),l=s(1196),d=s(389),u=s(565),p=s(5649);let h={body:{backgroundColor:"#f3f4f6",fontFamily:"Arial, sans-serif"},container:{margin:"0 auto",padding:"20px 0 48px",width:"580px"},section:{backgroundColor:"#ffffff",borderRadius:"5px",padding:"40px"},h1:{color:"#3b82f6",fontSize:"32px",fontWeight:"bold",textAlign:"center",margin:"0 0 20px"},text:{color:"#374151",fontSize:"16px",lineHeight:"24px",textAlign:"center"},button:{backgroundColor:"#3b82f6",borderRadius:"5px",color:"#ffffff",display:"inline-block",fontSize:"16px",fontWeight:"bold",padding:"12px 24px",textDecoration:"none",textAlign:"center",marginTop:"32px"}},f=({email:e})=>(0,r.jsxs)(a.V,{children:[(0,r.jsx)(n.F,{}),(0,r.jsx)(o.M,{children:"Welcome to our community!"}),(0,r.jsx)(c.u,{style:h.body,children:(0,r.jsx)(l.W,{style:h.container,children:(0,r.jsxs)(d.$,{style:h.section,children:[(0,r.jsx)(u.x,{style:h.h1,children:"Welcome aboard!"}),(0,r.jsxs)(u.x,{style:h.text,children:["We're thrilled to have you join us, ",e,"!"]}),(0,r.jsx)(u.x,{style:h.text,children:"Get ready for an amazing journey. We can't wait to see what you'll achieve with us."}),(0,r.jsx)(d.$,{style:{textAlign:"center"},children:(0,r.jsx)(p.r,{href:"http://localhost:3000/dashboard",style:h.button,children:"Get Started"})})]})})})]});var m=s(8753),x=s(6333),b=s(7121);class y{async sendThanksYouEmail(e){let{data:t,error:s}=await this.resend.emails.send({from:m.Z.resend.fromAdmin,to:[e],replyTo:m.Z.resend.forwardRepliesTo,subject:m.Z.resend.subjects.thankYou,react:f({email:e})});if(s)throw s;return t}async sendInvoice(e,t){let{data:s,error:r}=await this.resend.emails.send({from:m.Z.resend.fromAdmin,to:[e],replyTo:m.Z.resend.forwardRepliesTo,subject:"Invoice: "+t.id,react:i(t)});if(r)throw r;return s}async addNewEmailAddress(e){let t=await this.upsertAudience();return this.resend.contacts.create({email:e,unsubscribed:!1,audienceId:t.resend_id})}async upsertAudience(){let e=await x.Z.audiences.findFirst();if(e)return e;let{data:{id:t,name:s}}=await this.resend.audiences.create({name:"Waiting List"});return x.Z.audiences.create({data:{resend_id:t,name:s}})}constructor(){this.resend=new b.R(process.env.RESEND_API_KEY)}}let v=new y},8552:(e,t,s)=>{"use strict";s.d(t,{R:()=>a});var r=s(1735);class i{async createCustomerPortal(e,t){return(await this.stripe.billingPortal.sessions.create({customer:e,return_url:t})).url}async findCheckoutSession(e){try{return await this.stripe.checkout.sessions.retrieve(e,{expand:["line_items"]})}catch(e){return console.error(e),null}}async getSubscription(e){return this.stripe.subscriptions.retrieve(e)}async getCheckoutSession(e){return this.stripe.checkout.sessions.retrieve(e)}constructor(){this.stripe=new r.Z(process.env.STRIPE_SECRET_KEY,{typescript:!0})}}let a=new i}},e=>{var t=t=>e(e.s=t);e.O(0,[520,288,225,657,735,19],()=>t(2848));var s=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/webhook/stripe/route"]=s}]);
//# sourceMappingURL=route.js.map