import Link from "next/link";
import { getSEOTags } from "@/libs/seo";
import config from "@/config";

// CHATGPT PROMPT TO GENERATE YOUR PRIVACY POLICY — replace with your own data 👇

// 1. Go to https://chat.openai.com/
// 2. Copy paste bellow
// 3. Replace the data with your own (if needed)
// 4. Paste the answer from ChatGPT directly in the <pre> tag below

// You are an excellent lawyer.

// I need your help to write a simple privacy policy for my website. Here is some context:
// - Website: https://micro.st
// - Name: Micro Sass Fast
// - Description: A JavaScript code boilerplate to help entrepreneurs launch their startups faster
// - User data collected: name, email and payment information
// - Non-personal data collection: web cookies
// - Purpose of Data Collection: Order processing
// - Data sharing: we do not share the data with any other parties
// - Children's Privacy: we do not collect any data from children
// - Updates to the Privacy Policy: users will be updated by email
// - Contact information: <EMAIL>

// Please write a simple privacy policy for my site. Add the current date.  Do not add or explain your reasoning. Answer:

export const metadata = getSEOTags({
  title: `Privacy Policy | ${config.appName}`,
  canonicalUrlRelative: "/privacy-policy",
});

const PrivacyPolicy = () => {
  return (
    <main className="max-w-xl mx-auto">
      <div className="p-5">
        <Link href="/" className="btn btn-ghost text-white">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
            className="w-5 h-5"
          >
            <path
              fillRule="evenodd"
              d="M15 10a.75.75 0 01-.75.75H7.612l2.158 1.96a.75.75 0 11-1.04 1.08l-3.5-3.25a.75.75 0 010-1.08l3.5-3.25a.75.75 0 111.04 1.08L7.612 9.25h6.638A.75.75 0 0115 10z"
              clipRule="evenodd"
            />
          </svg>{" "}
          Back
        </Link>
        <h1 className="text-3xl font-extrabold pb-6 text-white">
          Privacy Policy for Javian Picardo Portal
        </h1>

        <pre
          className="leading-relaxed whitespace-pre-wrap text-white"
          style={{ fontFamily: "sans-serif" }}
        >
          {`Last Updated: March 2024

Welcome to Javian Picardo Portal ("we," "us," or "our"), a service of JAVIAN PICARDO GROUP INC. This Privacy Policy outlines how we collect, use, and protect your personal information when you use our website located at https://javianpicardo.com (the "Portal").

By accessing or using the Portal, you agree to the terms of this Privacy Policy. If you do not agree with the practices described in this policy, please do not use the Portal.

1. Information We Collect

1.1 Personal Information
We may collect the following personal information:
- Name and contact information (email address, phone number)
- Professional information (company name, job title)
- Communication preferences
- Any information you voluntarily provide through contact forms or direct communication

1.2 Technical Information
We automatically collect:
- IP address
- Browser type and version
- Operating system
- Pages visited
- Time spent on the Portal
- Cookies and similar tracking technologies

2. How We Use Your Information

We use the collected information to:
- Provide and maintain our services
- Respond to your inquiries
- Send you updates about our services
- Improve our Portal's functionality
- Comply with legal obligations
- Protect against fraud or unauthorized access

3. Data Protection

We implement appropriate security measures to protect your personal information, including:
- Encryption of data in transit and at rest
- Regular security assessments
- Access controls and authentication
- Secure data storage practices

4. Third-Party Services

We may use third-party services that collect information about you, including:
- Analytics services
- Social media platforms
- Email service providers

5. Your Rights

You have the right to:
- Access your personal information
- Correct inaccurate data
- Request deletion of your data
- Opt-out of marketing communications
- Export your data

6. Contact Information

For privacy-related inquiries, please contact:
JAVIAN PICARDO GROUP INC
Email: <EMAIL>
Phone: +16477715300
Location: Hamilton, Ontario, Canada

7. Changes to This Policy

We may update this Privacy Policy periodically. We will notify you of any material changes by posting the new policy on this page and updating the "Last Updated" date.

8. Compliance

This Portal complies with applicable privacy laws and regulations, including:
- Personal Information Protection and Electronic Documents Act (PIPEDA)
- General Data Protection Regulation (GDPR) where applicable

9. Children's Privacy

Our Portal is not intended for children under 13. We do not knowingly collect personal information from children under 13.

10. International Users

By using our Portal, you consent to the transfer of your information to Canada and its processing in Canada.`}
        </pre>
      </div>
    </main>
  );
};

export default PrivacyPolicy;
