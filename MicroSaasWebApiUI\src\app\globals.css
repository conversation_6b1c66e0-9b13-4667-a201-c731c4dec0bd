@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
}

/* Force light theme background color */
:root {
  background-color: white;
  color: black;
}

/* Force dark theme background color */
.dark {
  background-color: #010814;
  color: white;
}

/* Ensure consistent background colors */
body {
  background-color: white;
  color: black;
}

.dark body {
  background-color: #010814;
  color: white;
}

/* Ensure consistent background for AboutMeJP component */
.bg-background {
  background-color: white !important;
}

.dark .bg-background {
  background-color: #010814 !important;
}

/* Ensure consistent text colors */
.text-zinc-600 {
  color: black;
}

.dark .text-zinc-600 {
  color: white;
}

/* Ensure consistent card backgrounds */
.card {
  background-color: white;
}

.dark .card {
  background-color: #1a1a1a;
}
