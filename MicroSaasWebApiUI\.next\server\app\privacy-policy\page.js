"use strict";(()=>{var e={};e.id=310,e.ids=[310],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},85807:e=>{e.exports=require("module")},55315:e=>{e.exports=require("path")},17360:e=>{e.exports=require("url")},6005:e=>{e.exports=require("node:crypto")},38759:(e,a,t)=>{t.r(a),t.d(a,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c}),t(18479),t(8130),t(7629),t(12523);var o=t(23191),r=t(88716),i=t(37922),n=t.n(i),s=t(95231),l={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);t.d(a,l);let c=["",{children:["privacy-policy",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,18479)),"C:\\Projects\\PersonalPortal\\src\\app\\privacy-policy\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e),async e=>(await Promise.resolve().then(t.bind(t,71150))).default(e)],apple:[async e=>(await Promise.resolve().then(t.bind(t,63601))).default(e)],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,51922))).default(e)],twitter:[async e=>(await Promise.resolve().then(t.bind(t,40680))).default(e)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,8130)),"C:\\Projects\\PersonalPortal\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,7629)),"C:\\Projects\\PersonalPortal\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"C:\\Projects\\PersonalPortal\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e),async e=>(await Promise.resolve().then(t.bind(t,71150))).default(e)],apple:[async e=>(await Promise.resolve().then(t.bind(t,63601))).default(e)],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,51922))).default(e)],twitter:[async e=>(await Promise.resolve().then(t.bind(t,40680))).default(e)],manifest:void 0}}],d=["C:\\Projects\\PersonalPortal\\src\\app\\privacy-policy\\page.tsx"],p="/privacy-policy/page",u={require:t,loadChunk:()=>Promise.resolve()},m=new o.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/privacy-policy/page",pathname:"/privacy-policy",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},18479:(e,a,t)=>{t.r(a),t.d(a,{default:()=>l,metadata:()=>s});var o=t(19510),r=t(57371),i=t(7264),n=t(1542);let s=(0,i.A)({title:`Privacy Policy | ${n.Z.appName}`,canonicalUrlRelative:"/privacy-policy"}),l=()=>o.jsx("main",{className:"max-w-xl mx-auto",children:(0,o.jsxs)("div",{className:"p-5",children:[(0,o.jsxs)(r.default,{href:"/",className:"btn btn-ghost text-white",children:[o.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"w-5 h-5",children:o.jsx("path",{fillRule:"evenodd",d:"M15 10a.75.75 0 01-.75.75H7.612l2.158 1.96a.75.75 0 11-1.04 1.08l-3.5-3.25a.75.75 0 010-1.08l3.5-3.25a.75.75 0 111.04 1.08L7.612 9.25h6.638A.75.75 0 0115 10z",clipRule:"evenodd"})})," ","Back"]}),o.jsx("h1",{className:"text-3xl font-extrabold pb-6 text-white",children:"Privacy Policy for Javian Picardo Portal"}),o.jsx("pre",{className:"leading-relaxed whitespace-pre-wrap text-white",style:{fontFamily:"sans-serif"},children:`Last Updated: March 2024

Welcome to Javian Picardo Portal ("we," "us," or "our"), a service of JAVIAN PICARDO GROUP INC. This Privacy Policy outlines how we collect, use, and protect your personal information when you use our website located at https://javianpicardo.com (the "Portal").

By accessing or using the Portal, you agree to the terms of this Privacy Policy. If you do not agree with the practices described in this policy, please do not use the Portal.

1. Information We Collect

1.1 Personal Information
We may collect the following personal information:
- Name and contact information (email address, phone number)
- Professional information (company name, job title)
- Communication preferences
- Any information you voluntarily provide through contact forms or direct communication

1.2 Technical Information
We automatically collect:
- IP address
- Browser type and version
- Operating system
- Pages visited
- Time spent on the Portal
- Cookies and similar tracking technologies

2. How We Use Your Information

We use the collected information to:
- Provide and maintain our services
- Respond to your inquiries
- Send you updates about our services
- Improve our Portal's functionality
- Comply with legal obligations
- Protect against fraud or unauthorized access

3. Data Protection

We implement appropriate security measures to protect your personal information, including:
- Encryption of data in transit and at rest
- Regular security assessments
- Access controls and authentication
- Secure data storage practices

4. Third-Party Services

We may use third-party services that collect information about you, including:
- Analytics services
- Social media platforms
- Email service providers

5. Your Rights

You have the right to:
- Access your personal information
- Correct inaccurate data
- Request deletion of your data
- Opt-out of marketing communications
- Export your data

6. Contact Information

For privacy-related inquiries, please contact:
JAVIAN PICARDO GROUP INC
Email: <EMAIL>
Phone: +16477715300
Location: Hamilton, Ontario, Canada

7. Changes to This Policy

We may update this Privacy Policy periodically. We will notify you of any material changes by posting the new policy on this page and updating the "Last Updated" date.

8. Compliance

This Portal complies with applicable privacy laws and regulations, including:
- Personal Information Protection and Electronic Documents Act (PIPEDA)
- General Data Protection Regulation (GDPR) where applicable

9. Children's Privacy

Our Portal is not intended for children under 13. We do not knowingly collect personal information from children under 13.

10. International Users

By using our Portal, you consent to the transfer of your information to Canada and its processing in Canada.`})]})})}};var a=require("../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),o=a.X(0,[948,70,251,746],()=>t(38759));module.exports=o})();