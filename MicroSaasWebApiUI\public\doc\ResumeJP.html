<!DOCTYPE html>
<!-- Created by pdf2htmlEX (https://github.com/pdf2htmlEX/pdf2htmlEX) -->
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta charset="utf-8" />
    <meta name="generator" content="pdf2htmlEX" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <style type="text/css">
      /*! 
 * Base CSS for pdf2htmlEX
 * Copyright 2012,2013 Lu Wang <<EMAIL>> 
 * https://github.com/pdf2htmlEX/pdf2htmlEX/blob/master/share/LICENSE
 */
      #sidebar {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        width: 250px;
        padding: 0;
        margin: 0;
        overflow: auto;
      }
      #page-container {
        position: absolute;
        top: 0;
        left: 0;
        margin: 0;
        padding: 0;
        border: 0;
      }
      @media screen {
        #sidebar.opened + #page-container {
          left: 250px;
        }
        #page-container {
          bottom: 0;
          right: 0;
          overflow: auto;
        }
        .loading-indicator {
          display: none;
        }
        .loading-indicator.active {
          display: block;
          position: absolute;
          width: 64px;
          height: 64px;
          top: 50%;
          left: 50%;
          margin-top: -32px;
          margin-left: -32px;
        }
        .loading-indicator img {
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          right: 0;
        }
      }
      @media print {
        @page {
          margin: 0;
        }
        html {
          margin: 0;
        }
        body {
          margin: 0;
          -webkit-print-color-adjust: exact;
        }
        #sidebar {
          display: none;
        }
        #page-container {
          width: auto;
          height: auto;
          overflow: visible;
          background-color: transparent;
        }
        .d {
          display: none;
        }
      }
      .pf {
        position: relative;
        background-color: white;
        overflow: hidden;
        margin: 0;
        border: 0;
      }
      .pc {
        position: absolute;
        border: 0;
        padding: 0;
        margin: 0;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        display: block;
        transform-origin: 0 0;
        -ms-transform-origin: 0 0;
        -webkit-transform-origin: 0 0;
      }
      .pc.opened {
        display: block;
      }
      .bf {
        position: absolute;
        border: 0;
        margin: 0;
        top: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
        -ms-user-select: none;
        -moz-user-select: none;
        -webkit-user-select: none;
        user-select: none;
      }
      .bi {
        position: absolute;
        border: 0;
        margin: 0;
        -ms-user-select: none;
        -moz-user-select: none;
        -webkit-user-select: none;
        user-select: none;
      }
      @media print {
        .pf {
          margin: 0;
          box-shadow: none;
          page-break-after: always;
          page-break-inside: avoid;
        }
        @-moz-document url-prefix() {
          .pf {
            overflow: visible;
            border: 1px solid #fff;
          }
          .pc {
            overflow: visible;
          }
        }
      }
      .c {
        position: absolute;
        border: 0;
        padding: 0;
        margin: 0;
        overflow: hidden;
        display: block;
      }
      .t {
        position: absolute;
        white-space: pre;
        font-size: 1px;
        transform-origin: 0 100%;
        -ms-transform-origin: 0 100%;
        -webkit-transform-origin: 0 100%;
        unicode-bidi: bidi-override;
        -moz-font-feature-settings: "liga" 0;
      }
      .t:after {
        content: "";
      }
      .t:before {
        content: "";
        display: inline-block;
      }
      .t span {
        position: relative;
        unicode-bidi: bidi-override;
      }
      ._ {
        display: inline-block;
        color: transparent;
        z-index: -1;
      }
      ::selection {
        background: rgba(127, 255, 255, 0.4);
      }
      ::-moz-selection {
        background: rgba(127, 255, 255, 0.4);
      }
      .pi {
        display: none;
      }
      .d {
        position: absolute;
        transform-origin: 0 100%;
        -ms-transform-origin: 0 100%;
        -webkit-transform-origin: 0 100%;
      }
      .it {
        border: 0;
        background-color: rgba(255, 255, 255, 0);
      }
      .ir:hover {
        cursor: pointer;
      }
    </style>
    <style type="text/css">
      /*! 
 * Fancy styles for pdf2htmlEX
 * Copyright 2012,2013 Lu Wang <<EMAIL>> 
 * https://github.com/pdf2htmlEX/pdf2htmlEX/blob/master/share/LICENSE
 */
      @keyframes fadein {
        from {
          opacity: 0;
        }
        to {
          opacity: 1;
        }
      }
      @-webkit-keyframes fadein {
        from {
          opacity: 0;
        }
        to {
          opacity: 1;
        }
      }
      @keyframes swing {
        0 {
          transform: rotate(0);
        }
        10% {
          transform: rotate(0);
        }
        90% {
          transform: rotate(720deg);
        }
        100% {
          transform: rotate(720deg);
        }
      }
      @-webkit-keyframes swing {
        0 {
          -webkit-transform: rotate(0);
        }
        10% {
          -webkit-transform: rotate(0);
        }
        90% {
          -webkit-transform: rotate(720deg);
        }
        100% {
          -webkit-transform: rotate(720deg);
        }
      }
      @media screen {
        #sidebar {
          background-color: #2f3236;
          background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0IiBoZWlnaHQ9IjQiPgo8cmVjdCB3aWR0aD0iNCIgaGVpZ2h0PSI0IiBmaWxsPSIjNDAzYzNmIj48L3JlY3Q+CjxwYXRoIGQ9Ik0wIDBMNCA0Wk00IDBMMCA0WiIgc3Ryb2tlLXdpZHRoPSIxIiBzdHJva2U9IiMxZTI5MmQiPjwvcGF0aD4KPC9zdmc+");
        }
        #outline {
          font-family: Georgia, Times, "Times New Roman", serif;
          font-size: 13px;
          margin: 2em 1em;
        }
        #outline ul {
          padding: 0;
        }
        #outline li {
          list-style-type: none;
          margin: 1em 0;
        }
        #outline li > ul {
          margin-left: 1em;
        }
        #outline a,
        #outline a:visited,
        #outline a:hover,
        #outline a:active {
          line-height: 1.2;
          color: #e8e8e8;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-decoration: none;
          display: block;
          overflow: hidden;
          outline: 0;
        }
        #outline a:hover {
          color: #0cf;
        }
        #page-container {
          background-color: #9e9e9e;
          background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1IiBoZWlnaHQ9IjUiPgo8cmVjdCB3aWR0aD0iNSIgaGVpZ2h0PSI1IiBmaWxsPSIjOWU5ZTllIj48L3JlY3Q+CjxwYXRoIGQ9Ik0wIDVMNSAwWk02IDRMNCA2Wk0tMSAxTDEgLTFaIiBzdHJva2U9IiM4ODgiIHN0cm9rZS13aWR0aD0iMSI+PC9wYXRoPgo8L3N2Zz4=");
          -webkit-transition: left 500ms;
          transition: left 500ms;
        }
        .pf {
          margin: 13px auto;
          box-shadow: 1px 1px 3px 1px #333;
          border-collapse: separate;
        }
        .pc.opened {
          -webkit-animation: fadein 100ms;
          animation: fadein 100ms;
        }
        .loading-indicator.active {
          -webkit-animation: swing 1.5s ease-in-out 0.01s infinite alternate
            none;
          animation: swing 1.5s ease-in-out 0.01s infinite alternate none;
        }
        .checked {
          background: no-repeat
            url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAWCAYAAADEtGw7AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH3goQDSYgDiGofgAAAslJREFUOMvtlM9LFGEYx7/vvOPM6ywuuyPFihWFBUsdNnA6KLIh+QPx4KWExULdHQ/9A9EfUodYmATDYg/iRewQzklFWxcEBcGgEplDkDtI6sw4PzrIbrOuedBb9MALD7zv+3m+z4/3Bf7bZS2bzQIAcrmcMDExcTeXy10DAFVVAQDksgFUVZ1ljD3yfd+0LOuFpmnvVVW9GHhkZAQcxwkNDQ2FSCQyRMgJxnVdy7KstKZpn7nwha6urqqfTqfPBAJAuVymlNLXoigOhfd5nmeiKL5TVTV+lmIKwAOA7u5u6Lped2BsbOwjY6yf4zgQQkAIAcedaPR9H67r3uYBQFEUFItFtLe332lpaVkUBOHK3t5eRtf1DwAwODiIubk5DA8PM8bYW1EU+wEgCIJqsCAIQAiB7/u253k2BQDDMJBKpa4mEon5eDx+UxAESJL0uK2t7XosFlvSdf0QAEmlUnlRFJ9Waho2Qghc1/U9z3uWz+eX+Wr+lL6SZfleEAQIggA8z6OpqSknimIvYyybSCReMsZ6TislhCAIAti2Dc/zejVNWwCAavN8339j27YbTg0AGGM3WltbP4WhlRWq6Q/btrs1TVsYHx+vNgqKoqBUKn2NRqPFxsbGJzzP05puUlpt0ukyOI6z7zjOwNTU1OLo6CgmJyf/gA3DgKIoWF1d/cIY24/FYgOU0pp0z/Ityzo8Pj5OTk9PbwHA+vp6zWghDC+VSiuRSOQgGo32UErJ38CO42wdHR09LBQK3zKZDDY2NupmFmF4R0cHVlZWlmRZ/iVJUn9FeWWcCCE4ODjYtG27Z2Zm5juAOmgdGAB2d3cBADs7O8uSJN2SZfl+WKlpmpumaT6Yn58vn/fs6XmbhmHMNjc3tzDGFI7jYJrm5vb29sDa2trPC/9aiqJUy5pOp4f6+vqeJ5PJBAB0dnZe/t8NBajx/z37Df5OGX8d13xzAAAAAElFTkSuQmCC);
        }
      }
    </style>
    <style type="text/css">
      .ff0 {
        font-family: sans-serif;
        visibility: hidden;
      }
      @font-face {
        font-family: ff1;
        src: url("data:application/font-woff;base64,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******************************************+pLN+htIxyMyd7qLn/pKHhqNwuemKVioWL2S2HPeiHWZYyrXOW1eWxTkXLm6HXjlswQpfYsjAZ1GmHDKDiMb5mnPVRP495EuDGF3kRvcgO7QnQsuirVAomRoM9TQdS80W/wgHz/q8l8u0VO6XCg2ej0M7akfsnHlb150zoRLxNpHlX4XgXucVC7vDvlJmpFVuTWSUK1dzr9e/XOhvvMb/OPwPBbBQp3icfc63TkIBAEDR8x6JPQo2LNEFBSs+wApoDFFEEawoWAYHBgfc/DZ+T77Am5z9Cv3f00AgFDNk1JhxEyZNiUuYNmPWnHlJCxYtWbZiVcqadWkZGzZt2bZjV9aeSE5ewb4Dh44cO1FUUnbqzLkLl6qu1Fy7UXeroenOvQePg5dnLS9etXW8effhUz8Ig9jw7893tRLlRppfvW6jm43+ABuaFTQAAAEAAf//AA94nGNgZGBg4AFiASBmAmIWCA0AAjsAJgB4nGNgYGBkAIJHfZKTGZDAEwaVVQAsbgPQAAAA")
          format("woff");
      }
      .ff1 {
        font-family: ff1;
        line-height: 1.036133;
        font-style: normal;
        font-weight: normal;
        visibility: visible;
      }
      @font-face {
        font-family: ff2;
        src: url("data:application/font-woff;base64,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")
          format("woff");
      }
      .ff2 {
        font-family: ff2;
        line-height: 1.03616;
        font-style: normal;
        font-weight: normal;
        visibility: visible;
      }
      .m0 {
        transform: matrix(0.375, 0, 0, 0.375, 0, 0);
        -ms-transform: matrix(0.375, 0, 0, 0.375, 0, 0);
        -webkit-transform: matrix(0.375, 0, 0, 0.375, 0, 0);
      }
      .m1 {
        transform: matrix(1.5, 0, 0, 1.5, 0, 0);
        -ms-transform: matrix(1.5, 0, 0, 1.5, 0, 0);
        -webkit-transform: matrix(1.5, 0, 0, 1.5, 0, 0);
      }
      .v0 {
        vertical-align: 0px;
      }
      .ls0 {
        letter-spacing: 0px;
      }
      .sc_ {
        text-shadow: none;
      }
      .sc0 {
        text-shadow: -0.015em 0 transparent, 0 0.015em transparent,
          0.015em 0 transparent, 0 -0.015em transparent;
      }
      @media screen and (-webkit-min-device-pixel-ratio: 0) {
        .sc_ {
          -webkit-text-stroke: 0px transparent;
        }
        .sc0 {
          -webkit-text-stroke: 0.015em transparent;
          text-shadow: none;
        }
      }
      .ws0 {
        word-spacing: 0px;
      }
      ._17 {
        margin-left: -977.477782px;
      }
      ._a {
        margin-left: -754.683677px;
      }
      ._15 {
        margin-left: -645.875784px;
      }
      ._7 {
        margin-left: -633.418749px;
      }
      ._19 {
        margin-left: -591.126761px;
      }
      ._5 {
        margin-left: -567.771934px;
      }
      ._4 {
        margin-left: -544.693683px;
      }
      ._e {
        margin-left: -527.169683px;
      }
      ._1b {
        margin-left: -432.214895px;
      }
      ._c {
        margin-left: -401.144353px;
      }
      ._10 {
        margin-left: -353.61977px;
      }
      ._d {
        margin-left: -345.306625px;
      }
      ._1c {
        margin-left: -316.371613px;
      }
      ._f {
        margin-left: -287.159114px;
      }
      ._11 {
        margin-left: -200.312313px;
      }
      ._8 {
        margin-left: -2.497234px;
      }
      ._0 {
        margin-left: -1.189029px;
      }
      ._1 {
        width: 1.35951px;
      }
      ._14 {
        width: 2.513688px;
      }
      ._13 {
        width: 73.691555px;
      }
      ._12 {
        width: 75.00456px;
      }
      ._1e {
        width: 80.586801px;
      }
      ._1d {
        width: 81.915232px;
      }
      ._2 {
        width: 641.76126px;
      }
      ._b {
        width: 645.226308px;
      }
      ._3 {
        width: 702.906311px;
      }
      ._18 {
        width: 706.954448px;
      }
      ._9 {
        width: 730.066007px;
      }
      ._6 {
        width: 743.815936px;
      }
      ._16 {
        width: 752.143136px;
      }
      ._1a {
        width: 758.551391px;
      }
      .fc5 {
        color: transparent;
      }
      .fc2 {
        color: rgb(106, 63, 109);
      }
      .fc4 {
        color: rgb(124, 124, 124);
      }
      .fc3 {
        color: rgb(0, 0, 0);
      }
      .fc1 {
        color: rgb(25, 25, 25);
      }
      .fc0 {
        color: rgb(255, 255, 255);
      }
      .fs4 {
        font-size: 31.993853px;
      }
      .fs2 {
        font-size: 35.650293px;
      }
      .fs5 {
        font-size: 40.220843px;
      }
      .fs1 {
        font-size: 48.447834px;
      }
      .fs3 {
        font-size: 55.760714px;
      }
      .fs0 {
        font-size: 92.325117px;
      }
      .y0 {
        bottom: 0px;
      }
      .y1 {
        bottom: 2.556478px;
      }
      .y52 {
        bottom: 24.680968px;
      }
      .y51 {
        bottom: 55.532183px;
      }
      .y50 {
        bottom: 86.383398px;
      }
      .y4f {
        bottom: 117.234613px;
      }
      .y32 {
        bottom: 123.747647px;
      }
      .y31 {
        bottom: 146.02908px;
      }
      .y4e {
        bottom: 148.085828px;
      }
      .y30 {
        bottom: 162.483061px;
      }
      .y4d {
        bottom: 178.937043px;
      }
      .y2f {
        bottom: 184.764494px;
      }
      .y2e {
        bottom: 207.045928px;
      }
      .y4c {
        bottom: 209.788258px;
      }
      .y2d {
        bottom: 223.499909px;
      }
      .y4b {
        bottom: 240.639473px;
      }
      .y55 {
        bottom: 241.325055px;
      }
      .y2c {
        bottom: 260.178575px;
      }
      .y4a {
        bottom: 271.490688px;
      }
      .y2b {
        bottom: 273.547435px;
      }
      .y2a {
        bottom: 286.916295px;
      }
      .y49 {
        bottom: 302.341903px;
      }
      .y29 {
        bottom: 303.370276px;
      }
      .y28 {
        bottom: 323.594962px;
      }
      .y48 {
        bottom: 333.193117px;
      }
      .y27 {
        bottom: 344.162438px;
      }
      .y47 {
        bottom: 364.044332px;
      }
      .y26 {
        bottom: 380.155522px;
      }
      .y46 {
        bottom: 394.895547px;
      }
      .y25 {
        bottom: 396.609504px;
      }
      .y24 {
        bottom: 413.063485px;
      }
      .y78 {
        bottom: 418.20535px;
      }
      .y45 {
        bottom: 425.746762px;
      }
      .y23 {
        bottom: 429.517466px;
      }
      .y77 {
        bottom: 440.486783px;
      }
      .y22 {
        bottom: 445.971448px;
      }
      .y84 {
        bottom: 458.31193px;
      }
      .y21 {
        bottom: 462.425429px;
      }
      .y44 {
        bottom: 467.910089px;
      }
      .y76 {
        bottom: 477.16545px;
      }
      .y20 {
        bottom: 484.706862px;
      }
      .y75 {
        bottom: 490.53431px;
      }
      .y1f {
        bottom: 501.160843px;
      }
      .y74 {
        bottom: 503.90317px;
      }
      .y73 {
        bottom: 517.272029px;
      }
      .y43 {
        bottom: 522.756694px;
      }
      .y1e {
        bottom: 523.442276px;
      }
      .y72 {
        bottom: 533.726011px;
      }
      .y42 {
        bottom: 538.867884px;
      }
      .y1d {
        bottom: 539.896258px;
      }
      .y71 {
        bottom: 553.950696px;
      }
      .y1c {
        bottom: 556.350239px;
      }
      .y41 {
        bottom: 564.234438px;
      }
      .y70 {
        bottom: 574.518173px;
      }
      .y1b {
        bottom: 578.631672px;
      }
      .y40 {
        bottom: 580.345628px;
      }
      .y1a {
        bottom: 595.085653px;
      }
      .y19 {
        bottom: 611.539634px;
      }
      .y3f {
        bottom: 612.9108px;
      }
      .y6f {
        bottom: 617.709874px;
      }
      .y54 {
        bottom: 629.364781px;
      }
      .y6e {
        bottom: 634.163855px;
      }
      .y18 {
        bottom: 648.218301px;
      }
      .y6d {
        bottom: 656.445288px;
      }
      .y17 {
        bottom: 661.587161px;
      }
      .y3e {
        bottom: 667.757404px;
      }
      .y6c {
        bottom: 672.899269px;
      }
      .y16 {
        bottom: 674.956021px;
      }
      .y3d {
        bottom: 681.126264px;
      }
      .y83 {
        bottom: 690.724416px;
      }
      .y15 {
        bottom: 691.410002px;
      }
      .y3c {
        bottom: 701.350949px;
      }
      .y6b {
        bottom: 709.577936px;
      }
      .y14 {
        bottom: 711.634687px;
      }
      .y3b {
        bottom: 721.918426px;
      }
      .y6a {
        bottom: 722.946796px;
      }
      .y13 {
        bottom: 732.202164px;
      }
      .y69 {
        bottom: 739.400777px;
      }
      .y3a {
        bottom: 741.114737px;
      }
      .y68 {
        bottom: 759.625462px;
      }
      .y12 {
        bottom: 768.195248px;
      }
      .y39 {
        bottom: 776.76503px;
      }
      .y67 {
        bottom: 780.192939px;
      }
      .y11 {
        bottom: 784.649229px;
      }
      .y10 {
        bottom: 806.930662px;
      }
      .y66 {
        bottom: 823.38464px;
      }
      .yf {
        bottom: 823.384644px;
      }
      .y38 {
        bottom: 831.611634px;
      }
      .y65 {
        bottom: 839.838621px;
      }
      .y53 {
        bottom: 841.20979px;
      }
      .y37 {
        bottom: 847.037242px;
      }
      .y80 {
        bottom: 851.150733px;
      }
      .ye {
        bottom: 860.06331px;
      }
      .y64 {
        bottom: 862.120054px;
      }
      .y36 {
        bottom: 874.460544px;
      }
      .yd {
        bottom: 876.517292px;
      }
      .y63 {
        bottom: 884.401487px;
      }
      .y35 {
        bottom: 887.829404px;
      }
      .y7f {
        bottom: 896.056391px;
      }
      .yc {
        bottom: 896.741977px;
      }
      .y62 {
        bottom: 900.855468px;
      }
      .y34 {
        bottom: 903.255011px;
      }
      .yb {
        bottom: 917.309454px;
      }
      .y82 {
        bottom: 918.680615px;
      }
      .y33 {
        bottom: 919.708993px;
      }
      .y61 {
        bottom: 937.876926px;
      }
      .ya {
        bottom: 952.959746px;
      }
      .y60 {
        bottom: 958.101612px;
      }
      .y7e {
        bottom: 977.297923px;
      }
      .y5f {
        bottom: 978.669088px;
      }
      .y9 {
        bottom: 1008.834725px;
      }
      .y7d {
        bottom: 1015.347755px;
      }
      .y5e {
        bottom: 1021.860789px;
      }
      .y5d {
        bottom: 1038.31477px;
      }
      .y8 {
        bottom: 1038.657566px;
      }
      .y7c {
        bottom: 1053.397587px;
      }
      .y5c {
        bottom: 1060.596204px;
      }
      .y5b {
        bottom: 1077.050185px;
      }
      .y7b {
        bottom: 1091.447418px;
      }
      .y7 {
        bottom: 1092.475796px;
      }
      .y81 {
        bottom: 1094.875331px;
      }
      .y6 {
        bottom: 1108.929778px;
      }
      .y5a {
        bottom: 1114.071643px;
      }
      .y5 {
        bottom: 1125.383759px;
      }
      .y7a {
        bottom: 1129.49725px;
      }
      .y59 {
        bottom: 1134.296328px;
      }
      .y4 {
        bottom: 1141.83774px;
      }
      .y58 {
        bottom: 1153.49264px;
      }
      .y79 {
        bottom: 1167.547082px;
      }
      .y3 {
        bottom: 1171.31779px;
      }
      .y57 {
        bottom: 1174.060116px;
      }
      .y2 {
        bottom: 1197.712718px;
      }
      .y56 {
        bottom: 1211.081574px;
      }
      .h6 {
        height: 24.479671px;
      }
      .h4 {
        height: 27.277348px;
      }
      .h7 {
        height: 30.774444px;
      }
      .h3 {
        height: 37.069217px;
      }
      .h5 {
        height: 42.66457px;
      }
      .h2 {
        height: 70.641337px;
      }
      .h1 {
        height: 1260.443522px;
      }
      .h0 {
        height: 1263px;
      }
      .w1 {
        width: 892.285691px;
      }
      .w0 {
        width: 892.5px;
      }
      .x0 {
        left: 0px;
      }
      .x1 {
        left: 35.993084px;
      }
      .x2 {
        left: 37.364249px;
      }
      .x4 {
        left: 50.733109px;
      }
      .x3 {
        left: 65.815925px;
      }
      .x5 {
        left: 571.090268px;
      }
      .x6 {
        left: 581.374006px;
      }
      .x7 {
        left: 582.745171px;
      }
      @media print {
        .v0 {
          vertical-align: 0pt;
        }
        .ls0 {
          letter-spacing: 0pt;
        }
        .ws0 {
          word-spacing: 0pt;
        }
        ._17 {
          margin-left: -868.86914pt;
        }
        ._a {
          margin-left: -670.829935pt;
        }
        ._15 {
          margin-left: -574.111808pt;
        }
        ._7 {
          margin-left: -563.038888pt;
        }
        ._19 {
          margin-left: -525.446009pt;
        }
        ._5 {
          margin-left: -504.686163pt;
        }
        ._4 {
          margin-left: -484.172163pt;
        }
        ._e {
          margin-left: -468.595274pt;
        }
        ._1b {
          margin-left: -384.191018pt;
        }
        ._c {
          margin-left: -356.572758pt;
        }
        ._10 {
          margin-left: -314.328685pt;
        }
        ._d {
          margin-left: -306.939222pt;
        }
        ._1c {
          margin-left: -281.219211pt;
        }
        ._f {
          margin-left: -255.252546pt;
        }
        ._11 {
          margin-left: -178.05539pt;
        }
        ._8 {
          margin-left: -2.219764pt;
        }
        ._0 {
          margin-left: -1.056914pt;
        }
        ._1 {
          width: 1.208454pt;
        }
        ._14 {
          width: 2.23439pt;
        }
        ._13 {
          width: 65.503604pt;
        }
        ._12 {
          width: 66.67072pt;
        }
        ._1e {
          width: 71.632712pt;
        }
        ._1d {
          width: 72.813539pt;
        }
        ._2 {
          width: 570.454453pt;
        }
        ._b {
          width: 573.534496pt;
        }
        ._3 {
          width: 624.80561pt;
        }
        ._18 {
          width: 628.403953pt;
        }
        ._9 {
          width: 648.947562pt;
        }
        ._6 {
          width: 661.169721pt;
        }
        ._16 {
          width: 668.571677pt;
        }
        ._1a {
          width: 674.267903pt;
        }
        .fs4 {
          font-size: 28.43898pt;
        }
        .fs2 {
          font-size: 31.689149pt;
        }
        .fs5 {
          font-size: 35.751861pt;
        }
        .fs1 {
          font-size: 43.064741pt;
        }
        .fs3 {
          font-size: 49.565079pt;
        }
        .fs0 {
          font-size: 82.066771pt;
        }
        .y0 {
          bottom: 0pt;
        }
        .y1 {
          bottom: 2.272425pt;
        }
        .y52 {
          bottom: 21.938638pt;
        }
        .y51 {
          bottom: 49.36194pt;
        }
        .y50 {
          bottom: 76.785243pt;
        }
        .y4f {
          bottom: 104.208545pt;
        }
        .y32 {
          bottom: 109.997909pt;
        }
        .y31 {
          bottom: 129.803627pt;
        }
        .y4e {
          bottom: 131.631847pt;
        }
        .y30 {
          bottom: 144.429388pt;
        }
        .y4d {
          bottom: 159.055149pt;
        }
        .y2f {
          bottom: 164.235106pt;
        }
        .y2e {
          bottom: 184.040824pt;
        }
        .y4c {
          bottom: 186.478451pt;
        }
        .y2d {
          bottom: 198.666586pt;
        }
        .y4b {
          bottom: 213.901753pt;
        }
        .y55 {
          bottom: 214.51116pt;
        }
        .y2c {
          bottom: 231.269845pt;
        }
        .y4a {
          bottom: 241.325056pt;
        }
        .y2b {
          bottom: 243.153276pt;
        }
        .y2a {
          bottom: 255.036707pt;
        }
        .y49 {
          bottom: 268.748358pt;
        }
        .y29 {
          bottom: 269.662468pt;
        }
        .y28 {
          bottom: 287.639966pt;
        }
        .y48 {
          bottom: 296.17166pt;
        }
        .y27 {
          bottom: 305.922167pt;
        }
        .y47 {
          bottom: 323.594962pt;
        }
        .y26 {
          bottom: 337.91602pt;
        }
        .y46 {
          bottom: 351.018264pt;
        }
        .y25 {
          bottom: 352.541781pt;
        }
        .y24 {
          bottom: 367.167542pt;
        }
        .y78 {
          bottom: 371.738089pt;
        }
        .y45 {
          bottom: 378.441566pt;
        }
        .y23 {
          bottom: 381.793303pt;
        }
        .y77 {
          bottom: 391.543807pt;
        }
        .y22 {
          bottom: 396.419065pt;
        }
        .y84 {
          bottom: 407.388382pt;
        }
        .y21 {
          bottom: 411.044826pt;
        }
        .y44 {
          bottom: 415.920079pt;
        }
        .y76 {
          bottom: 424.147067pt;
        }
        .y20 {
          bottom: 430.850544pt;
        }
        .y75 {
          bottom: 436.030498pt;
        }
        .y1f {
          bottom: 445.476305pt;
        }
        .y74 {
          bottom: 447.913928pt;
        }
        .y73 {
          bottom: 459.797359pt;
        }
        .y43 {
          bottom: 464.672617pt;
        }
        .y1e {
          bottom: 465.282023pt;
        }
        .y72 {
          bottom: 474.423121pt;
        }
        .y42 {
          bottom: 478.993674pt;
        }
        .y1d {
          bottom: 479.907784pt;
        }
        .y71 {
          bottom: 492.400619pt;
        }
        .y1c {
          bottom: 494.533546pt;
        }
        .y41 {
          bottom: 501.541723pt;
        }
        .y70 {
          bottom: 510.68282pt;
        }
        .y1b {
          bottom: 514.339264pt;
        }
        .y40 {
          bottom: 515.862781pt;
        }
        .y1a {
          bottom: 528.965025pt;
        }
        .y19 {
          bottom: 543.590786pt;
        }
        .y3f {
          bottom: 544.8096pt;
        }
        .y6f {
          bottom: 549.075443pt;
        }
        .y54 {
          bottom: 559.435361pt;
        }
        .y6e {
          bottom: 563.701204pt;
        }
        .y18 {
          bottom: 576.194045pt;
        }
        .y6d {
          bottom: 583.506923pt;
        }
        .y17 {
          bottom: 588.077476pt;
        }
        .y3e {
          bottom: 593.562137pt;
        }
        .y6c {
          bottom: 598.132684pt;
        }
        .y16 {
          bottom: 599.960907pt;
        }
        .y3d {
          bottom: 605.445568pt;
        }
        .y83 {
          bottom: 613.977258pt;
        }
        .y15 {
          bottom: 614.586668pt;
        }
        .y3c {
          bottom: 623.423066pt;
        }
        .y6b {
          bottom: 630.735943pt;
        }
        .y14 {
          bottom: 632.564167pt;
        }
        .y3b {
          bottom: 641.705267pt;
        }
        .y6a {
          bottom: 642.619374pt;
        }
        .y13 {
          bottom: 650.846368pt;
        }
        .y69 {
          bottom: 657.245135pt;
        }
        .y3a {
          bottom: 658.768655pt;
        }
        .y68 {
          bottom: 675.222633pt;
        }
        .y12 {
          bottom: 682.840221pt;
        }
        .y39 {
          bottom: 690.457804pt;
        }
        .y67 {
          bottom: 693.504835pt;
        }
        .y11 {
          bottom: 697.465982pt;
        }
        .y10 {
          bottom: 717.2717pt;
        }
        .y66 {
          bottom: 731.897458pt;
        }
        .yf {
          bottom: 731.897461pt;
        }
        .y38 {
          bottom: 739.210342pt;
        }
        .y65 {
          bottom: 746.523219pt;
        }
        .y53 {
          bottom: 747.742036pt;
        }
        .y37 {
          bottom: 752.921993pt;
        }
        .y80 {
          bottom: 756.57843pt;
        }
        .ye {
          bottom: 764.50072pt;
        }
        .y64 {
          bottom: 766.328937pt;
        }
        .y36 {
          bottom: 777.298261pt;
        }
        .yd {
          bottom: 779.126481pt;
        }
        .y63 {
          bottom: 786.134655pt;
        }
        .y35 {
          bottom: 789.181692pt;
        }
        .y7f {
          bottom: 796.494569pt;
        }
        .yc {
          bottom: 797.10398pt;
        }
        .y62 {
          bottom: 800.760416pt;
        }
        .y34 {
          bottom: 802.893343pt;
        }
        .yb {
          bottom: 815.386181pt;
        }
        .y82 {
          bottom: 816.604991pt;
        }
        .y33 {
          bottom: 817.519105pt;
        }
        .y61 {
          bottom: 833.668379pt;
        }
        .ya {
          bottom: 847.07533pt;
        }
        .y60 {
          bottom: 851.645877pt;
        }
        .y7e {
          bottom: 868.709265pt;
        }
        .y5f {
          bottom: 869.928078pt;
        }
        .y9 {
          bottom: 896.741977pt;
        }
        .y7d {
          bottom: 902.531338pt;
        }
        .y5e {
          bottom: 908.320702pt;
        }
        .y5d {
          bottom: 922.946463pt;
        }
        .y8 {
          bottom: 923.25117pt;
        }
        .y7c {
          bottom: 936.35341pt;
        }
        .y5c {
          bottom: 942.752181pt;
        }
        .y5b {
          bottom: 957.377942pt;
        }
        .y7b {
          bottom: 970.175483pt;
        }
        .y7 {
          bottom: 971.089597pt;
        }
        .y81 {
          bottom: 973.222517pt;
        }
        .y6 {
          bottom: 985.715358pt;
        }
        .y5a {
          bottom: 990.285905pt;
        }
        .y5 {
          bottom: 1000.341119pt;
        }
        .y7a {
          bottom: 1003.997556pt;
        }
        .y59 {
          bottom: 1008.263403pt;
        }
        .y4 {
          bottom: 1014.96688pt;
        }
        .y58 {
          bottom: 1025.326791pt;
        }
        .y79 {
          bottom: 1037.819628pt;
        }
        .y3 {
          bottom: 1041.171369pt;
        }
        .y57 {
          bottom: 1043.608992pt;
        }
        .y2 {
          bottom: 1064.633527pt;
        }
        .y56 {
          bottom: 1076.516955pt;
        }
        .h6 {
          height: 21.759708pt;
        }
        .h4 {
          height: 24.246532pt;
        }
        .h7 {
          height: 27.355061pt;
        }
        .h3 {
          height: 32.950415pt;
        }
        .h5 {
          height: 37.924062pt;
        }
        .h2 {
          height: 62.7923pt;
        }
        .h1 {
          height: 1120.394242pt;
        }
        .h0 {
          height: 1122.666667pt;
        }
        .w1 {
          width: 793.142837pt;
        }
        .w0 {
          width: 793.333333pt;
        }
        .x0 {
          left: 0pt;
        }
        .x1 {
          left: 31.993853pt;
        }
        .x2 {
          left: 33.212666pt;
        }
        .x4 {
          left: 45.096097pt;
        }
        .x3 {
          left: 58.503045pt;
        }
        .x5 {
          left: 507.635793pt;
        }
        .x6 {
          left: 516.776894pt;
        }
        .x7 {
          left: 517.995708pt;
        }
      }
    </style>
    <script>
      /*
 Copyright 2012 Mozilla Foundation 
 Copyright 2013 Lu Wang <<EMAIL>>
 Apachine License Version 2.0 
*/
      (function () {
        function b(a, b, e, f) {
          var c = (a.className || "").split(/\s+/g);
          "" === c[0] && c.shift();
          var d = c.indexOf(b);
          0 > d && e && c.push(b);
          0 <= d && f && c.splice(d, 1);
          a.className = c.join(" ");
          return 0 <= d;
        }
        if (!("classList" in document.createElement("div"))) {
          var e = {
            add: function (a) {
              b(this.element, a, !0, !1);
            },
            contains: function (a) {
              return b(this.element, a, !1, !1);
            },
            remove: function (a) {
              b(this.element, a, !1, !0);
            },
            toggle: function (a) {
              b(this.element, a, !0, !0);
            },
          };
          Object.defineProperty(HTMLElement.prototype, "classList", {
            get: function () {
              if (this._classList) return this._classList;
              var a = Object.create(e, {
                element: { value: this, writable: !1, enumerable: !0 },
              });
              Object.defineProperty(this, "_classList", {
                value: a,
                writable: !1,
                enumerable: !1,
              });
              return a;
            },
            enumerable: !0,
          });
        }
      })();
    </script>
    <script>
      (function () {
        /*
 pdf2htmlEX.js: Core UI functions for pdf2htmlEX 
 Copyright 2012,2013 Lu Wang <<EMAIL>> and other contributors 
 https://github.com/pdf2htmlEX/pdf2htmlEX/blob/master/share/LICENSE 
*/
        var pdf2htmlEX = (window.pdf2htmlEX = window.pdf2htmlEX || {}),
          CSS_CLASS_NAMES = {
            page_frame: "pf",
            page_content_box: "pc",
            page_data: "pi",
            background_image: "bi",
            link: "l",
            input_radio: "ir",
            __dummy__: "no comma",
          },
          DEFAULT_CONFIG = {
            container_id: "page-container",
            sidebar_id: "sidebar",
            outline_id: "outline",
            loading_indicator_cls: "loading-indicator",
            preload_pages: 3,
            render_timeout: 100,
            scale_step: 0.9,
            key_handler: !0,
            hashchange_handler: !0,
            view_history_handler: !0,
            __dummy__: "no comma",
          },
          EPS = 1e-6;
        function invert(a) {
          var b = a[0] * a[3] - a[1] * a[2];
          return [
            a[3] / b,
            -a[1] / b,
            -a[2] / b,
            a[0] / b,
            (a[2] * a[5] - a[3] * a[4]) / b,
            (a[1] * a[4] - a[0] * a[5]) / b,
          ];
        }
        function transform(a, b) {
          return [
            a[0] * b[0] + a[2] * b[1] + a[4],
            a[1] * b[0] + a[3] * b[1] + a[5],
          ];
        }
        function get_page_number(a) {
          return parseInt(a.getAttribute("data-page-no"), 16);
        }
        function disable_dragstart(a) {
          for (var b = 0, c = a.length; b < c; ++b)
            a[b].addEventListener(
              "dragstart",
              function () {
                return !1;
              },
              !1
            );
        }
        function clone_and_extend_objs(a) {
          for (var b = {}, c = 0, e = arguments.length; c < e; ++c) {
            var h = arguments[c],
              d;
            for (d in h) h.hasOwnProperty(d) && (b[d] = h[d]);
          }
          return b;
        }
        function Page(a) {
          if (a) {
            this.shown = this.loaded = !1;
            this.page = a;
            this.num = get_page_number(a);
            this.original_height = a.clientHeight;
            this.original_width = a.clientWidth;
            var b = a.getElementsByClassName(
              CSS_CLASS_NAMES.page_content_box
            )[0];
            b &&
              ((this.content_box = b),
              (this.original_scale = this.cur_scale =
                this.original_height / b.clientHeight),
              (this.page_data = JSON.parse(
                a
                  .getElementsByClassName(CSS_CLASS_NAMES.page_data)[0]
                  .getAttribute("data-data")
              )),
              (this.ctm = this.page_data.ctm),
              (this.ictm = invert(this.ctm)),
              (this.loaded = !0));
          }
        }
        Page.prototype = {
          hide: function () {
            this.loaded &&
              this.shown &&
              (this.content_box.classList.remove("opened"), (this.shown = !1));
          },
          show: function () {
            this.loaded &&
              !this.shown &&
              (this.content_box.classList.add("opened"), (this.shown = !0));
          },
          rescale: function (a) {
            this.cur_scale = 0 === a ? this.original_scale : a;
            this.loaded &&
              ((a = this.content_box.style),
              (a.msTransform =
                a.webkitTransform =
                a.transform =
                  "scale(" + this.cur_scale.toFixed(3) + ")"));
            a = this.page.style;
            a.height = this.original_height * this.cur_scale + "px";
            a.width = this.original_width * this.cur_scale + "px";
          },
          view_position: function () {
            var a = this.page,
              b = a.parentNode;
            return [
              b.scrollLeft - a.offsetLeft - a.clientLeft,
              b.scrollTop - a.offsetTop - a.clientTop,
            ];
          },
          height: function () {
            return this.page.clientHeight;
          },
          width: function () {
            return this.page.clientWidth;
          },
        };
        function Viewer(a) {
          this.config = clone_and_extend_objs(
            DEFAULT_CONFIG,
            0 < arguments.length ? a : {}
          );
          this.pages_loading = [];
          this.init_before_loading_content();
          var b = this;
          document.addEventListener(
            "DOMContentLoaded",
            function () {
              b.init_after_loading_content();
            },
            !1
          );
        }
        Viewer.prototype = {
          scale: 1,
          cur_page_idx: 0,
          first_page_idx: 0,
          init_before_loading_content: function () {
            this.pre_hide_pages();
          },
          initialize_radio_button: function () {
            for (
              var a = document.getElementsByClassName(
                  CSS_CLASS_NAMES.input_radio
                ),
                b = 0;
              b < a.length;
              b++
            )
              a[b].addEventListener("click", function () {
                this.classList.toggle("checked");
              });
          },
          init_after_loading_content: function () {
            this.sidebar = document.getElementById(this.config.sidebar_id);
            this.outline = document.getElementById(this.config.outline_id);
            this.container = document.getElementById(this.config.container_id);
            this.loading_indicator = document.getElementsByClassName(
              this.config.loading_indicator_cls
            )[0];
            for (
              var a = !0, b = this.outline.childNodes, c = 0, e = b.length;
              c < e;
              ++c
            )
              if ("ul" === b[c].nodeName.toLowerCase()) {
                a = !1;
                break;
              }
            a || this.sidebar.classList.add("opened");
            this.find_pages();
            if (0 != this.pages.length) {
              disable_dragstart(
                document.getElementsByClassName(
                  CSS_CLASS_NAMES.background_image
                )
              );
              this.config.key_handler && this.register_key_handler();
              var h = this;
              this.config.hashchange_handler &&
                window.addEventListener(
                  "hashchange",
                  function (a) {
                    h.navigate_to_dest(document.location.hash.substring(1));
                  },
                  !1
                );
              this.config.view_history_handler &&
                window.addEventListener(
                  "popstate",
                  function (a) {
                    a.state && h.navigate_to_dest(a.state);
                  },
                  !1
                );
              this.container.addEventListener(
                "scroll",
                function () {
                  h.update_page_idx();
                  h.schedule_render(!0);
                },
                !1
              );
              [this.outline]
                .concat(Array.from(this.container.querySelectorAll("a.l")))
                .forEach(function (a) {
                  a.addEventListener("click", h.link_handler.bind(h), !1);
                });
              this.initialize_radio_button();
              this.render();
            }
          },
          find_pages: function () {
            for (
              var a = [],
                b = {},
                c = this.container.childNodes,
                e = 0,
                h = c.length;
              e < h;
              ++e
            ) {
              var d = c[e];
              d.nodeType === Node.ELEMENT_NODE &&
                d.classList.contains(CSS_CLASS_NAMES.page_frame) &&
                ((d = new Page(d)), a.push(d), (b[d.num] = a.length - 1));
            }
            this.pages = a;
            this.page_map = b;
          },
          load_page: function (a, b, c) {
            var e = this.pages;
            if (
              !(
                a >= e.length || ((e = e[a]), e.loaded || this.pages_loading[a])
              )
            ) {
              var e = e.page,
                h = e.getAttribute("data-page-url");
              if (h) {
                this.pages_loading[a] = !0;
                var d = e.getElementsByClassName(
                  this.config.loading_indicator_cls
                )[0];
                "undefined" === typeof d &&
                  ((d = this.loading_indicator.cloneNode(!0)),
                  d.classList.add("active"),
                  e.appendChild(d));
                var f = this,
                  g = new XMLHttpRequest();
                g.open("GET", h, !0);
                g.onload = function () {
                  if (200 === g.status || 0 === g.status) {
                    var b = document.createElement("div");
                    b.innerHTML = g.responseText;
                    for (
                      var d = null, b = b.childNodes, e = 0, h = b.length;
                      e < h;
                      ++e
                    ) {
                      var p = b[e];
                      if (
                        p.nodeType === Node.ELEMENT_NODE &&
                        p.classList.contains(CSS_CLASS_NAMES.page_frame)
                      ) {
                        d = p;
                        break;
                      }
                    }
                    b = f.pages[a];
                    f.container.replaceChild(d, b.page);
                    b = new Page(d);
                    f.pages[a] = b;
                    b.hide();
                    b.rescale(f.scale);
                    disable_dragstart(
                      d.getElementsByClassName(CSS_CLASS_NAMES.background_image)
                    );
                    f.schedule_render(!1);
                    c && c(b);
                  }
                  delete f.pages_loading[a];
                };
                g.send(null);
              }
              void 0 === b && (b = this.config.preload_pages);
              0 < --b &&
                ((f = this),
                setTimeout(function () {
                  f.load_page(a + 1, b);
                }, 0));
            }
          },
          pre_hide_pages: function () {
            var a =
                "@media screen{." +
                CSS_CLASS_NAMES.page_content_box +
                "{display:none;}}",
              b = document.createElement("style");
            b.styleSheet
              ? (b.styleSheet.cssText = a)
              : b.appendChild(document.createTextNode(a));
            document.head.appendChild(b);
          },
          render: function () {
            for (
              var a = this.container,
                b = a.scrollTop,
                c = a.clientHeight,
                a = b - c,
                b = b + c + c,
                c = this.pages,
                e = 0,
                h = c.length;
              e < h;
              ++e
            ) {
              var d = c[e],
                f = d.page,
                g = f.offsetTop + f.clientTop,
                f = g + f.clientHeight;
              g <= b && f >= a
                ? d.loaded
                  ? d.show()
                  : this.load_page(e)
                : d.hide();
            }
          },
          update_page_idx: function () {
            var a = this.pages,
              b = a.length;
            if (!(2 > b)) {
              for (
                var c = this.container,
                  e = c.scrollTop,
                  c = e + c.clientHeight,
                  h = -1,
                  d = b,
                  f = d - h;
                1 < f;

              ) {
                var g = h + Math.floor(f / 2),
                  f = a[g].page;
                f.offsetTop + f.clientTop + f.clientHeight >= e
                  ? (d = g)
                  : (h = g);
                f = d - h;
              }
              this.first_page_idx = d;
              for (var g = (h = this.cur_page_idx), k = 0; d < b; ++d) {
                var f = a[d].page,
                  l = f.offsetTop + f.clientTop,
                  f = f.clientHeight;
                if (l > c) break;
                f = (Math.min(c, l + f) - Math.max(e, l)) / f;
                if (d === h && Math.abs(f - 1) <= EPS) {
                  g = h;
                  break;
                }
                f > k && ((k = f), (g = d));
              }
              this.cur_page_idx = g;
            }
          },
          schedule_render: function (a) {
            if (void 0 !== this.render_timer) {
              if (!a) return;
              clearTimeout(this.render_timer);
            }
            var b = this;
            this.render_timer = setTimeout(function () {
              delete b.render_timer;
              b.render();
            }, this.config.render_timeout);
          },
          register_key_handler: function () {
            var a = this;
            window.addEventListener(
              "DOMMouseScroll",
              function (b) {
                if (b.ctrlKey) {
                  b.preventDefault();
                  var c = a.container,
                    e = c.getBoundingClientRect(),
                    c = [
                      b.clientX - e.left - c.clientLeft,
                      b.clientY - e.top - c.clientTop,
                    ];
                  a.rescale(Math.pow(a.config.scale_step, b.detail), !0, c);
                }
              },
              !1
            );
            window.addEventListener(
              "keydown",
              function (b) {
                var c = !1,
                  e = b.ctrlKey || b.metaKey,
                  h = b.altKey;
                switch (b.keyCode) {
                  case 61:
                  case 107:
                  case 187:
                    e && (a.rescale(1 / a.config.scale_step, !0), (c = !0));
                    break;
                  case 173:
                  case 109:
                  case 189:
                    e && (a.rescale(a.config.scale_step, !0), (c = !0));
                    break;
                  case 48:
                    e && (a.rescale(0, !1), (c = !0));
                    break;
                  case 33:
                    h
                      ? a.scroll_to(a.cur_page_idx - 1)
                      : (a.container.scrollTop -= a.container.clientHeight);
                    c = !0;
                    break;
                  case 34:
                    h
                      ? a.scroll_to(a.cur_page_idx + 1)
                      : (a.container.scrollTop += a.container.clientHeight);
                    c = !0;
                    break;
                  case 35:
                    a.container.scrollTop = a.container.scrollHeight;
                    c = !0;
                    break;
                  case 36:
                    (a.container.scrollTop = 0), (c = !0);
                }
                c && b.preventDefault();
              },
              !1
            );
          },
          rescale: function (a, b, c) {
            var e = this.scale;
            this.scale = a = 0 === a ? 1 : b ? e * a : a;
            c || (c = [0, 0]);
            b = this.container;
            c[0] += b.scrollLeft;
            c[1] += b.scrollTop;
            for (
              var h = this.pages, d = h.length, f = this.first_page_idx;
              f < d;
              ++f
            ) {
              var g = h[f].page;
              if (g.offsetTop + g.clientTop >= c[1]) break;
            }
            g = f - 1;
            0 > g && (g = 0);
            var g = h[g].page,
              k = g.clientWidth,
              f = g.clientHeight,
              l = g.offsetLeft + g.clientLeft,
              m = c[0] - l;
            0 > m ? (m = 0) : m > k && (m = k);
            k = g.offsetTop + g.clientTop;
            c = c[1] - k;
            0 > c ? (c = 0) : c > f && (c = f);
            for (f = 0; f < d; ++f) h[f].rescale(a);
            b.scrollLeft += (m / e) * a + g.offsetLeft + g.clientLeft - m - l;
            b.scrollTop += (c / e) * a + g.offsetTop + g.clientTop - c - k;
            this.schedule_render(!0);
          },
          fit_width: function () {
            var a = this.cur_page_idx;
            this.rescale(
              this.container.clientWidth / this.pages[a].width(),
              !0
            );
            this.scroll_to(a);
          },
          fit_height: function () {
            var a = this.cur_page_idx;
            this.rescale(
              this.container.clientHeight / this.pages[a].height(),
              !0
            );
            this.scroll_to(a);
          },
          get_containing_page: function (a) {
            for (; a; ) {
              if (
                a.nodeType === Node.ELEMENT_NODE &&
                a.classList.contains(CSS_CLASS_NAMES.page_frame)
              ) {
                a = get_page_number(a);
                var b = this.page_map;
                return a in b ? this.pages[b[a]] : null;
              }
              a = a.parentNode;
            }
            return null;
          },
          link_handler: function (a) {
            var b = a.target,
              c = b.getAttribute("data-dest-detail");
            c ||
              ((b = a.currentTarget), (c = b.getAttribute("data-dest-detail")));
            if (c) {
              if (this.config.view_history_handler)
                try {
                  var e = this.get_current_view_hash();
                  window.history.replaceState(e, "", "#" + e);
                  window.history.pushState(c, "", "#" + c);
                } catch (h) {}
              this.navigate_to_dest(c, this.get_containing_page(b));
              a.preventDefault();
            }
          },
          navigate_to_dest: function (a, b) {
            try {
              var c = JSON.parse(a);
            } catch (e) {
              return;
            }
            if (c instanceof Array) {
              var h = c[0],
                d = this.page_map;
              if (h in d) {
                for (
                  var f = d[h], h = this.pages[f], d = 2, g = c.length;
                  d < g;
                  ++d
                ) {
                  var k = c[d];
                  if (null !== k && "number" !== typeof k) return;
                }
                for (; 6 > c.length; ) c.push(null);
                var g = b || this.pages[this.cur_page_idx],
                  d = g.view_position(),
                  d = transform(g.ictm, [d[0], g.height() - d[1]]),
                  g = this.scale,
                  l = [0, 0],
                  m = !0,
                  k = !1,
                  n = this.scale;
                switch (c[1]) {
                  case "XYZ":
                    l = [
                      null === c[2] ? d[0] : c[2] * n,
                      null === c[3] ? d[1] : c[3] * n,
                    ];
                    g = c[4];
                    if (null === g || 0 === g) g = this.scale;
                    k = !0;
                    break;
                  case "Fit":
                  case "FitB":
                    l = [0, 0];
                    k = !0;
                    break;
                  case "FitH":
                  case "FitBH":
                    l = [0, null === c[2] ? d[1] : c[2] * n];
                    k = !0;
                    break;
                  case "FitV":
                  case "FitBV":
                    l = [null === c[2] ? d[0] : c[2] * n, 0];
                    k = !0;
                    break;
                  case "FitR":
                    (l = [c[2] * n, c[5] * n]), (m = !1), (k = !0);
                }
                if (k) {
                  this.rescale(g, !1);
                  var p = this,
                    c = function (a) {
                      l = transform(a.ctm, l);
                      m && (l[1] = a.height() - l[1]);
                      p.scroll_to(f, l);
                    };
                  h.loaded
                    ? c(h)
                    : (this.load_page(f, void 0, c), this.scroll_to(f));
                }
              }
            }
          },
          scroll_to: function (a, b) {
            var c = this.pages;
            if (!(0 > a || a >= c.length)) {
              c = c[a].view_position();
              void 0 === b && (b = [0, 0]);
              var e = this.container;
              e.scrollLeft += b[0] - c[0];
              e.scrollTop += b[1] - c[1];
            }
          },
          get_current_view_hash: function () {
            var a = [],
              b = this.pages[this.cur_page_idx];
            a.push(b.num);
            a.push("XYZ");
            var c = b.view_position(),
              c = transform(b.ictm, [c[0], b.height() - c[1]]);
            a.push(c[0] / this.scale);
            a.push(c[1] / this.scale);
            a.push(this.scale);
            return JSON.stringify(a);
          },
        };
        pdf2htmlEX.Viewer = Viewer;
      })();
    </script>
    <script>
      try {
        pdf2htmlEX.defaultViewer = new pdf2htmlEX.Viewer({});
      } catch (e) {}
    </script>
    <title></title>
  </head>
  <body>
    <div id="sidebar">
      <div id="outline"></div>
    </div>
    <div id="page-container">
      <div id="pf1" class="pf w0 h0" data-page-no="1">
        <div class="pc pc1 w0 h0">
          <img
            class="bi x0 y0 w0 h0"
            alt=""
            src="data:image/png;base64,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"
          />
          <div class="c x0 y1 w1 h1">
            <div class="t m0 x1 h2 y2 ff1 fs0 fc0 sc0 ls0 ws0">
              Ja<span class="_ _0"></span>vian<span class="_ _1"></span>
              Pic<span class="_ _1"></span>ardo
            </div>
            <div class="t m0 x2 h3 y3 ff1 fs1 fc1 sc0 ls0 ws0">
              Princip<span class="_ _1"></span>al/F<span class="_ _0"></span>ull
              Sta<span class="_ _1"></span>ck Sof<span class="_ _1"></span
              >t<span class="_ _1"></span>ware Eng<span class="_ _1"></span
              >ineer<span class="_ _1"></span>
            </div>
            <div class="t m0 x2 h4 y4 ff1 fs2 fc0 sc0 ls0 ws0">
              16+ years<span class="_ _1"></span> experience<span
                class="_ _1"
              ></span
              >d &amp; result-<span class="_ _1"></span>oriented Tech Lead
              wi<span class="_ _1"></span>th a trac<span class="_ _1"></span>k
              record of<span class="_ _1"></span> spearhead<span
                class="_ _1"
              ></span
              >ing teams t<span class="_ _1"></span>o develop n<span
                class="_ _1"
              ></span
              >umerous c<span class="_ _1"></span>ommercial<span
                class="_ _1"
              ></span
              >ly
            </div>
            <div class="t m0 x2 h4 y5 ff1 fs2 fc0 sc0 ls0 ws0">
              successf<span class="_ _1"></span>ul applicati<span
                class="_ _1"
              ></span
              >ons &amp; playe<span class="_ _1"></span>d a missio<span
                class="_ _1"
              ></span
              >n-cri<span class="_ _1"></span>tical role in<span
                class="_ _1"
              ></span>
              the develo<span class="_ _1"></span>pment lifec<span
                class="_ _1"
              ></span
              >ycle proces<span class="_ _1"></span>s. Hig<span
                class="_ _1"
              ></span
              >hly skilled<span class="_ _1"></span> in implem<span
                class="_ _1"
              ></span
              >enting core
            </div>
            <div class="t m0 x2 h4 y6 ff1 fs2 fc0 sc0 ls0 ws0">
              web dev<span class="_ _1"></span>elopment t<span
                class="_ _1"
              ></span
              >asks includ<span class="_ _1"></span>ing 3&apos;d party API
              in<span class="_ _1"></span>tegration, m<span class="_ _1"></span
              >icroservic<span class="_ _1"></span>es, cloud in<span
                class="_ _1"
              ></span
              >tegration, R<span class="_ _1"></span>EST API et<span
                class="_ _1"
              ></span
              >c. by deplo<span class="_ _1"></span>ying a wide
            </div>
            <div class="t m0 x2 h4 y7 ff1 fs2 fc0 sc0 ls0 ws0">
              range of<span class="_ _1"></span> technolog<span
                class="_ _1"
              ></span
              >ies a<span class="_ _1"></span>cross multi<span
                class="_ _1"
              ></span
              >ple framew<span class="_ _1"></span>orks, soft<span
                class="_ _1"
              ></span
              >ware<span class="_ _1"></span>, Operating<span
                class="_ _1"
              ></span>
              Systems &amp;<span class="_ _1"></span> programm<span
                class="_ _1"
              ></span
              >ing languag<span class="_ _1"></span>es.
            </div>
            <div class="t m0 x3 h4 y8 ff1 fs2 fc0 sc0 ls0 ws0">
              javian.pi<span class="_ _1"></span>cardo.jobs@<span
                class="_ _1"
              ></span
              >gmail.com<span class="_ _1"></span>
              <span class="_ _2"> </span>+16477715300
            </div>
            <div class="t m0 x3 h4 y9 ff1 fs2 fc0 sc0 ls0 ws0">
              Hamilton<span class="_ _1"></span>. Ontario, C<span
                class="_ _1"
              ></span
              >anada <span class="_ _3"> </span>linkedin.<span
                class="_ _1"
              ></span
              >com/in<span class="_ _0"></span>/javianp<span class="_ _1"></span
              >icardo
            </div>
            <div class="t m0 x2 h5 ya ff1 fs3 fc2 sc0 ls0 ws0">
              WO<span class="_ _0"></span>RK E<span class="_ _1"></span
              >XPERIE<span class="_ _1"></span>NCE
              <span class="_ _4"></span>WO<span class="_ _0"></span>RK E<span
                class="_ _1"
              ></span
              >XPERIE<span class="_ _1"></span>NCE
            </div>
            <div class="t m0 x2 h3 yb ff1 fs1 fc3 sc0 ls0 ws0">
              Lead Full St<span class="_ _1"></span>ack Eng<span
                class="_ _1"
              ></span
              >ine<span class="_ _1"></span>er <span class="_ _5"></span>Lead
              Full St<span class="_ _1"></span>ack Eng<span class="_ _1"></span
              >ine<span class="_ _1"></span>er
            </div>
            <div class="t m0 x2 h3 yc ff1 fs1 fc3 sc0 ls0 ws0">
              Ness USA L<span class="_ _1"></span>td. Clien<span
                class="_ _1"
              ></span
              >t: CIBC
            </div>
            <div class="t m0 x2 h6 yd ff2 fs4 fc2 sc0 ls0 ws0">
              12/2024 - Present<span class="ff1 fc3"
                ><span class="fc5 sc0">,</span><span class="fc5 sc0"> </span
              ></span>
              <span class="_ _6"> </span>T<span class="_ _0"></span>oronto,
              Ontar<span class="_ _1"></span>io
            </div>
            <div class="t m0 x2 h6 ye ff2 fs4 fc4 sc0 ls0 ws0">
              W<span class="_ _0"></span>orking on sha<span class="_ _1"></span
              >red data ser<span class="_ _1"></span>vices projec<span
                class="_ _1"
              ></span
              >t for interna<span class="_ _1"></span>l reporting s<span
                class="_ _1"
              ></span
              >ervices.
            </div>
            <div class="t m0 x4 h4 yf ff1 fs2 fc3 sc0 ls0 ws0">
              Impleme<span class="_ _1"></span>nted the p<span
                class="_ _1"
              ></span
              >ortal using <span class="_ _1"></span>React and d<span
                class="_ _1"
              ></span
              >eployed us<span class="_ _1"></span>ing A<span
                class="_ _1"
              ></span
              >zure App
            </div>
            <div class="t m0 x4 h4 y10 ff1 fs2 fc3 sc0 ls0 ws0">
              services.<span class="_ _1"></span>
            </div>
            <div class="t m0 x4 h4 y11 ff1 fs2 fc3 sc0 ls0 ws0">
              Develop<span class="_ _1"></span>ed A<span class="_ _1"></span
              >zure Func<span class="_ _1"></span>tions APIs w<span
                class="_ _1"
              ></span
              >ith<span class="_ _1"></span> C# &amp; .NE<span
                class="_ _1"
              ></span
              >T 8 used fo<span class="_ _1"></span>r UI and Da<span
                class="_ _1"
              ></span
              >ta
            </div>
            <div class="t m0 x4 h4 y12 ff1 fs2 fc3 sc0 ls0 ws0">
              services <span class="_ _1"></span>for Open B<span
                class="_ _1"
              ></span
              >anking inclu<span class="_ _1"></span>ding Auth<span
                class="_ _1"
              ></span
              >entication a<span class="_ _1"></span>nd Author<span
                class="_ _1"
              ></span
              >ization.
            </div>
            <div class="t m0 x2 h3 y13 ff1 fs1 fc3 sc0 ls0 ws0">
              Lead A<span class="_ _1"></span>pplicati<span class="_ _1"></span
              >on Deve<span class="_ _1"></span>loper
              <span class="_ _7"></span>Lead A<span class="_ _1"></span
              >pplicati<span class="_ _1"></span>on Deve<span
                class="_ _1"
              ></span
              >loper
            </div>
            <div class="t m0 x2 h3 y14 ff1 fs1 fc3 sc0 ls0 ws0">
              Portfolio<span class="_ _0"></span>XpressW<span
                class="_ _0"
              ></span
              >a<span class="_ _0"></span>y
            </div>
            <div class="t m0 x2 h6 y15 ff2 fs4 fc2 sc0 ls0 ws0">
              07<span class="_ _8"></span>/2022 - 1<span class="_ _1"></span
              >2<span class="_ _0"></span>/2024<span class="_ _1"></span
              ><span class="ff1 fc3"
                ><span class="fc5 sc0">,</span><span class="fc5 sc0"> </span
              ></span>
              <span class="_ _9"> </span>T<span class="_ _8"></span>or<span
                class="_ _1"
              ></span
              >onto, Ontar<span class="_ _1"></span>io
            </div>
            <div class="t m0 x2 h6 y16 ff2 fs4 fc4 sc0 ls0 ws0">
              PXW pro<span class="_ _1"></span>vides investm<span
                class="_ _1"
              ></span
              >ent professio<span class="_ _1"></span>nals with a
              <span class="_ _1"></span>fully digital 3<span class="_ _1"></span
              >60-degree vi<span class="_ _1"></span>ew of investo<span
                class="_ _1"
              ></span
              >r
            </div>
            <div class="t m0 x2 h6 y17 ff2 fs4 fc4 sc0 ls0 ws0">
              assets, no<span class="_ _1"></span> matter thei<span
                class="_ _1"
              ></span
              >r location or<span class="_ _1"></span> nature, as w<span
                class="_ _1"
              ></span
              >ell as the to<span class="_ _1"></span>ols to source<span
                class="_ _1"
              ></span>
              and recomm<span class="_ _1"></span>end
            </div>
            <div class="t m0 x2 h6 y18 ff2 fs4 fc4 sc0 ls0 ws0">
              the very b<span class="_ _1"></span>est investme<span
                class="_ _1"
              ></span
              >nt opportun<span class="_ _1"></span>ities.
            </div>
            <div class="t m0 x4 h4 y19 ff1 fs2 fc3 sc0 ls0 ws0">
              Created I<span class="_ _1"></span>n<span class="_ _0"></span
              >vestor <span class="_ _1"></span>asset Portfo<span
                class="_ _1"
              ></span
              >lio websi<span class="_ _1"></span>te fra<span
                class="_ _1"
              ></span
              >mework/conﬁguration/CICD
            </div>
            <div class="t m0 x4 h4 y1a ff1 fs2 fc3 sc0 ls0 ws0">
              process <span class="_ _1"></span>using Reac<span
                class="_ _1"
              ></span
              >t, Next.JS, S<span class="_ _1"></span>tor<span
                class="_ _1"
              ></span
              >ybook, Ant<span class="_ _1"></span> Design usi<span
                class="_ _1"
              ></span
              >ng A<span class="_ _1"></span>zure cloud
            </div>
            <div class="t m0 x4 h4 y1b ff1 fs2 fc3 sc0 ls0 ws0">
              platform<span class="_ _1"></span> using Doc<span
                class="_ _1"
              ></span
              >ker contain<span class="_ _1"></span>ers.
            </div>
            <div class="t m0 x4 h4 y1c ff1 fs2 fc3 sc0 ls0 ws0">
              Indepen<span class="_ _1"></span>dently deve<span
                class="_ _1"
              ></span
              >loped a co<span class="_ _1"></span>mprehensiv<span
                class="_ _1"
              ></span
              >e W<span class="_ _8"></span>e<span class="_ _1"></span>b API
              using<span class="_ _1"></span> C#, .NET
            </div>
            <div class="t m0 x4 h4 y1d ff1 fs2 fc3 sc0 ls0 ws0">
              Core/.NET 7 de<span class="_ _1"></span>ployed usin<span
                class="_ _1"
              ></span
              >g A<span class="_ _1"></span>zure App S<span class="_ _1"></span
              >ervices; ma<span class="_ _1"></span>naged by u<span
                class="_ _1"
              ></span
              >sing A<span class="_ _1"></span>zure
            </div>
            <div class="t m0 x4 h4 y1e ff1 fs2 fc3 sc0 ls0 ws0">
              Dev Ops<span class="_ _1"></span>.
            </div>
            <div class="t m0 x4 h4 y1f ff1 fs2 fc3 sc0 ls0 ws0">
              Created m<span class="_ _1"></span>icroservic<span
                class="_ _1"
              ></span
              >es using Dj<span class="_ _1"></span>ango &amp; P<span
                class="_ _1"
              ></span
              >ython<span class="_ _1"></span> 3 from Da<span
                class="_ _1"
              ></span
              >tabricks imp<span class="_ _1"></span>orted
            </div>
            <div class="t m0 x4 h4 y20 ff1 fs2 fc3 sc0 ls0 ws0">data.</div>
            <div class="t m0 x4 h4 y21 ff1 fs2 fc3 sc0 ls0 ws0">
              Impleme<span class="_ _1"></span>nted and o<span
                class="_ _1"
              ></span
              >ptimized A<span class="_ _1"></span>zur<span class="_ _1"></span
              >e cloud infr<span class="_ _1"></span>astructure
              <span class="_ _1"></span>by integrati<span class="_ _1"></span>ng
            </div>
            <div class="t m0 x4 h4 y22 ff1 fs2 fc3 sc0 ls0 ws0">
              A<span class="_ _1"></span>zure App S<span class="_ _1"></span
              >ervice, A<span class="_ _1"></span>zure F<span
                class="_ _1"
              ></span
              >unctions, A<span class="_ _1"></span>zur<span class="_ _1"></span
              >e SQL Data<span class="_ _1"></span>base, A<span
                class="_ _1"
              ></span
              >zure Sto<span class="_ _1"></span>rage,
            </div>
            <div class="t m0 x4 h4 y23 ff1 fs2 fc3 sc0 ls0 ws0">
              A<span class="_ _1"></span>zure DevO<span class="_ _1"></span>ps
              &amp; Pipelin<span class="_ _1"></span>es, A<span
                class="_ _1"
              ></span
              >zure Virtual Mach<span class="_ _1"></span>ines (<span
                class="_ _1"
              ></span
              >V<span class="_ _1"></span>Ms), Azure
              <span class="_ _1"></span>Active
            </div>
            <div class="t m0 x4 h4 y24 ff1 fs2 fc3 sc0 ls0 ws0">
              Director<span class="_ _1"></span>y (A<span class="_ _1"></span
              >zure<span class="_ _1"></span> AD), Azure<span
                class="_ _1"
              ></span>
              Log<span class="_ _1"></span>ic Apps, an<span class="_ _1"></span
              >d A<span class="_ _1"></span>zure B2C C<span class="_ _1"></span
              >ustom Poli<span class="_ _1"></span>cies,
            </div>
            <div class="t m0 x4 h4 y25 ff1 fs2 fc3 sc0 ls0 ws0">
              along wi<span class="_ _1"></span>th n<span class="_ _1"></span
              >etw<span class="_ _1"></span>orking, IAM<span class="_ _1"></span
              >, and AKS<span class="_ _1"></span> for seamle<span
                class="_ _1"
              ></span
              >ss project d<span class="_ _1"></span>eployment<span
                class="_ _1"
              ></span>
              and
            </div>
            <div class="t m0 x4 h4 y26 ff1 fs2 fc3 sc0 ls0 ws0">
              scalabili<span class="_ _1"></span>ty.
            </div>
            <div class="t m0 x2 h3 y27 ff1 fs1 fc3 sc0 ls0 ws0">
              Full Stack S<span class="_ _1"></span>enior .N<span
                class="_ _1"
              ></span
              >ET Deve<span class="_ _1"></span>loper
              <span class="_ _a"></span>Full Stack S<span class="_ _1"></span
              >enior .N<span class="_ _1"></span>ET Deve<span
                class="_ _1"
              ></span
              >loper
            </div>
            <div class="t m0 x2 h3 y28 ff1 fs1 fc3 sc0 ls0 ws0">
              Actex <span class="_ _1"></span>Learning<span class="_ _1"></span>
            </div>
            <div class="t m0 x2 h6 y29 ff2 fs4 fc2 sc0 ls0 ws0">
              04/2021 - 07<span class="_ _8"></span>/2022<span class="ff1 fc3"
                ><span class="fc5 sc0">,</span><span class="fc5 sc0"> </span
              ></span>
              <span class="_ _b"> </span>Portsmou<span class="_ _1"></span>th,
              NH.(USA).
            </div>
            <div class="t m0 x2 h6 y2a ff2 fs4 fc4 sc0 ls0 ws0">
              Actuaria<span class="_ _1"></span>l Learning he<span
                class="_ _1"
              ></span
              >lps actuaria<span class="_ _1"></span>l candidates<span
                class="_ _1"
              ></span>
              offering GOAL Actua<span class="_ _1"></span>rial Exam P<span
                class="_ _1"
              ></span
              >r<span class="_ _0"></span>actice
            </div>
            <div class="t m0 x2 h6 y2b ff2 fs4 fc4 sc0 ls0 ws0">
              tool/textb<span class="_ _1"></span>ooks, and th<span
                class="_ _1"
              ></span
              >e online lear<span class="_ _1"></span>ning platform<span
                class="_ _1"
              ></span>
              Actuarial <span class="_ _1"></span>University us<span
                class="_ _1"
              ></span
              >ed by many
            </div>
            <div class="t m0 x2 h6 y2c ff2 fs4 fc4 sc0 ls0 ws0">
              actuaries<span class="_ _1"></span> and student<span
                class="_ _1"
              ></span
              >s to help the<span class="_ _1"></span>m pass their<span
                class="_ _1"
              ></span>
              SOA and CAS Exam<span class="_ _1"></span>s.
            </div>
            <div class="t m0 x4 h4 y2d ff1 fs2 fc3 sc0 ls0 ws0">
              Used C#<span class="_ _1"></span>.NET<span class="_ _8"></span
              >,.NET6 t<span class="_ _1"></span>o create we<span
                class="_ _1"
              ></span
              >b applicati<span class="_ _1"></span>ons using A<span
                class="_ _1"
              ></span
              >SP<span class="_ _8"></span>.NET MVC Core,
            </div>
            <div class="t m0 x4 h4 y2e ff1 fs2 fc3 sc0 ls0 ws0">
              ASP<span class="_ _8"></span>.NET M<span class="_ _1"></span>VC 5,
              HTML, CSS,<span class="_ _1"></span> jQuer<span
                class="_ _1"
              ></span
              >y<span class="_ _8"></span>, B<span class="_ _1"></span>ootstrap,
              A<span class="_ _1"></span>zur<span class="_ _1"></span>e, SQL
              Serv<span class="_ _1"></span>er<span class="_ _8"></span>.
            </div>
            <div class="t m0 x4 h4 y2f ff1 fs2 fc3 sc0 ls0 ws0">
              Used R la<span class="_ _1"></span>nguage to
              <span class="_ _1"></span>create API i<span class="_ _1"></span
              >nterface us<span class="_ _1"></span>ing A<span
                class="_ _1"
              ></span
              >zure func<span class="_ _1"></span>tions.
            </div>
            <div class="t m0 x4 h4 y30 ff1 fs2 fc3 sc0 ls0 ws0">
              Impleme<span class="_ _1"></span>nted A<span class="_ _1"></span
              >zure fun<span class="_ _1"></span>ctions usi<span
                class="_ _1"
              ></span
              >ng custom <span class="_ _1"></span>handler in R<span
                class="_ _1"
              ></span>
              language <span class="_ _1"></span>&amp; R-
            </div>
            <div class="t m0 x4 h4 y31 ff1 fs2 fc3 sc0 ls0 ws0">Studio.</div>
            <div class="t m0 x4 h4 y32 ff1 fs2 fc3 sc0 ls0 ws0">
              Used A<span class="_ _1"></span>zure <span class="_ _1"></span>App
              service<span class="_ _1"></span>s for deplo<span
                class="_ _1"
              ></span
              >ying applic<span class="_ _1"></span>ations in clo<span
                class="_ _1"
              ></span
              >ud.
            </div>
            <div class="t m0 x5 h5 ya ff1 fs3 fc2 sc0 ls0 ws0">
              CERT<span class="_ _1"></span>IFIC<span class="_ _0"></span>ATES
              <span class="_ _c"></span>CERT<span class="_ _1"></span>IFIC<span
                class="_ _0"
              ></span
              >ATES
            </div>
            <div class="t m0 x5 h7 y33 ff1 fs5 fc3 sc0 ls0 ws0">
              Certiﬁe<span class="_ _1"></span>d Ethical H<span
                class="_ _1"
              ></span
              >acker(CEH)-<span class="_ _1"></span>v6
            </div>
            <div class="t m0 x5 h7 y34 ff1 fs5 fc3 sc0 ls0 ws0">
              (06<span class="_ _8"></span>/2010<span class="_ _1"></span> -
              11/2010)
            </div>
            <div class="t m0 x5 h6 y35 ff2 fs4 fc4 sc0 ls0 ws0">
              • EC Coun<span class="_ _1"></span>cil (www.ecouncil.org<span
                class="_ _0"
              ></span
              >). Member Id:
            </div>
            <div class="t m0 x5 h6 y36 ff2 fs4 fc4 sc0 ls0 ws0">
              ECC94<span class="_ _1"></span>4<span class="_ _1"></span>432
            </div>
            <div class="t m0 x5 h7 y37 ff1 fs5 fc3 sc0 ls0 ws0">
              Core Java (<span class="_ _0"></span>06<span class="_ _8"></span
              >/<span class="_ _1"></span>2008 - 10/2008)
            </div>
            <div class="t m0 x5 h6 y38 ff2 fs4 fc4 sc0 ls0 ws0">
              Rajesh Patkar Institute<span class="_ _1"></span> of
              Technology<span class="_ _1"></span>
            </div>
            <div class="t m0 x5 h5 y39 ff1 fs3 fc2 sc0 ls0 ws0">
              EDUCATIO<span class="_ _0"></span>N
              <span class="_ _d"></span>EDUCATIO<span class="_ _0"></span>N
            </div>
            <div class="t m0 x5 h3 y3a ff1 fs1 fc3 sc0 ls0 ws0">
              Bache<span class="_ _1"></span>lor’s in Comp<span
                class="_ _1"
              ></span
              >uter<span class="_ _e"></span>Bache<span class="_ _1"></span
              >lor’s in Comp<span class="_ _1"></span>uter
            </div>
            <div class="t m0 x5 h3 y3b ff1 fs1 fc3 sc0 ls0 ws0">
              Eng<span class="_ _1"></span>ineer<span class="_ _1"></span>ing
              <span class="_ _f"></span>Eng<span class="_ _1"></span>ineer<span
                class="_ _1"
              ></span
              >ing
            </div>
            <div class="t m0 x5 h3 y3c ff1 fs1 fc3 sc0 ls0 ws0">
              Univer<span class="_ _1"></span>sit<span class="_ _1"></span>y of
              Mu<span class="_ _1"></span>mbai
            </div>
            <div class="t m0 x5 h6 y3d ff2 fs4 fc2 sc0 ls0 ws0">
              2004 - 20<span class="_ _1"></span>08<span class="ff1 fc3"
                ><span class="fc5 sc0">,</span><span class="fc5 sc0"> </span
              ></span>
            </div>
            <div class="t m0 x5 h6 y3e ff2 fs4 fc2 sc0 ls0 ws0">
              Mumbai, <span class="_ _1"></span>India
            </div>
            <div class="t m0 x5 h5 y3f ff1 fs3 fc2 sc0 ls0 ws0">
              L<span class="_ _1"></span>ANGUAGES<span class="_ _1"></span>
              <span class="_ _10"></span>L<span class="_ _1"></span
              >ANGUAGES<span class="_ _1"></span>
            </div>
            <div class="t m0 x5 h4 y40 ff1 fs2 fc3 sc0 ls0 ws0">
              Eng<span class="_ _1"></span>lish
            </div>
            <div class="t m0 x5 h6 y41 ff2 fs4 fc2 sc0 ls0 ws0">
              Native or Bilingua<span class="_ _1"></span>l Proﬁciency<span
                class="_ _1"
              ></span>
            </div>
            <div class="t m0 x5 h4 y42 ff1 fs2 fc3 sc0 ls0 ws0">Hindi</div>
            <div class="t m0 x5 h6 y43 ff2 fs4 fc2 sc0 ls0 ws0">
              Native or Bilingua<span class="_ _1"></span>l Proﬁciency<span
                class="_ _1"
              ></span>
            </div>
            <div class="t m0 x5 h5 y44 ff1 fs3 fc2 sc0 ls0 ws0">
              SKILL<span class="_ _1"></span>S
              <span class="_ _11"></span>SKILL<span class="_ _1"></span>S
            </div>
            <div class="t m0 x6 h4 y45 ff1 fs2 fc0 sc0 ls0 ws0">
              .NET 8 <span class="_ _12"> </span>C#
              <span class="_ _12"> </span>REACT <span class="_ _12"> </span>MS
              SQL
            </div>
            <div class="t m0 x6 h4 y46 ff1 fs2 fc0 sc0 ls0 ws0">
              WEB.<span class="_ _1"></span>API
              <span class="_ _12"> </span>NEXT<span class="_ _8"></span>.<span
                class="_ _1"
              ></span
              >JS
            </div>
            <div class="t m0 x6 h4 y47 ff1 fs2 fc0 sc0 ls0 ws0">
              ASP<span class="_ _8"></span>.NET M<span class="_ _1"></span>VC
              <span class="_ _13"> </span>AZURE
            </div>
            <div class="t m0 x6 h4 y48 ff1 fs2 fc0 sc0 ls0 ws0">
              T<span class="_ _1"></span>YPESCRIP<span class="_ _1"></span>T
              <span class="_ _13"> </span>R <span class="_ _13"> </span>JQUERY
            </div>
            <div class="t m0 x6 h4 y49 ff1 fs2 fc0 sc0 ls0 ws0">
              BOOTST<span class="_ _1"></span>R<span class="_ _1"></span>AP
              <span class="_ _12"> </span>ANGUL<span class="_ _1"></span>AR
              <span class="_ _12"> </span>CSS
            </div>
            <div class="t m0 x6 h4 y4a ff1 fs2 fc0 sc0 ls0 ws0">
              HTML <span class="_ _12"> </span>GIT
              <span class="_ _13"> </span>N<span class="_ _1"></span>ODEJS
            </div>
            <div class="t m0 x6 h4 y4b ff1 fs2 fc0 sc0 ls0 ws0">
              PY<span class="_ _1"></span>THON 3
              <span class="_ _13"> </span>AZ<span class="_ _1"></span>URE APP
              S<span class="_ _1"></span>ERVICE
            </div>
            <div class="t m0 x6 h4 y4c ff1 fs2 fc0 sc0 ls0 ws0">
              AZURE S<span class="_ _1"></span>QL DA<span class="_ _0"></span
              >TAB<span class="_ _0"></span>ASE
            </div>
            <div class="t m0 x6 h4 y4d ff1 fs2 fc0 sc0 ls0 ws0">
              AZURE B<span class="_ _1"></span>L<span class="_ _0"></span>OB
              STO<span class="_ _1"></span>R<span class="_ _1"></span>AGE
            </div>
            <div class="t m0 x6 h4 y4e ff1 fs2 fc0 sc0 ls0 ws0">
              AZURE D<span class="_ _1"></span>EVOPS &amp; PIPELIN<span
                class="_ _1"
              ></span
              >ES
            </div>
            <div class="t m0 x6 h4 y4f ff1 fs2 fc0 sc0 ls0 ws0">
              POWER BI <span class="_ _13"> </span>ARM TEM<span
                class="_ _1"
              ></span
              >PL<span class="_ _1"></span>A<span class="_ _0"></span>TES
            </div>
            <div class="t m0 x6 h4 y50 ff1 fs2 fc0 sc0 ls0 ws0">
              TERR<span class="_ _14"></span>AFORM
              <span class="_ _13"> </span>M<span class="_ _1"></span
              >ULESOFT<span class="_ _1"></span>
            </div>
            <div class="t m0 x6 h4 y51 ff1 fs2 fc0 sc0 ls0 ws0">
              AZURE A<span class="_ _1"></span>CTIVE DIR<span
                class="_ _1"
              ></span
              >ECTORY
            </div>
            <div class="t m0 x6 h4 y52 ff1 fs2 fc0 sc0 ls0 ws0">
              AZURE A<span class="_ _1"></span>D B2C
              <span class="_ _13"> </span>AZURE KEY VA<span class="_ _8"></span
              >U<span class="_ _1"></span>L<span class="_ _8"></span>T
            </div>
            <div class="t m0 x2 h6 y53 ff2 fs4 fc2 sc0 ls0 ws0">
              Achievem<span class="_ _1"></span>ents/T<span class="_ _8"></span
              >asks
            </div>
            <div class="t m0 x2 h6 y54 ff2 fs4 fc2 sc0 ls0 ws0">
              Achievem<span class="_ _1"></span>ents/T<span class="_ _8"></span
              >asks
            </div>
            <div class="t m0 x2 h6 y55 ff2 fs4 fc2 sc0 ls0 ws0">
              Achievem<span class="_ _1"></span>ents/T<span class="_ _8"></span
              >asks
            </div>
          </div>
          <a class="l" href="mailto:<EMAIL>"
            ><div
              class="d m1"
              style="
                border-style: none;
                position: absolute;
                left: 37.028571px;
                bottom: 1038.257142px;
                width: 146.4px;
                height: 10.628571px;
                background-color: rgba(255, 255, 255, 0.000001);
              "
            ></div></a
          ><a class="l" href="https://www.linkedin.com/in/javianpicardo/"
            ><div
              class="d m1"
              style="
                border-style: none;
                position: absolute;
                left: 445.885713px;
                bottom: 1008.428571px;
                width: 165.257143px;
                height: 10.628571px;
                background-color: rgba(255, 255, 255, 0.000001);
              "
            ></div
          ></a>
        </div>
        <div
          class="pi"
          data-data='{"ctm":[1.500000,0.000000,0.000000,1.500000,0.000000,0.000000]}'
        ></div>
      </div>
      <div id="pf2" class="pf w0 h0" data-page-no="2">
        <div class="pc pc2 w0 h0">
          <img
            class="bi x0 y0 w0 h0"
            alt=""
            src="data:image/png;base64,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"
          />
          <div class="c x0 y1 w1 h1">
            <div class="t m0 x2 h5 y56 ff1 fs3 fc2 sc0 ls0 ws0">
              WO<span class="_ _0"></span>RK E<span class="_ _1"></span
              >XPERIE<span class="_ _1"></span>NCE
              <span class="_ _4"></span>WO<span class="_ _0"></span>RK E<span
                class="_ _1"
              ></span
              >XPERIE<span class="_ _1"></span>NCE
            </div>
            <div class="t m0 x2 h3 y57 ff1 fs1 fc3 sc0 ls0 ws0">
              Princip<span class="_ _1"></span>al Sof<span class="_ _1"></span
              >t<span class="_ _1"></span>ware Eng<span class="_ _1"></span
              >inee<span class="_ _1"></span>r
              <span class="_ _15"></span>Princip<span class="_ _1"></span>al
              Sof<span class="_ _1"></span>t<span class="_ _1"></span>ware
              Eng<span class="_ _1"></span>inee<span class="_ _1"></span>r
            </div>
            <div class="t m0 x2 h3 y58 ff1 fs1 fc3 sc0 ls0 ws0">
              Xchan<span class="_ _1"></span>gin<span class="_ _1"></span>g
              Soluti<span class="_ _1"></span>ons [<span class="_ _0"></span>A
              DXC T<span class="_ _8"></span>echnolog<span class="_ _1"></span>y
              Co<span class="_ _1"></span>mpany].
            </div>
            <div class="t m0 x2 h3 y59 ff1 fs1 fc3 sc0 ls0 ws0">
              Client:<span class="_ _1"></span> Li<span class="_ _1"></span
              >berty Mu<span class="_ _1"></span>tual.
            </div>
            <div class="t m0 x2 h6 y5a ff2 fs4 fc2 sc0 ls0 ws0">
              10<span class="_ _8"></span>/<span class="_ _1"></span>2015 -
              04/2021<span class="ff1 fc3"
                ><span class="fc5 sc0">,</span><span class="fc5 sc0"> </span
              ></span>
              <span class="_ _16"> </span>Dover, NH<span class="_ _1"></span>.
              USA
            </div>
            <div class="t m0 x4 h4 y5b ff1 fs2 fc3 sc0 ls0 ws0">
              W<span class="_ _8"></span>o<span class="_ _1"></span>rked on
              w<span class="_ _1"></span>eb applicati<span class="_ _1"></span
              >ons that ut<span class="_ _1"></span>ilized C#, A<span
                class="_ _1"
              ></span
              >SP<span class="_ _8"></span>.NET MVC, W<span class="_ _0"></span
              >eb API,
            </div>
            <div class="t m0 x4 h4 y5c ff1 fs2 fc3 sc0 ls0 ws0">
              HTML,CSS, J-Qu<span class="_ _1"></span>ery, Bootstrap, A<span
                class="_ _1"
              ></span
              >zure<span class="_ _1"></span> &amp; SQL Serv<span
                class="_ _1"
              ></span
              >er<span class="_ _8"></span>.
            </div>
            <div class="t m0 x4 h4 y5d ff1 fs2 fc3 sc0 ls0 ws0">
              Created m<span class="_ _1"></span>icroservic<span
                class="_ _1"
              ></span
              >es in MuleS<span class="_ _1"></span>oft over An<span
                class="_ _1"
              ></span
              >y Point Stu<span class="_ _1"></span>dio. Create<span
                class="_ _1"
              ></span
              >d W<span class="_ _8"></span>e<span class="_ _1"></span>b API
            </div>
            <div class="t m0 x4 h4 y5e ff1 fs2 fc3 sc0 ls0 ws0">
              applicati<span class="_ _1"></span>ons in Spri<span
                class="_ _1"
              ></span
              >ng boot &amp; <span class="_ _1"></span>J<span
                class="_ _0"
              ></span
              >ava, A<span class="_ _0"></span>WS.
            </div>
            <div class="t m0 x2 h3 y5f ff1 fs1 fc3 sc0 ls0 ws0">
              T<span class="_ _8"></span>ech Le<span class="_ _1"></span
              >ad/Senior So<span class="_ _1"></span>ft<span class="_ _1"></span
              >ware .NE<span class="_ _1"></span>T Eng<span class="_ _1"></span
              >inee<span class="_ _1"></span>r <span class="_ _17"></span>T<span
                class="_ _8"
              ></span
              >ech Le<span class="_ _1"></span>ad/Senior So<span
                class="_ _1"
              ></span
              >ft<span class="_ _1"></span>ware .NE<span class="_ _1"></span>T
              Eng<span class="_ _1"></span>inee<span class="_ _1"></span>r
            </div>
            <div class="t m0 x2 h3 y60 ff1 fs1 fc3 sc0 ls0 ws0">
              (SYNTEL In<span class="_ _1"></span>c.) Client<span
                class="_ _1"
              ></span
              >: Humana
            </div>
            <div class="t m0 x2 h6 y61 ff2 fs4 fc2 sc0 ls0 ws0">
              05/2011 - 08/2015<span class="ff1 fc3"
                ><span class="fc5 sc0">,</span><span class="fc5 sc0"> </span
              ></span>
              <span class="_ _18"> </span>Louisville,
              <span class="_ _1"></span>KY<span class="_ _8"></span>. US<span
                class="_ _1"
              ></span
              >A
            </div>
            <div class="t m0 x4 h4 y62 ff1 fs2 fc3 sc0 ls0 ws0">
              Built N-l<span class="_ _1"></span>ayer archi<span
                class="_ _1"
              ></span
              >tectur<span class="_ _1"></span>e for prese<span
                class="_ _1"
              ></span
              >ntation laye<span class="_ _1"></span>r<span class="_ _8"></span
              >, the<span class="_ _1"></span> Business a<span
                class="_ _1"
              ></span
              >nd Data
            </div>
            <div class="t m0 x4 h4 y63 ff1 fs2 fc3 sc0 ls0 ws0">
              Access C<span class="_ _1"></span>#.NET<span class="_ _8"></span>,
              ASP<span class="_ _8"></span>.NE<span class="_ _1"></span>T<span
                class="_ _8"
              ></span
              >, ASP<span class="_ _8"></span>.NET M<span class="_ _1"></span
              >V<span class="_ _0"></span>C, HTM<span class="_ _1"></span>L,
              CSS, JQu<span class="_ _1"></span>ery, Bootstrap.
            </div>
            <div class="t m0 x4 h4 y64 ff1 fs2 fc3 sc0 ls0 ws0">
              Created m<span class="_ _1"></span>edicare en<span
                class="_ _1"
              ></span
              >rollment p<span class="_ _1"></span>ortal using
              <span class="_ _1"></span>Angular &amp;
              <span class="_ _1"></span>Sql Server.
            </div>
            <div class="t m0 x4 h4 y65 ff1 fs2 fc3 sc0 ls0 ws0">
              Develop<span class="_ _1"></span>ed and pub<span
                class="_ _1"
              ></span
              >lished vario<span class="_ _1"></span>us SSRS (S<span
                class="_ _1"
              ></span
              >QL Server <span class="_ _1"></span>Reporting S<span
                class="_ _1"
              ></span
              >ervices)
            </div>
            <div class="t m0 x4 h4 y66 ff1 fs2 fc3 sc0 ls0 ws0">
              reports /<span class="_ _1"></span> SSIS (SQ<span
                class="_ _1"
              ></span
              >L Server Int<span class="_ _1"></span>egration Se<span
                class="_ _1"
              ></span
              >rvices) packages.
            </div>
            <div class="t m0 x2 h3 y67 ff1 fs1 fc3 sc0 ls0 ws0">
              Senior<span class="_ _1"></span> Sof<span class="_ _1"></span
              >t<span class="_ _1"></span>ware Eng<span class="_ _1"></span
              >ineer <span class="_ _19"></span>Senior<span class="_ _1"></span>
              Sof<span class="_ _1"></span>t<span class="_ _1"></span>ware
              Eng<span class="_ _1"></span>ineer
            </div>
            <div class="t m0 x2 h3 y68 ff1 fs1 fc3 sc0 ls0 ws0">
              Acty S<span class="_ _1"></span>ystem In<span class="_ _1"></span
              >dia P<span class="_ _14"></span>v<span class="_ _1"></span>t Ltd
            </div>
            <div class="t m0 x2 h6 y69 ff2 fs4 fc2 sc0 ls0 ws0">
              08/2009 - 04/2011<span class="ff1 fc3"
                ><span class="fc5 sc0">,</span><span class="fc5 sc0"> </span
              ></span>
              <span class="_ _1a"> </span>Mumbai,
              <span class="_ _1"></span>India
            </div>
            <div class="t m0 x2 h6 y6a ff2 fs4 fc4 sc0 ls0 ws0">
              Software<span class="_ _1"></span> service orga<span
                class="_ _1"
              ></span
              >nization foc<span class="_ _1"></span>used on prov<span
                class="_ _1"
              ></span
              >iding softwa<span class="_ _1"></span>re services to<span
                class="_ _1"
              ></span>
              J<span class="_ _0"></span>apanese<span class="_ _1"></span>
              client
            </div>
            <div class="t m0 x2 h6 y6b ff2 fs4 fc4 sc0 ls0 ws0">
              and local <span class="_ _1"></span>Indian orga<span
                class="_ _1"
              ></span
              >nizations.
            </div>
            <div class="t m0 x4 h4 y6c ff1 fs2 fc3 sc0 ls0 ws0">
              Created Window<span class="_ _1"></span>s forms ap<span
                class="_ _1"
              ></span
              >plications <span class="_ _1"></span>using C#, A<span
                class="_ _1"
              ></span
              >SP<span class="_ _8"></span>.NET &amp; S<span class="_ _1"></span
              >QL code,
            </div>
            <div class="t m0 x4 h4 y6d ff1 fs2 fc3 sc0 ls0 ws0">
              procedu<span class="_ _1"></span>res, functio<span
                class="_ _1"
              ></span
              >ns and ma<span class="_ _1"></span>intaining o<span
                class="_ _1"
              ></span
              >ptimal datab<span class="_ _1"></span>ase operat<span
                class="_ _1"
              ></span
              >ions.
            </div>
            <div class="t m0 x4 h4 y6e ff1 fs2 fc3 sc0 ls0 ws0">
              Design a<span class="_ _1"></span>nd develop<span
                class="_ _1"
              ></span
              >ment of UI<span class="_ _1"></span>, Business
              <span class="_ _1"></span>(SOA) and D<span class="_ _1"></span>ata
              layer u<span class="_ _1"></span>sing LINQ,
            </div>
            <div class="t m0 x4 h4 y6f ff1 fs2 fc3 sc0 ls0 ws0">
              Enti<span class="_ _1"></span>ty Frame<span class="_ _1"></span
              >work &amp; co<span class="_ _1"></span>de manage<span
                class="_ _1"
              ></span
              >ment activi<span class="_ _1"></span>ties
              <span class="_ _1"></span>using TFS
            </div>
            <div class="t m0 x2 h3 y70 ff1 fs1 fc3 sc0 ls0 ws0">
              Sof<span class="_ _1"></span>t<span class="_ _1"></span>ware
              Eng<span class="_ _1"></span>ineer
              <span class="_ _1b"></span>Sof<span class="_ _1"></span>t<span
                class="_ _1"
              ></span
              >ware Eng<span class="_ _1"></span>ineer
            </div>
            <div class="t m0 x2 h3 y71 ff1 fs1 fc3 sc0 ls0 ws0">
              ISF M<span class="_ _1"></span>ARITIME<span class="_ _1"></span>
              PV<span class="_ _1"></span>T L<span class="_ _8"></span>TD
            </div>
            <div class="t m0 x2 h6 y72 ff2 fs4 fc2 sc0 ls0 ws0">
              12/2008 - 08/2009<span class="ff1 fc3"
                ><span class="fc5 sc0">,</span><span class="fc5 sc0"> </span
              ></span>
              <span class="_ _16"> </span>Mumbai, In<span class="_ _1"></span
              >dia
            </div>
            <div class="t m0 x2 h6 y73 ff2 fs4 fc4 sc0 ls0 ws0">
              Created t<span class="_ _1"></span>r<span class="_ _0"></span
              >aining m<span class="_ _1"></span>odules, assess<span
                class="_ _1"
              ></span
              >ment modul<span class="_ _1"></span>es, chat room<span
                class="_ _1"
              ></span>
              made ava<span class="_ _1"></span>ilable by part<span
                class="_ _1"
              ></span
              >icular
            </div>
            <div class="t m0 x2 h6 y74 ff2 fs4 fc4 sc0 ls0 ws0">
              client to t<span class="_ _1"></span>heir speciﬁed<span
                class="_ _1"
              ></span>
              users to cle<span class="_ _1"></span>ar ORB (Oil Record B<span
                class="_ _1"
              ></span
              >ook) &amp; Cadet Selectio<span class="_ _1"></span>n Test
            </div>
            <div class="t m0 x2 h6 y75 ff2 fs4 fc4 sc0 ls0 ws0">
              examinat<span class="_ _1"></span>ion. (DLP) Distance<span
                class="_ _1"
              ></span>
              Learning Pr<span class="_ _1"></span>ogram support cont<span
                class="_ _1"
              ></span
              >aining e-lea<span class="_ _1"></span>rning
            </div>
            <div class="t m0 x2 h6 y76 ff2 fs4 fc4 sc0 ls0 ws0">
              contents <span class="_ _1"></span>created by (I<span
                class="_ _1"
              ></span
              >SF) compan<span class="_ _1"></span>y to the DLP users.
            </div>
            <div class="t m0 x4 h4 y77 ff1 fs2 fc3 sc0 ls0 ws0">
              Design, d<span class="_ _1"></span>evelopmen<span
                class="_ _1"
              ></span
              >t<span class="_ _0"></span>/<span class="_ _0"></span>coding
              and<span class="_ _1"></span> testing in
              <span class="_ _1"></span>C#, ASP<span class="_ _8"></span>.NET.
            </div>
            <div class="t m0 x4 h4 y78 ff1 fs2 fc3 sc0 ls0 ws0">
              Develop<span class="_ _1"></span>ed stored p<span
                class="_ _1"
              ></span
              >rocedures <span class="_ _1"></span>and databa<span
                class="_ _1"
              ></span
              >se tables in <span class="_ _1"></span>SQL Server<span
                class="_ _1"
              ></span>
            </div>
            <div class="t m0 x5 h5 y56 ff1 fs3 fc2 sc0 ls0 ws0">
              SKILL<span class="_ _1"></span>S
              <span class="_ _11"></span>SKILL<span class="_ _1"></span>S
            </div>
            <div class="t m0 x6 h4 y79 ff1 fs2 fc0 sc0 ls0 ws0">
              AZURE M<span class="_ _1"></span>ONITOR
            </div>
            <div class="t m0 x6 h4 y7a ff1 fs2 fc0 sc0 ls0 ws0">
              AZURE A<span class="_ _1"></span>PPLICATION INSI<span
                class="_ _1"
              ></span
              >GHTS
            </div>
            <div class="t m0 x6 h4 y7b ff1 fs2 fc0 sc0 ls0 ws0">
              AZURE A<span class="_ _1"></span>PI MANAG<span class="_ _1"></span
              >EMENT
            </div>
            <div class="t m0 x6 h4 y7c ff1 fs2 fc0 sc0 ls0 ws0">
              AZURE F<span class="_ _1"></span>UNCTION<span class="_ _1"></span
              >S
            </div>
            <div class="t m0 x6 h4 y7d ff1 fs2 fc0 sc0 ls0 ws0">
              AZURE K<span class="_ _1"></span>UBERNET<span class="_ _1"></span
              >ES SERVIC<span class="_ _1"></span>E(AKS)
            </div>
            <div class="t m0 x6 h4 y7e ff1 fs2 fc0 sc0 ls0 ws0">
              AZURE V<span class="_ _1"></span>M
              <span class="_ _13"> </span>SSIS <span class="_ _13"> </span>WCF
            </div>
            <div class="t m0 x5 h5 y7f ff1 fs3 fc2 sc0 ls0 ws0">
              INTE<span class="_ _1"></span>RESTS<span class="_ _1"></span>
              <span class="_ _1c"></span>INTE<span class="_ _1"></span
              >RESTS<span class="_ _1"></span>
            </div>
            <div class="t m0 x7 h4 y80 ff1 fs2 fc3 sc0 ls0 ws0">
              Pool <span class="_ _1d"> </span>Snooker<span class="_ _1"></span>
              <span class="_ _1e"> </span>Photogr<span class="_ _1"></span>aphy
            </div>
            <div class="t m0 x2 h6 y81 ff2 fs4 fc2 sc0 ls0 ws0">
              Achievem<span class="_ _1"></span>ents/T<span class="_ _8"></span
              >asks
            </div>
            <div class="t m0 x2 h6 y82 ff2 fs4 fc2 sc0 ls0 ws0">
              Achievem<span class="_ _1"></span>ents/T<span class="_ _8"></span
              >asks
            </div>
            <div class="t m0 x2 h6 y83 ff2 fs4 fc2 sc0 ls0 ws0">
              Achievem<span class="_ _1"></span>ents/T<span class="_ _8"></span
              >asks
            </div>
            <div class="t m0 x2 h6 y84 ff2 fs4 fc2 sc0 ls0 ws0">
              Achievem<span class="_ _1"></span>ents/T<span class="_ _8"></span
              >asks
            </div>
          </div>
        </div>
        <div
          class="pi"
          data-data='{"ctm":[1.500000,0.000000,0.000000,1.500000,0.000000,0.000000]}'
        ></div>
      </div>
    </div>
    <div class="loading-indicator">
      <img
        alt=""
        src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAMAAACdt4HsAAAABGdBTUEAALGPC/xhBQAAAwBQTFRFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAwAACAEBDAIDFgQFHwUIKggLMggPOgsQ/w1x/Q5v/w5w9w9ryhBT+xBsWhAbuhFKUhEXUhEXrhJEuxJKwBJN1xJY8hJn/xJsyhNRoxM+shNF8BNkZxMfXBMZ2xRZlxQ34BRb8BRk3hVarBVA7RZh8RZi4RZa/xZqkRcw9Rdjihgsqxg99BhibBkc5hla9xli9BlgaRoapho55xpZ/hpm8xpfchsd+Rtibxsc9htgexwichwdehwh/hxk9Rxedx0fhh4igB4idx4eeR4fhR8kfR8g/h9h9R9bdSAb9iBb7yFX/yJfpCMwgyQf8iVW/iVd+iVZ9iVWoCYsmycjhice/ihb/Sla+ylX/SpYmisl/StYjisfkiwg/ixX7CxN9yxS/S1W/i1W6y1M9y1Q7S5M6S5K+i5S6C9I/i9U+jBQ7jFK/jFStTIo+DJO9zNM7TRH+DRM/jRQ8jVJ/jZO8DhF9DhH9jlH+TlI/jpL8jpE8zpF8jtD9DxE7zw9/z1I9j1A9D5C+D5D4D8ywD8nwD8n90A/8kA8/0BGxEApv0El7kM5+ENA+UNAykMp7kQ1+0RB+EQ+7EQ2/0VCxUUl6kU0zkUp9UY8/kZByUkj1Eoo6Usw9Uw3300p500t3U8p91Ez11Ij4VIo81Mv+FMz+VM0/FM19FQw/lQ19VYv/lU1/1cz7Fgo/1gy8Fkp9lor4loi/1sw8l0o9l4o/l4t6l8i8mAl+WEn8mEk52Id9WMk9GMk/mMp+GUj72Qg8mQh92Uj/mUn+GYi7WYd+GYj6mYc62cb92ch8Gce7mcd6Wcb6mcb+mgi/mgl/Gsg+2sg+Wog/moj/msi/mwh/m0g/m8f/nEd/3Ic/3Mb/3Qb/3Ua/3Ya/3YZ/3cZ/3cY/3gY/0VC/0NE/0JE/w5wl4XsJQAAAPx0Uk5TAAAAAAAAAAAAAAAAAAAAAAABCQsNDxMWGRwhJioyOkBLT1VTUP77/vK99zRpPkVmsbbB7f5nYabkJy5kX8HeXaG/11H+W89Xn8JqTMuQcplC/op1x2GZhV2I/IV+HFRXgVSN+4N7n0T5m5RC+KN/mBaX9/qp+pv7mZr83EX8/N9+5Nip1fyt5f0RQ3rQr/zo/cq3sXr9xrzB6hf+De13DLi8RBT+wLM+7fTIDfh5Hf6yJMx0/bDPOXI1K85xrs5q8fT47f3q/v7L/uhkrP3lYf2ryZ9eit2o/aOUmKf92ILHfXNfYmZ3a9L9ycvG/f38+vr5+vz8/Pv7+ff36M+a+AAAAAFiS0dEQP7ZXNgAAAj0SURBVFjDnZf/W1J5Fsf9D3guiYYwKqglg1hqplKjpdSojYizbD05iz5kTlqjqYwW2tPkt83M1DIm5UuomZmkW3bVrmupiCY1mCNKrpvYM7VlTyjlZuM2Y+7nXsBK0XX28xM8957X53zO55z3OdcGt/zi7Azbhftfy2b5R+IwFms7z/RbGvI15w8DdkVHsVi+EGa/ZZ1bYMDqAIe+TRabNv02OiqK5b8Z/em7zs3NbQO0GoD0+0wB94Ac/DqQEI0SdobIOV98Pg8AfmtWAxBnZWYK0vYfkh7ixsVhhMDdgZs2zc/Pu9HsVwc4DgiCNG5WQoJ/sLeXF8070IeFEdzpJh+l0pUB+YBwRJDttS3cheJKp9MZDMZmD5r7+vl1HiAI0qDtgRG8lQAlBfnH0/Miqa47kvcnccEK2/1NCIdJ96Ctc/fwjfAGwXDbugKgsLggPy+csiOZmyb4LiEOjQMIhH/YFg4TINxMKxxaCmi8eLFaLJVeyi3N2eu8OTctMzM9O2fjtsjIbX5ewf4gIQK/5gR4uGP27i5LAdKyGons7IVzRaVV1Jjc/PzjP4TucHEirbUjEOyITvQNNH+A2MLj0NYDAM1x6RGk5e9raiQSkSzR+XRRcUFOoguJ8NE2kN2XfoEgsUN46DFoDlZi0DA3Bwiyg9TzpaUnE6kk/OL7xgdE+KBOgKSkrbUCuHJ1bu697KDrGZEoL5yMt5YyPN9glo9viu96GtEKQFEO/34tg1omEVVRidBy5bUdJXi7R4SIxWJzPi1cYwMMV1HO10gqnQnLFygPEDxSaPPuYPlEiD8B3IIrqDevvq9ytl1JPjhhrMBdIe7zaHG5oZn5sQf7YirgJqrV/aWHLPnPCQYis2U9RthjawHIFa0NnZcpZbCMTbRmnszN3mz5EwREJmX7JrQ6nU0eyFvbtX2dyi42/yqcQf40fnIsUsfSBIJIixhId7OCA7aA8nR3sTfF4EHn3d5elaoeONBEXXR/hWdzgZvHMrMjXWwtVczxZ3nwdm76fBvJfAvtajUgKPfxO1VHHRY5f6PkJBCBwrQcSor8WFIQFgl5RFQw/RuWjwveDGjr16jVvT3UBmXPYgdw0jPFOyCgEem5fw06BMqTu/+AGMeJjtrA8aGRFhJpqEejvlvl2qeqJC2J3+nSRHwhWlyZXvTkrLSEhAQuRxoW5RXA9aZ/yESUkMrv7IpffIWXbhSW5jkVlhQUpHuxHdbQt0b6ZcWF4vdHB9MjWNs5cgsAatd0szvu9rguSmFxWUVZSUmM9ERocbarPfoQ4nETNtofiIvzDIpCFUJqzgPFYI+rVt3k9MH2ys0bOFw1qG+R6DDelnmuYAcGF38vyHKxE++M28BBu47PbrE5kR62UB6qzSFQyBtvVZfDdVdwF2tO7jsrugCK93Rxoi1mf+QHtgNOyo3bxgsEis9i+a3BAA8GWlwHNRlYmTdqkQ64DobhHwNuzl0mVctKGKhS5jGBfW5mdjgJAs0nbiP9KyCVUSyaAwAoHvSPXGYMDgjRGCq0qgykE64/WAffrP5bPVl6ToJeZFFJDMCkp+/BUjUpwYvORdXWi2IL8uDR2NjIdaYJAOy7UpnlqlqHW3A5v66CgbsoQb3PLT2MB1mR+BkWiqTvACAuOnivEwFn82TixYuxsWYTQN6u7hI6Qg3KWvtLZ6/xy2E+rrqmCHhfiIZCznMyZVqSAAV4u4Dj4GwmpiYBoYXxeKSWgLvfpRaCl6qV4EbK4MMNcKVt9TVZjCWnIcjcgAV+9K+yXLCY2TwyTk1OvrjD0I4027f2DAgdwSaNPZ0xQGFq+SAQDXPvMe/zPBeyRFokiPwyLdRUODZtozpA6GeMj9xxbB24l4Eo5Di5VtUMdajqHYHOwbK5SrAVz/mDUoqzj+wJSfsiwJzKvJhh3aQxdmjsnqdicGCgu097X3G/t7tDq2wiN5bD1zIOL1aZY8fTXZMFAtPwguYBHvl5Soj0j8VDSEb9vQGN5hbS06tUqapIuBuHDzoTCItS/ER+DiUpU5C964Ootk3cZj58cdsOhycz4pvvXGf23W3q7I4HkoMnLOkR0qKCUDo6h2TtWgAoXvYz/jXZH4O1MQIzltiuro0N/8x6fygsLmYHoVOEIItnATyZNg636V8Mm3eDcK2avzMh6/bSM6V5lNwCjLAVMlfjozevB5mjk7qF0aNR1x27TGsoLC3dx88uwOYQIGsY4PmvM2+mnyO6qVGL9sq1GqF1By6dE+VRThQX54RG7qESTUdAfns7M/PGwHs29WrI8t6DO6lWW4z8vES0l1+St5dCsl9j6Uzjs7OzMzP/fnbKYNQjlhcZ1lt0dYWkinJG9JeFtLIAAEGPIHqjoW3F0fpKRU0e9aJI9Cfo4/beNmwwGPTv3hhSnk4bf16JcOXH3yvY/CIJ0LlP5gO8A5nsHDs8PZryy7TRgCxnLq+ug2V7PS+AWeiCvZUx75RhZjzl+bRxYkhuPf4NmH3Z3PsaSQXfCkBhePuf8ZSneuOrfyBLEYrqchXcxPYEkwwg1Cyc4RPA7Oyvo6cQw2ujbhRRLDLXdimVVVQgUjBGqFy7FND2G7iMtwaE90xvnHr18BekUSHHhoe21vY+Za+yZZ9zR13d5crKs7JrslTiUsATFDD79t2zU8xhvRHIlP7xI61W+3CwX6NRd7WkUmK0SuVBMpHo5PnncCcrR3g+a1rTL5+mMJ/f1r1C1XZkZASITEttPCWmoUel6ja1PwiCrATxKfDgXfNR9lH9zMtxJIAZe7QZrOu1wng2hTGk7UHnkI/b39IgDv8kdCXb4aFnoDKmDaNPEITJZDKY/KEObR84BTqH1JNX+mLBOxCxk7W9ezvz5vVr4yvdxMvHj/X94BT11+8BxN3eJvJqPvvAfaKE6fpa3eQkFohaJyJzGJ1D6kmr+m78J7iMGV28oz0ygRHuUG1R6e3TqIXEVQHQ+9Cz0cYFRAYQzMMXLz6Vgl8VoO0lsMeMoPGpqUmdZfiCbPGr/PRF4i0je6PBaBSS/vjHN35hK+QnoTP+//t6Ny+Cw5qVHv8XF+mWyZITVTkAAAAASUVORK5CYII="
      />
    </div>
  </body>
</html>
