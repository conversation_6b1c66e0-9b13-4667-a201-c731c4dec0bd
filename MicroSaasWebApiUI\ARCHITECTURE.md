# Personal Portal Architecture Overview

## Technology Stack
- **Framework**: Next.js 14 (App Router)
- **Database**: PostgreSQL + Prisma ORM
- **Auth**: Clerk
- **Payments**: Stripe
- **Styling**: Tailwind CSS + DaisyUI
- **Email**: Resend
- **Deployment**: Docker + Vercel

## Core Features
```mermaid
flowchart TD
    A[User Auth] -->|Clerk JWT| B(Subscription)
    B -->|Stripe Customer ID| C[Payment Processing]
    B --> D[Project Management]
    D --> E[Webhook Configuration]
    D --> F[AI Assistant Integration]
    B --> G[Email Audiences]
```

## Data Model Relationships
```mermaid
classDiagram
    class Subscription {
        +String id
        +String user_email
        +SubscriptionStatus status
        +String stripe_customer_id
        +DateTime createdAt
    }
    
    class Project {
        +String id
        +String connection_id
        +String webhook_id
        +String user_clerk_id
        +String status
    }
    
    class Audiences {
        +String id
        +String resend_id
        +String name
    }
    
    Subscription "1" -- "1..*" Project : Manages
    Subscription "1" -- "1..*" Audiences : Contains
```

## Security Architecture
1. **Authentication Flow**
```ts
// Middleware example
export default authMiddleware({
  publicRoutes: ['/', '/api/webhook'],
  ignoredRoutes: ['/api/webhook']
});
```

2. **Critical Security Configurations**
```js
// next.config.js
module.exports = {
  images: {
    domains: [
      // Trusted image sources
      'img.clerk.com',
      'res.cloudinary.com',
      // !!! Security Note: Verify ************
    ],
  },
}
```

## Deployment Pipeline
```mermaid
sequenceDiagram
    Local->>CI/CD: git push
    CI/CD->>Docker: Build container
    Docker->>Vercel: Deploy
    Vercel->>PostgreSQL: Database connection
```

## Monitoring & Observability
**Recommended Additions**:
- [ ] Sentry error tracking
- [ ] Prometheus metrics
- [ ] LogDNA integration

## Improvement Roadmap
1. Implement API rate limiting
2. Add database indexes for common queries
3. Set up CI/CD with GitHub Actions
4. Configure health check endpoints