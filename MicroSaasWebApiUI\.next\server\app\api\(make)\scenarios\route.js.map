{"version": 3, "file": "app/api/(make)/scenarios/route.js", "mappings": "oFAAAA,CAAAA,EAAAC,OAAA,CAAAC,QAAA,0CCAAF,CAAAA,EAAAC,OAAA,CAAAC,QAAA,kZEKO,IAAMC,EAAU,gBACVC,EAAU,OAEhB,eAAeC,IACrB,GAAI,CAGH,GAAI,CAFS,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,IAGlB,OAAOC,EAAAA,EAAYA,CAACC,IAAI,CACvB,CAAEC,MAAO,cAAe,EACxB,CAAEC,OAAQ,GAAI,GAIhB,IAAMC,EAAY,MAAMC,EAAAA,CAAKA,CAACC,GAAG,CAChC,CAAC,EAAEC,QAAQC,GAAG,CAACC,YAAY,CAAC,kBAAkB,EAAEF,QAAQC,GAAG,CAACE,YAAY,CAAC,CAAC,CAC1E,CAAEC,QAAS,CAAEC,cAAe,CAAC,MAAM,EAAEL,QAAQC,GAAG,CAACK,YAAY,CAAC,CAAC,CAAG,GAGnE,GAAI,CAACT,EAAUU,IAAI,EAAEV,UACpB,OAAOJ,EAAAA,EAAYA,CAACC,IAAI,CACvB,CAAEC,MAAO,oBAAqB,EAC9B,CAAEC,OAAQ,GAAI,GAIhB,OAAOH,EAAAA,EAAYA,CAACC,IAAI,CAAC,CACxBc,QAAS,GACTX,UAAWA,EAAUU,IAAI,CAACV,SAAS,EAErC,CAAE,MAAOY,EAAK,CAEb,OADAC,QAAQf,KAAK,CAAC,oBAAqBc,GAC5BhB,EAAAA,EAAYA,CAACC,IAAI,CACvB,CAAEC,MAAOc,GAAKE,SAAW,8BAA+B,EACxD,CAAEf,OAAQ,GAAI,EAEhB,CACD,CCnCA,IAAAgB,EAAA,IAAwBC,EAAAC,mBAAmB,EAC3CC,WAAA,CACAC,KAAcC,EAAAC,CAAS,CAAAC,SAAA,CACvBC,KAAA,8BACAC,SAAA,iBACAC,SAAA,QACAC,WAAA,gCACA,EACAC,iBAAA,2EACAC,iBAVA,GAWAC,SAAYC,CACZ,GAIA,CAAQC,oBAAAA,CAAA,CAAAC,6BAAAA,CAAA,CAAAC,YAAAA,CAAA,EAAiElB,EACzEmB,EAAA,8BACA,SAAAC,IACA,MAAW,GAAAC,EAAAC,EAAA,EAAW,CACtBJ,YAAAA,EACAD,6BAAAA,CACA,EACA,CC1BO,IAAAM,EAAqBC,EAC5BC,EAAeC,EAAAC,CAAsB,CAAAC,IAAA,CAAM5B", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/ignored|C:\\Projects\\PersonalPortal\\node_modules\\crypto-js|crypto", "webpack://_N_E/./src/app/api/(make)/scenarios/route.ts", "webpack://_N_E/./src/app/api/(make)/scenarios/route.ts?d9bd", "webpack://_N_E/?16a3"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "/* (ignored) */", "import { currentUser } from '@clerk/nextjs/server'\r\nimport axios from 'axios'\r\nimport { NextResponse } from 'next/server'\r\n\r\n// Mark this route as dynamic\r\nexport const dynamic = 'force-dynamic'\r\nexport const runtime = 'edge'\r\n\r\nexport async function GET() {\r\n\ttry {\r\n\t\tconst user = await currentUser()\r\n\r\n\t\tif (!user) {\r\n\t\t\treturn NextResponse.json(\r\n\t\t\t\t{ error: 'Unauthorized' },\r\n\t\t\t\t{ status: 401 }\r\n\t\t\t)\r\n\t\t}\r\n\r\n\t\tconst scenarios = await axios.get(\r\n\t\t\t`${process.env.MAKE_API_URL}/scenarios?teamId=${process.env.MAKE_TEAM_ID}`,\r\n\t\t\t{ headers: { Authorization: `Token ${process.env.MAKE_API_KEY}` } }\r\n\t\t)\r\n\r\n\t\tif (!scenarios.data?.scenarios) {\r\n\t\t\treturn NextResponse.json(\r\n\t\t\t\t{ error: 'No scenarios found' },\r\n\t\t\t\t{ status: 404 }\r\n\t\t\t)\r\n\t\t}\r\n\r\n\t\treturn NextResponse.json({\r\n\t\t\tsuccess: true,\r\n\t\t\tscenarios: scenarios.data.scenarios,\r\n\t\t})\r\n\t} catch (err) {\r\n\t\tconsole.error('Connection error:', err)\r\n\t\treturn NextResponse.json(\r\n\t\t\t{ error: err?.message || 'An unexpected error occurred' },\r\n\t\t\t{ status: 500 }\r\n\t\t)\r\n\t}\r\n}\r\n", "import { AppRouteRouteModule } from \"next/dist/server/future/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/future/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Projects\\\\PersonalPortal\\\\src\\\\app\\\\api\\\\(make)\\\\scenarios\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/(make)/scenarios/route\",\n        pathname: \"/api/scenarios\",\n        filename: \"route\",\n        bundlePath: \"app/api/(make)/scenarios/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\PersonalPortal\\\\src\\\\app\\\\api\\\\(make)\\\\scenarios\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/(make)/scenarios/route\";\nfunction patchFetch() {\n    return _patchFetch({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\nexport { routeModule, requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, originalPathname, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "import { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2F(make)%2Fscenarios%2Froute&page=%2Fapi%2F(make)%2Fscenarios%2Froute&pagePath=private-next-app-dir%2Fapi%2F(make)%2Fscenarios%2Froute.ts&appDir=C%3A%5CProjects%5CPersonalPortal%5Csrc%5Capp&appPaths=%2Fapi%2F(make)%2Fscenarios%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/(make)/scenarios/route.ts?__next_edge_ssr_entry__\";\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule);\n\n//# sourceMappingURL=edge-app-route.js.map"], "names": ["module", "exports", "require", "dynamic", "runtime", "GET", "currentUser", "NextResponse", "json", "error", "status", "scenarios", "axios", "get", "process", "env", "MAKE_API_URL", "MAKE_TEAM_ID", "headers", "Authorization", "MAKE_API_KEY", "data", "success", "err", "console", "message", "routeModule", "module_compiled", "AppRouteRouteModule", "definition", "kind", "route_kind", "x", "APP_ROUTE", "page", "pathname", "filename", "bundlePath", "resolvedPagePath", "nextConfigOutput", "userland", "route_namespaceObject", "requestAsyncStorage", "staticGenerationAsyncStorage", "serverHooks", "originalPathname", "patchFetch", "patch_fetch", "XH", "ComponentMod", "route_next_edge_ssr_entry_namespaceObject", "next_edge_app_route_loaderabsolutePagePath_private_next_app_dir_2Fapi_2F_make_2Fscenarios_2Froute_ts_page_2Fapi_2F_make_2Fscenarios_2Froute_appDirLoader_bmV4dC1hcHAtbG9hZGVyP25hbWU9YXBwJTJGYXBpJTJGKG1ha2UpJTJGc2NlbmFyaW9zJTJGcm91dGUmcGFnZT0lMkZhcGklMkYobWFrZSklMkZzY2VuYXJpb3MlMkZyb3V0ZSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRihtYWtlKSUyRnNjZW5hcmlvcyUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDUHJvamVjdHMlNUNQZXJzb25hbFBvcnRhbCU1Q3NyYyU1Q2FwcCZhcHBQYXRocz0lMkZhcGklMkYobWFrZSklMkZzY2VuYXJpb3MlMkZyb3V0ZSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCE_3D_nextConfigOutput_preferredRegion_middlewareConfig_e30_3D_", "edge_route_module_wrapper", "a", "wrap"], "sourceRoot": ""}