'use client'

import { Moon, Sun } from '@/icons'
import { useTheme } from 'next-themes'
import { useEffect } from 'react'

export const ThemeSwitch = () => {
  const { setTheme } = useTheme()

  useEffect(() => {
    setTheme('system')
  }, [])

  const handleChange = (e: any) => {
    const isDark = e.target.checked
    if (isDark) {
      setTheme('dark')
    } else {
      setTheme('light')
    }
  }

  return (
    <label className='flex items-center relative w-max cursor-pointer select-none'>
      <input
        type='checkbox'
        id='theme-toggle'
        onChange={handleChange}
        className='appearance-none transition-colors cursor-pointer w-14 h-[30px] rounded-full focus:outline-none border border-[#B7B8BB] dark:border-[#373C53] bg-white'
      />
      <span className='absolute font-medium text-xs uppercase right-1 text-white'>
        <Sun />
      </span>
      <span className='absolute font-medium text-xs uppercase right-8 text-white'>
        <Moon />
      </span>
      <span className='w-6 h-6 right-[29px] dark:right-[31px] absolute rounded-full transform transition-transform bg-[#0B111B] dark:bg-white' />
    </label>
  )
}

export default ThemeSwitch 