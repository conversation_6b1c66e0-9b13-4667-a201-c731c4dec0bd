'use client'

import { ReactNode } from 'react'
import AdUnit from './AdUnit'

// Define the ad unit types based on your Google AdSense account
export type AdUnitType = 'multiplex-vertical' | 'in-feed' | 'in-article' | 'display-horizontal'

// Define the props for the AdManager component
interface AdManagerProps {
  type: AdUnitType
  className?: string
  style?: React.CSSProperties
  children?: ReactNode
}

/**
 * AdManager component for easily displaying Google AdSense ads with predefined configurations
 *
 * @param type - The type of ad unit to display (multiplex-vertical, in-feed, in-article, display-horizontal)
 * @param className - Additional CSS classes
 * @param style - Additional inline styles
 * @param children - Optional children to display alongside the ad
 */
const AdManager = ({
  type,
  className = '',
  style = {},
  children
}: AdManagerProps) => {
  // Map ad unit types to their configurations
  const getAdConfig = () => {
    switch (type) {
      case 'multiplex-vertical':
        return {
          position: 'sidebar' as const,
          slot: '**********', // Multiplex Ad Vertical
          format: 'autorelaxed' as const
        }
      case 'in-feed':
        return {
          position: 'in-feed' as const,
          slot: '6889555982', // In feed Ad
          format: 'fluid' as const
        }
      case 'in-article':
        return {
          position: 'in-article' as const,
          slot: '2944836887', // InArticle
          format: 'in-article' as const
        }
      case 'display-horizontal':
        return {
          position: 'horizontal' as const,
          slot: '4454393519', // AD Hor JP
          format: 'auto' as const
        }
      default:
        return {
          position: 'horizontal' as const,
          slot: '4454393519', // Default to horizontal display ad
          format: 'auto' as const
        }
    }
  }

  const adConfig = getAdConfig()

  // Determine if this is a sidebar ad
  const isSidebar = type === 'multiplex-vertical'

  return (
    <div className={`ad-manager-container ${isSidebar ? '' : 'w-full max-w-[90rem] mx-auto px-4 sm:px-6 lg:px-8'} ${className}`} style={style}>
      <AdUnit
        position={adConfig.position}
        slot={adConfig.slot}
        format={adConfig.format}
      />
      {children}
    </div>
  )
}

export default AdManager
