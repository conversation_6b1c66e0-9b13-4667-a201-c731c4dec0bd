using MicroSaasWebApi.Models.Profile;
using MicroSaasWebApi.Controllers.MICROSAAS;

namespace MicroSaasWebApi.Services.MICROSAAS.Interface
{
    /// <summary>
    /// User profile service interface for MicroSaaS template
    /// </summary>
    public interface IUserProfileService
    {
        /// <summary>
        /// Get user profile by Supabase user ID
        /// </summary>
        /// <param name="supabaseUserId">Supabase user ID</param>
        /// <returns>User profile or null if not found</returns>
        Task<UserProfile?> GetUserProfileAsync(string supabaseUserId);

        /// <summary>
        /// Create a new user profile
        /// </summary>
        /// <param name="userProfile">User profile to create</param>
        /// <returns>Created user profile</returns>
        Task<UserProfile> CreateUserProfileAsync(UserProfile userProfile);

        /// <summary>
        /// Update user profile
        /// </summary>
        /// <param name="supabaseUserId">Supabase user ID</param>
        /// <param name="updateRequest">Update request data</param>
        /// <returns>Updated user profile or null if not found</returns>
        Task<UserProfile?> UpdateUserProfileAsync(string supabaseUserId, UpdateUserProfileRequest updateRequest);

        /// <summary>
        /// Delete user profile
        /// </summary>
        /// <param name="supabaseUserId">Supabase user ID</param>
        /// <returns>True if deleted successfully</returns>
        Task<bool> DeleteUserProfileAsync(string supabaseUserId);

        /// <summary>
        /// Upload profile image
        /// </summary>
        /// <param name="supabaseUserId">Supabase user ID</param>
        /// <param name="imageFile">Image file</param>
        /// <returns>Updated user profile with new image URL</returns>
        Task<UserProfile?> UploadProfileImageAsync(string supabaseUserId, IFormFile imageFile);

        /// <summary>
        /// Update user's last login timestamp
        /// </summary>
        /// <param name="supabaseUserId">Supabase user ID</param>
        /// <returns>Task</returns>
        Task UpdateLastLoginAsync(string supabaseUserId);

        /// <summary>
        /// Update user's subscription information
        /// </summary>
        /// <param name="supabaseUserId">Supabase user ID</param>
        /// <param name="subscriptionId">Subscription ID</param>
        /// <param name="subscriptionStatus">Subscription status</param>
        /// <param name="planType">Plan type</param>
        /// <param name="startDate">Subscription start date</param>
        /// <param name="endDate">Subscription end date</param>
        /// <returns>Updated user profile</returns>
        Task<UserProfile?> UpdateSubscriptionAsync(
            string supabaseUserId, 
            string subscriptionId, 
            string subscriptionStatus, 
            string planType, 
            DateTime? startDate, 
            DateTime? endDate);

        /// <summary>
        /// Get users by subscription status
        /// </summary>
        /// <param name="status">Subscription status</param>
        /// <returns>List of users with the specified subscription status</returns>
        Task<IEnumerable<UserProfile>> GetUsersBySubscriptionStatusAsync(string status);

        /// <summary>
        /// Search users by email or name
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <param name="limit">Maximum number of results</param>
        /// <returns>List of matching users</returns>
        Task<IEnumerable<UserProfile>> SearchUsersAsync(string searchTerm, int limit = 10);
    }
}
