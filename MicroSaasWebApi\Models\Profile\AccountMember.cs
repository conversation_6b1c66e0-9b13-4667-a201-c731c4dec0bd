﻿using MicroSaasWebApi.Properties.Enums;
using System.Xml.Serialization;

namespace MicroSaasWebApi.Models.Profile
{
    [Serializable]
    public class AccountMember
    {
        public Guid MasterID { get; set; }
        public bool isActive { get; set; } = true;
        public bool isInviteSent { get; set; } = false;
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        // public string AlphaCCOKey { get; set; }

        // public string PortalUserId { get; set; }
        public string LanguagePreference { get; set; } = "EN";
        public AccountInformation? Account { get; set; }
        public string? Email { get; set; }
        public eAccountMemberType MemberType { get; set; }
        public List<DateTime>? RegistrationAttempts { get; set; }
        [XmlIgnore]
        public bool RegisterOnPublish { get; set; }
        [XmlIgnore]
        public string? FullName
        {
            get
            {
                return FirstName + " " + LastName;
            }
        }
    }
}