'use client'

import { useEffect, useState } from 'react'
import GoogleAds from './GoogleAds'

interface AdUnitProps {
  position: 'sidebar' | 'horizontal' | 'in-article' | 'in-feed'
  slot?: string
  className?: string
  style?: React.CSSProperties
  format?: 'auto' | 'fluid' | 'rectangle' | 'horizontal' | 'vertical' | 'autorelaxed' | 'in-article'
}

/**
 * AdUnit component for displaying Google AdSense ads in different positions
 *
 * While this component can be used directly, it's recommended to use the AdManager
 * component instead, which provides predefined ad configurations.
 *
 * @param position - Where to display the ad (sidebar, horizontal, in-article, in-feed)
 * @param slot - The AdSense ad slot ID
 * @param className - Additional CSS classes
 * @param style - Additional inline styles
 */
const AdUnit = ({
  position,
  slot,
  className = '',
  style = {},
  format
}: AdUnitProps) => {
  const [mounted, setMounted] = useState(false)

  // Set mounted to true after component mounts to avoid hydration issues
  useEffect(() => {
    setMounted(true)
  }, [])

  // Only render on client side to avoid hydration issues
  if (!mounted) {
    // Return an empty div with the same dimensions to prevent layout shifts
    return <div className={`ad-placeholder ${className}`} style={{ minHeight: position === 'sidebar' ? '600px' : '280px', ...style }} />
  }

  // Default slots for different positions
  const getSlot = () => {
    if (slot) return slot

    switch (position) {
      case 'sidebar':
        return '5891334939' // Sidebar ad slot
      case 'horizontal':
        return '4454393519' // Horizontal ad slot
      case 'in-article':
        return '4454393519' // In-article ad slot
      case 'in-feed':
        return '4454393519' // In-feed ad slot
      default:
        return '4454393519' // Default ad slot
    }
  }

  // Get format based on position or use provided format
  const getFormat = () => {
    if (format) return format

    switch (position) {
      case 'sidebar':
        return 'autorelaxed' // For Multiplex ads
      case 'horizontal':
        return 'auto' // For Display ads
      case 'in-article':
        return 'in-article' // For In-article ads
      case 'in-feed':
        return 'fluid' // For In-feed ads
      default:
        return 'auto'
    }
  }

  // Get responsive setting based on position
  const getResponsive = () => {
    return position !== 'sidebar'
  }

  return (
    <GoogleAds
      slot={getSlot()}
      format={getFormat()}
      responsive={getResponsive()}
      layout={position}
      className={className}
      style={style}
    />
  )
}

export default AdUnit
