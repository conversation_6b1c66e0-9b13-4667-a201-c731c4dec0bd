{"Properties": {"AppName": "INFERNO App Settings", "Version": "01.00.00"}, "Configurations": {"Endpoints": {"PortfolioDataApiEndpoints": {"CreatePrivateSecurity": {"URL": "/data-api/v1/profiles/{profile_pk}/securities"}, "RetrievePrivateSecurityByID": {"URL": "/data-api/v1/profiles/{profile_pk}/securities/{id}"}, "ListPrivateSecuritiesByProfileID": {"URL": "/data-api/v1/profiles/{profile_pk}/securities"}, "PartiallyUpdatePrivateSecurityByID": {"URL": "/data-api/v1/profiles/{profile_pk}/securities/{id}"}, "CreatePrivateTransaction": {"URL": "/data-api/v1/profiles/{profile_pk}/securities/{security_pk}/transactions"}, "RetrievePrivateTransactionByID": {"URL": "/data-api/v1/profiles/{profile_pk}/securities/{security_pk}/transactions/{id}"}, "ListPrivateTransactionsBySecurityID": {"URL": "/data-api/v1/profiles/{profile_pk}/securities/{security_pk}/transactions"}, "PartiallyUpdatePrivateTransactionByID": {"URL": "/data-api/v1/profiles/{profile_pk}/securities/{security_pk}/transactions/{id}"}, "DestroyPrivateTransactionByID": {"URL": "/data-api/v1/profiles/{profile_pk}/securities/{security_pk}/transactions/{id}"}, "CreatePrivateSecurityPrice": {"URL": "/data-api/v1/profiles/{profile_pk}/securities/{security_pk}/prices"}, "RetrievePrivateSecurityPricebyID": {"URL": "/data-api/v1/profiles/{profile_pk}/securities/{security_pk}/prices/{id}"}, "ListPrivateSecurityPricesBySecurityID": {"URL": "/data-api/v1/profiles/{profile_pk}/securities/{security_pk}/prices"}, "PartiallyUpdatePrivateSecurityPriceByID": {"URL": "/data-api/v1/profiles/{profile_pk}/securities/{security_pk}/prices/{id}"}, "DestroyPrivateSecurityPriceByID": {"URL": "/data-api/v1/profiles/{profile_pk}/securities/{security_pk}/prices/{id}"}, "RetrieveAssetClass": {"URL": "/data-api/v1/asset-classes"}, "RetrieveSecurityCode": {"URL": "/data-api/v1/security-code"}, "RetrieveSecurityTypes": {"URL": "/data-api/v1/security-types"}, "RetrieveDataPipelineStatus": {"URL": "/data-api/v1/data-pipeline-status"}, "ExportPrivateSecuritiesTemplate": {"URL": "/data-api/v1/profiles/{profile_pk}/private-data/excel-template"}, "UploadPrivateSecuritiesTemplate": {"URL": "/data-api/v1/profiles/{profile_pk}/private-data/excel-template-upload"}, "CurrenciesList": {"URL": "/data-api/v1/currencies"}, "ProfilesAccountsSummaryRetrieve": {"URL": "/data-api/v1/profiles/{id}/accounts-summary"}, "ProfilesAsOfDatesRetrieve": {"URL": "/data-api/v1/profiles/{id}/as-of-dates"}, "ProfilesCustodianAccountCodesRetrieve": {"URL": "/data-api/v1/profiles/{id}/custodian-account-codes"}, "ProfilesDashboardLineGraphRetrieve": {"URL": "/data-api/v1/profiles/{id}/dashboard-line-graph"}, "ProfilesDashboardSummaryRetrieve": {"URL": "/data-api/v1/profiles/{id}/dashboard-summary"}, "ProfilesPortfolioSummaryRetrieve": {"URL": "/data-api/v1/profiles/{id}/portfolio-summary"}, "ProfilesFundingsRetrieve": {"URL": "/data-api/v1/profiles/{id}/fundings"}, "ProfilesHoldingsRetrieve": {"URL": "/data-api/v1/profiles/{id}/holdings"}, "ProfilesAllHoldingsRetrieve": {"URL": "/data-api/v1/profiles/{id}/all-holdings"}, "ProfilesPieChartByAccountRetrieve": {"URL": "/data-api/v1/profiles/{id}/pie-chart-by-account"}, "ProfilesPieChartByAssetClassRetrieve": {"URL": "/data-api/v1/profiles/{id}/pie-chart-by-asset-class"}, "ProfilesPieChartByCurrencyRetrieve": {"URL": "/data-api/v1/profiles/{id}/pie-chart-by-currency"}, "ProfilesPieChartsRetrieve": {"URL": "/data-api/v1/profiles/{id}/pie-charts"}, "ProfilesTopHoldingsRetrieve": {"URL": "/data-api/v1/profiles/{id}/top-holdings"}, "ProfilesTransactionsRetrieve": {"URL": "/data-api/v1/profiles/{id}/transactions"}, "ProfilesAccountsList": {"URL": "/data-api/v1/profiles/{profile_pk}/accounts"}, "ProfilesAccountsHoldingsRetrieve": {"URL": "/data-api/v1/profiles/{profile_pk}/accounts/{id}/holdings"}, "ProfilesAccountsTransactionsRetrieve": {"URL": "/data-api/v1/profiles/{profile_pk}/accounts/{id}/transactions"}, "LogUserLogin": {"URL": "/permissions/user-activities/log/login"}, "LogUserLogout": {"URL": "/permissions/user-activities/log/logout"}, "AccountLevelPerformance": {"URL": "/data-api/v1/profiles/{profile_id}/account-level-performance"}, "PresetDateRanges": {"URL": "/data-api/v1/profiles/{profile_id}/performance/preset-date-ranges"}, "RorTable": {"URL": "/data-api/v1/profiles/{profile_id}/performance/ror-table"}}, "PortfolioDocumentApiEndpoints": {"ListDocuments": {"URL": "/document-api/v1/documents"}, "SearchDocuments": {"URL": "/document-api/v1/documents/search"}, "DownloadDocuments": {"URL": "/document-api/v1/documents"}, "UploadDocuments": {"URL": "/document-api/v1/documents"}, "DeleteDocuments": {"URL": "/document-api/v1/documents"}, "MoveDocuments": {"URL": "/document-api/v1/documents"}, "CreateRole": {"URL": "/document-api/v1/roles/create"}, "ListRoles": {"URL": "/document-api/v1/roles/list"}, "DeleteRole": {"URL": "/document-api/v1/roles/{role_id}"}, "GetRole": {"URL": "/document-api/v1/roles/{role_id}"}, "CreateFolder": {"URL": "/document-api/v1/documents"}, "ActivateExternalUser": {"URL": "/document-api/v1/external-users/activate"}, "CreateExternalUser": {"URL": "/document-api/v1/external-users"}, "DeactivateExternalUser": {"URL": "/document-api/v1/external-users/deactivate"}, "InviteExternalUser": {"URL": "/document-api/v1/external-users/invite"}, "ListOrganizations": {"URL": "/document-api/v1/organizations"}, "CreateUser": {"URL": "/document-api/v1/users"}, "UpdateUser": {"URL": "/document-api/v1/users/{user_id}"}, "GetUser": {"URL": "/document-api/v1/users/{user_id}"}, "DeleteUser": {"URL": "/document-api/v1/users/{user_id}"}, "CreateGroup": {"URL": "/document-api/v1/groups"}, "UpdateGroup": {"URL": "/document-api/v1/groups/{group_id}"}, "GetGroup": {"URL": "/document-api/v1/groups/{group_id}"}, "DeleteGroup": {"URL": "/document-api/v1/groups/{group_id}"}, "GetDocumentCategories": {"URL": "/document-api/v1/documents/categories"}, "GetUserPermissions": {"URL": "/document-api/v1/users/{user_id}/permissions"}, "SearchUsers": {"URL": "/document-api/v1/users/search"}, "CreateShare": {"URL": "/document-api/v1/shares"}, "UpdateShare": {"URL": "/document-api/v1/shares/{share_id}"}, "GetShare": {"URL": "/document-api/v1/shares/{share_id}"}, "AddDocumentCategory": {"URL": "/document-api/v1/document-categories"}, "DeleteDocumentCategory": {"URL": "/document-api/v1/document-categories/{category_value}"}, "GetDocumentActivityFeed": {"URL": "/document-api/v1/documents/{document_id}/activity-feed"}, "UpdateDocumentMetadatas": {"URL": "/document-api/v1/documents/{document_id}/meta"}, "ListNotifications": {"URL": "/document-api/v1/users/{user_id}/notifications"}, "ListUnreadNotifications": {"URL": "/document-api/v1/users/{user_id}/notifications/unread"}, "UserSubscriptionNotifications": {"URL": "/document-api/v1/users/{user_id}/notifications/subscription"}, "GetAllShares": {"URL": "/document-api/v1/shares/all"}, "AddComment": {"URL": "/document-api/v1/documents/{document_id}/comments"}, "GetComment": {"URL": "/document-api/v1/documents/{document_id}/comments/{comment_id}"}, "ListComments": {"URL": "/document-api/v1/documents/{document_id}/comments"}, "DeleteComment": {"URL": "/document-api/v1/documents/{document_id}/comments/{comment_id}"}, "UpdateComment": {"URL": "/document-api/v1/documents/{document_id}/comments/{comment_id}"}, "ListUsers": {"URL": "/document-api/v1/documents/{document_id}/comments/users"}, "GetCommentUnreadCount": {"URL": "/document-api/v1/documents/{document_id}/comments/unread"}, "SetUserEmailNotification": {"URL": "/document-api/v1/users/{user_id}/notifications/email"}}, "PortfolioPermissionApiEndpoints": {"ListUsers": {"URL": "/permission-api/v1/users"}, "GetUser": {"URL": "/permission-api/v1/users/{user_id}"}, "CreateUser": {"URL": "/permission-api/v1/users"}, "SetUserLoginDatetime": {"URL": "/permission-api/v1/users/set-login-date"}, "ProfilesAccountsSearch": {"URL": "/permission-api/v1/accounts-profiles/search"}, "UpdateUser": {"URL": "/permission-api/v1/users/{user_id}"}, "DeleteUser": {"URL": "/permission-api/v1/users/{user_id}"}, "SendInviteUser": {"URL": "/permission-api/v1/users/{user_id}/invite"}, "CurrentUser": {"URL": "/permission-api/v1/users/me"}, "ListUserProfiles": {"URL": "/permission-api/v1/users/{user_id}/profiles"}, "AssignUserProfile": {"URL": "/permission-api/v1/users/{user_id}/profiles/{profile_id}"}, "ListUserRoles": {"URL": "/permission-api/v1/users/{user_id}/roles"}, "AssignUserRole": {"URL": "/permission-api/v1/users/{user_id}/roles/{role_id}"}, "ListProfiles": {"URL": "/permission-api/v1/profiles"}, "GetProfile": {"URL": "/permission-api/v1/profiles/{profile_id}"}, "UpdateProfile": {"URL": "/permission-api/v1/profiles/{profile_id}"}, "DeleteProfile": {"URL": "/permission-api/v1/profiles/{profile_id}"}, "CreateProfile": {"URL": "/permission-api/v1/profiles"}, "ListProfileAccounts": {"URL": "/permission-api/v1/profiles/{profile_id}/accounts"}, "AssignProfileAccount": {"URL": "/permission-api/v1/profiles/{profile_id}/accounts/{account_id}"}, "ListAccounts": {"URL": "/permission-api/v1/accounts"}, "GetAccount": {"URL": "/permission-api/v1/accounts/{account_id}"}, "CreateAccount": {"URL": "/permission-api/v1/accounts"}, "DeleteAccount": {"URL": "/permission-api/v1/accounts/{account_id}"}, "UpdateAccount": {"URL": "/permission-api/v1/accounts/{account_id}"}, "ListRoles": {"URL": "/permission-api/v1/roles"}, "GetRole": {"URL": "/permission-api/v1/roles/{role_id}"}, "DeleteRole": {"URL": "/permission-api/v1/roles/{role_id}"}, "UpdateRole": {"URL": "/permission-api/v1/roles/{role_id}"}, "CreateRole": {"URL": "/permission-api/v1/roles"}, "BatchAssignUsersToProfileGroup": {"URL": "/permission-api/v1/profile-groups/{profile_group_id}/batch-update"}, "BatchAssignUsersToProfile": {"URL": "/permission-api/v1/profiles/{profile_id}/batch-update"}}, "PortfolioDataInterchangeApiEndpoints": {"ListWorkflows": {"URL": "/fluxus/api/v1/workflows/"}, "CreateWorkflow": {"URL": "/fluxus/api/v1/workflows/"}, "GetWorkflow": {"URL": "/fluxus/api/v1/workflows/{workflow_id}/"}, "GetWorkflowSteps": {"URL": "/fluxus/api/v1/workflows/{workflow_id}/steps/"}, "UpdateWorkflow": {"URL": "/fluxus/api/v1/workflows/{workflow_id}/"}, "DeleteWorkflow": {"URL": "/fluxus/api/v1/workflows/{workflow_id}/"}, "ActivateWorkflow": {"URL": "/fluxus/api/v1/workflows/{workflow_id}/activate/"}, "ArchiveWorkflow": {"URL": "/fluxus/api/v1/workflows/{workflow_id}/archive/"}, "ListWorkflowVersions": {"URL": "/fluxus/api/v1/workflows/{workflow_id}/list_versions/"}, "GetWorkflowPreview": {"URL": "/fluxus/api/v1/workflows/{workflow_id}/preview/"}, "GetWorkflowVersionPreview": {"URL": "/fluxus/api/v1/workflows/{workflow_id}/preview_version/{version_id}/"}, "SetWorkflowPublicVersion": {"URL": "/fluxus/api/v1/workflows/{workflow_id}/set_public_version/"}, "UploadWorkflowVersion": {"URL": "/fluxus/api/v1/workflows/{workflow_id}/upload_version/"}, "GetWorkflowYAML": {"URL": "/fluxus/api/v1/workflows/{workflow_id}/yaml/"}, "GetWorkflowVersionYAML": {"URL": "/fluxus/api/v1/workflows/{workflow_id}/yaml/{version_id}/"}, "ListWorkflowInstances": {"URL": "/fluxus/api/v1/workflow/instance/"}, "CreateWorkflowInstance": {"URL": "/fluxus/api/v1/workflow/instance/"}, "GetWorkflowInstance": {"URL": "/fluxus/api/v1/workflow/instance/{instance_id}/"}, "UpdateWorkflowInstance": {"URL": "/fluxus/api/v1/workflow/instance/{instance_id}/"}, "GetWorkflowInstanceLogs": {"URL": "/fluxus/api/v1/workflow/instance/{instance_id}/audit/"}, "GetWorkflowInstanceComments": {"URL": "/fluxus/api/v1/workflow/instance/{instance_id}/comments/"}, "CreateWorkflowInstanceComment": {"URL": "/fluxus/api/v1/workflow/instance/{instance_id}/comments/"}, "GetWorkflowInstanceData": {"URL": "/fluxus/api/v1/workflow/instance/{instance_id}/data/"}, "CreateWorkflowInstanceData": {"URL": "/fluxus/api/v1/workflow/instance/{instance_id}/data/"}, "UpdateWorkflowInstanceData": {"URL": "/fluxus/api/v1/workflow/instance/{instance_id}/data-partial/"}, "GetWorkflowInstanceQuestions": {"URL": "/fluxus/api/v1/workflow/instance/{instance_id}/questions/"}, "GetWorkflowInstanceSteps": {"URL": "/fluxus/api/v1/workflow/instance/{instance_id}/steps/"}, "AssignWorkflowInstance": {"URL": "/fluxus/api/v1/workflow/instance/{instance_id}/assign/"}, "GetWorkflowInstanceFile": {"URL": "/fluxus/api/v1/workflow/instance/{instance_id}/file/{datatag}/"}}}}, "Tenants": [{"TenantId": "002", "TenantName": "PortfolioXpressWay", "TenantGuid": "ba5df4cb-ff5a-4a98-a927-7620fda84900", "Settings": {"AzureAdB2C": {"Instance": "https://devinfernob2c.b2clogin.com", "Domain": "devinfernob2c.onmicrosoft.com", "TenantId": "fb23f0ff-abc3-4dbe-a41a-56189f312374", "ClientId": "f2edcf1e-5af7-4ddf-901b-47b539af7103", "CallbackPath": "/signin-oidc", "Scopes": "access_as_user access_as_admin Scope.PXW", "SignUpSignInPolicyId": "B2C_1A_SIGNIN_TOTP", "SignedOutCallbackPath": "/signout/B2C_1A_SIGNIN_TOTP", "ResetPasswordPolicyId": "B2C_1_signup-reset", "EditProfilePolicyId": "B2C_1A_SIGNIN_TOTP", "EnablePiiLogging": true, "ApplicationIdUri": "https://devinfernob2c.onmicrosoft.com/f2edcf1e-5af7-4ddf-901b-47b539af7103", "JwksUri": "https://devinfernob2c.b2clogin.com/devinfernob2c.onmicrosoft.com/B2C_1A_SIGNIN_TOTP/discovery/v2.0/keys", "MetadataUrl": "https://devinfernob2c.b2clogin.com/devinfernob2c.onmicrosoft.com/B2C_1A_SIGNIN_TOTP/v2.0/.well-known/openid-configuration", "Issuer": "https://devinfernob2c.b2clogin.com/fb23f0ff-abc3-4dbe-a41a-56189f312374/v2.0/", "OrganizationId": "inferno-01", "BaseURL": "", "APIMKey": "", "APIMSecretKey": "", "GETMemberListURL": "users", "GETMemberURL": "users/{userid}", "GETProfilesForUserURL": "users/{userId}/profiles", "GETAccountsForProfileURL": "profiles/{profileId}/accounts", "GETMembersBriefURL": "users-details", "UPDATEMemberURL": "users/{userId}", "BearerTokenBaseURL": "https://login.microsoftonline.com/{0}/oauth2/v2.0/token", "BearerTokenTenant": "fb23f0ff-abc3-4dbe-a41a-56189f312374", "BearerTokenClientID": "", "BearerTokenClientSecret": "", "BearerTokenGrantType": "client_credentials", "BearerTokenScope": "https://devinfernob2c.onmicrosoft.com/f2edcf1e-5af7-4ddf-901b-47b539af7103/.default", "BearerTokenApplicationName": "INFERNO", "RedirectBaseURL": "devinfernob2c.b2clogin.com/devinfernob2c.onmicrosoft.com", "RedirectParam": "B2C_1A_CHANGEPASSWORD", "RedirectClientID": "f2edcf1e-5af7-4ddf-901b-47b539af7103", "RedirectMemberURI": ""}, "PortfolioDocumentApi": {"Domain": ""}, "PortfolioDataInterchangeApi": {"Domain": ""}, "Emails": {"EmailAddressForDocumentImportErrorMessage": "<EMAIL>", "DocumentImportErrorMessageEmailTemplate": "", "DocumentNotificationEmailTemplate": "", "DocumentNotificationSubject": "Canso - New documents available (DEV)", "DocumentNotificationDocumentCountPhase": "new document(s) for profile ", "SendGridEmailFrom": "<EMAIL>", "SendGridEmailFromName": "DEV - Do Not Reply"}, "Pages": {}, "Documents": {"SharePointDocumentSiteUrl": "", "SharePointDocumentClientName": "", "SharePointDocumentClientId": "", "SharePointDocumentClientSecret": "", "SharePointDocumentLandingDocumentLibrary": "", "SharePointDocumentLandingFolder": "", "SharePointDocumentFinalDocumentLibrary": "", "SharePointDocumentFinalFolder": "", "DocumentTypes": [{"Name": "", "DisplayName": "", "NoDocumentMessage": "", "Description": "", "IsActive": false}]}}}], "Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Error", "Microsoft.Hosting.Lifetime": "Error", "Microsoft.AspNetCore": "Error", "Microsoft.IdentityModel": "Error", "System": "Debug"}}, "Microsoft.IdentityModel.Logging": {"LogLevel": {"Default": "Trace"}, "IncludeScopes": false, "LogToConsole": true, "LogToTrace": true, "DisplayPII": true}, "AllowedHosts": "*"}