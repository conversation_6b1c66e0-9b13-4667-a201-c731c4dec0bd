class Elevator:
    def __init__(self, num_floors, current_floor=1):
        """Initialize the elevator system.
        
        Args:
            num_floors: Total number of floors in the building
            current_floor: Starting floor for the elevator
        """
        self.num_floors = num_floors
        self.current_floor = current_floor
        self.target_floors = []  # Queue of floors to visit
        self.direction = None    # 'up', 'down', or None when idle
        self.door_open = False
        self.maintenance_mode = False
        
    def request_floor(self, floor):
        """Request the elevator to visit a specific floor.
        
        Args:
            floor: The floor number to visit
            
        Returns:
            dict: Status of the request
        """
        if floor < 1 or floor > self.num_floors:
            return {"status": "error", "message": f"Invalid floor: {floor}"}
            
        if floor not in self.target_floors and floor != self.current_floor:
            self.target_floors.append(floor)
            self._update_direction()
            return {"status": "success", "message": f"Floor {floor} added to queue"}
        return {"status": "info", "message": f"Floor {floor} already in queue or current floor"}
    
    def call_elevator(self, floor, direction):
        """Call the elevator from a specific floor with intended direction.
        
        Args:
            floor: Floor from which elevator is called
            direction: Intended direction ('up' or 'down')
            
        Returns:
            dict: Status of the request
        """
        if floor < 1 or floor > self.num_floors:
            return {"status": "error", "message": f"Invalid floor: {floor}"}
            
        if direction not in ['up', 'down']:
            return {"status": "error", "message": f"Invalid direction: {direction}"}
            
        # Don't go up from top floor or down from bottom floor
        if (floor == self.num_floors and direction == 'up') or (floor == 1 and direction == 'down'):
            return {"status": "error", "message": f"Cannot go {direction} from floor {floor}"}
            
        return self.request_floor(floor)
    
    def _update_direction(self):
        """Update the elevator's direction based on the target floors."""
        if not self.target_floors:
            self.direction = None
            return
            
        if self.direction is None:
            # If idle, set direction based on the next target
            next_floor = self.target_floors[0]
            self.direction = 'up' if next_floor > self.current_floor else 'down'
        
    def move(self):
        """Move the elevator one step based on current direction and targets.
        
        Returns:
            dict: Status of the movement
        """
        if self.maintenance_mode:
            return {"status": "error", "message": "Elevator in maintenance mode"}
            
        if self.door_open:
            return {"status": "error", "message": "Cannot move with door open"}
            
        if not self.target_floors:
            return {"status": "info", "message": "No floors in queue"}
            
        # Sort target floors based on current direction for efficiency
        self.target_floors.sort(reverse=(self.direction == 'down'))
        
        # Move one floor in the current direction
        if self.direction == 'up':
            self.current_floor += 1
        else:
            self.current_floor -= 1
            
        # Check if we've reached a target floor
        if self.current_floor in self.target_floors:
            self.target_floors.remove(self.current_floor)
            self.open_door()
            
        # Update direction if needed
        self._update_direction()
        
        return {
            "status": "success", 
            "message": f"Moved to floor {self.current_floor}",
            "current_floor": self.current_floor,
            "direction": self.direction,
            "targets": self.target_floors
        }
    
    def open_door(self):
        """Open the elevator door.
        
        Returns:
            dict: Status of the operation
        """
        if self.door_open:
            return {"status": "info", "message": "Door already open"}
            
        self.door_open = True
        return {"status": "success", "message": "Door opened"}
    
    def close_door(self):
        """Close the elevator door.
        
        Returns:
            dict: Status of the operation
        """
        if not self.door_open:
            return {"status": "info", "message": "Door already closed"}
            
        self.door_open = False
        return {"status": "success", "message": "Door closed"}
    
    def emergency_stop(self):
        """Trigger emergency stop.
        
        Returns:
            dict: Status of the operation
        """
        self.target_floors = []
        self.direction = None
        return {"status": "success", "message": "Emergency stop activated"}
    
    def set_maintenance_mode(self, mode):
        """Set maintenance mode.
        
        Args:
            mode: Boolean indicating whether to enable maintenance mode
            
        Returns:
            dict: Status of the operation
        """
        self.maintenance_mode = mode
        if mode:
            self.target_floors = []
            self.direction = None
            return {"status": "success", "message": "Maintenance mode activated"}
        return {"status": "success", "message": "Maintenance mode deactivated"}
    
    def get_status(self):
        """Get the current status of the elevator.
        
        Returns:
            dict: Current elevator status
        """
        return {
            "current_floor": self.current_floor,
            "target_floors": self.target_floors,
            "direction": self.direction,
            "door_open": self.door_open,
            "maintenance_mode": self.maintenance_mode
        }


# Example API implementation using Flask
from flask import Flask, request, jsonify

app = Flask(__name__)
elevator = Elevator(num_floors=20)  # Building with 20 floors

@app.route('/elevator/status', methods=['GET'])
def get_status():
    return jsonify(elevator.get_status())

@app.route('/elevator/request', methods=['POST'])
def request_floor():
    data = request.json
    floor = data.get('floor')
    if floor is None:
        return jsonify({"status": "error", "message": "Floor parameter required"})
    return jsonify(elevator.request_floor(floor))

@app.route('/elevator/call', methods=['POST'])
def call_elevator():
    data = request.json
    floor = data.get('floor')
    direction = data.get('direction')
    if floor is None or direction is None:
        return jsonify({"status": "error", "message": "Floor and direction parameters required"})
    return jsonify(elevator.call_elevator(floor, direction))

@app.route('/elevator/door/open', methods=['POST'])
def open_door():
    return jsonify(elevator.open_door())

@app.route('/elevator/door/close', methods=['POST'])
def close_door():
    return jsonify(elevator.close_door())

@app.route('/elevator/move', methods=['POST'])
def move_elevator():
    return jsonify(elevator.move())

@app.route('/elevator/emergency', methods=['POST'])
def emergency():
    return jsonify(elevator.emergency_stop())

@app.route('/elevator/maintenance', methods=['POST'])
def maintenance():
    data = request.json
    mode = data.get('mode', False)
    return jsonify(elevator.set_maintenance_mode(mode))

if __name__ == '__main__':
    app.run(debug=True)