"use strict";(()=>{var e={};e.id=116,e.ids=[116],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},19415:(e,r,o)=>{o.r(r),o.d(r,{originalPathname:()=>h,patchFetch:()=>g,requestAsyncStorage:()=>d,routeModule:()=>u,serverHooks:()=>m,staticGenerationAsyncStorage:()=>l});var s={};o.r(s),o.d(s,{POST:()=>p,dynamic:()=>c});var t=o(49303),n=o(88716),a=o(60670),i=o(87070);let c="force-dynamic";async function p(e){try{let r=await e.j<PERSON>();return console.log("Received email from Resend webhook:",r),i.NextResponse.json({success:!0,message:"<PERSON><PERSON> received and processed successfully"})}catch(e){return console.error("Error processing incoming email:",e),i.NextResponse.json({success:!1,error:"Failed to process incoming email"},{status:500})}}let u=new t.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/webhook/resend/route",pathname:"/api/webhook/resend",filename:"route",bundlePath:"app/api/webhook/resend/route"},resolvedPagePath:"C:\\Projects\\PersonalPortal\\src\\app\\api\\webhook\\resend\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:d,staticGenerationAsyncStorage:l,serverHooks:m}=u,h="/api/webhook/resend/route";function g(){return(0,a.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:l})}},49303:(e,r,o)=>{e.exports=o(30517)}};var r=require("../../../../webpack-runtime.js");r.C(e);var o=e=>r(r.s=e),s=r.X(0,[948,70],()=>o(19415));module.exports=s})();