"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@vercel";
exports.ids = ["vendor-chunks/@vercel"];
exports.modules = {

/***/ "(ssr)/./node_modules/@vercel/analytics/dist/react/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@vercel/analytics/dist/react/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Analytics: () => (/* binding */ Analytics),\n/* harmony export */   track: () => (/* binding */ track)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ Analytics,track auto */ // src/react/index.tsx\n\n// package.json\nvar name = \"@vercel/analytics\";\nvar version = \"1.5.0\";\n// src/queue.ts\nvar initQueue = ()=>{\n    if (window.va) return;\n    window.va = function a(...params) {\n        (window.vaq = window.vaq || []).push(params);\n    };\n};\n// src/utils.ts\nfunction isBrowser() {\n    return \"undefined\" !== \"undefined\";\n}\nfunction detectEnvironment() {\n    try {\n        const env = \"development\";\n        if (env === \"development\" || env === \"test\") {\n            return \"development\";\n        }\n    } catch (e) {}\n    return \"production\";\n}\nfunction setMode(mode = \"auto\") {\n    if (mode === \"auto\") {\n        window.vam = detectEnvironment();\n        return;\n    }\n    window.vam = mode;\n}\nfunction getMode() {\n    const mode = isBrowser() ? window.vam : detectEnvironment();\n    return mode || \"production\";\n}\nfunction isProduction() {\n    return getMode() === \"production\";\n}\nfunction isDevelopment() {\n    return getMode() === \"development\";\n}\nfunction removeKey(key, { [key]: _, ...rest }) {\n    return rest;\n}\nfunction parseProperties(properties, options) {\n    if (!properties) return void 0;\n    let props = properties;\n    const errorProperties = [];\n    for (const [key, value] of Object.entries(properties)){\n        if (typeof value === \"object\" && value !== null) {\n            if (options.strip) {\n                props = removeKey(key, props);\n            } else {\n                errorProperties.push(key);\n            }\n        }\n    }\n    if (errorProperties.length > 0 && !options.strip) {\n        throw Error(`The following properties are not valid: ${errorProperties.join(\", \")}. Only strings, numbers, booleans, and null are allowed.`);\n    }\n    return props;\n}\nfunction getScriptSrc(props) {\n    if (props.scriptSrc) {\n        return props.scriptSrc;\n    }\n    if (isDevelopment()) {\n        return \"https://va.vercel-scripts.com/v1/script.debug.js\";\n    }\n    if (props.basePath) {\n        return `${props.basePath}/insights/script.js`;\n    }\n    return \"/_vercel/insights/script.js\";\n}\n// src/generic.ts\nfunction inject(props = {\n    debug: true\n}) {\n    var _a;\n    if (!isBrowser()) return;\n    setMode(props.mode);\n    initQueue();\n    if (props.beforeSend) {\n        (_a = window.va) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n    }\n    const src = getScriptSrc(props);\n    if (document.head.querySelector(`script[src*=\"${src}\"]`)) return;\n    const script = document.createElement(\"script\");\n    script.src = src;\n    script.defer = true;\n    script.dataset.sdkn = name + (props.framework ? `/${props.framework}` : \"\");\n    script.dataset.sdkv = version;\n    if (props.disableAutoTrack) {\n        script.dataset.disableAutoTrack = \"1\";\n    }\n    if (props.endpoint) {\n        script.dataset.endpoint = props.endpoint;\n    } else if (props.basePath) {\n        script.dataset.endpoint = `${props.basePath}/insights`;\n    }\n    if (props.dsn) {\n        script.dataset.dsn = props.dsn;\n    }\n    script.onerror = ()=>{\n        const errorMessage = isDevelopment() ? \"Please check if any ad blockers are enabled and try again.\" : \"Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.\";\n        console.log(`[Vercel Web Analytics] Failed to load script from ${src}. ${errorMessage}`);\n    };\n    if (isDevelopment() && props.debug === false) {\n        script.dataset.debug = \"false\";\n    }\n    document.head.appendChild(script);\n}\nfunction track(name2, properties, options) {\n    var _a, _b;\n    if (!isBrowser()) {\n        const msg = \"[Vercel Web Analytics] Please import `track` from `@vercel/analytics/server` when using this function in a server environment\";\n        if (isProduction()) {\n            console.warn(msg);\n        } else {\n            throw new Error(msg);\n        }\n        return;\n    }\n    if (!properties) {\n        (_a = window.va) == null ? void 0 : _a.call(window, \"event\", {\n            name: name2,\n            options\n        });\n        return;\n    }\n    try {\n        const props = parseProperties(properties, {\n            strip: isProduction()\n        });\n        (_b = window.va) == null ? void 0 : _b.call(window, \"event\", {\n            name: name2,\n            data: props,\n            options\n        });\n    } catch (err) {\n        if (err instanceof Error && isDevelopment()) {\n            console.error(err);\n        }\n    }\n}\nfunction pageview({ route, path }) {\n    var _a;\n    (_a = window.va) == null ? void 0 : _a.call(window, \"pageview\", {\n        route,\n        path\n    });\n}\n// src/react/utils.ts\nfunction getBasePath() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/react/index.tsx\nfunction Analytics(props) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        var _a;\n        if (props.beforeSend) {\n            (_a = window.va) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n        }\n    }, [\n        props.beforeSend\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        inject({\n            framework: props.framework || \"react\",\n            basePath: props.basePath ?? getBasePath(),\n            ...props.route !== void 0 && {\n                disableAutoTrack: true\n            },\n            ...props\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (props.route && props.path) {\n            pageview({\n                route: props.route,\n                path: props.path\n            });\n        }\n    }, [\n        props.route,\n        props.path\n    ]);\n    return null;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHZlcmNlbC9hbmFseXRpY3MvZGlzdC9yZWFjdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUMwQjs7QUNBeEIsSUFBQUMsT0FBUTtBQUNSLElBQUFDLFVBQVc7O0FDRk4sSUFBTUMsWUFBWTtJQUV2QixJQUFJQyxPQUFPQyxFQUFBLEVBQUk7SUFFZkQsT0FBT0MsRUFBQSxHQUFLLFNBQVNDLEVBQUEsR0FBS0MsTUFBQTtRQUN2QkgsQ0FBQUEsT0FBT0ksR0FBQSxHQUFNSixPQUFPSSxHQUFBLElBQU8sRUFBQyxFQUFHQyxJQUFBLENBQUtGO0lBQ3ZDO0FBQ0Y7O0FDTE8sU0FBU0c7SUFDZCxPQUFPLGdCQUFrQjtBQUMzQjtBQUVBLFNBQVNDO0lBQ1AsSUFBSTtRQUNGLE1BQU1DO1FBQ04sSUFBSUEsUUFBUSxpQkFBaUJBLFFBQVEsUUFBUTtZQUMzQyxPQUFPO1FBQ1Q7SUFDRixTQUFTQyxHQUFHLENBRVo7SUFDQSxPQUFPO0FBQ1Q7QUFFTyxTQUFTQyxRQUFRQyxPQUFhO0lBQ25DLElBQUlBLFNBQVMsUUFBUTtRQUNuQlgsT0FBT1ksR0FBQSxHQUFNTDtRQUNiO0lBQ0Y7SUFFQVAsT0FBT1ksR0FBQSxHQUFNRDtBQUNmO0FBRU8sU0FBU0U7SUFDZCxNQUFNRixPQUFPTCxjQUFjTixPQUFPWSxHQUFBLEdBQU1MO0lBQ3hDLE9BQU9JLFFBQVE7QUFDakI7QUFFTyxTQUFTRztJQUNkLE9BQU9ELGNBQWM7QUFDdkI7QUFFTyxTQUFTRTtJQUNkLE9BQU9GLGNBQWM7QUFDdkI7QUFFQSxTQUFTRyxVQUNQQyxHQUFBLEVBQ0EsRUFBRSxDQUFDQSxJQUFHLEVBQUdDLENBQUEsRUFBRyxHQUFHQyxNQUFLO0lBRXBCLE9BQU9BO0FBQ1Q7QUFFTyxTQUFTQyxnQkFDZEMsVUFBQSxFQUNBQyxPQUFBO0lBSUEsSUFBSSxDQUFDRCxZQUFZLE9BQU87SUFDeEIsSUFBSUUsUUFBUUY7SUFDWixNQUFNRyxrQkFBNEIsRUFBQztJQUNuQyxXQUFXLENBQUNQLEtBQUtRLE1BQUssSUFBS0MsT0FBT0MsT0FBQSxDQUFRTixZQUFhO1FBQ3JELElBQUksT0FBT0ksVUFBVSxZQUFZQSxVQUFVLE1BQU07WUFDL0MsSUFBSUgsUUFBUU0sS0FBQSxFQUFPO2dCQUNqQkwsUUFBUVAsVUFBVUMsS0FBS007WUFDekIsT0FBTztnQkFDTEMsZ0JBQWdCbkIsSUFBQSxDQUFLWTtZQUN2QjtRQUNGO0lBQ0Y7SUFFQSxJQUFJTyxnQkFBZ0JLLE1BQUEsR0FBUyxLQUFLLENBQUNQLFFBQVFNLEtBQUEsRUFBTztRQUNoRCxNQUFNRSxNQUNKLDJDQUEyQ04sZ0JBQWdCTyxJQUFBLENBQ3pELE1BQ0Q7SUFFTDtJQUNBLE9BQU9SO0FBQ1Q7QUE2Q08sU0FBU1MsYUFDZFQsS0FBQTtJQUVBLElBQUlBLE1BQU1VLFNBQUEsRUFBVztRQUNuQixPQUFPVixNQUFNVSxTQUFBO0lBQ2Y7SUFDQSxJQUFJbEIsaUJBQWlCO1FBQ25CLE9BQU87SUFDVDtJQUNBLElBQUlRLE1BQU1XLFFBQUEsRUFBVTtRQUNsQixPQUFPLEdBQUdYLE1BQU1XLFFBQVE7SUFDMUI7SUFDQSxPQUFPO0FBQ1Q7O0FDckdBLFNBQVNDLE9BQ1BaLFFBSUk7SUFDRmEsT0FBTztBQUNUO0lBdENGLElBQUFDO0lBd0NFLElBQUksQ0FBQy9CLGFBQWE7SUFFbEJJLFFBQVFhLE1BQU1aLElBQUk7SUFFbEJaO0lBRUEsSUFBSXdCLE1BQU1lLFVBQUEsRUFBWTtRQUNwQkQsQ0FBQUEsS0FBQXJDLE9BQU9DLEVBQUEsS0FBUCxnQkFBQW9DLEdBQUFFLElBQUEsQ0FBQXZDLFFBQVksY0FBY3VCLE1BQU1lLFVBQUE7SUFDbEM7SUFFQSxNQUFNRSxNQUFNUixhQUFhVDtJQUV6QixJQUFJa0IsU0FBU0MsSUFBQSxDQUFLQyxhQUFBLENBQWMsZ0JBQWdCSCxJQUFHLEdBQUksR0FBRztJQUUxRCxNQUFNSSxTQUFTSCxTQUFTSSxhQUFBLENBQWM7SUFDdENELE9BQU9KLEdBQUEsR0FBTUE7SUFDYkksT0FBT0UsS0FBQSxHQUFRO0lBQ2ZGLE9BQU9HLE9BQUEsQ0FBUUMsSUFBQSxHQUNibkQsT0FBZTBCLENBQUFBLE1BQU0wQixTQUFBLEdBQVksSUFBSTFCLE1BQU0wQixTQUFTLEtBQUs7SUFDM0RMLE9BQU9HLE9BQUEsQ0FBUUcsSUFBQSxHQUFPcEQ7SUFFdEIsSUFBSXlCLE1BQU00QixnQkFBQSxFQUFrQjtRQUMxQlAsT0FBT0csT0FBQSxDQUFRSSxnQkFBQSxHQUFtQjtJQUNwQztJQUNBLElBQUk1QixNQUFNNkIsUUFBQSxFQUFVO1FBQ2xCUixPQUFPRyxPQUFBLENBQVFLLFFBQUEsR0FBVzdCLE1BQU02QixRQUFBO0lBQ2xDLFdBQVc3QixNQUFNVyxRQUFBLEVBQVU7UUFDekJVLE9BQU9HLE9BQUEsQ0FBUUssUUFBQSxHQUFXLEdBQUc3QixNQUFNVyxRQUFRO0lBQzdDO0lBQ0EsSUFBSVgsTUFBTThCLEdBQUEsRUFBSztRQUNiVCxPQUFPRyxPQUFBLENBQVFNLEdBQUEsR0FBTTlCLE1BQU04QixHQUFBO0lBQzdCO0lBRUFULE9BQU9VLE9BQUEsR0FBVTtRQUNmLE1BQU1DLGVBQWV4QyxrQkFDakIsK0RBQ0E7UUFHSnlDLFFBQVFDLEdBQUEsQ0FDTixxREFBcURqQixJQUFHLElBQUtlLGFBQVk7SUFFN0U7SUFFQSxJQUFJeEMsbUJBQW1CUSxNQUFNYSxLQUFBLEtBQVUsT0FBTztRQUM1Q1EsT0FBT0csT0FBQSxDQUFRWCxLQUFBLEdBQVE7SUFDekI7SUFFQUssU0FBU0MsSUFBQSxDQUFLZ0IsV0FBQSxDQUFZZDtBQUM1QjtBQVFBLFNBQVNlLE1BQ1A5RCxLQUFBQSxFQUNBd0IsVUFBQSxFQUNBQyxPQUFBO0lBcEdGLElBQUFlLElBQUF1QjtJQXdHRSxJQUFJLENBQUN0RCxhQUFhO1FBQ2hCLE1BQU11RCxNQUNKO1FBRUYsSUFBSS9DLGdCQUFnQjtZQUVsQjBDLFFBQVFNLElBQUEsQ0FBS0Q7UUFDZixPQUFPO1lBQ0wsTUFBTSxJQUFJL0IsTUFBTStCO1FBQ2xCO1FBRUE7SUFDRjtJQUVBLElBQUksQ0FBQ3hDLFlBQVk7UUFDZmdCLENBQUFBLEtBQUFyQyxPQUFPQyxFQUFBLEtBQVAsZ0JBQUFvQyxHQUFBRSxJQUFBLENBQUF2QyxRQUFZLFNBQVM7WUFBRUgsTUFBQUE7WUFBTXlCO1FBQVE7UUFDckM7SUFDRjtJQUVBLElBQUk7UUFDRixNQUFNQyxRQUFRSCxnQkFBZ0JDLFlBQVk7WUFDeENPLE9BQU9kO1FBQ1Q7UUFFQThDLENBQUFBLEtBQUE1RCxPQUFPQyxFQUFBLEtBQVAsZ0JBQUEyRCxHQUFBckIsSUFBQSxDQUFBdkMsUUFBWSxTQUFTO1lBQ25CSCxNQUFBQTtZQUNBa0UsTUFBTXhDO1lBQ05EO1FBQ0Y7SUFDRixTQUFTMEMsS0FBSztRQUNaLElBQUlBLGVBQWVsQyxTQUFTZixpQkFBaUI7WUFFM0N5QyxRQUFRUyxLQUFBLENBQU1EO1FBQ2hCO0lBQ0Y7QUFDRjtBQUVBLFNBQVNFLFNBQVMsRUFDaEJDLEtBQUEsRUFDQUMsSUFBQSxFQUNGO0lBaEpBLElBQUEvQjtJQW9KRUEsQ0FBQUEsS0FBQXJDLE9BQU9DLEVBQUEsS0FBUCxnQkFBQW9DLEdBQUFFLElBQUEsQ0FBQXZDLFFBQVksWUFBWTtRQUFFbUU7UUFBT0M7SUFBSztBQUN4Qzs7QUNySk8sU0FBU0M7SUFLZCxJQUFJLE9BQU9DLFlBQVksZUFBZSxPQUFPQSxRQUFROUQsR0FBQSxLQUFRLGFBQWE7UUFDeEUsT0FBTztJQUNUO0lBQ0EsT0FBTzhELFFBQVE5RCxHQUFBLENBQUkrRCx1Q0FBQTtBQUNyQjs7QUxvQkEsU0FBU0MsVUFDUGpELEtBQUE7SUFPQTNCLGdEQUFTQSxDQUFDO1FBckNaLElBQUF5QztRQXNDSSxJQUFJZCxNQUFNZSxVQUFBLEVBQVk7WUFDcEJELENBQUFBLEtBQUFyQyxPQUFPQyxFQUFBLEtBQVAsZ0JBQUFvQyxHQUFBRSxJQUFBLENBQUF2QyxRQUFZLGNBQWN1QixNQUFNZSxVQUFBO1FBQ2xDO0lBQ0YsR0FBRztRQUFDZixNQUFNZSxVQUFVO0tBQUM7SUFHckIxQyxnREFBU0EsQ0FBQztRQUNSdUMsT0FBTztZQUNMYyxXQUFXMUIsTUFBTTBCLFNBQUEsSUFBYTtZQUM5QmYsVUFBVVgsTUFBTVcsUUFBQSxJQUFZbUM7WUFDNUIsR0FBSTlDLE1BQU00QyxLQUFBLEtBQVUsVUFBYTtnQkFBRWhCLGtCQUFrQjtZQUFLO1lBQzFELEdBQUc1QixLQUFBO1FBQ0w7SUFFRixHQUFHLEVBQUU7SUFFTDNCLGdEQUFTQSxDQUFDO1FBRVIsSUFBSTJCLE1BQU00QyxLQUFBLElBQVM1QyxNQUFNNkMsSUFBQSxFQUFNO1lBQzdCRixTQUFTO2dCQUFFQyxPQUFPNUMsTUFBTTRDLEtBQUE7Z0JBQU9DLE1BQU03QyxNQUFNNkMsSUFBQTtZQUFLO1FBQ2xEO0lBQ0YsR0FBRztRQUFDN0MsTUFBTTRDLEtBQUE7UUFBTzVDLE1BQU02QyxJQUFJO0tBQUM7SUFFNUIsT0FBTztBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWljcm8tc2Fzcy1mYXN0Ly4uLy4uL3NyYy9yZWFjdC9pbmRleC50c3g/ZTRiNyIsIndlYnBhY2s6Ly9taWNyby1zYXNzLWZhc3QvLi4vLi4vcGFja2FnZS5qc29uPzZmYzYiLCJ3ZWJwYWNrOi8vbWljcm8tc2Fzcy1mYXN0Ly4uLy4uL3NyYy9xdWV1ZS50cz84NzNlIiwid2VicGFjazovL21pY3JvLXNhc3MtZmFzdC8uLi8uLi9zcmMvdXRpbHMudHM/NjI1MiIsIndlYnBhY2s6Ly9taWNyby1zYXNzLWZhc3QvLi4vLi4vc3JjL2dlbmVyaWMudHM/MGJkZiIsIndlYnBhY2s6Ly9taWNyby1zYXNzLWZhc3QvLi4vLi4vc3JjL3JlYWN0L3V0aWxzLnRzP2NjZTkiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgaW5qZWN0LCB0cmFjaywgcGFnZXZpZXcgfSBmcm9tICcuLi9nZW5lcmljJztcbmltcG9ydCB0eXBlIHsgQW5hbHl0aWNzUHJvcHMsIEJlZm9yZVNlbmQsIEJlZm9yZVNlbmRFdmVudCB9IGZyb20gJy4uL3R5cGVzJztcbmltcG9ydCB7IGdldEJhc2VQYXRoIH0gZnJvbSAnLi91dGlscyc7XG5cbi8qKlxuICogSW5qZWN0cyB0aGUgVmVyY2VsIFdlYiBBbmFseXRpY3Mgc2NyaXB0IGludG8gdGhlIHBhZ2UgaGVhZCBhbmQgc3RhcnRzIHRyYWNraW5nIHBhZ2Ugdmlld3MuIFJlYWQgbW9yZSBpbiBvdXIgW2RvY3VtZW50YXRpb25dKGh0dHBzOi8vdmVyY2VsLmNvbS9kb2NzL2NvbmNlcHRzL2FuYWx5dGljcy9wYWNrYWdlKS5cbiAqIEBwYXJhbSBbcHJvcHNdIC0gQW5hbHl0aWNzIG9wdGlvbnMuXG4gKiBAcGFyYW0gW3Byb3BzLm1vZGVdIC0gVGhlIG1vZGUgdG8gdXNlIGZvciB0aGUgYW5hbHl0aWNzIHNjcmlwdC4gRGVmYXVsdHMgdG8gYGF1dG9gLlxuICogIC0gYGF1dG9gIC0gQXV0b21hdGljYWxseSBkZXRlY3QgdGhlIGVudmlyb25tZW50LiAgVXNlcyBgcHJvZHVjdGlvbmAgaWYgdGhlIGVudmlyb25tZW50IGNhbm5vdCBiZSBkZXRlcm1pbmVkLlxuICogIC0gYHByb2R1Y3Rpb25gIC0gQWx3YXlzIHVzZSB0aGUgcHJvZHVjdGlvbiBzY3JpcHQuIChTZW5kcyBldmVudHMgdG8gdGhlIHNlcnZlcilcbiAqICAtIGBkZXZlbG9wbWVudGAgLSBBbHdheXMgdXNlIHRoZSBkZXZlbG9wbWVudCBzY3JpcHQuIChMb2dzIGV2ZW50cyB0byB0aGUgY29uc29sZSlcbiAqIEBwYXJhbSBbcHJvcHMuZGVidWddIC0gV2hldGhlciB0byBlbmFibGUgZGVidWcgbG9nZ2luZyBpbiBkZXZlbG9wbWVudC4gRGVmYXVsdHMgdG8gYHRydWVgLlxuICogQHBhcmFtIFtwcm9wcy5iZWZvcmVTZW5kXSAtIEEgbWlkZGxld2FyZSBmdW5jdGlvbiB0byBtb2RpZnkgZXZlbnRzIGJlZm9yZSB0aGV5IGFyZSBzZW50LiBTaG91bGQgcmV0dXJuIHRoZSBldmVudCBvYmplY3Qgb3IgYG51bGxgIHRvIGNhbmNlbCB0aGUgZXZlbnQuXG4gKiBAZXhhbXBsZVxuICogYGBganNcbiAqIGltcG9ydCB7IEFuYWx5dGljcyB9IGZyb20gJ0B2ZXJjZWwvYW5hbHl0aWNzL3JlYWN0JztcbiAqXG4gKiBleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoKSB7XG4gKiAgcmV0dXJuIChcbiAqICAgPGRpdj5cbiAqICAgIDxBbmFseXRpY3MgLz5cbiAqICAgIDxoMT5NeSBBcHA8L2gxPlxuICogIDwvZGl2PlxuICogKTtcbiAqIH1cbiAqIGBgYFxuICovXG5mdW5jdGlvbiBBbmFseXRpY3MoXG4gIHByb3BzOiBBbmFseXRpY3NQcm9wcyAmIHtcbiAgICBmcmFtZXdvcms/OiBzdHJpbmc7XG4gICAgcm91dGU/OiBzdHJpbmcgfCBudWxsO1xuICAgIHBhdGg/OiBzdHJpbmcgfCBudWxsO1xuICAgIGJhc2VQYXRoPzogc3RyaW5nO1xuICB9XG4pOiBudWxsIHtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAocHJvcHMuYmVmb3JlU2VuZCkge1xuICAgICAgd2luZG93LnZhPy4oJ2JlZm9yZVNlbmQnLCBwcm9wcy5iZWZvcmVTZW5kKTtcbiAgICB9XG4gIH0sIFtwcm9wcy5iZWZvcmVTZW5kXSk7XG5cbiAgLy8gYmlvbWUtaWdub3JlIGxpbnQvY29ycmVjdG5lc3MvdXNlRXhoYXVzdGl2ZURlcGVuZGVuY2llczogb25seSBydW4gb25jZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGluamVjdCh7XG4gICAgICBmcmFtZXdvcms6IHByb3BzLmZyYW1ld29yayB8fCAncmVhY3QnLFxuICAgICAgYmFzZVBhdGg6IHByb3BzLmJhc2VQYXRoID8/IGdldEJhc2VQYXRoKCksXG4gICAgICAuLi4ocHJvcHMucm91dGUgIT09IHVuZGVmaW5lZCAmJiB7IGRpc2FibGVBdXRvVHJhY2s6IHRydWUgfSksXG4gICAgICAuLi5wcm9wcyxcbiAgICB9KTtcbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvZXhoYXVzdGl2ZS1kZXBzIC0tIG9ubHkgcnVuIG9uY2VcbiAgfSwgW10pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gZXhwbGljaXRlbHkgdHJhY2sgcGFnZSB2aWV3LCBzaW5jZSB3ZSBkaXNhYmxlZCBhdXRvIHRyYWNraW5nXG4gICAgaWYgKHByb3BzLnJvdXRlICYmIHByb3BzLnBhdGgpIHtcbiAgICAgIHBhZ2V2aWV3KHsgcm91dGU6IHByb3BzLnJvdXRlLCBwYXRoOiBwcm9wcy5wYXRoIH0pO1xuICAgIH1cbiAgfSwgW3Byb3BzLnJvdXRlLCBwcm9wcy5wYXRoXSk7XG5cbiAgcmV0dXJuIG51bGw7XG59XG5cbmV4cG9ydCB7IHRyYWNrLCBBbmFseXRpY3MgfTtcbmV4cG9ydCB0eXBlIHsgQW5hbHl0aWNzUHJvcHMsIEJlZm9yZVNlbmQsIEJlZm9yZVNlbmRFdmVudCB9O1xuIiwie1xuICBcIm5hbWVcIjogXCJAdmVyY2VsL2FuYWx5dGljc1wiLFxuICBcInZlcnNpb25cIjogXCIxLjUuMFwiLFxuICBcImRlc2NyaXB0aW9uXCI6IFwiR2FpbiByZWFsLXRpbWUgdHJhZmZpYyBpbnNpZ2h0cyB3aXRoIFZlcmNlbCBXZWIgQW5hbHl0aWNzXCIsXG4gIFwia2V5d29yZHNcIjogW1xuICAgIFwiYW5hbHl0aWNzXCIsXG4gICAgXCJ2ZXJjZWxcIlxuICBdLFxuICBcInJlcG9zaXRvcnlcIjoge1xuICAgIFwidXJsXCI6IFwiZ2l0aHViOnZlcmNlbC9hbmFseXRpY3NcIixcbiAgICBcImRpcmVjdG9yeVwiOiBcInBhY2thZ2VzL3dlYlwiXG4gIH0sXG4gIFwibGljZW5zZVwiOiBcIk1QTC0yLjBcIixcbiAgXCJleHBvcnRzXCI6IHtcbiAgICBcIi4vcGFja2FnZS5qc29uXCI6IFwiLi9wYWNrYWdlLmpzb25cIixcbiAgICBcIi5cIjoge1xuICAgICAgXCJicm93c2VyXCI6IFwiLi9kaXN0L2luZGV4Lm1qc1wiLFxuICAgICAgXCJpbXBvcnRcIjogXCIuL2Rpc3QvaW5kZXgubWpzXCIsXG4gICAgICBcInJlcXVpcmVcIjogXCIuL2Rpc3QvaW5kZXguanNcIlxuICAgIH0sXG4gICAgXCIuL2FzdHJvXCI6IHtcbiAgICAgIFwiaW1wb3J0XCI6IFwiLi9kaXN0L2FzdHJvL2NvbXBvbmVudC50c1wiXG4gICAgfSxcbiAgICBcIi4vbmV4dFwiOiB7XG4gICAgICBcImJyb3dzZXJcIjogXCIuL2Rpc3QvbmV4dC9pbmRleC5tanNcIixcbiAgICAgIFwiaW1wb3J0XCI6IFwiLi9kaXN0L25leHQvaW5kZXgubWpzXCIsXG4gICAgICBcInJlcXVpcmVcIjogXCIuL2Rpc3QvbmV4dC9pbmRleC5qc1wiXG4gICAgfSxcbiAgICBcIi4vbnV4dFwiOiB7XG4gICAgICBcImJyb3dzZXJcIjogXCIuL2Rpc3QvbnV4dC9pbmRleC5tanNcIixcbiAgICAgIFwiaW1wb3J0XCI6IFwiLi9kaXN0L251eHQvaW5kZXgubWpzXCIsXG4gICAgICBcInJlcXVpcmVcIjogXCIuL2Rpc3QvbnV4dC9pbmRleC5qc1wiXG4gICAgfSxcbiAgICBcIi4vcmVhY3RcIjoge1xuICAgICAgXCJicm93c2VyXCI6IFwiLi9kaXN0L3JlYWN0L2luZGV4Lm1qc1wiLFxuICAgICAgXCJpbXBvcnRcIjogXCIuL2Rpc3QvcmVhY3QvaW5kZXgubWpzXCIsXG4gICAgICBcInJlcXVpcmVcIjogXCIuL2Rpc3QvcmVhY3QvaW5kZXguanNcIlxuICAgIH0sXG4gICAgXCIuL3JlbWl4XCI6IHtcbiAgICAgIFwiYnJvd3NlclwiOiBcIi4vZGlzdC9yZW1peC9pbmRleC5tanNcIixcbiAgICAgIFwiaW1wb3J0XCI6IFwiLi9kaXN0L3JlbWl4L2luZGV4Lm1qc1wiLFxuICAgICAgXCJyZXF1aXJlXCI6IFwiLi9kaXN0L3JlbWl4L2luZGV4LmpzXCJcbiAgICB9LFxuICAgIFwiLi9zZXJ2ZXJcIjoge1xuICAgICAgXCJub2RlXCI6IFwiLi9kaXN0L3NlcnZlci9pbmRleC5tanNcIixcbiAgICAgIFwiZWRnZS1saWdodFwiOiBcIi4vZGlzdC9zZXJ2ZXIvaW5kZXgubWpzXCIsXG4gICAgICBcImltcG9ydFwiOiBcIi4vZGlzdC9zZXJ2ZXIvaW5kZXgubWpzXCIsXG4gICAgICBcInJlcXVpcmVcIjogXCIuL2Rpc3Qvc2VydmVyL2luZGV4LmpzXCIsXG4gICAgICBcImRlZmF1bHRcIjogXCIuL2Rpc3Qvc2VydmVyL2luZGV4LmpzXCJcbiAgICB9LFxuICAgIFwiLi9zdmVsdGVraXRcIjoge1xuICAgICAgXCJzdmVsdGVcIjogXCIuL2Rpc3Qvc3ZlbHRla2l0L2luZGV4Lm1qc1wiLFxuICAgICAgXCJ0eXBlc1wiOiBcIi4vZGlzdC9zdmVsdGVraXQvaW5kZXguZC50c1wiXG4gICAgfSxcbiAgICBcIi4vdnVlXCI6IHtcbiAgICAgIFwiYnJvd3NlclwiOiBcIi4vZGlzdC92dWUvaW5kZXgubWpzXCIsXG4gICAgICBcImltcG9ydFwiOiBcIi4vZGlzdC92dWUvaW5kZXgubWpzXCIsXG4gICAgICBcInJlcXVpcmVcIjogXCIuL2Rpc3QvdnVlL2luZGV4LmpzXCJcbiAgICB9XG4gIH0sXG4gIFwibWFpblwiOiBcIi4vZGlzdC9pbmRleC5tanNcIixcbiAgXCJ0eXBlc1wiOiBcIi4vZGlzdC9pbmRleC5kLnRzXCIsXG4gIFwidHlwZXNWZXJzaW9uc1wiOiB7XG4gICAgXCIqXCI6IHtcbiAgICAgIFwiKlwiOiBbXG4gICAgICAgIFwiZGlzdC9pbmRleC5kLnRzXCJcbiAgICAgIF0sXG4gICAgICBcIm5leHRcIjogW1xuICAgICAgICBcImRpc3QvbmV4dC9pbmRleC5kLnRzXCJcbiAgICAgIF0sXG4gICAgICBcIm51eHRcIjogW1xuICAgICAgICBcImRpc3QvbnV4dC9pbmRleC5kLnRzXCJcbiAgICAgIF0sXG4gICAgICBcInJlYWN0XCI6IFtcbiAgICAgICAgXCJkaXN0L3JlYWN0L2luZGV4LmQudHNcIlxuICAgICAgXSxcbiAgICAgIFwicmVtaXhcIjogW1xuICAgICAgICBcImRpc3QvcmVtaXgvaW5kZXguZC50c1wiXG4gICAgICBdLFxuICAgICAgXCJzZXJ2ZXJcIjogW1xuICAgICAgICBcImRpc3Qvc2VydmVyL2luZGV4LmQudHNcIlxuICAgICAgXSxcbiAgICAgIFwic3ZlbHRla2l0XCI6IFtcbiAgICAgICAgXCJkaXN0L3N2ZWx0ZWtpdC9pbmRleC5kLnRzXCJcbiAgICAgIF0sXG4gICAgICBcInZ1ZVwiOiBbXG4gICAgICAgIFwiZGlzdC92dWUvaW5kZXguZC50c1wiXG4gICAgICBdXG4gICAgfVxuICB9LFxuICBcInNjcmlwdHNcIjoge1xuICAgIFwiYnVpbGRcIjogXCJ0c3VwICYmIHBucG0gY29weS1hc3Ryb1wiLFxuICAgIFwiY29weS1hc3Ryb1wiOiBcImNwIC1SIHNyYy9hc3RybyBkaXN0L1wiLFxuICAgIFwiZGV2XCI6IFwicG5wbSBjb3B5LWFzdHJvICYmIHRzdXAgLS13YXRjaFwiLFxuICAgIFwibGludFwiOiBcImVzbGludCAuXCIsXG4gICAgXCJsaW50LWZpeFwiOiBcImVzbGludCAuIC0tZml4XCIsXG4gICAgXCJ0ZXN0XCI6IFwidml0ZXN0XCIsXG4gICAgXCJ0eXBlLWNoZWNrXCI6IFwidHNjIC0tbm9FbWl0XCJcbiAgfSxcbiAgXCJlc2xpbnRDb25maWdcIjoge1xuICAgIFwiZXh0ZW5kc1wiOiBbXG4gICAgICBcIkB2ZXJjZWwvZXNsaW50LWNvbmZpZ1wiXG4gICAgXSxcbiAgICBcInJ1bGVzXCI6IHtcbiAgICAgIFwidHNkb2Mvc3ludGF4XCI6IFwib2ZmXCJcbiAgICB9LFxuICAgIFwiaWdub3JlUGF0dGVybnNcIjogW1xuICAgICAgXCJqZXN0LnNldHVwLnRzXCJcbiAgICBdXG4gIH0sXG4gIFwiZGV2RGVwZW5kZW5jaWVzXCI6IHtcbiAgICBcIkBzd2MvY29yZVwiOiBcIl4xLjkuMlwiLFxuICAgIFwiQHRlc3RpbmctbGlicmFyeS9qZXN0LWRvbVwiOiBcIl42LjYuM1wiLFxuICAgIFwiQHRlc3RpbmctbGlicmFyeS9yZWFjdFwiOiBcIl4xNi4wLjFcIixcbiAgICBcIkB0eXBlcy9ub2RlXCI6IFwiXjIyLjkuMFwiLFxuICAgIFwiQHR5cGVzL3JlYWN0XCI6IFwiXjE4LjMuMTJcIixcbiAgICBcIkB2ZXJjZWwvZXNsaW50LWNvbmZpZ1wiOiBcIndvcmtzcGFjZTowLjAuMFwiLFxuICAgIFwic2VydmVyLW9ubHlcIjogXCJeMC4wLjFcIixcbiAgICBcInN2ZWx0ZVwiOiBcIl41LjEuMTBcIixcbiAgICBcInRzdXBcIjogXCI4LjMuNVwiLFxuICAgIFwidml0ZXN0XCI6IFwiXjIuMS41XCIsXG4gICAgXCJ2dWVcIjogXCJeMy41LjEyXCIsXG4gICAgXCJ2dWUtcm91dGVyXCI6IFwiXjQuNC41XCJcbiAgfSxcbiAgXCJwZWVyRGVwZW5kZW5jaWVzXCI6IHtcbiAgICBcIkByZW1peC1ydW4vcmVhY3RcIjogXCJeMlwiLFxuICAgIFwiQHN2ZWx0ZWpzL2tpdFwiOiBcIl4xIHx8IF4yXCIsXG4gICAgXCJuZXh0XCI6IFwiPj0gMTNcIixcbiAgICBcInJlYWN0XCI6IFwiXjE4IHx8IF4xOSB8fCBeMTkuMC4wLXJjXCIsXG4gICAgXCJzdmVsdGVcIjogXCI+PSA0XCIsXG4gICAgXCJ2dWVcIjogXCJeM1wiLFxuICAgIFwidnVlLXJvdXRlclwiOiBcIl40XCJcbiAgfSxcbiAgXCJwZWVyRGVwZW5kZW5jaWVzTWV0YVwiOiB7XG4gICAgXCJAcmVtaXgtcnVuL3JlYWN0XCI6IHtcbiAgICAgIFwib3B0aW9uYWxcIjogdHJ1ZVxuICAgIH0sXG4gICAgXCJAc3ZlbHRlanMva2l0XCI6IHtcbiAgICAgIFwib3B0aW9uYWxcIjogdHJ1ZVxuICAgIH0sXG4gICAgXCJuZXh0XCI6IHtcbiAgICAgIFwib3B0aW9uYWxcIjogdHJ1ZVxuICAgIH0sXG4gICAgXCJyZWFjdFwiOiB7XG4gICAgICBcIm9wdGlvbmFsXCI6IHRydWVcbiAgICB9LFxuICAgIFwic3ZlbHRlXCI6IHtcbiAgICAgIFwib3B0aW9uYWxcIjogdHJ1ZVxuICAgIH0sXG4gICAgXCJ2dWVcIjoge1xuICAgICAgXCJvcHRpb25hbFwiOiB0cnVlXG4gICAgfSxcbiAgICBcInZ1ZS1yb3V0ZXJcIjoge1xuICAgICAgXCJvcHRpb25hbFwiOiB0cnVlXG4gICAgfVxuICB9XG59XG4iLCJleHBvcnQgY29uc3QgaW5pdFF1ZXVlID0gKCk6IHZvaWQgPT4ge1xuICAvLyBpbml0aWFsaXplIHZhIHVudGlsIHNjcmlwdCBpcyBsb2FkZWRcbiAgaWYgKHdpbmRvdy52YSkgcmV0dXJuO1xuXG4gIHdpbmRvdy52YSA9IGZ1bmN0aW9uIGEoLi4ucGFyYW1zKTogdm9pZCB7XG4gICAgKHdpbmRvdy52YXEgPSB3aW5kb3cudmFxIHx8IFtdKS5wdXNoKHBhcmFtcyk7XG4gIH07XG59O1xuIiwiaW1wb3J0IHR5cGUgeyBBbGxvd2VkUHJvcGVydHlWYWx1ZXMsIEFuYWx5dGljc1Byb3BzLCBNb2RlIH0gZnJvbSAnLi90eXBlcyc7XG5cbmV4cG9ydCBmdW5jdGlvbiBpc0Jyb3dzZXIoKTogYm9vbGVhbiB7XG4gIHJldHVybiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJztcbn1cblxuZnVuY3Rpb24gZGV0ZWN0RW52aXJvbm1lbnQoKTogJ2RldmVsb3BtZW50JyB8ICdwcm9kdWN0aW9uJyB7XG4gIHRyeSB7XG4gICAgY29uc3QgZW52ID0gcHJvY2Vzcy5lbnYuTk9ERV9FTlY7XG4gICAgaWYgKGVudiA9PT0gJ2RldmVsb3BtZW50JyB8fCBlbnYgPT09ICd0ZXN0Jykge1xuICAgICAgcmV0dXJuICdkZXZlbG9wbWVudCc7XG4gICAgfVxuICB9IGNhdGNoIChlKSB7XG4gICAgLy8gZG8gbm90aGluZywgdGhpcyBpcyBva2F5XG4gIH1cbiAgcmV0dXJuICdwcm9kdWN0aW9uJztcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHNldE1vZGUobW9kZTogTW9kZSA9ICdhdXRvJyk6IHZvaWQge1xuICBpZiAobW9kZSA9PT0gJ2F1dG8nKSB7XG4gICAgd2luZG93LnZhbSA9IGRldGVjdEVudmlyb25tZW50KCk7XG4gICAgcmV0dXJuO1xuICB9XG5cbiAgd2luZG93LnZhbSA9IG1vZGU7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZXRNb2RlKCk6IE1vZGUge1xuICBjb25zdCBtb2RlID0gaXNCcm93c2VyKCkgPyB3aW5kb3cudmFtIDogZGV0ZWN0RW52aXJvbm1lbnQoKTtcbiAgcmV0dXJuIG1vZGUgfHwgJ3Byb2R1Y3Rpb24nO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gaXNQcm9kdWN0aW9uKCk6IGJvb2xlYW4ge1xuICByZXR1cm4gZ2V0TW9kZSgpID09PSAncHJvZHVjdGlvbic7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBpc0RldmVsb3BtZW50KCk6IGJvb2xlYW4ge1xuICByZXR1cm4gZ2V0TW9kZSgpID09PSAnZGV2ZWxvcG1lbnQnO1xufVxuXG5mdW5jdGlvbiByZW1vdmVLZXkoXG4gIGtleTogc3RyaW5nLFxuICB7IFtrZXldOiBfLCAuLi5yZXN0IH1cbik6IFJlY29yZDxzdHJpbmcsIHVua25vd24+IHtcbiAgcmV0dXJuIHJlc3Q7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBwYXJzZVByb3BlcnRpZXMoXG4gIHByb3BlcnRpZXM6IFJlY29yZDxzdHJpbmcsIHVua25vd24+IHwgdW5kZWZpbmVkLFxuICBvcHRpb25zOiB7XG4gICAgc3RyaXA/OiBib29sZWFuO1xuICB9XG4pOiBFcnJvciB8IFJlY29yZDxzdHJpbmcsIEFsbG93ZWRQcm9wZXJ0eVZhbHVlcz4gfCB1bmRlZmluZWQge1xuICBpZiAoIXByb3BlcnRpZXMpIHJldHVybiB1bmRlZmluZWQ7XG4gIGxldCBwcm9wcyA9IHByb3BlcnRpZXM7XG4gIGNvbnN0IGVycm9yUHJvcGVydGllczogc3RyaW5nW10gPSBbXTtcbiAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMocHJvcGVydGllcykpIHtcbiAgICBpZiAodHlwZW9mIHZhbHVlID09PSAnb2JqZWN0JyAmJiB2YWx1ZSAhPT0gbnVsbCkge1xuICAgICAgaWYgKG9wdGlvbnMuc3RyaXApIHtcbiAgICAgICAgcHJvcHMgPSByZW1vdmVLZXkoa2V5LCBwcm9wcyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBlcnJvclByb3BlcnRpZXMucHVzaChrZXkpO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIGlmIChlcnJvclByb3BlcnRpZXMubGVuZ3RoID4gMCAmJiAhb3B0aW9ucy5zdHJpcCkge1xuICAgIHRocm93IEVycm9yKFxuICAgICAgYFRoZSBmb2xsb3dpbmcgcHJvcGVydGllcyBhcmUgbm90IHZhbGlkOiAke2Vycm9yUHJvcGVydGllcy5qb2luKFxuICAgICAgICAnLCAnXG4gICAgICApfS4gT25seSBzdHJpbmdzLCBudW1iZXJzLCBib29sZWFucywgYW5kIG51bGwgYXJlIGFsbG93ZWQuYFxuICAgICk7XG4gIH1cbiAgcmV0dXJuIHByb3BzIGFzIFJlY29yZDxzdHJpbmcsIEFsbG93ZWRQcm9wZXJ0eVZhbHVlcz47XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBjb21wdXRlUm91dGUoXG4gIHBhdGhuYW1lOiBzdHJpbmcgfCBudWxsLFxuICBwYXRoUGFyYW1zOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmcgfCBzdHJpbmdbXT4gfCBudWxsXG4pOiBzdHJpbmcgfCBudWxsIHtcbiAgaWYgKCFwYXRobmFtZSB8fCAhcGF0aFBhcmFtcykge1xuICAgIHJldHVybiBwYXRobmFtZTtcbiAgfVxuXG4gIGxldCByZXN1bHQgPSBwYXRobmFtZTtcbiAgdHJ5IHtcbiAgICBjb25zdCBlbnRyaWVzID0gT2JqZWN0LmVudHJpZXMocGF0aFBhcmFtcyk7XG4gICAgLy8gc2ltcGxlIGtleXMgbXVzdCBiZSBoYW5kbGVkIGZpcnN0XG4gICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgZW50cmllcykge1xuICAgICAgaWYgKCFBcnJheS5pc0FycmF5KHZhbHVlKSkge1xuICAgICAgICBjb25zdCBtYXRjaGVyID0gdHVyblZhbHVlVG9SZWdFeHAodmFsdWUpO1xuICAgICAgICBpZiAobWF0Y2hlci50ZXN0KHJlc3VsdCkpIHtcbiAgICAgICAgICByZXN1bHQgPSByZXN1bHQucmVwbGFjZShtYXRjaGVyLCBgL1ske2tleX1dYCk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gICAgLy8gYXJyYXkgdmFsdWVzIG5leHRcbiAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBlbnRyaWVzKSB7XG4gICAgICBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHtcbiAgICAgICAgY29uc3QgbWF0Y2hlciA9IHR1cm5WYWx1ZVRvUmVnRXhwKHZhbHVlLmpvaW4oJy8nKSk7XG4gICAgICAgIGlmIChtYXRjaGVyLnRlc3QocmVzdWx0KSkge1xuICAgICAgICAgIHJlc3VsdCA9IHJlc3VsdC5yZXBsYWNlKG1hdGNoZXIsIGAvWy4uLiR7a2V5fV1gKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gcmVzdWx0O1xuICB9IGNhdGNoIChlKSB7XG4gICAgcmV0dXJuIHBhdGhuYW1lO1xuICB9XG59XG5cbmZ1bmN0aW9uIHR1cm5WYWx1ZVRvUmVnRXhwKHZhbHVlOiBzdHJpbmcpOiBSZWdFeHAge1xuICByZXR1cm4gbmV3IFJlZ0V4cChgLyR7ZXNjYXBlUmVnRXhwKHZhbHVlKX0oPz1bLz8jXXwkKWApO1xufVxuXG5mdW5jdGlvbiBlc2NhcGVSZWdFeHAoc3RyaW5nOiBzdHJpbmcpOiBzdHJpbmcge1xuICByZXR1cm4gc3RyaW5nLnJlcGxhY2UoL1suKis/XiR7fSgpfFtcXF1cXFxcXS9nLCAnXFxcXCQmJyk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZXRTY3JpcHRTcmMoXG4gIHByb3BzOiBBbmFseXRpY3NQcm9wcyAmIHsgYmFzZVBhdGg/OiBzdHJpbmcgfVxuKTogc3RyaW5nIHtcbiAgaWYgKHByb3BzLnNjcmlwdFNyYykge1xuICAgIHJldHVybiBwcm9wcy5zY3JpcHRTcmM7XG4gIH1cbiAgaWYgKGlzRGV2ZWxvcG1lbnQoKSkge1xuICAgIHJldHVybiAnaHR0cHM6Ly92YS52ZXJjZWwtc2NyaXB0cy5jb20vdjEvc2NyaXB0LmRlYnVnLmpzJztcbiAgfVxuICBpZiAocHJvcHMuYmFzZVBhdGgpIHtcbiAgICByZXR1cm4gYCR7cHJvcHMuYmFzZVBhdGh9L2luc2lnaHRzL3NjcmlwdC5qc2A7XG4gIH1cbiAgcmV0dXJuICcvX3ZlcmNlbC9pbnNpZ2h0cy9zY3JpcHQuanMnO1xufVxuIiwiaW1wb3J0IHsgbmFtZSBhcyBwYWNrYWdlTmFtZSwgdmVyc2lvbiB9IGZyb20gJy4uL3BhY2thZ2UuanNvbic7XG5pbXBvcnQgeyBpbml0UXVldWUgfSBmcm9tICcuL3F1ZXVlJztcbmltcG9ydCB0eXBlIHtcbiAgQWxsb3dlZFByb3BlcnR5VmFsdWVzLFxuICBBbmFseXRpY3NQcm9wcyxcbiAgRmxhZ3NEYXRhSW5wdXQsXG4gIEJlZm9yZVNlbmQsXG4gIEJlZm9yZVNlbmRFdmVudCxcbn0gZnJvbSAnLi90eXBlcyc7XG5pbXBvcnQge1xuICBpc0Jyb3dzZXIsXG4gIHBhcnNlUHJvcGVydGllcyxcbiAgc2V0TW9kZSxcbiAgaXNEZXZlbG9wbWVudCxcbiAgaXNQcm9kdWN0aW9uLFxuICBjb21wdXRlUm91dGUsXG4gIGdldFNjcmlwdFNyYyxcbn0gZnJvbSAnLi91dGlscyc7XG5cbi8qKlxuICogSW5qZWN0cyB0aGUgVmVyY2VsIFdlYiBBbmFseXRpY3Mgc2NyaXB0IGludG8gdGhlIHBhZ2UgaGVhZCBhbmQgc3RhcnRzIHRyYWNraW5nIHBhZ2Ugdmlld3MuIFJlYWQgbW9yZSBpbiBvdXIgW2RvY3VtZW50YXRpb25dKGh0dHBzOi8vdmVyY2VsLmNvbS9kb2NzL2NvbmNlcHRzL2FuYWx5dGljcy9wYWNrYWdlKS5cbiAqIEBwYXJhbSBbcHJvcHNdIC0gQW5hbHl0aWNzIG9wdGlvbnMuXG4gKiBAcGFyYW0gW3Byb3BzLm1vZGVdIC0gVGhlIG1vZGUgdG8gdXNlIGZvciB0aGUgYW5hbHl0aWNzIHNjcmlwdC4gRGVmYXVsdHMgdG8gYGF1dG9gLlxuICogIC0gYGF1dG9gIC0gQXV0b21hdGljYWxseSBkZXRlY3QgdGhlIGVudmlyb25tZW50LiAgVXNlcyBgcHJvZHVjdGlvbmAgaWYgdGhlIGVudmlyb25tZW50IGNhbm5vdCBiZSBkZXRlcm1pbmVkLlxuICogIC0gYHByb2R1Y3Rpb25gIC0gQWx3YXlzIHVzZSB0aGUgcHJvZHVjdGlvbiBzY3JpcHQuIChTZW5kcyBldmVudHMgdG8gdGhlIHNlcnZlcilcbiAqICAtIGBkZXZlbG9wbWVudGAgLSBBbHdheXMgdXNlIHRoZSBkZXZlbG9wbWVudCBzY3JpcHQuIChMb2dzIGV2ZW50cyB0byB0aGUgY29uc29sZSlcbiAqIEBwYXJhbSBbcHJvcHMuZGVidWddIC0gV2hldGhlciB0byBlbmFibGUgZGVidWcgbG9nZ2luZyBpbiBkZXZlbG9wbWVudC4gRGVmYXVsdHMgdG8gYHRydWVgLlxuICogQHBhcmFtIFtwcm9wcy5iZWZvcmVTZW5kXSAtIEEgbWlkZGxld2FyZSBmdW5jdGlvbiB0byBtb2RpZnkgZXZlbnRzIGJlZm9yZSB0aGV5IGFyZSBzZW50LiBTaG91bGQgcmV0dXJuIHRoZSBldmVudCBvYmplY3Qgb3IgYG51bGxgIHRvIGNhbmNlbCB0aGUgZXZlbnQuXG4gKiBAcGFyYW0gW3Byb3BzLmRzbl0gLSBUaGUgRFNOIG9mIHRoZSBwcm9qZWN0IHRvIHNlbmQgZXZlbnRzIHRvLiBPbmx5IHJlcXVpcmVkIHdoZW4gc2VsZi1ob3N0aW5nLlxuICogQHBhcmFtIFtwcm9wcy5kaXNhYmxlQXV0b1RyYWNrXSAtIFdoZXRoZXIgdGhlIGluamVjdGVkIHNjcmlwdCBzaG91bGQgdHJhY2sgcGFnZSB2aWV3cyBmcm9tIHB1c2hTdGF0ZSBldmVudHMuIERpc2FibGUgaWYgcm91dGUgaXMgdXBkYXRlZCBhZnRlciBwdXNoU3RhdGUsIGEgbWFudWFsbHkgY2FsbCBwYWdlIHBhZ2V2aWV3KCkuXG4gKi9cbmZ1bmN0aW9uIGluamVjdChcbiAgcHJvcHM6IEFuYWx5dGljc1Byb3BzICYge1xuICAgIGZyYW1ld29yaz86IHN0cmluZztcbiAgICBkaXNhYmxlQXV0b1RyYWNrPzogYm9vbGVhbjtcbiAgICBiYXNlUGF0aD86IHN0cmluZztcbiAgfSA9IHtcbiAgICBkZWJ1ZzogdHJ1ZSxcbiAgfVxuKTogdm9pZCB7XG4gIGlmICghaXNCcm93c2VyKCkpIHJldHVybjtcblxuICBzZXRNb2RlKHByb3BzLm1vZGUpO1xuXG4gIGluaXRRdWV1ZSgpO1xuXG4gIGlmIChwcm9wcy5iZWZvcmVTZW5kKSB7XG4gICAgd2luZG93LnZhPy4oJ2JlZm9yZVNlbmQnLCBwcm9wcy5iZWZvcmVTZW5kKTtcbiAgfVxuXG4gIGNvbnN0IHNyYyA9IGdldFNjcmlwdFNyYyhwcm9wcyk7XG5cbiAgaWYgKGRvY3VtZW50LmhlYWQucXVlcnlTZWxlY3Rvcihgc2NyaXB0W3NyYyo9XCIke3NyY31cIl1gKSkgcmV0dXJuO1xuXG4gIGNvbnN0IHNjcmlwdCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ3NjcmlwdCcpO1xuICBzY3JpcHQuc3JjID0gc3JjO1xuICBzY3JpcHQuZGVmZXIgPSB0cnVlO1xuICBzY3JpcHQuZGF0YXNldC5zZGtuID1cbiAgICBwYWNrYWdlTmFtZSArIChwcm9wcy5mcmFtZXdvcmsgPyBgLyR7cHJvcHMuZnJhbWV3b3JrfWAgOiAnJyk7XG4gIHNjcmlwdC5kYXRhc2V0LnNka3YgPSB2ZXJzaW9uO1xuXG4gIGlmIChwcm9wcy5kaXNhYmxlQXV0b1RyYWNrKSB7XG4gICAgc2NyaXB0LmRhdGFzZXQuZGlzYWJsZUF1dG9UcmFjayA9ICcxJztcbiAgfVxuICBpZiAocHJvcHMuZW5kcG9pbnQpIHtcbiAgICBzY3JpcHQuZGF0YXNldC5lbmRwb2ludCA9IHByb3BzLmVuZHBvaW50O1xuICB9IGVsc2UgaWYgKHByb3BzLmJhc2VQYXRoKSB7XG4gICAgc2NyaXB0LmRhdGFzZXQuZW5kcG9pbnQgPSBgJHtwcm9wcy5iYXNlUGF0aH0vaW5zaWdodHNgO1xuICB9XG4gIGlmIChwcm9wcy5kc24pIHtcbiAgICBzY3JpcHQuZGF0YXNldC5kc24gPSBwcm9wcy5kc247XG4gIH1cblxuICBzY3JpcHQub25lcnJvciA9ICgpOiB2b2lkID0+IHtcbiAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBpc0RldmVsb3BtZW50KClcbiAgICAgID8gJ1BsZWFzZSBjaGVjayBpZiBhbnkgYWQgYmxvY2tlcnMgYXJlIGVuYWJsZWQgYW5kIHRyeSBhZ2Fpbi4nXG4gICAgICA6ICdCZSBzdXJlIHRvIGVuYWJsZSBXZWIgQW5hbHl0aWNzIGZvciB5b3VyIHByb2plY3QgYW5kIGRlcGxveSBhZ2Fpbi4gU2VlIGh0dHBzOi8vdmVyY2VsLmNvbS9kb2NzL2FuYWx5dGljcy9xdWlja3N0YXJ0IGZvciBtb3JlIGluZm9ybWF0aW9uLic7XG5cbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tY29uc29sZSAtLSBMb2dnaW5nIHRvIGNvbnNvbGUgaXMgaW50ZW50aW9uYWxcbiAgICBjb25zb2xlLmxvZyhcbiAgICAgIGBbVmVyY2VsIFdlYiBBbmFseXRpY3NdIEZhaWxlZCB0byBsb2FkIHNjcmlwdCBmcm9tICR7c3JjfS4gJHtlcnJvck1lc3NhZ2V9YFxuICAgICk7XG4gIH07XG5cbiAgaWYgKGlzRGV2ZWxvcG1lbnQoKSAmJiBwcm9wcy5kZWJ1ZyA9PT0gZmFsc2UpIHtcbiAgICBzY3JpcHQuZGF0YXNldC5kZWJ1ZyA9ICdmYWxzZSc7XG4gIH1cblxuICBkb2N1bWVudC5oZWFkLmFwcGVuZENoaWxkKHNjcmlwdCk7XG59XG5cbi8qKlxuICogVHJhY2tzIGEgY3VzdG9tIGV2ZW50LiBQbGVhc2UgcmVmZXIgdG8gdGhlIFtkb2N1bWVudGF0aW9uXShodHRwczovL3ZlcmNlbC5jb20vZG9jcy9jb25jZXB0cy9hbmFseXRpY3MvY3VzdG9tLWV2ZW50cykgZm9yIG1vcmUgaW5mb3JtYXRpb24gb24gY3VzdG9tIGV2ZW50cy5cbiAqIEBwYXJhbSBuYW1lIC0gVGhlIG5hbWUgb2YgdGhlIGV2ZW50LlxuICogKiBFeGFtcGxlczogYFB1cmNoYXNlYCwgYENsaWNrIEJ1dHRvbmAsIG9yIGBQbGF5IFZpZGVvYC5cbiAqIEBwYXJhbSBbcHJvcGVydGllc10gLSBBZGRpdGlvbmFsIHByb3BlcnRpZXMgb2YgdGhlIGV2ZW50LiBOZXN0ZWQgb2JqZWN0cyBhcmUgbm90IHN1cHBvcnRlZC4gQWxsb3dlZCB2YWx1ZXMgYXJlIGBzdHJpbmdgLCBgbnVtYmVyYCwgYGJvb2xlYW5gLCBhbmQgYG51bGxgLlxuICovXG5mdW5jdGlvbiB0cmFjayhcbiAgbmFtZTogc3RyaW5nLFxuICBwcm9wZXJ0aWVzPzogUmVjb3JkPHN0cmluZywgQWxsb3dlZFByb3BlcnR5VmFsdWVzPixcbiAgb3B0aW9ucz86IHtcbiAgICBmbGFncz86IEZsYWdzRGF0YUlucHV0O1xuICB9XG4pOiB2b2lkIHtcbiAgaWYgKCFpc0Jyb3dzZXIoKSkge1xuICAgIGNvbnN0IG1zZyA9XG4gICAgICAnW1ZlcmNlbCBXZWIgQW5hbHl0aWNzXSBQbGVhc2UgaW1wb3J0IGB0cmFja2AgZnJvbSBgQHZlcmNlbC9hbmFseXRpY3Mvc2VydmVyYCB3aGVuIHVzaW5nIHRoaXMgZnVuY3Rpb24gaW4gYSBzZXJ2ZXIgZW52aXJvbm1lbnQnO1xuXG4gICAgaWYgKGlzUHJvZHVjdGlvbigpKSB7XG4gICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tY29uc29sZSAtLSBTaG93IHdhcm5pbmcgaW4gcHJvZHVjdGlvblxuICAgICAgY29uc29sZS53YXJuKG1zZyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihtc2cpO1xuICAgIH1cblxuICAgIHJldHVybjtcbiAgfVxuXG4gIGlmICghcHJvcGVydGllcykge1xuICAgIHdpbmRvdy52YT8uKCdldmVudCcsIHsgbmFtZSwgb3B0aW9ucyB9KTtcbiAgICByZXR1cm47XG4gIH1cblxuICB0cnkge1xuICAgIGNvbnN0IHByb3BzID0gcGFyc2VQcm9wZXJ0aWVzKHByb3BlcnRpZXMsIHtcbiAgICAgIHN0cmlwOiBpc1Byb2R1Y3Rpb24oKSxcbiAgICB9KTtcblxuICAgIHdpbmRvdy52YT8uKCdldmVudCcsIHtcbiAgICAgIG5hbWUsXG4gICAgICBkYXRhOiBwcm9wcyxcbiAgICAgIG9wdGlvbnMsXG4gICAgfSk7XG4gIH0gY2F0Y2ggKGVycikge1xuICAgIGlmIChlcnIgaW5zdGFuY2VvZiBFcnJvciAmJiBpc0RldmVsb3BtZW50KCkpIHtcbiAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby1jb25zb2xlIC0tIExvZ2dpbmcgdG8gY29uc29sZSBpcyBpbnRlbnRpb25hbFxuICAgICAgY29uc29sZS5lcnJvcihlcnIpO1xuICAgIH1cbiAgfVxufVxuXG5mdW5jdGlvbiBwYWdldmlldyh7XG4gIHJvdXRlLFxuICBwYXRoLFxufToge1xuICByb3V0ZT86IHN0cmluZyB8IG51bGw7XG4gIHBhdGg/OiBzdHJpbmc7XG59KTogdm9pZCB7XG4gIHdpbmRvdy52YT8uKCdwYWdldmlldycsIHsgcm91dGUsIHBhdGggfSk7XG59XG5cbmV4cG9ydCB7IGluamVjdCwgdHJhY2ssIHBhZ2V2aWV3LCBjb21wdXRlUm91dGUgfTtcbmV4cG9ydCB0eXBlIHsgQW5hbHl0aWNzUHJvcHMsIEJlZm9yZVNlbmQsIEJlZm9yZVNlbmRFdmVudCB9O1xuXG4vLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgaW1wb3J0L25vLWRlZmF1bHQtZXhwb3J0IC0tIERlZmF1bHQgZXhwb3J0IGlzIGludGVudGlvbmFsXG5leHBvcnQgZGVmYXVsdCB7XG4gIGluamVjdCxcbiAgdHJhY2ssXG4gIGNvbXB1dGVSb3V0ZSxcbn07XG4iLCJleHBvcnQgZnVuY3Rpb24gZ2V0QmFzZVBhdGgoKTogc3RyaW5nIHwgdW5kZWZpbmVkIHtcbiAgLy8gISEgaW1wb3J0YW50ICEhXG4gIC8vIGRvIG5vdCBhY2Nlc3MgZW52IHZhcmlhYmxlcyB1c2luZyBwcm9jZXNzLmVudlt2YXJuYW1lXVxuICAvLyBzb21lIGJ1bmRsZXMgd29uJ3QgcmVwbGFjZSB0aGUgdmFsdWUgYXQgYnVpbGQgdGltZS5cbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9wcmVmZXItb3B0aW9uYWwtY2hhaW4gLS0gd2UgY2FuJ3QgdXNlIG9wdGlvbm5hbCBoZXJlLCBpdCdsbCBicmVhayBpZiBwcm9jZXNzIGRvZXMgbm90IGV4aXN0LlxuICBpZiAodHlwZW9mIHByb2Nlc3MgPT09ICd1bmRlZmluZWQnIHx8IHR5cGVvZiBwcm9jZXNzLmVudiA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICByZXR1cm4gdW5kZWZpbmVkO1xuICB9XG4gIHJldHVybiBwcm9jZXNzLmVudi5SRUFDVF9BUFBfVkVSQ0VMX09CU0VSVkFCSUxJVFlfQkFTRVBBVEg7XG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwibmFtZSIsInZlcnNpb24iLCJpbml0UXVldWUiLCJ3aW5kb3ciLCJ2YSIsImEiLCJwYXJhbXMiLCJ2YXEiLCJwdXNoIiwiaXNCcm93c2VyIiwiZGV0ZWN0RW52aXJvbm1lbnQiLCJlbnYiLCJlIiwic2V0TW9kZSIsIm1vZGUiLCJ2YW0iLCJnZXRNb2RlIiwiaXNQcm9kdWN0aW9uIiwiaXNEZXZlbG9wbWVudCIsInJlbW92ZUtleSIsImtleSIsIl8iLCJyZXN0IiwicGFyc2VQcm9wZXJ0aWVzIiwicHJvcGVydGllcyIsIm9wdGlvbnMiLCJwcm9wcyIsImVycm9yUHJvcGVydGllcyIsInZhbHVlIiwiT2JqZWN0IiwiZW50cmllcyIsInN0cmlwIiwibGVuZ3RoIiwiRXJyb3IiLCJqb2luIiwiZ2V0U2NyaXB0U3JjIiwic2NyaXB0U3JjIiwiYmFzZVBhdGgiLCJpbmplY3QiLCJkZWJ1ZyIsIl9hIiwiYmVmb3JlU2VuZCIsImNhbGwiLCJzcmMiLCJkb2N1bWVudCIsImhlYWQiLCJxdWVyeVNlbGVjdG9yIiwic2NyaXB0IiwiY3JlYXRlRWxlbWVudCIsImRlZmVyIiwiZGF0YXNldCIsInNka24iLCJmcmFtZXdvcmsiLCJzZGt2IiwiZGlzYWJsZUF1dG9UcmFjayIsImVuZHBvaW50IiwiZHNuIiwib25lcnJvciIsImVycm9yTWVzc2FnZSIsImNvbnNvbGUiLCJsb2ciLCJhcHBlbmRDaGlsZCIsInRyYWNrIiwiX2IiLCJtc2ciLCJ3YXJuIiwiZGF0YSIsImVyciIsImVycm9yIiwicGFnZXZpZXciLCJyb3V0ZSIsInBhdGgiLCJnZXRCYXNlUGF0aCIsInByb2Nlc3MiLCJSRUFDVF9BUFBfVkVSQ0VMX09CU0VSVkFCSUxJVFlfQkFTRVBBVEgiLCJBbmFseXRpY3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@vercel/analytics/dist/react/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@vercel/speed-insights/dist/next/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@vercel/speed-insights/dist/next/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpeedInsights: () => (/* binding */ SpeedInsights2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var next_navigation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation.js */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ SpeedInsights auto */ // src/nextjs/index.tsx\n\n// src/react/index.tsx\n\n// package.json\nvar name = \"@vercel/speed-insights\";\nvar version = \"1.2.0\";\n// src/queue.ts\nvar initQueue = ()=>{\n    if (window.si) return;\n    window.si = function a(...params) {\n        (window.siq = window.siq || []).push(params);\n    };\n};\n// src/utils.ts\nfunction isBrowser() {\n    return \"undefined\" !== \"undefined\";\n}\nfunction detectEnvironment() {\n    try {\n        const env = \"development\";\n        if (env === \"development\" || env === \"test\") {\n            return \"development\";\n        }\n    } catch (e) {}\n    return \"production\";\n}\nfunction isDevelopment() {\n    return detectEnvironment() === \"development\";\n}\nfunction computeRoute(pathname, pathParams) {\n    if (!pathname || !pathParams) {\n        return pathname;\n    }\n    let result = pathname;\n    try {\n        const entries = Object.entries(pathParams);\n        for (const [key, value] of entries){\n            if (!Array.isArray(value)) {\n                const matcher = turnValueToRegExp(value);\n                if (matcher.test(result)) {\n                    result = result.replace(matcher, `/[${key}]`);\n                }\n            }\n        }\n        for (const [key, value] of entries){\n            if (Array.isArray(value)) {\n                const matcher = turnValueToRegExp(value.join(\"/\"));\n                if (matcher.test(result)) {\n                    result = result.replace(matcher, `/[...${key}]`);\n                }\n            }\n        }\n        return result;\n    } catch (e) {\n        return pathname;\n    }\n}\nfunction turnValueToRegExp(value) {\n    return new RegExp(`/${escapeRegExp(value)}(?=[/?#]|$)`);\n}\nfunction escapeRegExp(string) {\n    return string.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n}\nfunction getScriptSrc(props) {\n    if (props.scriptSrc) {\n        return props.scriptSrc;\n    }\n    if (isDevelopment()) {\n        return \"https://va.vercel-scripts.com/v1/speed-insights/script.debug.js\";\n    }\n    if (props.dsn) {\n        return \"https://va.vercel-scripts.com/v1/speed-insights/script.js\";\n    }\n    if (props.basePath) {\n        return `${props.basePath}/speed-insights/script.js`;\n    }\n    return \"/_vercel/speed-insights/script.js\";\n}\n// src/generic.ts\nfunction injectSpeedInsights(props = {}) {\n    var _a;\n    if (!isBrowser() || props.route === null) return null;\n    initQueue();\n    const src = getScriptSrc(props);\n    if (document.head.querySelector(`script[src*=\"${src}\"]`)) return null;\n    if (props.beforeSend) {\n        (_a = window.si) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n    }\n    const script = document.createElement(\"script\");\n    script.src = src;\n    script.defer = true;\n    script.dataset.sdkn = name + (props.framework ? `/${props.framework}` : \"\");\n    script.dataset.sdkv = version;\n    if (props.sampleRate) {\n        script.dataset.sampleRate = props.sampleRate.toString();\n    }\n    if (props.route) {\n        script.dataset.route = props.route;\n    }\n    if (props.endpoint) {\n        script.dataset.endpoint = props.endpoint;\n    } else if (props.basePath) {\n        script.dataset.endpoint = `${props.basePath}/speed-insights/vitals`;\n    }\n    if (props.dsn) {\n        script.dataset.dsn = props.dsn;\n    }\n    if (isDevelopment() && props.debug === false) {\n        script.dataset.debug = \"false\";\n    }\n    script.onerror = ()=>{\n        console.log(`[Vercel Speed Insights] Failed to load script from ${src}. Please check if any content blockers are enabled and try again.`);\n    };\n    document.head.appendChild(script);\n    return {\n        setRoute: (route)=>{\n            script.dataset.route = route ?? void 0;\n        }\n    };\n}\n// src/react/utils.ts\nfunction getBasePath() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/react/index.tsx\nfunction SpeedInsights(props) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        var _a;\n        if (props.beforeSend) {\n            (_a = window.si) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n        }\n    }, [\n        props.beforeSend\n    ]);\n    const setScriptRoute = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!setScriptRoute.current) {\n            const script = injectSpeedInsights({\n                framework: props.framework ?? \"react\",\n                basePath: props.basePath ?? getBasePath(),\n                ...props\n            });\n            if (script) {\n                setScriptRoute.current = script.setRoute;\n            }\n        } else if (props.route) {\n            setScriptRoute.current(props.route);\n        }\n    }, [\n        props.route\n    ]);\n    return null;\n}\n// src/nextjs/utils.ts\n\nvar useRoute = ()=>{\n    const params = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    const searchParams = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.useSearchParams)() || new URLSearchParams();\n    const path = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    if (!params) {\n        return null;\n    }\n    const finalParams = Object.keys(params).length ? params : Object.fromEntries(searchParams.entries());\n    return computeRoute(path, finalParams);\n};\nfunction getBasePath2() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.NEXT_PUBLIC_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/nextjs/index.tsx\nfunction SpeedInsightsComponent(props) {\n    const route = useRoute();\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(SpeedInsights, {\n        route,\n        ...props,\n        framework: \"next\",\n        basePath: getBasePath2()\n    });\n}\nfunction SpeedInsights2(props) {\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Suspense, {\n        fallback: null\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(SpeedInsightsComponent, {\n        ...props\n    }));\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@vercel/speed-insights/dist/next/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@vercel/analytics/dist/react/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@vercel/analytics/dist/react/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Analytics: () => (/* binding */ e0),
/* harmony export */   track: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Projects\MicroSaasWebApiRoot\MicroSaasWebApiUI\node_modules\@vercel\analytics\dist\react\index.mjs#Analytics`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Projects\MicroSaasWebApiRoot\MicroSaasWebApiUI\node_modules\@vercel\analytics\dist\react\index.mjs#track`);


/***/ }),

/***/ "(rsc)/./node_modules/@vercel/speed-insights/dist/next/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@vercel/speed-insights/dist/next/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SpeedInsights: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Projects\MicroSaasWebApiRoot\MicroSaasWebApiUI\node_modules\@vercel\speed-insights\dist\next\index.mjs#SpeedInsights`);


/***/ })

};
;