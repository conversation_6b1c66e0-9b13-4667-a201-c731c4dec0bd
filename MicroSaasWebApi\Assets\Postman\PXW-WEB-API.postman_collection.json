{"info": {"_postman_id": "e6f86a4e-59d9-4897-8035-2e21d69400ba", "name": "PXW-WEB-API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "22121195", "_collection_link": "https://grey-trinity-463850.postman.co/workspace/My-Workspace~eb59c6d4-fac1-406c-854f-363cdce43117/collection/22121195-e6f86a4e-59d9-4897-8035-2e21d69400ba?action=share&source=collection_link&creator=22121195"}, "item": [{"name": "Setup", "item": [{"name": "1. GetUserId", "event": [{"listen": "test", "script": {"exec": ["\r", "var jsonData = JSON.parse(responseBody);\r", "// Need to find the user where the email matches the env variable\r", "var email = pm.environment.get(\"userEmail\");\r", "\r", "var user = jsonData.users.find(user => user.email === email)\r", "\r", "pm.environment.set(\"userId\", user.id)"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Ocp-Apim-Subscription-Key", "value": "{{key}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/organization/{{organization}}/users", "host": ["{{baseUrl}}"], "path": ["organization", "{{organization}}", "users"]}}, "response": []}, {"name": "2. GetProfileId", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);\r", "// Need to find the user where the email matches the env variable\r", "\r", "var profileId = jsonData.profiles[0].id\r", "\r", "pm.environment.set(\"profileId\", profileId)"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Ocp-Apim-Subscription-Key", "value": "{{key}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/users/{{userId}}/profiles", "host": ["{{baseUrl}}"], "path": ["users", "{{userId}}", "profiles"]}}, "response": []}, {"name": "3. GetAccounts", "event": [{"listen": "test", "script": {"exec": ["\r", "var jsonData = JSON.parse(responseBody);\r", "\r", "var accounts = jsonData.accounts.map(a => a.accountCode)\r", "\r", "pm.environment.set(\"account_codes\", JSON.stringify(accounts))"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Ocp-Apim-Subscription-Key", "value": "{{key}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/profiles/{{profileId}}/accounts", "host": ["{{baseUrl}}"], "path": ["profiles", "{{profileId}}", "accounts"]}}, "response": []}]}, {"name": "Endpoints", "item": [{"name": "AccountProfile", "item": [{"name": "ProfilesList", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"ProfilesList\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Language\": \"01\",\r\n    \"Controller\": \"PXW/AccountProfile\",\r\n    \"Action\": \"ProfilesList\",\r\n    \"PathParameters\": {\r\n    },\r\n    \"PayloadParameters\": {\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "AccountsList", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"AccountsList\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Language\": \"01\",\r\n    \"Controller\": \"PXW/AccountProfile\",\r\n    \"Action\": \"AccountsList\",\r\n    \"PathParameters\": {\r\n        \"profile_id\": \"231c6928-2d20-4b81-9d40-5b1c760c3cb2\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "RetrieveMemberIdDetails", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"RetrieveMemberIdDetails\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Language\": \"01\",\r\n    \"Controller\": \"PXW/AccountProfile\",\r\n    \"Action\": \"RetrieveMemberIdDetails\",\r\n    \"PathParameters\": {\r\n    },\r\n    \"PayloadParameters\": {\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "UpdateMemberDetails", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"UpdateMemberIdDetails\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Language\": \"01\",\r\n    \"Controller\": \"PXW/AccountProfile\",\r\n    \"Action\": \"UpdateMemberIdDetails\",\r\n    \"PathParameters\": {\r\n    },\r\n    \"PayloadParameters\": {\r\n        \"pydioNotification\": true\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "GetAccountMemberData", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"GetAccountMemberData\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Language\": \"01\",\r\n    \"Controller\": \"PXW/AccountProfile\",\r\n    \"Action\": \"GetAccountMemberData\",\r\n    \"PathParameters\": {\r\n    },\r\n    \"PayloadParameters\": {\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "GetAccountMemberProfileIdData", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"GetAccountMemberProfileIdData\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Language\": \"01\",\r\n    \"Controller\": \"PXW/AccountProfile\",\r\n    \"Action\": \"GetAccountMemberProfileIdData\",\r\n    \"PathParameters\": {\r\n    },\r\n    \"PayloadParameters\": {\r\n        \"ProfileId\": \"{{profile_id}}\"   \r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}, {"name": "Sharepoint", "item": [{"name": "GetDocuments", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"GetDocuments\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Language\": \"01\",\r\n    \"Controller\": \"PXW/Sharepoint\",\r\n    \"Action\": \"GetDocuments\",\r\n    \"PathParameters\": {\r\n    },\r\n    \"PayloadParameters\": {\r\n        \"accountIds\":{{account_codes}}\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "DownloadDocument", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"DownloadDocument\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Language\": \"01\",\r\n    \"Controller\": \"PXW/Sharepoint\",\r\n    \"Action\": \"DownloadDocument\",\r\n    \"PathParameters\": {\r\n    },\r\n    \"PayloadParameters\": {\r\n        \"filename\":\"20220103_InvestmentInformation_5PXREY_0_1.pdf\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "DownloadDocuments", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"DownloadDocuments\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Language\": \"01\",\r\n    \"Controller\": \"PXW/Sharepoint\",\r\n    \"Action\": \"DownloadDocuments\",\r\n    \"PathParameters\": {\r\n    },\r\n    \"PayloadParameters\": {\r\n        \"filenames\":[\r\n        \"20230206_AccountApplication_45B02MA_0_2.pdf\",\r\n        \"20230206_AccountApplication_45B02MA_0_1.pdf\",\r\n        \"20230101_AccountApplication_5PXKAR_1_1.pdf\"\r\n    ]\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}, {"name": "DataApi", "item": [{"name": "Private Securities", "item": [{"name": "CreatePrivateSecurity", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"CreatePrivateSecurity\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"CreatePrivateSecurity\",\r\n    \"PathParameters\": {\r\n        \"profile_pk\": \"{{profile_id}}\"\r\n    },\r\n    \"PayloadParameters\": {\r\n        \"security_code\": \"ACURSU\",\r\n        \"security_symbol\": \"ACU\",\r\n        \"security_name\": \"Acme Corp RSU 2022\",\r\n        \"security_description\": \"Acme Corp, Restricted Stock Units (2022)\",\r\n        \"security_name_description\": \"Acme Corp, Restricted Stock Units (2022)\",\r\n        \"security_type_code\": \"RSTK\",\r\n        \"security_country_code\": \"US\",\r\n        \"security_currency_code\": \"USD\",\r\n        \"asset_class_code\": \"*********\",\r\n            \"account_id\": \"c5fec537-2733-4c5b-bacf-897fe1c52290\",\r\n    \"transaction_type_code\": \"SRD\",\r\n    \"trade_date\": \"2023-11-17\",\r\n    \"settle_date\": \"2023-11-20\",\r\n    \"quantity\": \"1.000000\",\r\n    \"unit_price\": \"1600000.000000\",\r\n    \"settlement_currency_code\": \"CAD\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "CheckSecurityCodeAvailability", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"CheckSecurityCodeAvailability\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"CheckSecurityCodeAvailability\",\r\n    \"PathParameters\": {\r\n        \"profile_pk\": \"{{profile_id}}\",\r\n        \"security_code\": \"VTPAPS\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "RetrievePrivateSecurityByID", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"RetrievePrivateSecurityByID\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"RetrievePrivateSecurityByID\",\r\n    \"PathParameters\": {\r\n        \"profile_pk\": \"{{profile_id}}\",\r\n        \"id\": \"{{security_id}}\"\r\n    },\r\n    \"QueryParameters\": {}\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "ListPrivateSecuritiesByProfileID", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"ListPrivateSecuritiesByProfileID\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"ListPrivateSecuritiesByProfileID\",\r\n    \"PathParameters\": {\r\n        \"profile_pk\": \"{{profile_id}}\"\r\n    },\r\n    \"QueryParameters\": {}\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "PartiallyUpdatePrivateSecurityByID", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"PartiallyUpdatePrivateSecurityByID\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"PartiallyUpdatePrivateSecurityByID\",\r\n    \"PathParameters\": {\r\n        \"profile_pk\": \"{{profile_id}}\",\r\n        \"id\": \"{{security_id}}\"\r\n    },\r\n    \"PayloadParameters\": {\r\n        \"security_description\": \"Der Schrei der Natur (1893) by <PERSON><PERSON>\",\r\n        \"security_name_description\": \"Der Schrei der Natur (1893) by <PERSON><PERSON>\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}, {"name": "Private Transactions", "item": [{"name": "CreatePrivateTransaction", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"CreatePrivateTransaction\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"CreatePrivateTransaction\",\r\n    \"PathParameters\": {\r\n        \"profile_pk\": \"{{profile_id}}\",\r\n        \"security_pk\": \"{{security_id}}\"\r\n    },\r\n    \"PayloadParameters\": {\r\n        \"account_id\": \"c5fec537-2733-4c5b-bacf-897fe1c52290\",\r\n        \"transaction_type_code\": \"JBY\",\r\n        \"trade_date\": \"2023-11-17\",\r\n        \"settle_date\": \"2023-11-20\",\r\n        \"quantity\": \"1.000000\",\r\n        \"unit_price\": \"1600000.000000\",\r\n        \"settlement_currency_code\": \"CAD\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "RetrievePrivateTransactionByID", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"RetrievePrivateTransactionByID\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"RetrievePrivateTransactionByID\",\r\n    \"PathParameters\": {\r\n        \"profile_pk\": \"{{profile_id}}\",\r\n        \"security_pk\": \"{{security_id}}\",\r\n        \"id\": \"{{transaction_id}}\"\r\n    },\r\n    \"QueryParameters\": {}\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "ListPrivateTransactionsBySecurityID", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"ListPrivateTransactionsBySecurityID\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"ListPrivateTransactionsBySecurityID\",\r\n    \"PathParameters\": {\r\n        \"profile_pk\": \"{{profile_id}}\",\r\n        \"security_pk\": \"{{security_id}}\"\r\n    },\r\n    \"QueryParameters\": {}\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "PartiallyUpdatePrivateTransactionByID", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"PartiallyUpdatePrivateTransactionByID\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"PartiallyUpdatePrivateTransactionByID\",\r\n    \"PathParameters\": {\r\n        \"profile_pk\": \"{{profile_id}}\",\r\n        \"security_pk\": \"{{security_id}}\",\r\n        \"id\": \"{{transaction_id}}\"\r\n    },\r\n    \"PayloadParameters\": {\r\n        \"unit_price\": \"1599999.990000\"\r\n    }\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "DestroyPrivateTransactionByID", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"DestroyPrivateTransactionByID\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"DestroyPrivateTransactionByID\",\r\n    \"PathParameters\": {\r\n        \"profile_pk\": \"{{profile_id}}\",\r\n        \"security_pk\": \"{{security_id}}\",\r\n        \"id\": \"{{transaction_id}}\"\r\n    },\r\n    \"QueryParameters\": {}\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}, {"name": "Private Security Prices", "item": [{"name": "CreatePrivateSecurityPrice", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"CreatePrivateSecurityPrice\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"CreatePrivateSecurityPrice\",\r\n    \"PathParameters\": {\r\n        \"profile_pk\": \"{{profile_id}}\",\r\n        \"security_pk\": \"{{security_id}}\"\r\n    },\r\n    \"PayloadParameters\": {\r\n        \"price\": \"2000000.000000\",\r\n        \"price_as_of\": \"2023-11-23\"\r\n    }\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "RetrievePrivateSecurityPriceByID", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"RetrievePrivateSecurityPricebyID\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"RetrievePrivateSecurityPricebyID\",\r\n    \"PathParameters\": {\r\n        \"profile_pk\": \"{{profile_id}}\",\r\n        \"security_pk\": \"{{security_id}}\",\r\n        \"id\": \"{{security_price_id}}\"\r\n    },\r\n    \"QueryParameters\": {}\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "ListPrivateSecurityPricesBySecurityID", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"ListPrivateSecurityPricesBySecurityID\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"ListPrivateSecurityPricesBySecurityID\",\r\n    \"PathParameters\": {\r\n        \"profile_pk\": \"{{profile_id}}\",\r\n        \"security_pk\": \"{{security_id}}\"\r\n    },\r\n    \"QueryParameters\": {}\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "PartiallyUpdatePrivateSecurityPriceByID", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"PartiallyUpdatePrivateSecurityPriceByID\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"PartiallyUpdatePrivateSecurityPriceByID\",\r\n    \"PathParameters\": {\r\n        \"profile_pk\": \"{{profile_id}}\",\r\n        \"security_pk\": \"{{security_id}}\",\r\n        \"id\": \"{{security_price_id}}\"\r\n    },\r\n    \"PayloadParameters\": {\r\n        \"price\": \"1599999.990000\"\r\n    }\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "DestroyPrivateSecurityPriceByID", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"DestroyPrivateSecurityPriceByID\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"DestroyPrivateSecurityPriceByID\",\r\n    \"PathParameters\": {\r\n        \"profile_pk\": \"{{profile_id}}\",\r\n        \"security_pk\": \"{{security_id}}\",\r\n        \"id\": \"{{security_price_id}}\"\r\n    }\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}, {"name": "Common", "item": [{"name": "CurrenciesList", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"CurrenciesList\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"CurrenciesList\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "DashboardData", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"DashboardData\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"DashboardData\",\r\n    \"PathParameters\": {\r\n        \"profile_id\" : \"{{profile_id}}\"\r\n    },\r\n    \"QueryParameters\": {\r\n       \"as_of_date\": \"{{as_of_date}}\",\r\n       \"currency\": \"CAD\"\r\n       }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "ProfilesAccountsSummaryRetrieve", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"CurrenciesRetrieveDropdown\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"ProfilesAccountsSummaryRetrieve\",\r\n    \"PathParameters\": {\r\n        \"id\" : \"{{profile_id}}\"\r\n    },\r\n    \"QueryParameters\": {\r\n        \"as_of_date\" : \"{{as_of_date}}\",\r\n        \"currency\" : \"CAD\",\r\n        \"is_registered\" : \"true\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "ProfilesAsOfDatesRetrieve", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"CurrenciesRetrieveDropdown\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"ProfilesAsOfDatesRetrieve\",\r\n    \"PathParameters\": {\r\n        \"id\" : \"{{profile_id}}\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "ProfilesCustodianAccountCodesRetrieve", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"CurrenciesRetrieveDropdown\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"ProfilesCustodianAccountCodesRetrieve\",\r\n    \"PathParameters\": {\r\n        \"id\" : \"{{profile_id}}\"\r\n    },\r\n    \"QueryParameters\": {        \r\n    },\r\n    \"PayloadParameters\":{\r\n        \"account_codes\": {{account_codes}}\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "ProfilesDashboardLineGraphRetrieve", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"CurrenciesRetrieveDropdown\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"ProfilesDashboardLineGraphRetrieve\",\r\n    \"PathParameters\": {\r\n        \"id\" : \"{{profile_id}}\"\r\n    },\r\n    \"QueryParameters\": {\r\n        \"currency\" : \"CAD\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "ProfilesDashboardSummaryRetrieve", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"ProfilesDashboardLineGraphRetrieve\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"ProfilesDashboardSummaryRetrieve\",\r\n    \"PathParameters\": {\r\n        \"id\" : \"{{profile_id}}\"\r\n    },\r\n    \"QueryParameters\": {\r\n        \"as_of_date\": \"{{as_of_date}}\",\r\n        \"currency\": \"CAD\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "ProfilesFundingsRetrieve", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"ProfilesDashboardLineGraphRetrieve\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"ProfilesFundingsRetrieve\",\r\n    \"PathParameters\": {\r\n        \"id\": \"{{profile_id}}\"\r\n    },\r\n    \"QueryParameters\": {\r\n        \"as_of_date\": \"{{as_of_date}}\",\r\n        \"currency\": \"CAD\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"], "query": [{"key": "as_of_date", "value": null, "disabled": true}]}}, "response": []}, {"name": "ProfilesHoldingsRetrieve", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"ProfilesDashboardLineGraphRetrieve\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"ProfilesHoldingsRetrieve\",\r\n    \"PathParameters\": {\r\n        \"id\" : \"{{profile_id}}\"\r\n    },\r\n    \"QueryParameters\": {\r\n        \"as_of_date\": \"{{as_of_date}}\",\r\n        \"currency\" : \"CAD\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "ProfilesPieChartByAccountRetrieve", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"ProfilesDashboardLineGraphRetrieve\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"ProfilesPieChartByAccountRetrieve\",\r\n    \"PathParameters\": {\r\n        \"id\" : \"{{profile_id}}\"\r\n    },\r\n    \"QueryParameters\": {\r\n        \"as_of_date\": \"{{as_of_date}}\",\r\n        \"currency\" : \"CAD\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "ProfilesPieChartByAssetClassRetrieve", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"ProfilesDashboardLineGraphRetrieve\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"ProfilesPieChartByAssetClassRetrieve\",\r\n    \"PathParameters\": {\r\n        \"id\" : \"{{profile_id}}\"\r\n    },\r\n    \"QueryParameters\": {\r\n        \"as_of_date\": \"{{as_of_date}}\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "ProfilesPieChartsRetrieve", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"ProfilesDashboardLineGraphRetrieve\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"ProfilesPieChartsRetrieve\",\r\n    \"PathParameters\": {\r\n        \"id\" : \"{{profile_id}}\"\r\n    },\r\n    \"QueryParameters\": {\r\n        \"as_of_date\": \"{{as_of_date}}\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "ProfilesAllHoldingsRetrieve", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ProfilesAllHoldingsRetrieve\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataApi\",\n    \"Action\": \"ProfilesAllHoldingsRetrieve\",\n    \"PathParameters\": {\n        \"id\" : \"{{profile_id}}\"\n    },\n    \"QueryParameters\": {\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "ProfilesTopHoldingsRetrieve", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"ProfilesDashboardLineGraphRetrieve\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"ProfilesTopHoldingsRetrieve\",\r\n    \"PathParameters\": {\r\n        \"id\" : \"{{profile_id}}\"\r\n    },\r\n    \"QueryParameters\": {\r\n        \"as_of_date\": \"{{as_of_date}}\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "ProfilesTransactionsRetrieve", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"ProfilesDashboardLineGraphRetrieve\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"ProfilesTransactionsRetrieve\",\r\n    \"PathParameters\": {\r\n        \"id\" : \"{{profile_id}}\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "ProfilesAccountsList", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"ProfilesDashboardLineGraphRetrieve\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"ProfilesAccountsList\",\r\n    \"PathParameters\": {\r\n        \"profile_pk\" : \"{{profile_id}}\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "ProfilesAccountsHoldingsRetrieve", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"ProfilesDashboardLineGraphRetrieve\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"ProfilesAccountsHoldingsRetrieve\",\r\n    \"PathParameters\": {\r\n        \"profile_pk\" : \"{{profile_id}}\",\r\n        \"id\": \"{{account_code}}\"\r\n    },\r\n    \"QueryParameters\": {\r\n        \"as_of_date\": \"{{as_of_date}}\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "ProfilesAccountsTransactionsRetrieve", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"ProfilesDashboardLineGraphRetrieve\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"ProfilesAccountsTransactionsRetrieve\",\r\n    \"PathParameters\": {\r\n        \"profile_pk\" : \"{{profile_id}}\",\r\n        \"id\": \"{{account_code}}\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "LogUserLogin", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"LogUserLogin\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"LogUserLogin\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "LogUserLogout", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"LogUserLogout\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"LogUserLogout\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}, {"name": "Saved Views", "item": [{"name": "List", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"ListSavedViews\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"ListSavedViews\",\r\n    \"QueryParameters\": {\r\n        \"data_set\": \"accounts\"\r\n    }\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Create", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"CreateSavedView\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"CreateSavedView\",\r\n    \"PayloadParameters\": {\r\n        \"name\": \"Accounts Group By Type\",\r\n        \"data_set\": \"accounts\",\r\n        \"view\": {\r\n            \"visibleColumns\": [\r\n                \"AccountCode\",\r\n                \"AccountType\",\r\n                \"Sum_of_MarketValueAccrued\",\r\n                \"Sum_of_Book_Value\",\r\n                \"Sum_of_UnrealizedGL\",\r\n                \"UnrealizedGL_Percent\"\r\n            ],\r\n            \"filters\": [\r\n                {\r\n                    \"column\": \"AccountType\",\r\n                    \"operator\": \"is\",\r\n                    \"value\": \"RRSP\"\r\n                }\r\n            ],\r\n            \"groupBy\": \"AccountType\"\r\n        },\r\n        \"client_version\": \"1.0.0\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Read", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"ReadSavedView\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"ReadSavedView\",\r\n    \"PathParameters\": {\r\n        \"id\": \"139b7095-75a1-4b74-9813-d6967168ffab\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Update", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"UpdateSavedView\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"UpdateSavedView\",\r\n    \"PathParameters\": {\r\n        \"id\": \"139b7095-75a1-4b74-9813-d6967168ffab\"\r\n    },\r\n    \"PayloadParameters\": {\r\n        \"name\": \"Accounts Group By Type\",\r\n        \"data_set\": \"accounts\",\r\n        \"view\": {\r\n            \"visibleColumns\": [\r\n                \"AccountCode\",\r\n                \"AccountType\",\r\n                \"Sum_of_MarketValueAccrued\",\r\n                \"Sum_of_Book_Value\",\r\n                \"Sum_of_UnrealizedGL\",\r\n                \"UnrealizedGL_Percent\"\r\n            ],\r\n            \"filters\": [\r\n                {\r\n                    \"column\": \"AccountType\",\r\n                    \"operator\": \"is\",\r\n                    \"value\": \"RRSP\"\r\n                }\r\n            ],\r\n            \"groupBy\": \"AccountType\"\r\n        },\r\n        \"client_version\": \"1.0.1\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Partial Update", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"PartialUpdateSavedView\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"PartialUpdateSavedView\",\r\n    \"PathParameters\": {\r\n        \"id\": \"139b7095-75a1-4b74-9813-d6967168ffab\"\r\n    },\r\n    \"PayloadParameters\": {\r\n        \"client_version\": \"1.0.2\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Delete", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Component\": \"DeleteSavedView\",\r\n    \"TenantGuid\": \"{{TenantGuid}}\",\r\n    \"Controller\": \"PXW/DataApi\",\r\n    \"Action\": \"DeleteSavedView\",\r\n    \"PathParameters\": {\r\n        \"id\": \"139b7095-75a1-4b74-9813-d6967168ffab\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}]}, {"name": "DocumentApi", "item": [{"name": "User", "item": [{"name": "Create User", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"CreateUser\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"CreateUser\",\n    \"PayloadParameters\": {\n        \"username\": \"testuser\",\n        \"group_path\": \"/users\",\n        \"profile\": \"standard\",\n        \"name\": \"Johnny Cadillac V2\",\n        \"email\": \"<EMAIL>\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Update User", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"UpdateUser\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"UpdateUser\",\n    \"PathParameters\": {\n        \"user_id\": \"testuser\"\n    },\n    \"PayloadParameters\": {\n        \"username\": \"testuser\",\n        \"name\": \"<PERSON> Cadillacwwwww\",\n        \"profile\": \"standard\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Delete User", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"DeleteUser\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"DeleteUser\",\n    \"PathParameters\": {\n        \"user_id\": \"testuser\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Get User", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetUser\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"GetUser\",\n    \"PathParameters\": {\n        \"user_id\": \"testuser\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Get User Permissions", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetUserPermissions\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"GetUserPermissions\",\n    \"PathParameters\": {\n        \"user_id\": \"<EMAIL>\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Search Users", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetUserPermissions\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"GetUserPermissions\",\n    \"PathParameters\": {\n        \"user_id\": \"<EMAIL>\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "List Notifications", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ListNotifications\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"ListNotifications\",\n    \"PathParameters\": {\n        \"user_id\": \"<EMAIL>\"\n    },\n    \"QueryParameters\": {\n        \"limit\": 20,\n        \"fromDate\": \"2024-01-23T13:51:48Z\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Get Notification Unread Count", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ListUnreadNotifications\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"ListUnreadNotifications\",\n    \"PathParameters\": {\n        \"user_id\": \"<EMAIL>\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Subscribe to Notifications", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"UserSubscriptionNotifications\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"UserSubscriptionNotifications\",\n    \"PathParameters\": {\n        \"user_id\": \"<EMAIL>\"\n    },\n    \"PayloadParameters\": {\n        \"document_id\": \"9aa177d5-cbc6-49f1-9afe-f099f7ff41a5\",\n        \"events\": [\n            \"read\",\n            \"change\"\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Notification Email OptIn", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"SetUserEmailNotification\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"SetUserEmailNotification\",\n    \"PathParameters\": {\n        \"user_id\": \"<EMAIL>\"\n    },\n    \"QueryParameters\": {\n        \"opt_in\": true\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}, {"name": "External User", "item": [{"name": "Activate", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ActivateExternalUser\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"ActivateExternalUser\",\n    \"QueryParameters\": {\n        \"profileName\": \"<PERSON>-<PERSON> Test Profile\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Create", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"CreateExternalUser\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"CreateExternalUser\",\n    \"PayloadParameters\": {\n        \"first_name\": \"<PERSON>\",\n        \"last_name\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n        \"email\": \"<EMAIL>\",\n        \"profile_name\": \"<PERSON> Test Profile 2\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Deactivate", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"DeactivateExternalUser\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"DeactivateExternalUser\",\n    \"QueryParameters\": {\n        \"profileName\": \"<PERSON>-<PERSON> Test Profile\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Invite", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"InviteExternalUser\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"InviteExternalUser\",\n    \"QueryParameters\": {\n        \"profileName\": \"<PERSON>-Robert Test Profile 4\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}, {"name": "Group", "item": [{"name": "Create Group", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"CreateGroup\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"CreateGroup\",\n    \"PayloadParameters\": {\n        \"group_name\": \"whazza\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Update Group", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"UpdateGroup\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"UpdateGroup\",\n    \"PathParameters\": {\n        \"group_id\": \"houhou\"\n    },\n    \"PayloadParameters\": {\n        \"current_group_name\": \"whazza\",\n        \"new_group_name\": \"houhou\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Delete Group", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"DeleteGroup\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"DeleteGroup\",\n    \"PathParameters\": {\n        \"group_id\": \"houhou\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Get Group", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetGroup\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"GetGroup\",\n    \"PathParameters\": {\n        \"group_id\": \"houhou\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}, {"name": "Share", "item": [{"name": "Get Share", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetShare\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"GetShare\",\n    \"PathParameters\": {\n        \"share_id\": \"edddc073-259d-4b17-9246-19989bd6fce7\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Create Share", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"CreateShare\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"CreateShare\",\n    \"PayloadParameters\": {\n        \"share_id\": \"7f6563fc-e3cb-46b4-800a-5a2866859d67\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Update Share", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"UpdateShare\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"UpdateShare\",\n    \"PathParameters\": {\n        \"share_id\": \"85d45aa7-164d-44f7-9cfe-bd3818e2d433\"\n    },\n    \"PayloadParameters\": {\n        \"users_info\": [\n            {\n                \"username\": \"user2\"\n            }\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Get All Shares", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetAllShares\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"GetAllShares\",\n    \"QueryParameters\": {\n        \"Limit\": 200,\n        \"Offset\": 0\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}, {"name": "Document Categories", "item": [{"name": "Add document category", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"AddDocumentCategory\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"AddDocumentCategory\",\n    \"PayloadParameters\": {\n        \"key\" : \"Test\",\n        \"value\": \"Test\",\n        \"color\": \"#3f4\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Delete document category Copy", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"DeleteDocumentCategory\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"DeleteDocumentCategory\",\n    \"PathParameters\": {\n        \"category_value\" : \"Test\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}, {"name": "Comments", "item": [{"name": "Add Comment", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"AddComment\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"AddComment\",\n    \"PayloadParameters\": {\n        \"user_id\": \"34e28262-2f67-4ccc-ac46-6f50f5fd1d43\",\n        \"content\": \"Oh Hello Donkay boi\",\n        \"full_path\": \"syrbrm-lip/2022-alphabet-annual-report-1_test.pdf\"\n    },\n    \"PathParameters\": {\n        \"document_id\": \"fcb35125-9fc4-4e8b-93ec-f6d16a37c20d\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Get Comment", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetComment\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"GetComment\",\n    \"PathParameters\": {\n        \"document_id\": \"b980d2b4-0a8d-48f8-acca-ae361902f998\",\n        \"comment_id\": \"71eb9f7c-a3d9-481f-8737-8fa85d209f6e\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "ListComments", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ListComments\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"ListComments\",\n    \"PathParameters\": {\n        \"document_id\": \"b980d2b4-0a8d-48f8-acca-ae361902f998\"\n    },\n    \"QueryParameters\": {\n        \"limit\": 50,\n        \"offset\": 0\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Update Comment", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"UpdateComment\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"UpdateComment\",\n    \"PathParameters\": {\n        \"document_id\": \"b980d2b4-0a8d-48f8-acca-ae361902f998\",\n        \"comment_id\": \"3fb3b55c-f7d6-4022-8553-5bd8754e5ed9\"\n    },\n    \"PayloadParameters\": {\n        \"content\": \"This is the still new content hihi\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Delete Comment", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"DeleteComment\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"DeleteComment\",\n    \"PathParameters\": {\n        \"document_id\": \"b980d2b4-0a8d-48f8-acca-ae361902f998\",\n        \"comment_id\": \"71eb9f7c-a3d9-481f-8737-8fa85d209f6e\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "ListUsers", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ListUsers\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"ListUsers\",\n    \"PathParameters\": {\n        \"document_id\": \"b980d2b4-0a8d-48f8-acca-ae361902f998\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Get Unread Count", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetCommentUnreadCount\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"GetCommentUnreadCount\",\n    \"PathParameters\": {\n        \"document_id\": \"38dc8e3d-1831-4be1-92f1-f2b0e9db2262\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}, {"name": "Organizations", "item": [{"name": "List Organizations", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ListOrganizations\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"ListOrganizations\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}, {"name": "list documents", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ListDocuments\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"ListDocuments\",\n    \"QueryParameters\": {\n        \"path\" : \"accounts-tesapg/\",\n        \"limit\": \"0\",\n        \"offset\": \"0\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "search documents", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"SearchDocuments\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"SearchDocuments\",\n    \"PayloadParameters\": {\n        \"Details\": true,\n        \"From\": 3,\n        \"Size\": 2,\n        \"Query\": {\n            // \"Content\": \"string\",\n            // \"Extension\": \"txt\",\n            //\"FileName\": \"NANI.txt\",\n            \"FileNameOrContent\": \"news\",\n            // \"DocumentCategory\": \"doc category hihi\",\n            \"RelatedAccount\": \"5PXABH\",\n            \"PathPrefix\": [\n                \"syrbrm-lip\"\n            ]\n            // \"Not\": false\n            // \"Paths\": [\n            //     \"pxw-test-cell/2021/*\"\n            // ]\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "download documents", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"DownloadDocuments\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"DownloadDocuments\",\n    \"PayloadParameters\": {\n        \"file_action\": \"download\",\n        \"paths\": [\n            \"syrbrm-lip/5PXABH.txt\"\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "move/copy documents", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"MoveDocuments\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"MoveDocuments\",\n    \"PayloadParameters\": {\n        \"action_type\": \"move\",\n        \"target\": \"syrbrm-lip/Brochure3.pdf\",\n        \"paths\":[\n             \"syrbrm-lip/Brochure.pdf\"\n        ],\n        \"target_parent\": false\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "document activityfeed", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetDocumentActivityFeed\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"GetDocumentActivityFeed\",\n    \"PathParameters\": {\n        \"document_id\": \"384b03b9-e888-454c-9262-3e5dcd2b7f10\"\n    },\n    \"QueryParameters\": {\n        \"Limit\": 200,\n        \"Offset\": 0\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "update document metadatas", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"UpdateDocumentMetadatas\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"UpdateDocumentMetadatas\",\n    \"PathParameters\": {\n        \"document_id\": \"54d3ce3d-76be-4a9b-965f-6a80d79ecb6c\"\n    },\n    \"PayloadParameters\": {\n        \"pxw_profile_id\": \"4cc0d900-3d9d-472a-8316-93d7eb5da3c7\",\n        \"related_accounts\": [\"5PXAZZ\"],\n        \"document_category\": \"Legal\",\n        \"description\": \"This is amazing\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "delete documents", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"DeleteDocuments\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"DeleteDocuments\",\n    \"PayloadParameters\": {\n        \"file_action\": \"delete\",\n        \"paths\": [\n            \"pxw-test-cell/2022-alphabet-annual-report-1.pdf\"\n        ],\n        \"remove_permanently\": true\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "upload documents", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file_action", "value": "upload", "type": "text"}, {"key": "documents", "type": "file", "src": "/Users/<USER>/Downloads/profilea.txt"}, {"key": "TenantGuid", "value": "{{TenantGuid}}", "type": "text"}, {"key": "path", "value": "test-profile-1/", "type": "text"}, {"key": "Controller", "value": "PXW/DocumentApi", "type": "text"}, {"key": "Action", "value": "UploadDocuments", "type": "text"}, {"key": "Component", "value": "UploadDocuments", "type": "text"}, {"key": "document_details", "value": "{\"category\":\"Legal\",\"description\":\"test\",\"fileName\":\"profilea.txt\",\"relatedAccounts\":[\"5PXAZZ\"],\"originalFileName\":\"profilea.txt\"}", "type": "text"}, {"key": "pxw_profile_id", "value": "4cc0d900-3d9d-472a-8316-93d7eb5da3c7", "type": "text"}]}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Create Role", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"CreateRole\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"CreateRole\",\n    \"PayloadParameters\": {\n        \"ForceOverride\": true,\n        \"Uuid\": \"testtest\",\n        \"Label\": \"This is a Test Role\",\n        \"Policies\": [\n            {\n                \"Action\": \"READ\",\n                \"Subject\": \"profile:standard\",\n                \"Effect\": \"allow\"\n            },\n            {\n                \"Action\": \"WRITE\",\n                \"Subject\": \"profile:admin\",\n                \"Effect\": \"allow\"\n            },\n            {\n                \"Action\": \"WRITE\",\n                \"Subject\": \"user:user2\",\n                \"Effect\": \"allow\"\n            }\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "List Roles", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ListRoles\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"ListRoles\",\n    \"PayloadParameters\": {\n        \"Limit\": 25,\n        \"Offset\": 0,\n        \"Operation\": \"AND\",\n        \"Queries\": [\n            {\n                \"Uuid\": [\n                    \"testtest\"\n                ]\n            }\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Delete Role", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"DeleteRole\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"DeleteRole\",\n    \"PathParameters\": {\n        \"role_id\": \"testtest\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Get Role", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetRole\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"GetRole\",\n    \"PathParameters\": {\n        \"role_id\": \"testtest\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Create Folder", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"CreateFolder\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"CreateFolder\",\n    \"PayloadParameters\": {\n        \"file_action\": \"create_folder\",\n        \"path\": \"personal-files/test9\",\n        \"description\": \"description test\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "document categories", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"DocumentCategoryDropDown\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DocumentApi\",\n    \"Action\": \"GetDocumentCategories\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// pm.request.headers.add({", "//     key: 'pydio_user_token',", "//     value: pm.globals.get('pydio_user_token')", "// })"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}, {"name": "PermissionApi", "item": [{"name": "Users", "item": [{"name": "UserProfiles", "item": [{"name": "List UserProfiles", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ListUserProfiles\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"ListUserProfiles\",\n    \"PathParameters\": {\n        \"user_id\": \"4a2c25e0-5dde-4d68-918e-cc8d68e5b286\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Assign UserProfile", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"AssignUserProfile\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"AssignUserProfile\",\n    \"PathParameters\": {\n        \"user_id\": \"00560f7b-850b-4074-9574-0d8c100ee5f0\",\n        \"profile_id\": \"f9a56b52-80d5-4f6f-802d-69f4ff05d2e0\" \n    },\n    \"PayloadParameters\": {\n        \"is_assigned\": false\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}, {"name": "UserRole", "item": [{"name": "List UserRoles", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ListUserRoles\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"ListUserRoles\",\n    \"PathParameters\": {\n        \"user_id\": \"4a2c25e0-5dde-4d68-918e-cc8d68e5b286\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Assign UserRole", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"AssignUserRole\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"AssignUserRole\",\n    \"PathParameters\": {\n        \"user_id\": \"6e972dd2-84d4-4930-8e04-4e466f6b572d\",\n        \"role_id\": \"d6b0c404-f554-4d30-9899-faf35bfd94d1\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}, {"name": "UserProfileGroups", "item": [{"name": "ListUserProfileGroups", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ListUserProfileGroups\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"ListUserProfileGroups\",\n    \"PathParameters\": {\n        \"user_id\": \"bf2c8aa8-7b62-4612-b244-61fbfb158bd6\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "AssignProfileGroupToUser", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"AssignProfileGroupToUser\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"AssignProfileGroupToUser\",\n    \"PathParameters\": {\n        \"user_id\": \"bf2c8aa8-7b62-4612-b244-61fbfb158bd6\",\n        \"profile_group_id\": \"74c558b1-8602-49d9-b0a3-012336462fc4\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "UnassignProfileGroupToUser", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"UnassignProfileGroupToUser\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"UnassignProfileGroupToUser\",\n    \"PathParameters\": {\n        \"user_id\": \"bf2c8aa8-7b62-4612-b244-61fbfb158bd6\",\n        \"profile_group_id\": \"74c558b1-8602-49d9-b0a3-012336462fc4\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}, {"name": "ListUsers", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ListUsers\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"ListUsers\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain.uat}}/api", "host": ["{{domain.uat}}"], "path": ["api"]}}, "response": []}, {"name": "GetUser", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetUser\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"GetUser\",\n    \"PathParameters\": {\n        \"user_id\": \"34e28262-2f67-4ccc-ac46-6f50f5fd1d43\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "DeleteUser", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"DeleteUser\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"DeleteUser\",\n    \"PathParameters\": {\n        \"user_id\": \"*************-4ef3-a26a-37baf4656188\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "UpdateUser", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"UpdateUser\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"UpdateUser\",\n    \"PathParameters\": {\n        \"user_id\": \"dfbce528-a0c4-45c2-8129-152a900c05b4\"\n    },\n    \"PayloadParameters\": {\n        \"firstName\": \"Francisco2\",\n        \"lastName\": \"De Gama\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Send User Invite", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"SendInviteUser\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"SendInviteUser\",\n    \"PathParameters\": {\n        \"user_id\": \"4a2c25e0-5dde-4d68-918e-cc8d68e5b286\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "CurrentUser", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"CurrentUser\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"CurrentUser\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "CreateUser", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"CreateUser\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"CreateUser\",\n    \"PayloadParameters\": {\n        \"firstname\": \"<PERSON>\",\n        \"lastname\": \"<PERSON> G<PERSON>\",\n        \"email\": \"<EMAIL>\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "SetUserLogindatetime", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"SetUserLoginDatetime\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"SetUserLoginDatetime\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}, {"name": "Profiles", "item": [{"name": "ProfileAccount", "item": [{"name": "List Profile Accounts", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ListProfileAccounts\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"ListProfileAccounts\",\n    \"PathParameters\": {\n        \"profile_id\": \"b02fb956-6f0a-49c6-bebe-8171ae89be33\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Assign Profile Account", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"AssignProfileAccount\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"AssignProfileAccount\",\n    \"PathParameters\": {\n        \"profile_id\": \"4cc0d900-3d9d-472a-8316-93d7eb5da3c7\",\n        \"account_id\": \"account_id\"\n    },\n    \"PayloadParameters\": {\n        \"is_assigned\": true\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "SearchAccount", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ProfilesAccountsSearch\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"ProfilesAccountsSearch\",\n    \"PayloadParameters\": {\n        \"searchTerm\": \"tori\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}, {"name": "GetProfile", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetProfile\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"GetProfile\",\n    \"QueryParameters\": {\n        \"with_users\": true\n    },\n    \"PathParameters\": {\n        \"profile_id\": \"54510ea8-8640-469c-9849-3c8627ed5951\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "DeleteProfile", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"DeleteProfile\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"DeleteProfile\",\n    \"PathParameters\": {\n        \"profile_id\": \"3cbcca97-c6ec-4e70-8af4-47c8ab869789\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Update Profile", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"UpdateProfile\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"UpdateProfile\",\n    \"PathParameters\": {\n        \"profile_id\": \"730af9d5-8bf4-4b50-a07d-d222eb855964\"\n    },\n    \"PayloadParameters\": {\n        \"name\": \"ayaya\",\n        \"alphaccoKey\": \"a key\",\n        \"initialWorkflowCompleted\": true,\n        \"canBypassInitialWorkflow\": true\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Create Profile", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"CreateProfile\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"CreateProfile\",\n    \"PayloadParameters\": {\n        \"name\": \"new profile\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "ListProfiles", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ListProfiles\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"ListProfiles\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Batch assign users", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"BatchAssignUsersToProfile\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"BatchAssignUsersToProfile\",\n    \"PathParameters\": {\n        \"profile_id\": \"787ea432-d130-4a8d-95aa-b0fb5f745711\"\n    },\n    \"PayloadParameters\": {\n        \"is_assigned\": true,\n        \"user_ids\": [\"2492b773-bee3-4dc4-b01a-9eb992dad9e4\", \"0e796e32-8357-4c04-b378-7cb6a20b4d1e\"]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}, {"name": "Accounts", "item": [{"name": "ListAccounts", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ListAccounts\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"ListAccounts\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "GetAccount", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetAccount\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"GetAccount\",\n    \"PathParameters\": {\n        \"account_id\": \"1aeeb27a-9d77-46df-9167-8f759a0d01a0\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Create Account", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"CreateAccount\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"CreateAccount\",\n    \"PayloadParameters\": {\n        \"profile_id\": \"54510ea8-8640-469c-9849-3c8627ed5951\",\n        \"name\": \"waowwaow\",\n        \"friendlyName\": \"<PERSON>\",\n        \"type\": \"Cash\",\n        \"accountCode\": \"ZAZVZX\",\n        \"accountCurrency\": \"CAD\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "DeleteAccount", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"DeleteAccount\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"DeleteAccount\",\n    \"PathParameters\": {\n        \"account_id\": \"f0fbb341-93d5-4893-ae38-2f11b13e6e55\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "UpdateAccount", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"UpdateAccount\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"UpdateAccount\",\n    \"PathParameters\": {\n        \"account_id\": \"f0fbb341-93d5-4893-ae38-2f11b13e6e55\"\n    },\n    \"PayloadParameters\": {\n        \"name\": \"new name\",\n        \"friendlyName\": \"very friendly account name\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}, {"name": "Roles", "item": [{"name": "ListRoles", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ListRoles\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"ListRoles\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "GetRole", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetRole\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"GetRole\",\n    \"PathParameters\": {\n        \"role_id\": \"ee61adbf-1d78-43ab-9645-a5a01205689f\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "DeleteRole", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"DeleteRole\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"DeleteRole\",\n    \"PathParameters\": {\n        \"role_id\": \"be1ccb88-de92-4bad-8dd5-195475459c3e\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Update Role", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"UpdateRole\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"UpdateRole\",\n    \"PathParameters\": {\n        \"role_id\": \"ee61adbf-1d78-43ab-9645-a5a01205689f\"\n    },\n    \"PayloadParameters\": {\n        \"name\": \"this is updated\",\n        \"description\": \"this is a test with updated description\",\n        \"permissions\": [\n            \"fake-permission\",\n            \"fake-permission2\"\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Create Role", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"CreateRole\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"CreateRole\",\n    \"PayloadParameters\": {\n        \"name\": \"testroleoulalahihih<PERSON>\",\n        \"description\": \"this is a test\",\n        \"permissions\": [\n            \"fake-permission\"\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}, {"name": "ProfileGroups", "item": [{"name": "ListProfileGroups", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ListProfileGroups\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"ListProfileGroups\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "CreateProfileGroup", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"CreateProfileGroup\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"CreateProfileGroup\",\n    \"PayloadParameters\": {\n        \"name\": \"Vincent Profile Group\",\n        \"profileIds\": [\n            \"03932ff1-2636-4cef-be6f-a4b061f0c0ac\",\n            \"4de2eb41-bc67-4a5e-ac6c-ccc3aaa04919\",\n            \"f365a92c-5081-4fe1-9a88-0f4a1c9388ba\"\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "GetProfileGroup", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetProfileGroup\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"GetProfileGroup\",\n    \"PathParameters\": {\n        \"profile_group_id\": \"74c558b1-8602-49d9-b0a3-012336462fc4\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "PartiallyUpdateProfileGroup", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"PartiallyUpdateProfileGroup\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"PartiallyUpdateProfileGroup\",\n    \"PathParameters\": {\n        \"profile_group_id\": \"74c558b1-8602-49d9-b0a3-012336462fc4\"\n    },\n    \"PayloadParameters\": {\n        \"name\": \"Vincent Profile Group - Updated\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "DeleteProfileGroup", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"DeleteProfileGroup\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"DeleteProfileGroup\",\n    \"PathParameters\": {\n        \"profile_group_id\": \"85ba4500-6f32-49fa-b141-04021869e0aa\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "AssignProfilesToProfileGroup", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"AssignProfilesToProfileGroup\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"AssignProfilesToProfileGroup\",\n    \"PathParameters\": {\n        \"profile_group_id\": \"74c558b1-8602-49d9-b0a3-012336462fc4\"\n    },\n    \"PayloadParameters\": {\n        \"profileIds\": [\n            \"03932ff1-2636-4cef-be6f-a4b061f0c0ac\",\n            \"4de2eb41-bc67-4a5e-ac6c-ccc3aaa04919\"\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "UnassignProfilesToProfileGroup", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"UnassignProfilesToProfileGroup\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"UnassignProfilesToProfileGroup\",\n    \"PathParameters\": {\n        \"profile_group_id\": \"74c558b1-8602-49d9-b0a3-012336462fc4\"\n    },\n    \"PayloadParameters\": {\n        \"profileIds\": [\n            \"03932ff1-2636-4cef-be6f-a4b061f0c0ac\",\n            \"4de2eb41-bc67-4a5e-ac6c-ccc3aaa04919\"\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "Batch assign Users", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"BatchAssignUsersToProfileGroup\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/PermissionApi\",\n    \"Action\": \"BatchAssignUsersToProfileGroup\",\n    \"PathParameters\": {\n        \"profile_group_id\": \"1141cfec-4eed-4a02-8ce0-7e020f1cfebd\"\n    },\n    \"PayloadParameters\": {\n        \"is_assigned\": true,\n        \"user_ids\": [\"2492b773-bee3-4dc4-b01a-9eb992dad9e4\", \"0e796e32-8357-4c04-b378-7cb6a20b4d1e\"]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}]}, {"name": "DataInterchangeApi", "item": [{"name": "Workflow", "item": [{"name": "ListWorkflows", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6Im1fZmN2QU9XNHZoWXdJTnRYYW0zbFNtamRJdkRQT004bER2aHNTNWdkY3MiLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.P8zGnbuLyFolnY_VTOzE7GgwOoJbpQytq7b9T5OAOHysqsqXfbN0ilWX8aICe__cdpD1f_XcYfAUF62e24d4Zns4ta_bXSzaUw7ZN96j-XQ8V0XrVRO03hxyHHZroxq47DoCfFBKXxxqSER2yOUqxItOehydYfZMb5xjzDQd7tvC4G_KTg7Qr26AjgcX03lEZ-O6UvkDLHsDz-5V8HngyZfi-Oqc4cJpYI8B_6I1yUq_nOicN0e6qIGXD7rqh1FEitjFu2EooLWtOrdAyYSwpy5DsSqJ9Q_5qmzxbOsIEc9r-JXuKPiSoHedTXci6GCgpD7UqSfbZESvGDjyb6gFKQ", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ListWorkflows\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"ListWorkflows\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "CreateWorkflow", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"CreateWorkflow\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"CreateWorkflow\",\n    \"PayloadParameters\": {\n        \"name\": \"New Workflow\",\n        \"description\": \"This is a new workflow\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain.uat}}/api", "host": ["{{domain.uat}}"], "path": ["api"]}}, "response": []}, {"name": "GetWorkflow", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetWorkflow\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"GetWorkflow\",\n    \"PathParameters\": {\n        \"workflow_id\": \"27d2c923-1002-4105-b37a-f3b9263d5eb8\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain.uat}}/api", "host": ["{{domain.uat}}"], "path": ["api"]}}, "response": []}, {"name": "UpdateWorkflow", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"UpdateWorkflow\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"UpdateWorkflow\",\n    \"PathParameters\": {\n        \"workflow_id\": \"27d2c923-1002-4105-b37a-f3b9263d5eb8\"\n    },\n    \"PayloadParameters\": {\n        \"name\": \"Blah\",\n        \"description\": \"Blah Blah\",\n        \"status\": \"ARCHIVE\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain.uat}}/api", "host": ["{{domain.uat}}"], "path": ["api"]}}, "response": []}, {"name": "PartialUpdateWorkflow", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"PartialUpdateWorkflow\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"PartialUpdateWorkflow\",\n    \"PathParameters\": {\n        \"workflow_id\": \"27d2c923-1002-4105-b37a-f3b9263d5eb8\"\n    },\n    \"PayloadParameters\": {\n        \"name\": \"Partially Updated Workflow\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain.uat}}/api", "host": ["{{domain.uat}}"], "path": ["api"]}}, "response": []}, {"name": "DeleteWorkflow", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"DeleteWorkflow\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"DeleteWorkflow\",\n    \"PathParameters\": {\n        \"workflow_id\": \"dc468580-a320-4148-a3db-20ef5b4e5359\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain.uat}}/api", "host": ["{{domain.uat}}"], "path": ["api"]}}, "response": []}, {"name": "ActivateWorkflow", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ActivateWorkflow\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"ActivateWorkflow\",\n    \"PathParameters\": {\n        \"workflow_id\": \"27d2c923-1002-4105-b37a-f3b9263d5eb8\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain.uat}}/api", "host": ["{{domain.uat}}"], "path": ["api"]}}, "response": []}, {"name": "ArchiveWorkflow", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ArchiveWorkflow\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"ArchiveWorkflow\",\n    \"PathParameters\": {\n        \"workflow_id\": \"27d2c923-1002-4105-b37a-f3b9263d5eb8\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain.uat}}/api", "host": ["{{domain.uat}}"], "path": ["api"]}}, "response": []}, {"name": "ListWorkflowVersions", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ListWorkflowVersions\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"ListWorkflowVersions\",\n    \"PathParameters\": {\n        \"workflow_id\": \"27d2c923-1002-4105-b37a-f3b9263d5eb8\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain.uat}}/api", "host": ["{{domain.uat}}"], "path": ["api"]}}, "response": []}, {"name": "GetWorkflowPreview", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetWorkflowPreview\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"GetWorkflowPreview\",\n    \"PathParameters\": {\n        \"workflow_id\": \"27d2c923-1002-4105-b37a-f3b9263d5eb8\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain.uat}}/api", "host": ["{{domain.uat}}"], "path": ["api"]}}, "response": []}, {"name": "GetWorkflowVersionPreview", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetWorkflowVersionPreview\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"GetWorkflowVersionPreview\",\n    \"PathParameters\": {\n        \"workflow_id\": \"27d2c923-1002-4105-b37a-f3b9263d5eb8\",\n        \"version_id\": \"2\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain.uat}}/api", "host": ["{{domain.uat}}"], "path": ["api"]}}, "response": []}, {"name": "SetWorkflowPublicVersion", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"SetWorkflowPublicVersion\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"SetWorkflowPublicVersion\",\n    \"PathParameters\": {\n        \"workflow_id\": \"27d2c923-1002-4105-b37a-f3b9263d5eb8\"\n    },\n    \"PayloadParameters\": {\n        \"version\": \"1\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain.uat}}/api", "host": ["{{domain.uat}}"], "path": ["api"]}}, "response": []}, {"name": "UploadWorkflowVersion", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"UploadWorkflowVersion\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"UploadWorkflowVersion\",\n    \"PathParameters\": {\n        \"workflow_id\": \"27d2c923-1002-4105-b37a-f3b9263d5eb8\"\n    },\n    \"PayloadParameters\": {\n        \"workflow_data\": \"---\\noptions:\\n    default_language: en\\nsections:\\n  - id: General\\n    name: General\\n  - id: Company\\n    name: Company\\n  - id: Documents\\n    name: Documents\\nform:\\n    questions:\\n      - datatag: generalLabel\\n        section: General\\n        type: label\\n        text: ''\\n        help_text: ''\\n        error_message: ''\\n        options:\\n          title: General Informations\\n          instructions: Read and complete all the forms\\n        required: false\\n      - datatag: civility\\n        section: General\\n        type: select\\n        text: Civility\\n        help_text: 'Please enter your civility'\\n        error_message: This is an error message\\n        options:\\n          radio: true\\n          list_items:\\n            - text: Mr.\\n              value: Mr.\\n            - text: Mrs.\\n              value: Mrs.\\n        required: true\\n      - datatag: shortName\\n        section: General\\n        type: text\\n        text: Short Name\\n        help_text: Please enter your usual name\\n        error_message: This is an error message\\n        options:\\n          minimum: 2\\n        required: true\\n      - datatag: longName\\n        section: General\\n        type: text\\n        text: Long Name\\n        help_text: Please enter your full name\\n        error_message: This is an error message\\n        required: false\\n      - datatag: securityType\\n        section: General\\n        type: select\\n        text: Security Type\\n        help_text: Please enter your security type\\n        error_message: This is an error message\\n        options:\\n          list_items:\\n            - text: Security Type A\\n              value: Security Type A\\n            - text: Security Type B\\n              value: Security Type B\\n            - text: Security Type C\\n              value: Security Type C\\n        required: true\\n      - datatag: securityCode\\n        section: General\\n        type: text\\n        text: Security Code\\n        help_text: Please enter your security code (3 digits only)\\n        error_message: This is an error message\\n        options:\\n          regex: ^\\\\b[0-9]{3}\\\\b$\\n        required: true\\n      - datatag: birthday\\n        section: General\\n        type: date\\n        text: Birthday\\n        help_text: Please enter your birthday\\n        error_message: This is an error message\\n        options:\\n          format: YYYY-MM-DD\\n          validation: past\\n        required: true\\n      - datatag: age\\n        section: General\\n        type: number\\n        text: Age\\n        help_text: Please enter your age\\n        error_message: This is an error message\\n        options:\\n          type: integer\\n          min_value: 0\\n          max_value: 200\\n        required: true\\n      - datatag: projectEnd\\n        section: Company\\n        type: date\\n        text: End of Project (Estimated)\\n        help_text: Please enter your end date\\n        error_message: This is an error message\\n        options:\\n          format: YYYY-MM-DD\\n          validation: future\\n        required: true\\n      - datatag: yearOfCreation\\n        section: Company\\n        type: date\\n        text: Year of Creation\\n        help_text: Please enter your year of creation\\n        error_message: This is an error message\\n        options:\\n          format: YYYY\\n          validation: past\\n        required: true\\n      - datatag: companyType\\n        section: Company\\n        type: select\\n        text: Company Type\\n        help_text: Please enter the company type\\n        error_message: This is an error message\\n        options:\\n          multiSelect: true\\n          list_items:\\n            - text: Partnership\\n              value: Partnership\\n            - text: Corporation\\n              value: Corporation\\n            - text: Incorporation\\n              value: Incorporation\\n            - text: Cooperative\\n              value: Cooperative\\n            - text: Nonprofit\\n              value: Nonprofit\\n        required: true\\n      - datatag: expirationYear\\n        section: Company\\n        type: date\\n        text: Expiration Year\\n        help_text: Please enter your expiration year\\n        error_message: This is an error message\\n        options:\\n          format: YYYY\\n          validation: future\\n        required: true\\n      - datatag: capital\\n        section: Company\\n        type: number\\n        text: Company Capital\\n        help_text: Please give an estimation of company capital\\n        error_message: This is an error message\\n        options:\\n          type: currency\\n        required: true\\n      - datatag: resume\\n        section: Documents\\n        type: file\\n        text: Resume\\n        help_text: Please upload your resume\\n        error_message: This is an error message\\n        options:\\n          max_size: 1\\n          allowed_types: .pdf\\n          single: true\\n        required: true\\n      - datatag: otherDocuments\\n        section: Documents\\n        type: file\\n        text: Other Documents\\n        help_text: Please upload other documents if necessary\\n        error_message: This is an error message\\n        required: false\\n      - datatag: decimal\\n        section: Documents\\n        type: number\\n        text: Decimal\\n        help_text: Please enter a decimal number\\n        error_message: This is an error message\\n        options:\\n          type: decimal\\n        required: true\\n      - datatag: percentage\\n        section: Documents\\n        type: number\\n        text: Percentage\\n        help_text: Please enter a decimal number\\n        error_message: This is an error message\\n        options:\\n          type: percentage\\n        required: true\\nworkflow:\\n    steps:\\n        - name: Survey\\n          description: This is a survey step\\n          action: survey\\n        - name: Review\\n          description: this is a review step\\n          action: review\\n        - name: Signature\\n          description: This is a signature step\\n          action: signature\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain.uat}}/api", "host": ["{{domain.uat}}"], "path": ["api"]}}, "response": []}]}, {"name": "Workflow Instance", "item": [{"name": "ListWorkflowInstances", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6Im1fZmN2QU9XNHZoWXdJTnRYYW0zbFNtamRJdkRQT004bER2aHNTNWdkY3MiLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.P8zGnbuLyFolnY_VTOzE7GgwOoJbpQytq7b9T5OAOHysqsqXfbN0ilWX8aICe__cdpD1f_XcYfAUF62e24d4Zns4ta_bXSzaUw7ZN96j-XQ8V0XrVRO03hxyHHZroxq47DoCfFBKXxxqSER2yOUqxItOehydYfZMb5xjzDQd7tvC4G_KTg7Qr26AjgcX03lEZ-O6UvkDLHsDz-5V8HngyZfi-Oqc4cJpYI8B_6I1yUq_nOicN0e6qIGXD7rqh1FEitjFu2EooLWtOrdAyYSwpy5DsSqJ9Q_5qmzxbOsIEc9r-JXuKPiSoHedTXci6GCgpD7UqSfbZESvGDjyb6gFKQ", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"ListWorkflowInstances\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"ListWorkflowInstances\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "GetWorkflowInstance", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetWorkflowInstance\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"GetWorkflowInstance\",\n    \"PathParameters\": {\n        \"instance_id\": \"{{instance_id}}\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "CreateWorkflowInstance", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"CreateWorkflowInstance\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"CreateWorkflowInstance\",\n    \"PayloadParameters\": {\n        \"user\": \"3bb165ae-9102-4ea4-86a9-0e701d8a72be\",\n        \"workflow\": \"27d2c923-1002-4105-b37a-f3b9263d5eb8\",\n        \"assigned\": \"3bb165ae-9102-4ea4-86a9-0e701d8a72be\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "UpdateWorkflowInstance", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"UpdateWorkflowInstance\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"UpdateWorkflowInstance\",\n    \"PathParameters\": {\n        \"instance_id\": \"2d814015-e5f2-4a64-8720-954b44814d05\"\n    },\n    \"PayloadParameters\": {\n        \"user\": \"Jane Doe\",\n        \"workflow\": \"d45bb317-8114-4d5f-9dff-79e64dda5f82\",\n        \"assigned\": \"Test McTest\"\n    }\n}"}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "PartialUpdateWorkflowInstance", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"PartialUpdateWorkflowInstance\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"PartialUpdateWorkflowInstance\",\n    \"PathParameters\": {\n        \"instance_id\": \"{{instance_id}}\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "GetWorkflowInstanceLogs", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetWorkflowInstanceLogs\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"GetWorkflowInstanceLogs\",\n    \"PathParameters\": {\n        \"instance_id\": \"7539d33d-0c92-4a95-96b7-882cc6257e74\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "GetWorkflowInstanceComments", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetWorkflowInstanceComments\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"GetWorkflowInstanceComments\",\n    \"PathParameters\": {\n        \"instance_id\": \"7539d33d-0c92-4a95-96b7-882cc6257e74\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "CreateWorkflowInstanceComment", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"CreateWorkflowInstanceComment\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"CreateWorkflowInstanceComment\",\n    \"PathParameters\": {\n        \"instance_id\": \"7539d33d-0c92-4a95-96b7-882cc6257e74\"\n    },\n    \"PayloadParameters\": {\n        \"comment\": \"This is a comment\",\n        \"user_name\": \"<PERSON>\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "CreateWorkflowInstanceData", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "picture", "type": "file", "src": "/C:/test/Javian.txt"}, {"key": "TenantGuid", "value": "{{TenantGuid}}", "type": "text"}, {"key": "Controller", "value": "PXW/DataInterchangeApi", "type": "text"}, {"key": "Action", "value": "CreateWorkflowInstanceData", "type": "text"}, {"key": "Component", "value": "CreateWorkflowInstanceData", "type": "text"}]}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "UpdateWorkflowInstanceData", "protocolProfileBehavior": {"disabledSystemHeaders": {"accept-encoding": true}}, "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "DriverLicense", "type": "file", "src": "/C:/Users/<USER>/Downloads/test_questionaire.yaml"}, {"key": "TenantGuid", "value": "{{TenantGuid}}", "type": "text"}, {"key": "Controller", "value": "PXW/DataInterchangeApi", "type": "text"}, {"key": "Action", "value": "UpdateWorkflowInstanceData", "type": "text"}, {"key": "Component", "value": "UpdateWorkflowInstanceData", "type": "text"}, {"key": "instance_id", "value": "{{instance_id}}", "type": "text"}]}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "GetWorkflowInstanceData", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetWorkflowInstanceData\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"GetWorkflowInstanceData\",\n    \"PathParameters\": {\n        \"instance_id\": \"6a8e0177-d4d8-4f81-9986-34382cc20038\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "GetWorkflowInstanceFile", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n        \"Component\": \"GetWorkflowInstanceFile\",\r\n        \"TenantGuid\": \"{{TenantGuid}}\",\r\n        \"Controller\": \"PXW/DataInterchangeApi\",\r\n        \"Action\": \"GetWorkflowInstanceFile\",\r\n        \"PathParameters\": {\r\n            \"instance_id\": \"{{instance_id}}\",\r\n            \"datatag\": \"DriverLicense\"\r\n\t}\r\n }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "GetWorkflowInstanceQuestions", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetWorkflowInstanceQuestions\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"GetWorkflowInstanceQuestions\",\n    \"PathParameters\": {\n        \"instance_id\": \"7539d33d-0c92-4a95-96b7-882cc6257e74\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}, {"name": "GetWorkflowInstanceSteps", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"Component\": \"GetWorkflowInstanceSteps\",\n    \"TenantGuid\": \"{{TenantGuid}}\",\n    \"Controller\": \"PXW/DataInterchangeApi\",\n    \"Action\": \"GetWorkflowInstanceSteps\",\n    \"PathParameters\": {\n        \"instance_id\": \"5608beec-3dc7-4661-b304-6e2559a5d0ee\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{domain}}/api", "host": ["{{domain}}"], "path": ["api"]}}, "response": []}]}]}]}], "auth": {"type": "oauth2", "oauth2": [{"key": "clientSecret", "value": "", "type": "string"}, {"key": "scope", "value": "openid", "type": "string"}, {"key": "clientId", "value": "{{b2c_client_id}}", "type": "string"}, {"key": "authUrl", "value": "{{b2c_auth_url}}", "type": "string"}, {"key": "accessTokenUrl", "value": "{{b2c_token_url}}", "type": "string"}, {"key": "redirect_uri", "value": "https://jwt.ms", "type": "string"}, {"key": "grant_type", "value": "authorization_code_with_pkce", "type": "string"}, {"key": "tokenName", "value": "id_token", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["pm.request.headers.add({", "    key: 'Pydio-User-Token',", "    value: pm.globals.get('Pydio-User-Token')", "})"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["if(pm.response.code === 200) {", "    pm.globals.set('Pydio-User-Token', pm.response.headers.get(\"Pydio-User-Token\"))", "}"]}}], "variable": [{"key": "local_domain", "value": "https://localhost:7163", "type": "string"}]}