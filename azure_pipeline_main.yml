trigger: none
resources:
- repo: self

parameters:
- name: client
  displayName: "Select Client"
  type: string
  default: PXW
  values:
  - PXW
  - PAL
  - PHW
  - INFERNO
  - CMG
- name: environment
  displayName: "Select Environment"
  type: string
  default: DEV
  values:
  - DEV
  - UAT
  - PROD

name: $(Date:yyyyMMdd).$(Rev:r)_${{ parameters.client }}_${{ parameters.environment }}_Branch_$(Build.SourceBranchName)_$(Build.DefinitionName)_$(Build.BuildId)

extends:
  template: azure_pipelines/${{ parameters.client }}/azure_pipeline_${{ parameters.client }}_${{ parameters.environment }}.yml
