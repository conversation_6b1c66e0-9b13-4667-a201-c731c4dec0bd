{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [{"source": "/:path*", "headers": [{"key": "Content-Security-Policy", "value": "upgrade-insecure-requests; default-src 'self' http: https: data: blob: 'unsafe-inline' 'unsafe-eval';"}], "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}], "dynamicRoutes": [{"page": "/blog/[articleId]", "regex": "^/blog/([^/]+?)(?:/)?$", "routeKeys": {"nxtParticleId": "nxtParticleId"}, "namedRegex": "^/blog/(?<nxtParticleId>[^/]+?)(?:/)?$"}, {"page": "/chat/[projectID]", "regex": "^/chat/([^/]+?)(?:/)?$", "routeKeys": {"nxtPprojectID": "nxtPprojectID"}, "namedRegex": "^/chat/(?<nxtPprojectID>[^/]+?)(?:/)?$"}, {"page": "/processing-page/[[...processing-page]]", "regex": "^/processing\\-page(?:/(.+?))?(?:/)?$", "routeKeys": {"nxtPprocessingpage": "nxtPprocessing-page"}, "namedRegex": "^/processing\\-page(?:/(?<nxtPprocessingpage>.+?))?(?:/)?$"}, {"page": "/sign-in/[[...sign-in]]", "regex": "^/sign\\-in(?:/(.+?))?(?:/)?$", "routeKeys": {"nxtPsignin": "nxtPsign-in"}, "namedRegex": "^/sign\\-in(?:/(?<nxtPsignin>.+?))?(?:/)?$"}, {"page": "/sign-up/[[...sign-up]]", "regex": "^/sign\\-up(?:/(.+?))?(?:/)?$", "routeKeys": {"nxtPsignup": "nxtPsign-up"}, "namedRegex": "^/sign\\-up(?:/(?<nxtPsignup>.+?))?(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/ad-demo", "regex": "^/ad\\-demo(?:/)?$", "routeKeys": {}, "namedRegex": "^/ad\\-demo(?:/)?$"}, {"page": "/apple-icon.png", "regex": "^/apple\\-icon\\.png(?:/)?$", "routeKeys": {}, "namedRegex": "^/apple\\-icon\\.png(?:/)?$"}, {"page": "/blog", "regex": "^/blog(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog(?:/)?$"}, {"page": "/cv", "regex": "^/cv(?:/)?$", "routeKeys": {}, "namedRegex": "^/cv(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/icon.png", "regex": "^/icon\\.png(?:/)?$", "routeKeys": {}, "namedRegex": "^/icon\\.png(?:/)?$"}, {"page": "/opengraph-image.png", "regex": "^/opengraph\\-image\\.png(?:/)?$", "routeKeys": {}, "namedRegex": "^/opengraph\\-image\\.png(?:/)?$"}, {"page": "/privacy-policy", "regex": "^/privacy\\-policy(?:/)?$", "routeKeys": {}, "namedRegex": "^/privacy\\-policy(?:/)?$"}, {"page": "/security-policy", "regex": "^/security\\-policy(?:/)?$", "routeKeys": {}, "namedRegex": "^/security\\-policy(?:/)?$"}, {"page": "/sitemap.xml", "regex": "^/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\.xml(?:/)?$"}, {"page": "/success", "regex": "^/success(?:/)?$", "routeKeys": {}, "namedRegex": "^/success(?:/)?$"}, {"page": "/test-email", "regex": "^/test\\-email(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-email(?:/)?$"}, {"page": "/tos", "regex": "^/tos(?:/)?$", "routeKeys": {}, "namedRegex": "^/tos(?:/)?$"}, {"page": "/twitter-image.png", "regex": "^/twitter\\-image\\.png(?:/)?$", "routeKeys": {}, "namedRegex": "^/twitter\\-image\\.png(?:/)?$"}, {"page": "/waiting-list", "regex": "^/waiting\\-list(?:/)?$", "routeKeys": {}, "namedRegex": "^/waiting\\-list(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}