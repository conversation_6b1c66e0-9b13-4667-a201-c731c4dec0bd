import { Icons } from "@/components/icons";
import { HomeIcon, NotebookIcon } from "lucide-react";
import { Github, Linkedin, Mail, Twitter, Youtube } from 'lucide-react'

interface SocialLink {
  name: string;
  url: string;
  icon: any;
}

interface WorkExperience {
  company: string;
  href: string;
  badges: string[];
  title: string;
  logoUrl: string;
  start: string;
  end: string;
  location: string;
  description: string;
}

interface Education {
  school: string;
  href?: string;
  degree: string;
  logoUrl: string;
  start: string;
  end: string;
  location: string;
}

interface Certification {
  title: string;
  organization: string;
  date: string;
  id?: string | undefined;
  logoUrl: string;
}

interface Language {
  name: string;
  level: string;
}

interface AppData {
  name: string;
  initials: string;
  url: string;
  location: string;
  locationLink: string;
  description: string;
  summary: string;
  avatarUrl: string;
  coreCompetencies: string[];
  skills: string[];
  navbar: Array<{
    title: string;
    href: string;
  }>;
  contact: {
    email: string;
    tel: string;
    social: Array<SocialLink>;
  };
  work: WorkExperience[];
  education: Education[];
  certifications: Certification[];
  languages: Language[];
  interests?: string[];
}

export const DATA: AppData = {
  name: "Javian <PERSON>",
  initials: "JP",
  url: "https://javianpicardo.com",
  location: "Hamilton, ON. Canada",
  locationLink: "https://www.google.com/maps/place/Hamilton",
  description: "Principal Software Engineer | Application Architect | Solopreneur",
  summary: `Visionary and results-driven Application Architect and Principal Software Engineer with 16+ years of experience specializing in enterprise-grade Microsoft technology solutions.

Proven track record of transforming complex business requirements into scalable, innovative applications while leading cross-functional development teams. Expert in designing sophisticated cloud architectures and implementing security-focused solutions across Azure ecosystems.

Consistently delivers digital transformation initiatives that drive business value for organizations in financial services, insurance, and specialized software sectors. Combines technical leadership with strategic vision to create resilient, future-proof technology platforms.`,
  avatarUrl: "/javian.png",
  coreCompetencies: [
    "Application Architecture",
    "Cloud Architecture (Azure, AWS)",
    "Microservices & Distributed Systems",
    "Full Stack Development (.NET, React, Python)",
    "DevOps & CI/CD Implementations",
    "API Design & Integration",
    "Security Implementation",
    "Team Leadership & Agile Methodologies"
  ],
  skills: [
    // Core Technologies
    ".NET 8", "C#", "REACT", "MS SQL", "WEB.API", "NEXT.JS", "ASP.NET MVC",
    "AZURE", "TYPESCRIPT", "R", "JQUERY", "BOOTSTRAP", "ANGULAR", "CSS",
    "HTML", "GIT", "NODEJS", "PYTHON 3",

    // Azure Services
    "AZURE APP SERVICE", "AZURE FUNCTIONS", "AZURE SQL DATABASE",
    "AZURE DEVOPS & PIPELINES", "POWER BI", "ARM TEMPLATES", "TERRAFORM",
    "MULESOFT", "AZURE ACTIVE DIRECTORY", "AZURE AD B2C", "AZURE KEY VAULT",
    "AZURE MONITOR", "AZURE APPLICATION INSIGHTS", "AZURE API MANAGEMENT",
    "DYNAMICS 365", "AZURE BLOB STORAGE", "AZURE KUBERNETES SERVICE(AKS)",
    "AZURE VM", "SSIS", "WCF"
  ],
  navbar: [
    {
      title: 'About',
      href: '#about',
    },
    {
      title: 'Experience',
      href: '#experience',
    },
    {
      title: 'Education',
      href: '#education',
    },
    {
      title: 'Core Competencies',
      href: '#core-competencies',
    },
    {
      title: 'Skills',
      href: '#skills',
    },
    {
      title: 'Certifications',
      href: '#certifications',
    },
  ],
  contact: {
    email: "<EMAIL>",
    tel: "+16477715300",
    social: [
      {
        name: "LinkedIn",
        url: "https://www.linkedin.com/in/javian-picardo/",
        icon: Linkedin,
      },
      {
        name: "GitHub",
        url: "https://github.com/JavianDev",
        icon: Github,
      },
      {
        name: "Twitter",
        url: "https://x.com/javian_picardo",
        icon: Twitter,
      },
      {
        name: "YouTube",
        url: "https://www.youtube.com/@javianpicardo",
        icon: Youtube,
      },
      {
        name: "Email",
        url: "mailto:<EMAIL>",
        icon: Mail,
      },
    ],
  },
  work: [
    {
      company: "Ness USA Ltd. (CIBC Client)",
      title: "Principal Software Engineer | Application Architect",
      location: "Toronto, Ontario",
      start: "12/2024",
      end: "03/2025",
      description: "Working on shared data services project for internal reporting services. Architected and implemented a comprehensive shared data services portal using React and Azure App Services. Implemented advanced React.JS features including extensive use of Web Hooks (useState, useEffect, useContext, useReducer) for efficient state management across enterprise applications. Leveraged modern state management solutions within React, including Context API and Redux for managing global application state in complex scenarios.Created comprehensive architecture blueprint for shared data services including system context diagrams and component interactions. Established technical standards and design patterns for consistent implementation across banking applications. Designed secure API gateway architecture with centralized authentication, rate limiting, and monitoring capabilities. Developed robust Azure Functions APIs with C# & .NET 8, focusing on Open Banking authentication and authorization mechanisms. Engineered scalable microservices infrastructure with advanced security protocols ensuring compliance with banking industry standards.",
      badges: ["React", "Azure", ".NET 8", "C#", "Microservices", "Architecture"],
      logoUrl: "https://ui-avatars.com/api/?name=Ness&background=0D9488&color=fff",
      href: "#"
    },
    {
      company: "PortfolioXpressWay",
      title: "Lead Application Developer | Application Architect",
      location: "Richmond Hill, Ontario",
      start: "07/2022",
      end: "12/2024",
      description: "Portfolioxpressway provides investment professionals, portfolio managers with a fully digital 360-degree view of investor assets, no matter their location or nature, as well as the tools to source and recommend the very best investment opportunities. Collaborated directly with CTO to architect a comprehensive enterprise-grade solution addressing complex investment portfolio management requirements achieving SOC2. Partnered with program managers, business analysts, and scrum masters to translate business requirements into scalable technical architecture. Worked with UI/UX teams to create an intuitive portfolio management portal aligned with architectural capabilities. Orchestrated team expansion from 4 to 10 developers, establishing robust DevOps processes with CI/CD pipelines and testing frameworks. Designed and implemented a comprehensive investor asset portfolio website using cutting-edge technologies: Frontend: React, Next.JS, Storybook, Ant Design. Backend: C#.NET Core,.NET 7, Django, Python 3. Cloud Infrastructure: Azure ecosystem. Created high-performance React components with optimized rendering cycles, significantly improving user experience in data-intensive portfolio management interfaces & utilized Next.JS with React to deliver server-side rendering capabilities, improving initial load performance and SEO capabilities for client-facing applications. Developed centralized authentication and authorization framework using Azure B2C with custom policies for enhanced security. Created microservices using Django & Python 3 from Databricks imported data. Engineered complex microservices integrating Databricks data with Django APIs. Implemented advanced Azure cloud infrastructure incorporating multiple services: Azure App Service, Azure Functions, Azure SQL Database, Azure Storage, Azure DevOps & Pipelines, Azure Virtual Machines (VMs), Azure Active Directory (Azure AD), Azure Logic Apps, Azure B2C Custom Policies, Dynamics 365 Customer Service integration.",
      badges: ["React", "Next.JS", "Python", "Azure", "Django", "Architecture"],
      logoUrl: "https://ui-avatars.com/api/?name=PX&background=2563EB&color=fff",
      href: "#"
    },
    {
      company: "Actex Learning",
      title: "Lead Full Stack .NET Developer | Application Architect",
      location: "Portsmouth, NH (USA)",
      start: "04/2021",
      end: "07/2022",
      description: "Actuarial Learning helps actuarial candidates offering product GOAL; an Actuarial Exam Practice tool/textbooks, and the online learning platform Actuarial University used by many actuaries and students to help them pass their SOA and CAS Exams. Architected scalable cloud-based infrastructure leveraging Azure App Services with microservices architecture to modularize the actuarial exam platform for better scalability and maintenance. Implemented/Designed multi-platform solutions using: C# .NET 6, ASP.NET MVC Core/MVC 5, jQuery, Bootstrap, Azure App Services, R Language for custom API interfaces, Azure, SQL Server. Created innovative Azure Functions with custom R language handlers for statistical processing of actuarial data. Designed and implemented comprehensive security framework including identity management, role-based access control, and data encryption. Developed comprehensive web applications for actuarial exam preparation platform. Enhanced online learning platform increasing user engagement and exam preparation effectiveness.",
      badges: [".NET 6", "Azure", "R", "SQL Server", "Architecture"],
      logoUrl: "https://ui-avatars.com/api/?name=Actex&background=DC2626&color=fff",
      href: "#"
    },
    {
      company: "Xchanging Solutions [A DXC Technology Company]. Client: Liberty Mutual.",
      title: "Principal Software Engineer",
      location: "Dover, NH (USA)",
      start: "10/2015",
      end: "04/2021",
      description: "Liberty Mutual is a global insurance company, headquartered in Boston, MA, that offers a wide range of insurance products and services, including personal and commercial lines, and is known for its global presence and focus on risk mitigation. Created comprehensive application architecture documentation including system diagrams, data flow models, and integration specifications. Led technology stack modernization initiative, transitioning from monolithic applications to cloud-native microservices. Designed hybrid cloud architecture leveraging both Azure and AWS services for optimal performance and cost efficiency. Developed microservices/web applications using: C#, ASP.NET MVC, Web API, HTML,CSS, J-Query, Bootstrap, Azure & SQL Server & AWS cloud infrastructure. Created microservices in MuleSoft over Any Point Studio, implementing API-led connectivity approach. Successfully delivered critical SSIS project, demonstrating technical leadership and project management skills.",
      badges: [".NET", "Azure", "AWS", "MuleSoft", "Architecture"],
      logoUrl: "https://ui-avatars.com/api/?name=DXC&background=4F46E5&color=fff",
      href: "#"
    },
    {
      company: "(SYNTEL Inc.) Client: Humana",
      title: "Tech Lead/Senior Software .NET Engineer",
      location: "Louisville, KY (USA)",
      start: "05/2011",
      end: "08/2015",
      description: "Humana Inc is a US-based for-profit health insurance company offering healthcare benefits to individuals and employer groups. Developed Medicaid enrollment portal using C#.NET, ASP.NET, ASP.NET MVC, HTML, CSS, JQuery, Bootstrap, Angular and SQL Server. Led technical implementation of Obamacare healthcare enrollment systems. Created comprehensive reporting solutions: SQL Server Reporting Services (SSRS) SQL Server Integration Services (SSIS). Led technical implementation of Obamacare healthcare enrollment systems.",
      badges: [".NET", "Angular", "SQL Server", "SSRS", "SSIS"],
      logoUrl: "https://ui-avatars.com/api/?name=SYNTEL&background=059669&color=fff",
      href: "#"
    },
    {
      company: "Acty System India Pvt Ltd",
      title: "Senior Software Engineer",
      location: "Mumbai, India",
      start: "08/2009",
      end: "04/2011",
      description: "Acty System India Pvt. Ltd. is a Mumbai-based IT services company combining Indian engineering expertise with Japanese business management. Developed innovative software solutions for Japanese and Indian clients. Created Diakanri (Diamond Process Management System), a complex Windows Forms application using Observer/Singleton Design Pattern, C#, ASP.NET, Entity Framework, LINQ, and TFS for code management.",
      badges: [".NET", "Entity Framework", "LINQ", "Windows Forms"],
      logoUrl: "https://ui-avatars.com/api/?name=Acty&background=7C3AED&color=fff",
      href: "#"
    },
    {
      company: "ISF MARITIME PVT LTD",
      title: "Software Engineer",
      location: "Mumbai, India",
      start: "12/2008",
      end: "08/2009",
      description: "Created training modules, assessment modules, chat room made available by particular client to their specified users to clear ORB (Oil Record Book) & Cadet Selection Test examination. (DLP) Distance Learning Program support containing e-learning contents created by (ISF) company to the DLP users. Design, development, coding and testing in C#, ASP.NET. Developed critical training and assessment modules for maritime industry. Designed database schemas and stored procedures in SQL Server. Created e-learning content and distance learning program support.",
      badges: [".NET", "SQL Server", "C#", "ASP.NET"],
      logoUrl: "https://ui-avatars.com/api/?name=ISF&background=0EA5E9&color=fff",
      href: "#"
    }
  ],
  education: [
    {
      school: "University of Mumbai",
      degree: "Bachelor's in Computer Engineering",
      start: "2004",
      end: "2008",
      location: "Mumbai, India",
      logoUrl: "https://ui-avatars.com/api/?name=UM&background=1E40AF&color=fff"
    }
  ],
  certifications: [
    {
      title: "Microsoft Certified: Azure Developer Associate",
      organization: "Microsoft",
      date: "01/2023 - Present",
      id: "MS-AZ204",
      logoUrl: "https://ui-avatars.com/api/?name=MS&background=0078D4&color=fff"
    },
    {
      title: "Certified Ethical Hacker(CEH)-v6",
      organization: "EC Council",
      date: "06/2010 - 11/2010",
      id: "ECC944432",
      logoUrl: "https://ui-avatars.com/api/?name=EC&background=991B1B&color=fff"
    },
    {
      title: "Core Java",
      organization: "Rajesh Patkar Institute of Technology",
      date: "06/2008 - 10/2008",
      logoUrl: "https://ui-avatars.com/api/?name=RPIT&background=065F46&color=fff"
    }
  ],
  languages: [
    {
      name: "English",
      level: "Native or Bilingual Proficiency"
    },
    {
      name: "Hindi",
      level: "Native or Bilingual Proficiency"
    }
  ],
  interests: [
    "Pool",
    "Snooker",
    "Photography"
  ]
}