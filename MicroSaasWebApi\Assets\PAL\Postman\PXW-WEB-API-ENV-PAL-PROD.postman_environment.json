{"id": "63ec31f5-85c5-4ff0-9931-92782581c9f6", "name": "PXW-WEB-API-ENV-PAL-PROD", "values": [{"key": "domain", "value": "https://localhost:7163", "type": "default", "enabled": true}, {"key": "domain.uat", "value": "https://pxw-webapi-uat.azurewebsites.net", "type": "default", "enabled": true}, {"key": "profile_id", "value": "643f0db9-66c4-4e1c-9ad6-5e224ff25047", "type": "default", "enabled": true}, {"key": "account_codes", "value": "[\"5PXRYD\", \"5PXREY\", \"5PXKAD\", \"5PXWAX\", \"5PXKAR\", \"5PXMCA\", \"5PXKAE\"]", "type": "default", "enabled": true}, {"key": "TenantGuid", "value": "8d3e500c-ccae-461e-ba84-947f558ea637", "type": "default", "enabled": true}, {"key": "account_code", "value": "5PXKAR", "type": "default", "enabled": true}, {"key": "as_of_date", "value": "2022-12-20", "type": "default", "enabled": true}, {"key": "b2c_client_secret", "value": "****************************************", "type": "default", "enabled": true}, {"key": "b2c_client_id", "value": "793b8451-2ba9-4fdb-80fd-ce0556d8bd54", "type": "default", "enabled": true}, {"key": "b2c_token_url", "value": "https://prodpalb2c.b2clogin.com/prodpalb2c.onmicrosoft.com/B2C_1A_SIGNIN_TOTP/oauth2/v2.0/token", "type": "default", "enabled": true}, {"key": "b2c_auth_url", "value": "https://prodpalb2c.b2clogin.com/prodpalb2c.onmicrosoft.com/B2C_1A_SIGNIN_TOTP/oauth2/v2.0/authorize", "type": "default", "enabled": true}, {"key": "baseUrl", "value": "*******************************************/v1.0", "type": "default", "enabled": true}, {"key": "key", "value": "73ae99ddbabe441989c56b35e03b4972", "type": "default", "enabled": true}, {"key": "organization", "value": "pxw-01", "type": "default", "enabled": true}, {"key": "userEmail", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "workflowKey", "value": "a3ede38a-855d-4924-8a85-ec6a83177db4", "type": "default", "enabled": true}, {"key": "security_id", "value": "2d252487-0d15-4178-b603-d60befbafed9", "type": "default", "enabled": true}, {"key": "transaction_id", "value": "3b65fdc9-e571-4ccf-b936-9c304f14a27a", "type": "default", "enabled": true}, {"key": "security_price_id", "value": "223227dd-3b42-4a4a-a154-ce029e2079bc", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2023-12-19T22:30:27.543Z", "_postman_exported_using": "Postman/10.21.4"}