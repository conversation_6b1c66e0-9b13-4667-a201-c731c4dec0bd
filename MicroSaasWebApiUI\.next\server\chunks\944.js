exports.id=944,exports.ids=[944],exports.modules={24111:(e,t,r)=>{Promise.resolve().then(r.bind(r,71569)),Promise.resolve().then(r.bind(r,72820))},26228:(e,t,r)=>{"use strict";function a(e){let t=document.querySelectorAll("pre, code, .blog-content pre, .blog-content code, .wp-block-code, .code-block, .code-snippet, .wp-block-preformatted"),r=new Set;t.forEach(t=>{let a=t;t.parentElement&&"PRE"===t.parentElement.tagName&&(a=t.parentElement);let s=a.id||`code-block-${Math.random().toString(36).substring(2,11)}`;if(r.has(s)||(r.add(s),a instanceof HTMLElement&&(a.style.position="relative"),t.textContent?.includes("Adsense")||t.textContent?.includes("AdSense")||t.textContent?.includes("ca-pub-")))return;let o=a.querySelectorAll(".copy-code-button"),l=(t.textContent||"").split("\n").filter(e=>e.trim().length>0).length;if(0===o.length&&l>1){let e=function(e){let t=document.createElement("button");return t.className="copy-code-button",t.setAttribute("aria-label","Copy code"),t.setAttribute("type","button"),t.style.position="absolute",t.style.top="0.5rem",t.style.right="0.5rem",t.style.padding="0.25rem",t.style.backgroundColor="transparent",t.style.border="none",t.style.cursor="pointer",t.style.color="inherit",t.style.zIndex="10",t.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect><path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path></svg>',t.onclick=async r=>{r.preventDefault(),r.stopPropagation();try{await navigator.clipboard.writeText(e.textContent?.trim()||"");let r=t.innerHTML;t.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>',setTimeout(()=>{t.innerHTML=r},2e3)}catch(e){console.error("Failed to copy:",e)}},t}(t);a.appendChild(e),a instanceof HTMLElement&&"relative"!==a.style.position&&(a.style.position="relative")}a.classList&&Array.from(a.classList).forEach(e=>{(e.includes("language-")||e.includes("hljs")||e.includes("prism"))&&a.classList.remove(e)}),a instanceof HTMLElement&&("dark"===e?(a.style.backgroundColor="#0F1121",a.style.color="#e2e8f0"):(a.style.backgroundColor="#f7fafc",a.style.color="#2d3748"),a.style.border="none",a.style.borderRadius="0.375rem",a.style.padding="1rem",a.style.margin="1rem 0",a.style.overflowX="auto"),a.querySelectorAll("*").forEach(e=>{e instanceof HTMLElement&&(e.style.color="inherit",e.style.backgroundColor="transparent",e.style.fontWeight="normal",e.style.fontStyle="normal")})})}function s(e){a(e),function(e){let t=document.querySelector(".blog-content");t&&(t.classList.remove("bg-white","bg-[#1E232C]","text-gray-800","text-white","border-gray-200","border-[#373C53]","light-theme","dark-theme"),"dark"===e?(t.classList.add("bg-[#1E232C]","text-white","border-[#373C53]","dark-theme"),t.classList.remove("light-theme"),t.style.backgroundColor="#1E232C",t.style.color="white",t.style.borderColor="#373C53"):(t.classList.add("bg-white","text-gray-800","border-gray-200","light-theme"),t.classList.remove("dark-theme"),t.style.backgroundColor="white",t.style.color="#2d3748",t.style.borderColor="#e2e8f0")),document.querySelectorAll(".blog-content img").forEach(e=>{e.classList.add("max-w-full","h-auto","rounded-md"),e.onerror=function(){console.error("Image failed to load:",e.src),e.style.border="2px dashed #ff0000",e.style.padding="10px"}}),document.querySelectorAll(".blog-content p").forEach(t=>{t.classList.remove("text-gray-800","text-gray-200"),"dark"===e?t.classList.add("text-gray-200"):t.classList.add("text-gray-800")}),document.querySelectorAll(".blog-content table").forEach(e=>{e.classList.add("w-full","border-collapse","my-4"),e.style.borderSpacing="0",e.querySelectorAll("th").forEach(e=>{e.classList.add("bg-gray-100","dark:bg-gray-800","p-2","text-left","border","border-gray-300","dark:border-gray-700")}),e.querySelectorAll("td").forEach(e=>{e.classList.add("p-2","border","border-gray-300","dark:border-gray-700")})}),document.querySelectorAll(".blog-content a:not(.copy-code-button)").forEach(e=>{e.classList.add("text-blue-500","hover:underline")}),document.querySelectorAll(".blog-content h1, .blog-content h2, .blog-content h3, .blog-content h4, .blog-content h5, .blog-content h6").forEach(t=>{t.classList.remove("text-gray-900","text-white"),"dark"===e?t.classList.add("text-white","font-semibold"):t.classList.add("text-gray-900","font-semibold")}),document.querySelectorAll(".blog-content pre").forEach(t=>{t.classList.remove("bg-gray-50","bg-[#0F1121]","border-gray-200","border-[#373C53]","text-gray-200","text-gray-800","border"),"dark"===e?t.classList.add("bg-[#0F1121]","text-gray-200"):t.classList.add("bg-gray-50","text-gray-800"),t.classList.contains("p-4")||t.classList.add("p-4","rounded-md","my-4","relative"),t.style.border="none"}),document.querySelectorAll(".blog-content code:not(pre code)").forEach(t=>{t.classList.remove("bg-gray-100","bg-gray-800","text-gray-800","text-gray-200"),"dark"===e?t.classList.add("text-gray-200"):t.classList.add("text-gray-800"),t.style.border="none",t.style.backgroundColor="transparent",t.style.padding="0",t.style.margin="0 0.1rem"}),document.querySelectorAll(".blog-content blockquote").forEach(t=>{t.classList.remove("text-gray-600","text-gray-400","border-l-gray-300","border-l-gray-700"),"dark"===e?t.classList.add("text-gray-400","border-l-gray-700"):t.classList.add("text-gray-600","border-l-gray-300")})}(e),"dark"===e?(document.documentElement.classList.add("dark"),document.documentElement.classList.remove("light")):(document.documentElement.classList.add("light"),document.documentElement.classList.remove("dark")),setTimeout(()=>a(e),500),setTimeout(()=>a(e),1e3),setTimeout(()=>a(e),2e3)}r.d(t,{$:()=>s,J:()=>a})},4721:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var a=r(10326),s=r(86004);let o=({type:e,className:t="",style:r={},children:o})=>{let l=(()=>{switch(e){case"multiplex-vertical":return{position:"sidebar",slot:"5891334939",format:"autorelaxed"};case"in-feed":return{position:"in-feed",slot:"6889555982",format:"fluid"};case"in-article":return{position:"in-article",slot:"2944836887",format:"in-article"};default:return{position:"horizontal",slot:"4454393519",format:"auto"}}})();return(0,a.jsxs)("div",{className:`ad-manager-container ${"multiplex-vertical"===e?"":"w-full max-w-[90rem] mx-auto px-4 sm:px-6 lg:px-8"} ${t}`,style:r,children:[a.jsx(s.Z,{position:l.position,slot:l.slot,format:l.format}),o]})}},86004:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var a=r(10326),s=r(17577);let o=({slot:e="4454393519",format:t="auto",style:r={},className:o="",responsive:l=!0,layout:n="in-article",containerClassName:i=""})=>{let d=(0,s.useRef)(null),[c,u]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{u(!0)},[]),(0,s.useEffect)(()=>{if(c)try{l&&d.current&&(d.current.style.minWidth="300px",d.current.style.width="100%")}catch(e){console.error("Error loading Google Ads:",e)}},[c,l]),c)?a.jsx("div",{className:`${(()=>{switch(n){case"sidebar":return"adsense-container adsense-container-sidebar";case"horizontal":return"adsense-container-horizontal";default:return i||""}})()} ${o}`,children:a.jsx("ins",{ref:d,className:"adsbygoogle",style:(()=>{let e={display:"block",textAlign:"left",background:"transparent",...r};switch(n){case"sidebar":return{...e,width:"300px",height:"600px",margin:"0 auto",maxWidth:"100%"};case"horizontal":return{...e,width:"100%",minHeight:"280px"};case"in-article":return{...e,minHeight:"280px"};default:return e}})(),"data-ad-client":"ca-pub-8317058180798912","data-ad-slot":e,"data-ad-format":t,"data-full-width-responsive":l})}):a.jsx("div",{className:`ad-placeholder ${o}`,style:{minHeight:"sidebar"===n?"600px":"280px",...r}})},l=({position:e,slot:t,className:r="",style:l={},format:n})=>{let[i,d]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{d(!0)},[]),i)?a.jsx(o,{slot:t||("sidebar"===e?"5891334939":"4454393519"),format:(()=>{if(n)return n;switch(e){case"sidebar":return"autorelaxed";case"horizontal":default:return"auto";case"in-article":return"in-article";case"in-feed":return"fluid"}})(),responsive:"sidebar"!==e,layout:e,className:r,style:l}):a.jsx("div",{className:`ad-placeholder ${r}`,style:{minHeight:"sidebar"===e?"600px":"280px",...l}})}},23609:(e,t,r)=>{"use strict";r.d(t,{a:()=>a});let a=e=>{let t=new Date(e);return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"2-digit"}).format(t)}},58840:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(19510),s=r(13188),o=r(61953);async function l({children:e}){return(0,a.jsxs)("div",{children:[a.jsx(s.Z,{}),a.jsx("main",{className:"",children:e}),a.jsx("div",{className:"h-24"}),a.jsx(o.Z,{})]})}},57939:(e,t,r)=>{"use strict";r.d(t,{G:()=>n});var a=r(29712),s=r(87378),o=r.n(s);class l{constructor(){let e=process.env.WP_REST_ENDPOINT||"";this.baseUrl=e.replace(/\/+$/,"").replace("/wp-admin","").replace("/wp-json",""),this.auth={username:process.env.WORDPRESS_DB_USER||"",password:process.env.WORDPRESS_DB_PASSWORD||""},this.wp=new(o())({endpoint:`${this.baseUrl}/wp-json`,username:this.auth.username,password:this.auth.password})}getApiUrl(e){return`${this.baseUrl}/wp-json${e}`}async getImageURLById(e){try{return(await a.Z.get(this.getApiUrl(`/wp/v2/media/${e}`),{auth:this.auth,headers:{Accept:"application/json","Referrer-Policy":"unsafe-url"}})).data}catch(e){return null}}async getAllCategories(){try{let e=await a.Z.get(this.getApiUrl("/wp/v2/categories"),{auth:this.auth,headers:{Accept:"application/json","Referrer-Policy":"unsafe-url"},params:{per_page:100}});if(!e?.data)return console.error("No categories data in WordPress response"),[];return Array.isArray(e.data)?e.data:[]}catch(e){return a.Z.isAxiosError(e)&&console.error("WordPress Categories API Error:",{status:e.response?.status,url:e.config?.url}),[]}}async getAllPosts(){try{let e=await a.Z.get(this.getApiUrl("/wp/v2/posts"),{auth:this.auth,headers:{Accept:"application/json","Referrer-Policy":"unsafe-url"},params:{_fields:"id,slug,title,featured_media,date,author,categories,excerpt",per_page:100,status:"publish",orderby:"date",order:"desc"}});if(!e?.data)return console.error("No data in WordPress response"),[];let t=Array.isArray(e.data)?e.data:[];if(0===t.length)return console.error("No posts found in WordPress response"),[];return await Promise.all(t.map(async e=>{let t="";if(e.featured_media>0){let r=await this.getImageURLById(e.featured_media);t=r?.guid?.rendered||""}let r="Javian Picardo";if(e.author)try{let t=await a.Z.get(this.getApiUrl(`/wp/v2/users/${e.author}`),{auth:this.auth,headers:{Accept:"application/json","Referrer-Policy":"unsafe-url"}});t?.data?.name&&(r=t.data.name)}catch(e){console.error("Error fetching author:",e)}return{...e,image_url:t||"",author:r}}))}catch(e){return a.Z.isAxiosError(e)&&console.error("WordPress API Error:",{status:e.response?.status,url:e.config?.url}),[]}}async getPostsForSitemap(){try{let e=await a.Z.get(this.getApiUrl("/wp/v2/posts"),{auth:this.auth,headers:{Accept:"application/json"},params:{_fields:"slug,date",status:"publish"}});return Array.isArray(e.data)?e.data:[]}catch(e){return[]}}async getPost(e){try{let t=await a.Z.get(this.getApiUrl("/wp/v2/posts"),{auth:this.auth,headers:{Accept:"application/json","Referrer-Policy":"unsafe-url"},params:{slug:e,_embed:!0,status:"publish"}});if(!t.data?.length)return{title:{rendered:"Post not found"},content:{rendered:"The requested post could not be found."},excerpt:{rendered:"Post not found"},slug:e};let r=t.data[0],s="";if(r.featured_media>0){let e=await this.getImageURLById(r.featured_media);s=e?.guid?.rendered||""}let o="Javian Picardo";if(r.author)try{let e=await a.Z.get(this.getApiUrl(`/wp/v2/users/${r.author}`),{auth:this.auth,headers:{Accept:"application/json","Referrer-Policy":"unsafe-url"}});e?.data?.name&&(o=e.data.name)}catch(e){console.error("Error fetching author:",e)}return{...r,image_url:s||"",author:o}}catch(t){return{title:{rendered:"Post not found"},content:{rendered:"The requested post could not be found."},excerpt:{rendered:"Post not found"},slug:e}}}}let n=new l}};