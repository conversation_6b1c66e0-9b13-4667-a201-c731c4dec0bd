exports.id=746,exports.ids=[746],exports.modules={80715:(e,t,a)=>{let s={"3b9a958b9655aed6e24c0afca1f10edd3ae8a4fa":()=>Promise.resolve().then(a.bind(a,8413)).then(e=>e.invalidateCacheAction)};async function l(e,...t){return(await s[e]()).apply(null,t)}e.exports={"3b9a958b9655aed6e24c0afca1f10edd3ae8a4fa":l.bind(null,"3b9a958b9655aed6e24c0afca1f10edd3ae8a4fa")}},35862:(e,t,a)=>{Promise.resolve().then(a.bind(a,57472)),Promise.resolve().then(a.bind(a,16566)),Promise.resolve().then(a.bind(a,91721)),Promise.resolve().then(a.bind(a,72248)),Promise.resolve().then(a.bind(a,49690)),Promise.resolve().then(a.bind(a,60270)),Promise.resolve().then(a.bind(a,71569)),Promise.resolve().then(a.bind(a,72820)),Promise.resolve().then(a.bind(a,11012))},96183:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,12994,23)),Promise.resolve().then(a.t.bind(a,96114,23)),Promise.resolve().then(a.t.bind(a,9727,23)),Promise.resolve().then(a.t.bind(a,79671,23)),Promise.resolve().then(a.t.bind(a,41868,23)),Promise.resolve().then(a.t.bind(a,84759,23))},90211:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,79404,23))},92226:(e,t,a)=>{Promise.resolve().then(a.bind(a,16507))},16507:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var s=a(10326),l=a(90434);function i({error:e,reset:t}){return s.jsx(s.Fragment,{children:(0,s.jsxs)("div",{className:"h-screen w-full flex flex-col justify-center items-center text-center gap-6 p-6",children:[s.jsx("div",{className:"p-6 bg-white rounded-xl",children:(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg","data-name":"Layer 1",className:"w-36 h-36 md:w-64 md:h-64",viewBox:"0 0 509.04617 507.58297",children:[s.jsx("path",{fill:"#f2f2f2",d:"M504.17 454.213c-6.3 13.08-17.91 22.81-30.08 30.72a189.914 189.914 0 0 1-42.66 20.65c-2.06.71-4.14 1.37-6.23 2h-84.51c-.59-.64-1.15-1.31-1.68-2-5.66-7.25-8.72-16.54-7.61-25.64 1.43-11.69 10.31-22.43 21.81-24.91 11.51-2.49 24.63 4.38 28.13 15.63 1.92-21.68 4.14-44.26 15.66-62.72 10.44-16.71 28.51-28.67 48.09-30.81a60.558 60.558 0 0 1 33.48 6.13c.95.48 1.89.98 2.81 1.5a56.015 56.015 0 0 1 16.14 13.77c12.21 15.46 15.2 37.93 6.65 55.68Z"}),s.jsx("path",{fill:"#fff",d:"M480.76 386.133a317.08 317.08 0 0 0-90.56 119.45c-.29.66-.58 1.33-.86 2h-3.98c.28-.67.56-1.34.85-2 3.85-9 8.1-17.84 12.77-26.45a321.861 321.861 0 0 1 34.91-51.66 316.97 316.97 0 0 1 44.26-43.95 1.93 1.93 0 0 1 .42-.26 2.032 2.032 0 0 1 2.81 1.5 1.478 1.478 0 0 1-.62 1.37Z"}),s.jsx("path",{fill:"#ffb8b8",d:"M167.214 293.587a9.497 9.497 0 0 0-1.203-14.513l-4.796-31.62-19.702 6.201 10.793 28.21a9.549 9.549 0 0 0 14.908 11.722Z"}),s.jsx("path",{className:"fill-primary",d:"m146.919 278.711-12.153-56.756a32.5 32.5 0 0 1 9.281-30.393c15.556-14.99 35.55-33.885 42.455-38.772a35.606 35.606 0 0 1 22.277-6.576l.166.018 10.454 9.88-22.773 37.489-34.571 21.66 3.847 55.63Z"}),s.jsx("rect",{width:59,height:8,x:134.48,y:201.583,fill:"#ccc",rx:4}),s.jsx("rect",{width:59,height:8,x:134.48,y:288.583,fill:"#ccc",rx:4}),s.jsx("path",{fill:"#ccc",d:"M164.48 506.583a2 2 0 0 1-1.992-1.823l-25.496-299a2 2 0 1 1 3.984-.354l23.475 276.252L187.26 205.41a2 2 0 1 1 3.985.344l-24.771 299a2 2 0 0 1-1.99 1.828Z"}),s.jsx("circle",{cx:221.715,cy:108.007,r:26.239,fill:"#ffb8b8"}),s.jsx("path",{fill:"#ffb8b8",d:"m143.516 494.591-12.478-3.977 9.402-50.025 18.418 5.87-15.342 48.132z"}),s.jsx("path",{fill:"#2f2e41",d:"m142.822 505.66-40.172-12.805.332-1.043a15.829 15.829 0 0 1 19.864-10.262l25.109 8.004Z"}),s.jsx("path",{fill:"#ffb8b8",d:"M234.305 494.332h-13.097l-6.233-50.518h19.33v50.518z"}),s.jsx("path",{fill:"#2f2e41",d:"M196.45 505.994h40.162V491.09h-25.354a14.826 14.826 0 0 0-14.809 14.81ZM233.922 477.938l-4.42-.66c-13.407-1.997-14.21-2.117-16.339-3.051-1.087-.478-1.163-7.693-.51-48.256.478-29.774 1.075-66.83-.083-86.723a3.5 3.5 0 0 0-6.703-1.18c-9.976 23.037-26.712 74.59-37.787 108.704-3.852 11.866-6.895 21.239-8.82 26.857a4.524 4.524 0 0 1-5.354 2.898c-9.114-2.278-8.504-3.247-21.485-6.604a4.489 4.489 0 0 1-3.282-5.282c2.348-11.072 45.314-190.6 48.603-207.666a4.502 4.502 0 0 1 5.305-3.571c9.674 1.949 15.56 4.017 25.12 6.016 10.315 2.158 20.982 4.39 31.386 6.472a4.463 4.463 0 0 1 2.619 1.605c2.574 3.252 6.392 5.077 10.434 7.01 2.724 1.302-3.45 1.185-1.077 3.063 9.61 7.608-4.21 162.088-12.56 196.978a4.515 4.515 0 0 1-5.047 3.39ZM232.898 130.967c-1.532-6.5 6.212-20.487 4.68-26.987-1.037-4.402-11.388-1.41-13.804-5.233s-6.461-6.921-10.984-6.972c-5.198-.058-10.313 3.798-15.237 2.131-4.978-1.685-6.57-8.503-4.368-13.276s6.942-7.814 11.627-10.197c7.978-4.059 17.266-7.023 25.805-4.337 5.161 1.623 9.477 5.14 13.653 8.58 3.807 3.136 7.678 6.342 10.339 10.495 5.213 8.135 4.899 19.095.444 27.669s-12.574 14.865-21.519 18.517"}),s.jsx("path",{className:"fill-primary",d:"M168.47 282.233c2.832-4.73 7.132-26.897 12.434-64.103 3.61-25.4 6.755-51.206 8.316-64.389a6.339 6.339 0 0 1 3.56-5.001q1.518-.734 3.02-1.388c12.922-5.621 26.019-7.484 38.923-5.536a70.619 70.619 0 0 1 34.92 15.91l.117.112.03.158c.056.293 5.476 29.498-5.625 49.23-10.948 19.468-11.076 71.026-11.076 71.544v.663l-.637-.182c-17.032-4.866-82.825 3.648-83.488 3.735l-1.025.133Z"}),s.jsx("rect",{width:59,height:8,x:212.48,y:214.583,fill:"#ccc",rx:4}),s.jsx("rect",{width:59,height:8,x:212.48,y:301.583,fill:"#ccc",rx:4}),s.jsx("path",{fill:"#ccc",d:"M242.48 507.583a2 2 0 0 1-1.992-1.823l-25.496-287a2 2 0 1 1 3.984-.354l23.475 264.252L265.26 218.41a2 2 0 0 1 3.985.344l-24.771 287a2 2 0 0 1-1.99 1.828Z"}),s.jsx("path",{fill:"#ffb8b8",d:"M226.013 302.85a9.497 9.497 0 0 1 8.896-11.53l21.208-23.938 13.17 15.91-24.39 17.818a9.549 9.549 0 0 1-18.884 1.74Z"}),s.jsx("path",{className:"fill-primary",d:"m238.36 286.189 33.458-44.61-17.253-36.97 1.253-43.845 14.144-2.613.15.075a35.606 35.606 0 0 1 15.128 17.624c3.142 7.856 9.659 34.581 14.573 55.619a32.5 32.5 0 0 1-8.723 30.557l-41.043 41.043Z"}),s.jsx("path",{fill:"#ccc",d:"m98.11 495.178 45.773 11.02a3.922 3.922 0 0 0 4.555-2.125l39.32-104.542a3.221 3.221 0 0 0 .003-2.546 3.644 3.644 0 0 0-2.094-1.961l-34.415-13.143a4.139 4.139 0 0 0-3.374.187 3.511 3.511 0 0 0-1.84 2.252l-.002.005-24.29 92.138-.36.023c-.14.009-14.065.956-20.327 8.472a13.291 13.291 0 0 0-2.949 10.22Z"}),s.jsx("path",{fill:"#3f3d56",d:"M496 506.583a.997.997 0 0 1-1 1H1a1 1 0 0 1 0-2h494a.997.997 0 0 1 1 1Z"}),s.jsx("path",{fill:"#b3b3b3",d:"m131.05 438.563.21-1.989 41.733 4.42-.211 1.989zM126.801 454.875l.82-1.824 33.583 15.11-.82 1.824zM177.384 426.985l-39.583-13.11.821-1.824 39.583 13.11-.821 1.824zM181.384 417.985l-39.583-19.11.821-1.824 39.583 19.11-.821 1.824zM122.832 470.232l1.299-1.521 28.54 24.38-1.3 1.52zM114.207 477.62l1.821-.827 11.46 25.27-1.822.825z"}),s.jsx("path",{fill:"#f2f2f2",d:"M384.34 477.987V22.183c0-6.155.117-12.323 0-18.477-.005-.27 0-.539 0-.808 0-3.86-6-3.867-6 0v455.804c0 6.155-.117 12.323 0 18.477.005.27 0 .539 0 .808 0 3.86 6 3.867 6 0Z"}),s.jsx("path",{fill:"#f2f2f2",d:"M417.523 130.291h-72a6.508 6.508 0 0 1-6.5-6.5v-87.5a6.508 6.508 0 0 1 6.5-6.5h72a6.508 6.508 0 0 1 6.5 6.5v87.5a6.508 6.508 0 0 1-6.5 6.5Z"})]})}),s.jsx("p",{className:"font-medium md:text-xl md:font-semibold",children:"Something went wrong \uD83E\uDD72"}),s.jsx("p",{className:"text-red-500",children:e?.message}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-4 justify-center",children:[(0,s.jsxs)("button",{className:"btn btn-sm",onClick:t,children:[s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"w-5 h-5",children:s.jsx("path",{fillRule:"evenodd",d:"M15.312 11.424a5.5 5.5 0 01-9.201 2.466l-.312-.311h2.433a.75.75 0 000-1.5H3.989a.75.75 0 00-.75.75v4.242a.75.75 0 001.5 0v-2.43l.31.31a7 7 0 0011.712-*********** 0 00-1.449-.39zm1.23-3.723a.75.75 0 00.219-.53V2.929a.75.75 0 00-1.5 0V5.36l-.31-.31A7 7 0 003.239 8.188a.75.75 0 101.448.389A5.5 5.5 0 0113.89 6.11l.311.31h-2.432a.75.75 0 000 1.5h4.243a.75.75 0 00.53-.219z",clipRule:"evenodd"})}),"Refresh"]}),(0,s.jsxs)(l.default,{href:"/",className:"btn btn-sm",children:[s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"w-5 h-5",children:s.jsx("path",{fillRule:"evenodd",d:"M9.293 2.293a1 1 0 011.414 0l7 7A1 1 0 0117 11h-1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-3a1 1 0 00-1-1H9a1 1 0 00-1 1v3a1 1 0 01-1 1H5a1 1 0 01-1-1v-6H3a1 1 0 01-.707-1.707l7-7z",clipRule:"evenodd"})}),"Home"]})]})]})})}},71569:(e,t,a)=>{"use strict";a.d(t,{default:()=>b});var s=a(10326),l=a(46226),i=a(62763),r=a(90434),d=a(74227),o=a(12893),n=a(32019),c=a(5932),h=a(74857),m=a(81109);let x={github:o.Z,linkedin:n.Z,email:c.Z,x:h.Z,youtube:m.Z},f={src:"/_next/static/media/footer-bg1.96b003a2.svg",height:350,width:1440,blurWidth:0,blurHeight:0},p={src:"/_next/static/media/footer-bg2.0853b915.svg",height:548,width:1288,blurWidth:0,blurHeight:0},u=[{title:"Terms of services",link:"https://elegant-nightshade-79e.notion.site/Terms-Services-4335df481a50486ca26c81e699a2fbf1"},{title:"Privacy Policy",link:"https://elegant-nightshade-79e.notion.site/Privacy-policy-f2a3142bfb8c4b16b80369c19263c23a"},{title:"Licences",link:"#"}],g=[{name:"LinkedIn",link:"https://linkedin.com/in/javianpicardo",icon:x.linkedin},{name:"YouTube",link:"https://www.youtube.com/@javianpicardo",icon:x.youtube},{name:"X",link:"https://x.com/javian_picardo",icon:x.x}],b=()=>(0,s.jsxs)("div",{className:"relative flex justify-center items-center w-full",children:[s.jsx(l.default,{src:f,alt:"background",fill:!0,objectFit:"cover",className:"z-0 block dark:hidden"}),s.jsx(l.default,{src:p,alt:"background",fill:!0,objectFit:"cover",className:"z-0 hidden dark:block"}),s.jsx("div",{className:"relative z-10 max-w-[1440px] w-full h-full px-4 sm:px-12 py-12",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 h-full gap-x-4 gap-y-12",children:[s.jsx("div",{className:"flex flex-col !min-h-[inherit] justify-between gap-8",children:s.jsx(r.default,{href:"/",children:s.jsx(i.TR,{})})}),(0,s.jsxs)("div",{className:"sm:order-3 xl:order-2",children:[s.jsx("p",{className:"uppercase text-[#86898E] dark:text-[#808389] font-semibold text-base mb-8",children:"Legal"}),s.jsx(d.Z,{nav_links:u,isFooter:!0})]}),(0,s.jsxs)("div",{className:"sm:order-2 xl:order-last",children:[s.jsx("p",{className:"uppercase text-[#86898E] dark:text-[#808389] font-semibold text-base mb-8",children:"Connect With Me"}),s.jsx("div",{className:"flex items-center gap-4",children:g.map(e=>s.jsx(r.default,{href:e.link,target:"_blank",rel:"noopener noreferrer",children:s.jsx("div",{className:"shadow-lg bg-white h-[32px] w-[32px] flex items-center justify-center rounded-[8px] hover:bg-gray-100 transition-colors",children:s.jsx(e.icon,{className:"h-5 w-5 text-zinc-600"})})},e.name))})]})]})})]})},72820:(e,t,a)=>{"use strict";a.d(t,{default:()=>P});var s=a(10326),l=a(62763),i=a(91664),r=a(66775),d=a(46226),o=a(90434),n=a(35047);let c=({text:e="Login/SignUp",extraStyle:t})=>{let a=(0,n.useRouter)(),{isSignedIn:l,user:c}=(0,r.aF)(),{openSignIn:h,signOut:m}=(0,r.ll)();return l&&c?(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsxs)("div",{className:`flex items-center gap-2 cursor-pointer rounded-full bg-[#006fee] text-white px-4 py-1.5 text-sm font-medium hover:bg-blue-700 transition-all duration-300 ${t||""}`,children:[c.hasImage?s.jsx(d.default,{src:c.imageUrl,alt:c.firstName||"Account",className:"w-6 h-6 rounded-full shrink-0",referrerPolicy:"no-referrer",width:24,height:24}):s.jsx("span",{className:"w-6 h-6 bg-base-300 flex justify-center items-center rounded-full shrink-0",children:c.firstName?c.firstName.charAt(0):c.primaryEmailAddress?.emailAddress||"A"}),c.firstName||c.primaryEmailAddress?.emailAddress||"Account"]}),s.jsx("div",{className:"absolute right-0 mt-2 w-48 bg-white dark:bg-[#1E232C] border border-gray-200 dark:border-[#373C53] rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50",children:(0,s.jsxs)("div",{className:"py-1",children:[s.jsx(o.default,{href:"/",className:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-[#373C53]",children:"Home"}),s.jsx("div",{className:"border-t border-gray-200 dark:border-[#373C53] my-1"}),s.jsx("button",{className:"block w-full text-left px-4 py-2 text-sm text-red-500 hover:bg-gray-100 dark:hover:bg-[#373C53]",onClick:()=>m(()=>a.push("/")),children:"Logout"})]})})]}):s.jsx(i.z,{className:`bg-[#006fee] border-none transition-all duration-300 rounded-full px-5 py-1.5 h-8 text-sm font-medium hover:bg-blue-700 ${t||""}`,onClick:()=>{l?a.push("/"):h({redirectUrl:"/"})},children:e})};var h=a(74227),m=a(17577),x=a(85477),f=a(79360),p=a(12358);let u=x.fC,g=x.xz;x.x8;let b=x.h_,v=m.forwardRef(({className:e,...t},a)=>s.jsx(x.aV,{className:(0,p.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:a}));v.displayName=x.aV.displayName;let j=(0,f.j)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),w=m.forwardRef(({side:e="right",className:t,children:a,...l},i)=>(0,s.jsxs)(b,{children:[s.jsx(v,{}),(0,s.jsxs)(x.VY,{ref:i,className:(0,p.cn)(j({side:e}),t),...l,children:[a,(0,s.jsxs)(x.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[s.jsx("p",{className:"outline-none",children:"✕"}),s.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));w.displayName=x.VY.displayName;let C=({className:e,...t})=>s.jsx("div",{className:(0,p.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...t});C.displayName="SheetHeader";let k=m.forwardRef(({className:e,...t},a)=>s.jsx(x.Dx,{ref:a,className:(0,p.cn)("text-lg font-semibold text-foreground",e),...t}));k.displayName=x.Dx.displayName,m.forwardRef(({className:e,...t},a)=>s.jsx(x.dk,{ref:a,className:(0,p.cn)("text-sm text-muted-foreground",e),...t})).displayName=x.dk.displayName;var y=a(41339),N=a(14831);let Z=[{icon:s.jsx(y.l3,{width:18,height:18}),title:"Blog",link:"/blog"}],M=()=>{let{setTheme:e,theme:t}=(0,N.F)();return(0,m.useEffect)(()=>{"light"!==t&&"dark"!==t&&e("light"),"true"!==sessionStorage.getItem("themeInitialized")&&sessionStorage.setItem("themeInitialized","true"),"dark"===t?(document.documentElement.classList.add("dark"),document.documentElement.classList.remove("light"),document.body.style.backgroundColor="#010814",document.body.style.color="white"):(document.documentElement.classList.add("light"),document.documentElement.classList.remove("dark"),document.body.style.backgroundColor="white",document.body.style.color="black")},[t,e]),s.jsx("div",{className:"flex items-center gap-2",children:s.jsx("button",{type:"button",onClick:()=>{let a="dark"===t?"light":"dark";console.log("Theme toggled to:",a),e(a),window.dispatchEvent(new CustomEvent("themeChange",{detail:{theme:a}}))},className:`flex items-center justify-center w-8 h-8 rounded-md transition-colors ${"light"===t?"bg-blue-100 text-blue-800":"bg-gray-700 text-yellow-300"}`,"aria-label":`Currently in ${"light"===t?"light":"dark"} mode. Click to switch to ${"light"===t?"dark":"light"} mode`,title:`Currently in ${"light"===t?"light":"dark"} mode. Click to switch to ${"light"===t?"dark":"light"} mode`,children:"light"===t?s.jsx(y.kO,{className:"w-5 h-5"}):s.jsx(y.JF,{className:"w-5 h-5"})})})},L=()=>{let{isSignedIn:e,user:t}=(0,r.aF)(),{signOut:a}=(0,r.ll)(),i=(0,n.useRouter)();return(0,s.jsxs)(u,{children:[s.jsx(g,{children:s.jsx("div",{className:"text-black1 dark:text-white",children:s.jsx(y.qR,{})})}),(0,s.jsxs)(w,{className:"bg-white dark:bg-black1 px-0 pt-4 border-l-0 min-w-[320px]",children:[s.jsx(C,{children:s.jsx(k,{className:"text-black1 dark:text-white text-xl font-bold border-b border-[#b3b3b3] text-left pb-4 pl-4",children:"Menu"})}),s.jsx(o.default,{href:"/",className:"flex items-center gap-2 mt-8 mx-auto w-fit",children:s.jsx(l.TR,{})}),s.jsx("div",{className:"my-8 mx-auto w-fit",children:s.jsx(h.Z,{nav_links:Z})}),e&&t?(0,s.jsxs)("div",{className:"flex flex-col gap-4 mb-8 mx-auto w-fit",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-4 text-black1 dark:text-white",children:[t.hasImage?s.jsx(d.default,{src:t.imageUrl,alt:t.firstName||"Account",className:"w-6 h-6 rounded-full shrink-0",referrerPolicy:"no-referrer",width:24,height:24}):s.jsx("span",{className:"w-6 h-6 bg-base-300 flex justify-center items-center rounded-full shrink-0",children:t.firstName?t.firstName.charAt(0):t.primaryEmailAddress?.emailAddress||"A"}),s.jsx("span",{className:"font-medium",children:t.firstName||t.primaryEmailAddress?.emailAddress||"Account"})]}),(0,s.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-4",children:[s.jsx(o.default,{href:"/",className:"block text-black1 dark:text-white hover:underline py-2",children:"Home"}),s.jsx(o.default,{href:"https://accounts.clerk.com/account",target:"_blank",rel:"noopener noreferrer",className:"block text-black1 dark:text-white hover:underline py-2",children:"Manage Profile"}),s.jsx("button",{className:"text-red-500 hover:underline text-left block w-full py-2",onClick:()=>a(()=>i.push("/")),children:"Logout"})]})]}):s.jsx("div",{className:"mb-8 mx-auto w-fit block",children:s.jsx(c,{text:"Login/SignUp"})})]})]})},P=()=>s.jsx("div",{className:"w-full fixed top-0 z-50 bg-white dark:bg-[#010814] border-b border-gray-200 dark:border-gray-800 shadow-sm",children:(0,s.jsxs)("div",{className:"container mx-auto max-w-[1440px] w-full flex justify-between items-center gap-4 px-4 sm:px-12 py-1.5",children:[s.jsx(o.default,{href:"/",className:"flex items-center",children:s.jsx(l.TR,{})}),(0,s.jsxs)("div",{className:"flex items-center gap-6",children:[s.jsx("div",{className:"hidden lg:flex items-center justify-end",children:s.jsx(h.Z,{nav_links:Z})}),(0,s.jsxs)("div",{className:"hidden lg:flex items-center gap-4",children:[s.jsx("div",{dangerouslySetInnerHTML:{__html:'<a href="https://www.buymeacoffee.com/javian" target="_blank"><img src="https://cdn.buymeacoffee.com/buttons/v2/default-yellow.png" alt="Buy Me A Coffee" style="height: 32px !important;width: auto !important;" ></a>'}}),s.jsx(M,{}),s.jsx(c,{})]}),(0,s.jsxs)("div",{className:"lg:hidden flex gap-2",children:[s.jsx(M,{}),s.jsx(L,{})]})]})]})})},14160:(e,t,a)=>{"use strict";a.d(t,{Z:()=>l});var s=a(10326);let l=({text:e,isLeft:t,icon:a,isDownload:l,isDisable:i,isSubmit:r=!0,isLoading:d})=>s.jsx("button",{type:r?"submit":"button",className:"text-white font-medium whitespace-nowrap text-sm px-5 py-1.5 h-8 rounded-full w-full transition-all duration-300 bg-[#006fee] hover:bg-blue-700",disabled:d,children:d?s.jsx("div",{className:"flex justify-center",children:s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"animate-spin",children:s.jsx("path",{d:"M21 12a9 9 0 1 1-6.219-8.56"})})}):(0,s.jsxs)("span",{className:`flex items-center justify-center ${a?"gap-2":""}`,children:[t&&s.jsx("span",{className:`${l?"rotate-90":""}`,children:a}),s.jsx("span",{children:e}),!t&&!i&&s.jsx("span",{className:`${l?"rotate-90":""}`,children:a})]})})},62763:(e,t,a)=>{"use strict";a.d(t,{TR:()=>m}),a(14160);var s=a(10326),l=a(41339);a(40381),a(35047),a(39642),a(87296);var i=a(17577),r=a(85477),d=a(12358);r.fC,r.xz;let o=r.h_;r.x8;let n=i.forwardRef(({className:e,...t},a)=>s.jsx(r.aV,{ref:a,className:(0,d.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));n.displayName=r.aV.displayName,i.forwardRef(({className:e,children:t,...a},l)=>(0,s.jsxs)(o,{children:[s.jsx(n,{}),(0,s.jsxs)(r.VY,{ref:l,className:(0,d.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border-none bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a,children:[t,s.jsx(r.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"})]})]})).displayName=r.VY.displayName,i.forwardRef(({className:e,...t},a)=>s.jsx(r.Dx,{ref:a,className:(0,d.cn)("text-lg font-semibold leading-none tracking-tight",e),...t})).displayName=r.Dx.displayName,i.forwardRef(({className:e,...t},a)=>s.jsx(r.dk,{ref:a,className:(0,d.cn)("text-sm text-muted-foreground",e),...t})).displayName=r.dk.displayName;let c={src:"/_next/static/media/logo.9487aadc.svg",height:1200,width:1200,blurWidth:0,blurHeight:0};var h=a(46226);l.eM,l.BB;let m=({isLarge:e=!1})=>(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[s.jsx(h.default,{src:c,width:24,height:24,layout:"lazy",alt:"logo",className:`${e?"hidden":""}`}),(0,s.jsxs)("div",{className:"flex gap-1 items-center",children:[s.jsx("p",{className:`text-black1 dark:text-white ${e?"text-[20px] sm:text-[32px] leading-[20px] sm:leading-[32px] font-bold":"text-[20px] leading-[20px] font-semibold"}`,children:"Javian"}),s.jsx("p",{className:`text-white dark:text-black1 bg-black dark:bg-white font-semibold px-2 rounded-[3px] pb-[3px] pt-[2px] ${e?"text-[20px] sm:text-[32px] leading-[20px] sm:leading-[32px]":"text-[20px] leading-[20px]"}`,children:"Picardo"})]})]})},74227:(e,t,a)=>{"use strict";a.d(t,{Z:()=>d});var s=a(10326);let l=e=>{let t=document.getElementById(e);t&&setTimeout(()=>{t.scrollIntoView({behavior:"smooth",block:"start"})},200)};var i=a(90434),r=a(35047);let d=({nav_links:e,isFooter:t=!1})=>{let a=(0,r.useRouter)(),d=(0,r.usePathname)(),o=(e,t)=>{e.download||("/"===d&&e?.link==="/"?l(t.toString()):(a.push(e?.link),setTimeout(()=>{l(t.toString())},1500)))};return s.jsx("div",{className:`flex ${t?"flex-col":"flex-col lg:flex-row lg:items-center justify-end"} gap-x-6 gap-y-4`,children:e?.map((e,a)=>"/"===d&&e?.link==="/"?s.jsxs("p",{className:`text-black1 dark:text-white cursor-pointer hover:text-[#006fee] dark:hover:text-[#006fee] transition-colors duration-300 ease-in-out ${t?"font-medium":"text-center flex items-center font-medium text-base gap-2"}`,style:{lineHeight:"normal"},onClick:()=>o(e,a),children:[s.jsx("span",{children:e?.icon})," ",e?.title]},a):s.jsxs(i.default,{href:e?.link,download:e?.download,className:`text-black1 dark:text-white cursor-pointer hover:text-[#006fee] dark:hover:text-[#006fee] transition-colors duration-300 ease-in-out ${t?"font-medium":"text-center flex items-center font-medium text-base gap-2"}`,style:{lineHeight:"normal"},onClick:()=>o(e,a),children:[s.jsx("span",{children:e?.icon})," ",e?.title]},a))})}},11012:(e,t,a)=>{"use strict";a.d(t,{Providers:()=>d});var s=a(10326),l=a(14831),i=a(40381),r=a(49389);function d({children:e}){return(0,s.jsxs)(s.Fragment,{children:[s.jsx(l.f,{attribute:"class",defaultTheme:"light",enableSystem:!1,disableTransitionOnChange:!0,children:s.jsx("div",{className:"min-h-screen bg-background",children:e})}),s.jsx(i.x7,{position:"bottom-center",toastOptions:{duration:3e3,className:"text-sm dark:bg-black dark:text-white"}}),s.jsx(r.u,{id:"tooltip",className:"z-[60] !opacity-100 max-w-sm shadow-lg"})]})}},91664:(e,t,a)=>{"use strict";a.d(t,{z:()=>n});var s=a(10326),l=a(17577),i=a(34214),r=a(79360),d=a(12358);let o=(0,r.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-white transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 dark:ring-offset-slate-950 dark:focus-visible:ring-slate-300",{variants:{variant:{default:"bg-slate-900 text-slate-50 hover:bg-slate-900/90 dark:bg-slate-50 dark:text-slate-900 dark:hover:bg-slate-50/90",destructive:"bg-red-500 text-slate-50 hover:bg-red-500/90 dark:bg-red-900 dark:text-slate-50 dark:hover:bg-red-900/90",outline:"border border-slate-200 bg-white hover:bg-slate-100 hover:text-slate-900 dark:border-slate-800 dark:bg-slate-950 dark:hover:bg-slate-800 dark:hover:text-slate-50",secondary:"bg-slate-100 text-slate-900 hover:bg-slate-100/80 dark:bg-slate-800 dark:text-slate-50 dark:hover:bg-slate-800/80",ghost:"hover:bg-slate-100 hover:text-slate-900 dark:hover:bg-slate-800 dark:hover:text-slate-50",link:"text-slate-900 underline-offset-4 hover:underline dark:text-slate-50"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),n=l.forwardRef(({className:e,variant:t,size:a,asChild:l=!1,...r},n)=>{let c=l?i.g7:"button";return s.jsx(c,{className:(0,d.cn)(o({variant:t,size:a,className:e})),ref:n,...r})});n.displayName="Button"},12358:(e,t,a)=>{"use strict";a.d(t,{cn:()=>i});var s=a(41135),l=a(31009);function i(...e){return(0,l.m6)((0,s.W)(e))}},41339:(e,t,a)=>{"use strict";a.d(t,{l3:()=>o,BB:()=>h,JF:()=>r,wc:()=>n,qR:()=>l,kO:()=>d,dR:()=>i,eM:()=>c});var s=a(10326);let l=()=>(0,s.jsxs)("svg",{width:"32",height:"33",viewBox:"0 0 32 33",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[s.jsx("path",{d:"M6.66663 7.1665H25.3333",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round"}),s.jsx("path",{d:"M6.66663 16.5H25.3333",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round"}),s.jsx("path",{d:"M6.66663 25.8333H25.3333",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round"})]}),i=({width:e=19,height:t=10})=>(0,s.jsxs)("svg",{width:e,height:t,viewBox:"0 0 21 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[s.jsx("path",{d:"M10.8924 18.7292C15.4847 18.7292 19.2076 15.0063 19.2076 10.414C19.2076 5.8216 15.4847 2.09875 10.8924 2.09875C6.3 2.09875 2.57715 5.8216 2.57715 10.414C2.57715 15.0063 6.3 18.7292 10.8924 18.7292Z",stroke:"#1AAB12",strokeWidth:"1.66304",strokeLinecap:"round",strokeLinejoin:"round"}),s.jsx("path",{d:"M8.39746 10.4133L10.0605 12.0763L13.3866 8.75024",stroke:"#1AAB12",strokeWidth:"1.66304",strokeLinecap:"round",strokeLinejoin:"round"})]}),r=({className:e})=>s.jsx("svg",{width:"17",height:"17",viewBox:"0 0 17 17",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:s.jsx("path",{d:"M8.36364 16.7273C12.9829 16.7273 16.7273 12.9829 16.7273 8.36364C16.7273 7.9764 16.1468 7.912 15.9469 8.24404C15.5206 8.95021 14.9392 9.55011 14.2466 9.99828C13.5541 10.4465 12.7687 10.7312 11.9498 10.8308C11.131 10.9305 10.3002 10.8425 9.52037 10.5735C8.74056 10.3046 8.03218 9.86167 7.44889 9.27838C6.8656 8.69509 6.4227 7.98672 6.15373 7.20691C5.88476 6.42709 5.79678 5.5963 5.89645 4.77745C5.99611 3.9586 6.28082 3.17317 6.72899 2.48064C7.17717 1.78812 7.77706 1.20667 8.48324 0.780327C8.81527 0.5796 8.75087 0 8.36364 0C3.7444 0 0 3.7444 0 8.36364C0 12.9829 3.7444 16.7273 8.36364 16.7273Z",fill:"currentColor"})}),d=({className:e})=>s.jsx("svg",{width:"17",height:"17",viewBox:"0 0 21 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:s.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.1786 17.2054V20.0179C11.1786 20.2151 11.0187 20.375 10.8214 20.375H9.92857C9.73132 20.375 9.57143 20.2151 9.57143 20.0179V17.2054C9.92512 17.242 10.193 17.2604 10.375 17.2604C10.557 17.2604 10.8249 17.242 11.1786 17.2054ZM15.7601 14.6237L17.7488 16.6124C17.8883 16.7519 17.8883 16.978 17.7488 17.1175L17.1175 17.7488C16.978 17.8883 16.7519 17.8883 16.6124 17.7488L14.6237 15.76C14.8997 15.5359 15.102 15.3594 15.2308 15.2307C15.3595 15.102 15.5359 14.8997 15.7601 14.6236M4.98993 14.6236C5.2141 14.8997 5.39054 15.102 5.51924 15.2307C5.64796 15.3594 5.85033 15.5359 6.12634 15.76L4.13761 17.7488C3.99815 17.8883 3.77199 17.8883 3.63254 17.7488L3.00118 17.1174C2.86172 16.978 2.86172 16.7518 3.00118 16.6124L4.98993 14.6236ZM10.375 5.15179C13.2597 5.15179 15.5982 7.49029 15.5982 10.375C15.5982 13.2597 13.2597 15.5982 10.375 15.5982C7.49029 15.5982 5.15179 13.2597 5.15179 10.375C5.15179 7.49029 7.49029 5.15179 10.375 5.15179ZM3.54464 9.57143C3.50798 9.92512 3.48964 10.193 3.48964 10.375C3.48964 10.557 3.50798 10.8249 3.54464 11.1786H0.732143C0.534888 11.1786 0.375 11.0187 0.375 10.8214V9.92857C0.375 9.73132 0.534888 9.57143 0.732143 9.57143H3.54464ZM20.0179 9.57143C20.2151 9.57143 20.375 9.73132 20.375 9.92857V10.8214C20.375 11.0187 20.2151 11.1786 20.0179 11.1786H17.2054C17.242 10.8249 17.2604 10.557 17.2604 10.375C17.2604 10.193 17.242 9.92512 17.2054 9.57143H20.0179ZM17.1175 2.97886L17.7488 3.61022C17.8883 3.74969 17.8883 3.97583 17.7488 4.11529L15.76 6.10402C15.5359 5.82801 15.3594 5.62564 15.2307 5.49692C15.102 5.36821 14.8997 5.19178 14.6236 4.96761L16.6124 2.97886C16.7518 2.8394 16.978 2.8394 17.1174 2.97886M4.13759 2.97886L6.12632 4.96761C5.85031 5.19178 5.64794 5.36821 5.51922 5.49692C5.39051 5.62564 5.21408 5.82801 4.98991 6.10402L3.00116 4.11529C2.8617 3.97583 2.8617 3.74967 3.00116 3.61022L3.63252 2.97886C3.77199 2.8394 3.99813 2.8394 4.13759 2.97886ZM10.8214 0.375C11.0187 0.375 11.1786 0.534888 11.1786 0.732143V3.54464C10.8249 3.50798 10.557 3.48964 10.375 3.48964C10.193 3.48964 9.92512 3.50798 9.57143 3.54464V0.732143C9.57143 0.534888 9.73132 0.375 9.92857 0.375H10.8214Z",fill:"currentColor"})}),o=({height:e=32,width:t=32})=>(0,s.jsxs)("svg",{width:e,height:t,viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[s.jsx("path",{d:"M4.12597 0H23.6246C23.5873 0.0346657 23.5513 0.0706661 23.5153 0.106666L21.4886 2.13333H4.12603C2.94738 2.13333 1.9927 3.088 1.9927 4.26666V8.00015H15.6221L13.4888 10.1335H1.9927V27.7333C1.9927 28.9119 2.94738 29.8666 4.12603 29.8666H27.5927C28.7713 29.8666 29.726 28.9119 29.726 27.7333V10.3707L31.7526 8.34405C31.7886 8.30805 31.8246 8.27205 31.8593 8.23472V27.7333C31.8593 30.0893 29.9486 32 27.5927 32H4.12603C1.77005 32 -0.140625 30.0893 -0.140625 27.7333V4.26672C-0.140625 1.91074 1.76999 0 4.12597 0Z",fill:"currentColor"}),s.jsx("path",{d:"M6.25821 15.4668H12.2861L12.1408 16.7721C12.1101 17.0521 12.1235 17.3308 12.1781 17.6001H6.25807C5.66873 17.6001 5.19141 17.1228 5.19141 16.5335C5.19141 15.9441 5.66886 15.4668 6.25821 15.4668Z",fill:"currentColor"}),s.jsx("path",{d:"M5.19141 22.9329C5.19141 22.3435 5.66873 21.8662 6.25807 21.8662H25.458C26.0474 21.8662 26.5247 22.3435 26.5247 22.9329C26.5247 23.5222 26.0474 23.9995 25.458 23.9995H6.25807C5.66873 23.9995 5.19141 23.5222 5.19141 22.9329Z",fill:"currentColor"}),s.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M30.6224 1.23699C28.9718 -0.41233 26.297 -0.41233 24.6477 1.23699L14.5972 11.2875C14.4265 11.4582 14.3185 11.6822 14.2918 11.9235L13.7332 16.949C13.6972 17.2703 13.8092 17.5916 14.0385 17.821C14.2679 18.0503 14.5892 18.1623 14.9105 18.1263L19.936 17.5676C20.1773 17.541 20.4013 17.433 20.572 17.2623L30.6225 7.21178C32.2718 5.56246 32.2717 2.88767 30.6224 1.23699ZM26.1557 2.745C26.973 1.92901 28.297 1.92901 29.1144 2.745C29.9304 3.56232 29.9304 4.88631 29.1144 5.70367L19.3277 15.489L16.001 15.8583L16.3704 12.5317L26.1557 2.745Z",fill:"currentColor"})]}),n=()=>(0,s.jsxs)("svg",{width:"18",height:"18",viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[s.jsx("path",{d:"M9 16.5C13.1421 16.5 16.5 13.1421 16.5 9C16.5 4.85786 13.1421 1.5 9 1.5C4.85786 1.5 1.5 4.85786 1.5 9C1.5 13.1421 4.85786 16.5 9 16.5Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),s.jsx("path",{d:"M6.77154 12.1763L9.00249 9.93983L11.232 12.177L11.2319 12.1771L11.234 12.1791C11.3578 12.2947 11.5214 12.3577 11.6906 12.3547C11.8598 12.3517 12.0212 12.283 12.1407 12.163C12.2603 12.0431 12.3287 11.8813 12.3317 11.7118C12.3347 11.5424 12.272 11.3783 12.1567 11.2542L12.1568 11.2542L12.1548 11.2522L9.9264 9.01457L12.155 6.77824C12.2171 6.71798 12.2666 6.64597 12.3008 6.56639C12.335 6.48657 12.353 6.40072 12.3538 6.31386C12.3546 6.22699 12.3381 6.14082 12.3054 6.06039C12.2726 5.97996 12.2242 5.90686 12.163 5.84537C12.1017 5.78387 12.0289 5.73521 11.9487 5.70225C11.8684 5.66928 11.7825 5.65266 11.6958 5.65338C11.6091 5.65409 11.5234 5.67212 11.4437 5.70641C11.3643 5.7406 11.2924 5.79027 11.2323 5.85252L9.00253 8.08851L6.77406 5.85304C6.71452 5.78923 6.64283 5.73801 6.5632 5.70242C6.48311 5.66663 6.39665 5.64737 6.30898 5.64582C6.2213 5.64427 6.13422 5.66045 6.05292 5.69339C5.97163 5.72633 5.8978 5.77535 5.83583 5.83751C5.77386 5.89968 5.72502 5.97371 5.69222 6.05518C5.65941 6.13666 5.6433 6.22393 5.64485 6.31177C5.64639 6.39961 5.66556 6.48625 5.70121 6.56652C5.73666 6.64633 5.78769 6.71822 5.85129 6.77795L8.07863 9.01452L5.8488 11.2514L5.84876 11.2513L5.8468 11.2534C5.73153 11.3775 5.66882 11.5416 5.6718 11.7111C5.67479 11.8806 5.74323 12.0423 5.86279 12.1623C5.98236 12.2822 6.14372 12.351 6.3129 12.3539C6.48208 12.3569 6.64576 12.2939 6.76948 12.1783L6.76951 12.1783L6.77154 12.1763Z",fill:"currentColor",stroke:"currentColor",strokeWidth:"0.163636"})]}),c=()=>(0,s.jsxs)("svg",{width:"27",height:"27",viewBox:"0 0 27 27",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsxs)("g",{"clip-path":"url(#clip0_206_3026)",children:[s.jsx("path",{d:"M1.45289 0.461914H25.9211C26.469 0.461914 26.9131 0.906025 26.9131 1.45387V25.9221C26.9131 26.47 26.469 26.9141 25.9211 26.9141H1.45289C0.905049 26.9141 0.460938 26.47 0.460938 25.9221V1.45387C0.460938 0.906025 0.905049 0.461914 1.45289 0.461914Z",fill:"white"}),s.jsx("path",{d:"M1.45289 0.461914H25.9211C26.469 0.461914 26.9131 0.906025 26.9131 1.45387V25.9221C26.9131 26.47 26.469 26.9141 25.9211 26.9141H1.45289C0.905049 26.9141 0.460938 26.47 0.460938 25.9221V1.45387C0.460938 0.906025 0.905049 0.461914 1.45289 0.461914ZM15.1653 14.5345V12.365H5.75116V14.5345H9.11221V24.1942H11.7877V14.5345H15.1653ZM16.232 23.9391C16.6635 24.1591 17.1738 24.3241 17.763 24.4341C18.3521 24.5441 18.973 24.5991 19.6257 24.5991C20.2619 24.5991 20.8662 24.5386 21.4387 24.4176C22.0113 24.2966 22.5133 24.0972 22.9448 23.8195C23.3762 23.5417 23.7178 23.1788 23.9695 22.7306C24.2212 22.2823 24.347 21.7282 24.347 21.0682C24.347 20.5898 24.2751 20.1705 24.1313 19.8102C23.9875 19.45 23.78 19.1296 23.509 18.8491C23.238 18.5686 22.9129 18.317 22.534 18.0943C22.1551 17.8716 21.7278 17.6612 21.252 17.4632C20.9036 17.3202 20.591 17.1813 20.3144 17.0466C20.0379 16.9119 19.8027 16.7743 19.6091 16.6341C19.4155 16.4938 19.2662 16.3454 19.1611 16.1886C19.056 16.032 19.0034 15.8545 19.0034 15.6565C19.0034 15.475 19.0505 15.3114 19.1445 15.1657C19.2385 15.02 19.3713 14.8948 19.5428 14.7903C19.7142 14.6858 19.9244 14.6048 20.1734 14.547C20.4223 14.4893 20.6989 14.4604 21.0032 14.4604C21.2243 14.4604 21.4581 14.4769 21.7043 14.5098C21.9505 14.5429 22.198 14.5938 22.4468 14.6624C22.6958 14.7312 22.9378 14.8178 23.173 14.9224C23.4081 15.0269 23.6252 15.1478 23.8243 15.2853V12.8187C23.4205 12.6647 22.9793 12.5505 22.5009 12.4764C22.0223 12.4021 21.4733 12.365 20.8538 12.365C20.2231 12.365 19.6257 12.4323 19.0615 12.5671C18.4972 12.7018 18.0008 12.9122 17.5721 13.1982C17.1434 13.4842 16.8046 13.8485 16.5556 14.2912C16.3067 14.734 16.1823 15.2633 16.1823 15.8792C16.1823 16.6658 16.4105 17.3368 16.8669 17.8922C17.3232 18.4476 18.016 18.9179 18.9454 19.3029C19.3104 19.4514 19.6507 19.5971 19.9659 19.7401C20.2812 19.8831 20.5537 20.0316 20.7832 20.1856C21.0128 20.3396 21.1939 20.5073 21.3267 20.6888C21.4595 20.8703 21.5259 21.0765 21.5259 21.3075C21.5259 21.478 21.4844 21.6361 21.4014 21.7818C21.3185 21.9276 21.1926 22.0541 21.0239 22.1613C20.8551 22.2686 20.6449 22.3525 20.3932 22.4129C20.1415 22.4735 19.847 22.5037 19.5095 22.5037C18.9343 22.5037 18.3646 22.4034 17.8003 22.2026C17.2361 22.0019 16.7133 21.7008 16.232 21.2993V23.9391Z",fill:"#3178C6"})]}),s.jsx("defs",{children:s.jsx("clipPath",{id:"clip0_206_3026",children:s.jsx("rect",{width:"26.4521",height:"26.4521",fill:"white",transform:"translate(0.460938 0.461914)"})})})]}),h=()=>s.jsx("svg",{width:"28",height:"22",viewBox:"0 0 28 22",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M14.0013 21.1937C12.2 19.9257 10.1146 19.1177 7.92797 18.8457C6.3771 18.5306 4.79706 18.3818 3.21464 18.4017C3.01216 18.4034 2.81133 18.3651 2.62376 18.2888C2.43618 18.2125 2.26557 18.0999 2.12178 17.9573C1.97798 17.8148 1.86384 17.6451 1.78595 17.4582C1.70806 17.2713 1.66796 17.0708 1.66797 16.8683L1.69597 2.34435C1.69512 1.95464 1.84268 1.57924 2.10867 1.29443C2.37466 1.00961 2.73912 0.836766 3.12797 0.811012C4.73883 0.778991 6.34836 0.922955 7.92797 1.24035C10.1123 1.52215 12.196 2.32819 14.0013 3.58968M14.0013 21.1937V3.58968M14.0013 21.1937C15.8026 19.9257 17.888 19.1177 20.0746 18.8457C21.6255 18.5306 23.2055 18.3818 24.788 18.4017C24.9904 18.4034 25.1913 18.3651 25.3788 18.2888C25.5664 18.2125 25.737 18.0999 25.8808 17.9573C26.0246 17.8148 26.1388 17.6451 26.2167 17.4582C26.2945 17.2713 26.3346 17.0708 26.3346 16.8683L26.3053 2.34435C26.3062 1.95486 26.1588 1.57964 25.8931 1.29486C25.6274 1.01008 25.2632 0.83709 24.8746 0.811012C23.2638 0.778991 21.6542 0.922955 20.0746 1.24035C17.8903 1.52215 15.8066 2.32819 14.0013 3.58968",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})});a(17577)},7629:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(68570).createProxy)(String.raw`C:\Projects\PersonalPortal\src\app\error.tsx#default`)},8130:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>f,metadata:()=>x,viewport:()=>m});var s=a(19510),l=a(67077),i=a.n(l);let r=(0,a(68570).createProxy)(String.raw`C:\Projects\PersonalPortal\src\components\providers.tsx#Providers`);var d=a(7264),o=a(7311),n=a(36674),c=a(25787);a(21946);var h=a(13188);a(61953);let m={themeColor:"#000000",width:"device-width",initialScale:1},x=(0,d.A)();function f({children:e}){return(0,s.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,s.jsxs)("head",{children:[s.jsx("script",{async:!0,src:"https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-8317058180798912",crossOrigin:"anonymous"}),s.jsx("meta",{name:"referrer",content:"strict-origin-when-cross-origin"}),s.jsx("meta",{httpEquiv:"Content-Security-Policy",content:"upgrade-insecure-requests"})]}),s.jsx("body",{className:`${i().className} bg-white text-black`,children:s.jsx(r,{children:(0,s.jsxs)(o.El,{children:[s.jsx(h.Z,{}),s.jsx("main",{className:"min-h-screen pt-12 bg-white dark:bg-[#010814]",children:e}),s.jsx(n.c,{}),s.jsx(c.c,{})]})})})]})}},12523:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var s=a(19510),l=a(57371);function i(){return(0,s.jsxs)("section",{className:"relative bg-base-100 text-base-content h-screen w-full flex flex-col justify-center gap-8 items-center p-10",children:[s.jsx("div",{className:"p-6 bg-white rounded-xl",children:(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"w-56 h-56",viewBox:"0 0 860.13137 571.14799",children:[s.jsx("path",{fill:"#f2f2f2",d:"M435.735 160.527c-7.669-12.684-16.757-26.228-30.99-30.37-16.481-4.796-33.412 4.732-47.774 14.135a1392.157 1392.157 0 0 0-123.893 91.283l.043.493 92.451-6.376c22.265-1.535 45.296-3.283 64.972-13.816 7.467-3.996 14.745-9.335 23.206-9.707 10.511-.463 19.677 6.879 26.88 14.549 42.607 45.37 54.937 114.754 102.738 154.616a1516.995 1516.995 0 0 0-107.633-214.807Z"}),s.jsx("path",{fill:"#e4e4e4",d:"M697.636 545.355c-4.711-5.95-6.637-7.343-11.284-13.347q-56.765-73.417-106.708-151.793-33.924-53.23-64.483-108.504-14.549-26.278-28.3-52.969-10.67-20.695-20.864-41.638a841.984 841.984 0 0 1-5.711-12.009c-4.428-9.442-8.774-18.93-13.44-28.244-5.317-10.616-11.789-21.745-21.552-28.877a29.405 29.405 0 0 0-15.319-5.895c-7.948-.513-15.282 2.769-22.176 6.353-50.438 26.301-97.659 59.276-140.37 96.798A730.778 730.778 0 0 0 133.39 331.82c-1.009 1.44-3.393.064-2.375-1.384q6.01-8.498 12.257-16.813a734.817 734.817 0 0 1 187.6-174.986q18.248-11.825 37.182-22.542c6.362-3.603 12.752-7.16 19.251-10.497 6.372-3.272 13.137-6.215 20.416-6.325 24.77-.385 37.595 27.667 46.405 46.542q4.153 8.911 8.406 17.767 16.075 33.62 33.388 66.628 10.684 20.379 21.837 40.52 34.707 62.717 73.778 122.896c34.506 53.143 68.737 100.089 108.046 149.785 1.082 1.375-.852 3.337-1.944 1.943ZM244.982 191.378c-1.44-1.604-2.87-3.209-4.318-4.813-11.422-12.632-23.679-25.118-39.364-32.36a57.11 57.11 0 0 0-23.927-5.547c-8.562.028-16.932 2.274-24.843 5.418-3.74 1.494-7.399 3.19-11.001 4.996-4.116 2.072-8.16 4.281-12.183 6.51q-11.332 6.27-22.369 13.09-21.96 13.572-42.545 29.216-10.671 8.113-20.902 16.758-9.516 8.03-18.646 16.492c-1.302 1.201-3.245-.742-1.944-1.943a441.255 441.255 0 0 1 4.85-4.446q6.875-6.216 13.971-12.193 12.94-10.918 26.549-20.993 21.162-15.676 43.782-29.226 11.304-6.765 22.919-12.962a198.735 198.735 0 0 1 7.095-3.621 113.116 113.116 0 0 1 16.868-6.867 60.006 60.006 0 0 1 25.476-2.502 66.327 66.327 0 0 1 23.505 8.131c15.401 8.608 27.346 21.92 38.97 34.91 1.174 1.32-.76 3.272-1.943 1.952Z"}),s.jsx("path",{fill:"#e4e4e4",d:"m560.542 322.285 36.905-13.498 18.323-6.702c5.968-2.183 11.921-4.667 18.09-6.23a28.539 28.539 0 0 1 16.374.208 37.738 37.738 0 0 1 12.77 7.917 103.64 103.64 0 0 1 10.475 11.186c3.99 4.795 7.92 9.64 11.868 14.467q24.442 29.891 48.563 60.042 24.121 30.15 47.92 60.556 23.857 30.48 47.386 61.216 2.882 3.765 5.76 7.534c1.059 1.388 3.449.02 2.374-1.388q-23.702-31.045-47.735-61.835-24.092-30.864-48.516-61.466-24.425-30.601-49.179-60.937-6.167-7.558-12.354-15.099c-3.48-4.24-6.92-8.527-10.737-12.474-7.005-7.245-15.757-13.648-26.234-13.822-6.16-.102-12.121 1.853-17.844 3.923-6.17 2.232-12.325 4.506-18.486 6.76l-37.163 13.592-9.29 3.398c-1.65.603-.937 3.262.73 2.652Z"}),s.jsx("path",{fill:"#f2f2f2",d:"M196.443 170.1c-18.754-9.639-42.771-7.75-60.005 4.291a855.847 855.847 0 0 1 97.37 22.726c-13.282-7.784-23.672-19.98-37.365-27.017ZM136.253 174.358l-3.61 2.935a53.444 53.444 0 0 1 3.795-2.902c-.062-.01-.123-.022-.185-.033ZM661.615 322.42c-3.633-4.422-7.56-9.052-12.994-10.849l-5.073.2a575.436 575.436 0 0 0 153.267 175.221l-135.2-164.572ZM346.15 285.94a37.481 37.481 0 0 0 14.93 20.96c2.82 1.92 6.157 3.761 7.122 7.034a8.379 8.379 0 0 1-.873 6.15 24.884 24.884 0 0 1-3.862 5.041l-.136.512c-6.999-4.147-13.657-9.393-17.523-16.551s-4.405-16.539.342-23.146M579.15 488.94a37.481 37.481 0 0 0 14.93 20.96c2.82 1.92 6.157 3.761 7.122 7.034a8.379 8.379 0 0 1-.873 6.15 24.884 24.884 0 0 1-3.862 5.041l-.136.512c-6.999-4.147-13.657-9.393-17.523-16.551s-4.405-16.539.342-23.146M114.15 474.94a37.481 37.481 0 0 0 14.93 20.96c2.82 1.92 6.157 3.761 7.122 7.034a8.379 8.379 0 0 1-.873 6.15 24.884 24.884 0 0 1-3.862 5.041l-.136.512c-6.999-4.147-13.657-9.393-17.523-16.551s-4.405-16.539.342-23.146"}),s.jsx("circle",{cx:649.249,cy:51,r:51,className:"fill-primary"}),s.jsx("path",{fill:"#f0f0f0",d:"M741.284 11.87c-24.717-3.34-52.935 10.02-59.341 34.124a21.597 21.597 0 0 0-41.094 2.109l2.83 2.026a372.275 372.275 0 0 0 160.659-.726C787.145 31.334 766 15.21 741.284 11.87ZM635.284 79.87c-24.717-3.34-52.935 10.02-59.341 34.124a21.597 21.597 0 0 0-41.094 2.109l2.83 2.026a372.275 372.275 0 0 0 160.659-.726C681.145 99.334 660 83.21 635.284 79.87Z"}),s.jsx("path",{fill:"#ccc",d:"M851.011 92.728a.982.982 0 0 1-.302-.047C586.303 9.063 353.265 19.998 204.33 43.895a1294.017 1294.017 0 0 0-60.403 11.161 1196.246 1196.246 0 0 0-15.597 3.378 1023.104 1023.104 0 0 0-18.532 4.306q-3.873.917-7.595 1.849a972.21 972.21 0 0 0-11.66 2.957 930.173 930.173 0 0 0-13.797 3.671.442.442 0 0 1-.051.015v.001a926.363 926.363 0 0 0-15.323 4.325c-2.698.78-5.304 1.548-7.8 2.307-.278.077-.525.151-.776.227l-.536.164c-.31.094-.617.187-.924.275l-.02.006h.001l-.811.253c-.968.293-1.912.579-2.841.864C23.119 87.22 9.626 92.604 9.493 92.656a1 1 0 1 1-.744-1.856c.134-.053 13.693-5.463 38.327-13.058.932-.286 1.88-.572 2.85-.866l.754-.235c.026-.01.051-.017.078-.025.305-.087.61-.18.92-.273l.536-.164c.268-.08.532-.16.802-.235a593.8 593.8 0 0 1 7.797-2.307 932.235 932.235 0 0 1 15.334-4.328c.017-.006.033-.01.05-.014v-.001a941.379 941.379 0 0 1 13.844-3.685 993.766 993.766 0 0 1 11.68-2.962q3.738-.93 7.61-1.852a1026.011 1026.011 0 0 1 18.563-4.313c5.299-1.183 10.555-2.322 15.622-3.383a1295.424 1295.424 0 0 1 60.497-11.178c149.149-23.932 382.52-34.884 647.299 48.854a1 1 0 0 1-.3 1.953Z"}),s.jsx("path",{fill:"#3f3d56",d:"M262.989 419.84a6.73 6.73 0 0 0-1.7-2.67 6.43 6.43 0 0 0-.92-.71c-2.61-1.74-6.51-2.13-8.99 0a5.81 5.81 0 0 0-.69.71q-1.11 1.365-2.28 2.67a88.226 88.226 0 0 1-3.96 4.24c-.39.38-.78.77-1.18 1.15-.23.23-.46.45-.69.67-.88.84-1.78 1.65-2.69 2.45-.48.43-.96.85-1.45 1.26-.73.61-1.46 1.22-2.2 1.81-.07.05-.14.1-.21.16-.02.01-.03.03-.05.04-.01 0-.02 0-.03.02a.179.179 0 0 0-.07.05c-.22.15-.37.25-.48.34.04-.02.08-.05.12-.07-.18.14-.37.28-.55.42a92.853 92.853 0 0 1-5.37 3.69 99.21 99.21 0 0 1-14.22 7.55c-.33.13-.67.27-1.01.4a85.97 85.97 0 0 1-40.85 6.02q-2.13-.165-4.26-.45c-1.64-.24-3.27-.53-4.89-.86a97.932 97.932 0 0 1-18.02-5.44 118.652 118.652 0 0 1-20.66-12.12c-1-.71-2.01-1.42-3.02-2.11 1.15-2.82 2.28-5.64 3.38-8.48.55-1.37 1.08-2.74 1.6-4.12 4.09-10.63 7.93-21.36 11.61-32.13q5.58-16.365 10.53-32.92.51-1.68.99-3.36 2.595-8.745 4.98-17.53c.15-.57.31-1.13.45-1.7q.69-2.52 1.35-5.04c1-3.79-1.26-8.32-5.24-9.23a7.634 7.634 0 0 0-9.22 5.24c-.43 1.62-.86 3.23-1.3 4.85q-3.165 11.745-6.66 23.41l-1.02 3.36q-7.71 25.41-16.93 50.31-1.11 3.015-2.25 6.01c-.37.98-.74 1.96-1.12 2.94-.73 1.93-1.48 3.86-2.23 5.79-.43 1.13-.87 2.26-1.31 3.38-.29.71-.57 1.42-.85 2.12a41.81 41.81 0 0 0-8.81-2.12l-.48-.06a27.397 27.397 0 0 0-7.01.06 23.914 23.914 0 0 0-17.24 10.66c-4.77 7.51-4.71 18.25 1.98 24.63 6.89 6.57 17.32 6.52 25.43 2.41a28.351 28.351 0 0 0 10.52-9.86 50.57 50.57 0 0 0 2.74-4.65c.21.14.42.28.63.43.8.56 1.6 1.13 2.39 1.69a111.738 111.738 0 0 0 14.51 8.91 108.359 108.359 0 0 0 34.62 10.47c.27.03.53.07.8.1 1.33.17 2.67.3 4.01.41a103.782 103.782 0 0 0 55.58-11.36q2.175-1.125 4.31-2.36 3.315-1.92 6.48-4.08c1.15-.78 2.27-1.57 3.38-2.4a101.042 101.042 0 0 0 13.51-11.95q2.355-2.475 4.51-5.11a8.061 8.061 0 0 0 2.2-5.3 7.564 7.564 0 0 0-.5-2.64Zm-165.59 23.82c.21-.15.42-.31.62-.47-.06.15-.35.32-.62.47Zm3.21-3.23c-.23.26-.44.52-.67.78a23.366 23.366 0 0 1-2.25 2.2c-.11.1-.23.2-.35.29a.01.01 0 0 0-.01.01 3.804 3.804 0 0 0-.42.22q-.645.39-1.32.72a17.005 17.005 0 0 1-2.71.75 16.8 16.8 0 0 1-2.13.02h-.02a14.823 14.823 0 0 1-1.45-.4c-.24-.12-.47-.26-.7-.4-.09-.08-.17-.16-.22-.21a2.44 2.44 0 0 1-.27-.29.01.01 0 0 0-.01-.01c-.11-.2-.23-.4-.34-.6a.031.031 0 0 1-.01-.02c-.08-.25-.15-.51-.21-.77a12.51 12.51 0 0 1 .01-1.37 13.467 13.467 0 0 1 .54-1.88 11.068 11.068 0 0 1 .69-1.26c.02-.04.12-.2.23-.38.01-.01.01-.01.01-.02.15-.17.3-.35.46-.51.27-.3.56-.56.85-.83a18.022 18.022 0 0 1 1.75-1.01 19.48 19.48 0 0 1 2.93-.79 24.99 24.99 0 0 1 4.41.04 30.301 30.301 0 0 1 4.1 1.01 36.945 36.945 0 0 1-2.77 4.54c-.04.06-.08.12-.12.17Zm-11.12-3.29a2.18 2.18 0 0 1-.31.39 1.409 1.409 0 0 1 .31-.39Z"}),s.jsx("path",{fill:"#3f3d56",d:"m232.929 317.71-.27 9.42q-.285 10.455-.59 20.92-.315 11.775-.66 23.54-.165 6.075-.34 12.15-.465 16.365-.92 32.72c-.03 1.13-.07 2.25-.1 3.38l-.45 16.23q-.255 8.805-.5 17.61-.18 6.6-.37 13.21l-2.7 95.79a7.648 7.648 0 0 1-7.5 7.5 7.561 7.561 0 0 1-7.5-7.5q.75-26.94 1.52-53.88.675-24.36 1.37-48.72l.45-16.06q.345-12.09.68-24.18c.03-1.13.07-2.25.1-3.38.02-.99.05-1.97.08-2.96l1.32-46.96q.27-9.24.52-18.49l.6-21.08c.09-3.09.17-6.17.26-9.26a7.648 7.648 0 0 1 7.5-7.5 7.561 7.561 0 0 1 7.5 7.5ZM644.357 319.791a893.238 893.238 0 0 1-28.161 87.941c-3.007 7.947-6.083 15.877-9.372 23.712l.756-1.791a54.583 54.583 0 0 1-5.59 10.612q-.229.32-.466.636 1.166-1.49.443-.589c-.254.3-.505.602-.768.895a23.664 23.664 0 0 1-2.249 2.204q-.301.257-.612.504l.938-.73c-.109.258-.873.598-1.11.744a18.254 18.254 0 0 1-2.405 1.218l1.791-.756a19.086 19.086 0 0 1-4.23 1.16l1.993-.267a17.02 17.02 0 0 1-4.298.046l1.994.268a14.002 14.002 0 0 1-3.405-.917l1.791.756a12.012 12.012 0 0 1-1.678-.896c-.272-.177-1.106-.809-.015.024 1.133.866.145.075-.088-.155-.194-.192-.37-.4-.56-.595-.882-.905.997 1.556.397.498a18.182 18.182 0 0 1-.878-1.637l.756 1.792a11.925 11.925 0 0 1-.728-2.651l.268 1.993a13.651 13.651 0 0 1-.003-3.404l-.268 1.993a15.964 15.964 0 0 1 .995-3.68l-.756 1.792a16.73 16.73 0 0 1 1.178-2.299 6.73 6.73 0 0 1 .728-1.071c.05.016-1.268 1.513-.57.757.184-.198.355-.406.54-.602.296-.314.613-.6.925-.898 1.045-.994-1.461.966-.256.18a19.049 19.049 0 0 1 2.75-1.5l-1.792.756a20.311 20.311 0 0 1 4.995-1.34l-1.994.268a25.628 25.628 0 0 1 6.46.076l-1.993-.267a33.21 33.21 0 0 1 7.892 2.22l-1.792-.757c5.39 2.314 10.163 5.75 14.928 9.118a111.95 111.95 0 0 0 14.506 8.907 108.388 108.388 0 0 0 34.622 10.474 103.933 103.933 0 0 0 92.586-36.752 8.078 8.078 0 0 0 2.197-5.304 7.632 7.632 0 0 0-2.197-5.303c-2.752-2.526-7.95-3.239-10.607 0a95.636 95.636 0 0 1-8.106 8.727q-2.018 1.914-4.143 3.71-1.213 1.026-2.46 2.011c-.394.31-1.62 1.138.263-.197-.432.306-.845.64-1.27.954a99.269 99.269 0 0 1-20.333 11.565l1.792-.756a96.836 96.836 0 0 1-24.172 6.623l1.994-.268a97.643 97.643 0 0 1-25.753-.038l1.993.268a99.8 99.8 0 0 1-24.857-6.77l1.792.755a116.025 116.025 0 0 1-21.736-12.59 86.877 86.877 0 0 0-11.113-6.995 42.824 42.824 0 0 0-14.438-4.388c-9.44-1.111-19.057 2.565-24.247 10.72-4.775 7.505-4.714 18.244 1.974 24.625 6.888 6.573 17.319 6.517 25.436 2.406 7.817-3.96 12.513-12.186 15.815-19.942 7.43-17.455 14.01-35.314 20.14-53.263q9.096-26.637 16.498-53.813.917-3.366 1.807-6.74c1.001-3.788-1.261-8.32-5.238-9.225a7.633 7.633 0 0 0-9.226 5.238Z"}),s.jsx("path",{fill:"#3f3d56",d:"m719.19 317.71-2.7 95.793-2.686 95.294-1.518 53.883a7.565 7.565 0 0 0 7.5 7.5 7.65 7.65 0 0 0 7.5-7.5l2.7-95.793 2.685-95.294 1.518-53.883a7.565 7.565 0 0 0-7.5-7.5 7.65 7.65 0 0 0-7.5 7.5Z"}),s.jsx("path",{d:"M459.591 535.935h2.33V429.893h54.328v-2.322H461.92v-44.745h41.956q-.923-1.173-1.899-2.317H461.92v-29.553a65.378 65.378 0 0 0-2.329-.943v30.496H413.94v-37.865c-.782.036-1.552.09-2.329.155v37.71h-36.42v-28.25a54.63 54.63 0 0 0-2.317 1.092v27.158h-30.615v2.317h30.615v44.744h-30.615v2.323h30.615v106.042h2.317V429.893a36.413 36.413 0 0 1 36.42 36.42v69.622h2.33V429.893h45.651Zm-84.4-108.365v-44.744h36.42v44.745Zm38.748 0v-44.744h.914a44.741 44.741 0 0 1 44.738 44.745Z",opacity:.2}),s.jsx("path",{fill:"#3f3d56",d:"M445.369 504.14a63.059 63.059 0 0 1-20.05 33.7c-.74.64-1.48 1.26-2.25 1.87q-2.805.255-5.57.52c-1.53.14-3.04.29-4.54.43l-.27.03-.19-1.64-.76-6.64a37.623 37.623 0 0 1-3.3-32.44c2.64-7.12 7.42-13.41 12.12-19.65 6.49-8.62 12.8-17.14 13.03-27.65a60.544 60.544 0 0 1 7.9 13.33 16.432 16.432 0 0 0-5.12 3.77c-.41.45-.82 1.08-.54 1.62.24.46.84.57 1.36.63l3.76.39c1 .11 2 .21 3 .32a63.99 63.99 0 0 1 2.45 12.18 61.189 61.189 0 0 1-1.03 19.23Z"}),s.jsx("path",{className:"fill-primary",d:"M478.569 477.93c-5.9 4.29-9.35 10.46-12.03 17.26a16.628 16.628 0 0 0-7.17 4.58c-.41.45-.82 1.08-.54 1.62.24.46.84.57 1.36.63l3.76.39c-2.68 8.04-5.14 16.36-9.88 23.15a36.99 36.99 0 0 1-12.03 10.91 38.492 38.492 0 0 1-4.02 1.99q-7.62.585-14.95 1.25-2.805.255-5.57.52c-1.53.14-3.04.29-4.54.43q-.015-.825 0-1.65a63.304 63.304 0 0 1 15.25-39.86c.45-.52.91-1.03 1.38-1.54a61.792 61.792 0 0 1 16.81-12.7 62.654 62.654 0 0 1 32.17-6.98Z"}),s.jsx("path",{className:"fill-primary",d:"m419.229 535.1-1.15 3.4-.58 1.73c-1.53.14-3.04.29-4.54.43l-.27.03-4.96.51c-.43-.5-.86-1.01-1.28-1.53a62.03 62.03 0 0 1 8.07-87.11c-1.32 6.91.22 13.53 2.75 20.1-.27.11-.53.22-.78.34a16.432 16.432 0 0 0-5.12 3.77c-.41.45-.82 1.08-.54 1.62.24.46.84.57 1.36.63l3.76.39c1 .11 2 .21 3 .32l1.41.15c.07.15.13.29.2.44 2.85 6.18 5.92 12.39 7.65 18.83a43.666 43.666 0 0 1 1.02 4.91 37.604 37.604 0 0 1-10 31.04Z"}),s.jsx("path",{fill:"#3f3d56",d:"M519.887 390.06c-8.609-16.792-21.946-30.92-37.632-41.303a114.237 114.237 0 0 0-52.563-18.38q-3.69-.335-7.399-.393c-2.921-.043-46.866 12.632-61.587 22.982a114.295 114.295 0 0 0-35.333 39.527 102.5 102.5 0 0 0-12.126 51.634 113.564 113.564 0 0 0 14.703 51.476 110.475 110.475 0 0 0 36.444 38.745c15.338 9.787 30.745 35.736 48.855 36.652 18.246.923 39.054-23.555 55.695-30.987a104.425 104.425 0 0 0 41.725-34.005 110.25 110.25 0 0 0 19.6-48.948c2.573-18.083 1.374-36.733-4.802-54.016a111.86 111.86 0 0 0-5.58-12.983c-1.78-3.506-6.996-4.796-10.261-2.691a7.68 7.68 0 0 0-2.691 10.261q1.568 3.088 2.915 6.278l-.756-1.792a101.15 101.15 0 0 1 6.877 25.539l-.268-1.994a109.229 109.229 0 0 1-.066 28.682l.267-1.994a109.734 109.734 0 0 1-7.554 27.675l.756-1.792a104.212 104.212 0 0 1-6.672 13.098q-1.923 3.186-4.08 6.222c-.632.888-1.283 1.761-1.94 2.631-.855 1.136 1.168-1.483.283-.37-.15.19-.3.38-.452.57q-.681.852-1.382 1.688a93.613 93.613 0 0 1-10.176 10.383q-1.366 1.193-2.778 2.331c-.469.379-.932.773-1.42 1.125.018-.013 1.579-1.2.655-.51-.29.216-.579.435-.87.651q-2.91 2.156-5.974 4.092a103.485 103.485 0 0 1-14.756 7.713l1.792-.756a109.215 109.215 0 0 1-27.597 7.552l1.994-.268a108.154 108.154 0 0 1-28.589.05l1.994.268a99.835 99.835 0 0 1-25.096-6.784l1.792.756a93.643 93.643 0 0 1-13.416-6.991q-3.174-2-6.184-4.248c-.286-.213-.57-.43-.855-.645-.915-.691.658.51.67.518a19.169 19.169 0 0 1-1.534-1.225q-1.454-1.184-2.862-2.422a101.99 101.99 0 0 1-10.493-10.71q-1.213-1.433-2.374-2.91c-.335-.426-.946-1.29.404.53-.177-.24-.362-.475-.541-.713q-.647-.858-1.276-1.728-2.203-3.048-4.188-6.246a109.29 109.29 0 0 1-7.805-15.108l.756 1.791a106.588 106.588 0 0 1-7.34-26.837l.267 1.994a97.866 97.866 0 0 1-.048-25.636l-.268 1.994a94.673 94.673 0 0 1 6.595-23.959l-.757 1.792a101.557 101.557 0 0 1 7.196-13.857q2.065-3.323 4.377-6.484.526-.719 1.063-1.428c.324-.428 1.215-1.494-.306.388.15-.184.293-.374.44-.56q1.269-1.608 2.6-3.165a107.402 107.402 0 0 1 10.883-11.02q1.474-1.293 2.994-2.53.691-.562 1.391-1.113c.187-.147.376-.29.562-.438-1.998 1.59-.555.432-.102.092q3.134-2.348 6.436-4.46a103.644 103.644 0 0 1 15.386-8.109l-1.791.756c7.76-3.258 42.14-10.949 48.394-10.11l-1.994-.267a106.225 106.225 0 0 1 26.72 7.382l-1.792-.756a110.313 110.313 0 0 1 12.6 6.33q3.044 1.783 5.968 3.762 1.383.936 2.738 1.915.677.489 1.346.989c.248.185.494.372.741.558 1.04.779-1.431-1.129-.342-.267a110.843 110.843 0 0 1 10.368 9.253q2.401 2.445 4.637 5.045 1.147 1.335 2.246 2.708c.365.455 1.605 2.1.085.084.372.493.747.983 1.114 1.48a97.977 97.977 0 0 1 8.392 13.537c1.793 3.498 6.987 4.802 10.261 2.691a7.677 7.677 0 0 0 2.69-10.261Z"}),s.jsx("path",{fill:"#3f3d56",d:"M432.497 512.456a3.78 3.78 0 0 1-2.74-6.552l.26-1.03-.103-.247c-3.48-8.297-25.685 14.834-26.645 22.632a30.029 30.029 0 0 0 .527 10.328 120.392 120.392 0 0 1-10.952-50.003 116.202 116.202 0 0 1 .72-12.963q.598-5.293 1.658-10.51a121.787 121.787 0 0 1 24.151-51.617c6.874.383 12.898-.664 13.48-13.986.103-2.37 1.86-4.421 2.248-6.756a30.72 30.72 0 0 1-1.98.183l-.623.032-.077.004a3.745 3.745 0 0 1-3.076-6.101l.85-1.046c.43-.538.872-1.065 1.302-1.603a1.865 1.865 0 0 0 .14-.161c.495-.613.99-1.216 1.485-1.829a10.83 10.83 0 0 0-3.55-3.432c-4.96-2.904-11.802-.893-15.384 3.593-3.593 4.486-4.271 10.78-3.023 16.385a43.398 43.398 0 0 0 6.003 13.383c-.27.344-.549.677-.818 1.022a122.574 122.574 0 0 0-12.793 20.268c1.016-7.939-11.412-36.608-16.218-42.68-5.773-7.295-17.611-4.112-18.628 5.135l-.03.268q1.072.604 2.097 1.283a5.127 5.127 0 0 1-2.067 9.33l-.104.016c-9.556 13.644 21.077 49.155 28.745 41.182a125.11 125.11 0 0 0-6.735 31.692 118.664 118.664 0 0 0 .086 19.16l-.032-.226c-1.704-13.882-30.931-34.522-39.466-32.803-4.917.99-9.76.765-9.013 5.725l.036.237a34.442 34.442 0 0 1 3.862 1.861q1.07.605 2.096 1.283a5.127 5.127 0 0 1-2.067 9.33l-.104.016-.215.033c-4.35 14.966 27.907 39.12 47.517 31.434h.011a125.075 125.075 0 0 0 8.402 24.528h30.015c.107-.333.204-.678.301-1.011a34.102 34.102 0 0 1-8.305-.495c2.227-2.732 4.454-5.486 6.68-8.219a1.861 1.861 0 0 0 .14-.161c1.13-1.399 2.27-2.787 3.4-4.185v-.002a49.952 49.952 0 0 0-1.463-12.725Zm-34.37-67.613.015-.022-.016.043Zm-6.65 59.932-.257-.58c.01-.42.01-.84 0-1.27 0-.119-.022-.237-.022-.355.097.742.183 1.484.29 2.227Z"}),s.jsx("circle",{cx:95.249,cy:439,r:11,fill:"#3f3d56"}),s.jsx("circle",{cx:227.249,cy:559,r:11,fill:"#3f3d56"}),s.jsx("circle",{cx:728.249,cy:559,r:11,fill:"#3f3d56"}),s.jsx("circle",{cx:755.249,cy:419,r:11,fill:"#3f3d56"}),s.jsx("circle",{cx:723.249,cy:317,r:11,fill:"#3f3d56"}),s.jsx("path",{fill:"#3f3d56",d:"M264.249 419a10.949 10.949 0 1 1-.21-2.16 10.992 10.992 0 0 1 .21 2.16Z"}),s.jsx("circle",{cx:484.249,cy:349,r:11,fill:"#3f3d56"}),s.jsx("path",{fill:"#3f3d56",d:"M375.249 349a10.949 10.949 0 1 1-.21-2.16 10.992 10.992 0 0 1 .21 2.16ZM233.249 317a10.949 10.949 0 1 1-.21-2.16 10.992 10.992 0 0 1 .21 2.16Z"}),s.jsx("circle",{cx:599.249,cy:443,r:11,fill:"#3f3d56"}),s.jsx("circle",{cx:426.249,cy:338,r:16,fill:"#3f3d56"}),s.jsx("path",{fill:"#cacaca",d:"m858.94 570.84-857.75.308a1.19 1.19 0 1 1 0-2.381l857.75-.308a1.19 1.19 0 0 1 0 2.382Z"})]})}),s.jsx("p",{className:"text-lg md:text-xl font-semibold",children:"This page doesn't exist \uD83D\uDE05"}),s.jsx("div",{className:"flex flex-wrap gap-4 justify-center",children:(0,s.jsxs)(l.default,{href:"/",className:"btn btn-sm",children:[s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"w-5 h-5",children:s.jsx("path",{fillRule:"evenodd",d:"M9.293 2.293a1 1 0 011.414 0l7 7A1 1 0 0117 11h-1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-3a1 1 0 00-1-1H9a1 1 0 00-1 1v3a1 1 0 01-1 1H5a1 1 0 01-1-1v-6H3a1 1 0 01-.707-1.707l7-7z",clipRule:"evenodd"})}),"Home"]})})]})}},61953:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(68570).createProxy)(String.raw`C:\Projects\PersonalPortal\src\components\Footer.tsx#default`)},13188:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(68570).createProxy)(String.raw`C:\Projects\PersonalPortal\src\components\Header.tsx#default`)},1542:(e,t,a)=>{"use strict";a.d(t,{Z:()=>l});var s=a(423);let l={appName:"Javian Picardo",appDescription:"Portal for the services provided by Javian Picardo.",domainName:"www.javianpicardo.com",robots:{index:!0,follow:!0,"max-image-preview":"large","max-snippet":-1,googleBot:{index:!0,follow:!0,"max-image-preview":"large","max-snippet":-1}},stripe:{products:[{type:"one-time",title:"Quick Shoots",productId:"prod_S0c6NHMe0vINXA",subtitle:"Professional photography session",price:300,isBest:!1,linkTitle:"Book Quick Shoots",featuresTitle:"Package Features",priceId:"price_1R6aslP82YH9JfOlJZcweC4d",features:[{title:"One hour of footage time",disabled:!1},{title:"50 edited photos",disabled:!1},{title:"Help with posing",disabled:!1},{title:"Location list to pick from",disabled:!1},{title:"7 business days turn around time",disabled:!1}]},{type:"one-time",title:"Mini Shoots",productId:"prod_S0c9zDLLtK9rnS",subtitle:"Perfect for professional portraits",price:150,isBest:!0,linkTitle:"Book Mini Shoots",featuresTitle:"Package Features",priceId:"price_1R6avXP82YH9JfOlIGpAfwjU",features:[{title:"30 minutes of footage time",disabled:!1},{title:"25 edited photos",disabled:!1},{title:"Help with posing",disabled:!1},{title:"Location list to pick from",disabled:!1},{title:"7 business days turn around time",disabled:!1}]}]},colors:{theme:"light",main:a.n(s)().light.primary},resend:{fromAdmin:"Javian Picardo <<EMAIL>>",supportEmail:"<EMAIL>",forwardRepliesTo:"<EMAIL>",subjects:{thankYou:"Welcome to Javian Picardo"}}}},7264:(e,t,a)=>{"use strict";a.d(t,{A:()=>l}),a(19510);var s=a(1542);let l=({title:e,description:t,keywords:a,openGraph:l,canonicalUrlRelative:i,extraTags:r}={})=>({title:e||s.Z.appName,description:t||s.Z.appDescription,keywords:a||[s.Z.appName],applicationName:s.Z.appName,metadataBase:new URL(`https://${s.Z.domainName}/`),robots:{index:!0,follow:!0,"max-image-preview":"large","max-snippet":-1,googleBot:{index:!0,follow:!0,"max-image-preview":"large","max-snippet":-1}},openGraph:{title:l?.title||s.Z.appName,description:l?.description||s.Z.appDescription,url:l?.url||`https://${s.Z.domainName}/`,siteName:l?.title||s.Z.appName,locale:"en_US",type:"website"},twitter:{title:l?.title||s.Z.appName,description:l?.description||s.Z.appDescription,card:"summary_large_image",creator:"@dennis_babych"},...i&&{alternates:{canonical:i}},...r})},63601:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var s=a(66621);let l=e=>[{type:"image/png",sizes:"1294x1284",url:(0,s.fillMetadataSegment)(".",e.params,"apple-icon.png")+"?b3daf136661722b9"}]},73881:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var s=a(66621);let l=e=>[{type:"image/x-icon",sizes:"256x256",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},71150:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var s=a(66621);let l=e=>[{type:"image/png",sizes:"1200x1200",url:(0,s.fillMetadataSegment)(".",e.params,"icon.png")+"?991587c210d41334"}]},51922:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var s=a(66621);let l=e=>[{type:"image/png",width:595,height:645,url:(0,s.fillMetadataSegment)(".",e.params,"opengraph-image.png")+"?be1cabe57d4d827d"}]},40680:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var s=a(66621);let l=e=>[{type:"image/png",width:595,height:645,url:(0,s.fillMetadataSegment)(".",e.params,"twitter-image.png")+"?be1cabe57d4d827d"}]},21946:()=>{}};