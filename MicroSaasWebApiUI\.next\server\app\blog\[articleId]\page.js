(()=>{var e={};e.id=433,e.ids=[433],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},32694:e=>{"use strict";e.exports=require("http2")},35240:e=>{"use strict";e.exports=require("https")},85807:e=>{"use strict";e.exports=require("module")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},6005:e=>{"use strict";e.exports=require("node:crypto")},56268:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),r(34730),r(58840),r(8130),r(7629),r(12523);var s=r(23191),a=r(88716),i=r(37922),l=r.n(i),o=r(95231),n={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);r.d(t,n);let d=["",{children:["blog",{children:["[articleId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,34730)),"C:\\Projects\\PersonalPortal\\src\\app\\blog\\[articleId]\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,58840)),"C:\\Projects\\PersonalPortal\\src\\app\\blog\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e),async e=>(await Promise.resolve().then(r.bind(r,71150))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,63601))).default(e)],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,51922))).default(e)],twitter:[async e=>(await Promise.resolve().then(r.bind(r,40680))).default(e)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,8130)),"C:\\Projects\\PersonalPortal\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,7629)),"C:\\Projects\\PersonalPortal\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"C:\\Projects\\PersonalPortal\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e),async e=>(await Promise.resolve().then(r.bind(r,71150))).default(e)],apple:[async e=>(await Promise.resolve().then(r.bind(r,63601))).default(e)],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,51922))).default(e)],twitter:[async e=>(await Promise.resolve().then(r.bind(r,40680))).default(e)],manifest:void 0}}],c=["C:\\Projects\\PersonalPortal\\src\\app\\blog\\[articleId]\\page.tsx"],u="/blog/[articleId]/page",x={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/blog/[articleId]/page",pathname:"/blog/[articleId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},65218:(e,t,r)=>{Promise.resolve().then(r.bind(r,16356))},16356:(e,t,r)=>{"use strict";r.d(t,{default:()=>h});var s=r(10326);r(91405);var a=r(46226),i=r(90434),l=r(23609),o=r(4721);let n=({currentBlog:e,AllPosts:t})=>{let r=t.filter(t=>t.title.rendered!==e);return(0,s.jsxs)("div",{className:"w-full max-w-[90rem] mx-auto px-4 sm:px-6 lg:px-8 mb-6",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsxs)("div",{className:"flex-1 mx-auto max-w-5xl pr-8",children:[s.jsx("h2",{className:"text-4xl font-bold mb-6 text-left",children:"Other Articles"}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:r.slice(0,4).map((e,t)=>(0,s.jsxs)(i.default,{href:`/blog/${e.slug}`,className:"bg-white dark:bg-[#1E232C] rounded-xl overflow-hidden shadow-lg dark:border border-solid border-[#373C53] hover:border-[#5b6285] transition-all duration-500 transform hover:scale-105 ease-in-out",children:[s.jsx(a.default,{src:e.featured_img,alt:e.title.rendered,className:"object-cover",width:500,height:156}),(0,s.jsxs)("div",{className:"p-4",children:[s.jsx("h2",{className:"font-normal text-[16px] dark:text-white mb-2",children:e.title.rendered}),s.jsx("p",{className:"text-gray-400",children:(0,l.a)(e.date)})]})]},t))})]}),s.jsx("div",{className:"hidden lg:block w-[320px]"})]}),s.jsx("div",{className:"mt-6 max-w-5xl mx-auto",children:s.jsx(o.default,{type:"display-horizontal",className:"px-0"})})]})},d={src:"/_next/static/media/back-arrow.1d46893e.svg",height:16,width:20,blurWidth:0,blurHeight:0},c=({post:e})=>{let{yoast_head_json:t}=e,r=t?.schema?.["@graph"]?.find(e=>"Person"===e["@type"]),o=r?.name||"Javian Picardo",n=r?.image?.contentUrl||"";return(0,s.jsxs)("div",{children:[(0,s.jsxs)(i.default,{href:"/blog",className:"flex items-center dark:text-gray-500 text-black1/70 gap-2 mb-4",children:[s.jsx(a.default,{alt:"Back Arrow",src:d,height:10,width:15}),s.jsx("span",{children:"Back to blog"})]}),s.jsx("h1",{className:"text-[42px] !leading-[1.19] font-bold mb-2",children:e?.title?.rendered}),(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[n?s.jsx(a.default,{src:n,width:50,height:50,alt:"Author's profile picture",className:"w-10 h-10 rounded-full mr-2",onError:e=>{let t=e.target;t.src.startsWith("https://")&&(t.src=t.src.replace("https://","http://"))}}):"",(0,s.jsxs)("div",{children:[s.jsx("div",{className:"font-bold",children:o}),s.jsx("div",{className:"text-gray-500",children:(0,l.a)(e.date)})]})]})]})};var u=r(17577),x=r(14831),p=r(26228);let m=({content:e})=>{let{theme:t}=(0,x.F)();return(0,u.useEffect)(()=>{(0,p.$)(t);let e=e=>{let r=e.detail?.theme||t;(0,p.$)(r)};return window.addEventListener("themeChange",e),()=>{window.removeEventListener("themeChange",e)}},[e,t]),s.jsx("div",{className:"dark"===t?"blog-content bg-[#1E232C] text-white border border-[#373C53] dark-theme":"blog-content bg-white text-gray-800 border border-gray-200 light-theme",dangerouslySetInnerHTML:{__html:e}})},h=({postDetails:e,allPosts:t})=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"w-full max-w-[90rem] mx-auto px-4 sm:px-6 lg:px-8 mt-16",children:[s.jsx(c,{post:e}),(0,s.jsxs)("div",{className:"mt-4 flex justify-between",children:[(0,s.jsxs)("div",{className:"flex-1 mx-auto max-w-5xl pr-8",children:[s.jsx(m,{content:e?.content?.rendered||""}),s.jsx("div",{className:"mt-6 mb-6",children:s.jsx(o.default,{type:"in-article"})})]}),s.jsx("div",{className:"hidden lg:block w-[320px] sticky top-32 h-screen",children:s.jsx(o.default,{type:"multiplex-vertical"})})]})]}),s.jsx(n,{currentBlog:e?.title?.rendered,AllPosts:t}),s.jsx("div",{className:"mt-12",children:s.jsx(o.default,{type:"display-horizontal"})})]})},34730:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,generateMetadata:()=>l});var s=r(19510);let a=(0,r(68570).createProxy)(String.raw`C:\Projects\PersonalPortal\src\components\BlogDetails.tsx#default`);var i=r(57939);async function l({params:e}){let t=await i.G.getPost(e.articleId),r=t?.title?.rendered||"Blog Article",s=t?.excerpt?.rendered||"Read our latest blog post",a=t?.slug||e.articleId;return{title:r,description:s,openGraph:{title:r,description:s,type:"article",url:`http://localhost:3000/blog/${a}`},twitter:{card:"summary_large_image",title:r,description:s}}}async function o({params:e}){let t=e.articleId;console.log("slug1",t);let r=await i.G.getPost(t),l=await i.G.getAllPosts();return s.jsx(s.Fragment,{children:s.jsx(a,{postDetails:r,allPosts:l})})}},91405:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[948,70,251,684,762,746,944],()=>r(56268));module.exports=s})();