﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Identity.Client;
using MicroSaasWebApi.Properties.PXW;
using MicroSaasWebApi.Services.PXW.Interface;
using MicroSaasWebApi.Models.PXW.AppSettings;
using System.Net;
using System.Net.Http.Headers;

using Newtonsoft.Json.Linq;
using MicroSaasWebApi.Models.Profile;
using MicroSaasWebApi.Models.PXW;
using MicroSaasWebApi.Services.Profile.PXW;
using Newtonsoft.Json;
using MicroSaasWebApi.Services.PXW.Utilities;

namespace MicroSaasWebApi.Services.PXW
{
    public class BaseService : IBaseService
    {
        protected readonly IHttpContextAccessor _httpContextAccessor;
        protected readonly IConfiguration _configuration;
        protected readonly ILoggerService _loggerService;
        protected readonly IExternalApiService _externalApiService;
        protected readonly IApplicationSettingsService _applicationSettingsService;

        private const string BEARER_PREFIX = "Bearer ";
        protected string apiUrl = string.Empty;
        public IHttpContextAccessor HttpContextAccessor
        {
            get => _httpContextAccessor;
            set => throw new NotImplementedException("Setter for HttpContextAccessor is not implemented.");
        }
        public Dictionary<string, string> PathParametersDictionary { get; set; } = new();
        public Dictionary<string, string> QueryParametersDictionary { get; set; } = new();
        public MultipartFormDataContent FilteredFormData { get; set; } = new MultipartFormDataContent();

        protected Dictionary<string, List<IFormFile>> formParametersDictionary = new();
        public string PayloadString { get; set; } = string.Empty;
        private int _domainId;
        private int _languageId;
        private bool isFormsData = false;

        private readonly string[] NO_TRANSFORM_ENDPOINTS = new string[] { "DownloadDocuments" };
        private readonly string[] FORM_DATA_ENDPOINTS = new string[] { "UploadDocuments", "CreateWorkflowInstanceData", "UpdateWorkflowInstanceData" };
        private readonly string[] ACCEPTED_FILE_CONTENT_TYPES = new string[] {
            "application/pdf",
            "application/zip",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel",
            "text/plain; charset=utf-8",
            "text/plain",
            "text/csv",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "application/vnd.ms-powerpoint",
            "image/png",
            "image/jpeg",
            "image/gif",
            "application/x-ole-storage",
            "application/octet-stream"
        };

        public BaseService(IHttpContextAccessor httpContextAccessor, IConfiguration configuration, ILoggerService loggerService,
         IExternalApiService externalApiService, IApplicationSettingsService applicationSettingsService)
        {
            _httpContextAccessor = httpContextAccessor;
            _configuration = configuration;
            _loggerService = loggerService;
            _externalApiService = externalApiService;
            _applicationSettingsService = applicationSettingsService;
        }
        public int GetDomainId()
        {
            if (_httpContextAccessor?.HttpContext?.Items["TenantId"] is string tenantIdString &&
                    int.TryParse(tenantIdString, out int tenantId))
            {
                _domainId = tenantId;
            }
            else
            {
                // Handle the case when the TenantId is not found or is not a valid integer
                _domainId = 000; // Default value, change as needed
            }
            return _domainId;
        }
        public int GetLanguageId()
        {
            if (_httpContextAccessor?.HttpContext?.Items["Language"] is string languageIdString &&
                    int.TryParse(languageIdString, out int languageId))
            {
                _languageId = languageId;
            }
            else
            {
                // Handle the case when the LanguageId is not found or is not a valid integer
                _languageId = 0; // Default value, change as needed
            }
            return _languageId;
        }
        public Tenant? GetFilteredTenant()
        {
            return _httpContextAccessor?.HttpContext?.Items["FilteredTenant"] as Tenant;
        }
        public void SetFilteredTenant(Tenant tenant)
        {
            if (tenant != null)
            {
                if (_httpContextAccessor?.HttpContext?.Items != null)
                {
                    _httpContextAccessor.HttpContext.Items["FilteredTenant"] = tenant;
                }
            }
        }

        public async Task<IActionResult> FetchApiEndpointsWithBearerToken(string endpointName, bool isAuthHeaderPassed, string? httpMethodName = "GET")
        {
            try
            {
                // Log information about the request
                await _loggerService.LogRequestInformationAsync();

                // Initialize variables
                var httpMethod = httpMethodName ?? "GET";
                string jwtToken = string.Empty;

                // Handle authorization if required
                if (isAuthHeaderPassed)
                {
                    String? authHeader = _httpContextAccessor.HttpContext?.Request.Headers[Constants.Authorization];
                    if (HasBearerToken(authHeader))
                    {
                        jwtToken = authHeader.Substring(BEARER_PREFIX.Length).Trim();
                    }
                    else
                    {
                        await _loggerService.LogInformationAsync("Bearer Token not found");
                        throw new Exception("Bearer Token not found");
                    }
                }
                await SetEndpointUrl();
                ReadRequestBodyFromApi();
                var request = _httpContextAccessor.HttpContext?.Request;
                if (request != null)
                {
                    // Call external API using the service
                    var response = await MakeExternalApiCallByContentType(httpMethod, apiUrl, jwtToken, request, endpointName);
                    return await ProcessThirdPartyApiResponse(response);
                }
                else
                {
                    throw new Exception("Request not found in the API call.");
                }

            }
            catch (MsalException ex)
            {
                throw new Exception($"MsalException occurred : {ex.Message}");
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public virtual async Task SetEndpointUrl()
        {
            var context = _httpContextAccessor.HttpContext;

            if (context == null)
            {
                throw new Exception("HttpContext is null.");
            }

            // Initialize controller and action strings
            string controller = string.Empty;
            string action = string.Empty;

            // Check if the request is multipart form data
            if (context.Request.ContentType != null && MultipartRequestHelper.IsMultipartContentType(context.Request.ContentType))
            {
                // Handle the multipart request and extract the controller and action
                var formCollection = await context.Request.ReadFormAsync();
                if (formCollection.TryGetValue("controller", out var controllerValue))
                {
                    controller = controllerValue;
                }
                if (formCollection.TryGetValue("action", out var actionValue))
                {
                    action = actionValue;
                }
            }
            else
            {
                // Handle non-multipart request (e.g., JSON or query parameters)
                RequestBody? requestBody = context.Items[Constants.RequestBody] as RequestBody;
                controller = requestBody?.Controller ?? throw new Exception("Controller is not specified in the request.");
                action = requestBody?.Action ?? throw new Exception("Action is not specified in the request.");
            }

            // Get the value of "FilteredTenant" from HttpContext.Items
            Tenant? filteredTenantJson = context.Items["FilteredTenant"] as Tenant;

            // Validate that the tenant is not null
            if (filteredTenantJson == null)
            {
                throw new Exception("Tenant information not found in the current context.");
            }

            // Determine the appropriate domain based on the controller name
            string? apiDomainUrl = controller switch
            {
                "PXW/DataApi" => filteredTenantJson.Settings?.PortfolioDataApi?.Domain,
                "PXW/DocumentApi" => filteredTenantJson.Settings?.PortfolioDocumentApi?.Domain,
                "PXW/PermissionApi" => filteredTenantJson.Settings?.PortfolioPermissionApi?.Domain,
                "PXW/DataInterchangeApi" => filteredTenantJson.Settings?.PortfolioDataInterchangeApi?.Domain,
                _ => throw new Exception($"Unknown controller request for Domain URL: {controller}")
            };

            var endpoints = context.Items["Endpoints"] as Endpoints;

            // Use object type to handle different endpoint classes
            object? apiEndpoints = controller switch
            {
                "PXW/DataApi" => endpoints?.PortfolioDataApiEndpoints,
                "PXW/DocumentApi" => endpoints?.PortfolioDocumentApiEndpoints,
                "PXW/PermissionApi" => endpoints?.PortfolioPermissionApiEndpoints,
                "PXW/DataInterchangeApi" => endpoints?.PortfolioDataInterchangeApiEndpoints,
                _ => throw new Exception($"Unknown controller request for Endpoint URLs: {controller}")
            };

            // Use reflection to get the endpoint URL based on the action
            var endpointValue = apiEndpoints?.GetType().GetProperty(action)?.GetValue(apiEndpoints, null) as MicroSaasWebApi.Models.PXW.AppSettings.Endpoint;
            string endpointUrl = endpointValue?.URL ?? throw new Exception($"Endpoint URL not found for action: {action}");

            // Check if the domain URL is null or empty
            if (string.IsNullOrEmpty(apiDomainUrl) || string.IsNullOrEmpty(endpointUrl))
            {
                throw new Exception($"Endpoint URL not found for controller request: {controller}");
            }

            // Set the apiUrl
            apiUrl = $"{apiDomainUrl}{endpointUrl}";
        }        

        public async void ReadRequestBodyFromApi()
        {
            var request = _httpContextAccessor.HttpContext?.Request;

            if (request?.ContentType != null && request.ContentType.Contains("multipart/form-data"))
            {
                isFormsData = true;
            }
            else
            {
                // Populate PathParametersDictionary and QueryParametersDictionary from RequestBody if available
                RequestBody? requestBody = _httpContextAccessor.HttpContext?.Items[Constants.RequestBody] as RequestBody;

                if (requestBody != null)
                {
                    if (requestBody.PathParameters != null && requestBody.PathParameters.Count > 0)
                    {
                        PathParametersDictionary = CreatePathParametersDictionary(requestBody);
                        foreach (var pathParameter in PathParametersDictionary)
                        {
                            string bracketKey = $"{{{pathParameter.Key}}}";
                            apiUrl = apiUrl.Replace(bracketKey, pathParameter.Value.ToString());
                        }
                    }

                    if (requestBody.QueryParameters != null && requestBody.QueryParameters.Count > 0)
                    {
                        QueryParametersDictionary = CreateQueryParametersDictionary(requestBody);
                    }

                    if (requestBody.PayloadParameters != null && requestBody.PayloadParameters.Count > 0)
                    {
                        PayloadString = CreatePayloadParametersDictionary(requestBody);
                    }
                }
            }
        }

        public async Task<HttpResponseMessage> MakeExternalApiCallByContentType(
            string httpMethod,
            string apiUrl,
            string jwtToken,
            HttpRequest request,
            string endpointName,
            string? pydioUserToken = "")
        {
            // Determine if the request is multipart form-data
            if (request?.ContentType != null && request.ContentType.Contains("multipart/form-data"))
            {                
                return await _externalApiService.GetThirdPartyAsyncFilteredFormData(apiUrl, httpMethod, jwtToken, request, pydioUserToken);
            }
            else if (Array.Exists(NO_TRANSFORM_ENDPOINTS, endpoint => endpoint == endpointName))
            {
                // Call no-transform method for specific endpoints
                return await _externalApiService.GetThirdPartyDataAsyncNoTransform(apiUrl, httpMethod, jwtToken, QueryParametersDictionary, PayloadString, pydioUserToken);
            }
            else
            {
                // Default to calling the full response method for other cases
                return await _externalApiService.GetFullResponseThirdPartyDataAsync(apiUrl, httpMethod, jwtToken, QueryParametersDictionary, PayloadString, pydioUserToken);
            }
        }

        public void ReadRequestBody()
        {
            var request = _httpContextAccessor.HttpContext?.Request;

            // Check if the request contains form data
            if (request?.ContentType != null && request.ContentType.Contains("multipart/form-data"))
            {
                isFormsData = true;
                // Handle form data
                // For example, you might want to read the files and form data here
                var formCollection = request.Form;
                // Process form fields
                foreach (var field in formCollection)
                {
                    QueryParametersDictionary[field.Key] = field.Value;
                }
                // Process files
                foreach (var file in formCollection.Files)
                {
                    if (!formParametersDictionary.ContainsKey(file.Name))
                    {
                        formParametersDictionary[file.Name] = new List<IFormFile>();
                    }
                    formParametersDictionary[file.Name].Add(file);
                }
            }
            else
            {
                RequestBody? requestBody = _httpContextAccessor.HttpContext?.Items[Constants.RequestBody] as RequestBody;
                if (requestBody?.PathParameters != null && requestBody?.PathParameters.Count > 0)
                {
                    PathParametersDictionary = CreatePathParametersDictionary(requestBody);
                    foreach (var pathParameter in PathParametersDictionary)
                    {
                        string bracketkey = new string($"{{{pathParameter.Key}}}");
                        apiUrl = apiUrl.Replace(bracketkey, pathParameter.Value.ToString());
                    }
                }
                if (requestBody?.QueryParameters != null && requestBody?.QueryParameters.Count > 0)
                {
                    QueryParametersDictionary = CreateQueryParametersDictionary(requestBody);
                }
                if (requestBody?.PayloadParameters != null && requestBody?.PayloadParameters.Count > 0)
                {
                    PayloadString = CreatePayloadParametersDictionary(requestBody);
                }
            }
        }
        public Dictionary<string, string> CreatePathParametersDictionary(RequestBody requestBody)
        {
            if (requestBody == null)
            {
                throw new ArgumentNullException(nameof(requestBody));
            }

            Dictionary<string, string> pathParameters = new Dictionary<string, string>();
            if (requestBody.PathParameters != null)
            {
                foreach (var pair in requestBody.PathParameters)
                {
                    pathParameters[pair.Key] = pair.Value.ToString();
                }
            }
            return pathParameters;
        }
        public Dictionary<string, string> CreateQueryParametersDictionary(RequestBody requestBody)
        {
            if (requestBody == null)
            {
                throw new ArgumentNullException(nameof(requestBody));
            }

            Dictionary<string, string> queryParameters = new Dictionary<string, string>();
            if (requestBody.QueryParameters != null)
            {
                foreach (var pair in requestBody.QueryParameters)
                {
                    queryParameters[pair.Key] = pair.Value.ToString();
                }
            }
            return queryParameters;
        }
        public string CreatePayloadParametersDictionary(RequestBody requestBody)
        {
            if (requestBody == null)
            {
                throw new ArgumentNullException(nameof(requestBody));
            }
            else
            {
                return System.Text.Json.JsonSerializer.Serialize(requestBody.PayloadParameters);
            }
        }
        public bool HasBearerToken(string authHeader)
        {
            return authHeader != null && authHeader.StartsWith("Bearer ");
        }
        public string GetEndpointUrl(string endpointName)
        {
            var endpoints = _httpContextAccessor.HttpContext?.Items["Endpoints"] as Endpoints;
            var portfolioDocumentApiEndpoints = endpoints?.PortfolioDocumentApiEndpoints;
            var endpointValue = portfolioDocumentApiEndpoints?.GetType().GetProperty(endpointName)?.GetValue(portfolioDocumentApiEndpoints, null) as MicroSaasWebApi.Models.PXW.AppSettings.Endpoint;
            return endpointValue?.URL ?? string.Empty;
        }

        public async Task<IActionResult> FetchApiEndpointsDataWithOptionalToken(string endpointName, bool isAuthHeaderPassed, string? httpMethodName = "GET")
        {
            try
            {
                // Log information about the request
                await _loggerService.LogRequestInformationAsync();

                // Initialize variables
                var httpMethod = httpMethodName ?? "GET";
                string jwtToken = string.Empty;

                // Handle authorization if required
                if (isAuthHeaderPassed)
                {
                    String? authHeader = _httpContextAccessor.HttpContext?.Request.Headers[Constants.Authorization];
                    if (authHeader != null && authHeader.StartsWith(BEARER_PREFIX))
                    {
                        jwtToken = authHeader.Substring(BEARER_PREFIX.Length).Trim();
                    }
                    else
                    {
                        await _loggerService.LogInformationAsync("Bearer Token not found");
                        throw new Exception("Bearer Token not found");
                    }
                }

                // Get Api Url
                var dataApiDomainUrl = GetDataApiDomainUrl();
                var endpointUrl = string.Empty;
                if (_httpContextAccessor.HttpContext?.Items["Endpoints"] is Endpoints endpoints)
                {
                    var portfolioDataApiEndpoints = endpoints.PortfolioDataApiEndpoints;
                    var propertyInfo = portfolioDataApiEndpoints?.GetType().GetProperty(endpointName);
                    var propertyValue = propertyInfo?.GetValue(portfolioDataApiEndpoints, null);

                    if (propertyValue is MicroSaasWebApi.Models.PXW.AppSettings.Endpoint endpointValue)
                    {
                        endpointUrl = endpointValue.URL;
                    }
                }
                apiUrl = $"{dataApiDomainUrl}{endpointUrl}";
                if (_httpContextAccessor.HttpContext is not null)
                {
                    ReadRequestBody();
                    if (isFormsData)
                    {
                        return (IActionResult)await _externalApiService.GetThirdPartyAsyncFormData(apiUrl, httpMethod, jwtToken, _httpContextAccessor.HttpContext.Request);
                    }
                    else
                    {
                        var language = _httpContextAccessor.HttpContext?.Request.Headers[Constants.AcceptLanguage];
                        // Call external API using the service
                        var (success, result, errorMessage, statusCode) = await _externalApiService.
                            GetThirdPartyDataAsync(apiUrl, httpMethod, jwtToken, QueryParametersDictionary, PayloadString, language);
                        HttpStatusCode httpStatusCode = (HttpStatusCode)statusCode;
                        int statusCodeValue = (int)httpStatusCode;
                        if (success)
                        {
                            return new ObjectResult(result) { StatusCode = statusCodeValue };
                        }
                        else
                        {
                            return new ObjectResult(errorMessage) { StatusCode = statusCodeValue };
                        }
                    }
                }
                else
                {
                    // Return an appropriate response if context is null
                    return new ObjectResult("Invalid context") { StatusCode = StatusCodes.Status400BadRequest };
                }
            }
            catch (MsalException ex)
            {
                await _loggerService.LogInformationAsync($"MsalException occurred : {ex.Message}");
                return new ObjectResult($"MsalException occurred : {ex.Message}") { StatusCode = StatusCodes.Status400BadRequest };
            }
            catch (Exception ex)
            {
                await _loggerService.LogInformationAsync(ex.Message);
                return new ObjectResult(ex.Message) { StatusCode = StatusCodes.Status500InternalServerError };
            }
        }

        public async Task<IActionResult> FetchApiEndpointsPermissionWithOptionalToken(string endpointName,
        bool isAuthHeaderPassed, string? httpMethodName = "GET")
        {
            try
            {
                // Log information about the request
                await _loggerService.LogRequestInformationAsync();

                // Initialize variables
                var httpMethod = httpMethodName ?? "GET";
                string jwtToken = string.Empty;

                // Handle authorization if required
                if (isAuthHeaderPassed)
                {
                    String? authHeader = _httpContextAccessor.HttpContext?.Request.Headers[Constants.Authorization];
                    if (authHeader != null && authHeader.StartsWith(BEARER_PREFIX))
                    {
                        jwtToken = authHeader.Substring(BEARER_PREFIX.Length).Trim();
                    }
                    else
                    {
                        await _loggerService.LogInformationAsync("Bearer Token not found");
                        throw new Exception("Bearer Token not found");
                    }
                }

                // Get Api Url
                var permissionApiDomainUrl = GetPermissionApiDomainUrl();
                var endpointUrl = string.Empty;
                if (_httpContextAccessor.HttpContext?.Items["Endpoints"] is Endpoints endpoints)
                {
                    var portfolioPermissionApiEndpoints = endpoints.PortfolioPermissionApiEndpoints;
                    var propertyInfo = portfolioPermissionApiEndpoints?.GetType().GetProperty(endpointName);
                    var propertyValue = propertyInfo?.GetValue(portfolioPermissionApiEndpoints, null);

                    if (propertyValue is MicroSaasWebApi.Models.PXW.AppSettings.Endpoint endpointValue)
                    {
                        endpointUrl = endpointValue.URL;
                    }
                }
                apiUrl = $"{permissionApiDomainUrl}{endpointUrl}";
                ReadRequestBody();
                var language = _httpContextAccessor.HttpContext?.Request.Headers[Constants.AcceptLanguage];
                // Call external API using the service
                var (success, result, errorMessage, statusCode) = await _externalApiService.
                    GetThirdPartyDataAsync(apiUrl, httpMethod, jwtToken, QueryParametersDictionary, PayloadString, language);
                HttpStatusCode httpStatusCode = (HttpStatusCode)statusCode;
                int statusCodeValue = (int)httpStatusCode;
                if (success)
                {
                    return new ObjectResult(result) { StatusCode = statusCodeValue };
                }
                else
                {
                    return new ObjectResult(errorMessage) { StatusCode = statusCodeValue };
                }
            }
            catch (MsalException ex)
            {
                await _loggerService.LogInformationAsync($"MsalException occurred : {ex.Message}");
                return new ObjectResult($"MsalException occurred : {ex.Message}") { StatusCode = StatusCodes.Status400BadRequest };
            }
            catch (Exception ex)
            {
                await _loggerService.LogInformationAsync(ex.Message);
                return new ObjectResult(ex.Message) { StatusCode = StatusCodes.Status500InternalServerError };
            }
        }
        public async Task<IActionResult> FetchApiEndpointsDataInterchangeWithOptionalToken(string endpointName,
         bool isAuthHeaderPassed, string? httpMethodName = "GET")
        {
            try
            {
                // Log information about the request
                await _loggerService.LogRequestInformationAsync();

                // Initialize variables
                var httpMethod = httpMethodName ?? "GET";
                string jwtToken = string.Empty;

                // Handle authorization if required
                if (isAuthHeaderPassed)
                {
                    String? authHeader = _httpContextAccessor.HttpContext?.Request.Headers[Constants.Authorization];
                    if (authHeader != null && authHeader.StartsWith(BEARER_PREFIX))
                    {
                        jwtToken = authHeader.Substring(BEARER_PREFIX.Length).Trim();
                    }
                    else
                    {
                        await _loggerService.LogInformationAsync("Bearer Token not found");
                        throw new Exception("Bearer Token not found");
                    }
                }

                // Get Api Url
                var dataInterchangeApiDomainUrl = GetDataInterchangeApiUrl();
                var endpointUrl = string.Empty;

                if (_httpContextAccessor.HttpContext?.Items["Endpoints"] is Endpoints endpoints)
                {
                    var dataInterchangeApiEndpoints = endpoints.PortfolioDataInterchangeApiEndpoints;
                    var propertyInfo = dataInterchangeApiEndpoints?.GetType().GetProperty(endpointName);
                    var propertyValue = propertyInfo?.GetValue(dataInterchangeApiEndpoints, null);

                    if (propertyValue is MicroSaasWebApi.Models.PXW.AppSettings.Endpoint endpointValue)
                    {
                        endpointUrl = endpointValue.URL;
                    }
                }
                apiUrl = $"{dataInterchangeApiDomainUrl}{endpointUrl}";
                if (_httpContextAccessor.HttpContext is not null)
                {
                    ReadRequestBodyFromApi();
                    if (isFormsData)
                    {
                        return (IActionResult)await _externalApiService.GetThirdPartyAsyncFormData(apiUrl, httpMethod, jwtToken, _httpContextAccessor.HttpContext.Request);
                    }
                    else
                    {
                        var language = _httpContextAccessor.HttpContext?.Request.Headers[Constants.AcceptLanguage];
                        // Call external API using the service
                        var (success, result, errorMessage, statusCode) = await _externalApiService.
                            GetThirdPartyDataAsync(apiUrl, httpMethod, jwtToken, QueryParametersDictionary, PayloadString, language);
                        HttpStatusCode httpStatusCode = (HttpStatusCode)statusCode;
                        int statusCodeValue = (int)httpStatusCode;
                        if (success)
                        {
                            return new ObjectResult(result) { StatusCode = statusCodeValue };
                        }
                        else
                        {
                            return new ObjectResult(errorMessage) { StatusCode = statusCodeValue };
                        }
                    }
                }
                else
                {
                    // Return an appropriate response if context is null
                    return new ObjectResult("Invalid context") { StatusCode = StatusCodes.Status400BadRequest };
                }
            }
            catch (MsalException ex)
            {
                await _loggerService.LogInformationAsync($"MsalException occurred : {ex.Message}");
                return new ObjectResult($"MsalException occurred : {ex.Message}") { StatusCode = StatusCodes.Status400BadRequest };
            }
            catch (Exception ex)
            {
                await _loggerService.LogInformationAsync(ex.Message);
                return new ObjectResult(ex.Message) { StatusCode = StatusCodes.Status500InternalServerError };
            }
        }
        public AccountProfile GetAccountProfile(string token, string profileId)
        {
            ProfileEngine profileEngine = new ProfileEngine(GetDomainId(), GetLanguageId(), _applicationSettingsService, _httpContextAccessor);
            try
            {
                // Retrieve UserID from HttpContext
                string? UserID = _httpContextAccessor?.HttpContext?.Items["UserId"] as string;
                if (string.IsNullOrEmpty(UserID))
                {
                    throw new ArgumentException("User ID is null or empty");
                }
                // Get AccountMember
                AccountMember accountMember = profileEngine.GetAccountMember(UserID, token, profileId);
                // Find and return the matching AccountProfile
                return accountMember?.Account.Profiles.FirstOrDefault(a => a.ProfileID.Equals(profileId));
            }
            catch (Exception ex)
            {
                // Log the exception and rethrow
                // Assuming there's a logging mechanism in place, e.g., _loggerService
                _loggerService.LogErrorAsync(ex, "An error occurred while retrieving the account profile.");
                throw;  // This preserves the original stack trace
            }
        }
        public AccountProfile GetAccountProfileFromAccountMember(AccountMember member, string alphaCCOKey)
        {
            AccountProfile? accountProfile = null;
            if (member?.Account?.Profiles != null)
                accountProfile = member.Account?.Profiles?.FirstOrDefault(e => e.AlphaCCOKey.Equals(alphaCCOKey));

            if (accountProfile == null) accountProfile = new AccountProfile();
            return accountProfile;
        }

        public string GetParameter(JObject contentRequest, string key)
        {
            try { return contentRequest.GetValue(key).ToString(); } catch { return string.Empty; }
        }

        // This method converts the payload string to a strongly-typed object
        public T ConvertPayloadToObject<T>(string payloadString)
        {
            if (string.IsNullOrEmpty(payloadString))
            {
                // If the payloadString is null or empty, return the default value for T
                return default(T);
            }
            try
            {
                // Parse the payloadString to a JObject
                JObject contentRequest = JObject.Parse(payloadString);

                // Convert the JObject to the desired strongly-typed object of type T
                T request = contentRequest.ToObject<T>();

                // Return the converted object
                return request;
            }
            catch (Exception ex)
            {
                // Handle any exceptions that occur during parsing or conversion.
                // You may log the exception or take appropriate actions based on your requirements.
                // For simplicity, we are re-throwing the exception here.
                throw;
            }
        }

        public virtual string GetDocumentApiDomainUrl()
        {
            // Get the value of "FilteredTenant" from HttpContext.Items
            Tenant? filteredTenantJson = _httpContextAccessor.HttpContext?.Items["FilteredTenant"] as Tenant;
            if (string.IsNullOrEmpty(filteredTenantJson?.Settings?.PortfolioDocumentApi?.Domain))
            {
                // Throw a more specific exception
                throw new InvalidOperationException("Document Domain Url not found");
            }
            return filteredTenantJson?.Settings?.PortfolioDocumentApi?.Domain ?? throw new InvalidOperationException("Document Domain is null");
        }
        public async Task<IActionResult> GetDocumentApiEndpointsWithB2CToken(string endpointName, string? httpMethodName = "GET")
        {
            try
            {
                await _loggerService.LogRequestInformationAsync();

                String? authHeader = _httpContextAccessor.HttpContext?.Request.Headers[Constants.Authorization];

                if (HasBearerToken(authHeader))
                {
                    var httpMethod = httpMethodName ?? "GET";
                    var jwtToken = authHeader.Substring("Bearer ".Length).Trim();

                    var documentApiDomainUrl = GetDocumentApiDomainUrl();
                    var endpointUrl = GetEndpointUrl(endpointName);
                    apiUrl = $"{documentApiDomainUrl}{endpointUrl}";
                    var pydioUserToken = _httpContextAccessor.HttpContext?.Request.Headers["Pydio-User-Token"];
                    // do not read body for upload documents
                    if (!Array.Exists(FORM_DATA_ENDPOINTS, endpoint => endpoint == endpointName))
                    {
                        ReadRequestBody();
                    }
                    var response = await MakeExternalApiCall(httpMethod, apiUrl, jwtToken, endpointName, pydioUserToken);
                    return await ProcessThirdPartyApiResponse(response);
                }
                else
                {
                    throw new Exception("Bearer Token not found");
                }
            }
            catch (MsalException ex)
            {
                throw new Exception($"MsalException occurred : {ex.Message}");
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<HttpResponseMessage> MakeExternalApiCall(string httpMethod, string apiUrl, string jwtToken, string endpointName, string? pydioUserToken = "")
        {
            if (Array.Exists(NO_TRANSFORM_ENDPOINTS, endpoint => endpoint == endpointName))
            {
                return await _externalApiService.GetThirdPartyDataAsyncNoTransform(apiUrl, httpMethod, jwtToken, QueryParametersDictionary, PayloadString, pydioUserToken);
            }
            else if (Array.Exists(FORM_DATA_ENDPOINTS, endpoint => endpoint == endpointName))
            {
                return await _externalApiService.GetThirdPartyAsyncFormData(apiUrl, httpMethod, jwtToken, _httpContextAccessor.HttpContext?.Request, pydioUserToken);
            }
            else
            {
                return await _externalApiService.GetFullResponseThirdPartyDataAsync(apiUrl, httpMethod, jwtToken, QueryParametersDictionary, PayloadString, pydioUserToken);
            }
        }
        public async Task<IActionResult> ProcessThirdPartyApiResponse(HttpResponseMessage response)
        {
            var httpStatusCode = (HttpStatusCode)(int)response.StatusCode;
            var statusCodeValue = (int)httpStatusCode;

            if (response.IsSuccessStatusCode)
            {
                await _loggerService.LogInformationAsync("ProcessThirdPartyApiResponse - Is SuccessStatusCode");
                var contentType = response.Content.Headers.ContentType?.ToString();
                await _loggerService.LogInformationAsync("ProcessThirdPartyApiResponse - RESPONSE CONTENT-TYPE: " + contentType);
                // Handle different content types appropriately
                if (contentType != null && contentType.Contains("application/json"))
                {
                    await _loggerService.LogInformationAsync("ProcessThirdPartyApiResponse - ContentType is application/json");
                    var content = await response.Content.ReadAsStringAsync();
                    var json = JsonConvert.DeserializeObject(content);
                    var res = new ObjectResult(json) { StatusCode = statusCodeValue };
                    if (response.Headers != null && response.Headers.Contains("Pydio-User-Token"))
                    {
                        _httpContextAccessor.HttpContext?.Response.Headers.Add("Pydio-User-Token", response.Headers.GetValues("Pydio-User-Token").ToArray());
                    }
                    return res;
                }
                else if (contentType != null && ACCEPTED_FILE_CONTENT_TYPES.Contains(contentType))
                {
                    await _loggerService.LogInformationAsync("ProcessThirdPartyApiResponse - content type is accepted file content type");
                    var content = response.Content.ReadAsByteArrayAsync().Result;
                    ContentDispositionHeaderValue contentDisposition = response.Content.Headers.ContentDisposition;
                    string filename = contentDisposition?.FileName?.Trim('"');
                    var res = new FileContentResult(content, contentType) { FileDownloadName = filename };
                    if (response.Headers != null && response.Headers.Contains("Pydio-User-Token"))
                    {
                        _httpContextAccessor.HttpContext.Response.Headers.Add("Pydio-User-Token", response.Headers.GetValues("Pydio-User-Token").ToArray());
                    }
                    return res;
                }
                else
                {
                    await _loggerService.LogInformationAsync("ProcessThirdPartyApiResponse - content type is not accepted content type");
                    var content = response.Content.ReadAsStringAsync().Result;
                    var res = new ObjectResult("Unhandled Content Type") { StatusCode = statusCodeValue };
                    if (response.Headers != null && response.Headers.Contains("Pydio-User-Token"))
                    {
                        _httpContextAccessor.HttpContext?.Response.Headers.Add("Pydio-User-Token", response.Headers.GetValues("Pydio-User-Token").ToArray());
                    }
                    return res;
                }
            }
            else
            {
                await _loggerService.LogInformationAsync("ProcessThirdPartyApiResponse - Response Status Code not Success");
                var errorMessage = response.ReasonPhrase ?? "Unknown Error";
                var res = new ObjectResult(errorMessage) { StatusCode = statusCodeValue };
                return res;
            }
        }




        public virtual string? GetDataApiDomainUrl()
        {
            // Get the value of "FilteredTenant" from HttpContext.Items
            Tenant? filteredTenantJson = _httpContextAccessor.HttpContext?.Items["FilteredTenant"] as Tenant;
            if (string.IsNullOrEmpty(filteredTenantJson?.Settings?.PortfolioDataApi?.Domain))
            {
                // Handle the case where the tenant is not found or is null
                throw new Exception("Data Domain Url not found");
            }
            return filteredTenantJson?.Settings?.PortfolioDataApi?.Domain;
        }
        public virtual string? GetPermissionApiDomainUrl()
        {
            // Get the value of "FilteredTenant" from HttpContext.Items
            Tenant? filteredTenantJson = _httpContextAccessor.HttpContext?.Items["FilteredTenant"] as Tenant;
            if (string.IsNullOrEmpty(filteredTenantJson?.Settings?.PortfolioPermissionApi?.Domain))
            {
                // Handle the case where the tenant is not found or is null
                throw new Exception("PermissionApi Domain Url not found");
            }
            return filteredTenantJson?.Settings?.PortfolioPermissionApi?.Domain;
        }
        public virtual string? GetDataInterchangeApiUrl()
        {
            // Get the value of "FilteredTenant" from HttpContext.Items
            Tenant? filteredTenantJson = _httpContextAccessor.HttpContext?.Items["FilteredTenant"] as Tenant;
            if (string.IsNullOrEmpty(filteredTenantJson?.Settings?.PortfolioDataInterchangeApi?.Domain))
            {
                // Handle the case where the tenant is not found or is null
                throw new Exception("DataInterchange Domain Url not found");
            }
            return filteredTenantJson?.Settings?.PortfolioDataInterchangeApi?.Domain;
        }
    }

}
