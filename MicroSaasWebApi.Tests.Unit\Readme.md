﻿Why XUnit Testing?
--> More Popular Currently & It is a evolution from NUnit, created from the developers of Nunit team.
--> In NUnit the instance is initialed once for all the [Tests] in a [TextFixture].
--> In XUnit the class is initialed once per test [Fact] using dependency injection.Leads to better tests.
No need to add code to create different state.
--> Uses C# contructs & [Setup] & [Teardown] Attribute usage can be performed using constructor & IPisposible interface from C#.
Ref: https://docs.nunit.org/articles/nunit/writing-tests/setup-teardown/index.html
--> Attribute-Based Test Execution: xUnit uses a more modern and flexible approach to controlling test execution
through attributes, which can be more extensible and customizable compared to NUnit's approach.
--> Immutability and Parallelization: xUnit encourages immutability and better isolation between tests 
by not allowing setup [SetUp] and teardown [TearDown] methods, which can be shared across multiple tests.
This approach allows for safer parallel test execution, which can result in faster test runs.
--> Community and Support: xUnit has been gaining more traction in the .NET community and has good support,
especially for .NET Core and .NET 5/6+. It’s actively maintained and has good documentation.
--> Built-in Functionality: xUnit provides some built-in functionality like data theories (similar to NUnit's TestCase),
which allow tests to be run with different input data, thus avoiding the need for multiple similar tests.
--> Cleaner Test Output: Some developers find that xUnit produces cleaner, more concise test output compared to NUnit,
which can be especially helpful in CI/CD pipelines.
--> ASP.NET Core Integration: xUnit has been used and recommended by Microsoft for testing ASP.NET Core applications,
which may make integration smoother if you are working in that ecosystem.
--> Constructor Injection for Test Classes: xUnit supports constructor injection in test classes, making it easier
to work with Dependency Injection and promoting better design principles.
--> Custom Test Framework Extensibility: xUnit provides a way to define custom behaviors by implementing interface methods,
offering more flexibility and control over the testing framework.

Run Code coverage report
Local(Console):
cd YourSolutionFolder/MicroSaasWebApi.Tests.Unit
dotnet add package coverlet.msbuild
dotnet tool install -g dotnet-reportgenerator-globaltool

dotnet test /p:CollectCoverage=true /p:CoverletOutputFormat=cobertura
dotnet test /p:CollectCoverage=true /p:CoverletOutputFormat=cobertura /p:Exclude="[MicroSaasWebApi.Models]*"

reportgenerator -reports:coverage.cobertura.xml -targetdir:coverlet/reports -reporttypes:"Cobertura"
reportgenerator -reports:coverage.cobertura.xml -targetdir:coverlet/reports -reporttypes:HtmlInline_AzurePipelines

dotnet test /p:CollectCoverage=true /p:CoverletOutputFormat=opencover /p:CoverletOutput=./coverage-reports/
dotnet test MicroSaasWebApi.Tests.Unit.csproj /p:CollectCoverage=true /p:CoverletOutputFormat=json /p:CoverletOutput=./coverage-reports/

or 
dotnet test --no-build --collect:"XPlat Code Coverage" '/p:Exclude=[*]SharePointAPI*;[*]MicroSaasWebApi.Properties*;[*]MicroSaasWebApi.Controllers.PHW*' '/p:ExcludeByFile=*.json;*.md'
dotnet test --no-build --collect:"XPlat Code Coverage" /p:Exclude="[*]SharePointAPI*;[*]MicroSaasWebApi.Properties*;[*]MicroSaasWebApi.Models*" /p:ExcludeByFile="*.json;*.md"

or
coverlet .\bin\Debug\net7.0\MicroSaasWebApi.Tests.Unit.dll --target "dotnet" --targetargs "test --no-build" --exclude "[*]SharePointAPI*]" --exclude "[*]MicroSaasWebApi.Properties*" --exclude "[*]MicroSaasWebApi.Properties*" --exclude "[*]MicroSaasWebApi.Controllers.PHW*" --exclude-by-file "*.json" --exclude-by-file "*.md"
or
coverlet .\bin\Debug\net7.0\MicroSaasWebApi.Tests.Unit.dll --target "dotnet" --targetargs "test --no-build" --format "opencover" --output .\coverage-reports\

To integrate this into your Azure Pipeline, you can add a script step in your
azure-pipelines.yml to execute this command. 
After generating the coverage report, you might also want to publish it to Azure DevOps using 
the PublishCodeCoverageResults@1 task.