﻿using MicroSaasWebApi.Models.AlphaCCO;
using MicroSaasWebApi.Properties.Enums;

namespace MicroSaasWebApi.Models.Profile
{
    /// <summary>
    /// Summary description for Account
    /// </summary>
    public class AccountProfile
    {
        //TODO: later add permissioning properties to this object for access rights.  
        public string? ExternalId { get; set; }  //for future use
        public string? Email { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Organization { get; set; }
        public List<Account>? Accounts { get; set; }
        public string? PreferredLanguage { get; set; }
        public string? AlphaCCOKey { get; set; }
        public bool InitialWorkflowsCompleted { get; set; } = false;
        public bool CanBypassInitialWorkflows { get; set; } = false;
        public eAccountProfileType ProfileType { get; set; }
        public List<AlphaCCOWorkflowResponse>? Workflows { get; set; }
        public string? Name { get; set; }
        public int ID { get; set; }
        public bool IsActive { get; set; }
        public Guid MasterID { get; set; }
        public int SortOrder { get; set; }
        public string? ProfileID { get; set; }
    }
}