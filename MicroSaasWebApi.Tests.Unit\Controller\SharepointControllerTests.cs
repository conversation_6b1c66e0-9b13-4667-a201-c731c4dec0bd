﻿using FakeItEasy;
using Microsoft.Extensions.DependencyInjection;
using MicroSaasWebApi.Controllers.PXW;
using MicroSaasWebApi.Services.PXW.Interface;
using Microsoft.AspNetCore.Hosting;

namespace MicroSaasWebApi.Tests.Unit.Controller
{
    public class SharepointControllerTests
    {        
        private SharepointController _sharepointController;       

        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly IBaseService _commonService;
        private readonly ILoggerService _loggerService;


        const string endpointName = "ProfilesAccountsSummaryRetrieve";
        const string fakeAuthHeader = "Bearer someValidToken";
        const string fakeUrl = "http://fakedportfolioxpressway.sharepoint.com";
        const string jwtToken = "eyJhbGciOiJSUzI1NiIsImtpZCI6Im1fZmN2QU9XNHZoWXdJTnRYYW0zbFNtamRJdkRQT004bER2aHNTNWdkY3MiLCJ0eXAiOiJKV1QifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YSBt7aLlhCdSbDkb8d5XzPlIgu1owNU--UKxI0mAKKr1RoWsQ52J2Na4knfDiAWzXegHMMl-CvyUIbg7o5Tyq2qPzBcVRA4hfpTuQVwxEofc2-3mJX0sjsu47dwwYKAMGGj8mkHFwZZ_gUr35wtWMbXI-6fii_V52HTbu7ukiQSRXVXl0zkeC9jUt7MFkhVvm6NRei-vNeVK1eX7i05kaT5PVq8IjovC2zlcX69QrzcxJEkPgtMO1qfbDJBVJPl1-YPcwolD6wStdS0rzYWJam1rM0jeR-vOTBXDXf46P7I_SvkKYKE8LBfiCY6Ns-MKlHa2WTRI6w0Z2p26Nkc81w";
        const string responseBody = "{ \"success\": true }";

        public SharepointControllerTests()
        {
            //_httpContextAccessor = A.Fake<IHttpContextAccessor>();
            //_configuration = A.Fake<IConfiguration>();
            //var builder = new ConfigurationBuilder()
            //                   .SetBasePath(Directory.GetCurrentDirectory())
            //                   .AddJsonFile("appsettings.json");
            //_configuration = builder.Build();

            //_serviceProvider = A.Fake<IServiceProvider>();
            //_applicationSettingsService = A.Fake<IApplicationSettingsService>();
            _webHostEnvironment = A.Fake<IWebHostEnvironment>();
            _loggerService = A.Fake<ILoggerService>();

            _sharepointController = new SharepointController(_webHostEnvironment, _commonService, _loggerService);
        }

        #region ShouldReturn200OK_WhenCalledSuccessfully
        [Fact]
        public async Task GetDocuments_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
                        

            // Mock External API call
                
                        
            //// Act
            //var result = await _dataApiController.ProfilesAccountsSummaryRetrieve();

            //// Assert
            //result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            //A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            //result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        public async Task ProfilesAccountsSummaryRetrieve_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            

            //// Act
            //var result = await _dataApiController.ProfilesAccountsSummaryRetrieve();

            //// Assert
            //result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            //A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            //result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        public async Task ProfilesAsOfDatesRetrieve_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            

            //// Act
            //var result = await _dataApiController.ProfilesAccountsSummaryRetrieve();

            //// Assert
            //result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            //A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            //result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        public async Task ProfilesCustodianAccountCodesRetrieve_ShouldReturn200OK_WhenCalledSuccessfully()
        {
            // Arrange
            // Set up your specific scenario, faking necessary calls, and setting expectations.
            

            //// Act
            //var result = await _dataApiController.ProfilesAccountsSummaryRetrieve();

            //// Assert
            //result.Should().BeOfType<ObjectResult>().Which.Value.Should().Be("SomeResult");
            //A.CallTo(() => _loggerService.LogRequestInformationAsync()).MustHaveHappenedOnceExactly();
            //result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status200OK);
        }
        
        #endregion
        //#region ShouldReturn500_WhenEndpointUrlNotFound
        //[Fact]
        //public async Task AccountLevelPerformance_ShouldReturn500_WhenEndpointUrlNotFound()
        //{
        //    // Arrange
        //    //var mockExternalApiService = new Mock<IExternalApiService>();
        //    //var mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
        //    //var mockResponseLoggerService = new Mock<ILoggerService>();
        //    //var controller = new YourController(mockExternalApiService.Object, mockHttpContextAccessor.Object, mockResponseLoggerService.Object);

        //    //mockHttpContextAccessor.Setup(x => x.HttpContext.Request.Headers[It.IsAny<string>()])
        //    //    .Returns("Bearer SampleToken");
        //    //mockHttpContextAccessor.Setup(x => x.HttpContext.Items["Endpoints"])
        //    //    .Returns(new Endpoints());

        //    // Act
        //    var result = await _dataApiController.AccountLevelPerformance();

        //    // Assert
        //    var internalServerErrorResult = Assert.IsType<ObjectResult>(result);
        //    Assert.Equal(500, internalServerErrorResult.StatusCode);
        //}
        //#endregion
        //#region ShouldReturn400BadRequest_WhenMsalExceptionOccurs
        //[Fact]
        //public async Task ProfilesAccountsSummaryRetrieve_ShouldReturn400BadRequest_WhenBearerTokenNotFound()
        //{
        //    // Arrange
        //    // Set up fake HttpContextAccessor with null or invalid Authorization header.

        //    // Act
        //    var result = await _dataApiController.ProfilesAccountsSummaryRetrieve();

        //    // Assert
        //    result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
        //}

        //#endregion
        //#region ShouldReturn400BadRequest_WhenMsalExceptionOccurs
        //[Fact]
        //public async Task ProfilesAccountsSummaryRetrieve_ShouldReturn400BadRequest_WhenMsalExceptionOccurs()
        //{
        //    // Arrange
        //    // Fake the necessary service to throw MsalException.

        //    // Act
        //    var result = await _dataApiController.ProfilesAccountsSummaryRetrieve();

        //    // Assert
        //    result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
        //}
        //#endregion
        //#region ShouldReturn500InternalServerError_WhenGenericExceptionOccurs
        //[Fact]
        //public async Task ProfilesAccountsSummaryRetrieve_ShouldReturn500InternalServerError_WhenGenericExceptionOccurs()
        //{
        //    // Arrange
        //    // Fake the necessary service to throw a generic exception.

        //    // Act
        //    var result = await _dataApiController.ProfilesAccountsSummaryRetrieve();

        //    // Assert
        //    result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status500InternalServerError);
        //}
        //#endregion
        //#region WhenApiCallFails
        //[Fact]
        //public async Task ProfilesAccountsSummaryRetrieve_ShouldReturnStatusCode_WhenApiCallFails()
        //{
        //    // Arrange
        //    // Fake the ExternalApiService to return failure.

        //    // Act
        //    var result = await _dataApiController.ProfilesAccountsSummaryRetrieve();

        //    // Assert
        //    result.Should().BeOfType<ObjectResult>();
        //    // Additional assertions based on failure status code.
        //}
        //#endregion


    }

}
