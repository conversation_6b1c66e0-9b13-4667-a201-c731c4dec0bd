﻿using Microsoft.Identity.Client;
using MicroSaasWebApi.Models.PXW.AppSettings;
using MicroSaasWebApi.Properties.PXW;
using MicroSaasWebApi.Services.Auth.PXW.Interface;
using MicroSaasWebApi.Services.PXW.Interface;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

namespace MicroSaasWebApi.Middlewares
{
    /// <summary>
    /// B2C Token validation in Middleware
    ///https://ivanstambuk.github.io/asp.net/2020/05/20/manually-validating-azure-ad-microsoft-identity-platform-JWT-access-tokens-in-ASP.NET.html
    ///https://learn.microsoft.com/en-us/shows/seth-juarez/aspnet-core-authorization-with-barry-dorrans
    /// </summary>
    public class AzureB2CTokenAuthenticationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IConfiguration _configuration;
        private readonly IJwtValidationService _jwtValidationService;
        private readonly ILoggerService _loggerService;
        private readonly IApplicationSettingsService _applicationSettingsService;
        public static string?[] AzureB2CClientIds = Array.Empty<string>();

        public AzureB2CTokenAuthenticationMiddleware(RequestDelegate next,
            IConfiguration configuration, IServiceProvider serviceProvider)
        {
            _next = next;
            _configuration = configuration;
            using (var scope = serviceProvider.CreateScope())
            {
                _jwtValidationService = scope.ServiceProvider.GetRequiredService<IJwtValidationService>();
                _loggerService = scope.ServiceProvider.GetRequiredService<ILoggerService>();
                _applicationSettingsService = scope.ServiceProvider.GetRequiredService<IApplicationSettingsService>();
            }
            // later in your code:
            var tenants = new List<Tenant>();
            _configuration.Bind($"{Constants.Tenants}", tenants);
            //Get list of ClientIDs from Tenants Collection from appsettings.
            if (tenants != null && tenants.Any(t => t.Settings?.AzureAdB2C != null && t.Settings?.AzureAdB2C.ClientId != null))
            {
                AzureB2CClientIds = tenants.Where(t => t.Settings?.AzureAdB2C != null && t.Settings?.AzureAdB2C.ClientId != null)
                                           .Select(t => t.Settings?.AzureAdB2C?.ClientId).ToArray();
            }
        }

        public async Task InvokeAsync(HttpContext context)
        {
            bool processed = false;  // Initialize flag
            await _loggerService.LogRequestInformationAsync();
            if (context.Request.Path == "/api/PHW/Token" && context.Request.Method == HttpMethods.Get)
            {
                context.Response.Redirect($"/api/PHW/GetToken");
            }
            //Read the Authorization Header
            String? authHeader = context.Request.Headers[Constants.Authorization];
            if (authHeader == null)
            {
                await LogAndHandleUnprotectedAPI();
            }
            else if (authHeader != null && authHeader.StartsWith($"{Constants.Bearer} "))
            {
                try
                {
                    await _loggerService.LogInformationAsync(Constants.AuthorizationHeaderPresent);
                    await HandleAuthenticatedRequest(authHeader, context);
                    processed = false;
                    await _loggerService.LogInformationAsync(Constants.BearerTokenSuccessful);
                }
                catch (MsalException ex)
                {
                    await _loggerService.SetResponseAndLogAsync(400, ex.ErrorCode);
                    processed = true;
                    return;
                }
                catch (Exception ex)
                {
                    await _loggerService.SetResponseAndLogAsync(StatusCodes.Status401Unauthorized, ex.Message, ex);
                    processed = true;
                    return;
                }
            }
            if (!processed)
            {
                await _next(context);
            }
        }

        private async Task LogAndHandleUnprotectedAPI()
        {
            await _loggerService.LogTraceAsync(Constants.AuthorizationHeaderNotPresent);
            await _applicationSettingsService.SetAppSettingsEndPointsInHttpContext();
        }

        private async Task HandleAuthenticatedRequest(string authHeader, HttpContext context)
        {
            var jwtToken = authHeader.Substring($"{Constants.Bearer} ".Length).Trim();
            var jwtSecurityTokenHandler = new JwtSecurityTokenHandler();
            var decodedToken = jwtSecurityTokenHandler.ReadJwtToken(jwtToken);

            // do something with the claims...
            var aud = decodedToken.Audiences.First();  // Client ID

            if (!AzureB2CClientIds.Contains(aud))
            {
                await _loggerService.SetResponseAndLogAsync(400, Constants.InvalidClientRequest);
                return;
            }
            if (AzureB2CClientIds.Contains(aud))
            {
                await ValidateAndSetClaims(decodedToken, jwtToken, context);
            }
        }

        private async Task ValidateAndSetClaims(JwtSecurityToken decodedToken, string jwtToken, HttpContext context)
        {
            var claims = decodedToken.Claims;
            var host = context.Request.Host.Host;
            var decodedTokenClientId = decodedToken.Audiences.First();
            ClaimsPrincipal claimsPrincipal = await _jwtValidationService.ValidateToken(jwtToken, decodedTokenClientId);

            if (claimsPrincipal != null && claimsPrincipal?.Identity?.IsAuthenticated == true)
            {
                _applicationSettingsService.SetTokenValuesInHttpContext(decodedToken);
            }
            else
            {
                throw new Exception("Invalid Token or Authentication failed");
            }

        }

        //public async Task<AuthenticationResult> GetNewAzureB2CTokenAuthenticationResult()
        //{
        //    string clientId = _configuration["AzureAdB2C:ClientId"];
        //    string clientSecret = _configuration["AzureAdB2C:ClientSecret"];
        //    string instance = _configuration["AzureAdB2C:Instance"];
        //    string tenantId = _configuration["AzureAdB2C:TenantId"];
        //    string baseUrl = _configuration["AzureAdB2C:BaseUrl"];
        //    string scopesForToken = _configuration["AzureAdB2C:Scopes"];
        //    string Token = _configuration["AzureAdB2C:Token"];

        //    // For Web applications that use OpenID Connect Authorization Code flow, use IConfidentialClientApplication
        //    IConfidentialClientApplication app;

        //    app = ConfidentialClientApplicationBuilder
        //            .Create(clientId)
        //            .WithClientSecret(clientSecret)
        //            .WithAuthority(new Uri($"https://login.microsoftonline.com/{tenantId}"))
        //            .WithLegacyCacheCompatibility(false)
        //            .Build();

        //    // For confidential clients, this value should use a format similar to {Application ID URI}/.default.
        //    // https://learn.microsoft.com/en-us/azure/active-directory/develop/quickstart-v2-netcore-daemon#requesting-tokens
        //    string[] scopes = new string[] { $"{baseUrl}/{clientId}/.default" };

        //    AuthenticationResult result = null;

        //    try
        //    {
        //        result = await app.AcquireTokenForClient(scopes).ExecuteAsync();
        //        _logger.LogInformation("Token Acquaired");
        //    }
        //    catch (MsalServiceException ex) when (ex.Message.Contains("AADSTS70011"))
        //    {
        //        // Invalid scope. The scope has to be of the form "https://resourceurl/.default"
        //        // Mitigation: change the scope to be as expected
        //        _logger.LogInformation("Scope provided is not supported");
        //    }

        //    return AuthenticationResult;
        //}
    }
}
