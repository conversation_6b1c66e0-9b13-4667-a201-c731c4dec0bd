﻿namespace MicroSaasWebApi.Models.Profile
{   /// <summary>
    /// Summary description for AccountInfo
    /// </summary>
    public class AccountInformation
    {
        public string? UserId { get; set; }
        public string? Email { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Organization { get; set; }
        public List<AccountProfile>? Profiles { get; set; }
    }
}