(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[932],{26385:function(e,t,s){Promise.resolve().then(s.bind(s,76429))},25523:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return a}});let a=s(47043)._(s(2265)).default.createContext(null)},76429:function(e,t,s){"use strict";s.d(t,{default:function(){return f}});var a=s(57437),i={src:"/_next/static/media/hero-bg1.10d13118.svg",height:800,width:1440,blurWidth:0,blurHeight:0},l={src:"/_next/static/media/hero-bg2.aaeb8708.svg",height:800,width:1440,blurWidth:0,blurHeight:0},r={src:"/_next/static/media/tools1.6d5a1b57.png",height:573,width:619,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAMAAAACh/xsAAAANlBMVEXU3vTp6ery+Pvh4+bk5ujo6Onl5fHj4+bJzOrq6e3t6+vS0NipsMIRHUptdo7t7e/g5ejj5+v5eMsBAAAAEnRSTlMBVliuT7IiLw6Q6dSbzVR10ILTxfUAAAAACXBIWXMAAAsTAAALEwEAmpwYAAAANUlEQVR4nB3GWRIAIQjFwKjgA2e//2WnMD9poN/XsRIp7ZQmo6GkiojYH0orzM+eVuivuwM/JmwBFiFUIHgAAAAASUVORK5CYII=",blurWidth:8,blurHeight:7},o={src:"/_next/static/media/tools2.bf90bbc0.png",height:573,width:619,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAMAAAACh/xsAAAAM1BMVEUtLTcQFSIRHSweJDEjKDgcITEWHy8gJzkqM0I+R1QWHScnLzkfJCscKlR/jZsXHiYYHSZttTFSAAAAEXRSTlMBMyLSl4lZUi4PrK/pzRJ2v7T0OOsAAAAJcEhZcwAACxMAAAsTAQCanBgAAAA1SURBVHicHcbJEcAgDMBAAT6BhPRfbcboowXcn7GFCDVprbOcUKp8M+9XiBe6+rGCjflN4AciigERfodBxAAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:7},n=s(51133),c=s(33145),d=s(2265),x=s(69064);let h=[{alt:"User",src:"https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=3276&q=80"},{alt:"User",src:"https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80"},{alt:"User",src:"https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80"},{alt:"User",src:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80"},{alt:"User",src:"https://images.unsplash.com/photo-1488161628813-04466f872be2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=3376&q=80"}],u=e=>{let{priority:t}=e;return(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-start gap-3",children:[(0,a.jsx)("div",{className:"flex -space-x-2",children:h.map((e,s)=>(0,a.jsx)(c.default,{src:e.src,alt:e.alt,priority:t,width:48,height:48,className:"inline-block size-12 rounded-full ring-2 ring-white"},s))}),(0,a.jsxs)("div",{className:"flex flex-col justify-center items-center md:items-start gap-1",children:[(0,a.jsx)("div",{className:"rating flex",children:[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"w-5 h-5 text-yellow-500",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401z",clipRule:"evenodd"})},t))}),(0,a.jsxs)("div",{className:"text-base font-medium text-[#7B7E83] dark:text-[#808389]",children:[(0,a.jsx)("span",{className:"text-black1 dark:text-white",children:"25"})," makers ship faster"]})]})]})};var f=()=>{let[e,t]=(0,d.useState)(""),[s,h]=(0,d.useState)(""),f=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(String(e).toLowerCase()),A=async s=>{if(s.preventDefault(),!e){h("Email is required.");return}if(!f(e)){h("Please enter a valid email address.");return}try{let s=await fetch("/api/waiting-list",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e})});if(!s.ok)throw Error("Network response was not ok "+s.statusText);let a=await s.json();console.log("Success:",a),x.Am.success("Successfully added to waiting list!"),t("")}catch(e){console.error("Error:",e)}};return(0,a.jsxs)("div",{className:"relative flex justify-center items-center w-full",children:[(0,a.jsx)(c.default,{src:i,alt:"background",fill:!0,objectFit:"cover",priority:!0,className:"z-0 block dark:hidden"}),(0,a.jsx)(c.default,{src:l,alt:"background",fill:!0,objectFit:"cover",priority:!0,className:"z-0 hidden dark:block"}),(0,a.jsx)("div",{className:"relative z-10 max-w-[1440px] w-full px-4 sm:px-12 pb-12 mt-40",children:(0,a.jsxs)("div",{className:"flex justify-between lg:items-center flex-col lg:flex-row gap-8",children:[(0,a.jsxs)("div",{className:"lg:w-[45%] max-w-[529px]",children:[(0,a.jsxs)("div",{className:"text-[#7B7E83] dark:text-white mb-4 px-[5px] py-2 gap-2 text-sm font-medium border border-[#1AAB12] rounded-[10px] w-fit",children:[(0,a.jsx)("span",{className:"text-white bg-[#1AAB12] py-1 px-2 rounded-[5px] font-semibold",style:{lineHeight:"none"},children:"New"})," ","MicroSaaS Fast v2 is now availabe \uD83D\uDE80"]}),(0,a.jsxs)("h1",{className:"text-[42px] leading-[50px] font-bold text-black1 dark:text-white",children:["Launch Your SaaS ",(0,a.jsx)("span",{className:"",children:"Startup "}),(0,a.jsx)("span",{className:"line-through text-[#7B7E83] dark:text-[#4D525A]",children:"in 3 Month,"})," ","1 Day"]}),(0,a.jsx)("p",{className:"text-[#7B7E83] dark:text-[#808389] font-inter text-base font-medium my-6",children:"This NEXT.js boilerplate and SaaS starter kit with pre build modules is all what you need to ship your SaaS, AI tool or a startup in 38 min and start making money FAST"}),(0,a.jsx)("form",{onSubmit:A,children:(0,a.jsxs)("div",{className:"w-full flex flex-col sm:flex-row gap-y-4 gap-x-2",children:[(0,a.jsx)(x.x7,{position:"top-center",reverseOrder:!1}),(0,a.jsx)("input",{type:"email",placeholder:"Your Email",value:e,onChange:e=>{t(e.target.value),h("")},className:"border border-[#86898E] dark:border-[#5A5E66] w-full py-2 text-[#B7B8BB] dark:text-[#4D525A] placeholder-[#B7B8BB] dark:placeholder-[#4D525A] rounded-[8px] px-3 text-base font-medium bg-transparent"}),(0,a.jsx)("div",{className:"w-fit",children:(0,a.jsx)(n.Z,{text:"Get MicroSaaSFast",isIcon:!0})})]})}),(0,a.jsxs)("p",{className:"mt-4 mb-8 text-xs font-semibold text-[#7B7E83] dark:text-[#5A5E66]",children:["For Developers","   ","|","   ","Entrepreneurs","   ","|","   ","No-code lovers"]}),(0,a.jsx)(u,{})]}),(0,a.jsxs)("div",{className:"",children:[(0,a.jsx)(c.default,{src:r,alt:"tools",width:550,height:550,priority:!0,className:"object-contain lg:w-[550px] mx-auto block dark:hidden"}),(0,a.jsx)(c.default,{src:o,alt:"tools",width:550,height:550,priority:!0,className:"object-contain lg:w-[550px] mx-auto hidden dark:block"})]})]})})]})}},51133:function(e,t,s){"use strict";var a=s(57437);t.Z=e=>{let{text:t,isLeft:s,icon:i,isDownload:l,isDisable:r,isSubmit:o=!0,isLoading:n}=e;return(0,a.jsx)("button",{type:o?"submit":"button",className:"text-white font-medium whitespace-nowrap text-sm px-5 py-1.5 h-8 rounded-full w-full transition-all duration-300 bg-[#006fee] hover:bg-blue-700",disabled:n,children:n?(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"animate-spin",children:(0,a.jsx)("path",{d:"M21 12a9 9 0 1 1-6.219-8.56"})})}):(0,a.jsxs)("span",{className:"flex items-center justify-center ".concat(i?"gap-2":""),children:[s&&(0,a.jsx)("span",{className:"".concat(l?"rotate-90":""),children:i}),(0,a.jsx)("span",{children:t}),!s&&!r&&(0,a.jsx)("span",{className:"".concat(l?"rotate-90":""),children:i})]})})}}},function(e){e.O(0,[145,64,971,117,744],function(){return e(e.s=26385)}),_N_E=e.O()}]);