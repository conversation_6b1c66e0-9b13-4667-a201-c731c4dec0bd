﻿using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace MicroSaasWebApi.Health
{
    public class DataApiHealthCheck : IHealthCheck
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;
        public DataApiHealthCheck(IHttpClientFactory httpClientFactory, IConfiguration configuration)
        {
            _httpClientFactory = httpClientFactory;
            _configuration = configuration;
        }
        public async Task<HealthCheckResult> CheckHealthAsync
        (HealthCheckContext context,
            CancellationToken cancellationToken = default)
        {
            using (var httpClient = _httpClientFactory.CreateClient())
            {
                // Access the Domain value
                string portfolioDataApiDomain = _configuration["Tenants:0:Settings:PortfolioDataApi:Domain"];
                if (string.IsNullOrEmpty(portfolioDataApiDomain))
                {
                    // Handle the case where the tenant is not found or is null
                    throw new Exception("Data Domain Url not found");
                }
                var response = await
                httpClient.GetAsync(portfolioDataApiDomain);
                if (response.IsSuccessStatusCode)
                {
                    return await Task.FromResult(new HealthCheckResult(
                      status: HealthStatus.Healthy,
                      description: "The Data API Domain is up and running."));
                }
                return await Task.FromResult(new HealthCheckResult(
                  status: HealthStatus.Unhealthy,
                  description: "The Data API Domain is down."));
            }
        }
    }
}
