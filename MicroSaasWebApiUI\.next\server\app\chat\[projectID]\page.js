(()=>{var e={};e.id=448,e.ids=[448],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},85807:e=>{"use strict";e.exports=require("module")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},6005:e=>{"use strict";e.exports=require("node:crypto")},27557:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c}),s(88375),s(8130),s(7629),s(12523);var r=s(23191),a=s(88716),i=s(37922),n=s.n(i),o=s(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c=["",{children:["chat",{children:["[projectID]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,88375)),"C:\\Projects\\PersonalPortal\\src\\app\\chat\\[projectID]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e),async e=>(await Promise.resolve().then(s.bind(s,71150))).default(e)],apple:[async e=>(await Promise.resolve().then(s.bind(s,63601))).default(e)],openGraph:[async e=>(await Promise.resolve().then(s.bind(s,51922))).default(e)],twitter:[async e=>(await Promise.resolve().then(s.bind(s,40680))).default(e)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,8130)),"C:\\Projects\\PersonalPortal\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,7629)),"C:\\Projects\\PersonalPortal\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"C:\\Projects\\PersonalPortal\\src\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e),async e=>(await Promise.resolve().then(s.bind(s,71150))).default(e)],apple:[async e=>(await Promise.resolve().then(s.bind(s,63601))).default(e)],openGraph:[async e=>(await Promise.resolve().then(s.bind(s,51922))).default(e)],twitter:[async e=>(await Promise.resolve().then(s.bind(s,40680))).default(e)],manifest:void 0}}],d=["C:\\Projects\\PersonalPortal\\src\\app\\chat\\[projectID]\\page.tsx"],u="/chat/[projectID]/page",p={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/chat/[projectID]/page",pathname:"/chat/[projectID]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},65274:(e,t,s)=>{Promise.resolve().then(s.bind(s,81226))},81226:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var r=s(10326),a=s(91664),i=s(44099),n=s(23703);let o=(0,s(62881).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);var l=s(35047),c=s(17577),d=s(10123);let u=d.Ry().shape({message:d.Z_().trim().required("Message cannot be empty")});function p(){let{projectID:e}=(0,l.useParams)(),[t,s]=(0,c.useState)([]),[d,p]=(0,c.useState)(""),[m,x]=(0,c.useState)(!0),h=async e=>{let t=await i.Z.post(d,{message:e});t.data&&s(e=>[...e,{id:Date.now().toString(),text:t.data.toString(),sender:"ai",timestamp:new Date}])},f=async(e,{resetForm:t})=>{if(!e.message.trim())return;let r={id:Date.now().toString(),text:e.message,sender:"user",timestamp:new Date};s(e=>[...e,r]),await h(e.message),t()};return m?r.jsx("div",{className:"flex justify-center items-center h-screen",children:r.jsx(o,{className:"animate-spin text-primary",size:32})}):(0,r.jsxs)("div",{className:"flex flex-col h-[800px] justify-between items-center w-full p-5",children:[(0,r.jsxs)("div",{className:"flex flex-col w-3/4 h-5/6 rounded-b-lg",children:[r.jsx("h1",{className:"text-2xl text-center font-bold",children:"Chat"}),r.jsx("div",{className:"flex flex-col gap-2 h-full w-full",children:t.map(e=>r.jsx("div",{className:`flex rounded-lg flex-col ${"user"===e.sender?"text-end":"text-start"}`,children:r.jsx("p",{children:e.text})},e.id))})]}),r.jsx(n.J9,{initialValues:{message:""},validationSchema:u,onSubmit:f,children:({errors:e,touched:t,isSubmitting:s})=>(0,r.jsxs)(n.l0,{className:"flex gap-3 w-3/4 rounded-b-lg",children:[r.jsx(n.gN,{type:"text",id:"message",name:"message",placeholder:"Send message...",className:`flex-1 p-2 rounded-md focus:outline-none focus:ring-2 
								${e.message&&t.message?"focus:ring-red-500 border-red-500":"focus:ring-blue-500"}`}),r.jsx(a.z,{type:"submit",disabled:s,className:"px-5 py-2 rounded-md transition-colors duration-200",children:s?r.jsx(o,{className:"animate-spin",size:16}):"Send"})]})})]})}},88375:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(68570).createProxy)(String.raw`C:\Projects\PersonalPortal\src\app\chat\[projectID]\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[948,70,251,777,746],()=>s(27557));module.exports=r})();