'use client'

import { convertToReadableDate } from '@/utils/functions'
import Link from 'next/link'
import { useState, useEffect } from 'react'
import { enhanceCodeBlocks } from '../scripts/codeBlockEnhancer'
import '../styles/blog.css'
import '../styles/theme-override.css'

type blogTitle = {
	rendered: string
}

type blogTypes = {
	title: blogTitle
	slug: string
	image_url: string
	date: string
	categories?: number[]
	excerpt?: {
		rendered: string
	}
	content?: {
		rendered: string
	}
}

interface props {
	articles: blogTypes[]
	selectedCategoryId?: number | null
}

const BlogListingNew = ({ articles, selectedCategoryId }: props) => {
	const [visiblePosts, setVisiblePosts] = useState(8)
	const [filteredArticles, setFilteredArticles] = useState<blogTypes[]>([])

	useEffect(() => {
		if (selectedCategoryId === null || selectedCategoryId === undefined) {
			setFilteredArticles(articles)
		} else {
			const filtered = articles.filter(article =>
				article.categories?.includes(selectedCategoryId)
			)
			setFilteredArticles(filtered)
		}
		setVisiblePosts(8)
	}, [articles, selectedCategoryId])

	useEffect(() => {
		// Enhance code blocks with current theme
		const theme = document.documentElement.classList.contains('dark') ? 'dark' : 'light'
		enhanceCodeBlocks(theme)
	}, [filteredArticles])

	const loadMore = () => {
		setVisiblePosts(prevVisiblePosts => prevVisiblePosts + 8)
	}

	// Function to strip HTML tags from excerpt
	const stripHtml = (html: string) => {
		const tmp = document.createElement('DIV')
		tmp.innerHTML = html
		return tmp.textContent || tmp.innerText || ''
	}

	return (
		<>
			<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
				{filteredArticles?.slice(0, visiblePosts).map((post: any, index: any) => (
					<Link
						href={`/blog/${post.slug}`}
						key={index}
						className='bg-white dark:bg-[#1E232C] rounded-xl overflow-hidden shadow-lg dark:border border-solid border-[#373C53] hover:border-[#5b6285] transition-all duration-500 transform hover:scale-105 ease-in-out h-full flex flex-col'
					>
						{post.image_url && (
							<div className='w-full h-40 overflow-hidden'>
								<img
									src={post.image_url}
									alt={post.title.rendered}
									className='w-full h-full object-cover'
									onError={(e) => {
										const target = e.target as HTMLImageElement;
										console.error('Image failed to load:', target.src);
										// Try the HTTP version if HTTPS fails
										if (target.src.startsWith('https://')) {
											target.src = target.src.replace('https://', 'http://');
										}
									}}
								/>
							</div>
						)}
						<div className='p-5 flex flex-col flex-grow'>
							<h2 className='font-semibold text-lg dark:text-white mb-3'>
								{post.title.rendered}
							</h2>
							{post.excerpt && (
								<p className='text-gray-600 dark:text-gray-300 mb-3 flex-grow'>
									{stripHtml(post.excerpt.rendered).substring(0, 100)}...
								</p>
							)}
							<div className='flex justify-between items-center mt-auto'>
								<p className='text-gray-400 text-sm'>
									{convertToReadableDate(post.date)}
								</p>
								<span className='text-blue-500 text-sm'>Read more →</span>
							</div>
						</div>
					</Link>
				))}
			</div>
			{visiblePosts < filteredArticles.length && (
				<div className='flex justify-start'>
					<button
						className='bg-[#006fee] rounded-full text-white font-medium text-sm px-5 py-1.5 h-8 mt-8'
						onClick={loadMore}
					>
						See more
					</button>
				</div>
			)}
		</>
	)
}

export default BlogListingNew
