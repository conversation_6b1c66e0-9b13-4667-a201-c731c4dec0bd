import prisma from '@/libs/prisma'
import { currentUser } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'

// Mark this route as dynamic
export const dynamic = 'force-dynamic'
export const runtime = 'edge'

export async function GET() {
	try {
		const user = await currentUser()

		if (!user) {
			return NextResponse.json(
				{ error: 'Unauthorized' },
				{ status: 401 }
			)
		}

		const projects = await prisma.project.findMany({
			where: {
				user_clerk_id: user.id,
			},
		})

		if (!projects || projects.length === 0) {
			return NextResponse.json({
				success: true,
				projects: [],
				message: 'No projects found'
			})
		}

		return NextResponse.json({ 
			success: true, 
			projects 
		})
	} catch (err) {
		console.error('Connection error:', err)
		return NextResponse.json(
			{ error: err?.message || 'An unexpected error occurred' },
			{ status: 500 }
		)
	}
}
