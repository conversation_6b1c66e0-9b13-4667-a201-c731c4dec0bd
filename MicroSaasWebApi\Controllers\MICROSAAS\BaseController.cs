﻿using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace MicroSaasWebApi.Controllers.MICROSAAS
{
    /// <summary>
    /// Base controller for MicroSaaS template with common functionality
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public abstract class BaseController : ControllerBase
    {
        protected readonly ILogger _logger;

        protected BaseController(ILogger logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Get current user ID from JWT token
        /// </summary>
        /// <returns>User ID</returns>
        protected string? GetCurrentUserId()
        {
            return User.FindFirst(ClaimTypes.NameIdentifier)?.Value ??
                   User.FindFirst("sub")?.Value ??
                   User.FindFirst("user_id")?.Value;
        }

        /// <summary>
        /// Get current user email from JWT token
        /// </summary>
        /// <returns>User email</returns>
        protected string? GetCurrentUserEmail()
        {
            return User.FindFirst(ClaimTypes.Email)?.Value ??
                   User.FindFirst("email")?.Value;
        }

        /// <summary>
        /// Get current user roles from JWT token
        /// </summary>
        /// <returns>User roles</returns>
        protected IEnumerable<string> GetCurrentUserRoles()
        {
            return User.FindAll(ClaimTypes.Role).Select(c => c.Value);
        }

        /// <summary>
        /// Check if current user has specific role
        /// </summary>
        /// <param name="role">Role to check</param>
        /// <returns>True if user has the role</returns>
        protected bool HasRole(string role)
        {
            return User.IsInRole(role);
        }

        /// <summary>
        /// Create standardized error response
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="statusCode">HTTP status code</param>
        /// <returns>Error response</returns>
        protected IActionResult CreateErrorResponse(string message, int statusCode = 400)
        {
            var response = new
            {
                success = false,
                message = message,
                timestamp = DateTime.UtcNow
            };

            return StatusCode(statusCode, response);
        }

        /// <summary>
        /// Create standardized success response
        /// </summary>
        /// <param name="data">Response data</param>
        /// <param name="message">Success message</param>
        /// <returns>Success response</returns>
        protected IActionResult CreateSuccessResponse(object? data = null, string message = "Success")
        {
            var response = new
            {
                success = true,
                message = message,
                data = data,
                timestamp = DateTime.UtcNow
            };

            return Ok(response);
        }
    }
}
