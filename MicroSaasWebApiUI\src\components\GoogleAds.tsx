'use client'

/**
 * GoogleAds component for displaying Google AdSense ads
 *
 * @internal This is an internal component used by AdUnit.
 * Do not use this component directly. Use AdManager or AdUnit instead.
 */

import Script from 'next/script'
import { useEffect, useRef, useState } from 'react'

// Add type declaration for window.adsbygoogle
declare global {
  interface Window {
    adsbygoogle: any[]
  }
}

interface GoogleAdsProps {
  slot?: string
  format?: 'auto' | 'fluid' | 'rectangle' | 'horizontal' | 'vertical' | 'autorelaxed' | 'in-article'
  style?: React.CSSProperties
  className?: string
  responsive?: boolean
  layout?: 'in-article' | 'in-feed' | 'sidebar' | 'horizontal'
  containerClassName?: string
}

const GoogleAds = ({
  slot = '4454393519',
  format = 'auto',
  style = {},
  className = '',
  responsive = true,
  layout = 'in-article',
  containerClassName = ''
}: GoogleAdsProps) => {
  // Using a more specific type for the ins element
  const adRef = useRef<HTMLModElement>(null)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    if (!isClient) return

    try {
      // Check if the script is loaded
      if (typeof window !== 'undefined') {
        // Wait for AdSense script to load
        const tryPushAd = () => {
          if (window.adsbygoogle) {
            try {
              (window.adsbygoogle = window.adsbygoogle || []).push({})
            } catch (pushError) {
              console.error('Error pushing ad:', pushError)
            }
          } else {
            setTimeout(tryPushAd, 500)
          }
        }

        // Start trying to push the ad
        setTimeout(tryPushAd, 1000) // Give a bit more time for AdSense to initialize
      }

      // Ensure the div has a minimum width when responsive
      if (responsive && adRef.current) {
        adRef.current.style.minWidth = '300px' // Minimum width for most ad formats
        adRef.current.style.width = '100%'
      }
    } catch (err) {
      console.error('Error loading Google Ads:', err)
    }
  }, [isClient, responsive])

  // Handle ad load error
  const handleAdError = (error: Error) => {
    // Silently handle errors
    if (adRef.current) {
      adRef.current.style.display = 'none'
    }
  }

  // Handle script load success
  const handleAdLoad = () => {
    // Silently handle success
  }

  // Get container classes based on layout
  const getContainerClasses = () => {
    switch (layout) {
      case 'sidebar':
        return 'adsense-container adsense-container-sidebar'
      case 'horizontal':
        return 'adsense-container-horizontal'
      default:
        return containerClassName || ''
    }
  }

  // Get ad style based on layout
  const getAdStyle = () => {
    const baseStyle = {
      display: 'block',
      textAlign: 'left' as const,
      background: 'transparent',
      ...style
    }

    switch (layout) {
      case 'sidebar':
        return {
          ...baseStyle,
          width: '300px',
          height: '600px',
          margin: '0 auto',
          maxWidth: '100%',
        }
      case 'horizontal':
        return {
          ...baseStyle,
          width: '100%',
          minHeight: '280px',
        }
      case 'in-article':
        return {
          ...baseStyle,
          minHeight: '280px',
        }
      default:
        return baseStyle
    }
  }

  // Only render on client side to avoid hydration issues
  if (!isClient) {
    // Return an empty div with the same dimensions to prevent layout shifts
    return <div className={`ad-placeholder ${className}`} style={{ minHeight: layout === 'sidebar' ? '600px' : '280px', ...style }} />
  }

  return (
    <div className={`${getContainerClasses()} ${className}`}>
      <ins
        ref={adRef}
        className="adsbygoogle"
        style={getAdStyle()}
        data-ad-client="ca-pub-8317058180798912"
        data-ad-slot={slot}
        data-ad-format={format}
        data-full-width-responsive={responsive}
      />
      {/* Script to initialize the ad */}
      {/* AdSense initialization is now handled in the useEffect hook */}
    </div>
  )
}

export default GoogleAds