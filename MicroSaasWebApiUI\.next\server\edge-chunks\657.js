"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[657],{5657:(e,t,s)=>{s.d(t,{N:()=>L});var i,n,a,l,r,c,o,h,d,u,p,f,k,y,g,b,w,m,v,K=s(3916),E=s(2829);s(5036),s(9102);var S=s(1657),C=s(1696),M=s(3369),x=class{constructor(){(0,<PERSON><PERSON>)(this,a),(0,<PERSON>.Ko)(this,i,"clerk_telemetry_throttler"),(0,<PERSON>.Ko)(this,n,864e5)}isEventThrottled(e){var t;if(!(0,M.ac)(this,a,c))return!1;let s=Date.now(),o=(0,M.U9)(this,a,l).call(this,e),h=null==(t=(0,M.ac)(this,a,r))?void 0:t[o];if(!h){let e={...(0,M.ac)(this,a,r),[o]:s};localStorage.setItem((0,M.ac)(this,i),JSON.stringify(e))}if(h&&s-h>(0,M.ac)(this,n)){let e=(0,M.ac)(this,a,r);delete e[o],localStorage.setItem((0,M.ac)(this,i),JSON.stringify(e))}return!!h}};i=new WeakMap,n=new WeakMap,a=new WeakSet,l=function(e){let{sk:t,pk:s,payload:i,...n}=e,a={...i,...n};return JSON.stringify(Object.keys({...i,...n}).sort().map(e=>a[e]))},r=function(){let e=localStorage.getItem((0,M.ac)(this,i));return e?JSON.parse(e):{}},c=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem((0,M.ac)(this,i)),!1}};var R={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},U=class{constructor(e){var t,s,i,n,a,l;(0,M.Ko)(this,f),(0,M.Ko)(this,o),(0,M.Ko)(this,h),(0,M.Ko)(this,d,{}),(0,M.Ko)(this,u,[]),(0,M.Ko)(this,p),(0,M.qx)(this,o,{maxBufferSize:null!=(t=e.maxBufferSize)?t:R.maxBufferSize,samplingRate:null!=(s=e.samplingRate)?s:R.samplingRate,disabled:null!=(i=e.disabled)&&i,debug:null!=(n=e.debug)&&n,endpoint:R.endpoint}),e.clerkVersion||"undefined"!=typeof window?(0,M.ac)(this,d).clerkVersion=null!=(a=e.clerkVersion)?a:"":(0,M.ac)(this,d).clerkVersion="",(0,M.ac)(this,d).sdk=e.sdk,(0,M.ac)(this,d).sdkVersion=e.sdkVersion,(0,M.ac)(this,d).publishableKey=null!=(l=e.publishableKey)?l:"";let r=(0,C.nQ)(e.publishableKey);r&&((0,M.ac)(this,d).instanceType=r.instanceType),e.secretKey&&((0,M.ac)(this,d).secretKey=e.secretKey.substring(0,16)),(0,M.qx)(this,h,new x)}get isEnabled(){var e;return!("development"!==(0,M.ac)(this,d).instanceType||(0,M.ac)(this,o).disabled||"undefined"!=typeof process&&(0,S.fQ)(process.env.CLERK_TELEMETRY_DISABLED))&&("undefined"==typeof window||null==(e=null==window?void 0:window.navigator)||!e.webdriver)}get isDebug(){return(0,M.ac)(this,o).debug||"undefined"!=typeof process&&(0,S.fQ)(process.env.CLERK_TELEMETRY_DEBUG)}record(e){let t=(0,M.U9)(this,f,v).call(this,e.event,e.payload);(0,M.U9)(this,f,w).call(this,t.event,t),(0,M.U9)(this,f,k).call(this,t,e.eventSamplingRate)&&((0,M.ac)(this,u).push(t),(0,M.U9)(this,f,g).call(this))}};function T(e){let t={...e},s=(0,K.gf)(t),i=(0,K.sA)({options:t,apiClient:s}),n=new U({...e.telemetry,publishableKey:t.publishableKey,secretKey:t.secretKey,...t.sdkMetadata?{sdk:t.sdkMetadata.name,sdkVersion:t.sdkMetadata.version}:{}});return{...s,...i,telemetry:n}}o=new WeakMap,h=new WeakMap,d=new WeakMap,u=new WeakMap,p=new WeakMap,f=new WeakSet,k=function(e,t){return this.isEnabled&&!this.isDebug&&(0,M.U9)(this,f,y).call(this,e,t)},y=function(e,t){let s=Math.random();return!(0,M.ac)(this,h).isEventThrottled(e)&&s<=(0,M.ac)(this,o).samplingRate&&(void 0===t||s<=t)},g=function(){if("undefined"==typeof window){(0,M.U9)(this,f,b).call(this);return}if((0,M.ac)(this,u).length>=(0,M.ac)(this,o).maxBufferSize){(0,M.ac)(this,p)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)((0,M.ac)(this,p)),(0,M.U9)(this,f,b).call(this);return}(0,M.ac)(this,p)||("requestIdleCallback"in window?(0,M.qx)(this,p,requestIdleCallback(()=>{(0,M.U9)(this,f,b).call(this)})):(0,M.qx)(this,p,setTimeout(()=>{(0,M.U9)(this,f,b).call(this)},0)))},b=function(){fetch(new URL("/v1/event",(0,M.ac)(this,o).endpoint),{method:"POST",body:JSON.stringify({events:(0,M.ac)(this,u)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,M.qx)(this,u,[])}).catch(()=>void 0)},w=function(e,t){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",e),console.log(t),console.groupEnd()):console.log("[clerk/telemetry]",e,t))},m=function(){let e={name:(0,M.ac)(this,d).sdk,version:(0,M.ac)(this,d).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e},v=function(e,t){var s,i;let n=(0,M.U9)(this,f,m).call(this);return{event:e,cv:null!=(s=(0,M.ac)(this,d).clerkVersion)?s:"",it:null!=(i=(0,M.ac)(this,d).instanceType)?i:"",sdk:n.name,sdkv:n.version,...(0,M.ac)(this,d).publishableKey?{pk:(0,M.ac)(this,d).publishableKey}:{},...(0,M.ac)(this,d).secretKey?{sk:(0,M.ac)(this,d).secretKey}:{},payload:t}},(0,E.A)(K.WX);var _=s(6449),D=s(6751),O=s(2690),I=s(2067);s(9985);let N=new I.AsyncLocalStorage;var V=s(6068),W=s(8802);let q={secretKey:V.Cn,publishableKey:V.Am,apiUrl:V.T5,apiVersion:V.Gn,userAgent:"@clerk/nextjs@5.7.5",proxyUrl:V.NM,domain:V.yK,isSatellite:V.lo,sdkMetadata:V.nE,telemetry:{disabled:V.CY,debug:V.w4}},A=e=>T({...q,...e}),B=T(q),L=new Proxy(Object.assign(()=>{var e,t;let s;try{let e=(0,O.vX)(),t=(0,W.Pg)(e,_._G.Headers.ClerkRequestData);s=(0,W.om)(t)}catch(e){if(e&&(0,O.mx)(e))throw e}let i=null!=(t=null==(e=N.getStore())?void 0:e.get("requestData"))?t:s;return(null==i?void 0:i.secretKey)||(null==i?void 0:i.publishableKey)?A(i):B},B),{get:(e,t,s)=>((0,D.x9)("clerkClient singleton","Use `clerkClient()` as a function instead."),Reflect.get(e,t,s))})}}]);
//# sourceMappingURL=657.js.map