using Microsoft.Extensions.DependencyInjection;
using MicroSaasWebApi.Services.MICROSAAS.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MicroSaasWebApi.Tests.Unit.Services
{
    public class FakeServiceScopeMicroSaas : IServiceScope
    {
        private readonly IResponseLoggerService _responseLoggerService;
        private readonly IApplicationSettingsService _applicationSettingsService;

        public FakeServiceScopeMicroSaas(IResponseLoggerService responseLoggerService, IApplicationSettingsService applicationSettingsService)
        {
            _responseLoggerService = responseLoggerService;
            _applicationSettingsService = applicationSettingsService;
        }

        public IServiceProvider ServiceProvider { get; }

        public void Dispose() { }

        public object GetService(Type serviceType)
        {
            if (serviceType == typeof(IResponseLoggerService)) return _responseLoggerService;
            if (serviceType == typeof(IApplicationSettingsService)) return _applicationSettingsService;

            return null;
        }
    }
}
