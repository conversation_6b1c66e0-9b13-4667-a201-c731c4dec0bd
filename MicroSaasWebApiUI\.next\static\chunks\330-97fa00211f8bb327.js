"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[330],{6466:function(e,t,n){n.d(t,{HT:function(){return G},iZ:function(){return X}});var r={},o=Uint8Array,a=Uint16Array,i=Int32Array,u=new o([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),l=new o([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),c=new o([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),s=function(e,t){for(var n=new a(31),r=0;r<31;++r)n[r]=t+=1<<e[r-1];for(var o=new i(n[30]),r=1;r<30;++r)for(var u=n[r];u<n[r+1];++u)o[u]=u-n[r]<<5|r;return{b:n,r:o}},d=s(u,2),f=d.b,v=d.r;f[28]=258,v[258]=28;for(var m=s(l,0),p=m.b,h=m.r,y=new a(32768),g=0;g<32768;++g){var b=(43690&g)>>1|(21845&g)<<1;b=(61680&(b=(52428&b)>>2|(13107&b)<<2))>>4|(3855&b)<<4,y[g]=((65280&b)>>8|(255&b)<<8)>>1}for(var w=function(e,t,n){for(var r,o=e.length,i=0,u=new a(t);i<o;++i)e[i]&&++u[e[i]-1];var l=new a(t);for(i=1;i<t;++i)l[i]=l[i-1]+u[i-1]<<1;if(n){r=new a(1<<t);var c=15-t;for(i=0;i<o;++i)if(e[i])for(var s=i<<4|e[i],d=t-e[i],f=l[e[i]-1]++<<d,v=f|(1<<d)-1;f<=v;++f)r[y[f]>>c]=s}else for(i=0,r=new a(o);i<o;++i)e[i]&&(r[i]=y[l[e[i]-1]++]>>15-e[i]);return r},E=new o(288),g=0;g<144;++g)E[g]=8;for(var g=144;g<256;++g)E[g]=9;for(var g=256;g<280;++g)E[g]=7;for(var g=280;g<288;++g)E[g]=8;for(var C=new o(32),g=0;g<32;++g)C[g]=5;var x=w(E,9,0),S=w(E,9,1),k=w(C,5,0),M=w(C,5,1),N=function(e){for(var t=e[0],n=1;n<e.length;++n)e[n]>t&&(t=e[n]);return t},T=function(e,t,n){var r=t/8|0;return(e[r]|e[r+1]<<8)>>(7&t)&n},R=function(e,t){var n=t/8|0;return(e[n]|e[n+1]<<8|e[n+2]<<16)>>(7&t)},L=function(e){return(e+7)/8|0},O=function(e,t,n){return(null==t||t<0)&&(t=0),(null==n||n>e.length)&&(n=e.length),new o(e.subarray(t,n))},A=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],D=function(e,t,n){var r=Error(t||A[e]);if(r.code=e,Error.captureStackTrace&&Error.captureStackTrace(r,D),!n)throw r;return r},P=function(e,t,n,r){var a=e.length,i=r?r.length:0;if(!a||t.f&&!t.l)return n||new o(0);var s=!n,d=s||2!=t.i,v=t.i;s&&(n=new o(3*a));var m=function(e){var t=n.length;if(e>t){var r=new o(Math.max(2*t,e));r.set(n),n=r}},h=t.f||0,y=t.p||0,g=t.b||0,b=t.l,E=t.d,C=t.m,x=t.n,k=8*a;do{if(!b){h=T(e,y,1);var A=T(e,y+1,3);if(y+=3,A){if(1==A)b=S,E=M,C=9,x=5;else if(2==A){var P=T(e,y,31)+257,I=T(e,y+10,15)+4,W=P+T(e,y+5,31)+1;y+=14;for(var j=new o(W),F=new o(19),_=0;_<I;++_)F[c[_]]=T(e,y+3*_,7);y+=3*I;for(var U=N(F),Z=(1<<U)-1,z=w(F,U,1),_=0;_<W;){var B=z[T(e,y,Z)];y+=15&B;var V=B>>4;if(V<16)j[_++]=V;else{var K=0,H=0;for(16==V?(H=3+T(e,y,3),y+=2,K=j[_-1]):17==V?(H=3+T(e,y,7),y+=3):18==V&&(H=11+T(e,y,127),y+=7);H--;)j[_++]=K}}var Y=j.subarray(0,P),$=j.subarray(P);C=N(Y),x=N($),b=w(Y,C,1),E=w($,x,1)}else D(1)}else{var V=L(y)+4,q=e[V-4]|e[V-3]<<8,X=V+q;if(X>a){v&&D(0);break}d&&m(g+q),n.set(e.subarray(V,X),g),t.b=g+=q,t.p=y=8*X,t.f=h;continue}if(y>k){v&&D(0);break}}d&&m(g+131072);for(var G=(1<<C)-1,J=(1<<x)-1,Q=y;;Q=y){var K=b[R(e,y)&G],ee=K>>4;if((y+=15&K)>k){v&&D(0);break}if(K||D(2),ee<256)n[g++]=ee;else if(256==ee){Q=y,b=null;break}else{var et=ee-254;if(ee>264){var _=ee-257,en=u[_];et=T(e,y,(1<<en)-1)+f[_],y+=en}var er=E[R(e,y)&J],eo=er>>4;er||D(3),y+=15&er;var $=p[eo];if(eo>3){var en=l[eo];$+=R(e,y)&(1<<en)-1,y+=en}if(y>k){v&&D(0);break}d&&m(g+131072);var ea=g+et;if(g<$){var ei=i-$,eu=Math.min($,ea);for(ei+g<0&&D(3);g<eu;++g)n[g]=r[ei+g]}for(;g<ea;++g)n[g]=n[g-$]}}t.l=b,t.p=Q,t.b=g,t.f=h,b&&(h=1,t.m=C,t.d=E,t.n=x)}while(!h);return g!=n.length&&s?O(n,0,g):n.subarray(0,g)},I=function(e,t,n){n<<=7&t;var r=t/8|0;e[r]|=n,e[r+1]|=n>>8},W=function(e,t,n){n<<=7&t;var r=t/8|0;e[r]|=n,e[r+1]|=n>>8,e[r+2]|=n>>16},j=function(e,t){for(var n=[],r=0;r<e.length;++r)e[r]&&n.push({s:r,f:e[r]});var i=n.length,u=n.slice();if(!i)return{t:V,l:0};if(1==i){var l=new o(n[0].s+1);return l[n[0].s]=1,{t:l,l:1}}n.sort(function(e,t){return e.f-t.f}),n.push({s:-1,f:25001});var c=n[0],s=n[1],d=0,f=1,v=2;for(n[0]={s:-1,f:c.f+s.f,l:c,r:s};f!=i-1;)c=n[n[d].f<n[v].f?d++:v++],s=n[d!=f&&n[d].f<n[v].f?d++:v++],n[f++]={s:-1,f:c.f+s.f,l:c,r:s};for(var m=u[0].s,r=1;r<i;++r)u[r].s>m&&(m=u[r].s);var p=new a(m+1),h=F(n[f-1],p,0);if(h>t){var r=0,y=0,g=h-t,b=1<<g;for(u.sort(function(e,t){return p[t.s]-p[e.s]||e.f-t.f});r<i;++r){var w=u[r].s;if(p[w]>t)y+=b-(1<<h-p[w]),p[w]=t;else break}for(y>>=g;y>0;){var E=u[r].s;p[E]<t?y-=1<<t-p[E]++-1:++r}for(;r>=0&&y;--r){var C=u[r].s;p[C]==t&&(--p[C],++y)}h=t}return{t:new o(p),l:h}},F=function(e,t,n){return -1==e.s?Math.max(F(e.l,t,n+1),F(e.r,t,n+1)):t[e.s]=n},_=function(e){for(var t=e.length;t&&!e[--t];);for(var n=new a(++t),r=0,o=e[0],i=1,u=function(e){n[r++]=e},l=1;l<=t;++l)if(e[l]==o&&l!=t)++i;else{if(!o&&i>2){for(;i>138;i-=138)u(32754);i>2&&(u(i>10?i-11<<5|28690:i-3<<5|12305),i=0)}else if(i>3){for(u(o),--i;i>6;i-=6)u(8304);i>2&&(u(i-3<<5|8208),i=0)}for(;i--;)u(o);i=1,o=e[l]}return{c:n.subarray(0,r),n:t}},U=function(e,t){for(var n=0,r=0;r<t.length;++r)n+=e[r]*t[r];return n},Z=function(e,t,n){var r=n.length,o=L(t+2);e[o]=255&r,e[o+1]=r>>8,e[o+2]=255^e[o],e[o+3]=255^e[o+1];for(var a=0;a<r;++a)e[o+a+4]=n[a];return(o+4+r)*8},z=function(e,t,n,r,o,i,s,d,f,v,m){I(t,m++,n),++o[256];for(var p,h,y,g,b=j(o,15),S=b.t,M=b.l,N=j(i,15),T=N.t,R=N.l,L=_(S),O=L.c,A=L.n,D=_(T),P=D.c,F=D.n,z=new a(19),B=0;B<O.length;++B)++z[31&O[B]];for(var B=0;B<P.length;++B)++z[31&P[B]];for(var V=j(z,7),K=V.t,H=V.l,Y=19;Y>4&&!K[c[Y-1]];--Y);var $=v+5<<3,q=U(o,E)+U(i,C)+s,X=U(o,S)+U(i,T)+s+14+3*Y+U(z,K)+2*z[16]+3*z[17]+7*z[18];if(f>=0&&$<=q&&$<=X)return Z(t,m,e.subarray(f,f+v));if(I(t,m,1+(X<q)),m+=2,X<q){p=w(S,M,0),h=S,y=w(T,R,0),g=T;var G=w(K,H,0);I(t,m,A-257),I(t,m+5,F-1),I(t,m+10,Y-4),m+=14;for(var B=0;B<Y;++B)I(t,m+3*B,K[c[B]]);m+=3*Y;for(var J=[O,P],Q=0;Q<2;++Q)for(var ee=J[Q],B=0;B<ee.length;++B){var et=31&ee[B];I(t,m,G[et]),m+=K[et],et>15&&(I(t,m,ee[B]>>5&127),m+=ee[B]>>12)}}else p=x,h=E,y=k,g=C;for(var B=0;B<d;++B){var en=r[B];if(en>255){var et=en>>18&31;W(t,m,p[et+257]),m+=h[et+257],et>7&&(I(t,m,en>>23&31),m+=u[et]);var er=31&en;W(t,m,y[er]),m+=g[er],er>3&&(W(t,m,en>>5&8191),m+=l[er])}else W(t,m,p[en]),m+=h[en]}return W(t,m,p[256]),m+h[256]},B=new i([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),V=new o(0),K=function(e,t,n,r,c,s){var d=s.z||e.length,f=new o(r+d+5*(1+Math.ceil(d/7e3))+c),m=f.subarray(r,f.length-c),p=s.l,y=7&(s.r||0);if(t){y&&(m[0]=s.r>>3);for(var g=B[t-1],b=g>>13,w=8191&g,E=(1<<n)-1,C=s.p||new a(32768),x=s.h||new a(E+1),S=Math.ceil(n/3),k=2*S,M=function(t){return(e[t]^e[t+1]<<S^e[t+2]<<k)&E},N=new i(25e3),T=new a(288),R=new a(32),A=0,D=0,P=s.i||0,I=0,W=s.w||0,j=0;P+2<d;++P){var F=M(P),_=32767&P,U=x[F];if(C[_]=U,x[F]=_,W<=P){var V=d-P;if((A>7e3||I>24576)&&(V>423||!p)){y=z(e,m,0,N,T,R,D,I,j,P-j,y),I=A=D=0,j=P;for(var K=0;K<286;++K)T[K]=0;for(var K=0;K<30;++K)R[K]=0}var H=2,Y=0,$=w,q=_-U&32767;if(V>2&&F==M(P-q))for(var X=Math.min(b,V)-1,G=Math.min(32767,P),J=Math.min(258,V);q<=G&&--$&&_!=U;){if(e[P+H]==e[P+H-q]){for(var Q=0;Q<J&&e[P+Q]==e[P+Q-q];++Q);if(Q>H){if(H=Q,Y=q,Q>X)break;for(var ee=Math.min(q,Q-2),et=0,K=0;K<ee;++K){var en=P-q+K&32767,er=C[en],eo=en-er&32767;eo>et&&(et=eo,U=en)}}}U=C[_=U],q+=_-U&32767}if(Y){N[I++]=268435456|v[H]<<18|h[Y];var ea=31&v[H],ei=31&h[Y];D+=u[ea]+l[ei],++T[257+ea],++R[ei],W=P+H,++A}else N[I++]=e[P],++T[e[P]]}}for(P=Math.max(P,W);P<d;++P)N[I++]=e[P],++T[e[P]];y=z(e,m,p,N,T,R,D,I,j,P-j,y),p||(s.r=7&y|m[y/8|0]<<3,y-=7,s.h=x,s.p=C,s.i=P,s.w=W)}else{for(var P=s.w||0;P<d+p;P+=65535){var eu=P+65535;eu>=d&&(m[y/8|0]=p,eu=d),y=Z(m,y+1,e.subarray(P,eu))}s.i=d}return O(f,0,r+L(y)+c)},H=function(){var e=1,t=0;return{p:function(n){for(var r=e,o=t,a=0|n.length,i=0;i!=a;){for(var u=Math.min(i+2655,a);i<u;++i)o+=r+=n[i];r=(65535&r)+15*(r>>16),o=(65535&o)+15*(o>>16)}e=r,t=o},d:function(){return e%=65521,t%=65521,(255&e)<<24|(65280&e)<<8|(255&t)<<8|t>>8}}},Y=function(e,t,n,r,a){if(!a&&(a={l:1},t.dictionary)){var i=t.dictionary.subarray(-32768),u=new o(i.length+e.length);u.set(i),u.set(e,i.length),e=u,a.w=i.length}return K(e,null==t.level?6:t.level,null==t.mem?a.l?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(e.length)))):20:12+t.mem,n,r,a)},$=function(e,t,n){for(;n;++t)e[t]=n,n>>>=8},q=function(e,t){var n=t.level;if(e[0]=120,e[1]=(0==n?0:n<6?1:9==n?3:2)<<6|(t.dictionary&&32),e[1]|=31-(e[0]<<8|e[1])%31,t.dictionary){var r=H();r.p(t.dictionary),$(e,2,r.d())}};function X(e,t){t||(t={});var n=H();n.p(e);var r=Y(e,t,t.dictionary?6:2,4);return q(r,t),$(r,r.length-4,n.d()),r}function G(e,t){var n;return P(e.subarray((n=t&&t.dictionary,((15&e[0])!=8||e[0]>>4>7||(e[0]<<8|e[1])%31)&&D(6,"invalid zlib data"),(e[1]>>5&1)==+!n&&D(6,"invalid zlib data: "+(32&e[1]?"need":"unexpected")+" dictionary"),(e[1]>>3&4)+2),-4),{i:2},t&&t.out,t&&t.dictionary)}var J="undefined"!=typeof TextDecoder&&new TextDecoder;try{J.decode(V,{stream:!0})}catch(e){}"function"==typeof queueMicrotask?queueMicrotask:"function"==typeof setTimeout&&setTimeout},75135:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},50598:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},89345:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},42351:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},74767:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("Youtube",[["path",{d:"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17",key:"1q2vi4"}],["path",{d:"m10 15 5-3-5-3z",key:"1jp15x"}]])},27648:function(e,t,n){n.d(t,{default:function(){return o.a}});var r=n(72972),o=n.n(r)},41154:function(e,t,n){n.d(t,{Z:function(){return r}});function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}},73966:function(e,t,n){n.d(t,{b:function(){return i},k:function(){return a}});var r=n(2265),o=n(57437);function a(e,t){let n=r.createContext(t),a=e=>{let{children:t,...a}=e,i=r.useMemo(()=>a,Object.values(a));return(0,o.jsx)(n.Provider,{value:i,children:t})};return a.displayName=e+"Provider",[a,function(o){let a=r.useContext(n);if(a)return a;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function i(e,t=[]){let n=[],a=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return a.scopeName=e,[function(t,a){let i=r.createContext(a),u=n.length;n=[...n,a];let l=t=>{let{scope:n,children:a,...l}=t,c=n?.[e]?.[u]||i,s=r.useMemo(()=>l,Object.values(l));return(0,o.jsx)(c.Provider,{value:s,children:a})};return l.displayName=t+"Provider",[l,function(n,o){let l=o?.[e]?.[u]||i,c=r.useContext(l);if(c)return c;if(void 0!==a)return a;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(a,...t)]}},92685:function(e,t,n){let r;n.d(t,{x8:function(){return tf},VY:function(){return tc},dk:function(){return td},aV:function(){return tl},h_:function(){return tu},fC:function(){return ta},Dx:function(){return ts},xz:function(){return ti}});var o,a,i,u,l,c,s,d,f=n(2265),v=n.t(f,2);function m(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var p=n(98575),h=n(73966),y=n(61188),g=v["useId".toString()]||(()=>void 0),b=0;function w(e){let[t,n]=f.useState(g());return(0,y.b)(()=>{e||n(e=>e??String(b++))},[e]),e||(t?`radix-${t}`:"")}var E=n(26606),C=n(66840),x=n(57437),S="dismissableLayer.update",k=f.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),M=f.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:a,onPointerDownOutside:i,onFocusOutside:u,onInteractOutside:l,onDismiss:c,...d}=e,v=f.useContext(k),[h,y]=f.useState(null),g=null!==(r=null==h?void 0:h.ownerDocument)&&void 0!==r?r:null===(n=globalThis)||void 0===n?void 0:n.document,[,b]=f.useState({}),w=(0,p.e)(t,e=>y(e)),M=Array.from(v.layers),[R]=[...v.layersWithOutsidePointerEventsDisabled].slice(-1),L=M.indexOf(R),O=h?M.indexOf(h):-1,A=v.layersWithOutsidePointerEventsDisabled.size>0,D=O>=L,P=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,E.W)(e),o=f.useRef(!1),a=f.useRef(()=>{});return f.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){T("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",a.current),a.current=t,n.addEventListener("click",a.current,{once:!0})):t()}else n.removeEventListener("click",a.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",a.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...v.branches].some(e=>e.contains(t));!D||n||(null==i||i(e),null==l||l(e),e.defaultPrevented||null==c||c())},g),I=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,E.W)(e),o=f.useRef(!1);return f.useEffect(()=>{let e=e=>{e.target&&!o.current&&T("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;[...v.branches].some(e=>e.contains(t))||(null==u||u(e),null==l||l(e),e.defaultPrevented||null==c||c())},g);return!function(e,t=globalThis?.document){let n=(0,E.W)(e);f.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{O!==v.layers.size-1||(null==a||a(e),!e.defaultPrevented&&c&&(e.preventDefault(),c()))},g),f.useEffect(()=>{if(h)return o&&(0===v.layersWithOutsidePointerEventsDisabled.size&&(s=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),v.layersWithOutsidePointerEventsDisabled.add(h)),v.layers.add(h),N(),()=>{o&&1===v.layersWithOutsidePointerEventsDisabled.size&&(g.body.style.pointerEvents=s)}},[h,g,o,v]),f.useEffect(()=>()=>{h&&(v.layers.delete(h),v.layersWithOutsidePointerEventsDisabled.delete(h),N())},[h,v]),f.useEffect(()=>{let e=()=>b({});return document.addEventListener(S,e),()=>document.removeEventListener(S,e)},[]),(0,x.jsx)(C.WV.div,{...d,ref:w,style:{pointerEvents:A?D?"auto":"none":void 0,...e.style},onFocusCapture:m(e.onFocusCapture,I.onFocusCapture),onBlurCapture:m(e.onBlurCapture,I.onBlurCapture),onPointerDownCapture:m(e.onPointerDownCapture,P.onPointerDownCapture)})});function N(){let e=new CustomEvent(S);document.dispatchEvent(e)}function T(e,t,n,r){let{discrete:o}=r,a=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),o?(0,C.jH)(a,i):a.dispatchEvent(i)}M.displayName="DismissableLayer",f.forwardRef((e,t)=>{let n=f.useContext(k),r=f.useRef(null),o=(0,p.e)(t,r);return f.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,x.jsx)(C.WV.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var R="focusScope.autoFocusOnMount",L="focusScope.autoFocusOnUnmount",O={bubbles:!1,cancelable:!0},A=f.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...i}=e,[u,l]=f.useState(null),c=(0,E.W)(o),s=(0,E.W)(a),d=f.useRef(null),v=(0,p.e)(t,e=>l(e)),m=f.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;f.useEffect(()=>{if(r){let e=function(e){if(m.paused||!u)return;let t=e.target;u.contains(t)?d.current=t:I(d.current,{select:!0})},t=function(e){if(m.paused||!u)return;let t=e.relatedTarget;null===t||u.contains(t)||I(d.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&I(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,m.paused]),f.useEffect(()=>{if(u){W.add(m);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(R,O);u.addEventListener(R,c),u.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(I(r,{select:t}),document.activeElement!==n)return}(D(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&I(u))}return()=>{u.removeEventListener(R,c),setTimeout(()=>{let t=new CustomEvent(L,O);u.addEventListener(L,s),u.dispatchEvent(t),t.defaultPrevented||I(null!=e?e:document.body,{select:!0}),u.removeEventListener(L,s),W.remove(m)},0)}}},[u,c,s,m]);let h=f.useCallback(e=>{if(!n&&!r||m.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,a]=function(e){let t=D(e);return[P(t,e),P(t.reverse(),e)]}(t);r&&a?e.shiftKey||o!==a?e.shiftKey&&o===r&&(e.preventDefault(),n&&I(a,{select:!0})):(e.preventDefault(),n&&I(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,m.paused]);return(0,x.jsx)(C.WV.div,{tabIndex:-1,...i,ref:v,onKeyDown:h})});function D(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function P(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function I(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}A.displayName="FocusScope";var W=(r=[],{add(e){let t=r[0];e!==t&&(null==t||t.pause()),(r=j(r,e)).unshift(e)},remove(e){var t;null===(t=(r=j(r,e))[0])||void 0===t||t.resume()}});function j(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var F=n(54887),_=f.forwardRef((e,t)=>{var n,r;let{container:o,...a}=e,[i,u]=f.useState(!1);(0,y.b)(()=>u(!0),[]);let l=o||i&&(null===(r=globalThis)||void 0===r?void 0:null===(n=r.document)||void 0===n?void 0:n.body);return l?F.createPortal((0,x.jsx)(C.WV.div,{...a,ref:t}),l):null});_.displayName="Portal";var U=e=>{var t,n;let r,o;let{present:a,children:i}=e,u=function(e){var t,n;let[r,o]=f.useState(),a=f.useRef({}),i=f.useRef(e),u=f.useRef("none"),[l,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},f.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return f.useEffect(()=>{let e=Z(a.current);u.current="mounted"===l?e:"none"},[l]),(0,y.b)(()=>{let t=a.current,n=i.current;if(n!==e){let r=u.current,o=Z(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==o?c("ANIMATION_OUT"):c("UNMOUNT"),i.current=e}},[e,c]),(0,y.b)(()=>{if(r){var e;let t;let n=null!==(e=r.ownerDocument.defaultView)&&void 0!==e?e:window,o=e=>{let o=Z(a.current).includes(e.animationName);if(e.target===r&&o&&(c("ANIMATION_END"),!i.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},l=e=>{e.target===r&&(u.current=Z(a.current))};return r.addEventListener("animationstart",l),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",l),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}c("ANIMATION_END")},[r,c]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:f.useCallback(e=>{e&&(a.current=getComputedStyle(e)),o(e)},[])}}(a),l="function"==typeof i?i({present:u.isPresent}):f.Children.only(i),c=(0,p.e)(u.ref,(r=null===(t=Object.getOwnPropertyDescriptor(l.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in r&&r.isReactWarning?l.ref:(r=null===(n=Object.getOwnPropertyDescriptor(l,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning?l.props.ref:l.props.ref||l.ref);return"function"==typeof i||u.isPresent?f.cloneElement(l,{ref:c}):null};function Z(e){return(null==e?void 0:e.animationName)||"none"}U.displayName="Presence";var z=0;function B(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var V=function(){return(V=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function K(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}var H="right-scroll-bar-position",Y="width-before-scroll-bar";function $(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var q="undefined"!=typeof window?f.useLayoutEffect:f.useEffect,X=new WeakMap,G=(void 0===o&&(o={}),(void 0===a&&(a=function(e){return e}),i=[],u=!1,l={read:function(){if(u)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return i.length?i[i.length-1]:null},useMedium:function(e){var t=a(e,u);return i.push(t),function(){i=i.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(u=!0;i.length;){var t=i;i=[],t.forEach(e)}i={push:function(t){return e(t)},filter:function(){return i}}},assignMedium:function(e){u=!0;var t=[];if(i.length){var n=i;i=[],n.forEach(e),t=i}var r=function(){var n=t;t=[],n.forEach(e)},o=function(){return Promise.resolve().then(r)};o(),i={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),i}}}}).options=V({async:!0,ssr:!1},o),l),J=function(){},Q=f.forwardRef(function(e,t){var n,r,o,a,i=f.useRef(null),u=f.useState({onScrollCapture:J,onWheelCapture:J,onTouchMoveCapture:J}),l=u[0],c=u[1],s=e.forwardProps,d=e.children,v=e.className,m=e.removeScrollBar,p=e.enabled,h=e.shards,y=e.sideCar,g=e.noIsolation,b=e.inert,w=e.allowPinchZoom,E=e.as,C=e.gapMode,x=K(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=(n=[i,t],r=function(e){return n.forEach(function(t){return $(t,e)})},(o=(0,f.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,a=o.facade,q(function(){var e=X.get(a);if(e){var t=new Set(e),r=new Set(n),o=a.current;t.forEach(function(e){r.has(e)||$(e,null)}),r.forEach(function(e){t.has(e)||$(e,o)})}X.set(a,n)},[n]),a),k=V(V({},x),l);return f.createElement(f.Fragment,null,p&&f.createElement(y,{sideCar:G,removeScrollBar:m,shards:h,noIsolation:g,inert:b,setCallbacks:c,allowPinchZoom:!!w,lockRef:i,gapMode:C}),s?f.cloneElement(f.Children.only(d),V(V({},k),{ref:S})):f.createElement(void 0===E?"div":E,V({},k,{className:v,ref:S}),d))});Q.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},Q.classNames={fullWidth:Y,zeroRight:H};var ee=function(e){var t=e.sideCar,n=K(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return f.createElement(r,V({},n))};ee.isSideCarExport=!0;var et=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=d||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,a;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},en=function(){var e=et();return function(t,n){f.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},er=function(){var e=en();return function(t){return e(t.styles,t.dynamic),null}},eo={left:0,top:0,right:0,gap:0},ea=function(e){return parseInt(e||"",10)||0},ei=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[ea(n),ea(r),ea(o)]},eu=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return eo;var t=ei(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},el=er(),ec="data-scroll-locked",es=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(ec,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(H," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(Y," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(H," .").concat(H," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(Y," .").concat(Y," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(ec,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},ed=function(){var e=parseInt(document.body.getAttribute(ec)||"0",10);return isFinite(e)?e:0},ef=function(){f.useEffect(function(){return document.body.setAttribute(ec,(ed()+1).toString()),function(){var e=ed()-1;e<=0?document.body.removeAttribute(ec):document.body.setAttribute(ec,e.toString())}},[])},ev=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;ef();var a=f.useMemo(function(){return eu(o)},[o]);return f.createElement(el,{styles:es(a,!t,o,n?"":"!important")})},em=!1;if("undefined"!=typeof window)try{var ep=Object.defineProperty({},"passive",{get:function(){return em=!0,!0}});window.addEventListener("test",ep,ep),window.removeEventListener("test",ep,ep)}catch(e){em=!1}var eh=!!em&&{passive:!1},ey=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},eg=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),eb(e,r)){var o=ew(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},eb=function(e,t){return"v"===e?ey(t,"overflowY"):ey(t,"overflowX")},ew=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},eE=function(e,t,n,r,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),u=i*r,l=n.target,c=t.contains(l),s=!1,d=u>0,f=0,v=0;do{var m=ew(e,l),p=m[0],h=m[1]-m[2]-i*p;(p||h)&&eb(e,l)&&(f+=h,v+=p),l instanceof ShadowRoot?l=l.host:l=l.parentNode}while(!c&&l!==document.body||c&&(t.contains(l)||t===l));return d&&(o&&1>Math.abs(f)||!o&&u>f)?s=!0:!d&&(o&&1>Math.abs(v)||!o&&-u>v)&&(s=!0),s},eC=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ex=function(e){return[e.deltaX,e.deltaY]},eS=function(e){return e&&"current"in e?e.current:e},ek=0,eM=[],eN=(c=function(e){var t=f.useRef([]),n=f.useRef([0,0]),r=f.useRef(),o=f.useState(ek++)[0],a=f.useState(er)[0],i=f.useRef(e);f.useEffect(function(){i.current=e},[e]),f.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(eS),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=f.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,a=eC(e),u=n.current,l="deltaX"in e?e.deltaX:u[0]-a[0],c="deltaY"in e?e.deltaY:u[1]-a[1],s=e.target,d=Math.abs(l)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=eg(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=eg(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(l||c)&&(r.current=o),!o)return!0;var v=r.current||o;return eE(v,t,e,"h"===v?l:c,!0)},[]),l=f.useCallback(function(e){if(eM.length&&eM[eM.length-1]===a){var n="deltaY"in e?ex(e):eC(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(i.current.shards||[]).map(eS).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=f.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),s=f.useCallback(function(e){n.current=eC(e),r.current=void 0},[]),d=f.useCallback(function(t){c(t.type,ex(t),t.target,u(t,e.lockRef.current))},[]),v=f.useCallback(function(t){c(t.type,eC(t),t.target,u(t,e.lockRef.current))},[]);f.useEffect(function(){return eM.push(a),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:v}),document.addEventListener("wheel",l,eh),document.addEventListener("touchmove",l,eh),document.addEventListener("touchstart",s,eh),function(){eM=eM.filter(function(e){return e!==a}),document.removeEventListener("wheel",l,eh),document.removeEventListener("touchmove",l,eh),document.removeEventListener("touchstart",s,eh)}},[]);var m=e.removeScrollBar,p=e.inert;return f.createElement(f.Fragment,null,p?f.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?f.createElement(ev,{gapMode:e.gapMode}):null)},G.useMedium(c),ee),eT=f.forwardRef(function(e,t){return f.createElement(Q,V({},e,{ref:t,sideCar:eN}))});eT.classNames=Q.classNames;var eR=new WeakMap,eL=new WeakMap,eO={},eA=0,eD=function(e){return e&&(e.host||eD(e.parentNode))},eP=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=eD(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});eO[n]||(eO[n]=new WeakMap);var a=eO[n],i=[],u=new Set,l=new Set(o),c=function(e){!e||u.has(e)||(u.add(e),c(e.parentNode))};o.forEach(c);var s=function(e){!e||l.has(e)||Array.prototype.forEach.call(e.children,function(e){if(u.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,l=(eR.get(e)||0)+1,c=(a.get(e)||0)+1;eR.set(e,l),a.set(e,c),i.push(e),1===l&&o&&eL.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),u.clear(),eA++,function(){i.forEach(function(e){var t=eR.get(e)-1,o=a.get(e)-1;eR.set(e,t),a.set(e,o),t||(eL.has(e)||e.removeAttribute(r),eL.delete(e)),o||e.removeAttribute(n)}),--eA||(eR=new WeakMap,eR=new WeakMap,eL=new WeakMap,eO={})}},eI=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),eP(r,o,n,"aria-hidden")):function(){return null}},eW=n(37053),ej="Dialog",[eF,e_]=(0,h.b)(ej),[eU,eZ]=eF(ej),ez=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:a,modal:i=!0}=e,u=f.useRef(null),l=f.useRef(null),[c=!1,s]=function({prop:e,defaultProp:t,onChange:n=()=>{}}){let[r,o]=function({defaultProp:e,onChange:t}){let n=f.useState(e),[r]=n,o=f.useRef(r),a=(0,E.W)(t);return f.useEffect(()=>{o.current!==r&&(a(r),o.current=r)},[r,o,a]),n}({defaultProp:t,onChange:n}),a=void 0!==e,i=a?e:r,u=(0,E.W)(n);return[i,f.useCallback(t=>{if(a){let n="function"==typeof t?t(e):t;n!==e&&u(n)}else o(t)},[a,e,o,u])]}({prop:r,defaultProp:o,onChange:a});return(0,x.jsx)(eU,{scope:t,triggerRef:u,contentRef:l,contentId:w(),titleId:w(),descriptionId:w(),open:c,onOpenChange:s,onOpenToggle:f.useCallback(()=>s(e=>!e),[s]),modal:i,children:n})};ez.displayName=ej;var eB="DialogTrigger",eV=f.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eZ(eB,n),a=(0,p.e)(t,o.triggerRef);return(0,x.jsx)(C.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":e9(o.open),...r,ref:a,onClick:m(e.onClick,o.onOpenToggle)})});eV.displayName=eB;var eK="DialogPortal",[eH,eY]=eF(eK,{forceMount:void 0}),e$=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:o}=e,a=eZ(eK,t);return(0,x.jsx)(eH,{scope:t,forceMount:n,children:f.Children.map(r,e=>(0,x.jsx)(U,{present:n||a.open,children:(0,x.jsx)(_,{asChild:!0,container:o,children:e})}))})};e$.displayName=eK;var eq="DialogOverlay",eX=f.forwardRef((e,t)=>{let n=eY(eq,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=eZ(eq,e.__scopeDialog);return a.modal?(0,x.jsx)(U,{present:r||a.open,children:(0,x.jsx)(eG,{...o,ref:t})}):null});eX.displayName=eq;var eG=f.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eZ(eq,n);return(0,x.jsx)(eT,{as:eW.g7,allowPinchZoom:!0,shards:[o.contentRef],children:(0,x.jsx)(C.WV.div,{"data-state":e9(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),eJ="DialogContent",eQ=f.forwardRef((e,t)=>{let n=eY(eJ,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=eZ(eJ,e.__scopeDialog);return(0,x.jsx)(U,{present:r||a.open,children:a.modal?(0,x.jsx)(e0,{...o,ref:t}):(0,x.jsx)(e1,{...o,ref:t})})});eQ.displayName=eJ;var e0=f.forwardRef((e,t)=>{let n=eZ(eJ,e.__scopeDialog),r=f.useRef(null),o=(0,p.e)(t,n.contentRef,r);return f.useEffect(()=>{let e=r.current;if(e)return eI(e)},[]),(0,x.jsx)(e2,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:m(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:m(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:m(e.onFocusOutside,e=>e.preventDefault())})}),e1=f.forwardRef((e,t)=>{let n=eZ(eJ,e.__scopeDialog),r=f.useRef(!1),o=f.useRef(!1);return(0,x.jsx)(e2,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var a,i;null===(a=e.onCloseAutoFocus)||void 0===a||a.call(e,t),t.defaultPrevented||(r.current||null===(i=n.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:t=>{var a,i;null===(a=e.onInteractOutside)||void 0===a||a.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(o.current=!0));let u=t.target;(null===(i=n.triggerRef.current)||void 0===i?void 0:i.contains(u))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),e2=f.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:a,...i}=e,u=eZ(eJ,n),l=f.useRef(null),c=(0,p.e)(t,l);return f.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:B()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:B()),z++,()=>{1===z&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),z--}},[]),(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(A,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:a,children:(0,x.jsx)(M,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":e9(u.open),...i,ref:c,onDismiss:()=>u.onOpenChange(!1)})}),(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(tr,{titleId:u.titleId}),(0,x.jsx)(to,{contentRef:l,descriptionId:u.descriptionId})]})]})}),e5="DialogTitle",e3=f.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eZ(e5,n);return(0,x.jsx)(C.WV.h2,{id:o.titleId,...r,ref:t})});e3.displayName=e5;var e6="DialogDescription",e8=f.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eZ(e6,n);return(0,x.jsx)(C.WV.p,{id:o.descriptionId,...r,ref:t})});e8.displayName=e6;var e4="DialogClose",e7=f.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eZ(e4,n);return(0,x.jsx)(C.WV.button,{type:"button",...r,ref:t,onClick:m(e.onClick,()=>o.onOpenChange(!1))})});function e9(e){return e?"open":"closed"}e7.displayName=e4;var te="DialogTitleWarning",[tt,tn]=(0,h.k)(te,{contentName:eJ,titleName:e5,docsSlug:"dialog"}),tr=e=>{let{titleId:t}=e,n=tn(te),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return f.useEffect(()=>{t&&!document.getElementById(t)&&console.error(r)},[r,t]),null},to=e=>{let{contentRef:t,descriptionId:n}=e,r=tn("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return f.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(o)},[o,t,n]),null},ta=ez,ti=eV,tu=e$,tl=eX,tc=eQ,ts=e3,td=e8,tf=e7},66840:function(e,t,n){n.d(t,{WV:function(){return u},jH:function(){return l}});var r=n(2265),o=n(54887),a=n(37053),i=n(57437),u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=r.forwardRef((e,n)=>{let{asChild:r,...o}=e,u=r?a.g7:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(u,{...o,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function l(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},26606:function(e,t,n){n.d(t,{W:function(){return o}});var r=n(2265);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},61188:function(e,t,n){n.d(t,{b:function(){return o}});var r=n(2265),o=globalThis?.document?r.useLayoutEffect:()=>{}},25922:function(e,t,n){n.d(t,{F:function(){return s},f:function(){return d}});var r=n(2265),o=(e,t,n,r,o,a,i,u)=>{let l=document.documentElement,c=["light","dark"];function s(t){(Array.isArray(e)?e:[e]).forEach(e=>{let n="class"===e,r=n&&a?o.map(e=>a[e]||e):o;n?(l.classList.remove(...r),l.classList.add(t)):l.setAttribute(e,t)}),u&&c.includes(t)&&(l.style.colorScheme=t)}if(r)s(r);else try{let e=localStorage.getItem(t)||n,r=i&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;s(r)}catch(e){}},a=["light","dark"],i="(prefers-color-scheme: dark)",u="undefined"==typeof window,l=r.createContext(void 0),c={setTheme:e=>{},themes:[]},s=()=>{var e;return null!=(e=r.useContext(l))?e:c},d=e=>r.useContext(l)?r.createElement(r.Fragment,null,e.children):r.createElement(v,{...e}),f=["light","dark"],v=e=>{let{forcedTheme:t,disableTransitionOnChange:n=!1,enableSystem:o=!0,enableColorScheme:u=!0,storageKey:c="theme",themes:s=f,defaultTheme:d=o?"system":"light",attribute:v="data-theme",value:g,children:b,nonce:w,scriptProps:E}=e,[C,x]=r.useState(()=>p(c,d)),[S,k]=r.useState(()=>p(c)),M=g?Object.values(g):s,N=r.useCallback(e=>{let t=e;if(!t)return;"system"===e&&o&&(t=y());let r=g?g[t]:t,i=n?h(w):null,l=document.documentElement,c=e=>{"class"===e?(l.classList.remove(...M),r&&l.classList.add(r)):e.startsWith("data-")&&(r?l.setAttribute(e,r):l.removeAttribute(e))};if(Array.isArray(v)?v.forEach(c):c(v),u){let e=a.includes(d)?d:null,n=a.includes(t)?t:e;l.style.colorScheme=n}null==i||i()},[w]),T=r.useCallback(e=>{let t="function"==typeof e?e(C):e;x(t);try{localStorage.setItem(c,t)}catch(e){}},[C]),R=r.useCallback(e=>{k(y(e)),"system"===C&&o&&!t&&N("system")},[C,t]);r.useEffect(()=>{let e=window.matchMedia(i);return e.addListener(R),R(e),()=>e.removeListener(R)},[R]),r.useEffect(()=>{let e=e=>{e.key===c&&(e.newValue?x(e.newValue):T(d))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[T]),r.useEffect(()=>{N(null!=t?t:C)},[t,C]);let L=r.useMemo(()=>({theme:C,setTheme:T,forcedTheme:t,resolvedTheme:"system"===C?S:C,themes:o?[...s,"system"]:s,systemTheme:o?S:void 0}),[C,T,t,S,o,s]);return r.createElement(l.Provider,{value:L},r.createElement(m,{forcedTheme:t,storageKey:c,attribute:v,enableSystem:o,enableColorScheme:u,defaultTheme:d,value:g,themes:s,nonce:w,scriptProps:E}),b)},m=r.memo(e=>{let{forcedTheme:t,storageKey:n,attribute:a,enableSystem:i,enableColorScheme:u,defaultTheme:l,value:c,themes:s,nonce:d,scriptProps:f}=e,v=JSON.stringify([a,n,l,t,s,c,i,u]).slice(1,-1);return r.createElement("script",{...f,suppressHydrationWarning:!0,nonce:"undefined"==typeof window?d:"",dangerouslySetInnerHTML:{__html:"(".concat(o.toString(),")(").concat(v,")")}})}),p=(e,t)=>{let n;if(!u){try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t}},h=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},y=e=>(e||(e=window.matchMedia(i)),e.matches?"dark":"light")}}]);