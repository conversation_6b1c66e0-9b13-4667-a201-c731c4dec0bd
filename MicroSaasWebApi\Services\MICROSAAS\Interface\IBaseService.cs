﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using MicroSaasWebApi.Models.Profile;
using MicroSaasWebApi.Models.PXW;
using MicroSaasWebApi.Models.PXW.AppSettings;
using System.Net.Http;

namespace MicroSaasWebApi.Services.PXW.Interface
{
    public interface IBaseService
    {
        // Properties
        string PayloadString { get; set; }
        IHttpContextAccessor HttpContextAccessor { get; set; }
        Dictionary<string, string> PathParametersDictionary { get; set; }
        Dictionary<string, string> QueryParametersDictionary { get; set; }
        MultipartFormDataContent FilteredFormData { get; set; }

        // Domain and Language Methods
        int GetDomainId();
        int GetLanguageId();
        Tenant? GetFilteredTenant();
        void SetFilteredTenant(Tenant tenant);

        // API Domain URL Methods
        string? GetDataApiDomainUrl();
        string? GetPermissionApiDomainUrl();
        string? GetDataInterchangeApiUrl();
        string GetDocumentApiDomainUrl();

        // Endpoint URL and Request Methods
        Task SetEndpointUrl();
        void ReadRequestBodyFromApi();
        void ReadRequestBody();
        Dictionary<string, string> CreatePathParametersDictionary(RequestBody requestBody);
        Dictionary<string, string> CreateQueryParametersDictionary(RequestBody requestBody);
        string CreatePayloadParametersDictionary(RequestBody requestBody);
        bool HasBearerToken(string authHeader);
        string GetEndpointUrl(string endpointName);

        // Fetch API Endpoint Methods
        Task<IActionResult> FetchApiEndpointsDataWithOptionalToken(string endpointName, bool isAuthHeaderPassed, string? httpMethodName = "GET");
        Task<IActionResult> FetchApiEndpointsPermissionWithOptionalToken(string endpointName, bool isAuthHeaderPassed, string? httpMethodName = "GET");
        Task<IActionResult> FetchApiEndpointsDataInterchangeWithOptionalToken(string endpointName, bool isAuthHeaderPassed, string? httpMethodName = "GET");
        Task<IActionResult> FetchApiEndpointsWithBearerToken(string endpointName, bool isAuthHeaderPassed, string? httpMethodName = "GET");
        Task<IActionResult> GetDocumentApiEndpointsWithB2CToken(string endpointName, string? httpMethodName = "GET");

        // Account Profile Methods
        AccountProfile GetAccountProfile(string token, string profileId);
        AccountProfile GetAccountProfileFromAccountMember(AccountMember member, string alphaCCOKey);

        // Utility Methods
        string GetParameter(JObject contentRequest, string key);
        T ConvertPayloadToObject<T>(string payloadString);

        // External API Call Methods
        Task<HttpResponseMessage> MakeExternalApiCall(string httpMethod, string apiUrl, string jwtToken, string endpointName, string? pydioUserToken = "");
        Task<HttpResponseMessage> MakeExternalApiCallByContentType(string httpMethod, string apiUrl, string jwtToken, HttpRequest request, string endpointName, string? pydioUserToken = "");
        Task<IActionResult> ProcessThirdPartyApiResponse(HttpResponseMessage response);
    }
}
