// import Link from "next/link";
// import AuthorAvatar from "@/app/blog/_assets/components/Author";
// import BlogSpotlight from "@/components/BlogSpotlight";
// import FaqsV2 from "@/components/FAQsV2";
// import BlogMoreArticles from "@/components/BlogMoreArticles";
import BlogDetails from '@/components/BlogDetails'
import { wordpressService } from '@/libs/wp'

export async function generateMetadata({
	params,
}: {
	params: { articleId: string }
}) {
	const article = await wordpressService.getPost(params.articleId)
	
	// If yoast_head_json doesn't exist, use fallback values
	const title = article?.title?.rendered || 'Blog Article'
	const description = article?.excerpt?.rendered || 'Read our latest blog post'
	const slug = article?.slug || params.articleId

	return {
		title: title,
		description: description,
		openGraph: {
			title: title,
			description: description,
			type: 'article',
			url: `${process.env.NEXT_PUBLIC_APP_URL}/blog/${slug}`,
		},
		twitter: {
			card: 'summary_large_image',
			title: title,
			description: description,
		},
	}
}

export default async function Article({
	params,
}: {
	params: { articleId: string }
}) {
	const slug = params.articleId
	console.log('slug1', slug)
	const article = await wordpressService.getPost(slug)
	const articles = await wordpressService.getAllPosts()

	return (
		<>
			<BlogDetails postDetails={article} allPosts={articles} />
		</>
	)
}
