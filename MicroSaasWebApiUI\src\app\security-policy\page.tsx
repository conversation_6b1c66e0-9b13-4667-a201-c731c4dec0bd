import { getSEOTags } from '@/libs/seo'
import config from '@/config'

export const metadata = getSEOTags({
  title: `Security Policy | ${config.appName}`,
  description: 'Security policy and vulnerability disclosure for javianpicardo.com',
  canonicalUrlRelative: '/security-policy',
})

export default function SecurityPolicy() {
  return (
    <div className="container mx-auto px-4 py-16 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8">Security Policy</h1>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4">Reporting Security Issues</h2>
        <p className="mb-4">
          The security of javianpicardo.com is a top priority. If you believe you've found a security vulnerability in our website,
          we encourage you to let us know right away.
        </p>
        <p className="mb-4">
          Please email security concerns to <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a>.
        </p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4">What We Ask From You</h2>
        <ul className="list-disc pl-6 mb-4 space-y-2">
          <li>Provide detailed reports with reproducible steps</li>
          <li>Allow a reasonable time for us to address the issue before public disclosure</li>
          <li>Do not access or modify user data without explicit permission</li>
          <li>Act in good faith and do not conduct denial of service attacks or other disruptive activities</li>
        </ul>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4">Our Commitment</h2>
        <p className="mb-4">
          When you report a vulnerability to us, we commit to:
        </p>
        <ul className="list-disc pl-6 mb-4 space-y-2">
          <li>Acknowledge receipt of your vulnerability report in a timely manner</li>
          <li>Provide an estimated timeframe for addressing the vulnerability</li>
          <li>Notify you when the vulnerability is fixed</li>
          <li>Recognize your contribution if you wish (unless you prefer to remain anonymous)</li>
        </ul>
      </section>

      <section>
        <h2 className="text-2xl font-semibold mb-4">Thank You</h2>
        <p>
          We appreciate your help in keeping javianpicardo.com and our users safe. Thank you for your contributions to our security.
        </p>
      </section>
    </div>
  )
}
