{"version": 3, "file": "app/api/(make)/active/route.js", "mappings": "oFAAAA,CAAAA,EAAAC,OAAA,CAAAC,QAAA,0CCAAF,CAAAA,EAAAC,OAAA,CAAAC,QAAA,saEKO,IAAMC,EAAU,gBACVC,EAAU,OAEhB,eAAeC,EAAKC,CAAgB,EAC1C,GAAI,CACH,IAAMC,EAAO,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,IACb,CAAEC,UAAAA,CAAS,CAAE,CAAG,MAAMH,EAAII,IAAI,GAEpC,GAAI,CAACH,GAAQ,CAACE,EACb,OAAOE,EAAAA,EAAYA,CAACD,IAAI,CACvB,CAAEE,MAAO,oCAAqC,EAC9C,CAAEC,OAAQ,GAAI,GAKhB,IAAMC,EAAU,MAAMC,EAAAA,CAAMA,CAACD,OAAO,CAACE,UAAU,CAAC,CAC/CC,MAAO,CACNC,GAAIT,CACL,CACD,GAEA,GAAI,CAACK,GAAW,CAACA,EAAQK,WAAW,CACnC,OAAOR,EAAAA,EAAYA,CAACD,IAAI,CACvB,CAAEE,MAAO,+BAAgC,EACzC,CAAEC,OAAQ,GAAI,GAIhB,GAAGC,UAAAA,EAAQD,MAAM,CAAc,CAW9B,GAAI,CAACO,CAVuB,MAAMC,EAAAA,CAAKA,CAACC,IAAI,CAC3C,CAAC,sCAAsC,EAAER,EAAQK,WAAW,CAAC,KAAK,CAAC,CACnE,CAAC,EACD,CACCI,QAAS,CACRC,cAAe,CAAC,MAAM,EAAEC,QAAQC,GAAG,CAACC,YAAY,CAAC,CAAC,CAEpD,IAGwBC,IAAI,CAACC,QAAQ,CACrC,MAAM,MAAU,iCAYjB,GAAI,CATc,MAAMd,EAAAA,CAAMA,CAACD,OAAO,CAACgB,MAAM,CAAC,CAC7Cb,MAAO,CACNC,GAAIT,CACL,EACAmB,KAAM,CACLf,OAAQ,UACT,CACD,GAGC,MAAM,MAAU,2BAElB,KAAO,CAWN,GAAI,CAACkB,CAVoB,MAAMV,EAAAA,CAAKA,CAACC,IAAI,CACxC,CAAC,sCAAsC,EAAER,EAAQK,WAAW,CAAC,MAAM,CAAC,CACpE,CAAC,EACD,CACCI,QAAS,CACRC,cAAe,CAAC,MAAM,EAAEC,QAAQC,GAAG,CAACC,YAAY,CAAC,CAAC,CAEpD,IAGqBC,IAAI,CAACC,QAAQ,CAClC,MAAM,MAAU,iCAYjB,GAAI,CATc,MAAMd,EAAAA,CAAMA,CAACD,OAAO,CAACgB,MAAM,CAAC,CAC7Cb,MAAO,CACNC,GAAIT,CACL,EACAmB,KAAM,CACLf,OAAQ,QACT,CACD,GAGC,MAAM,MAAU,2BAElB,CAEA,OAAOF,EAAAA,EAAYA,CAACD,IAAI,CAAC,CACxBsB,QAAS,EACV,EACD,CAAE,MAAOC,EAAK,CAEb,OADAC,QAAQtB,KAAK,CAAC,oBAAqBqB,GAC5BtB,EAAAA,EAAYA,CAACD,IAAI,CACvB,CAAEE,MAAO,6BAA8B,EACvC,CAAEC,OAAQ,GAAI,EAEhB,CACD,CAEO,eAAesB,EAAI7B,CAAgB,EACzC,GAAI,CACH,IAAMC,EAAO,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,IACb,CAAE4B,aAAAA,CAAY,CAAE,CAAG,IAAIC,IAAI/B,EAAIgC,GAAG,EAQlCC,EAAazB,CANH,MAAMC,EAAAA,CAAMA,CAACD,OAAO,CAACE,UAAU,CAAC,CAC/CC,MAAO,CACNC,GAAIkB,EAAaI,GAAG,CAAC,KACtB,CACD,IAE2BrB,WAAW,CAEtC,GAAI,CAACZ,GAAQ,CAACgC,EACb,MAAM,MAAU,wBAGjB,IAAME,EAAc,MAAMpB,EAAAA,CAAKA,CAACmB,GAAG,CAClC,CAAC,sCAAsC,EAAED,EAAW,CAAC,CACrD,CAAEhB,QAAS,CAAEC,cAAe,CAAC,MAAM,EAAEC,QAAQC,GAAG,CAACC,YAAY,CAAC,CAAC,CAAG,GAGnE,GAAI,CAACc,EAAYb,IAAI,CAACC,QAAQ,CAACX,EAAE,CAChC,MAAMwB,MAAM,sBAGb,GAAM,CAAEC,SAAAA,CAAQ,CAAE,CAAGF,EAAYb,IAAI,CAACC,QAAQ,CAE9C,OAAOlB,EAAAA,EAAYA,CAACD,IAAI,CAAC,CAAEsB,QAAS,GAAMW,SAAAA,CAAS,EACpD,CAAE,MAAOV,EAAK,CAEb,GADAC,QAAQtB,KAAK,CAAC,oBAAqBqB,GAC/BA,EACH,OAAOtB,EAAAA,EAAYA,CAACD,IAAI,CAAC,CAAEE,MAAOqB,EAAIW,OAAO,GAE9C,OAAOjC,EAAAA,EAAYA,CAACD,IAAI,CACvB,CAAEE,MAAO,8BAA+B,EACxC,CAAEC,OAAQ,GAAI,EAEhB,CACD,CCtIA,IAAAgC,EAAA,IAAwBC,EAAAC,mBAAmB,EAC3CC,WAAA,CACAC,KAAcC,EAAAC,CAAS,CAAAC,SAAA,CACvBC,KAAA,2BACAC,SAAA,cACAC,SAAA,QACAC,WAAA,6BACA,EACAC,iBAAA,wEACAC,iBAVA,GAWAC,SAAYC,CACZ,GAIA,CAAQC,oBAAAA,CAAA,CAAAC,6BAAAA,CAAA,CAAAC,YAAAA,CAAA,EAAiElB,EACzEmB,EAAA,2BACA,SAAAC,IACA,MAAW,GAAAC,EAAAC,EAAA,EAAW,CACtBJ,YAAAA,EACAD,6BAAAA,CACA,EACA,CC1BO,IAAAM,EAAqBC,EAC5BC,EAAeC,EAAAC,CAAsB,CAAAC,IAAA,CAAM5B,iDCA3C,IAAA6B,EAFe,GAAIC,UAAAA,YAAYA", "sources": ["webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/ignored|C:\\Projects\\PersonalPortal\\node_modules\\crypto-js|crypto", "webpack://_N_E/./src/app/api/(make)/active/route.ts", "webpack://_N_E/./src/app/api/(make)/active/route.ts?e2ac", "webpack://_N_E/?a9b1", "webpack://_N_E/./src/libs/prisma.ts"], "sourcesContent": ["module.exports = require(\"node:async_hooks\");", "module.exports = require(\"node:buffer\");", "/* (ignored) */", "import prisma from '@/libs/prisma'\r\nimport { currentUser } from '@clerk/nextjs/server'\r\nimport axios from 'axios'\r\nimport { NextRequest, NextResponse } from 'next/server'\r\n\r\nexport const dynamic = 'force-dynamic'\r\nexport const runtime = 'edge'\r\n\r\nexport async function POST(req: NextRequest) {\r\n\ttry {\r\n\t\tconst user = await currentUser()\r\n\t\tconst { projectId } = await req.json() as { projectId: string }\r\n\r\n\t\tif (!user || !projectId) {\r\n\t\t\treturn NextResponse.json(\r\n\t\t\t\t{ error: 'Unauthorized or missing project ID' },\r\n\t\t\t\t{ status: 401 }\r\n\t\t\t)\r\n\t\t}\r\n\r\n\t\t// Get the project to find the scenario ID\r\n\t\tconst project = await prisma.project.findUnique({\r\n\t\t\twhere: {\r\n\t\t\t\tid: projectId,\r\n\t\t\t},\r\n\t\t})\r\n\r\n\t\tif (!project || !project.scenario_id) {\r\n\t\t\treturn NextResponse.json(\r\n\t\t\t\t{ error: 'Project or scenario not found' },\r\n\t\t\t\t{ status: 404 }\r\n\t\t\t)\r\n\t\t}\r\n\r\n\t\tif(project.status == 'active') {\r\n\t\t\tconst disActivateResponse = await axios.post(\r\n\t\t\t\t`https://eu2.make.com/api/v2/scenarios/${project.scenario_id}/stop`,\r\n\t\t\t\t{},\r\n\t\t\t\t{\r\n\t\t\t\t\theaders: {\r\n\t\t\t\t\t\tAuthorization: `Token ${process.env.MAKE_API_KEY}`,\r\n\t\t\t\t\t},\r\n\t\t\t\t}\r\n\t\t\t)\r\n\r\n\t\t\tif (!disActivateResponse.data.scenario) {\r\n\t\t\t\tthrow new Error('Failed to deactivate scenario')\r\n\t\t\t}\r\n\r\n\t\t\tconst dbProject = await prisma.project.update({\r\n\t\t\t\twhere: {\r\n\t\t\t\t\tid: projectId,\r\n\t\t\t\t},\r\n\t\t\t\tdata: {\r\n\t\t\t\t\tstatus: 'inactive',\r\n\t\t\t\t},\r\n\t\t\t})\r\n\r\n\t\t\tif (!dbProject) {\r\n\t\t\t\tthrow new Error('Failed to update project')\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tconst activateResponse = await axios.post(\r\n\t\t\t\t`https://eu2.make.com/api/v2/scenarios/${project.scenario_id}/start`,\r\n\t\t\t\t{},\r\n\t\t\t\t{\r\n\t\t\t\t\theaders: {\r\n\t\t\t\t\t\tAuthorization: `Token ${process.env.MAKE_API_KEY}`,\r\n\t\t\t\t\t},\r\n\t\t\t\t}\r\n\t\t\t)\r\n\r\n\t\t\tif (!activateResponse.data.scenario) {\r\n\t\t\t\tthrow new Error('Failed to deactivate scenario')\r\n\t\t\t}\r\n\r\n\t\t\tconst dbProject = await prisma.project.update({\r\n\t\t\t\twhere: {\r\n\t\t\t\t\tid: projectId,\r\n\t\t\t\t},\r\n\t\t\t\tdata: {\r\n\t\t\t\t\tstatus: 'active',\r\n\t\t\t\t},\r\n\t\t\t})\r\n\r\n\t\t\tif (!dbProject) {\r\n\t\t\t\tthrow new Error('Failed to update project')\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn NextResponse.json({\r\n\t\t\tsuccess: true,\r\n\t\t})\r\n\t} catch (err) {\r\n\t\tconsole.error('Activation error:', err)\r\n\t\treturn NextResponse.json(\r\n\t\t\t{ error: 'Failed to activate scenario' },\r\n\t\t\t{ status: 500 }\r\n\t\t)\r\n\t}\r\n}\r\n\r\nexport async function GET(req: NextRequest) {\r\n\ttry {\r\n\t\tconst user = await currentUser()\r\n\t\tconst { searchParams } = new URL(req.url)\r\n\r\n\t\tconst project = await prisma.project.findUnique({\r\n\t\t\twhere: {\r\n\t\t\t\tid: searchParams.get('id'),\r\n\t\t\t},\r\n\t\t})\r\n\r\n\t\tconst scenarioId = project.scenario_id\r\n\r\n\t\tif (!user || !scenarioId) {\r\n\t\t\tthrow new Error('Something went wrong')\r\n\t\t}\r\n\r\n\t\tconst checkStatus = await axios.get(\r\n\t\t\t`https://eu2.make.com/api/v2/scenarios/${scenarioId}`,\r\n\t\t\t{ headers: { Authorization: `Token ${process.env.MAKE_API_KEY}` } }\r\n\t\t)\r\n\r\n\t\tif (!checkStatus.data.scenario.id) {\r\n\t\t\tthrow Error('check status error')\r\n\t\t}\r\n\r\n\t\tconst { isActive } = checkStatus.data.scenario\r\n\r\n\t\treturn NextResponse.json({ success: true, isActive })\r\n\t} catch (err) {\r\n\t\tconsole.error('Connection error:', err)\r\n\t\tif (err) {\r\n\t\t\treturn NextResponse.json({ error: err.message })\r\n\t\t}\r\n\t\treturn NextResponse.json(\r\n\t\t\t{ error: 'An unexpected error occurred' },\r\n\t\t\t{ status: 500 }\r\n\t\t)\r\n\t}\r\n}\r\n", "import { AppRouteRouteModule } from \"next/dist/server/future/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/future/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Projects\\\\PersonalPortal\\\\src\\\\app\\\\api\\\\(make)\\\\active\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/(make)/active/route\",\n        pathname: \"/api/active\",\n        filename: \"route\",\n        bundlePath: \"app/api/(make)/active/route\"\n    },\n    resolvedPagePath: \"C:\\\\Projects\\\\PersonalPortal\\\\src\\\\app\\\\api\\\\(make)\\\\active\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/(make)/active/route\";\nfunction patchFetch() {\n    return _patchFetch({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\nexport { routeModule, requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, originalPathname, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "import { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2F(make)%2Factive%2Froute&page=%2Fapi%2F(make)%2Factive%2Froute&pagePath=private-next-app-dir%2Fapi%2F(make)%2Factive%2Froute.ts&appDir=C%3A%5CProjects%5CPersonalPortal%5Csrc%5Capp&appPaths=%2Fapi%2F(make)%2Factive%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/(make)/active/route.ts?__next_edge_ssr_entry__\";\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule);\n\n//# sourceMappingURL=edge-app-route.js.map", "import { PrismaClient } from \"@prisma/client\";\r\n\r\nconst prisma = new PrismaClient();\r\n\r\nexport default prisma;\r\n"], "names": ["module", "exports", "require", "dynamic", "runtime", "POST", "req", "user", "currentUser", "projectId", "json", "NextResponse", "error", "status", "project", "prisma", "findUnique", "where", "id", "scenario_id", "disActivateResponse", "axios", "post", "headers", "Authorization", "process", "env", "MAKE_API_KEY", "data", "scenario", "update", "activateResponse", "success", "err", "console", "GET", "searchParams", "URL", "url", "scenarioId", "get", "checkStatus", "Error", "isActive", "message", "routeModule", "module_compiled", "AppRouteRouteModule", "definition", "kind", "route_kind", "x", "APP_ROUTE", "page", "pathname", "filename", "bundlePath", "resolvedPagePath", "nextConfigOutput", "userland", "route_namespaceObject", "requestAsyncStorage", "staticGenerationAsyncStorage", "serverHooks", "originalPathname", "patchFetch", "patch_fetch", "XH", "ComponentMod", "route_next_edge_ssr_entry_namespaceObject", "next_edge_app_route_loaderabsolutePagePath_private_next_app_dir_2Fapi_2F_make_2Factive_2Froute_ts_page_2Fapi_2F_make_2Factive_2Froute_appDirLoader_bmV4dC1hcHAtbG9hZGVyP25hbWU9YXBwJTJGYXBpJTJGKG1ha2UpJTJGYWN0aXZlJTJGcm91dGUmcGFnZT0lMkZhcGklMkYobWFrZSklMkZhY3RpdmUlMkZyb3V0ZSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRihtYWtlKSUyRmFjdGl2ZSUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDUHJvamVjdHMlNUNQZXJzb25hbFBvcnRhbCU1Q3NyYyU1Q2FwcCZhcHBQYXRocz0lMkZhcGklMkYobWFrZSklMkZhY3RpdmUlMkZyb3V0ZSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCE_3D_nextConfigOutput_preferredRegion_middlewareConfig_e30_3D_", "edge_route_module_wrapper", "a", "wrap", "__WEBPACK_DEFAULT_EXPORT__", "PrismaClient"], "sourceRoot": ""}