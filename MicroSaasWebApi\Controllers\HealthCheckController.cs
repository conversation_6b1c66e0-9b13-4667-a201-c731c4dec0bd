﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using MicroSaasWebApi.Services.PXW.Interface;

namespace MicroSaasWebApi.Controllers
{
    [Route("api/[controller]")]
    [Controller]
    //https://blog.devart.com/how-to-implement-health-checks-in-asp-net-6.html
    //https://learn.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/monitor-app-health
    public class HealthCheckController : ControllerBase
    {
        private readonly ILoggerService _logger;
        private readonly HealthCheckService _service;
        public HealthCheckController(ILoggerService logger,
        HealthCheckService service)
        {
            _logger = logger;
            _service = service;
        }

        [NonAction]
        [HttpGet]
        public async Task<IActionResult> Get()
        {
            var report = await _service.CheckHealthAsync();
            string json = System.Text.Json.JsonSerializer.Serialize(report);

            if (report.Status == HealthStatus.Healthy)
            {
                await _logger.LogInformationAsync("Service available");
                return Ok(json);
            }
            else
            {
                await _logger.LogInformationAsync("Service unavailable");
                return NotFound("Service unavailable");
            }


        }
    }
}
