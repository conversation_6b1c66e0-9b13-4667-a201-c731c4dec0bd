#MAKE

MAKE_ORGANIZATION_ID=
MAKE_TEAM_ID=
MAKE_API_KEY=
MAKE_API_URL=

#N8N
N8N_API_KEY=
N8N_API_URL=
N8N_WEBHOOK_URL=

POSTGRES_USER=postgres
POSTGRES_PASSWORD=
POSTGRES_DBNAME=postgres
DATABASE_HOST=
DATABASE_PORT=5432

#DB PRISMA
DATABASE_URL=

# Stripe keys
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=

# Clerk keys
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=
CLERK_SECRET_KEY=
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up

# Your app's base URL
NEXT_PUBLIC_APP_URL=http://localhost:3000

#Resend
RESEND_API_KEY=

#WordPress
WORDPRESS_DB_USER=user
WORDPRESS_DB_PASSWORD=
WORDPRESS_DB_NAME=exampledb
WP_REST_ENDPOINT=

#MySQL for WordPress
MYSQL_DATABASE=wp_db
MYSQL_USER=admin
MYSQL_PASSWORD=adminpass
MYSQL_RANDOM_ROOT_PASSWORD='1'