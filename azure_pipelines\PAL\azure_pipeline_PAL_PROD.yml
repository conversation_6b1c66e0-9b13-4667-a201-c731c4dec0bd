extends:
  template: ../../azure_pipeline_template.yml
  parameters:
    environment: PROD
    client: PAL
    dockerRegistryServiceConnection: "prod-pal-acr-azdvp-sc"
    imageRepository: "prod-pal-webapi"
    containerRegistry: "prodpalacr.azurecr.io"
    dockerfilePath: "$(Build.SourcesDirectory)/Dockerfile"
    tag: "$(Build.BuildId)"
    poolName: "terraform-pool"
    azureSubscription: "prod-pal-apps-azdvp-sc"
    webAppName: "prod-pal-webapi-appService"
    appSettingsEnvironment: "-ASPNETCORE_ENVIRONMENT Staging"
