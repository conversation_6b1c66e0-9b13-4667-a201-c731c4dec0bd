extends:
  template: ../../azure_pipeline_template.yml
  parameters:
    environment: PROD
    client: INFERNO
    dockerRegistryServiceConnection: "************************************"
    imageRepository: "prod-canso-webapi"
    containerRegistry: "prodcansoacr.azurecr.io"
    dockerfilePath: "$(Build.SourcesDirectory)/Dockerfile"
    tag: "INFERNO-PROD-$(Build.BuildId)"
    poolName: "terraform-pool"
    azureSubscription: "Inferno PROD(9f4034d1-8409-40fe-9590-b0a56f9cff64)"
    webAppName: "prod-canso-webapi-appService"
    appSettingsEnvironment: "-ASPNETCORE_ENVIRONMENT Staging"
