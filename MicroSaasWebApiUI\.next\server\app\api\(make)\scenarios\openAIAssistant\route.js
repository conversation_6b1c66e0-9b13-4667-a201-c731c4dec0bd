(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[451],{2067:e=>{"use strict";e.exports=require("node:async_hooks")},6195:e=>{"use strict";e.exports=require("node:buffer")},2480:()=>{},5382:(e,t,a)=>{"use strict";a.r(t),a.d(t,{ComponentMod:()=>y,default:()=>E});var o={};a.r(o),a.d(o,{POST:()=>k,dynamic:()=>m,runtime:()=>h});var r={};a.r(r),a.d(r,{originalPathname:()=>f,patchFetch:()=>I,requestAsyncStorage:()=>w,routeModule:()=>A,serverHooks:()=>_,staticGenerationAsyncStorage:()=>g});var s=a(932),n=a(2561),i=a(4828),p=a(6631),c=a(6333),d=a(462),u=a(1175),l=a(9985);let m="force-dynamic",h="edge";async function k(e){try{let{id:t,apiKey:a,apiOrg:o,assistantId:r}=await e.json(),s=await (0,d.a)();if(!s)throw Error("User not found");let n=process.env.MAKE_API_KEY,i=process.env.MAKE_TEAM_ID,p=process.env.MAKE_API_URL,m=new Date().toISOString(),h=(await u.Z.post(`${p}/connections?teamId=${i}`,{accountName:`OpenAI ${m}`,accountType:"openai-gpt-3",apiKey:a,apiOrg:o},{headers:{Authorization:`Token ${n}`}})).data.connection.id;if(!h)throw Error("Connection ID missed");let k=await u.Z.post(`${p}/hooks?teamId=${i}`,{name:`webhook openAI ${m}`,team_id:i,teamId:i,typeName:"gateway-webhook",method:!0,headers:!0,stringify:!1,interface:[{name:"message",type:"text"}]},{headers:{Authorization:`Token ${n}`}}),A=k.data.hook.id,w=k.data.hook.url;if(!A||!w)throw Error("hook creating error");let g=(await u.Z.get(`${p}/scenarios/${t}/blueprint`,{headers:{Authorization:`Token ${n}`}})).data.response.blueprint,_=g.flow.map(e=>"gateway:CustomWebHook"===e.module?{...e,parameters:{...e.parameters,hook:A}}:"openai-gpt-3:messageAssistantAdvanced"===e.module?{...e,parameters:{...e.parameters,__IMTCONN__:h},mapper:{...e.mapper,assistantId:r}}:e),f=(await u.Z.post(`${p}/scenarios`,{name:`Cloned Scenario ${m}`,teamId:i,blueprint:JSON.stringify({...g,name:`Cloned Scenario ${m}`,flow:_}),scheduling:JSON.stringify({type:"indefinitely",interval:900}),basedon:20},{headers:{Authorization:`Token ${n}`}})).data.scenario.id;if(!f)throw Error("Failed to create new scenario");if(await u.Z.post(`${p}/scenarios/${f}/start`,{},{headers:{Authorization:`Token ${n}`}}),!await c.Z.project.create({data:{connection_id:h.toString(),webhook_id:A.toString(),scenario_id:f.toString(),user_clerk_id:s.id,webhookLink:w,assistant_id:r,type:"make",status:"active"}}))throw Error("Failed to create project");return l.xk.json({success:!0,id:f,webhookLink:w,connectionId:h})}catch(e){return console.error("Error in scenario cloning process:",e),l.xk.json({success:!1,error:e.response?.data?.message||"Failed to clone and update scenario",details:e.response?.data||e.message},{status:e.response?.status||500})}}let A=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/(make)/scenarios/openAIAssistant/route",pathname:"/api/scenarios/openAIAssistant",filename:"route",bundlePath:"app/api/(make)/scenarios/openAIAssistant/route"},resolvedPagePath:"C:\\Projects\\PersonalPortal\\src\\app\\api\\(make)\\scenarios\\openAIAssistant\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:w,staticGenerationAsyncStorage:g,serverHooks:_}=A,f="/api/(make)/scenarios/openAIAssistant/route";function I(){return(0,p.XH)({serverHooks:_,staticGenerationAsyncStorage:g})}let y=r,E=s.a.wrap(A)},6333:(e,t,a)=>{"use strict";a.d(t,{Z:()=>o});let o=new(a(7225)).PrismaClient}},e=>{var t=t=>e(e.s=t);e.O(0,[520,288,225,436,657,551],()=>t(5382));var a=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/(make)/scenarios/openAIAssistant/route"]=a}]);
//# sourceMappingURL=route.js.map