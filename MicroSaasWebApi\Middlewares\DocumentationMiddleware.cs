﻿using Markdig;
using Microsoft.Extensions.FileProviders;

namespace MicroSaasWebApi.Middlewares
{
    // You may need to install the Microsoft.AspNetCore.Http.Abstractions package into your project
    public class DocumentationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IFileProvider _fileProvider;
        private readonly MarkdownPipeline _pipeline;

        public DocumentationMiddleware(RequestDelegate next, IFileProvider fileProvider)
        {
            _next = next;
            _fileProvider = fileProvider;
            _pipeline = new MarkdownPipelineBuilder().UseAdvancedExtensions().Build();
        }

        public async Task InvokeAsync(HttpContext context)
        {
            if (context.Request.Path.StartsWithSegments("/documentation", out var subPath))
            {
                var fileInfo = _fileProvider.GetFileInfo(subPath);
                if (fileInfo.Exists && fileInfo.Name.EndsWith(".md"))
                {
                    using var streamReader = new StreamReader(fileInfo.CreateReadStream());
                    var markdown = streamReader.ReadToEnd();
                    var html = $"<!DOCTYPE html><html><head><link rel=\"stylesheet\" href=\"/documentation/custom.css\"></head><body>{Markdown.ToHtml(markdown, _pipeline)}</body></html>";
                    context.Response.ContentType = "text/html";
                    await context.Response.WriteAsync(html);
                    return;
                }
            }
            await _next(context);
        }
    }


}
