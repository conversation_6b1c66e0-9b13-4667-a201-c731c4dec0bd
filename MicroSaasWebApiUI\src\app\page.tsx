import { Suspense } from "react";
import Header from "@/components/Header";
import ZeroRisk from "@/components/ZeroRisk";
import Features from "@/components/Features";
import Pricing from "@/components/Pricing";
import FAQ from "@/components/FAQ";
import CTA from "@/components/CTA";
import Footer from "@/components/Footer";
import Testimonials from "@/components/Testimonials";
import Hero from "@/components/Hero";
import AboutUs from '@/components/AboutUs';
import Doc from "@/components/Doc";

export default function Home() {
  return (
    <>
      <Suspense>
        <Header />
      </Suspense>
      <div className="min-h-[100vh] flex flex-col justify-between w-full bg-white dark:bg-[#010814]">
        <Hero />
        <ZeroRisk />
        <Testimonials />
        <Features />
        <Pricing />
        <AboutUs />
        <FAQ />
        <CTA />
      </div>

      <Suspense>
        <Footer />
      </Suspense>
    </>
  );
}