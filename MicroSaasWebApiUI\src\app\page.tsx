import { Suspense } from "react";
import Header from "@/components/Header";
import ZeroRisk from "@/components/ZeroRisk";
import Features from "@/components/Features";
import Pricing from "@/components/Pricing";
import FAQ from "@/components/FAQ";
import CTA from "@/components/CTA";
import Footer from "@/components/Footer";
import Testimonials from "@/components/Testimonials";
import Hero from "@/components/Hero";
import AboutMe from '@/components/AboutMe';
import Doc from "@/components/Doc";
import AboutMeJP from "@/components/AboutMeJP";
import AdManager from "@/components/AdManager";

export default function Home() {
  return (
    <>
      <Suspense>
        <Header />
      </Suspense>
      <div className="min-h-[100vh] flex flex-col justify-between w-full bg-white dark:bg-[#010814]">
        {/* <Hero />
        <ZeroRisk />
        <Testimonials />
        <Features />
        <Pricing />

        <FAQ />
        <CTA /> */}
        <AboutMeJP />
        <AdManager type="display-horizontal" />
      </div>

      <Suspense>
        <Footer />
      </Suspense>
    </>
  );
}